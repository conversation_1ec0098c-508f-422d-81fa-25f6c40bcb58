package com.stt.android.questionnaire

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.compose.setContent
import androidx.activity.viewModels
import androidx.annotation.AnimRes
import androidx.appcompat.app.AppCompatActivity
import androidx.compose.animation.AnimatedContentTransitionScope
import androidx.compose.animation.core.tween
import androidx.lifecycle.lifecycleScope
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.compose.theme.AppTheme
import com.stt.android.domain.workout.ActivityType
import com.stt.android.domain.workouts.ActivityGroup
import com.stt.android.extensions.groupAndSort
import com.stt.android.questionnaire.QuestionnaireViewModel.Companion.DESTINATION_MOTIVATION_SURVEY
import com.stt.android.questionnaire.QuestionnaireViewModel.Companion.DESTINATION_SPORTS_SURVEY
import com.stt.android.questionnaire.screens.MotivationQuestionnaireScreen
import com.stt.android.questionnaire.screens.SportsQuestionnaireScreen
import com.stt.android.ui.utils.rememberWindowInfo
import com.stt.android.utils.putEnumExtra
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import timber.log.Timber

@AndroidEntryPoint
class QuestionnaireActivity : AppCompatActivity() {
    val viewModel: QuestionnaireViewModel by viewModels()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        viewModel.load(
            ActivityType.values().groupAndSort(
                this,
                listOf(
                    ActivityGroup.Running,
                    ActivityGroup.Cycling,
                    ActivityGroup.OutdoorAdventures,
                    ActivityGroup.WinterSports,
                    ActivityGroup.Watersports,
                    ActivityGroup.Performance,
                    ActivityGroup.IndoorSports,
                    ActivityGroup.Diving,
                    ActivityGroup.TeamAndRacketSports
                )
            )
        )

        setContent {
            val windowInfo = rememberWindowInfo()
            AppTheme {
                val navController = rememberNavController()
                NavHost(
                    navController = navController,
                    startDestination = viewModel.startDestination
                ) {
                    composable(
                        DESTINATION_SPORTS_SURVEY,
                        enterTransition = {
                            when (initialState.destination.route) {
                                DESTINATION_MOTIVATION_SURVEY ->
                                    slideIntoContainer(
                                        AnimatedContentTransitionScope.SlideDirection.Left,
                                        animationSpec = tween(700)
                                    )
                                else -> null
                            }
                        },
                        exitTransition = {
                            when (targetState.destination.route) {
                                DESTINATION_MOTIVATION_SURVEY ->
                                    slideOutOfContainer(
                                        AnimatedContentTransitionScope.SlideDirection.Left,
                                        animationSpec = tween(700)
                                    )
                                else -> null
                            }
                        },
                        popEnterTransition = {
                            when (initialState.destination.route) {
                                DESTINATION_MOTIVATION_SURVEY ->
                                    slideIntoContainer(
                                        AnimatedContentTransitionScope.SlideDirection.Right,
                                        animationSpec = tween(700)
                                    )
                                else -> null
                            }
                        },
                        popExitTransition = {
                            when (targetState.destination.route) {
                                DESTINATION_MOTIVATION_SURVEY ->
                                    slideOutOfContainer(
                                        AnimatedContentTransitionScope.SlideDirection.Right,
                                        animationSpec = tween(700)
                                    )
                                else -> null
                            }
                        }
                    ) {
                        SportsQuestionnaireScreen(
                            state = viewModel.sportState.value,
                            windowInfo = windowInfo,
                            onTagClick = { tag -> viewModel.onSportTagClick(tag) },
                            onShowAllClick = { showDialog -> viewModel.onShowAllClick(showDialog) },
                            onDone = { onSportsQuestionnaireDone(navController) },
                            onDismiss = ::onSportsQuestionnaireSkipped
                        )
                    }
                    composable(
                        DESTINATION_MOTIVATION_SURVEY,
                        enterTransition = {
                            when (initialState.destination.route) {
                                DESTINATION_SPORTS_SURVEY ->
                                    slideIntoContainer(
                                        AnimatedContentTransitionScope.SlideDirection.Left,
                                        animationSpec = tween(700)
                                    )
                                else -> null
                            }
                        },
                        exitTransition = {
                            when (targetState.destination.route) {
                                DESTINATION_SPORTS_SURVEY ->
                                    slideOutOfContainer(
                                        AnimatedContentTransitionScope.SlideDirection.Left,
                                        animationSpec = tween(700)
                                    )
                                else -> null
                            }
                        },
                        popEnterTransition = {
                            when (initialState.destination.route) {
                                DESTINATION_SPORTS_SURVEY ->
                                    slideIntoContainer(
                                        AnimatedContentTransitionScope.SlideDirection.Right,
                                        animationSpec = tween(700)
                                    )
                                else -> null
                            }
                        },
                        popExitTransition = {
                            when (targetState.destination.route) {
                                DESTINATION_SPORTS_SURVEY ->
                                    slideOutOfContainer(
                                        AnimatedContentTransitionScope.SlideDirection.Right,
                                        animationSpec = tween(700)
                                    )
                                else -> null
                            }
                        }
                    ) {
                        MotivationQuestionnaireScreen(
                            state = viewModel.motivationState.value,
                            windowInfo = windowInfo,
                            onTagClick = { tag -> viewModel.onMotivationTagClick(tag) },
                            onShowAllClick = {},
                            onDone = ::onMotivationQuestionnaireDone,
                            onDismiss = ::onMotivationQuestionnaireSkipped
                        )
                    }
                }
            }
        }
    }

    override fun finish() {
        super.finish()

        val extras = intent.extras ?: return

        val enterAnim = if (extras.containsKey(ARG_FINISH_ENTER_ANIMATION)) {
            extras.getInt(ARG_FINISH_ENTER_ANIMATION)
        } else {
            null
        }

        val exitAnim = if (extras.containsKey(ARG_FINISH_EXIT_ANIMATION)) {
            extras.getInt(ARG_FINISH_EXIT_ANIMATION)
        } else {
            null
        }

        if (enterAnim != null && exitAnim != null) {
            overridePendingTransition(enterAnim, exitAnim)
        }
    }

    private fun onSportsQuestionnaireDone(navController: NavHostController) {
        if (viewModel.mode == QuestionnaireMode.SPORTS_AND_MOTIVATION_QUESTIONNAIRE) {
            navController.navigate(DESTINATION_MOTIVATION_SURVEY)
        } else {
            saveAndFinish()
        }
    }

    private fun onSportsQuestionnaireSkipped() {
        viewModel.trackSurveySkippedEvent(AnalyticsPropertyValue.SurveyStepSkipped.FAVORITE_SPORTS)
        finish()
    }

    private fun onMotivationQuestionnaireDone() {
        saveAndFinish()
    }

    private fun onMotivationQuestionnaireSkipped() {
        viewModel.trackSurveySkippedEvent(AnalyticsPropertyValue.SurveyStepSkipped.MOTIVATION)
        if (viewModel.sportState.value.selectedTags.isNotEmpty()) {
            // Store sport questionnaire results even if motivation questionnaire is skipped.
            saveAndFinish(ignoreMotivations = true)
        } else {
            finish()
        }
    }

    private fun saveAndFinish(ignoreMotivations: Boolean = false) {
        lifecycleScope.launch {
            kotlin.runCatching { viewModel.save(ignoreMotivations) }
                .onFailure { Timber.w(it, "Storing questionnaire results failed.") }
                .onSuccess {
                    setResult(RESULT_OK)
                    finish()
                }
        }
    }

    companion object {
        private const val ARG_FINISH_ENTER_ANIMATION = "ARG_FINISH_ENTER_ANIMATION"
        private const val ARG_FINISH_EXIT_ANIMATION = "ARG_FINISH_EXIT_ANIMATION"

        @JvmStatic
        fun newStartIntent(
            context: Context,
            mode: QuestionnaireMode,
            @AnimRes enterAnim: Int?,
            @AnimRes exitAnim: Int?,
            analyticsContext: String
        ): Intent =
            Intent(
                context,
                QuestionnaireActivity::class.java
            ).apply {
                putEnumExtra(QuestionnaireViewModel.MODE, mode)
                putExtra(QuestionnaireViewModel.ANALYTICS_CONTEXT, analyticsContext)
                enterAnim?.run {
                    putExtra(ARG_FINISH_ENTER_ANIMATION, enterAnim)
                }
                exitAnim?.run {
                    putExtra(ARG_FINISH_EXIT_ANIMATION, exitAnim)
                }
            }
    }
}
