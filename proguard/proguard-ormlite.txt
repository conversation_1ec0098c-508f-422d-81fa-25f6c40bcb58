# Don't obfuscate get<PERSON>ing<PERSON> for OrmLite data persisters
-keepclassmembers class * implements com.j256.ormlite.field.DataPersister {
  *;
}

#Keep as much as possible from ormlite
-keep class com.j256.**
-keepnames class com.j256.** { *; }
-keepclassmembers class com.j256.**
-keepclassmembers class com.j256.** { *; }
-keepclassmembers class com.j256.**$*
-keep enum com.j256.**
-keepclassmembers enum com.j256.** { *; }
-keep interface com.j256.**
-keepclassmembers interface com.j256.** { *; }

-dontnote com.j256.ormlite.**