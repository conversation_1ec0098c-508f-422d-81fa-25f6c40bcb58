# Gson uses generic type information stored in a class file when working with fields. Proguard
# removes such information by default, so configure it to keep all of it.
-keepattributes Signature

# For using GSON @Expose annotation
-keepattributes *Annotation*

# Gson specific classes
-keep class sun.misc.Unsafe { *; }
#-keep class com.google.gson.stream.** { *; }

# Application classes that will be serialized/deserialized over Gson
-keep class com.google.gson.examples.android.model.** { *; }

# Preserve the special static methods that are required in all enumeration classes.
-keepclassmembers enum * {
    <fields>;
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

# Required as of R8 - https://r8.googlesource.com/r8/+/refs/heads/master/compatibility-faq.md#gson
-keepclassmembers,allowobfuscation class * { @com.google.gson.annotations.SerializedName <fields>; }
