# Platform calls Class.forName on types which do not exist on Android to determine platform.
-dontnote retrofit2.Platform
# Platform used when running on Java 8 VMs. Will not be used at runtime.
-dontwarn retrofit2.Platform$Java8
# Retain generic type information for use by reflection by converters and adapters.
-keepattributes Signature
# Retain declared checked exceptions for use by a Proxy instance.
-keepattributes Exceptions
# Retrofit uses annotations, proguard complains about this https://github.com/square/retrofit/issues/2335
-dontwarn javax.annotation.**
# Retrofit2 specific rule
-keepclasseswithmembers class * {
   @retrofit2.http.* <methods>;
}
