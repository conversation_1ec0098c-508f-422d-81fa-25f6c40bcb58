-keep class com.amap.api.**{*;}
-keep class com.autonavi.**{*;}
-keep class com.amap.api.maps.**{*;}
-keep class com.amap.api.trace.**{*;}
-keep class com.autonavi.amap.mapcore.*{*;}
-keep class com.amap.api.navi.**{*;}
-keep class com.autonavi.**{*;}

-keep class com.amap.api.mapcore.*{*;}

-keep class com.amap.api.location.**{*;}
-keep class com.amap.api.fence.**{*;}

-keep class com.amap.api.services.**{*;}

-keep class com.amap.api.maps2d.**{*;}
-keep class com.amap.api.mapcore2d.**{*;}

-keep class com.loc.**{*;}
-keep class com.autonavi.aps.amapapi.model.**{*;}

-keepclassmembers class * {
    public <init>(...);
}

-keep interface com.amap.api.maps.** { *; }
-keep interface com.amap.api.location.** { *; }

