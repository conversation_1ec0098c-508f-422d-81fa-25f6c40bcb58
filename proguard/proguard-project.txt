#-dontobfuscate
#-dontoptimize
-dontusemixedcaseclassnames
-dontskipnonpubliclibraryclasses
-dontskipnonpubliclibraryclassmembers 
-dontpreverify 

# The -optimizations option disables some arithmetic simplifications that Dalvik 1.0 and 1.5 can't handle. 
-optimizations !code/simplification/arithmetic,!field/*,!class/merging/*

# Explicitly preserve all serialization members. The Serializable interface
# is only a marker interface, so it wouldn't save them.
-keepclassmembers class * implements java.io.Serializable {
    static final long serialVersionUID;
    private static final java.io.ObjectStreamField[] serialPersistentFields;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();
}

-keepattributes Signature

#Keep DatabaseHelper context constructor in use in PlatformSpecificModule
-keepclassmembers class * extends com.j256.ormlite.android.apptools.OrmLiteSqliteOpenHelper {
	<init>(...);
}

# Keep the names of the fields as they're the column names in the database
-keepnames class com.stt.android.** {
	@com.j256.ormlite.field.DatabaseField <fields>;
}
-keepclassmembers class com.stt.android.** {
	@com.j256.ormlite.field.DatabaseField <fields>;
}

-keep class * extends com.stt.android.remote.feed.BackendFeedEvent

# Don't warn for things used in Espresso (hamcrest, google common and squareup)
-dontwarn org.hamcrest.**
-dontwarn com.google.android.apps.common.testing.testrunner.GoogleInstrumentation
-dontwarn com.google.common.cache.**
-dontwarn com.google.common.primitives.**
-dontwarn com.squareup.javawriter.JavaWriter

# NOTE: You might need to remove duplicated LICENSE.TXT from jar files. Use command:
# zip -d hamcrest-core-1.1.jar LICENSE.txt

# Android in-app billing v3
-keep class com.android.vending.billing.**
# Keep the SubscriptionLength and SubscriptionType enumeration intact
-keep enum com.stt.android.domain.user.SubscriptionInfo$** { *; }

-dontwarn com.google.android.gms.**

-dontnote android.**
-dontnote com.braze.**
-dontnote com.facebook.**
-dontnote com.google.**
-dontnote com.github.chrisbanes.**
-dontnote org.apache.commons.codec.**
-dontnote org.apache.http.**

# Keep logbook and sim.
-keep class com.stt.android.logbook.** {*;}
-keep interface com.stt.android.logbook.** {*;}
-keep enum com.stt.android.logbook.** {*;}

-keep class com.stt.android.sim.** {*;}
-keep interface com.stt.android.sim.** {*;}
-keep enum com.stt.android.sim.** {*;}

# Remote config
-keep class com.stt.android.remote.remoteconfig.AskoRemoteConfigResponse {*;}

# AndroidX
-keep class androidx.lifecycle.** { *; }

# Keeping all exceptions
-keep class com.stt.android.exceptions.** { *; }

# Keep old serialized class used in DatabaseUpgrade58To59Helper
-keep class com.stt.android.domain.user.workout.WorkoutIntensityZone { *; }

