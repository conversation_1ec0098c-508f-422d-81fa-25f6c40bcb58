-dontwarn org.jetbrains.annotations.**
-keep class kotlin.Metadata { *; }

# Coroutines
-keep class kotlinx.coroutines.** { *; }
-keepclassmembernames class kotlinx.** {
    volatile <fields>;
}

# Kotlin serialization
-keepattributes *Annotation*, InnerClasses
-dontnote kotlinx.serialization.SerializationKt
-keep,includedescriptorclasses class com.stt.android.**$$serializer { *; }
-keepclassmembers class com.stt.android.** {
    *** Companion;
}
-keepclasseswithmembers class com.stt.android.** {
    kotlinx.serialization.KSerializer serializer(...);
}
