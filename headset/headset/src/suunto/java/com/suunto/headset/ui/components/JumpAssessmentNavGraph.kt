package com.suunto.headset.ui.components

import androidx.compose.animation.AnimatedContentTransitionScope
import androidx.compose.animation.core.tween
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Scaffold
import androidx.compose.material.SnackbarHost
import androidx.compose.material.SnackbarHostState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.core.net.toUri
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.stt.android.analytics.AnalyticsPropertyValue.HeadphoneFeaturePageName
import com.suunto.headset.HeadsetConstant.PATH_JUMP_ASSESSMENT_MAIN
import com.suunto.headset.HeadsetConstant.SU07_VIDEO_ANIMATION_PATH
import com.suunto.headset.model.JumpStep
import com.suunto.headset.ui.components.TransitionDuration.TRANSITION_DURATION_MS
import com.suunto.headset.ui.components.jump.AssessmentMainScreen
import com.suunto.headset.ui.components.jump.JumpAssessmentProcessScreen
import com.suunto.headset.ui.components.jump.JumpAssessmentReportDetailScreen
import com.suunto.headset.ui.components.jump.JumpAssessmentResultScreen
import com.suunto.headset.viewmodel.JumpAssessmentViewModel
import com.suunto.music.R
import kotlinx.coroutines.launch

private object NavDestination {
    const val ASSESSMENT_RESULT = "assessment_result"
    const val ASSESSMENT_PROGRESS = "assessment_progress"
    const val ASSESSMENT_MAIN = "assessment_main"
    const val REPORT_DETAIL = "report_detail"
}

@Composable
fun JumpAssessmentNavGraph(
    jumpAssessmentVM: JumpAssessmentViewModel,
    exit: () -> Unit,
    modifier: Modifier = Modifier,
    navController: NavHostController = rememberNavController(),
) {
    val snackbarHostState = remember {
        SnackbarHostState()
    }
    val coroutineScope = rememberCoroutineScope()
    val currentContext = LocalContext.current
    Scaffold(
        modifier = modifier,
        snackbarHost = {
            SnackbarHost(hostState = snackbarHostState)
        }
    ) { paddingValue ->
        NavHost(
            modifier = Modifier.padding(paddingValue),
            navController = navController,
            startDestination = NavDestination.ASSESSMENT_MAIN,
            enterTransition = {
                slideIntoContainer(
                    AnimatedContentTransitionScope.SlideDirection.Left,
                    tween(TRANSITION_DURATION_MS)
                )
            },
            exitTransition = {
                slideOutOfContainer(
                    AnimatedContentTransitionScope.SlideDirection.Left,
                    tween(TRANSITION_DURATION_MS)
                )
            },
            popEnterTransition = {
                slideIntoContainer(
                    AnimatedContentTransitionScope.SlideDirection.Right,
                    tween(TRANSITION_DURATION_MS)
                )
            },
            popExitTransition = {
                slideOutOfContainer(
                    AnimatedContentTransitionScope.SlideDirection.Right,
                    tween(TRANSITION_DURATION_MS)
                )
            }
        ) {
            composable(NavDestination.ASSESSMENT_MAIN) {
                AssessmentMainScreen(
                    graphData = jumpAssessmentVM.assessmentMainGraphData.collectAsState().value,
                    baselineValue = jumpAssessmentVM.baselineValue.collectAsState().value,
                    unit = jumpAssessmentVM.unit,
                    onAddMeasurement = {
                        jumpAssessmentVM.getAssessmentSteps(baselineAssessment = false)
                        navController.navigate(NavDestination.ASSESSMENT_PROGRESS)
                    },
                    onReRecordBaseline = {
                        jumpAssessmentVM.getAssessmentSteps(baselineAssessment = true)
                        navController.navigate(NavDestination.ASSESSMENT_PROGRESS)
                    },
                    onBack = exit,
                    onHelp = { jumpAssessmentVM.showGuideSheet() },
                    loading = jumpAssessmentVM.loading.collectAsState().value,
                    showGuide = jumpAssessmentVM.showGuide.collectAsState().value,
                    onBuildBaseline = {
                        if (!jumpAssessmentVM.getShowGuide()) {
                            jumpAssessmentVM.getTSBValue {
                                jumpAssessmentVM.getAssessmentSteps(true)
                                navController.navigate(NavDestination.ASSESSMENT_PROGRESS)
                            }
                        }
                    },
                    onHideGuide = { jumpAssessmentVM.hideGuide() },
                    assetBaseUrl = jumpAssessmentVM.assetsBaseUrl,
                    swimming = jumpAssessmentVM.swimming.collectAsState().value,
                    buildBaselineAnimationUri = (jumpAssessmentVM.assetsBaseUrl + SU07_VIDEO_ANIMATION_PATH + PATH_JUMP_ASSESSMENT_MAIN).toUri(),
                    haveBaselineData = jumpAssessmentVM.haveBaseline.collectAsState().value,
                    onlyHaveBaselineData = jumpAssessmentVM.onlyHaveBaseline.collectAsState().value,
                    userAgent = jumpAssessmentVM.userAgent,
                )
            }

            composable(NavDestination.ASSESSMENT_PROGRESS) {
                JumpAssessmentProcessScreen(
                    assessmentSteps = jumpAssessmentVM.assessmentSteps.collectAsState().value,
                    showFatigueReminder = jumpAssessmentVM.showFatigueReminder.collectAsState().value,
                    onDismissFatigueReminder = { jumpAssessmentVM.dismissFatigueReminder() },
                    onStartAssessment = {
                        jumpAssessmentVM.startAssessment(JumpStep.FIRST)
                    },
                    onNextAssessment = {
                        when {
                            jumpAssessmentVM.updateAssessmentValue.value -> navController.popBackStack()
                            it == JumpStep.LAST -> {
                                jumpAssessmentVM.sendFeatureExposureEvent(HeadphoneFeaturePageName.JUMP_ASSESSMENT_RESULT)
                                navController.navigate(NavDestination.ASSESSMENT_RESULT)
                            }

                            else -> jumpAssessmentVM.startAssessment(it, true)
                        }
                    },
                    onContinueAssessment = {
                        jumpAssessmentVM.dismissFatigueReminder()
                        jumpAssessmentVM.startAssessment(JumpStep.FIRST)
                    },
                    onReRecord = { jumpAssessmentVM.startAssessment(it, false) },
                    onJumpToAssessmentResult = { navController.popBackStack() },
                    onBack = { navController.popBackStack() },
                    onExitAssessment = {
                        jumpAssessmentVM.stopAssessment(it)
                        navController.popBackStack()
                    },
                    buildBaseLineValue = jumpAssessmentVM.baselineAssessment.collectAsState().value,
                    updatedAssessmentValue = jumpAssessmentVM.updateAssessmentValue.collectAsState().value,
                    currentUpdateAssessment = jumpAssessmentVM.getCurrentUpdateAssessment(),
                    deviceDisConnected = jumpAssessmentVM.showDeviceDisconnectedDialog.collectAsState().value,
                )
            }
            composable(NavDestination.ASSESSMENT_RESULT) {
                JumpAssessmentResultScreen(
                    jumpAssessmentResultValues = jumpAssessmentVM.assessmentResultValues.collectAsState().value,
                    onSaveBaselineValue = {
                        jumpAssessmentVM.saveBaselineToRemote { success ->
                            if (success) {
                                navController.navigate(NavDestination.ASSESSMENT_MAIN) {
                                    popUpTo(NavDestination.ASSESSMENT_MAIN) {
                                        inclusive = true
                                    }
                                }
                            } else {
                                coroutineScope.launch {
                                    snackbarHostState.showSnackbar(currentContext.getString(R.string.error_info))
                                }
                            }
                        }
                    },
                    onSaveJumpAssessment = {
                        jumpAssessmentVM.saveAssessmentToRemote { success ->
                            if (success) {
                                navController.navigate(NavDestination.ASSESSMENT_MAIN) {
                                    popUpTo(NavDestination.ASSESSMENT_MAIN) {
                                        inclusive = true
                                    }
                                }
                            } else {
                                coroutineScope.launch {
                                    snackbarHostState.showSnackbar(currentContext.getString(R.string.error_info))
                                }
                            }
                        }
                    },
                    onGenerateJumpAssessmentReport = {
                        jumpAssessmentVM.generateJumpAssessmentReport { success ->
                            if (success) {
                                navController.navigate(NavDestination.REPORT_DETAIL) {
                                    popUpTo(NavDestination.ASSESSMENT_MAIN)
                                }
                            } else {
                                coroutineScope.launch {
                                    snackbarHostState.showSnackbar(currentContext.getString(R.string.error_info))
                                }
                            }
                        }
                    },
                    onUpdateAssessmentValue = {
                        navController.navigate(NavDestination.ASSESSMENT_PROGRESS)
                        jumpAssessmentVM.updateAssessmentValue(it)
                    },
                    onExit = {
                        navController.navigate(NavDestination.ASSESSMENT_MAIN) {
                            popUpTo(NavDestination.ASSESSMENT_MAIN) {
                                inclusive = true
                            }
                        }
                    },
                    baselineAssessment = jumpAssessmentVM.baselineAssessment.collectAsState().value
                )
            }

            composable(NavDestination.REPORT_DETAIL) {
                JumpAssessmentReportDetailScreen(
                    reportDetail = jumpAssessmentVM.reportDetail.collectAsState().value,
                    saveAndReassess = {
                        jumpAssessmentVM.saveAssessmentToRemote { success ->
                            if (success) {
                                jumpAssessmentVM.getAssessmentSteps()
                                navController.navigate(NavDestination.ASSESSMENT_PROGRESS) {
                                    popUpTo(NavDestination.ASSESSMENT_MAIN)
                                }
                            } else {
                                coroutineScope.launch {
                                    snackbarHostState.showSnackbar(currentContext.getString(R.string.error_info))
                                }
                            }
                        }
                    },
                    abandonAndReassess = {
                        jumpAssessmentVM.getAssessmentSteps()
                        navController.navigate(NavDestination.ASSESSMENT_PROGRESS) {
                            popUpTo(NavDestination.ASSESSMENT_MAIN)
                        }
                    },
                    onBack = {
                        jumpAssessmentVM.saveAssessmentToRemote { success ->
                            if (success) {
                                navController.popBackStack(NavDestination.ASSESSMENT_MAIN, false)
                            } else {
                                coroutineScope.launch {
                                    snackbarHostState.showSnackbar(currentContext.getString(R.string.error_info))
                                }
                            }
                        }
                    })
            }
        }
    }
}
