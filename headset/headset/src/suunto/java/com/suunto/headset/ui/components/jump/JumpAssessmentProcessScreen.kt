package com.suunto.headset.ui.components.jump

import android.net.Uri
import androidx.activity.compose.BackHandler
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.Button
import androidx.compose.material.MaterialTheme
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material3.Divider
import androidx.compose.material3.Icon
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.stt.android.compose.layout.ContentCenteringColumn
import com.stt.android.compose.theme.SuuntoIcons
import com.stt.android.compose.theme.attention
import com.stt.android.compose.theme.bodyBold
import com.stt.android.compose.theme.bodyLarge
import com.stt.android.compose.theme.bodyLargeBold
import com.stt.android.compose.theme.bodySmall
import com.stt.android.compose.theme.bodySmallBold
import com.stt.android.compose.theme.cloudyGrey
import com.stt.android.compose.theme.darkGrey
import com.stt.android.compose.theme.mediumGrey
import com.stt.android.compose.theme.nearBlack
import com.stt.android.compose.theme.nearWhite
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.widgets.ConfirmationDialog
import com.stt.android.compose.widgets.PrimaryButton
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.ui.utils.TextFormatter
import com.suunto.headset.R
import com.suunto.headset.model.JumpAssessmentStepInfo
import com.suunto.headset.model.JumpStep
import com.suunto.headset.model.NeckMobilityAssessmentState
import com.suunto.headset.ui.components.CommonTopAppBar
import com.suunto.headset.ui.components.HeadPhoneDisconnectedDialog
import com.suunto.headset.ui.components.VideoAnimationPlayer
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf
import kotlinx.coroutines.launch
import com.stt.android.R as BR

@OptIn(ExperimentalFoundationApi::class)
@Composable
fun JumpAssessmentProcessScreen(
    assessmentSteps: ImmutableList<JumpAssessmentStepInfo>,
    showFatigueReminder: Boolean,
    onDismissFatigueReminder: () -> Unit,
    onStartAssessment: () -> Unit,
    onNextAssessment: (JumpStep) -> Unit,
    onContinueAssessment: () -> Unit,
    onReRecord: (JumpStep) -> Unit,
    onJumpToAssessmentResult: () -> Unit,
    onBack: () -> Unit,
    onExitAssessment: (JumpStep) -> Unit,
    modifier: Modifier = Modifier,
    buildBaseLineValue: Boolean = false,
    updatedAssessmentValue: Boolean = false,
    currentUpdateAssessment: JumpStep = JumpStep.FIRST,
    deviceDisConnected: Boolean = false,
) {
    var showQuitConfirmation by remember {
        mutableStateOf(false)
    }
    // make the disconnected dialog dismiss normally
    var shouldShowDisconnectedDialog by remember {
        mutableStateOf(true)
    }
    Scaffold(
        modifier = modifier,
        topBar = {
            CommonTopAppBar(
                title = stringResource(id = if (buildBaseLineValue) R.string.build_baseline else R.string.jump_assessment),
                navigationIcon = {
                    Icon(
                        painter = if (updatedAssessmentValue) {
                            SuuntoIcons.ActionBack.asPainter()
                        } else {
                            SuuntoIcons.ActionClose.asPainter()
                        },
                        contentDescription = ""
                    )
                },
                onBackClick = {
                    if (updatedAssessmentValue) {
                        assessmentSteps.indexOfFirst { it.assessmentState == NeckMobilityAssessmentState.ASSESSING }
                            .takeIf { it != -1 }?.let {
                                onExitAssessment.invoke(assessmentSteps[it].jumpStep)
                            } ?: run {
                            onBack.invoke()
                        }
                    } else showQuitConfirmation = true
                }
            )
        }
    ) { paddingValues ->
        BackHandler {
            showQuitConfirmation = true
        }
        ContentCenteringColumn(
            modifier = Modifier
                .padding(paddingValues)
                .background(MaterialTheme.colors.surface)
        ) {
            val coroutineScope = rememberCoroutineScope()
            LaunchedEffect(true) {
                if (!showFatigueReminder && !updatedAssessmentValue) {
                    onStartAssessment.invoke()
                }
            }
            val currentPage =
                if (updatedAssessmentValue) assessmentSteps.indexOfFirst { it.jumpStep == currentUpdateAssessment } else assessmentSteps.indexOfFirst { it.assessmentState == NeckMobilityAssessmentState.ASSESSING }
            val pagerState =
                rememberPagerState {
                    assessmentSteps.size
                }
            if (currentPage != -1) {
                LaunchedEffect(currentPage) {
                    coroutineScope.launch {
                        pagerState.scrollToPage(currentPage)
                    }
                }
            }
            AssessmentProgress(steps = assessmentSteps, assessmentSteps[pagerState.currentPage])
            HorizontalPager(
                state = pagerState,
                userScrollEnabled = assessmentSteps.all {
                    it.assessmentState == NeckMobilityAssessmentState.COMPLETE || it.assessmentState == NeckMobilityAssessmentState.FAIL
                }
            ) { page ->
                AssessmentPage(
                    currentAssessmentStep = assessmentSteps[page],
                    updatedAssessmentValue = updatedAssessmentValue,
                    onNextAssessment = { jumpStep ->
                        val assessing =
                            assessmentSteps.any { it.assessmentState == null || it.assessmentState == NeckMobilityAssessmentState.ASSESSING }
                        if (!assessing && !updatedAssessmentValue && pagerState.currentPage < assessmentSteps.indices.last) {
                            coroutineScope.launch {
                                pagerState.scrollToPage(pagerState.currentPage + 1)
                            }
                        } else onNextAssessment.invoke(jumpStep)
                    },
                    onJumpToAssessmentResult = onJumpToAssessmentResult,
                    onReRecord = {
                        onReRecord.invoke(it)
                    }
                )
            }

            if (showFatigueReminder) {
                ConfirmationDialog(
                    text = buildAnnotatedString {
                        withStyle(SpanStyle(fontWeight = FontWeight.Bold)) {
                            append(stringResource(id = R.string.fatigued_reminder_title))
                        }
                        append("\n\n")
                        append(stringResource(id = R.string.fatigued_reminder_content))
                    },
                    cancelButtonText = stringResource(id = BR.string.quit),
                    confirmButtonText = stringResource(id = BR.string.continue_str),
                    onDismissRequest = onDismissFatigueReminder,
                    onConfirm = onContinueAssessment,
                    onDismiss = {
                        onDismissFatigueReminder.invoke()
                        onBack.invoke()
                    },
                    dismissOnClickOutside = false
                )
            }
            if (showQuitConfirmation) {
                ConfirmationDialog(
                    text = buildAnnotatedString {
                        withStyle(SpanStyle(fontWeight = FontWeight.Bold)) {
                            append(stringResource(id = R.string.jump_assessment_exit_confirmation_title))
                        }
                        append("\n\n")
                        append(stringResource(id = R.string.jump_assessment_exit_confirmation_content))
                    },
                    cancelButtonText = stringResource(id = BR.string.no),
                    confirmButtonText = stringResource(id = BR.string.yes),
                    onDismissRequest = { showQuitConfirmation = false },
                    onConfirm = {
                        showQuitConfirmation = false
                        val currentAssessment = assessmentSteps[pagerState.currentPage]
                        if (currentAssessment.assessmentState == NeckMobilityAssessmentState.ASSESSING) {
                            onExitAssessment.invoke(currentAssessment.jumpStep)
                        } else {
                            onBack.invoke()
                        }
                    }
                )
            }
            if (shouldShowDisconnectedDialog && deviceDisConnected) {
                HeadPhoneDisconnectedDialog(
                    onConfirm = {
                        shouldShowDisconnectedDialog = false
                        onBack.invoke()
                    }
                )
            }
        }
    }
}

@Composable
private fun AssessmentPage(
    currentAssessmentStep: JumpAssessmentStepInfo,
    updatedAssessmentValue: Boolean,
    onNextAssessment: (JumpStep) -> Unit,
    onJumpToAssessmentResult: () -> Unit,
    onReRecord: (JumpStep) -> Unit
) {
    when (currentAssessmentStep.assessmentState) {
        null,
        NeckMobilityAssessmentState.ASSESSING -> JumpAssessing(
            jumpStep = currentAssessmentStep.jumpStep,
            currentAssessmentStep.animationUri,
            currentAssessmentStep.placeholderResId
        )

        NeckMobilityAssessmentState.COMPLETE -> AssessmentResult(
            jumpStep = currentAssessmentStep.jumpStep,
            success = true,
            onReRecord = onReRecord,
            onNext = {
                if (updatedAssessmentValue) onJumpToAssessmentResult.invoke()
                else {
                    onNextAssessment.invoke(it)
                }
            },
            jumpHeight = currentAssessmentStep.assessmentValueInCm,
            unit = currentAssessmentStep.measurementUnit,
            updatedAssessmentValue = updatedAssessmentValue
        )

        NeckMobilityAssessmentState.FAIL -> AssessmentResult(
            jumpStep = currentAssessmentStep.jumpStep,
            success = false,
            onReRecord = onReRecord
        )
    }
}

@Composable
private fun AssessmentProgress(
    steps: ImmutableList<JumpAssessmentStepInfo>,
    currentStepInfo: JumpAssessmentStepInfo,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.padding(
            horizontal = MaterialTheme.spacing.large,
            vertical = MaterialTheme.spacing.medium
        )
    ) {
        Row(
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            steps.forEachIndexed { index, jumpAssessmentStepInfo ->
                AssessmentStep(
                    stepInfo = jumpAssessmentStepInfo,
                    currentStepInfo.jumpStep == jumpAssessmentStepInfo.jumpStep
                )
                if (index != steps.indices.last) {
                    Divider(
                        thickness = 2.dp,
                        color = MaterialTheme.colors.cloudyGrey,
                        modifier = Modifier.weight(1f)
                    )
                }
            }
        }
        Row(
            modifier = Modifier
                .padding(
                    vertical = MaterialTheme.spacing.smaller,
                )
                .fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
        ) {
            steps.forEach { stepInfo ->
                Text(
                    text = when (stepInfo.jumpStep) {
                        JumpStep.FIRST -> stringResource(id = R.string.first_jump)
                        JumpStep.SECOND -> stringResource(
                            id = R.string.second_jump
                        )

                        JumpStep.LAST -> stringResource(id = R.string.last_jump)
                    },
                    style = stepInfo.assessmentState?.let { MaterialTheme.typography.bodySmallBold }
                        ?: MaterialTheme.typography.bodySmall,
                    color = if (currentStepInfo.jumpStep == stepInfo.jumpStep) {
                        when (stepInfo.assessmentState) {
                            NeckMobilityAssessmentState.ASSESSING,
                            NeckMobilityAssessmentState.COMPLETE -> MaterialTheme.colors.primary

                            NeckMobilityAssessmentState.FAIL -> MaterialTheme.colors.attention
                            null -> MaterialTheme.colors.nearBlack
                        }
                    } else {
                        MaterialTheme.colors.nearBlack
                    }
                )
            }
        }
    }
}

@Composable
private fun AssessmentStep(
    stepInfo: JumpAssessmentStepInfo,
    currentStep: Boolean,
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier) {
        Box(
            modifier = Modifier
                .size(40.dp)
                .clip(CircleShape)
                .border(
                    2.dp,
                    when (stepInfo.assessmentState) {
                        NeckMobilityAssessmentState.COMPLETE,
                        NeckMobilityAssessmentState.ASSESSING -> MaterialTheme.colors.primary

                        NeckMobilityAssessmentState.FAIL -> MaterialTheme.colors.attention
                        null -> MaterialTheme.colors.cloudyGrey
                    },
                    CircleShape
                )
                .background(
                    if (currentStep) {
                        when (stepInfo.assessmentState) {
                            NeckMobilityAssessmentState.COMPLETE,
                            NeckMobilityAssessmentState.ASSESSING -> MaterialTheme.colors.primary

                            NeckMobilityAssessmentState.FAIL -> MaterialTheme.colors.attention
                            null -> MaterialTheme.colors.surface
                        }
                    } else {
                        MaterialTheme.colors.surface
                    }
                ),
            contentAlignment = Alignment.Center
        ) {
            when (stepInfo.assessmentState) {
                NeckMobilityAssessmentState.ASSESSING ->
                    Text(
                        text = (stepInfo.jumpStep.ordinal + 1).toString(),
                        color = MaterialTheme.colors.nearWhite,
                        style = MaterialTheme.typography.bodyBold
                    )

                NeckMobilityAssessmentState.COMPLETE ->
                    Icon(
                        painter = painterResource(id = R.drawable.icon_standard),
                        contentDescription = "",
                        tint = if (currentStep) MaterialTheme.colors.surface else MaterialTheme.colors.primary
                    )

                NeckMobilityAssessmentState.FAIL ->
                    Icon(
                        painter = painterResource(id = R.drawable.icon_exclamation),
                        contentDescription = "",
                        tint = if (currentStep) MaterialTheme.colors.surface else MaterialTheme.colors.attention
                    )

                null ->
                    Text(
                        text = (stepInfo.jumpStep.ordinal + 1).toString(),
                        color = MaterialTheme.colors.mediumGrey,
                        style = MaterialTheme.typography.bodyBold
                    )
            }
        }
    }
}

@Composable
private fun AssessmentResult(
    jumpStep: JumpStep,
    success: Boolean,
    onReRecord: (JumpStep) -> Unit,
    modifier: Modifier = Modifier,
    onNext: (JumpStep) -> Unit = {},
    jumpHeight: Float = 0f,
    unit: MeasurementUnit = MeasurementUnit.METRIC,
    updatedAssessmentValue: Boolean = false
) {
    Column(modifier = modifier.fillMaxSize(), horizontalAlignment = Alignment.CenterHorizontally) {
        Box(modifier = Modifier.weight(1f), contentAlignment = Alignment.Center) {
            Icon(
                painter = painterResource(id = if (success) R.drawable.icon_success else R.drawable.icon_exclamation),
                contentDescription = "",
                tint = if (success) MaterialTheme.colors.primary else MaterialTheme.colors.attention,
            )
        }
        Text(
            text = if (success)
                "${
                    TextFormatter.formatRoundJumpHeight(
                        jumpHeight,
                        unit
                    )
                } ${stringResource(id = unit.shorterDistanceUnitResId)}"
            else when (jumpStep) {
                JumpStep.FIRST -> stringResource(id = R.string.first_jump_assessment_failed)
                JumpStep.SECOND -> stringResource(id = R.string.second_jump_assessment_failed)
                JumpStep.LAST -> stringResource(id = R.string.last_jump_assessment_failed)
            },
            color = MaterialTheme.colors.nearBlack,
            style = MaterialTheme.typography.bodyLargeBold
        )
        Text(
            modifier = Modifier.padding(
                top = MaterialTheme.spacing.medium,
                bottom = MaterialTheme.spacing.xxlarge
            ),
            text = if (success) stringResource(id = R.string.jump_height) else stringResource(id = R.string.jump_assessment_failed_hint),
            color = MaterialTheme.colors.darkGrey,
            style = MaterialTheme.typography.bodyLarge
        )

        if (success) {
            PrimaryButton(
                text = stringResource(
                    id = when {
                        updatedAssessmentValue -> BR.string.done
                        jumpStep == JumpStep.LAST -> R.string.complete
                        else -> R.string.next
                    }
                ),
                onClick = { onNext.invoke(jumpStep) },
                modifier = Modifier
                    .padding(MaterialTheme.spacing.large)
                    .fillMaxWidth()
            )
            Text(
                modifier = Modifier
                    .clickable(onClick = {
                        onReRecord.invoke(jumpStep)
                    })
                    .padding(MaterialTheme.spacing.medium),
                text = stringResource(id = R.string.re_record).uppercase(),
                color = MaterialTheme.colors.primary,
                style = MaterialTheme.typography.bodyBold
            )
        } else {
            Button(
                onClick = { onReRecord.invoke(jumpStep) },
                modifier = Modifier
                    .padding(
                        horizontal = MaterialTheme.spacing.medium,
                        vertical = MaterialTheme.spacing.xlarge
                    )
                    .fillMaxWidth()
                    .height(48.dp),
            ) {
                Icon(
                    imageVector = Icons.Default.Refresh,
                    contentDescription = "",
                    tint = MaterialTheme.colors.surface
                )
                Text(
                    modifier = Modifier.padding(start = MaterialTheme.spacing.xsmall),
                    text = stringResource(id = R.string.re_record).uppercase(),
                    style = MaterialTheme.typography.bodyBold,
                    color = MaterialTheme.colors.onPrimary
                )
            }
        }

    }
}

@Composable
private fun JumpAssessing(
    jumpStep: JumpStep,
    animationUri: Uri,
    placeholderResId: Int,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .fillMaxSize(),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        if (animationUri != Uri.EMPTY) {
            VideoAnimationPlayer(
                uri = animationUri,
                placeHolderResId = placeholderResId,
                modifier = Modifier
                    .fillMaxWidth()
                    .aspectRatio(1124f / 1020f)
            )
        }

        Column(
            modifier = Modifier.padding(MaterialTheme.spacing.medium),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = when (jumpStep) {
                    JumpStep.FIRST -> stringResource(id = R.string.first_jump)
                    JumpStep.SECOND -> stringResource(id = R.string.second_jump)
                    JumpStep.LAST -> stringResource(id = R.string.last_jump)
                },
                color = MaterialTheme.colors.nearBlack,
                style = MaterialTheme.typography.bodyLargeBold
            )
            Text(
                modifier = Modifier.padding(vertical = MaterialTheme.spacing.medium),
                text = stringResource(id = R.string.jump_reminder),
                color = MaterialTheme.colors.darkGrey,
                style = MaterialTheme.typography.bodyLarge
            )
        }
        Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
            Text(
                text = stringResource(id = R.string.assessing),
                color = MaterialTheme.colors.cloudyGrey,
                style = MaterialTheme.typography.bodyBold
            )
        }
    }
}

@Composable
@Preview
private fun JumpAssessmentProcessScreenPreview() {
    JumpAssessmentProcessScreen(
        assessmentSteps = persistentListOf(
            JumpAssessmentStepInfo(JumpStep.FIRST, NeckMobilityAssessmentState.ASSESSING),
            JumpAssessmentStepInfo(JumpStep.SECOND, NeckMobilityAssessmentState.FAIL),
            JumpAssessmentStepInfo(JumpStep.LAST, NeckMobilityAssessmentState.COMPLETE)
        ),
        showFatigueReminder = false,
        onDismissFatigueReminder = {},
        onStartAssessment = {},
        onNextAssessment = {},
        onContinueAssessment = {},
        onReRecord = {},
        onJumpToAssessmentResult = {},
        onBack = {},
        onExitAssessment = {}
    )
}

