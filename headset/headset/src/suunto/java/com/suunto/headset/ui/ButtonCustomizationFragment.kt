package com.suunto.headset.ui

import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import by.kirich1409.viewbindingdelegate.viewBinding
import com.stt.android.ThemeColors
import com.stt.android.common.viewstate.loaded
import com.stt.android.ui.utils.EpoxyConditionalDividerItemDecoration
import com.suunto.headset.R
import com.suunto.headset.capability.BaseHeadsetFeature
import com.suunto.headset.capability.SU07HeadsetFeature
import com.suunto.headset.capability.SU08HeadsetFeature
import com.suunto.headset.capability.SupportedHeadsetDevices
import com.suunto.headset.contract.HeadsetSettingContract
import com.suunto.headset.databinding.FragmentCustomizeButtonBinding
import com.suunto.headset.ext.getButtonFunctionResId
import com.suunto.headset.model.HeadsetButton
import com.suunto.headset.mvi.BaseStateFragment
import com.suunto.headset.ui.adapter.DeviceButtonTypeMode_
import com.suunto.headset.ui.controller.HeadsetButtonController
import com.suunto.headset.ui.data.HeadsetButtonViewState
import com.suunto.headset.ui.view.AlertDialogFragment
import com.suunto.headset.ui.view.ButtonSettingDialogFragment
import com.suunto.headset.ui.view.buildAlertDialogFragment
import com.suunto.headset.viewmodel.BaseHeadsetSettingViewModel
import com.suunto.headset.viewmodel.ButtonCustomizedUseCase
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject
import kotlin.reflect.KClass

@AndroidEntryPoint
class ButtonCustomizationFragment :
    BaseStateFragment<HeadsetSettingContract.UIIntent, HeadsetSettingContract.UIState, BaseHeadsetSettingViewModel>() {

    override val viewModelClass: KClass<BaseHeadsetSettingViewModel>
        get() = BaseHeadsetSettingViewModel::class

    private val binding by viewBinding(FragmentCustomizeButtonBinding::bind, R.id.root)
    override fun getLayoutResId(): Int = R.layout.fragment_customize_button

    override fun getTitleResId(): Int = R.string.setting_button_customization_title

    @Inject
    lateinit var supportHeadsetDevice: SupportedHeadsetDevices

    @Inject
    lateinit var baseHeadsetFeature: BaseHeadsetFeature

    @Inject
    lateinit var controller: HeadsetButtonController

    @Inject
    lateinit var buttonCustomizedUseCase: ButtonCustomizedUseCase

    private var buttonSettingDialogFragment: ButtonSettingDialogFragment? = null
    private var buttonConfirmDialogFragment: AlertDialogFragment? = null

    override fun initView() {
        super.initView()
        setHeadsetImage()
        handleObserveConnectState()
        initRecyclerView()
        loadHeadsetButtonData()
    }

    private fun setHeadsetImage() {
        when (baseHeadsetFeature){
            is SU08HeadsetFeature -> binding.ivInstructions.setImageResource(R.drawable.su08_button_customization_instruction)
            is SU07HeadsetFeature -> binding.ivInstructions.setImageResource(R.drawable.su07_button_customization_instruction)
        }
    }

    private fun initRecyclerView() {
        binding.list.layoutManager =
            LinearLayoutManager(requireContext(), RecyclerView.VERTICAL, false)
        val dividerHeight = resources.getDimensionPixelSize(com.stt.android.R.dimen.size_divider)
        val dividerColor =
            ThemeColors.resolveColor(requireContext(), com.stt.android.R.attr.suuntoDividerColor)
        binding.list.addItemDecoration(EpoxyConditionalDividerItemDecoration(dividerColor = dividerColor) { _, nextItem ->
            if (nextItem is DeviceButtonTypeMode_) {
                null
            } else {
                dividerHeight
            }
        })
        binding.list.adapter = controller.adapter
    }

    private fun loadHeadsetButtonData() {
        HeadsetSettingContract.UIIntent.GetHeadsetButtons.sendIntent()
    }

    override fun bindState() {
        super.bindState()
        HeadsetSettingContract.UIState::headsetButtons.bindStateNotNull {
            val viewState = loaded(HeadsetButtonViewState(it, ::onButtonClick))
            Timber.d("Headset: bindState: $viewState")
            controller.setData(viewState)
        }
    }

    private fun onButtonClick(button: HeadsetButton) {
        Timber.d("Headset: onButtonClick: $button")
        if (!button.isSupportCustomized) return
        showButtonSettingDialog(button)
    }

    private fun showButtonSettingDialog(button: HeadsetButton) {
        lifecycleScope.launch {
            val shortcutList = buttonCustomizedUseCase.getButtonShortcutListWithoutSport()
            ButtonSettingDialogFragment.newInstance(button, shortcutList).apply {
                buttonSettingDialogFragment = this
                this.setOnShortcutKeySelectListener {
                    Timber.d("Headset: onShortcutKey: $it")
                    val buttonCopy = button.copy(buttonShortcutKey = it)
                    handleButtonSettingClick(buttonCopy)
                }
            }.show(
                childFragmentManager, BUTTON_SETTING_BOTTOM_SHEET_TAG
            )
        }
    }

    private fun handleButtonSettingClick(updateButton: HeadsetButton) {
        lifecycleScope.launch {
            val conflictButton =
                buttonCustomizedUseCase.getShortcutKeyConflictButton(updateButton)
            if (conflictButton != null) {
                showButtonSettingConfirmDialog(conflictButton, updateButton)
                return@launch
            }
            buttonSettingDialogFragment?.dismiss()
            HeadsetSettingContract.UIIntent.UpdateHeadsetButtons(updateButton).sendIntent()
        }
    }

    private fun showButtonSettingConfirmDialog(
        previousButton: HeadsetButton,
        button: HeadsetButton
    ) {
        val dialogTitle = getString(R.string.button_setting_dialog_title)
        val dialogContent = getString(
            R.string.button_setting_dialog_content,
            getString(getButtonFunctionResId(button.buttonFunction)),
            getString(getButtonFunctionResId(previousButton.buttonFunction))
        )
        buttonConfirmDialogFragment = buildAlertDialogFragment(
            title = dialogTitle,
            content = dialogContent,
            positiveText = getString(com.stt.android.R.string.ok),
            negativeText = getString(com.stt.android.R.string.cancel)
        ) {
            onPositiveClick = {
                buttonSettingDialogFragment?.dismiss()
                HeadsetSettingContract.UIIntent.UpdateHeadsetButtons(button).sendIntent()
            }
            show(
                <EMAIL>,
                BUTTON_SETTING_CONFIRM_DIALOG_TAG
            )
        }
    }

    companion object {
        const val BUTTON_SETTING_BOTTOM_SHEET_TAG = "BUTTON_SETTING_BOTTOM_SHEET_TAG"
        const val BUTTON_SETTING_CONFIRM_DIALOG_TAG = "BUTTON_SETTING_CONFIRM_DIALOG_TAG"
    }
}
