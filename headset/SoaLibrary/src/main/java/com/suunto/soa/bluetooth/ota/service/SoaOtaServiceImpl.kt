package com.suunto.soa.bluetooth.ota.service

import android.annotation.SuppressLint
import android.bluetooth.BluetoothDevice
import android.bluetooth.BluetoothSocket
import android.content.Context
import com.stt.android.coroutines.runSuspendCatchingWithTimeout
import com.suunto.soa.ble.request.UpdateFileInfoRequest
import com.suunto.soa.ble.scanner.validName
import com.suunto.soa.bluetooth.ota.exception.ConnectionException
import com.suunto.soa.bluetooth.ota.exception.DeviceCancellationException
import com.suunto.soa.bluetooth.ota.exception.EnterUpdateModeException
import com.suunto.soa.bluetooth.ota.exception.FirmwareStatusException
import com.suunto.soa.bluetooth.ota.exception.FirmwareTransferException
import com.suunto.soa.bluetooth.ota.exception.OtaException
import com.suunto.soa.bluetooth.ota.exception.UnableUpdateException
import com.suunto.soa.bluetooth.ota.request.DeviceCanUpdateRequest
import com.suunto.soa.bluetooth.ota.request.EnterUpdateModeRequest
import com.suunto.soa.bluetooth.ota.request.ExitUpdateModeRequest
import com.suunto.soa.bluetooth.ota.request.FirmwareDataBlockRequest
import com.suunto.soa.bluetooth.ota.request.FirmwareUpdateResultRequest
import com.suunto.soa.bluetooth.ota.request.OtaDataBlockSender
import com.suunto.soa.bluetooth.ota.response.DeviceCanUpdateResponse
import com.suunto.soa.bluetooth.ota.response.EnterUpdateModeResponse
import com.suunto.soa.bluetooth.ota.response.FirmwareDataBlockResponse
import com.suunto.soa.bluetooth.ota.response.GetFirmwareUpdateResultResponse
import com.suunto.soa.bluetooth.ota.utils.DataDispatcher
import com.suunto.soa.command.Request
import com.suunto.soa.command.Response
import com.suunto.soa.command.SoaUnique
import com.suunto.soa.log.SoaOtaFileTimberTree
import com.suunto.soa.state.DeviceRunning
import com.suunto.soa.state.RunningState
import com.suunto.soa.to4Bytes
import com.suunto.soa.toUInt
import com.suunto.soa.utils.DataUtils
import com.suunto.soa.utils.TransformUtils
import com.suunto.soa.utils.ext.toBytes
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.launch
import timber.log.Timber
import java.io.ByteArrayOutputStream
import java.io.File
import java.io.IOException
import java.io.InputStream
import java.io.OutputStream
import java.nio.ByteBuffer
import java.util.UUID
import java.util.zip.CRC32

class SoaOtaServiceImpl : SoaOtaService {

    companion object {
        private const val SPP_UUID = "0000FD2D-0000-1000-8000-00805F9B34FB"

        private val otaService by lazy(LazyThreadSafetyMode.SYNCHRONIZED) {
            SoaOtaServiceImpl()
        }

        fun getInstance(context: Context, deviceRunning: DeviceRunning): SoaOtaServiceImpl {
            return otaService.apply {
                this.initOtaData(context, deviceRunning)
            }
        }
    }

    private lateinit var context: Context
    private lateinit var deviceRunning: DeviceRunning

    private var btSocket: BluetoothSocket? = null
    private var btOutputStream: OutputStream? = null
    private var btInputStream: InputStream? = null

    private val dataDispatcher by lazy { DataDispatcher() }
    private val otaFileTimberTree by lazy { SoaOtaFileTimberTree() }

    private fun initOtaData(context: Context, deviceRunning: DeviceRunning) {
        this.context = context
        this.deviceRunning = deviceRunning
    }

    /**
     * the entire state of the app during ota
     */
    private val otaState = MutableSharedFlow<OtaState>(
        replay = 1,
        extraBufferCapacity = 3
    ).apply { tryEmit(OtaState.None) }

    private var otaJob: Job? = null
    private var receiveJob: Job? = null
    private var coroutineScope = CoroutineScope(
        SupervisorJob() + Dispatchers.IO + CoroutineExceptionHandler { _, thr ->
            if (thr.cause is DeviceCancellationException) return@CoroutineExceptionHandler
            timber(true).w(thr, "OTA failed, an exception occurred.")
            val logFile = otaFileTimberTree.writeLogsToFile("earplugs_ota_", getLogDir())
            otaState.tryEmit(OtaState.Failed(thr, logFile?.absolutePath))
            deviceRunning.setRunningState(RunningState.Unknown)
            otaFileTimberTree.apply {
                uproot()
                clearCachedLog()
            }
            disconnect()
        }
    )

    @SuppressLint("MissingPermission")
    private fun connect(device: BluetoothDevice): Boolean {
        return try {
            timber().i("Connect to ${device.address}.")
            val rfcommSocket = device.createRfcommSocketToServiceRecord(UUID.fromString(SPP_UUID))
                .also { btSocket = it }
            rfcommSocket.connect()
            btOutputStream = rfcommSocket.outputStream

            timber().i("Connect succeed, launch receive response job.")
            receiveJob = launchReceiveResponse(rfcommSocket)
            timber().i("Receive response job launched.")
            true
        } catch (e: IOException) {
            timber().w(e, "Connect device failed.")
            btClose()
            false
        }
    }

    private fun btClose() {
        try {
            btInputStream?.close()
        } catch (e: Exception) {
            timber().w(e, "Exception occurs while inputStream close.")
        } finally {
            btInputStream = null
        }
        try {
            btOutputStream?.close()
        } catch (e: Exception) {
            timber().w(e, "Exception occurs while outputStream close.")
        } finally {
            btOutputStream = null
        }
        try {
            btSocket?.close()
        } catch (e: Exception) {
            timber().w(e, "Exception occurs while bt socket close.")
        } finally {
            btSocket = null
        }
    }

    override suspend fun initialization() {
        otaState.emit(OtaState.None)
    }

    override fun disconnect() {
        cancelReceiveJob()
        cancelOtaJob()
        coroutineScope.launch {
            dataDispatcher.unregisterAll()
        }

        timber().i("Close connection")
        btClose()

        // reset transfer state
        otaState.tryEmit(OtaState.None)
        timber().i("Current ota transfer finished.")
    }

    private fun launchReceiveResponse(
        rfcommSocket: BluetoothSocket
    ) = coroutineScope.launch {
        val readInputStream =
            rfcommSocket.inputStream.also { <EMAIL> = it }
        val bytesOutputStream = ByteArrayOutputStream()
        val readBuffer = ByteArray(1024)
        var readLength: Int
        try {
            while (readInputStream.read(readBuffer).also { readLength = it } != -1) {
                bytesOutputStream.write(readBuffer, 0, readLength)
                val readDataBytes = bytesOutputStream.toByteArray()
                if (DataUtils.dataIntegrity(readDataBytes)) {
                    bytesOutputStream.reset()
                    dataDispatcher.receiveData(readDataBytes)
                }
            }
        } catch (thr: Throwable) {
            if (isOtaJobActive()) {
                DeviceCancellationException("Cancel ota transfer job.").apply {
                    // If ota file is transmission, CancellationException will be sendRequestWithTimeOut capture and passed to CoroutineExceptionHandler, we don't deal with it
                    cancelOtaJob(this)
                }
                throw thr
            } else {
                timber().i(thr, "Receive bt response failed.")
            }
        }
    }

    private fun cancelReceiveJob() {
        receiveJob?.cancel()
        receiveJob = null
    }

    private fun writeRequestData(data: ByteArray, cacheLog: Boolean): Boolean {
        val writeStream = btOutputStream
        if (writeStream == null) {
            timber().i("Write stream is null.")
            return false
        }
        try {
            writeStream.write(data)
            timber(cacheLog).i("[Write] ${TransformUtils.bytes2HexString(data)}")
            return true
        } catch (e: Exception) {
            timber().w(e, "[Write] request failed: ${TransformUtils.bytes2HexString(data)}")
        }
        return false
    }

    private suspend fun sendRequestWithTimeOut(
        request: Request,
        cacheLog: Boolean = true
    ): Response<out Any> {
        val writeBytes = request.toByteArray()
        return runSuspendCatchingWithTimeout(Request.TIME_OUT) {
            val handlerKey = SoaUnique.uniqueKey(request.opCode, request.opCodeSn)
            val dispatcher = dataDispatcher.getOrPutNewDispatcher(handlerKey)
            if (writeRequestData(writeBytes, cacheLog)) {
                dataDispatcher.awaitResponse(dispatcher, handlerKey).also {
                    timber(cacheLog).i("[Receive] ${TransformUtils.bytes2HexString(it.toByteArray())}")
                }
            } else {
                dataDispatcher.removeDispatcher(handlerKey)
                throw OtaException(
                    "Write request data failed -> ${TransformUtils.bytes2HexString(writeBytes)}"
                )
            }
        }.getOrElse { e ->
            throw OtaException(
                "Error sending bt request data -> ${TransformUtils.bytes2HexString(writeBytes)}",
                e
            )
        }
    }

    private fun isOtaJobActive() = otaJob?.isActive == true

    private fun cancelOtaJob(cause: CancellationException? = null) {
        otaJob?.cancel(cause)
        otaJob = null
    }

    @SuppressLint("MissingPermission")
    override fun transferFirmware(
        device: BluetoothDevice,
        otaFile: File,
        upgradeInfo: UpgradeInfo
    ) {
        otaJob = coroutineScope.launch {
            otaFileTimberTree.apply {
                plant()
                clearCachedLog()
            }
            deviceRunning.setRunningState(RunningState.Transferring)

            timber().i("ApplicationId -> ${upgradeInfo.applicationId}, VersionCode -> ${upgradeInfo.versionCode}, VersionName -> ${upgradeInfo.versionName}")
            timber().i("Product name -> ${upgradeInfo.productName}, Current version -> ${upgradeInfo.currentVersion}, Target version -> ${upgradeInfo.targetVersion}, Device mac address -> ${upgradeInfo.deviceMac}")
            otaState.emit(OtaState.Preparing)
            timber().i("State Preparing, read ota file into ByteArray.")
            val otaDataBlock = OtaDataBlockSender(otaFile.readBytes())
            timber().i("File size: ${otaDataBlock.getSize()}.")

            if (!connect(device)) {
                throw ConnectionException("Can not connect to device(${device.validName()}, ${device.address}).")
            }
            otaState.emit(OtaState.Connected)
            timber().i("State Connected.")
            // Delay one second avoid write data error
            delay(1000)

            // Exit last upgrade
            sendRequestWithTimeOut(
                DeviceCanUpdateRequest((otaDataBlock.getSize() + 1).to4Bytes())
            )
            delay(200)
            sendRequestWithTimeOut(ExitUpdateModeRequest(), true)
            delay(200)
            // Reset the headset related flag bits
            sendRequestWithTimeOut(UpdateFileInfoRequest())

            // Query if device can update
            val canUpdateResponse = sendRequestWithTimeOut(
                DeviceCanUpdateRequest((otaDataBlock.getSize() + 1).to4Bytes())
            ) as DeviceCanUpdateResponse
            if (!canUpdateResponse.isSuccess() || !canUpdateResponse.canUpdate()) {
                throw UnableUpdateException.newInstance(canUpdateResponse.data.toUInt())
            }
            otaState.emit(OtaState.UpgradeAllowed)

            // Enter update mode
            val enterUpdateResponse =
                sendRequestWithTimeOut(EnterUpdateModeRequest()) as EnterUpdateModeResponse
            if (!enterUpdateResponse.isSuccess() || !enterUpdateResponse.data.isInUpdateMode) {
                throw EnterUpdateModeException("Device is unable to enter update mode.")
            }

            // Start data transfer
            timber().i("Start transfer ota file data.")
            var offset = enterUpdateResponse.data.offset
            var otaDataLength = enterUpdateResponse.data.offsetDataLength
            val needCrc32 = enterUpdateResponse.data.crc32Code
            val totalSize = otaDataBlock.getSize()
            while (true) {
                val byteData = otaDataBlock.read(offset, otaDataLength)
                val firmWareRequest = if (needCrc32) {
                    val crc32 = CRC32()
                    crc32.update(byteData)
                    FirmwareDataBlockRequest(
                        ByteBuffer.allocate(byteData.size + 4).apply {
                            put(byteData)
                            put(crc32.value.toBytes())
                        }.array()
                    )
                } else {
                    FirmwareDataBlockRequest(byteData)
                }
                val firmwareResponse =
                    sendRequestWithTimeOut(firmWareRequest, false) as FirmwareDataBlockResponse
                if (!firmwareResponse.isSuccess() || !firmwareResponse.data.transferSuccess) {
                    timber().i(
                        "File transfer failed: %s --> %s",
                        TransformUtils.bytes2HexString(firmWareRequest.toByteArray()),
                        TransformUtils.bytes2HexString(firmwareResponse.toByteArray())
                    )
                    // Attempt to forcibly exit the upgrade mode.
                    sendRequestWithTimeOut(ExitUpdateModeRequest(), true)
                    throw FirmwareTransferException("OTA data transfer failed.")
                }
                offset = firmwareResponse.data.offset
                otaDataLength = firmwareResponse.data.offsetDataLength
                if (offset == 0 && otaDataLength == 0) {
                    otaState.emit(
                        OtaState.Transferring(
                            TransferProgress(totalSize, totalSize)
                        )
                    )
                    timber().i("State Transferring, file transfer completed.")
                    break
                } else if (firmwareResponse.data.delay > 0) {
                    timber().i("State Transferring, file transfer offset:$offset, totalSize:$totalSize")
                    otaState.emit(
                        OtaState.Transferring(
                            TransferProgress(offset, totalSize)
                        )
                    )
                    delay(firmwareResponse.data.delay.toLong())
                }
            }

            // get update result
            val updateResultResponse =
                sendRequestWithTimeOut(FirmwareUpdateResultRequest()) as GetFirmwareUpdateResultResponse
            if (!updateResultResponse.isSuccess() || !updateResultResponse.updateSucceed()) {
                throw FirmwareStatusException.newInstance(updateResultResponse.data.toUInt())
            }
            otaState.emit(OtaState.Succeed)
            timber().i("State Succeed.")
            // wait for device verification and restart
            deviceRunning.setRunningState(RunningState.CheckingBin)

            timber().i("OTA process end.")
            disconnect()
        }
    }

    override fun getLogDir(): File = File(context.filesDir, "earplugs_logs").also {
        if (!it.exists()) {
            it.mkdirs()
        }
    }

    override fun getOtaStateFlow(): Flow<OtaState> = otaState.asSharedFlow()

    override fun isInTransferring(): Boolean {
        return isOtaJobActive() || deviceRunning.getRunningState().isTransferring()
    }

    override fun isInCheckingBin(): Boolean {
        return deviceRunning.getRunningState().isCheckingBin()
    }

    override fun isInProgress(): Boolean {
        return isOtaJobActive() || deviceRunning.getRunningState().isOtaState()
    }

    /**
     * Cache log to the lines field of SoaOtaFileTimberTree if need.
     */
    fun timber(cacheLog: Boolean = true) =
        if (cacheLog) Timber.tag(SoaOtaFileTimberTree.OTA_TAG) else Timber
}
