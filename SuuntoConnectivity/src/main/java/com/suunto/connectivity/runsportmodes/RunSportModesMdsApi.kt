package com.suunto.connectivity.runsportmodes

import androidx.core.net.toUri
import com.movesense.mds.MdsException
import com.squareup.moshi.Moshi
import com.squareup.moshi.Types
import com.stt.android.coroutines.await
import com.suunto.connectivity.STATUS_CONTINUE
import com.suunto.connectivity.STATUS_OK
import com.suunto.connectivity.sdsmanager.MdsRx
import com.suunto.connectivity.sdsmanager.model.MdsContent
import com.suunto.connectivity.sdsmanager.model.MdsValue
import com.suunto.connectivity.suuntoconnectivity.SuuntoConnectivityConstants.MDS_CONTRACT_EMPTY
import com.suunto.connectivity.suuntoconnectivity.SuuntoConnectivityConstants.MDS_SCHEME_PREFIX
import java.util.Base64
import javax.inject.Inject

class RunSportModesMdsApi @Inject constructor(
    private val mdsRx: MdsRx,
    private val moshi: <PERSON><PERSON>,
) : RunSportModesWatchApi {
    private companion object {
        private const val ALL_SPORTS_URI = "$MDS_SCHEME_PREFIX%s/SportMode2/Sports"
        private const val RECENT_SPORTS_URI = "$MDS_SCHEME_PREFIX%s/SportMode2/RecentSports"
        private const val TRAINING_MODE_TEMPLATE_LIST_URI =
            "$MDS_SCHEME_PREFIX%s/SportMode2/TrainingModeTemplateList"
        private const val TRAINING_MODE_LIST_URI =
            "$MDS_SCHEME_PREFIX%s/SportMode2/TrainingModeList"
        private const val TRAINING_MODE_URI = "$MDS_SCHEME_PREFIX%s/SportMode2/TrainingMode"
        private const val TRAINING_MODE_TEMPLATE_URI =
            "$MDS_SCHEME_PREFIX%s/SportMode2/TrainingModeTemplate"
        private const val DATA_SCREEN_TEMPLATE_URI =
            "$MDS_SCHEME_PREFIX%s/SportMode2/DataScreenTemplates"
        private const val DATA_SCREEN_URI = "$MDS_SCHEME_PREFIX%s/SportMode2/DataScreens"
        private const val COMPETITION_TARGET_INFO_URI: String =
            "$MDS_SCHEME_PREFIX%s/SportMode2/Competition/ChallengeRivalInfo"
        private const val COMPETITION_TARGET_SAMPLES_URI: String =
            "$MDS_SCHEME_PREFIX%s/SportMode2/CompetitionSample/Stream"
        private const val COMPETITION_SPEED_DISTANCE_TIME_SAMPLES_URI: String =
            "$MDS_SCHEME_PREFIX%s/SportMode2/CompetitionSample2/Stream"
    }

    override suspend fun getAllSports(serial: String, withSportTags: Boolean): GetAllSportsResponse {
        val uri = generateWithSportTagUri(serial, ALL_SPORTS_URI, withSportTags)
        return mdsGet(serial, uri, MDS_CONTRACT_EMPTY)
    }

    override suspend fun getRecentSports(serial: String, withSportTags: Boolean): GetRecentSportsResponse {
        val uri = generateWithSportTagUri(serial, RECENT_SPORTS_URI, withSportTags)
        return mdsGet(serial, uri, MDS_CONTRACT_EMPTY)
    }

    override suspend fun getTrainingModeHeaderList(
        serial: String,
        contract: SportItemContract
    ): GetTrainingModeHeaderListResponse {
        val uri = generateWithSportIdUri(serial, TRAINING_MODE_TEMPLATE_LIST_URI, contract)
        return mdsRx[uri, MDS_CONTRACT_EMPTY].await()
            .decodeMdsResponseWrappedInContent<GetTrainingModeHeaderListResponse>()
    }

    override suspend fun getTrainingModeList(
        serial: String,
        contract: SportItemContract
    ): GetTrainingModeListResponse {
        val uri = generateWithSportIdUri(serial, TRAINING_MODE_LIST_URI, contract)
        val response = mdsRx.getWithHeader(uri, MDS_CONTRACT_EMPTY).await()
        if (response.statusCode != STATUS_OK && response.statusCode != STATUS_CONTINUE) {
            throw MdsException("Failed to get training mode list", response.statusCode)
        }
        return response.body.decodeMdsResponseWrappedInContent<GetTrainingModeListResponse>()
    }

    override suspend fun getTrainingMode(
        serial: String,
        contract: TrainingModeItemContract
    ): TrainingModeResponse {
        val json = encodeMdsValue(contract)
        return mdsGet(serial, TRAINING_MODE_URI, json)
    }

    override suspend fun getDefaultTrainingMode(
        serial: String,
        contract: TrainingModeItemContract
    ): TrainingModeResponse {
        val json = encodeMdsValue(contract)
        return mdsGet(serial, TRAINING_MODE_TEMPLATE_URI, json)
    }

    override suspend fun getDefaultDataScreens(
        serial: String,
        contract: TrainingModeBaseContract
    ): GetDataScreenResponse {
        val json = encodeMdsValue(contract)
        return mdsGet(serial, DATA_SCREEN_TEMPLATE_URI, json)
    }

    override suspend fun getDataScreens(
        serial: String,
        contract: TrainingModeItemContract
    ): GetDataScreenResponse {
        val json = encodeMdsValue(contract)
        return mdsGet(serial, DATA_SCREEN_URI, json)
    }

    override suspend fun createTrainingMode(
        serial: String,
        contract: SaveTrainingModeContract
    ) {
        val response = mdsRx.postWithHeader(
            String.format(TRAINING_MODE_URI, serial),
            encodeMdsValue(contract)
        ).await()
        if (response.statusCode != STATUS_OK) {
            throw MdsException("Failed to create training mode", response.statusCode)
        }
    }

    override suspend fun updateTrainingMode(
        serial: String,
        contract: SaveTrainingModeContract
    ) {
        val response = mdsRx.putWithHeader(
            String.format(TRAINING_MODE_URI, serial),
            encodeMdsValue(contract)
        ).await()
        if (response.statusCode != STATUS_OK) {
            throw MdsException("Failed to update training mode", response.statusCode)
        }
    }

    override suspend fun deleteTrainingMode(
        serial: String,
        contract: TrainingModeItemContract
    ) {
        mdsDelete(serial, TRAINING_MODE_URI, contract)
    }

    override suspend fun createDataScreen(
        serial: String,
        contract: DataScreenContract
    ) {
        mdsPost(serial, DATA_SCREEN_URI, contract)
    }

    override suspend fun updateDataScreen(
        serial: String,
        contract: DataScreenContract
    ) {
        mdsPut(serial, DATA_SCREEN_URI, contract)
    }

    override suspend fun deleteDataScreen(
        serial: String,
        contract: DataScreenContract
    ) {
        mdsDelete(serial, DATA_SCREEN_URI, contract)
    }

    override suspend fun putCompetitionInfoTarget(
        serial: String,
        contract: CompetitionTargetContract
    ) {
        mdsPut(serial, COMPETITION_TARGET_INFO_URI, contract)
    }

    override suspend fun putCompetitionSamplesTarget(serial: String, samples: List<Int>, distanceIncluded: Boolean) {
        val uri = String.format(if (distanceIncluded) COMPETITION_SPEED_DISTANCE_TIME_SAMPLES_URI else COMPETITION_TARGET_SAMPLES_URI, serial)
        val byteArray = samples.toLittleEndianByteArray()
        val byteStream = Base64.getEncoder().withoutPadding().encodeToString(byteArray)
        mdsRx.put(uri, encodeMdsValue(byteStream)).await()
    }

    private fun generateWithSportIdUri(
        serial: String,
        url: String,
        contract: SportItemContract
    ): String {
        val uri = String.format(url, serial)
        val withParamsUri = uri.toUri()
            .buildUpon()
            .appendQueryParameter("sportId", contract.sportId.toString())
            .apply {
                contract.lastModeId?.let {
                    appendQueryParameter("startAfterModeId", it.toString())
                }
                contract.sportTag?.let {
                    appendQueryParameter("sportTag", it.toString())
                }
            }
            .build()
            .toString()
        return withParamsUri
    }

    private fun generateWithSportTagUri(
        serial: String,
        url: String,
        withSportTag: Boolean
    ): String {
        val uri = String.format(url, serial)
        val withParamsUri = uri.toUri()
            .buildUpon()
            .appendQueryParameter("withSportTag", withSportTag.toString())
            .build()
            .toString()
        return withParamsUri
    }

    private inline fun <reified T> encodeMdsValue(value: T): String {
        val type = Types.newParameterizedType(
            MdsValue::class.java, T::class.java
        )
        val adapter = moshi.adapter<MdsValue<T>>(type)
        return adapter.toJson(MdsValue(value))
    }

    private inline fun <reified T> String.decodeMdsResponseWrappedInContent(): T {
        return run {
            val type = Types.newParameterizedType(
                MdsContent::class.java, T::class.java
            )
            val adapter = moshi.adapter<MdsContent<T>>(type)
            adapter.fromJson(this)?.content
        } ?: throw UnsupportedOperationException("Invalid json.")
    }

    private suspend inline fun <reified T> mdsGet(
        serial: String,
        url: String,
        contract: String
    ): T {
        val uri = String.format(url, serial)
        return mdsRx[uri, contract].await().decodeMdsResponseWrappedInContent<T>()
    }

    private suspend inline fun <reified T> mdsPut(serial: String, url: String, contract: T) {
        mdsRx.put(
            String.format(url, serial),
            encodeMdsValue(contract)
        ).await()
    }

    private suspend inline fun <reified T> mdsPost(serial: String, url: String, contract: T) {
        mdsRx.post(
            String.format(url, serial),
            encodeMdsValue(contract)
        ).await()
    }

    private suspend inline fun <reified T> mdsDelete(serial: String, url: String, contract: T) {
        mdsRx.delete(
            String.format(url, serial),
            encodeMdsValue(contract)
        ).await()
    }
}

fun List<Int>.toLittleEndianByteArray(): ByteArray {
    val byteArray = ByteArray(this.size * 4)
    forEachIndexed { index, value ->
        val offset = index * 4
        byteArray[offset] = (value and 0xFF).toByte()
        byteArray[offset + 1] = ((value shr 8) and 0xFF).toByte()
        byteArray[offset + 2] = ((value shr 16) and 0xFF).toByte()
        byteArray[offset + 3] = ((value shr 24) and 0xFF).toByte()
    }
    return byteArray
}
