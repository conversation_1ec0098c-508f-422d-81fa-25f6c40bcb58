package com.suunto.connectivity.ngBleManager;

import android.os.Handler;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import com.movesense.mds.BLEDelegate;
import com.movesense.mds.BLEWrapper;
import com.suunto.connectivity.legacy.LegacyBleCentral;
import com.suunto.connectivity.legacy.LegacyBleCentralListener;
import com.suunto.connectivity.sdsmanager.SdsBleAddressMap;
import com.suunto.connectivity.suuntoconnectivity.ble.BleCore;
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoDeviceType;
import com.suunto.connectivity.suuntoconnectivity.utils.ConnectMetadata;
import com.suunto.connectivity.util.DeviceSerialUtils;
import com.suunto.connectivity.util.DisconnectReason;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import rx.Completable;
import rx.Observable;
import rx.Single;
import rx.schedulers.Schedulers;
import rx.subjects.BehaviorSubject;
import rx.subjects.SerializedSubject;
import timber.log.Timber;

/**
 * 'Bottom' interface to Whiteboard
 */
public class NgBleManager implements BLEDelegate, NgBleCentralListener, LegacyBleCentralListener {

    private enum DeviceBleStates {
        NOT_SET,
        DEVICE_ABOUT_TO_CONNECT,
        DEVICE_FOUND,
        DEVICE_LOST,
    }

    private static class DeviceBleState {
        private enum Type {
            LEGACY,
            NON_LEGACY,
            UNKNOWN
        }
        @Nullable
        final String bleMac;
        final DeviceBleStates state;
        final Type deviceType;

        DeviceBleState(@Nullable String bleMac,
            DeviceBleStates state,
            Type deviceType) {
            this.bleMac = bleMac;
            this.state = state;
            this.deviceType = deviceType;
        }

        DeviceBleState() {
            bleMac = null;
            state = DeviceBleStates.NOT_SET;
            deviceType = Type.UNKNOWN;
        }
    }

    private static final int DEFAULT_RUNNABLE_DELAY_MS = 1;

    private final SdsBleAddressMap sdsBleAddressMap;
    private final BLEWrapper bleWrapper;
    // Connection towards Whiteboard, a counterpart to NGBLEDelegate
    private final NgBLECentral bleCentral;    // Connection towards BLE HW
    private final LegacyBleCentral legacyBleCentral;
    private final IncomingDataPacketizer incomingDataPacketizer;

    // Behaviour subject (serialized) for device BLE state. Behavioral subject is used in order
    // not to miss DEVICE_LOST state in case bleConnectionLostError is subscribed after
    // device is lost.
    private final SerializedSubject<DeviceBleState, DeviceBleState> deviceBleState =
        BehaviorSubject.create(new DeviceBleState()).toSerialized();

    private String wbAddress = "";
    // The address given by the WB, will translate to BLE MAC
    private volatile int handle = -1;
    // The address given by NgBLECentral, will translate to a BT device (and, thus,
    // ultimately to a BLE MAC)
    private Date connectStartTime;
    private final Handler ngBleManagerHandler;
    // The latest disconnect reason. Used for analytics.
    private DisconnectReason disconnectReason = DisconnectReason.Unknown;
    // Invalid packets detected when disconnected?. Used for analytics.
    private boolean invalidPacketDetectedWhenDisconnected = false;

    ///////////////////////////////////////////////////////////////////////////////////////////////
    // Initialization

    /**
     * Constructor
     */
    public NgBleManager(SdsBleAddressMap sdsBleAddressMap,
        NgBLECentral bleCentral, LegacyBleCentral legacyBleCentral,
        IncomingDataPacketizer incomingDataPacketizer, Handler ngBleManagerHandler) {
        this.sdsBleAddressMap = sdsBleAddressMap;
        this.incomingDataPacketizer = incomingDataPacketizer;
        this.ngBleManagerHandler = ngBleManagerHandler;
        this.bleWrapper = new BLEWrapper();
        this.bleWrapper.setDelegate(this);
        this.bleCentral = bleCentral;
        this.bleCentral.setNgBleCentralListener(this);
        this.legacyBleCentral = legacyBleCentral;
        this.legacyBleCentral.setLegacyBleCentralListener(this);
    }

    public static class StartDataNotifyError extends Exception {
        @Nullable
        final Exception bleException;

        @Nullable
        public Exception getBleException() {
            return bleException;
        }

        public StartDataNotifyError(Exception bleException) {
            this.bleException = bleException;
        }
    }

    public static class BleConnectionLostError extends RuntimeException {
    }

    /**
     * Is BLE already connected?
     *
     * @return True, if connected.
     */
    public boolean isAlreadyConnected() {
        return bleCentral.isConnected(handle);
    }

    /**
     * BLE connect and and block for deviceFound for this connect (device is bonded after this).
     * Notice, failing directConnect does not guarantee BLE disconnection. BLE disconnection must
     * be ensured by user of this method.
     *
     * @param uuid UUID for the device
     * @param deviceType Type of the device
     * @return Completable which will complete when the connection either succeeds or fails
     */
    public Completable directConnect(
        @NonNull final String uuid,
        SuuntoDeviceType deviceType,
        @NonNull ConnectMetadata connectMetadata) {
        // Get the bleMac from address table
        final String bleMac = sdsBleAddressMap.wbAddressToBleMac(uuid);
        if (bleMac == null) {
            Timber.e("directConnect: bleMac == null for wbAddress/uuid: %s. Connect failed!",
                uuid);
            return Completable.error(new NgBleManagerException("No bleMac found for uuid"));
        }

        // Is connection already done?
        if (isAlreadyConnected()) {
            return Completable.complete();
        }

        return bleConnectedToDevice(bleMac)
            .zipWith(
                bleCentral.connect(bleMac, deviceType, connectMetadata)
                    .flatMap(result -> {
                        if (result != BleCore.BLE_OK) {
                            Timber.e("directConnect: bleCentral.connect(\"%s\") returned NOT_OK! wbAddress was: %s", bleMac, uuid);
                            return Single.error(new NgBleManagerException(result, "Could not "
                                + "connect"));
                        } else {
                            return Single.just(DeviceBleState.Type.UNKNOWN);
                        }
                    })
                    .doOnError(throwable ->
                        Timber.e("directConnect: bleCentral.connect(\"%s\") returned exception %s. wbAddress was: %s",
                        bleMac, throwable.toString(), uuid)),
                (type, ignore) -> type
            )
            .flatMapCompletable(type -> startDataNotify(type)
                .subscribeOn(Schedulers.io()))
            .doOnCompleted(() -> Timber.v("directConnect: completed with device %s", bleMac))
            .doOnError(throwable -> Timber.e("directConnect failed with device %s", bleMac))
            .doOnSubscribe(subscription -> {
                // Reset disconnect reason, when starting connect.
                resetDisconnectReason();
                // Set device ble state to DEVICE_ABOUT_TO_CONNECT.
                deviceBleState.onNext(
                    new DeviceBleState(bleMac, DeviceBleStates.DEVICE_ABOUT_TO_CONNECT, null));
                // Store the current wbAddress
                wbAddress = uuid;
                // Call the connect
                Timber.v("directConnect: Calling bleCentral.connect(\"%s\"); wbAddress was: %s",
                    bleMac, uuid);
                connectStartTime = new Date();
            });
    }

    private void resetDisconnectReason() {
        this.disconnectReason = DisconnectReason.Unknown;
        invalidPacketDetectedWhenDisconnected =
            incomingDataPacketizer.isInvalidPacketDetected();
    }

    private void setDisconnectReasonUnlessKnown(DisconnectReason disconnectReason) {
        if (this.disconnectReason == DisconnectReason.Unknown) {
            this.disconnectReason = disconnectReason;
            invalidPacketDetectedWhenDisconnected =
                incomingDataPacketizer.isInvalidPacketDetected();
        }
    }

    public DisconnectReason getDisconnectReason() {
        return disconnectReason;
    }

    public boolean isInvalidPacketDetectedWhenDisconnected() {
        return invalidPacketDetectedWhenDisconnected;
    }

    /**
     * Disconnect ble to device. This is used by watch connector for ensuring BLE is disconnected
     * in case MDS fails to connect.
     *
     * @param macAddress Device mac address.
     */
    public void disconnectBle(String macAddress) {
        setDisconnectReasonUnlessKnown(DisconnectReason.DisconnectedByMobileApp);
        bleCentral.disconnect(macAddress);
    }

    ///////////////////////////////////////////////////////////////////////////////////////////////
    // Called from the BLE side

    /**
     * Indicates that a BLE device, with handle, has been found and bonded
     *
     * @param handle Handle for the device that was found
     */
    @Override
    public void deviceFound(final int handle) {
        final String bleMac = bleCentral.getPeripheralID(handle);
        this.handle = handle; // Set the current handle
        // Set device ble state to DEVICE_FOUND.
        deviceBleState.onNext(
            new DeviceBleState(bleMac, DeviceBleStates.DEVICE_FOUND, DeviceBleState.Type.NON_LEGACY));
    }

    private Completable startDataNotify(DeviceBleState.Type deviceType) {
        if (deviceType != DeviceBleState.Type.NON_LEGACY) {
            return Completable.complete();
        }
        final String bleMac = bleCentral.getPeripheralID(handle);
        return Completable.fromEmitter(completableEmitter -> {
            try {
                if (bleCentral.startDataNotify(handle).value() == BleCore.BLE_OK) {
                    delayRunnable(() -> {
                        Date date = new Date();
                        if (connectStartTime != null) {
                            Timber.v("deviceFound: Call bypassConnect: handle: %d, wbAddress: %s, elapsed: %dus",
                                handle, wbAddress,
                                (date.getTime() - connectStartTime.getTime()));
                        }
                        String uuid = "";
                        if (bleMac != null) {
                            uuid = Integer.toHexString(
                                sdsBleAddressMap.getBleHandle(
                                    bleMac)); // WB Address == WB side handle
                        }
                        Timber.v("deviceFound: Call bypassConnect: uuid: %s", uuid);
                        bleWrapper.bypassConnect(uuid);
                        completableEmitter.onCompleted();
                    });
                } else {
                    completableEmitter.onError(new StartDataNotifyError(null));
                }
            } catch (Exception e) {
                Timber.v("StartDataNotify failed on %s", e.toString());
                completableEmitter.onError(new StartDataNotifyError(e));
            }
        });
    }

    /**
     * Monitor ble connect.
     *
     * @param bleMac Mac address of the device to be monitored.
     * @return Completable completing when BLE connection to device exists.
     */
    public Completable bleConnected(final String bleMac) {
        return deviceBleState.filter(
            deviceBleState -> (deviceBleState.bleMac != null &&
                deviceBleState.bleMac.equals(bleMac)) &&
                deviceBleState.state == DeviceBleStates.DEVICE_FOUND)
            .first()
            .toCompletable();
    }

    /**
     * Monitor ble connect. Emits DeviceBleState.Type after connect.
     *
     * @param bleMac Mac address of the device to be monitored.
     * @return Single emitting DeviceBleState.Type after connect.
     */
    private Single<DeviceBleState.Type> bleConnectedToDevice(final String bleMac) {
        return deviceBleState.filter(
            deviceBleState -> (deviceBleState.bleMac != null &&
                deviceBleState.bleMac.equals(bleMac)) &&
                deviceBleState.state == DeviceBleStates.DEVICE_FOUND)
            .map(deviceBleState -> deviceBleState.deviceType)
            .first()
            .toSingle();
    }

    /**
     * Emits an exception BleConnectionLostError if device loses ble connection.
     *
     * @param bleMac Mac address of the device to be monitored.
     * @return An observable which will emit an BleConnectionLostError if device loses ble
     * connection.
     */
    public Observable<String> bleConnectionLostError(final String bleMac) {
        return deviceBleState.filter(
            deviceBleState -> (deviceBleState.bleMac != null &&
                deviceBleState.bleMac.equals(bleMac)) &&
                deviceBleState.state == DeviceBleStates.DEVICE_LOST)
            .map(deviceBleState -> deviceBleState.bleMac)
            .doOnNext(s -> {
                Timber.v("BleConnectionLostError");
                throw new BleConnectionLostError();
            });
    }

    /**
     * Emits true if ble connection is active.
     *
     * @return Single emitting true when ble connection is active.
     */
    public Single<Boolean> isBleOn() {
        return deviceBleState
            .map(deviceBleState -> deviceBleState.state == DeviceBleStates.DEVICE_FOUND)
            .first()
            .toSingle();
    }

    /**
     * Indicates that the BT has lost connection to remote device indicated by handle
     *
     * @param handle Handle of the device that was lost
     */
    @Override
    public void deviceLost(final int handle) {
        final String bleMac = bleCentral.getPeripheralID(handle);
        // Set device ble state to DEVICE_LOST.
        deviceBleState.onNext(
            new DeviceBleState(bleMac, DeviceBleStates.DEVICE_LOST,
                DeviceBleState.Type.NON_LEGACY));
        incomingDataPacketizer.clearAllPackets();
        Timber.v("deviceLost: Device lost with handle: %d", handle);
        this.handle = -1; // Unset the current handle
        setDisconnectReasonUnlessKnown(DisconnectReason.ConnectionLost);
        delayRunnable(() -> {
            Timber.v("deviceLost: Call bypassDisconnect: wbAddress: %s", wbAddress);
            String uuid = "";
            if (bleMac != null) {
                uuid = Integer.toHexString(
                    sdsBleAddressMap.getBleHandle(bleMac)); // WB Address == WB side handle
            }
            bleWrapper.bypassDisconnect(uuid);
            // bleWrapper.disconnectCompleted(wbAddress, true);
        });
    }

    /**
     * BLE has completed sending the data given in sendCb
     *
     * @param wbLocalDataPointer Pointer to the send that was completed
     * @param success TODO: Calling bleWrapper.sendCompleted DISABLED. Should be refactored away
     * once confirmed to work.
     */
    @Override
    public void dataSent(final long wbLocalDataPointer, final boolean success) {
        // ..
    }

    /**
     * Indicates that the BLE device has data to pass to the WB connection
     *
     * @param handle Handle for the data
     */
    @Override
    public void dataAvailableCallback(int handle) {
        byte[] data = bleCentral.getData(handle);
        if (data != null) {
            incomingDataPacketizer.addData(data);
            byte[] decodedData = incomingDataPacketizer.fetchPacket(true);
            while (decodedData != null) {
                if (decodedData.length > 1) { // A successful decode
                    Timber.v("dataAvailableCallback: handle: %d, decoded: %s",
                        handle, wbPacketDemystifier(decodedData));
                    bleWrapper.dataReceived(wbAddress, decodedData, decodedData.length);
                }
                decodedData = incomingDataPacketizer.fetchPacket(true);
            }
        } else {
            Timber.d("dataAvailableCallback: bleCentral.getData returned null");
        }
    }

    /**
     * Analytics method. Has there been invalid packet(s) since clearing the the invalid packets
     * detected state.
     *
     * @return True, if invalid packets detected.
     */
    public boolean isInvalidPacketDetected() {
        return incomingDataPacketizer.isInvalidPacketDetected();
    }

    /**
     * Clear invalid packets detected state.
     */
    public void clearInvalidPacketDetectedState() {
        incomingDataPacketizer.clearInvalidPacketDetectedState();
    }

    ///////////////////////////////////////////////////////////////////////////////////////////////
    // Called from the WB side

    /**
     * WB asks to connect to local BLE device
     *
     * @param wbAddress WB address
     * @return True if connection establishment was started, false otherwise
     */
    @Override
    public boolean connectCb(final String wbAddress) {
        Timber.v("connectCb: Returning immediately and calling connectCompleted... wbAddress: %s",
            wbAddress);

        delayRunnable(() -> {
            Timber.v(
                "connectCb:delayDummyConnect: Call bleWrapper.connectCompleted. address: %s, true",
                wbAddress);
            bleWrapper.connectCompleted(wbAddress, true);
        });

        return true;
    }

    /**
     * WB asks to cancel the (in progress) bonding process to the local BLE device
     *
     * @param wbAddress WB address
     * @return True if connection cancel was started, false otherwise
     */
    @Override
    public boolean cancelConnectCb(String wbAddress) {
        Timber.v("cancelConnectCb: Currently supported as a disconnect. Always return true. wbAddress: %s",
            wbAddress);
        String bleMac = sdsBleAddressMap.wbAddressToBleMac(wbAddress);
        if (bleMac != null) {
            Timber.v("cancelConnectCb: Calling bleCentral.disconnect(\"%s\"); wbAddress was: %s",
                bleMac, wbAddress);
            int bleOk = bleCentral.disconnect(bleMac);
            Timber.v("cancelConnectCb: Done calling bleCentral.disconnect(\"%s\"); bleOk: %s",
                bleMac, (bleOk == BleCore.BLE_OK ? "OK" : "NOT_OK"));
        } else {
            Timber.e("cancelConnectCb: bleMac == null for wbAddress: %s", wbAddress);
            return false;
        }
        return true;
    }

    /**
     * WB asks to disconnect from a local BLE device
     *
     * @param wbAddress WB address
     * @return True if disconnection was started, false otherwise
     */
    @Override
    public boolean disconnectCb(final String wbAddress) {
        String bleMac = sdsBleAddressMap.wbAddressToBleMac(wbAddress);
        if (bleMac != null) {
            setDisconnectReasonUnlessKnown(DisconnectReason.DisconnectedByMiddleware);
            Timber.v("disconnectCb: Calling bleCentral.disconnect(\"%s\"); wbAddress was: %s",
                bleMac, wbAddress);
            int bleOk = bleCentral.disconnect(bleMac);
            Timber.v("disconnectCb: Done calling bleCentral.disconnect(\"%s\"); bleOk: %s",
                bleMac, (bleOk == BleCore.BLE_OK ? "OK" : "NOT_OK"));
            if (bleOk
                == BleCore.BLE_OK) { // If we are returning false from this function, the
                // disconnectCompleted should not be called
                delayRunnable(() -> {
                    Timber.v(
                        "disconnectCb: Call bleWrapper.disconnectCompleted. address: %s, true",
                        wbAddress);
                    bleWrapper.disconnectCompleted(wbAddress, true);
                });
            }
            return (bleOk
                == BleCore.BLE_OK); // Anything else is an error and should propagate back to the WB
        } else {
            Timber.e("disconnectCb: bleMac == null for wbAddress: %s", wbAddress);
            return false;
        }
    }

    /**
     * WB asks to send data to a local BLE device
     *
     * @param wbAddress Address to which send data
     * @param wbData Data to send
     * @param wbSize Size of the data to send
     * @param wbLocalDataPointer Pointer which identifies this send for possible cancelling later
     * @return True if send started
     */
    @Override
    public boolean sendCb(String wbAddress, byte[] wbData, int wbSize, long wbLocalDataPointer) {
        String bleMac = sdsBleAddressMap.wbAddressToBleMac(wbAddress);
        if (bleMac == null) {
            Timber.e("sendCb: bleMac == null for wbAddress: %s, wbData: %s",
                wbAddress, wbPacketDemystifier(wbData));
        }
        Timber.d("write data: %s", wbPacketDemystifier(wbData));
        int bleOk =
            bleCentral.dataWrite(handle, SuuntoSerialFrame.encode(wbData), wbLocalDataPointer);
        return (bleOk
            == BleCore.BLE_OK); // Anything else is an error and should propagate back to the WB
    }

    /**
     * WB asks to cancel the ongoing data write to a local BLE device
     *
     * @param wbLocalDataPointer Pointer to the send that should be cancelled
     * @return True if cancelling the send was successful
     */
    @Override
    public boolean cancelSendCb(
        long wbLocalDataPointer) { // true if sending was canceled, false otherwise
        Timber.v("cancelSendCb: Not really supported. Return true. wbLocalDataPointer: 0x%s",
            Long.toHexString(wbLocalDataPointer));
        return true;
    }

    /**
     * Given a deviceId, return a wbAddress to Whiteboard
     *
     * @param wbDeviceId Device id to get address for
     * @return Address of the device with given id
     */
    @Override
    public BLEWrapper.WbAddress deviceToWhiteboardCb(int wbDeviceId) {
        Timber.v("deviceToWhiteboardCb: wbDeviceId: %d", wbDeviceId);
        return new BLEWrapper.WbAddress(Integer.toHexString(wbDeviceId));
    }

    /**
     * Return all devices with a valid BLE connection
     * Implements the WB Enumerate functionality
     *
     * @return A Java Object that contains a String Array. In the Array, each device is a triplet:
     * uuid, serial, name
     */
    @Override
    public BLEWrapper.ConnectedDevices getConnectedBleDevicesCb() {
        List<String> devicesList =
            new ArrayList<>();        // Collect the connected BLE devices here
        int[] handles = bleCentral.getPeripheralHandles();  // Get the BLE side handles
        try {
            for (int handle : handles) {
                if (!bleCentral.isConnected(
                    handle)) { // If the handle/device is not connected, skip
                    Timber.v("getConnectedBleDevicesCb: Skipping non-connected device: %d",
                        handle);
                    continue;
                }
                String name = bleCentral.getPeripheralName(handle);
                String bleMac = bleCentral.getPeripheralID(handle);
                String uuid = "";
                if (bleMac != null) {
                    uuid = Integer.toHexString(
                        sdsBleAddressMap.getBleHandle(bleMac)); // WB Address == WB side handle
                } else {
                    Timber.w("getConnectedBleDevicesCb: bleMac == null for handle: %d",
                        handle);
                }

                String serial = DeviceSerialUtils.getVisibleSerial(name);
                if (serial != null
                    && name != null
                    && !uuid.isEmpty()
                    && !name.isEmpty()) {
                    devicesList.add(uuid);
                    devicesList.add(serial);
                    devicesList.add(name);
                } else {
                    Timber.w("getConnectedBleDevicesCb: Invalid device info: handle: %d, uuid: %s, serial: %s, name: %s",
                        handle, uuid, (serial == null ? "null" : serial),
                        (name == null ? "null" : name));
                }
            }
        } catch (Exception ex) {
            Timber.e("getConnectedBleDevicesCb: Exception: %s", ex.toString());
            String[] emptyArray = {};
            return new BLEWrapper.ConnectedDevices(emptyArray);
        }
        Timber.v("getConnectedBleDevicesCb: Found connected devices: %s",
            devicesList.toString());
        return new BLEWrapper.ConnectedDevices(devicesList.toArray(new String[devicesList.size()]));
    }

    /**
     * Given a wbAddress, return a deviceId to Whiteboard
     *
     * @param wbAddress WB address of the device
     * @return Device ID as an int
     */
    @Override
    public int whiteboardToDevice(String wbAddress) {
        Timber.v("whiteboardToDevice: wbAddress: %s", wbAddress);
        return Integer.valueOf(wbAddress, 16);
    }

    private enum WbMessageType {
        WB_MSG_INVALID(0),
        WB_DATAMSG_CLIENT_ON_NOTIFY(1),
        WB_DATAMSG_CLIENT_ON_GET_RESOURCE_RESULT(2),
        WB_DATAMSG_CLIENT_ON_RELEASE_RESOURCE_RESULT(3),
        DEPRECATED_WB_DATAMSG_CLIENT_ON_GET_RESOURCE_METADATA_RESULT(4),
        WB_DATAMSG_CLIENT_ON_GET_RESULT(5),
        WB_DATAMSG_CLIENT_ON_GET_STREAM(6),
        WB_DATAMSG_CLIENT_ON_PUT_RESULT(7),
        WB_DATAMSG_CLIENT_ON_SUBSCRIBE_RESULT(8),
        WB_DATAMSG_CLIENT_ON_UNSUBSCRIBE_RESULT(9),
        WB_DATAMSG_RESOURCE_GET_REQUEST(10),
        WB_DATAMSG_RESOURCE_RELEASE_REQUEST(11),
        DEPRECATED_WB_DATAMSG_RESOURCE_GET_METADATA_REQUEST(12),
        WB_DATAMSG_PROVIDER_GET_REQUEST(13),
        WB_DATAMSG_PROVIDER_PUT_REQUEST(14),
        DEPRECATED_WB_DATAMSG_PROVIDER_PUT_STREAM(15),
        WB_DATAMSG_PROVIDER_SUBSCRIBE_REQUEST(16),
        WB_DATAMSG_PROVIDER_UNSUBSCRIBE_REQUEST(17),
        WB_COMMMSG_HELLO(18),
        WB_COMMMSG_HELLO_ACK(19),
        WB_COMMMSG_KEEP_ALIVE(20),
        NOT_USED_2(21),
        // Thid was used by initial timer implementation, but was not ever transferred over the
        // network
        WB_DATAMSG_PROVIDER_POST_REQUEST(22),
        WB_DATAMSG_PROVIDER_DELETE_REQUEST(23),
        WB_DATAMSG_CLIENT_ON_POST_RESULT(24),
        WB_DATAMSG_CLIENT_ON_DELETE_RESULT(25),
        WB_DATAMSG_DONT_UNDERSTAND(26),
        UNKNOWN(99);

        private final int value;

        WbMessageType(int v) {
            this.value = v;
        }

        public static WbMessageType fromInt(int v) {
            for (WbMessageType i : WbMessageType.values()) {
                if (i.value == v) {
                    return i;
                }
            }
            return WbMessageType.UNKNOWN;
        }
    }

    /**
     * Provide a more human readable interpretation of a WB packet.
     * Debug/logging only!
     *
     * @param wbData Data to demystify
     * @return Demystified String
     */
    private String wbPacketDemystifier(byte[] wbData) {
        if (wbData == null) {
            return "Null packet";
        }
        if (wbData[0] != (byte) 0xA5) { // 0xA5 is a magic number for WB packets
            return "Not a valid packet. Does not start with 0xA5";
        }
        if (wbData.length < 2) {
            return "Empty packet";
        }
        if (wbData.length < 4) {
            return "Packet too short to be meaningful";
        }
        boolean routed;
        routed = ((wbData[1] & 0x80) == 0x80);
        int type = wbData[1] & 0x7F;
        String result = String.format(Locale.US, "%s, type: %d, %s msg",
            WbMessageType.fromInt(type).toString(), type, (routed ? "routed" : "direct"));

        int msgLen = (wbData[3] << 8) + wbData[2];
        int requestId = 0;
        if (wbData.length > 4) {
            requestId = ((wbData[4] << 8) + wbData[5]) & 0x0000ffff;
        }

        result = String.format(Locale.US, "%s, msgLen: %d, reqId: %d, size: %d", result,
            msgLen, requestId, wbData.length);
        return result;
    }

    private void delayRunnable(final Runnable runnable) {
        ngBleManagerHandler.postDelayed(runnable, DEFAULT_RUNNABLE_DELAY_MS);
    }

    @Override
    public void legacyDeviceFound(int handle) {
        Timber.v("legacyDeviceFound");
        this.handle = handle;
        final String bleMac = legacyBleCentral.getPeripheralID(handle);
        deviceBleState.onNext(new DeviceBleState(bleMac, DeviceBleStates.DEVICE_FOUND,
            DeviceBleState.Type.LEGACY));
    }

    @Override
    public void legacyDeviceLost(int handle, boolean byCommand) {
        Timber.v("legacyDeviceLost");
        if (byCommand) {
            setDisconnectReasonUnlessKnown(DisconnectReason.DisconnectedByMiddleware);
        } else {
            setDisconnectReasonUnlessKnown(DisconnectReason.ConnectionLost);
        }
        final String bleMac = legacyBleCentral.getPeripheralID(handle);
        deviceBleState.onNext(new DeviceBleState(bleMac, DeviceBleStates.DEVICE_LOST,
            DeviceBleState.Type.LEGACY));
    }
}
