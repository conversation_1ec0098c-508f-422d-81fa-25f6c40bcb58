package com.suunto.connectivity.watch;

import android.util.Pair;
import androidx.annotation.Nullable;
import com.google.gson.Gson;
import com.google.gson.JsonParseException;
import com.stt.android.analytics.AnalyticsEvent;
import com.stt.android.utils.TraceKt;
import com.suunto.connectivity.capabilities.SuuntoWatchCapabilities;
import com.suunto.connectivity.deviceid.SuuntoDeviceCapabilityInfoProvider;
import com.suunto.connectivity.logbook.Logbook;
import com.suunto.connectivity.logbook.json.LogbookEntriesJson;
import com.suunto.connectivity.logbook.json.LogbookEntryJson;
import com.suunto.connectivity.poi.POIResource;
import com.suunto.connectivity.recovery.RecoveryDataResource;
import com.suunto.connectivity.repository.LogbookEntrySyncResult;
import com.suunto.connectivity.repository.LogbookSyncResult;
import com.suunto.connectivity.repository.SingleLogbookEntrySyncResultEvent;
import com.suunto.connectivity.repository.SyncResult;
import com.suunto.connectivity.routes.RouteResource;
import com.suunto.connectivity.sdsmanager.model.MdsDeviceInfo;
import com.suunto.connectivity.settings.SettingsResource;
import com.suunto.connectivity.sleep.SleepResource;
import com.suunto.connectivity.suuntoplusguides.CurrentStateBusyStateProvider;
import com.suunto.connectivity.suuntoplusguides.SuuntoPlusGuideResource;
import com.suunto.connectivity.sync.AlreadySynchronizingException;
import com.suunto.connectivity.sync.SyncState;
import com.suunto.connectivity.sync.SynchronizerStorage;
import com.suunto.connectivity.sync.WatchBusyException;
import com.suunto.connectivity.sync.WatchNotConnectedException;
import com.suunto.connectivity.sync.WatchSynchronizeException;
import com.suunto.connectivity.sync.WatchSynchronizer;
import com.suunto.connectivity.systemevents.SystemEventsResource;
import com.suunto.connectivity.trainingzone.TrainingZoneResource;
import com.suunto.connectivity.trenddata.TrendDataResource;
import com.suunto.connectivity.util.SupportedDevices;
import com.suunto.connectivity.weather.WeatherResource;
import com.suunto.connectivity.zonesense.ZoneSenseResource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import kotlin.Triple;
import rx.Completable;
import rx.Observable;
import rx.Single;
import rx.exceptions.Exceptions;
import rx.functions.Func1;
import rx.schedulers.Schedulers;
import timber.log.Timber;

/*
 * Base class for WatchSynchronizer classes that has the common functionality for all watches
 */
public abstract class WatchSynchronizerBase implements WatchSynchronizer {

    /**
     * Facilitates injection in the parent class.
     * TODO: This is just a temporary workaround until we have proper Dagger setup.
     */
    public static abstract class InjectionBase {
        @Nullable
        private final POIResource poiResource;

        @Nullable
        protected final RouteResource routeResource;

        @Nullable
        protected final RecoveryDataResource recoveryDataResource;

        @Nullable
        protected final SleepResource sleepResource;

        @Nullable
        protected final SystemEventsResource systemEventsResource;

        @Nullable
        protected final TrendDataResource trendDataResource;

        @Nullable
        private final SuuntoPlusGuideResource suuntoPlusGuideResource;

        @Nullable
        private final WeatherResource weatherResource;

        @Nullable
        private final TrainingZoneResource trainingZoneResource;

        @Nullable
        private final SettingsResource settingsResource;

        @Nullable
        private final ZoneSenseResource zoneSenseResource;

        public InjectionBase() {
            this.poiResource = null;
            this.routeResource = null;
            this.recoveryDataResource = null;
            this.sleepResource = null;
            this.systemEventsResource = null;
            this.trendDataResource = null;
            this.suuntoPlusGuideResource = null;
            this.weatherResource = null;
            this.trainingZoneResource = null;
            this.settingsResource = null;
            this.zoneSenseResource = null;
        }

        public InjectionBase(
            @Nullable POIResource poiResource,
            @Nullable RouteResource routeResource,
            @Nullable SuuntoPlusGuideResource suuntoPlusGuideResource,
            @Nullable WeatherResource weatherResource,
            @Nullable TrainingZoneResource trainingZoneResource,
            @Nullable SettingsResource settingsResource,
            @Nullable ZoneSenseResource zoneSenseResource
        ) {
            this.poiResource = poiResource;
            this.routeResource = routeResource;
            this.suuntoPlusGuideResource = suuntoPlusGuideResource;
            this.recoveryDataResource = null;
            this.sleepResource = null;
            this.systemEventsResource = null;
            this.trendDataResource = null;
            this.weatherResource = weatherResource;
            this.trainingZoneResource = trainingZoneResource;
            this.settingsResource = settingsResource;
            this.zoneSenseResource = zoneSenseResource;
        }
    }

    public static final String CANNOT_STORE_ERROR = "Could not store json to filesystem";
    public static final String MDS_RESPONSE_ERROR = "MDS error: unhandled response status";
    public static final String MDS_RESPONSE_INVALID_TIMESTAMP = "MDS error: invalid timestamp: %d";

    private static final String CANNOT_CONVERT_JSON = "Could not convert json to proper object";

    private final WatchBt watchBt;
    private final Gson gson;
    private final InjectionBase injection;

    protected final SynchronizerStorage synchronizerStorage;
    protected final AtomicInteger currentState;
    protected final SupportedDevices supportedDevices;
    @Nullable
    private ImportedEntry importedEntry;

    @Nullable
    private WatchSynchronizerListener listener;

    WatchSynchronizerBase(
        WatchBt watchBt,
        Gson gson,
        SynchronizerStorage synchronizerStorage,
        InjectionBase injection,
        SupportedDevices supportedDevices
    ) {
        this.watchBt = watchBt;
        this.gson = gson;
        this.synchronizerStorage = synchronizerStorage;
        this.currentState = new AtomicInteger(SyncState.NOT_SYNCING);
        this.injection = injection;
        this.supportedDevices = supportedDevices;
    }

    String getWatchSerial() {
        return watchBt.getSuuntoBtDevice().getSerial();
    }

    @Override
    public void setWatchSynchronizerListener(WatchSynchronizerListener listener) {
        this.listener = listener;
    }

    @Override
    public void removeWatchSynchronizerListener() {
        this.listener = null;
    }

    @Override
    public boolean isSynchronizing() {
        return currentState.get() != SyncState.NOT_SYNCING;
    }

    @Override
    public Single<SpartanSyncResult> synchronize(boolean isActivityDataOnly, boolean foreground) {
        return isActivityDataOnly ? partialSync() : foreground ? fullSync() : backgroundSync();
    }

    @Override
    public Completable onInitialConnectCompletable() {
        POIResource poiResource = injection.poiResource;
        Completable preparePois = poiResource != null
            ? poiResource.prepareAfterInitialConnect()
            : Completable.complete();

        RouteResource routeResource = injection.routeResource;
        Completable prepareRoutes = routeResource != null
            ? routeResource.prepareAfterInitialConnect()
            : Completable.complete();

        TrainingZoneResource trainingZoneResource = injection.trainingZoneResource;
        Completable prepareTrainingZone = trainingZoneResource != null
            ? Completable.fromAction(() -> trainingZoneResource.clearPrefs(getWatchSerial()))
            : Completable.complete();

        ZoneSenseResource zoneSenseResource = injection.zoneSenseResource;
        Completable prepareZoneSense = zoneSenseResource != null
            ? Completable.fromAction(() -> zoneSenseResource.clearPrefs(getWatchSerial()))
            : Completable.complete();

        SettingsResource settingsResource = injection.settingsResource;
        Completable prepareSettings = settingsResource != null
            ? Completable.fromAction(() -> settingsResource.clearPrefs(getWatchSerial()))
            : Completable.complete();

        return preparePois
            .andThen(prepareRoutes)
            .andThen(prepareTrainingZone)
            .andThen(prepareZoneSense)
            .andThen(prepareSettings);
    }

    public Boolean supportsClimbGuidance() {
        return supportedDevices.supportsClimbGuidance(watchBt);
    }

    public int getMaxRoutePointsAllowed() {
        return supportedDevices.maxRoutePointsAllowed(watchBt);
    }

    @Override
    public Completable explicitSyncRoutes(String navigateRouteId) {
        boolean supportsRouteSync = supportedDevices.supportsRouteSync(watchBt);
        boolean supportsRouteSyncInBusyState = supportedDevices.supportsRouteSyncInBusyState(watchBt);
        boolean supportsTurnByTurnWaypoints = supportedDevices.supportsTurnByTurnWaypoints(watchBt);
        boolean supportsClimbGuidance = supportedDevices.supportsClimbGuidance(watchBt);
        boolean supportsDeleteRoute = supportedDevices.supportsDeleteRoute(watchBt);
        int maxRoutePointsAllowed = supportedDevices.maxRoutePointsAllowed(watchBt);
        SynchronizationAnalytics analytics = new SynchronizationAnalytics(watchBt);

        if (!supportsRouteSync) {
            // skip
            return Completable.complete();
        }
        Timber.d("Triggering explicit route sync");
        Completable routeSyncCompletable = Completable.complete();

        RouteResource routeResource = injection.routeResource;

        if (routeResource == null) {
            return routeSyncCompletable;
        }

        final SpartanSyncResult.Builder builder = SpartanSyncResult.builder()
            .syncStartTimestamp(System.currentTimeMillis())
            .macAddress(watchBt.getSuuntoBtDevice().getMacAddress());
        routeSyncCompletable = ensureConnected()
            .andThen(ensureIsNotSyncing())
            .doOnCompleted(() -> setSyncState(new SyncState(SyncState.SYNCING_ROUTES)))
            .andThen(routeResource.sync(
                getWatchSerial(),
                supportsTurnByTurnWaypoints,
                supportsClimbGuidance,
                navigateRouteId,
                supportsDeleteRoute,
                maxRoutePointsAllowed,
                builder
            ))
            .doOnCompleted(() -> setSyncState(new SyncState(SyncState.NOT_SYNCING)));

        // if watch doesn't support route sync in busy state we also append that condition
        if (!supportsRouteSyncInBusyState) {
            routeSyncCompletable = ensureIsNotBusy().andThen(routeSyncCompletable);
        }

        return routeSyncCompletable
            .onErrorResumeNext(e -> {
                // if we were syncing routes and were not interrupted by another ongoing sync,
                // then we reset state
                if (currentState.get() == SyncState.SYNCING_ROUTES &&
                    !(e instanceof AlreadySynchronizingException)) {
                    setSyncState(new SyncState(SyncState.NOT_SYNCING));
                }
                return Completable.error(e);
            })
            .doOnCompleted(
                () -> analytics.sendSyncResult(AnalyticsEvent.SUUNTO_SYNC_FOREGROUND_METHOD,
                    builder.build()));
    }

    protected Completable syncRoutes(SpartanSyncResult.Builder builder) {
        RouteResource routeResource = injection.routeResource;
        boolean supportsRouteSync = supportedDevices.supportsRouteSync(watchBt);
        boolean supportsTurnByTurnWaypoints = supportedDevices.supportsTurnByTurnWaypoints(watchBt);
        boolean supportsClimbGuidance = supportedDevices.supportsClimbGuidance(watchBt);
        boolean supportsDeleteRoute = supportedDevices.supportsDeleteRoute(watchBt);
        int maxRoutePointsAllowed = supportedDevices.maxRoutePointsAllowed(watchBt);
        if (routeResource == null || !supportsRouteSync) {
            builder.routeResult(RouteSyncResult.skipped());
            return Completable.complete();
        }
        // this route sync is during watch sync, we check if backend sync is needed
        return routeResource.sync(
                getWatchSerial(),
                supportsTurnByTurnWaypoints,
                supportsClimbGuidance,
                null,
                supportsDeleteRoute,
                maxRoutePointsAllowed,
                builder)
            .compose(checkBusyState())
            .doOnSubscribe(s -> setSyncState(routeResource.getSyncState()));
    }

    protected Completable syncPOIs(SpartanSyncResult.Builder builder) {
        POIResource poiResource = injection.poiResource;
        boolean supportsPOISync = supportedDevices.supportsPOISync(watchBt);
        if (poiResource == null || !supportsPOISync) {
            builder.poiResult(POISyncResult.skipped());
            return Completable.complete();
        }
        // this POI sync is during watch sync, we check if backend sync is needed
        return poiResource.sync(getWatchSerial(), builder, true)
            .compose(checkBusyState())
            .doOnSubscribe(s -> setSyncState(poiResource.getSyncState()));
    }

    protected Completable syncSuuntoPlusGuides(SpartanSyncResult.Builder builder) {
        SuuntoPlusGuideResource suuntoPlusGuideResource = injection.suuntoPlusGuideResource;
        if (suuntoPlusGuideResource == null) {
            Timber.w("No SuuntoPlusGuideResource: cannot sync SuuntoPlus™ guides");
            return Completable.complete();
        } else {
            // Defer getting device info until later in the sync process
            return Completable.defer(() -> {
                MdsDeviceInfo deviceInfo = watchBt.getCurrentState().getDeviceInfo();
                if (deviceInfo == null) {
                    builder.suuntoPlusGuidesResult(SuuntoPlusGuidesSyncResult.skipped());
                    Timber.w("No device info: cannot sync SuuntoPlus™ guides");
                    return Completable.complete();
                } else {
                    return suuntoPlusGuideResource.sync(
                            deviceInfo.getSerial(),
                            deviceInfo.getVariant(),
                            new SuuntoWatchCapabilities(deviceInfo.getCapabilities()),
                            new CurrentStateBusyStateProvider(watchBt),
                            builder
                        )
                        .compose(checkBusyState())
                        .doOnSubscribe(s -> setSyncState(suuntoPlusGuideResource.getSyncState()));
                }
            });
        }
    }

    protected Completable syncWeather(SpartanSyncResult.Builder builder) {
        WeatherResource weatherResource = injection.weatherResource;
        if (weatherResource == null) {
            Timber.w("No WeatherResource: cannot sync weather");
            return Completable.complete();
        } else {
            if (!supportsWeather()) {
                builder.weatherResult(SyncStepResult.skipped());
                Timber.i("Device doesn't support weather: cannot sync Weather");
                return Completable.complete();
            }
            Timber.i("Request to sync weather");
            return weatherResource
                .sync(watchBt, builder)
                .doOnSubscribe(s -> setSyncState(weatherResource.getSyncState()));
        }
    }

    protected Completable syncZoneSense(SpartanSyncResult.Builder builder) {
        ZoneSenseResource zoneSenseResource = injection.zoneSenseResource;
        if (zoneSenseResource == null) {
            Timber.w("No ZoneSenseResource: cannot sync zone sense");
            return Completable.complete();
        } else {
            if (!supportsZoneSense()) {
                builder.zoneSenseResult(ZoneSenseSyncResult.skipped());
                Timber.i("Device doesn't support zone sense: cannot sync");
                return Completable.complete();
            }
            Timber.i("Request to sync zone sense");
            return zoneSenseResource
                .sync(watchBt, builder)
                .doOnSubscribe(s -> setSyncState(zoneSenseResource.getSyncState()));
        }
    }

    private boolean supportsWeather() {
        MdsDeviceInfo deviceInfo = watchBt.getCurrentState().getDeviceInfo();
        if (deviceInfo == null) {
            return false;
        }
        return SuuntoDeviceCapabilityInfoProvider.get(
                watchBt.getSuuntoBtDevice().getDeviceType())
            .supportsWeather(deviceInfo.getCapabilities());
    }

    private boolean supportsZoneSense() {
        MdsDeviceInfo deviceInfo = watchBt.getCurrentState().getDeviceInfo();
        if (deviceInfo == null) {
            return false;
        }
        return SuuntoDeviceCapabilityInfoProvider.get(
                watchBt.getSuuntoBtDevice().getDeviceType())
            .supportsZoneSenseSync(deviceInfo.getCapabilities());
    }

    @Override
    public Completable explicitSyncPOIs() {
        POIResource poiResource = injection.poiResource;
        boolean supportsPOISync = supportedDevices.supportsPOISync(watchBt);
        if (poiResource == null || !supportsPOISync) {
            return Completable.complete();
        }
        final SpartanSyncResult.Builder builder = SpartanSyncResult.builder()
            .syncStartTimestamp(System.currentTimeMillis())
            .macAddress(watchBt.getSuuntoBtDevice().getMacAddress());

        return ensureConnected()
            .andThen(ensureIsNotSyncing())
            .andThen(ensureIsNotBusy())
            .doOnCompleted(() -> setSyncState(new SyncState(SyncState.SYNCING_POIS)))
            // this POI sync is triggered from the backend sync so no need to check if backend sync is needed
            .andThen(poiResource.sync(getWatchSerial(), builder, false))
            .doOnCompleted(() -> setSyncState(new SyncState(SyncState.NOT_SYNCING)))
            .onErrorResumeNext(e -> {
                // if we were syncing pois and were not interrupted by another ongoing sync, then we reset state
                if (currentState.get() == SyncState.SYNCING_POIS &&
                    !(e instanceof AlreadySynchronizingException)) {
                    setSyncState(new SyncState(SyncState.NOT_SYNCING));
                }
                return Completable.error(e);
            });
    }

    @Override
    public Single<SpartanSyncResult> explicitSync247() {
        Timber.d("Triggering explicit 247 sync");
        AtomicBoolean terminated = new AtomicBoolean(false);

        final SpartanSyncResult.Builder builder = SpartanSyncResult.builder()
            .syncStartTimestamp(System.currentTimeMillis())
            .macAddress(watchBt.getSuuntoBtDevice().getMacAddress());
        Completable activityDataCompletable = activityDataSyncCompletable(builder);
        return ensureConnected()
            .andThen(ensureIsNotSyncing())
            .andThen(ensureIsNotBusy())
            .andThen(activityDataCompletable)
            .doOnCompleted(() -> setSyncState(new SyncState(SyncState.NOT_SYNCING)))
            .onErrorResumeNext(e -> {
                terminated.set(true);
                boolean is247SyncState = currentState.get() == SyncState.SYNCING_TREND_DATA
                    || currentState.get() == SyncState.SYNCING_RECOVERY_DATA
                    || currentState.get() == SyncState.SYNCING_SLEEP;
                // if we were syncing 247 data and were not interrupted by another ongoing sync, then we reset state
                if (is247SyncState && !(e instanceof AlreadySynchronizingException)) {
                    Timber.w("Explicit 247 sync failed. Setting sync state to NOT_SYNCING");
                    setSyncState(new SyncState(SyncState.NOT_SYNCING));
                }
                Timber.w(e, "Explicit 247 sync failed. Sync state: %d", currentState.get());
                return Completable.error(e);
            })
            .doOnUnsubscribe(() -> {
                if (!terminated.get()) {
                    // Cancellation happened (no terminal event occurred)
                    setSyncState(new SyncState(SyncState.NOT_SYNCING));
                }
            })
            .toSingle(() -> builder.syncEndTimestamp(System.currentTimeMillis()).build());
    }

    @Override
    public Completable explicitSyncSuuntoPlusGuides() {
        SuuntoPlusGuideResource suuntoPlusGuideResource = injection.suuntoPlusGuideResource;
        MdsDeviceInfo deviceInfo = watchBt.getCurrentState().getDeviceInfo();
        if (suuntoPlusGuideResource == null || deviceInfo == null) {
            return Completable.complete();
        }

        final SpartanSyncResult.Builder builder = SpartanSyncResult.builder()
            .syncStartTimestamp(System.currentTimeMillis())
            .macAddress(watchBt.getSuuntoBtDevice().getMacAddress());

        return ensureConnected()
            .andThen(ensureIsNotSyncing())
            .andThen(ensureIsNotBusy())
            .doOnCompleted(() -> setSyncState(new SyncState(SyncState.SYNCING_SUUNTO_PLUS_GUIDES)))
            .andThen(suuntoPlusGuideResource.sync(
                deviceInfo.getSerial(),
                deviceInfo.getVariant(),
                new SuuntoWatchCapabilities(deviceInfo.getCapabilities()),
                new CurrentStateBusyStateProvider(watchBt),
                builder
            ))
            .doOnCompleted(() -> setSyncState(new SyncState(SyncState.NOT_SYNCING)))
            .onErrorResumeNext(e -> {
                // if we were syncing SuuntoPlus guides and were not interrupted by another ongoing sync, then we reset state
                if (currentState.get() == SyncState.SYNCING_SUUNTO_PLUS_GUIDES &&
                    !(e instanceof AlreadySynchronizingException)) {
                    setSyncState(new SyncState(SyncState.NOT_SYNCING));
                }
                return Completable.error(e);
            });
    }

    private Completable ensureIsNotSyncing() {
        return Completable.fromAction(() -> {
            if (currentState.get() != SyncState.NOT_SYNCING) {
                throw new AlreadySynchronizingException();
            }
        });
    }

    /**
     * Synchronize everything
     *
     * @return {@link Single} full sync result
     */
    protected Single<SpartanSyncResult> fullSync() {
        // Builder for the sync result to pass along different steps
        final SpartanSyncResult.Builder builder = SpartanSyncResult.builder()
            .syncStartTimestamp(System.currentTimeMillis())
            .macAddress(watchBt.getSuuntoBtDevice().getMacAddress())
            .serial(watchBt.getSerial());

        return TraceKt.traceSingleV1(
            sync(fullSyncCompletable(builder), builder),
            "fullSync"
        );
    }

    /**
     * Synchronize only activity data
     *
     * @return {@link Completable} which completes when sync is fully done
     */
    protected Single<SpartanSyncResult> partialSync() {
        // Builder for the sync result to pass along different steps
        final SpartanSyncResult.Builder builder = SpartanSyncResult.builder()
            .syncStartTimestamp(System.currentTimeMillis())
            .macAddress(watchBt.getSuuntoBtDevice().getMacAddress());

        return TraceKt.traceSingleV1(
            sync(activityDataSyncCompletable(builder), builder),
            "partialSync"
        );
    }

    @Override
    public Single<SpartanSyncResult> synchronizeTrainingZone() {
        final SpartanSyncResult.Builder builder = SpartanSyncResult.builder()
            .syncStartTimestamp(System.currentTimeMillis())
            .macAddress(watchBt.getSuuntoBtDevice().getMacAddress());
        return TraceKt.traceSingleV1(
            sync(trainingZoneSyncCompletable(builder), builder),
            "synchronizeTrainingZone"
        );
    }

    /**
     * Synchronize everything
     *
     * @return {@link Single} full sync result
     */
    protected Single<SpartanSyncResult> backgroundSync() {
        // Builder for the sync result to pass along different steps
        final SpartanSyncResult.Builder builder = SpartanSyncResult.builder()
            .syncStartTimestamp(System.currentTimeMillis())
            .macAddress(watchBt.getSuuntoBtDevice().getMacAddress())
            .serial(watchBt.getSerial());

        return TraceKt.traceSingleV1(
            sync(backgroundSyncCompletable(builder), builder),
            "backgroundSync"
        );
    }

    /**
     * Performs sync with specified chain
     * @param syncCompletable sync chain (full or partial)
     * @param builder         sync result builder
     * @return {@link Single} which emits the result of the sync
     */
    protected Single<SpartanSyncResult> sync(Completable syncCompletable,
        SpartanSyncResult.Builder builder) {
        AtomicBoolean terminated = new AtomicBoolean(false);

        return ensureConnected()
            .andThen(startSyncing())
            .andThen(syncCompletable)
            .toSingle(() -> builder.syncEndTimestamp(System.currentTimeMillis()).build())
            .doOnSuccess(result -> {
                terminated.set(true);
                setSyncState(new SyncState(SyncState.NOT_SYNCING));
                if (!synchronizerStorage.storeSyncResult(result)) {
                    throw new WatchSynchronizeException(CANNOT_STORE_ERROR);
                }
            })
            .doOnError(error -> {
                terminated.set(true);
                if (!(error instanceof AlreadySynchronizingException)) {
                    setSyncState(new SyncState(SyncState.NOT_SYNCING));
                }
            })
            .doOnUnsubscribe(() -> {
                if (!terminated.get()) {
                    // Cancellation happened (no terminal event occurred)
                    setSyncState(new SyncState(SyncState.NOT_SYNCING));
                }
            });
    }

    /**
     * Updates the current sync state
     *
     * @param syncState New sync state
     */
    protected void setSyncState(SyncState syncState) {
        // We can remove this warn print later. Added in case the hourly 247 sync misbehaves to
        // include set sync states in the log dumps.
        Timber.w("Setting sync state to: %d", syncState.getState());
        currentState.set(syncState.getState());
        watchBt.setSyncState(syncState);
    }

    /**
     * Helper method to set state to START_SYNCING with immediate failure if current state
     * was not correctly NOT_SYNCING
     *
     * @return {@link Completable} which completes with success only if we were not syncing already
     */
    protected Completable startSyncing() {
        return Completable.fromAction(() -> {
            if (!currentState.compareAndSet(SyncState.NOT_SYNCING, SyncState.STARTING_SYNC)) {
                throw new AlreadySynchronizingException();
            }
            setSyncState(new SyncState(SyncState.STARTING_SYNC));
        });
    }

    /**
     * Store log book entry list to file.
     */
    synchronized private LogbookEntriesJson storeLogBookEntries(
        Pair<LogbookEntriesJson, String> pair) {
        ImportedEntry imported = importedEntry;
        String json = pair.second;
        LogbookEntriesJson logbookEntriesJson = pair.first;
        String mac = watchBt.getSuuntoBtDevice().getMacAddress();
        final String jsonToFile;
        if (imported == null) {
            jsonToFile = json;
        } else {
            if (importedEntryExists(imported, logbookEntriesJson)) {
                jsonToFile = json;
            } else {
                jsonToFile = getLogbookEntriesJsonWithImportedEntry(imported, logbookEntriesJson);
            }
        }
        if (!synchronizerStorage.storeLogbookEntries(jsonToFile, mac)) {
            throw new WatchSynchronizeException(CANNOT_STORE_ERROR);
        }
        return logbookEntriesJson;
    }

    private synchronized void addImportedEntryToEntriesFile() {
        ImportedEntry imported = importedEntry;
        String mac = watchBt.getSuuntoBtDevice().getMacAddress();
        LogbookEntriesJson storedEntries = synchronizerStorage.getLogbookEntries(mac);
        if (!importedEntryExists(imported, storedEntries)) {
            String jsonToFile = getLogbookEntriesJsonWithImportedEntry(imported, storedEntries);
            synchronizerStorage.storeLogbookEntries(jsonToFile, mac);
        }
    }

    private boolean importedEntryExists(
        ImportedEntry imported,
        LogbookEntriesJson logbookEntriesJson) {
        for (Logbook.Entry entry : logbookEntriesJson.getEntries()) {
            if (entry.getId() == imported.importedEntryId) {
                return true;
            }
        }
        return false;
    }

    private String getLogbookEntriesJsonWithImportedEntry(
        ImportedEntry imported,
        LogbookEntriesJson logbookEntriesJson) {
        List<Logbook.Entry> entries = new ArrayList<>(logbookEntriesJson.getEntries());
        entries.add(new LogbookEntryJson(imported.importedEntryId, imported.timeStamp,
            imported.size));
        LogbookEntriesJson entriesToFile = new LogbookEntriesJson(entries);
        return gson.toJson(entriesToFile);
    }

    /**
     * Helper function to convert json string to LogbookEntriesJson so that null results are thrown
     * as an Exception
     */
    protected Func1<String, Pair<LogbookEntriesJson, String>> convertToLogbookEntriesJson =
        new Func1<String, Pair<LogbookEntriesJson, String>>() {

            @Override
            public Pair<LogbookEntriesJson, String> call(String json) {
                try {
                    LogbookEntriesJson entriesJson = gson.fromJson(json, LogbookEntriesJson.class);
                    if (entriesJson == null) {
                        throw new WatchSynchronizeException(CANNOT_CONVERT_JSON);
                    }
                    return new Pair<>(entriesJson,json);
                } catch (JsonParseException e) {
                    Timber.w(e, "Can't convert to a valid list of entries");
                    throw Exceptions.propagate(e);
                }
            }
        };

    /**
     * Helper method to check that the connection to Spartan is valid before starting sync
     *
     * @return {@link Completable} which completes if connection to Spartan is up and otherwise
     * throws an Exception
     */
    protected Completable ensureConnected() {
        return watchBt.getStateChangeObservable()
            .first()
            .doOnNext(watchState -> {
                if (!watchState.isConnected()) {
                    throw new WatchNotConnectedException();
                }
            })
            .toCompletable();
    }

    /**
     * Helper method to check that the watch are not busy
     *
     * @return {@link Completable} which completes if device is not busy, throws an exception otherwise
     */
    protected Completable ensureIsNotBusy() {
        return watchBt.getStateChangeObservable()
            .first()
            .doOnNext(watchState -> {
                Timber.v("is device busy %s", watchState.isDeviceBusy());
                if (watchState.isDeviceBusy()) {
                    throw new WatchBusyException();
                }
            })
            .toCompletable();
    }

    /**
     * Transformer to apply to completables on the sync chain, checks the busy state,
     * if device became busy it breaks the chain
     *
     * @return transformer that checks busy state before running completable
     */
    protected Completable.Transformer checkBusyState () {
        return completable -> ensureIsNotBusy().andThen(completable);
    }

    /**
     * Transformer to apply to completables on the sync chain, checks the watch connection state,
     * if device has disconnected it breaks the chain
     *
     * @return transformer that checks connection state before running completable
     */
    protected Completable.Transformer checkConnectedState () {
        return completable -> ensureConnected().andThen(completable);
    }

    /**
     * Filter out entries which are not yet synced to backend or marked as synced.
     *
     * @param logbookEntriesJson Logbook entries.
     * @param macAddress         Mac address.
     * @return Entries to be synced and entries to be marked as synced.
     */
    private Pair<List<Logbook.Entry>, List<Long>> filterEntriesNotYetSyncedOrNotMarkedAsSynced(
        LogbookEntriesJson logbookEntriesJson,
        String macAddress) {
        List<Long> entriesInBackend =
            synchronizerStorage.getEntriesFromSyncedToBackend(macAddress);
        List<Long> entriesToBeMarkedAsSynced =
            synchronizerStorage.getEntriesToBeMarkedAsSynced
                (macAddress);
        List<Logbook.Entry> entriesToBeSynced = new ArrayList<>();
        List<Long> entriesToMark = new ArrayList<>();
        Set<Long> itemsInWatch = new HashSet<>();

        // Continue with entries not yet synced to backend or waiting to be marked as synced to
        // watch.
        for (Logbook.Entry entry : logbookEntriesJson.getEntries()) {
            itemsInWatch.add(entry.getId());
            if (!entriesInBackend.contains(entry.getId())) {
                entriesToBeSynced.add(entry);
            }
            if (entriesToBeMarkedAsSynced.contains(entry.getId())) {
                entriesToMark.add(entry.getId());
            }
        }

        // Remove entries from synchronizer storage, which do not exist in watch anymore.
        List<Long> itemsToRemoveFromSyncedToBackEnd = new ArrayList<>();
        for (Long id : entriesInBackend) {
            if (!itemsInWatch.contains(id)) {
                ImportedEntry imported = importedEntry;
                // Remove imported entry if id matches with it.
                if (imported != null && imported.importedEntryId == id) {
                    importedEntry = null;
                }
                // Remove entry from storage, because it is synced and not in watch. Basically,
                // we could delete all the synced entries but in order to avoid any regression,
                // let's keep conservative approach.
                synchronizerStorage.removeLogbookEntry(macAddress, id);
                // Add entry to the list of itemes to be removed.
                itemsToRemoveFromSyncedToBackEnd.add(id);
            }
        }
        List<Long> itemsToRemoveFromToBeMarkedAsSynced = new ArrayList<>();
        for (Long id : entriesToBeMarkedAsSynced) {
            if (!itemsInWatch.contains(id)) {
                itemsToRemoveFromToBeMarkedAsSynced.add(id);
            }
        }
        synchronizerStorage.removeEntriesFromSyncedToBackend(macAddress,
            itemsToRemoveFromSyncedToBackEnd);
        synchronizerStorage.removeEntriesFromToBeMarkedAsSynced(macAddress,
            itemsToRemoveFromToBeMarkedAsSynced);
        entriesToBeSynced.sort(Comparator.comparingLong(Logbook.Entry::getId));
        return new Pair<>(entriesToBeSynced, entriesToMark);
    }

    /**
     * Download logbook and moves from watch which have not been previously downloaded.
     *
     * @param builder Sync result builder.
     * @return Completable which will never return an error, but store sync results via sync result
     * builder.
     */
    protected Completable syncLogbook(final SpartanSyncResult.Builder builder) {
        SynchronizationAnalytics analytics = new SynchronizationAnalytics(watchBt);
        final String macAddress = watchBt.getSuuntoBtDevice().getMacAddress();
        AtomicLong syncStartTimestamp = new AtomicLong();
        Completable syncCompletable = watchBt.getLogbookJson()
            .doOnSubscribe(() -> syncStartTimestamp.set(System.currentTimeMillis()))
            .observeOn(Schedulers.io())
            .retry(logbookSyncRetryCount())
            .doOnSubscribe(() -> {
                // Update state
                setSyncState(new SyncState(SyncState.CHECKING_FOR_NEW_WORKOUTS));
            })

            // Parse device's logbook entries from Json.
            .map(convertToLogbookEntriesJson)

            // Store log book entries list to file
            .map(this::storeLogBookEntries)

            // Resolve logbook entries requiring sync or marking as synced.
            .map(logbookEntriesJson ->
                filterEntriesNotYetSyncedOrNotMarkedAsSynced(logbookEntriesJson, macAddress))
            .observeOn(Schedulers.io())

            // Sync logbook entries.
            .flatMapObservable(pair -> syncEntries(pair.first, macAddress, analytics)
                .flatMap(result -> {
                    if (result.getSummaryResult().isSuccess() || result.getSummaryResult().isAlreadySynced()) {
                        return markEntriesSynced(pair.first, pair.second, macAddress).toCompletable()
                            .andThen(Observable.just(result));
                    } else {
                        return Observable.just(result);
                    }
                })
            )
            .observeOn(Schedulers.io())

            // Filter out logbook entries which were skipped while running syncEntries.
            .filter(
                logbookEntrySyncResult -> !logbookEntrySyncResult.getSummaryResult().isSkipped())
            .toList()
            .map(logbookEntrySyncResults -> LogbookSyncResult.builder()
                .logbookResult(SyncResult.success())
                .logbookEntriesResult(logbookEntrySyncResults)
                .syncDuration(System.currentTimeMillis() - syncStartTimestamp.get())
                .build())
            .toSingle()
            .doOnSuccess(builder::logbookResult)
            .doOnError(throwable -> builder.logbookResult(LogbookSyncResult.builder()
                .logbookResult(SyncResult.failed(throwable))
                .syncDuration(System.currentTimeMillis() - syncStartTimestamp.get())
                .build()))
            .toCompletable()
            .onErrorComplete()
            .subscribeOn(Schedulers.io()); // Complete on any error.

        return TraceKt.traceCompletableV1(syncCompletable, "syncLogbook");
    }

    /**
     * Creates an SML zip file
     *
     * @param entryId the id of the log entry
     * @return SyncResult
     */
    protected Single<SyncResult> createSMLZip(long entryId) {
        final String macAddress = watchBt.getSuuntoBtDevice().getMacAddress();
        return Single.fromCallable( () ->
            new Triple<>(synchronizerStorage.logbookEntrySummaryExists(macAddress, entryId),
                synchronizerStorage.logbookEntrySamplesExists(macAddress, entryId),
                synchronizerStorage.logbookSMLZipExists(macAddress, entryId)))
            .map(samplesSummaryAndSml -> {
                if (samplesSummaryAndSml.getThird()) {
                    Timber.v("Logbook entry [" + entryId + "] sml zip is already synced.");
                    return SyncResult.alreadySynced();
                } else if (samplesSummaryAndSml.getFirst() && samplesSummaryAndSml.getSecond()) {
                    if (synchronizerStorage.saveSMLZipFromSummaryAndSamplesFiles(macAddress, entryId)) {
                        return SyncResult.success();
                    }
                    return SyncResult.failed(new GenericSyncError("Storing logbook sml zip failed"));
                } else {
                    return SyncResult.failed(new GenericSyncError("Cannot store sml zip without summary and samples"));
                }
            });
    }

    /**
     * Syncronizes the summary and the samples
     *
     * @param entry the logbook entry
     * @param step the step
     * @param steps total steps
     * @param analytics analytics
     * @return SyncResult
     */
    protected Single<LogbookEntrySyncResult> synchronizeSummaryAndSamples(
        Logbook.Entry entry, final int step, final int steps,
        final SynchronizationAnalytics analytics) {
        return synchronizeSummaryAndSamples(entry.getId())
            .observeOn(Schedulers.io())
            .doOnError(e -> Timber.w(e,"Error on syncing summary+samples for entry %d", entry.getId()))
            .retry(logbookSyncRetryCount())
            .map(summaryAndSamplesResult -> LogbookEntrySyncResult.builder()
                .entryId(entry.getId())
                .summaryResult(summaryAndSamplesResult)
                .samplesResult(summaryAndSamplesResult)
                .build())
            .flatMap(syncResult -> {
                if (syncResult.isSuccess()) {
                    return createSMLZip(syncResult.getEntryId())
                        .map(smlResult -> syncResult.toBuilder().smlZipResult(smlResult).build())
                        .doOnError(e ->
                            Timber.w(e,"Error on creating sml zip for entry %d", syncResult.getEntryId()));
                } else {
                    SyncResult failedSMLZipResult = SyncResult.failed(
                        new GenericSyncError("Cannot create sml zip entry if summary or samples sync failed"));
                    LogbookEntrySyncResult entrySyncResult = syncResult
                        .toBuilder()
                        .smlZipResult(failedSMLZipResult)
                        .build();
                    return Single.just(entrySyncResult);
                }
            })
            .onErrorReturn(throwable -> LogbookEntrySyncResult.builder()
                // Either synchronizeSummary, synchronizeSamples or zip sml creation failed.
                // For simplicity set each as failed.
                .samplesResult(SyncResult.failed(throwable))
                .summaryResult(SyncResult.failed(throwable))
                .smlZipResult(SyncResult.failed(throwable))
                .build())
            .doOnSubscribe(() -> {
                // Make the state correct here
                setSyncState(new SyncState(SyncState.SYNCING_WORKOUTS, step, steps));
            })
            .subscribeOn(Schedulers.io());
    }

    /**
     * Syncronizes the summary and the samples
     *
     * @param entryId the logbook entry ID
     * @return SyncResult
     */
    protected Single<SyncResult> synchronizeSummaryAndSamples(final long entryId) {
        final String macAddress = watchBt.getSuuntoBtDevice().getMacAddress();
        return Single.fromCallable(() ->
            synchronizerStorage.logbookEntrySummaryExists(macAddress, entryId))
            .flatMap(alreadySynced -> {
                if (alreadySynced) {
                    Timber.v("Logbook entry [" + entryId + "] summary is already " + "synced.");
                    return Single.just(SyncResult.alreadySynced());
                } else {
                    return watchBt.getLogEntryJsonsToFiles(entryId, synchronizerStorage)
                        .andThen(Single.just(SyncResult.success()));
                }
            });
    }

    /**
     * Mark entries as synced. Base class implementation only emits entries to be synced on next
     * step
     *
     * @param entriesToSync Entries to be synced.
     * @param entriesToMark Entries to be marked as synced.
     * @param macAddress    Mac address.
     * @return Observable emitting LogbookEntriesJson.
     */
    protected Observable<List<Logbook.Entry>> markEntriesSynced(
        List<Logbook.Entry> entriesToSync,
        List<Long> entriesToMark,
        String macAddress) {

        // Do nothing on base class, All devices do not support marking as synced. Just pass
        // entries to be synced on next step
        return Observable.just(entriesToSync);
    }

    /**
     * Sync entries and mark entries as synced, if needed.
     *
     * @param entries    Entries to sync
     * @param macAddress Mac address.
     * @param analytics  Analytics
     * @return Observable emitting sync results.
     */
    private Observable<LogbookEntrySyncResult> syncEntries(
        List<Logbook.Entry> entries,
        String macAddress,
        SynchronizationAnalytics analytics) {

        // Gather all singles for individual entries to a list
        List<Observable<LogbookEntrySyncResult>> syncObservables = new ArrayList<>();

        // Resolve number of sync steps shown to the user, which is the number of workouts
        // requiring the loading of data from the device. These syncs will take time and
        // showing the sync as "step" is the most meaningful for the user.
        int syncStepsShownToTheUser = 0;
        for (Logbook.Entry entry : entries) {
            if (isSyncLoadingDataFromDevice(entry, macAddress)) {
                syncStepsShownToTheUser++;
            }
        }
        int step = 1;
        boolean firstEntryLoadingDataFound=false;

        // Create sync tasks.
        for (int i = 0; i < entries.size(); ++i) {
            final long entryId = entries.get(i).getId();
            if (!synchronizerStorage.logbookEntrySummaryExists(macAddress,
                entryId) || !synchronizerStorage.logbookEntrySamplesExists(
                macAddress, entryId)) {
            }
            if (isSyncLoadingDataFromDevice(entries.get(i), macAddress)) {
                // Syncing the entry will take time and will be show as sync "step" to user.
                if (firstEntryLoadingDataFound) {
                    step++;
                }
                firstEntryLoadingDataFound = true;
            }
            Single<LogbookEntrySyncResult> syncSingle =
                synchronizeSummaryAndSamplesIfNeeded(entries.get(i), step, syncStepsShownToTheUser,
                    analytics)
                    //Check if device became busy, abort in that case
                    .compose(logbookEntrySyncResultSingle ->
                        ensureIsNotBusy().andThen(logbookEntrySyncResultSingle));
            syncObservables.add(syncSingle.toObservable());
        }
        return Observable.concat(syncObservables);
    }

    /**
     * Will syncing the entry require loading of data from the device?
     *
     * @param entry      Entry to be synced.
     * @param macAddress Mac address of the device
     * @return True, if syncing the entry will load data from the device.
     */
    private boolean isSyncLoadingDataFromDevice(Logbook.Entry entry, String macAddress) {
        List<Long> syncedToBackend = synchronizerStorage.getEntriesFromSyncedToBackend(
            watchBt.getSuuntoBtDevice().getMacAddress());
        final long entryId = entry.getId();
        if (syncedToBackend.contains(entryId)) {
            return false;
        } else {
            return !synchronizerStorage.logbookEntrySummaryExists(macAddress,
                entryId) || !synchronizerStorage.logbookEntrySamplesExists(macAddress, entryId);
        }
    }

    /**
     * Return syncSingle or skip sync if entry has been synced to backend. Skipping is needed here
     * because list of backend synced items may change during the sync.
     *
     * @param entry     Entry to be synced from device.
     * @param step      Step number.
     * @param steps     Number of step.
     * @param analytics Synchronization analytics.
     * @return Single for doing synchronizing summary and samples.
     */
    private Single<LogbookEntrySyncResult> synchronizeSummaryAndSamplesIfNeeded(
        Logbook.Entry entry,
        final int step,
        final int steps,
        final SynchronizationAnalytics analytics) {
        return Single.fromCallable(() -> synchronizerStorage.getEntriesFromSyncedToBackend(
            watchBt.getSuuntoBtDevice().getMacAddress()).contains(entry.getId()))
            .flatMap((Func1<Boolean, Single<LogbookEntrySyncResult>>) alreadySyncedToBackend -> {
                if (alreadySyncedToBackend) {
                    return Single.just(LogbookEntrySyncResult.builder()
                        .summaryResult(SyncResult.skipped())
                        .build());
                } else {
                    return synchronizeSummaryAndSamples(entry, step, steps, analytics)
                        .doOnSuccess(logbookEntrySyncResult -> {
                            if (logbookEntrySyncResult.isSuccess()) {
                                final WatchSynchronizerListener listenerCopy = listener;
                                if (listenerCopy != null) {
                                    listenerCopy.onSingleLogbookEntrySynced(
                                        new SingleLogbookEntrySyncResultEvent(
                                            logbookEntrySyncResult,
                                            watchBt.getMacAddress()));
                                }
                            }
                        });
                }
            });
    }

    protected abstract Completable backgroundSyncCompletable(SpartanSyncResult.Builder builder);

    protected abstract Completable fullSyncCompletable(SpartanSyncResult.Builder builder);

    protected abstract Completable activityDataSyncCompletable(SpartanSyncResult.Builder builder);

    protected abstract Completable trainingZoneSyncCompletable(SpartanSyncResult.Builder builder);

    protected int logbookSyncRetryCount() {
        return 0;
    }

    @Override
    public void setImportedEntry(ImportedEntry importedEntry) {
        this.importedEntry = importedEntry;
        addImportedEntryToEntriesFile();
    }
}
