package com.suunto.connectivity.util;

import android.Manifest;
import android.content.Context;
import android.content.pm.PackageManager;
import android.location.LocationManager;
import android.os.Process;
import com.stt.android.utils.NearbyDevicesUtilsKt;

/**
 * Utility class to help with checking if user has granted permissions
 */
public class LocationHelper {

    private final Context context;

    public LocationHelper(Context context) {
        this.context = context;
    }

    public boolean isLocationPermissionGranted() {
        return isPermissionGranted(Manifest.permission.ACCESS_COARSE_LOCATION)
                || isPermissionGranted(Manifest.permission.ACCESS_FINE_LOCATION);
    }

    public boolean isLocationProviderEnabled() {
        LocationManager locationManager = (LocationManager) context.getSystemService(Context.LOCATION_SERVICE);

        return locationManager != null &&
                (locationManager.isProviderEnabled(LocationManager.NETWORK_PROVIDER)
                        || locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER));
    }

    public boolean isNearbyDevicesPermissionGranted() {
        return NearbyDevicesUtilsKt.isNearbyDevicesPermissionGranted(context);
    }

    /**
     * Copied from androidx.core.content.ContextCompat for backwards compatibility
     *
     * @param permission the permission to check
     * @return true is granted
     */
    private boolean isPermissionGranted(String permission) {
        if (permission == null) {
            throw new IllegalArgumentException("permission is null");
        }

        return context.checkPermission(permission, android.os.Process.myPid(), Process.myUid()) == PackageManager.PERMISSION_GRANTED;
    }

}
