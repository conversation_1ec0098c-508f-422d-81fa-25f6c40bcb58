package com.suunto.connectivity.repository.workoutFileImport

import com.google.gson.JsonArray
import com.google.gson.JsonObject
import com.google.gson.annotations.SerializedName

//
// Ultimate manager file conversion classes.
//

data class UltimateManagerJson(
    @SerializedName("DeviceLog") val deviceLog: DeviceLog
)

data class DeviceLog(
    @SerializedName("Header") val header: JsonObject,
    @SerializedName("Device") val device: JsonObject,
    @SerializedName("DiveHeader") val diveHeader: JsonObject?,
    @SerializedName("DiveFooter") val diveFooter: JsonObject?,
    @SerializedName("Zapps") val zapps: List<DeviceLogZapp>?,
    @SerializedName("Windows") val suuntoLogbookWindows: List<JsonObject>,
)

data class DeviceLogZapp(
    // Depending on which UM file you are looking at, Zapp information is either under a "Zapp"
    // object...
    @SerializedName("Zapp") val zapp: JsonObject?,

    // ... or directly under the objects in the "Zapps" array, like this:
    @SerializedName("Name") val name: String?,
    @SerializedName("SummaryOutputs") val summaryOutputs: JsonArray?
) {
    val zappJsonObject: JsonObject?
        get() = if (zapp != null && !zapp.isJsonNull) {
            // Zapp information wrapped in a "Zapp" objects in the array
            zapp
        } else if (name != null || summaryOutputs != null) {
            // Zapp information directly inside the objects in the array
            JsonObject().apply {
                name?.let { addProperty("Name", it) }
                summaryOutputs?.let { add("SummaryOutputs", it) }
            }
        } else {
            null
        }
}

class TimelineSampleJsonCv(
    @SerializedName("Attributes") private val attributes: TimelineAttributesJsonCv,
    @SerializedName("Source") private val source: String?,
    @SerializedName("TimeISO8601") private val timeStamp: String?
)

data class TimelineResponseJsonCv(
    @SerializedName("Samples") private val samples: List<TimelineSampleJsonCv>
)

data class TimelineSuuntoSmlCv(
    @SerializedName("Header") private val header: JsonObject? = null,
    @SerializedName(value = "Sample") private val sample: JsonObject? = null,
    @SerializedName("Windows") private val windows: List<JsonObject>? = null,
    @SerializedName("Zapps") private val zapps: List<JsonObject>? = null,
    @SerializedName("R-R") val rr: JsonObject? = null,
    @SerializedName("DiveHeader") val diveHeader: JsonObject? = null,
    @SerializedName("DiveFooter") val diveFooter: JsonObject? = null
)

data class TimelineAttributesJsonCv(
    @SerializedName("suunto/sml") private val suuntoSml: TimelineSuuntoSmlCv
)
