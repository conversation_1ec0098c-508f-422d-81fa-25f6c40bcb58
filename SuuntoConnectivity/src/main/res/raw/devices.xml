<?xml version="1.0" encoding="utf-8" ?>
<devices>
  <device name="Suunto Ambit3 Peak" type="Emu" model="Emu">
    <port type="BLE">
      <match>
        <type>BLE</type>
      </match>
      <options>
        <gattserver>true</gattserver>
        <match>Ambit3 *</match>
      </options>
    </port>
    <host type="NspHost">
      <match>
        <device>Emu</device>
        <swVersion op="gte">1.5.36</swVersion>
      </match>
    </host>
    <protocols>
      <protocol type="BleFrame"/>
      <protocol type="SerialFrame"/>
      <protocol type="CRC32Protocol"/>
      <protocol type="NSP"/>
    </protocols>
    <options>
      <trainingprogram/>
      <firmware>
        <bootupdate>true</bootupdate>
      </firmware>
      <custommodeconfig>
        <intervaltimer>true</intervaltimer>
        <ruleconfig>true</ruleconfig>
        <custommodeid>true</custommodeid>
        <usehrlimits>true</usehrlimits>
        <autoscrolling>true</autoscrolling>
        <rulestoresize>200000</rulestoresize>
        <rulestorelocation>600000</rulestorelocation>
        <displaymode>true</displaymode>
        <navigationselection>true</navigationselection>
      </custommodeconfig>
      <log type="PMEM" version="2.0">
    <distancesources>
      <source>
        <id>0</id>
        <name>Bikepod</name>
      </source>
      <source>
        <id>1</id>
        <name>Footpod</name>
      </source>
      <source>
        <id>2</id>
        <name>Gps</name>
      </source>
      <source>
        <id>3</id>
        <name>Wrist</name>
      </source>
      <source>
        <id>4</id>
        <name>Indoorswimming</name>
      </source>
      <source>
        <id>5</id>
        <name>Outdoorswimming</name>
      </source>
    </distancesources>
        <event>
        </event>
        <training>
          <periodic>
            <sample>
              <id>1</id>
              <name>Latitude</name>
              <type>int32</type>
              <value>
                <unit>degrees</unit>
                <scale>0.0000001</scale>
                <limit>
                  <min>-90</min>
                  <max>90</max>
                </limit>
              </value>
            </sample>
            <sample>
              <id>2</id>
              <name>Longitude</name>
              <type>int32</type>
              <value>
                <unit>degrees</unit>
                <scale>0.0000001</scale>
                <limit>
                  <min>-180</min>
                  <max>180</max>
                </limit>
              </value>
            </sample>
            <sample>
              <id>3</id>
              <name>Distance</name>
              <type>uint32</type>
              <value>
                <unit>meters</unit>
              </value>
            </sample>
            <sample>
              <id>4</id>
              <name>Speed</name>
              <type>uint16</type>
              <value>
                <scale>0.01</scale>
                <unit>m/s</unit>
                <ignoreif>65535</ignoreif>
              </value>
            </sample>
            <sample>
              <id>5</id>
              <name>HR</name>
              <type>uint8</type>
              <value>
                <unit>bpm</unit>
                <ignoreif>255</ignoreif>
              </value>
            </sample>
            <sample>
              <id>6</id>
              <name>Time</name>
              <type>uint32</type>
              <value>
                <unit>msec</unit>
              </value>
            </sample>
            <sample>
              <id>7</id>
              <name>GPSSpeed</name>
              <type>uint16</type>
              <value>
                <unit>m/s</unit>
                <scale>0.01</scale>
              </value>
            </sample>
            <sample>
              <id>10</id>
              <name>EHPE</name>
              <type>uint32</type>
              <value>
                <unit>meters</unit>
                <scale>0.01</scale>
              </value>
            </sample>
            <sample>
              <id>11</id>
              <name>EVPE</name>
              <type>uint32</type>
              <value>
                <unit>meters</unit>
                <scale>0.01</scale>
              </value>
            </sample>
            <sample>
              <id>12</id>
              <name>Altitude</name>
              <type>int16</type>
              <value>
                <unit>meters</unit>
                <limit>
                  <min>-1000</min>
                  <max>15000</max>
                </limit>
              </value>
            </sample>
            <sample>
              <id>13</id>
              <name>AbsPressure</name>
              <type>uint16</type>
              <value>
                <unit>hpa</unit>
                <scale>0.1</scale>
              </value>
            </sample>
            <sample>
              <id>14</id>
              <name>EnergyConsumption</name>
              <type>uint16</type>
              <value>
                <unit>hcal/min</unit>
              </value>
            </sample>
            <sample>
              <id>15</id>
              <name>Temperature</name>
              <type>int16</type>
              <value>
                <unit>celsius</unit>
                <scale>0.1</scale>
                <limit>
                  <min>-100</min>
                  <max>100</max>
                </limit>
              </value>
            </sample>
            <sample>
              <id>16</id>
              <name>BatteryCharge</name>
              <type>uint8</type>
              <value>
                <unit>percent</unit>
              </value>
            </sample>
            <sample>
              <id>17</id>
              <name>GPSAltitude</name>
              <type>uint32</type>
              <value>
                <unit>meters</unit>
                <scale>0.01</scale>
                <limit>
                  <min>-1000</min>
                  <max>15000</max>
                </limit>
              </value>
            </sample>
            <sample>
              <id>18</id>
              <name>GPSHeading</name>
              <type>uint16</type>
              <value>
                <unit>degrees</unit>
                <scale>0.01</scale>
                <limit>
                  <min>0</min>
                  <max>360</max>
                </limit>
                <ignoreif>65535</ignoreif>
              </value>
            </sample>
            <sample>
              <id>19</id>
              <name>GpsHDOP</name>
              <type>uint8</type>
              <value>
                <ignoreif>255</ignoreif>
              </value>
            </sample>
            <sample>
              <id>20</id>
              <name>GpsVDOP</name>
              <type>uint8</type>
              <value>
                <ignoreif>255</ignoreif>
              </value>
            </sample>
            <sample>
              <id>21</id>
              <name>WristCadence</name>
              <type>uint16</type>
              <value>
                <unit>rpm</unit>
                <ignoreif>65535</ignoreif>
              </value>
            </sample>
            <sample>
              <id>22</id>
              <name>SNR</name>
              <type>buffer</type>
              <length>16</length>
            </sample>
            <sample>
              <id>23</id>
              <name>NumberOfSatellites</name>
              <type>uint8</type>
              <value>
                <ignoreif>255</ignoreif>
              </value>
            </sample>
            <sample>
              <id>24</id>
              <name>SeaLevelPressure</name>
              <type>int16</type>
              <value>
                <unit>hpa</unit>
                <scale>0.1</scale>
              </value>
            </sample>
            <sample>
              <id>25</id>
              <name>VerticalSpeed</name>
              <type>int16</type>
              <value>
                <unit>m/s</unit>
                <scale>0.01</scale>
              </value>
            </sample>
            <sample>
              <id>26</id>
              <name>Cadence</name>
              <type>uint8</type>
              <value>
                <unit>rpm</unit>
                <ignoreif>255</ignoreif>
              </value>
            </sample>
            <sample>
              <id>31</id>
              <name>BikePower</name>
              <type>uint16</type>
              <value>
                <unit>watt</unit>
              </value>
            </sample>
            <sample>
              <id>32</id>
              <name>SwimmingStrokeCount</name>
              <type>uint32</type>
            </sample>
            <sample>
                <id>40</id>
                <name>RelativePerformanceLevel</name>
                <type>int8</type>
                <value>
                    <limit>
                        <min>0.80</min>
                        <max>1.20</max>
                    </limit>
                    <ignoreif>-127</ignoreif>
                    <scale>0.01</scale>
                    <offset>100</offset>
                    <precision>2</precision>
                </value>
            </sample>
          </periodic>
          <episodic>
            <sample>
              <id>1</id>
              <name>GPS test</name>
              <type>buffer</type>
              <length>16</length>
            </sample>
            <sample>
              <id>2</id>
              <name>gpsdata</name>
              <type>buffer</type>
              <length>?</length>
            </sample>
            <sample>
              <id>3</id>
              <name>accdata</name>
              <type>buffer</type>
              <length>?</length>
            </sample>
            <sample>
              <id>4</id>
              <name>logpause</name>
              <type>buffer</type>
              <length>0</length>
            </sample>
            <sample>
              <id>5</id>
              <name>logrestart</name>
              <type>buffer</type>
              <length>0</length>
            </sample>
            <sample>
              <id>6</id>
              <name>ibidata</name>
              <type>buffer</type>
              <length>64</length>
            </sample>
            <sample>
              <id>7</id>
              <name>ttff</name>
              <type>uint16</type>
              <value>
                <unit>seconds</unit>
                <scale>0.1</scale>
              </value>
            </sample>
            <sample>
              <id>8</id>
              <name>distancesource</name>
              <type>uint8</type>
              <value>
                <ignoreif>255</ignoreif>
              </value>
            </sample>
            <sample>
              <id>9</id>
              <name>lapinfo</name>
              <type>buffer</type>
              <length>23</length>
            </sample>
            <sample>
              <id>10</id>
              <name>gpsuserdata</name>
              <type>buffer</type>
              <length>17</length>
            </sample>
            <sample>
              <id>11</id>
              <name>gpstestdata</name>
              <type>buffer</type>
              <length>61</length>
            </sample>
            <sample>
              <id>12</id>
              <name>timeref</name>
              <type>buffer</type>
              <length>14</length>
            </sample>
            <sample>
              <id>19</id>
              <name>ruledata</name>
              <type>buffer</type>
              <length>9</length>
            </sample>
          </episodic>
        </training>
      </log>
    </options>
  </device>
  
  <device name="Suunto Ambit3 Sport" type="Emu" model="Finch">
    <port type="BLE">
      <match>
        <type>BLE</type>
      </match>
      <options>
        <gattserver>true</gattserver>
        <match>Ambit3S *</match>
      </options>
    </port>
    <host type="NspHost">
      <match>
        <device>Finch</device>
        <swVersion op="gte">1.5.36</swVersion>
      </match>
    </host>
    <protocols>
      <protocol type="BleFrame"/>
      <protocol type="SerialFrame"/>
      <protocol type="CRC32Protocol"/>
      <protocol type="NSP"/>
    </protocols>
    <options>
      <trainingprogram/>
      <firmware>
        <bootupdate>true</bootupdate>
      </firmware>
      <custommodeconfig>
        <intervaltimer>true</intervaltimer>
        <ruleconfig>true</ruleconfig>
        <custommodeid>true</custommodeid>
        <usehrlimits>true</usehrlimits>
        <autoscrolling>true</autoscrolling>
        <rulestoresize>200000</rulestoresize>
        <rulestorelocation>600000</rulestorelocation>
        <displaymode>true</displaymode>
        <navigationselection>true</navigationselection>
      </custommodeconfig>
      <log type="PMEM" version="2.0">
    <distancesources>
      <source>
        <id>0</id>
        <name>Bikepod</name>
      </source>
      <source>
        <id>1</id>
        <name>Footpod</name>
      </source>
      <source>
        <id>2</id>
        <name>Gps</name>
      </source>
      <source>
        <id>3</id>
        <name>Wrist</name>
      </source>
      <source>
        <id>4</id>
        <name>Indoorswimming</name>
      </source>
      <source>
        <id>5</id>
        <name>Outdoorswimming</name>
      </source>
    </distancesources>
        <event>
        </event>
        <training>
          <periodic>
            <sample>
              <id>1</id>
              <name>Latitude</name>
              <type>int32</type>
              <value>
                <unit>degrees</unit>
                <scale>0.0000001</scale>
                <limit>
                  <min>-90</min>
                  <max>90</max>
                </limit>
              </value>
            </sample>
            <sample>
              <id>2</id>
              <name>Longitude</name>
              <type>int32</type>
              <value>
                <unit>degrees</unit>
                <scale>0.0000001</scale>
                <limit>
                  <min>-180</min>
                  <max>180</max>
                </limit>
              </value>
            </sample>
            <sample>
              <id>3</id>
              <name>Distance</name>
              <type>uint32</type>
              <value>
                <unit>meters</unit>
              </value>
            </sample>
            <sample>
              <id>4</id>
              <name>Speed</name>
              <type>uint16</type>
              <value>
                <scale>0.01</scale>
                <unit>m/s</unit>
                <ignoreif>65535</ignoreif>
              </value>
            </sample>
            <sample>
              <id>5</id>
              <name>HR</name>
              <type>uint8</type>
              <value>
                <unit>bpm</unit>
                <ignoreif>255</ignoreif>
              </value>
            </sample>
            <sample>
              <id>6</id>
              <name>Time</name>
              <type>uint32</type>
              <value>
                <unit>msec</unit>
              </value>
            </sample>
            <sample>
              <id>7</id>
              <name>GPSSpeed</name>
              <type>uint16</type>
              <value>
                <unit>m/s</unit>
                <scale>0.01</scale>
              </value>
            </sample>
            <sample>
              <id>10</id>
              <name>EHPE</name>
              <type>uint32</type>
              <value>
                <unit>meters</unit>
                <scale>0.01</scale>
              </value>
            </sample>
            <sample>
              <id>11</id>
              <name>EVPE</name>
              <type>uint32</type>
              <value>
                <unit>meters</unit>
                <scale>0.01</scale>
              </value>
            </sample>
            <sample>
              <id>12</id>
              <name>Altitude</name>
              <type>int16</type>
              <value>
                <unit>meters</unit>
                <limit>
                  <min>-1000</min>
                  <max>15000</max>
                </limit>
              </value>
            </sample>
            <sample>
              <id>13</id>
              <name>AbsPressure</name>
              <type>uint16</type>
              <value>
                <unit>hpa</unit>
                <scale>0.1</scale>
              </value>
            </sample>
            <sample>
              <id>14</id>
              <name>EnergyConsumption</name>
              <type>uint16</type>
              <value>
                <unit>hcal/min</unit>
              </value>
            </sample>
            <sample>
              <id>15</id>
              <name>Temperature</name>
              <type>int16</type>
              <value>
                <unit>celsius</unit>
                <scale>0.1</scale>
                <limit>
                  <min>-100</min>
                  <max>100</max>
                </limit>
              </value>
            </sample>
            <sample>
              <id>16</id>
              <name>BatteryCharge</name>
              <type>uint8</type>
              <value>
                <unit>percent</unit>
              </value>
            </sample>
            <sample>
              <id>17</id>
              <name>GPSAltitude</name>
              <type>uint32</type>
              <value>
                <unit>meters</unit>
                <scale>0.01</scale>
                <limit>
                  <min>-1000</min>
                  <max>15000</max>
                </limit>
              </value>
            </sample>
            <sample>
              <id>18</id>
              <name>GPSHeading</name>
              <type>uint16</type>
              <value>
                <unit>degrees</unit>
                <scale>0.01</scale>
                <limit>
                  <min>0</min>
                  <max>360</max>
                </limit>
                <ignoreif>65535</ignoreif>
              </value>
            </sample>
            <sample>
              <id>19</id>
              <name>GpsHDOP</name>
              <type>uint8</type>
              <value>
                <ignoreif>255</ignoreif>
              </value>
            </sample>
            <sample>
              <id>20</id>
              <name>GpsVDOP</name>
              <type>uint8</type>
              <value>
                <ignoreif>255</ignoreif>
              </value>
            </sample>
            <sample>
              <id>23</id>
              <name>NumberOfSatellites</name>
              <type>uint8</type>
              <value>
                <ignoreif>255</ignoreif>
              </value>
            </sample>
            <sample>
              <id>24</id>
              <name>SeaLevelPressure</name>
              <type>int16</type>
              <value>
                <unit>hpa</unit>
                <scale>0.1</scale>
              </value>
            </sample>
            <sample>
              <id>25</id>
              <name>VerticalSpeed</name>
              <type>int16</type>
              <value>
                <unit>m/s</unit>
                <scale>0.01</scale>
              </value>
            </sample>
            <sample>
              <id>26</id>
              <name>Cadence</name>
              <type>uint8</type>
              <value>
                <unit>rpm</unit>
                <ignoreif>255</ignoreif>
              </value>
            </sample>
            <sample>
              <id>31</id>
              <name>BikePower</name>
              <type>uint16</type>
              <value>
                <unit>watt</unit>
              </value>
            </sample>
            <sample>
              <id>32</id>
              <name>SwimmingStrokeCount</name>
              <type>uint32</type>
            </sample>
            <sample>
                <id>40</id>
                <name>RelativePerformanceLevel</name>
                <type>int8</type>
                <value>
                    <limit>
                        <min>0.80</min>
                        <max>1.20</max>
                    </limit>
                    <ignoreif>-127</ignoreif>
                    <scale>0.01</scale>
                    <offset>100</offset>
                    <precision>2</precision>
                </value>
            </sample>
          </periodic>
          <episodic>
            <sample>
              <id>1</id>
              <name>GPS test</name>
              <type>buffer</type>
              <length>16</length>
            </sample>
            <sample>
              <id>2</id>
              <name>gpsdata</name>
              <type>buffer</type>
              <length>?</length>
            </sample>
            <sample>
              <id>3</id>
              <name>accdata</name>
              <type>buffer</type>
              <length>?</length>
            </sample>
            <sample>
              <id>4</id>
              <name>logpause</name>
              <type>buffer</type>
              <length>0</length>
            </sample>
            <sample>
              <id>5</id>
              <name>logrestart</name>
              <type>buffer</type>
              <length>0</length>
            </sample>
            <sample>
              <id>6</id>
              <name>ibidata</name>
              <type>buffer</type>
              <length>64</length>
            </sample>
            <sample>
              <id>7</id>
              <name>ttff</name>
              <type>uint16</type>
              <value>
                <unit>seconds</unit>
                <scale>0.1</scale>
              </value>
            </sample>
            <sample>
              <id>8</id>
              <name>distancesource</name>
              <type>uint8</type>
              <value>
                <ignoreif>255</ignoreif>
              </value>
            </sample>
            <sample>
              <id>9</id>
              <name>lapinfo</name>
              <type>buffer</type>
              <length>23</length>
            </sample>
            <sample>
              <id>10</id>
              <name>gpsuserdata</name>
              <type>buffer</type>
              <length>17</length>
            </sample>
            <sample>
              <id>11</id>
              <name>gpstestdata</name>
              <type>buffer</type>
              <length>61</length>
            </sample>
            <sample>
              <id>12</id>
              <name>timeref</name>
              <type>buffer</type>
              <length>14</length>
            </sample>
            <sample>
              <id>19</id>
              <name>ruledata</name>
              <type>buffer</type>
              <length>9</length>
            </sample>
          </episodic>
        </training>
      </log>
    </options>
  </device>

  <device name="Suunto Ambit3 Run" type="Emu" model="Ibisbill">
    <port type="BLE">
      <match>
        <type>BLE</type>
      </match>
      <options>
        <gattserver>true</gattserver>
        <match>Ambit3R *</match>
      </options>
    </port>
    <host type="NspHost">
      <match>
        <device>Ibisbill</device>
        <swVersion op="gte">1.5.36</swVersion>
      </match>
    </host>
    <protocols>
      <protocol type="BleFrame"/>
      <protocol type="SerialFrame"/>
      <protocol type="CRC32Protocol"/>
      <protocol type="NSP"/>
    </protocols>
    <options>
      <trainingprogram/>
      <firmware>
        <bootupdate>true</bootupdate>
      </firmware>
      <custommodeconfig>
        <intervaltimer>true</intervaltimer>
        <ruleconfig>true</ruleconfig>
        <custommodeid>true</custommodeid>
        <usehrlimits>true</usehrlimits>
        <autoscrolling>true</autoscrolling>
        <rulestoresize>200000</rulestoresize>
        <rulestorelocation>600000</rulestorelocation>
        <displaymode>true</displaymode>
        <navigationselection>true</navigationselection>
      </custommodeconfig>
      <log type="PMEM" version="2.0">
        <distancesources>
          <source>
            <id>0</id>
            <name>Bikepod</name>
          </source>
          <source>
            <id>1</id>
            <name>Footpod</name>
          </source>
          <source>
            <id>2</id>
            <name>Gps</name>
          </source>
          <source>
            <id>3</id>
            <name>Wrist</name>
          </source>
          <source>
            <id>4</id>
            <name>Indoorswimming</name>
          </source>
          <source>
            <id>5</id>
            <name>Outdoorswimming</name>
          </source>
        </distancesources>
        <event>
        </event>
        <training>
          <periodic>
            <sample>
              <id>1</id>
              <name>Latitude</name>
              <type>int32</type>
              <value>
                <unit>degrees</unit>
                <scale>0.0000001</scale>
                <limit>
                  <min>-90</min>
                  <max>90</max>
                </limit>
              </value>
            </sample>
            <sample>
              <id>2</id>
              <name>Longitude</name>
              <type>int32</type>
              <value>
                <unit>degrees</unit>
                <scale>0.0000001</scale>
                <limit>
                  <min>-180</min>
                  <max>180</max>
                </limit>
              </value>
            </sample>
            <sample>
              <id>3</id>
              <name>Distance</name>
              <type>uint32</type>
              <value>
                <unit>meters</unit>
              </value>
            </sample>
            <sample>
              <id>4</id>
              <name>Speed</name>
              <type>uint16</type>
              <value>
                <scale>0.01</scale>
                <unit>m/s</unit>
                <ignoreif>65535</ignoreif>
              </value>
            </sample>
            <sample>
              <id>5</id>
              <name>HR</name>
              <type>uint8</type>
              <value>
                <unit>bpm</unit>
                <ignoreif>255</ignoreif>
              </value>
            </sample>
            <sample>
              <id>6</id>
              <name>Time</name>
              <type>uint32</type>
              <value>
                <unit>msec</unit>
              </value>
            </sample>
            <sample>
              <id>7</id>
              <name>GPSSpeed</name>
              <type>uint16</type>
              <value>
                <unit>m/s</unit>
                <scale>0.01</scale>
              </value>
            </sample>
            <sample>
              <id>8</id>
              <name>WristAccSpeed</name>
              <type>uint16</type>
              <value>
                <unit>m/s</unit>
                <scale>0.01</scale>
              </value>
            </sample>
            <sample>
              <id>9</id>
              <name>BikePodSpeed</name>
              <type>uint16</type>
              <value>
                <unit>m/s</unit>
                <scale>0.01</scale>
              </value>
            </sample>
            <sample>
              <id>10</id>
              <name>EHPE</name>
              <type>uint32</type>
              <value>
                <unit>meters</unit>
                <scale>0.01</scale>
              </value>
            </sample>
            <sample>
              <id>11</id>
              <name>EVPE</name>
              <type>uint32</type>
              <value>
                <unit>meters</unit>
                <scale>0.01</scale>
              </value>
            </sample>
            <sample>
              <id>12</id>
              <name>Altitude</name>
              <type>int16</type>
              <value>
                <unit>meters</unit>
                <limit>
                  <min>-1000</min>
                  <max>15000</max>
                </limit>
              </value>
            </sample>
            <sample>
              <id>13</id>
              <name>AbsPressure</name>
              <type>uint16</type>
              <value>
                <unit>hpa</unit>
                <scale>0.1</scale>
              </value>
            </sample>
            <sample>
              <id>14</id>
              <name>EnergyConsumption</name>
              <type>uint16</type>
              <value>
                <unit>hcal/min</unit>
              </value>
            </sample>
            <sample>
              <id>15</id>
              <name>Temperature</name>
              <type>int16</type>
              <value>
                <unit>celsius</unit>
                <scale>0.1</scale>
                <limit>
                  <min>-100</min>
                  <max>100</max>
                </limit>
              </value>
            </sample>
            <sample>
              <id>16</id>
              <name>BatteryCharge</name>
              <type>uint8</type>
              <value>
                <unit>percent</unit>
              </value>
            </sample>
            <sample>
              <id>17</id>
              <name>GPSAltitude</name>
              <type>uint32</type>
              <value>
                <unit>meters</unit>
                <scale>0.01</scale>
                <limit>
                  <min>-1000</min>
                  <max>15000</max>
                </limit>
              </value>
            </sample>
            <sample>
              <id>18</id>
              <name>GPSHeading</name>
              <type>uint16</type>
              <value>
                <unit>degrees</unit>
                <scale>0.01</scale>
                <limit>
                  <min>0</min>
                  <max>360</max>
                </limit>
                <ignoreif>65535</ignoreif>
              </value>
            </sample>
            <sample>
              <id>19</id>
              <name>GpsHDOP</name>
              <type>uint8</type>
              <value>
                <ignoreif>255</ignoreif>
              </value>
            </sample>
            <sample>
              <id>20</id>
              <name>GpsVDOP</name>
              <type>uint8</type>
              <value>
                <ignoreif>255</ignoreif>
              </value>
            </sample>
            <sample>
              <id>21</id>
              <name>WristCadence</name>
              <type>uint16</type>
              <value>
                <unit>rpm</unit>
                <ignoreif>65535</ignoreif>
              </value>
            </sample>
            <sample>
              <id>22</id>
              <name>SNR</name>
              <type>buffer</type>
              <length>16</length>
            </sample>
            <sample>
              <id>23</id>
              <name>NumberOfSatellites</name>
              <type>uint8</type>
              <value>
                <ignoreif>255</ignoreif>
              </value>
            </sample>
            <sample>
              <id>24</id>
              <name>SeaLevelPressure</name>
              <type>int16</type>
              <value>
                <unit>hpa</unit>
                <scale>0.1</scale>
              </value>
            </sample>
            <sample>
              <id>25</id>
              <name>VerticalSpeed</name>
              <type>int16</type>
              <value>
                <unit>m/s</unit>
                <scale>0.01</scale>
              </value>
            </sample>
            <sample>
              <id>26</id>
              <name>Cadence</name>
              <type>uint8</type>
              <value>
                <unit>rpm</unit>
                <ignoreif>255</ignoreif>
              </value>
            </sample>
            <sample>
              <id>31</id>
              <name>BikePower</name>
              <type>uint16</type>
              <value>
                <unit>watt</unit>
              </value>
            </sample>
            <sample>
              <id>32</id>
              <name>SwimmingStrokeCount</name>
              <type>uint32</type>
            </sample>
            <sample>
              <id>40</id>
              <name>RelativePerformanceLevel</name>
              <type>int8</type>
              <value>
                <limit>
                  <min>0.80</min>
                  <max>1.20</max>
                </limit>
                <ignoreif>-127</ignoreif>
                <scale>0.01</scale>
                <offset>100</offset>
                <precision>2</precision>
              </value>
            </sample>
          </periodic>
          <episodic>
            <sample>
              <id>1</id>
              <name>GPS test</name>
              <type>buffer</type>
              <length>16</length>
            </sample>
            <sample>
              <id>2</id>
              <name>gpsdata</name>
              <type>buffer</type>
              <length>?</length>
            </sample>
            <sample>
              <id>3</id>
              <name>accdata</name>
              <type>buffer</type>
              <length>?</length>
            </sample>
            <sample>
              <id>4</id>
              <name>logpause</name>
              <type>buffer</type>
              <length>0</length>
            </sample>
            <sample>
              <id>5</id>
              <name>logrestart</name>
              <type>buffer</type>
              <length>0</length>
            </sample>
            <sample>
              <id>6</id>
              <name>ibidata</name>
              <type>buffer</type>
              <length>64</length>
            </sample>
            <sample>
              <id>7</id>
              <name>ttff</name>
              <type>uint16</type>
              <value>
                  <unit>seconds</unit>
                  <scale>0.1</scale>
              </value>
            </sample>
            <sample>
              <id>8</id>
              <name>distancesource</name>
              <type>uint8</type>
              <value>
                  <ignoreif>255</ignoreif>
              </value>
            </sample>
            <sample>
              <id>9</id>
              <name>lapinfo</name>
              <type>buffer</type>
              <length>23</length>
            </sample>
            <sample>
              <id>10</id>
              <name>gpsuserdata</name>
              <type>buffer</type>
              <length>17</length>
            </sample>
            <sample>
              <id>11</id>
              <name>gpstestdata</name>
              <type>buffer</type>
              <length>61</length>
            </sample>
            <sample>
              <id>12</id>
              <name>timeref</name>
              <type>buffer</type>
              <length>14</length>
            </sample>
            <sample>
              <id>19</id>
              <name>ruledata</name>
              <type>buffer</type>
              <length>9</length>
            </sample>
          </episodic>
        </training>
      </log>
    </options>    
  </device>

  <device name="Suunto Traverse" type="Emu" model="Jabiru">    
    <port type="BLE">
      <match>
        <type>BLE</type>
      </match>
      <options>
        <gattserver>true</gattserver>
        <match>Traverse *</match>
      </options>
    </port>
    <host type="NspHost">
      <match>
        <device>Jabiru</device>
      </match>
    </host>
    <protocols>
      <protocol type="BleFrame"/>
      <protocol type="SerialFrame"/>
      <protocol type="CRC32Protocol"/>
      <protocol type="NSP"/>
    </protocols>
    <options>
        <trainingprogram/>
        <firmware>
            <bootupdate>true</bootupdate>
        </firmware>
        <custommodeconfig>
            <intervaltimer>true</intervaltimer>
            <ruleconfig>true</ruleconfig>
            <custommodeid>true</custommodeid>
            <usehrlimits>true</usehrlimits>
            <autoscrolling>true</autoscrolling>
            <rulestoresize>200000</rulestoresize>
            <rulestorelocation>600000</rulestorelocation>
            <displaymode>true</displaymode>
            <navigationselection>true</navigationselection>
            <traverseDisplays>
                <cardinalCompass>
                    <match>
                        <swVersion op="gte">1.8.0</swVersion>
                    </match>
                </cardinalCompass>
                <altiGraph>
                    <match>
                        <swVersion op="gte">1.5.0</swVersion>
                    </match>
                </altiGraph>
                <moonCalendar>
                    <match>
                        <swVersion op="gte">1.9.0</swVersion>
                    </match>
                </moonCalendar>
                <huntingFishing>
                    <match>
                        <swVersion op="gte">1.9.0</swVersion>
                    </match>
                </huntingFishing>
            </traverseDisplays>
        </custommodeconfig>
        <log type="PMEM" version="2.0">
            <distancesources>
                <source>
                    <id>0</id>
                    <name>Bikepod</name>
                </source>
                <source>
                    <id>1</id>
                    <name>Footpod</name>
                </source>
                <source>
                    <id>2</id>
                    <name>Gps</name>
                </source>
                <source>
                    <id>3</id>
                    <name>Wrist</name>
                </source>
                <source>
                    <id>4</id>
                    <name>Indoorswimming</name>
                </source>
                <source>
                    <id>5</id>
                    <name>Outdoorswimming</name>
                </source>
            </distancesources>
            <event>
            </event>
            <training>
                <periodic>
                    <sample>
                        <id>1</id>
                        <name>Latitude</name>
                        <type>int32</type>
                        <value>
                            <unit>degrees</unit>
                            <scale>0.0000001</scale>
                            <limit>
                                <min>-90</min>
                                <max>90</max>
                            </limit>
                        </value>
                    </sample>
                    <sample>
                        <id>2</id>
                        <name>Longitude</name>
                        <type>int32</type>
                        <value>
                            <unit>degrees</unit>
                            <scale>0.0000001</scale>
                            <limit>
                                <min>-180</min>
                                <max>180</max>
                            </limit>
                        </value>
                    </sample>
                    <sample>
                        <id>3</id>
                        <name>Distance</name>
                        <type>uint32</type>
                        <value>
                            <unit>meters</unit>
                        </value>
                    </sample>
                    <sample>
                        <id>4</id>
                        <name>Speed</name>
                        <type>uint16</type>
                        <value>
                            <scale>0.01</scale>
                            <unit>m/s</unit>
                            <ignoreif>65535</ignoreif>
                        </value>
                    </sample>
                    <sample>
                        <id>5</id>
                        <name>HR</name>
                        <type>uint8</type>
                        <value>
                            <unit>bpm</unit>
                            <ignoreif>255</ignoreif>
                        </value>
                    </sample>
                    <sample>
                        <id>6</id>
                        <name>Time</name>
                        <type>uint32</type>
                        <value>
                            <unit>msec</unit>
                        </value>
                    </sample>
                    <sample>
                        <id>7</id>
                        <name>GPSSpeed</name>
                        <type>uint16</type>
                        <value>
                            <unit>m/s</unit>
                            <scale>0.01</scale>
                        </value>
                    </sample>
                    <sample>
                        <id>8</id>
                        <name>WristAccSpeed</name>
                        <type>uint16</type>
                        <value>
                            <unit>m/s</unit>
                            <scale>0.01</scale>
                        </value>
                    </sample>
                    <sample>
                        <id>9</id>
                        <name>BikePodSpeed</name>
                        <type>uint16</type>
                        <value>
                            <unit>m/s</unit>
                            <scale>0.01</scale>
                        </value>
                    </sample>
                    <sample>
                        <id>10</id>
                        <name>EHPE</name>
                        <type>uint32</type>
                        <value>
                            <unit>meters</unit>
                            <scale>0.01</scale>
                        </value>
                    </sample>
                    <sample>
                        <id>11</id>
                        <name>EVPE</name>
                        <type>uint32</type>
                        <value>
                            <unit>meters</unit>
                            <scale>0.01</scale>
                        </value>
                    </sample>
                    <sample>
                        <id>12</id>
                        <name>Altitude</name>
                        <type>int16</type>
                        <value>
                            <unit>meters</unit>
                            <limit>
                                <min>-1000</min>
                                <max>15000</max>
                            </limit>
                        </value>
                    </sample>
                    <sample>
                        <id>13</id>
                        <name>AbsPressure</name>
                        <type>uint16</type>
                        <value>
                            <unit>hpa</unit>
                            <scale>0.1</scale>
                        </value>
                    </sample>
                    <sample>
                        <id>14</id>
                        <name>EnergyConsumption</name>
                        <type>uint16</type>
                        <value>
                            <unit>hcal/min</unit>
                        </value>
                    </sample>
                    <sample>
                        <id>15</id>
                        <name>Temperature</name>
                        <type>int16</type>
                        <value>
                            <unit>celsius</unit>
                            <scale>0.1</scale>
                            <limit>
                                <min>-100</min>
                                <max>100</max>
                            </limit>
                        </value>
                    </sample>
                    <sample>
                        <id>16</id>
                        <name>BatteryCharge</name>
                        <type>uint8</type>
                        <value>
                            <unit>percent</unit>
                        </value>
                    </sample>
                    <sample>
                        <id>17</id>
                        <name>GPSAltitude</name>
                        <type>uint32</type>
                        <value>
                            <unit>meters</unit>
                            <scale>0.01</scale>
                            <limit>
                                <min>-1000</min>
                                <max>15000</max>
                            </limit>
                        </value>
                    </sample>
                    <sample>
                        <id>18</id>
                        <name>GPSHeading</name>
                        <type>uint16</type>
                        <value>
                            <unit>degrees</unit>
                            <scale>0.01</scale>
                            <limit>
                                <min>0</min>
                                <max>360</max>
                            </limit>
                            <ignoreif>65535</ignoreif>
                        </value>
                    </sample>
                    <sample>
                        <id>19</id>
                        <name>GpsHDOP</name>
                        <type>uint8</type>
                        <value>
                            <ignoreif>255</ignoreif>
                        </value>
                    </sample>
                    <sample>
                        <id>20</id>
                        <name>GpsVDOP</name>
                        <type>uint8</type>
                        <value>
                            <ignoreif>255</ignoreif>
                        </value>
                    </sample>
                    <sample>
                        <id>21</id>
                        <name>WristCadence</name>
                        <type>uint16</type>
                        <value>
                            <unit>rpm</unit>
                            <ignoreif>65535</ignoreif>
                        </value>
                    </sample>
                    <sample>
                        <id>22</id>
                        <name>SNR</name>
                        <type>buffer</type>
                        <length>16</length>
                    </sample>
                    <sample>
                        <id>23</id>
                        <name>NumberOfSatellites</name>
                        <type>uint8</type>
                        <value>
                            <ignoreif>255</ignoreif>
                        </value>
                    </sample>
                    <sample>
                        <id>24</id>
                        <name>SeaLevelPressure</name>
                        <type>int16</type>
                        <value>
                            <unit>hpa</unit>
                            <scale>0.1</scale>
                        </value>
                    </sample>
                    <sample>
                        <id>25</id>
                        <name>VerticalSpeed</name>
                        <type>int16</type>
                        <value>
                            <unit>m/s</unit>
                            <scale>0.01</scale>
                        </value>
                    </sample>
                    <sample>
                        <id>26</id>
                        <name>Cadence</name>
                        <type>uint8</type>
                        <value>
                            <unit>rpm</unit>
                            <ignoreif>255</ignoreif>
                        </value>
                    </sample>
                    <sample>
                        <id>31</id>
                        <name>BikePower</name>
                        <type>uint16</type>
                        <value>
                            <unit>watt</unit>
                        </value>
                    </sample>
                    <sample>
                        <id>32</id>
                        <name>SwimmingStrokeCount</name>
                        <type>uint32</type>
                    </sample>
                </periodic>
                <episodic>
                    <sample>
                        <id>1</id>
                        <name>GPS test</name>
                        <type>buffer</type>
                        <length>16</length>
                    </sample>
                    <sample>
                        <id>2</id>
                        <name>gpsdata</name>
                        <type>buffer</type>
                        <length>?</length>
                    </sample>
                    <sample>
                        <id>3</id>
                        <name>accdata</name>
                        <type>buffer</type>
                        <length>?</length>
                    </sample>
                    <sample>
                        <id>4</id>
                        <name>logpause</name>
                        <type>buffer</type>
                        <length>0</length>
                    </sample>
                    <sample>
                        <id>5</id>
                        <name>logrestart</name>
                        <type>buffer</type>
                        <length>0</length>
                    </sample>
                    <sample>
                        <id>6</id>
                        <name>ibidata</name>
                        <type>buffer</type>
                        <length>64</length>
                    </sample>
                    <sample>
                        <id>7</id>
                        <name>ttff</name>
                        <type>uint16</type>
                        <value>
                            <unit>seconds</unit>
                            <scale>0.1</scale>
                        </value>
                    </sample>
                    <sample>
                        <id>8</id>
                        <name>distancesource</name>
                        <type>uint8</type>
                        <value>
                            <ignoreif>255</ignoreif>
                        </value>
                    </sample>
                    <sample>
                        <id>9</id>
                        <name>lapinfo</name>
                        <type>buffer</type>
                        <length>23</length>
                    </sample>
                    <sample>
                        <id>10</id>
                        <name>gpsuserdata</name>
                        <type>buffer</type>
                        <length>17</length>
                    </sample>
                    <sample>
                        <id>11</id>
                        <name>gpstestdata</name>
                        <type>buffer</type>
                        <length>61</length>
                    </sample>
                    <sample>
                        <id>12</id>
                        <name>timeref</name>
                        <type>buffer</type>
                        <length>14</length>
                    </sample>
                    <sample>
                        <id>19</id>
                        <name>ruledata</name>
                        <type>buffer</type>
                        <length>9</length>
                    </sample>
                </episodic>
            </training>
        </log>
        <glonass>
            <download/>
        </glonass>
    </options>    
  </device>

  <device name="Suunto Traverse Alpha" type="Emu" model="Loon">
    <port type="BLE">
      <match>
        <type>BLE</type>
      </match>
      <options>
        <gattserver>true</gattserver>
        <match>TraverseA *</match>
      </options>
    </port>
    <host type="NspHost">
      <match>
        <device>Loon</device>
      </match>
    </host>
    <protocols>
      <protocol type="BleFrame"/>
      <protocol type="SerialFrame"/>
      <protocol type="CRC32Protocol"/>
      <protocol type="NSP"/>
    </protocols>
    <options>
        <trainingprogram/>
        <firmware>
            <bootupdate>true</bootupdate>
        </firmware>
        <custommodeconfig>
            <intervaltimer>true</intervaltimer>
            <ruleconfig>true</ruleconfig>
            <custommodeid>true</custommodeid>
            <usehrlimits>true</usehrlimits>
            <autoscrolling>true</autoscrolling>
            <rulestoresize>200000</rulestoresize>
            <rulestorelocation>600000</rulestorelocation>
            <displaymode>true</displaymode>
            <navigationselection>true</navigationselection>
            <traverseDisplays>
                <cardinalCompass>
                    <match>
                        <swVersion op="gte">1.8.0</swVersion>
                    </match>
                </cardinalCompass>
                <altiGraph>
                    <match>
                        <swVersion op="gte">1.5.0</swVersion>
                    </match>
                </altiGraph>
                <moonCalendar>
                    <match>
                        <swVersion op="gte">1.9.0</swVersion>
                    </match>
                </moonCalendar>
                <huntingFishing>
                    <match>
                        <swVersion op="gte">1.9.0</swVersion>
                    </match>
                </huntingFishing>
            </traverseDisplays>
        </custommodeconfig>
        <log type="PMEM" version="2.0">
            <distancesources>
                <source>
                    <id>0</id>
                    <name>Bikepod</name>
                </source>
                <source>
                    <id>1</id>
                    <name>Footpod</name>
                </source>
                <source>
                    <id>2</id>
                    <name>Gps</name>
                </source>
                <source>
                    <id>3</id>
                    <name>Wrist</name>
                </source>
                <source>
                    <id>4</id>
                    <name>Indoorswimming</name>
                </source>
                <source>
                    <id>5</id>
                    <name>Outdoorswimming</name>
                </source>
            </distancesources>
            <event>
            </event>
            <training>
                <periodic>
                    <sample>
                        <id>1</id>
                        <name>Latitude</name>
                        <type>int32</type>
                        <value>
                            <unit>degrees</unit>
                            <scale>0.0000001</scale>
                            <limit>
                                <min>-90</min>
                                <max>90</max>
                            </limit>
                        </value>
                    </sample>
                    <sample>
                        <id>2</id>
                        <name>Longitude</name>
                        <type>int32</type>
                        <value>
                            <unit>degrees</unit>
                            <scale>0.0000001</scale>
                            <limit>
                                <min>-180</min>
                                <max>180</max>
                            </limit>
                        </value>
                    </sample>
                    <sample>
                        <id>3</id>
                        <name>Distance</name>
                        <type>uint32</type>
                        <value>
                            <unit>meters</unit>
                        </value>
                    </sample>
                    <sample>
                        <id>4</id>
                        <name>Speed</name>
                        <type>uint16</type>
                        <value>
                            <scale>0.01</scale>
                            <unit>m/s</unit>
                            <ignoreif>65535</ignoreif>
                        </value>
                    </sample>
                    <sample>
                        <id>5</id>
                        <name>HR</name>
                        <type>uint8</type>
                        <value>
                            <unit>bpm</unit>
                            <ignoreif>255</ignoreif>
                        </value>
                    </sample>
                    <sample>
                        <id>6</id>
                        <name>Time</name>
                        <type>uint32</type>
                        <value>
                            <unit>msec</unit>
                        </value>
                    </sample>
                    <sample>
                        <id>7</id>
                        <name>GPSSpeed</name>
                        <type>uint16</type>
                        <value>
                            <unit>m/s</unit>
                            <scale>0.01</scale>
                        </value>
                    </sample>
                    <sample>
                        <id>8</id>
                        <name>WristAccSpeed</name>
                        <type>uint16</type>
                        <value>
                            <unit>m/s</unit>
                            <scale>0.01</scale>
                        </value>
                    </sample>
                    <sample>
                        <id>9</id>
                        <name>BikePodSpeed</name>
                        <type>uint16</type>
                        <value>
                            <unit>m/s</unit>
                            <scale>0.01</scale>
                        </value>
                    </sample>
                    <sample>
                        <id>10</id>
                        <name>EHPE</name>
                        <type>uint32</type>
                        <value>
                            <unit>meters</unit>
                            <scale>0.01</scale>
                        </value>
                    </sample>
                    <sample>
                        <id>11</id>
                        <name>EVPE</name>
                        <type>uint32</type>
                        <value>
                            <unit>meters</unit>
                            <scale>0.01</scale>
                        </value>
                    </sample>
                    <sample>
                        <id>12</id>
                        <name>Altitude</name>
                        <type>int16</type>
                        <value>
                            <unit>meters</unit>
                            <limit>
                                <min>-1000</min>
                                <max>15000</max>
                            </limit>
                        </value>
                    </sample>
                    <sample>
                        <id>13</id>
                        <name>AbsPressure</name>
                        <type>uint16</type>
                        <value>
                            <unit>hpa</unit>
                            <scale>0.1</scale>
                        </value>
                    </sample>
                    <sample>
                        <id>14</id>
                        <name>EnergyConsumption</name>
                        <type>uint16</type>
                        <value>
                            <unit>hcal/min</unit>
                        </value>
                    </sample>
                    <sample>
                        <id>15</id>
                        <name>Temperature</name>
                        <type>int16</type>
                        <value>
                            <unit>celsius</unit>
                            <scale>0.1</scale>
                            <limit>
                                <min>-100</min>
                                <max>100</max>
                            </limit>
                        </value>
                    </sample>
                    <sample>
                        <id>16</id>
                        <name>BatteryCharge</name>
                        <type>uint8</type>
                        <value>
                            <unit>percent</unit>
                        </value>
                    </sample>
                    <sample>
                        <id>17</id>
                        <name>GPSAltitude</name>
                        <type>uint32</type>
                        <value>
                            <unit>meters</unit>
                            <scale>0.01</scale>
                            <limit>
                                <min>-1000</min>
                                <max>15000</max>
                            </limit>
                        </value>
                    </sample>
                    <sample>
                        <id>18</id>
                        <name>GPSHeading</name>
                        <type>uint16</type>
                        <value>
                            <unit>degrees</unit>
                            <scale>0.01</scale>
                            <limit>
                                <min>0</min>
                                <max>360</max>
                            </limit>
                            <ignoreif>65535</ignoreif>
                        </value>
                    </sample>
                    <sample>
                        <id>19</id>
                        <name>GpsHDOP</name>
                        <type>uint8</type>
                        <value>
                            <ignoreif>255</ignoreif>
                        </value>
                    </sample>
                    <sample>
                        <id>20</id>
                        <name>GpsVDOP</name>
                        <type>uint8</type>
                        <value>
                            <ignoreif>255</ignoreif>
                        </value>
                    </sample>
                    <sample>
                        <id>21</id>
                        <name>WristCadence</name>
                        <type>uint16</type>
                        <value>
                            <unit>rpm</unit>
                            <ignoreif>65535</ignoreif>
                        </value>
                    </sample>
                    <sample>
                        <id>22</id>
                        <name>SNR</name>
                        <type>buffer</type>
                        <length>16</length>
                    </sample>
                    <sample>
                        <id>23</id>
                        <name>NumberOfSatellites</name>
                        <type>uint8</type>
                        <value>
                            <ignoreif>255</ignoreif>
                        </value>
                    </sample>
                    <sample>
                        <id>24</id>
                        <name>SeaLevelPressure</name>
                        <type>int16</type>
                        <value>
                            <unit>hpa</unit>
                            <scale>0.1</scale>
                        </value>
                    </sample>
                    <sample>
                        <id>25</id>
                        <name>VerticalSpeed</name>
                        <type>int16</type>
                        <value>
                            <unit>m/s</unit>
                            <scale>0.01</scale>
                        </value>
                    </sample>
                    <sample>
                        <id>26</id>
                        <name>Cadence</name>
                        <type>uint8</type>
                        <value>
                            <unit>rpm</unit>
                            <ignoreif>255</ignoreif>
                        </value>
                    </sample>
                    <sample>
                        <id>31</id>
                        <name>BikePower</name>
                        <type>uint16</type>
                        <value>
                            <unit>watt</unit>
                        </value>
                    </sample>
                    <sample>
                        <id>32</id>
                        <name>SwimmingStrokeCount</name>
                        <type>uint32</type>
                    </sample>
                </periodic>
                <episodic>
                    <sample>
                        <id>1</id>
                        <name>GPS test</name>
                        <type>buffer</type>
                        <length>16</length>
                    </sample>
                    <sample>
                        <id>2</id>
                        <name>gpsdata</name>
                        <type>buffer</type>
                        <length>?</length>
                    </sample>
                    <sample>
                        <id>3</id>
                        <name>accdata</name>
                        <type>buffer</type>
                        <length>?</length>
                    </sample>
                    <sample>
                        <id>4</id>
                        <name>logpause</name>
                        <type>buffer</type>
                        <length>0</length>
                    </sample>
                    <sample>
                        <id>5</id>
                        <name>logrestart</name>
                        <type>buffer</type>
                        <length>0</length>
                    </sample>
                    <sample>
                        <id>6</id>
                        <name>ibidata</name>
                        <type>buffer</type>
                        <length>64</length>
                    </sample>
                    <sample>
                        <id>7</id>
                        <name>ttff</name>
                        <type>uint16</type>
                        <value>
                            <unit>seconds</unit>
                            <scale>0.1</scale>
                        </value>
                    </sample>
                    <sample>
                        <id>8</id>
                        <name>distancesource</name>
                        <type>uint8</type>
                        <value>
                            <ignoreif>255</ignoreif>
                        </value>
                    </sample>
                    <sample>
                        <id>9</id>
                        <name>lapinfo</name>
                        <type>buffer</type>
                        <length>23</length>
                    </sample>
                    <sample>
                        <id>10</id>
                        <name>gpsuserdata</name>
                        <type>buffer</type>
                        <length>17</length>
                    </sample>
                    <sample>
                        <id>11</id>
                        <name>gpstestdata</name>
                        <type>buffer</type>
                        <length>61</length>
                    </sample>
                    <sample>
                        <id>12</id>
                        <name>timeref</name>
                        <type>buffer</type>
                        <length>14</length>
                    </sample>
                    <sample>
                        <id>19</id>
                        <name>ruledata</name>
                        <type>buffer</type>
                        <length>9</length>
                    </sample>
                    <sample>
                        <id>32</id>
                        <name>shot</name>
                        <type>buffer</type>
                        <length>8</length>
                        <location>
                            <latitude>
                                <type>int32</type>
                                <value>
                                    <unit>degrees</unit>
                                    <scale>0.0000001</scale>
                                    <limit>
                                        <min>-90</min>
                                        <max>90</max>
                                    </limit>
                                    <ignoreif>2147483647</ignoreif>
                                </value>
                            </latitude>
                            <longitude>
                                <type>int32</type>
                                <value>
                                    <unit>degrees</unit>
                                    <scale>0.0000001</scale>
                                    <limit>
                                        <min>-180</min>
                                        <max>180</max>
                                    </limit>
                                    <ignoreif>2147483647</ignoreif>
                                </value>
                            </longitude>
                        </location>
                    </sample>
                    <sample>
                        <id>33</id>
                        <name>catch</name>
                        <type>buffer</type>
                        <length>16</length>
                        <location>
                            <latitude>
                                <type>int32</type>
                                <value>
                                    <unit>degrees</unit>
                                    <scale>0.0000001</scale>
                                    <limit>
                                        <min>-90</min>
                                        <max>90</max>
                                    </limit>
                                    <ignoreif>2147483647</ignoreif>
                                </value>
                            </latitude>
                            <longitude>
                                <type>int32</type>
                                <value>
                                    <unit>degrees</unit>
                                    <scale>0.0000001</scale>
                                    <limit>
                                        <min>-180</min>
                                        <max>180</max>
                                    </limit>
                                    <ignoreif>2147483647</ignoreif>
                                </value>
                            </longitude>
                        </location>
                    </sample>
                </episodic>
            </training>
        </log>
        <glonass>
            <download/>
        </glonass>
    </options>
  </device>

  <device name="Suunto Ambit3 Vertical" type="Emu" model="Kaka">
    <port type="BLE">
      <match>
        <type>BLE</type>
      </match>
      <options>
        <gattserver>true</gattserver>
        <match>Ambit3V *</match>
      </options>
    </port>
    <host type="NspHost">
      <match>
        <device>Kaka</device>
      </match>
    </host>
    <protocols>
      <protocol type="BleFrame"/>
      <protocol type="SerialFrame"/>
      <protocol type="CRC32Protocol"/>
      <protocol type="NSP"/>
    </protocols>
    <options>
      <trainingprogram/>
      <firmware>
        <bootupdate>true</bootupdate>
      </firmware>
      <custommodeconfig>
        <intervaltimer>true</intervaltimer>
        <ruleconfig>true</ruleconfig>
        <custommodeid>true</custommodeid>
        <usehrlimits>true</usehrlimits>
        <autoscrolling>true</autoscrolling>
        <rulestoresize>200000</rulestoresize>
        <rulestorelocation>600000</rulestorelocation>
        <displaymode>true</displaymode>
        <navigationselection>true</navigationselection>
      </custommodeconfig>
      <log type="PMEM" version="2.0">
        <distancesources>
          <source>
            <id>0</id>
            <name>Bikepod</name>
          </source>
          <source>
            <id>1</id>
            <name>Footpod</name>
          </source>
          <source>
            <id>2</id>
            <name>Gps</name>
          </source>
          <source>
            <id>3</id>
            <name>Wrist</name>
          </source>
          <source>
            <id>4</id>
            <name>Indoorswimming</name>
          </source>
          <source>
            <id>5</id>
            <name>Outdoorswimming</name>
          </source>
        </distancesources>
        <event>
        </event>
        <training>
          <periodic>
            <sample>
              <id>1</id>
              <name>Latitude</name>
              <type>int32</type>
              <value>
                <unit>degrees</unit>
                <scale>0.0000001</scale>
                <limit>
                  <min>-90</min>
                  <max>90</max>
                </limit>
              </value>
            </sample>
            <sample>
              <id>2</id>
              <name>Longitude</name>
              <type>int32</type>
              <value>
                <unit>degrees</unit>
                <scale>0.0000001</scale>
                <limit>
                  <min>-180</min>
                  <max>180</max>
                </limit>
              </value>
            </sample>
            <sample>
              <id>3</id>
              <name>Distance</name>
              <type>uint32</type>
              <value>
                <unit>meters</unit>
              </value>
            </sample>
            <sample>
              <id>4</id>
              <name>Speed</name>
              <type>uint16</type>
              <value>
                <scale>0.01</scale>
                <unit>m/s</unit>
                <ignoreif>65535</ignoreif>
              </value>
            </sample>
            <sample>
              <id>5</id>
              <name>HR</name>
              <type>uint8</type>
              <value>
                <unit>bpm</unit>
                <ignoreif>255</ignoreif>
              </value>
            </sample>
            <sample>
              <id>6</id>
              <name>Time</name>
              <type>uint32</type>
              <value>
                <unit>msec</unit>
              </value>
            </sample>
            <sample>
              <id>7</id>
              <name>GPSSpeed</name>
              <type>uint16</type>
              <value>
                <unit>m/s</unit>
                <scale>0.01</scale>
              </value>
            </sample>
            <sample>
              <id>8</id>
              <name>WristAccSpeed</name>
              <type>uint16</type>
              <value>
                <unit>m/s</unit>
                <scale>0.01</scale>
              </value>
            </sample>
            <sample>
              <id>9</id>
              <name>BikePodSpeed</name>
              <type>uint16</type>
              <value>
                <unit>m/s</unit>
                <scale>0.01</scale>
              </value>
            </sample>
            <sample>
              <id>10</id>
              <name>EHPE</name>
              <type>uint32</type>
              <value>
                <unit>meters</unit>
                <scale>0.01</scale>
              </value>
            </sample>
            <sample>
              <id>11</id>
              <name>EVPE</name>
              <type>uint32</type>
              <value>
                <unit>meters</unit>
                <scale>0.01</scale>
              </value>
            </sample>
            <sample>
              <id>12</id>
              <name>Altitude</name>
              <type>int16</type>
              <value>
                <unit>meters</unit>
                <limit>
                  <min>-1000</min>
                  <max>15000</max>
                </limit>
              </value>
            </sample>
            <sample>
              <id>13</id>
              <name>AbsPressure</name>
              <type>uint16</type>
              <value>
                <unit>hpa</unit>
                <scale>0.1</scale>
              </value>
            </sample>
            <sample>
              <id>14</id>
              <name>EnergyConsumption</name>
              <type>uint16</type>
              <value>
                <unit>hcal/min</unit>
              </value>
            </sample>
            <sample>
              <id>15</id>
              <name>Temperature</name>
              <type>int16</type>
              <value>
                <unit>celsius</unit>
                <scale>0.1</scale>
                <limit>
                  <min>-100</min>
                  <max>100</max>
                </limit>
              </value>
            </sample>
            <sample>
              <id>16</id>
              <name>BatteryCharge</name>
              <type>uint8</type>
              <value>
                <unit>percent</unit>
              </value>
            </sample>
            <sample>
              <id>17</id>
              <name>GPSAltitude</name>
              <type>uint32</type>
              <value>
                <unit>meters</unit>
                <scale>0.01</scale>
                <limit>
                  <min>-1000</min>
                  <max>15000</max>
                </limit>
              </value>
            </sample>
            <sample>
              <id>18</id>
              <name>GPSHeading</name>
              <type>uint16</type>
              <value>
                <unit>degrees</unit>
                <scale>0.01</scale>
                <limit>
                  <min>0</min>
                  <max>360</max>
                </limit>
                <ignoreif>65535</ignoreif>
              </value>
            </sample>
            <sample>
              <id>19</id>
              <name>GpsHDOP</name>
              <type>uint8</type>
              <value>
                <ignoreif>255</ignoreif>
              </value>
            </sample>
            <sample>
              <id>20</id>
              <name>GpsVDOP</name>
              <type>uint8</type>
              <value>
                <ignoreif>255</ignoreif>
              </value>
            </sample>
            <sample>
              <id>21</id>
              <name>WristCadence</name>
              <type>uint16</type>
              <value>
                <unit>rpm</unit>
                <ignoreif>65535</ignoreif>
              </value>
            </sample>
            <sample>
              <id>22</id>
              <name>SNR</name>
              <type>buffer</type>
              <length>16</length>
            </sample>
            <sample>
              <id>23</id>
              <name>NumberOfSatellites</name>
              <type>uint8</type>
              <value>
                <ignoreif>255</ignoreif>
              </value>
            </sample>
            <sample>
              <id>24</id>
              <name>SeaLevelPressure</name>
              <type>int16</type>
              <value>
                <unit>hpa</unit>
                <scale>0.1</scale>
              </value>
            </sample>
            <sample>
              <id>25</id>
              <name>VerticalSpeed</name>
              <type>int16</type>
              <value>
                <unit>m/s</unit>
                <scale>0.01</scale>
              </value>
            </sample>
            <sample>
              <id>26</id>
              <name>Cadence</name>
              <type>uint8</type>
              <value>
                <unit>rpm</unit>
                <ignoreif>255</ignoreif>
              </value>
            </sample>
            <sample>
              <id>31</id>
              <name>BikePower</name>
              <type>uint16</type>
              <value>
                <unit>watt</unit>
              </value>
            </sample>
            <sample>
              <id>32</id>
              <name>SwimmingStrokeCount</name>
              <type>uint32</type>
            </sample>
            <sample>
              <id>40</id>
              <name>RelativePerformanceLevel</name>
              <type>int8</type>
              <value>
                <limit>
                  <min>0.80</min>
                  <max>1.20</max>
                </limit>
                <ignoreif>-127</ignoreif>
                <scale>0.01</scale>
                <offset>100</offset>
                <precision>2</precision>
              </value>
            </sample>
          </periodic>
          <episodic>
            <sample>
              <id>1</id>
              <name>GPS test</name>
              <type>buffer</type>
              <length>16</length>
            </sample>
            <sample>
              <id>2</id>
              <name>gpsdata</name>
              <type>buffer</type>
              <length>?</length>
            </sample>
            <sample>
              <id>3</id>
              <name>accdata</name>
              <type>buffer</type>
              <length>?</length>
            </sample>
            <sample>
              <id>4</id>
              <name>logpause</name>
              <type>buffer</type>
              <length>0</length>
            </sample>
            <sample>
              <id>5</id>
              <name>logrestart</name>
              <type>buffer</type>
              <length>0</length>
            </sample>
            <sample>
              <id>6</id>
              <name>ibidata</name>
              <type>buffer</type>
              <length>64</length>
            </sample>
            <sample>
              <id>7</id>
              <name>ttff</name>
              <type>uint16</type>
              <value>
                <unit>seconds</unit>
                <scale>0.1</scale>
              </value>
            </sample>
            <sample>
              <id>8</id>
              <name>distancesource</name>
              <type>uint8</type>
              <value>
                <ignoreif>255</ignoreif>
              </value>
            </sample>
            <sample>
              <id>9</id>
              <name>lapinfo</name>
              <type>buffer</type>
              <length>23</length>
            </sample>
            <sample>
              <id>10</id>
              <name>gpsuserdata</name>
              <type>buffer</type>
              <length>17</length>
            </sample>
            <sample>
              <id>11</id>
              <name>gpstestdata</name>
              <type>buffer</type>
              <length>61</length>
            </sample>
            <sample>
              <id>12</id>
              <name>timeref</name>
              <type>buffer</type>
              <length>14</length>
            </sample>
            <sample>
              <id>19</id>
              <name>ruledata</name>
              <type>buffer</type>
              <length>9</length>
            </sample>
          </episodic>
        </training>
      </log>
      <glonass>
        <download/>
      </glonass>
    </options>
  </device>

  <device name="Suunto EON Steel" type="Guru" model="Guru">
    <port type="BLE">
      <match>
      <type>BLE</type>
      </match>
    </port>
    <host type="NspHost">
      <match>
        <device>GURU</device>
      </match>
    </host>
    <protocols>
      <protocol type="BleFrame"/>
      <protocol type="SerialFrame"/>
      <protocol type="CRC32Protocol"/>
      <protocol type="NSP"/>
    </protocols>
    <options>
      <firmware>
        <bootupdate>true</bootupdate>
      </firmware>
    </options>
  </device>

  <device name="Suunto EON Steel Black" type="Guru" model="GuruE">
    <port type="BLE">
      <match>
      <type>BLE</type>
      </match>
    </port>
    <host type="NspHost">
      <match>
        <device>GURUE</device>
      </match>
    </host>
    <protocols>
      <protocol type="BleFrame"/>
      <protocol type="SerialFrame"/>
      <protocol type="CRC32Protocol"/>
      <protocol type="NSP"/>
    </protocols>
    <options>
      <firmware>
        <bootupdate>true</bootupdate>
      </firmware>
    </options>
  </device>

  <device name="Suunto EON Core" type="Guru" model="Guru2">
    <port type="BLE">
      <match>
        <type>BLE</type>
      </match>
    </port>
    <host type="NspHost">
      <match>
        <device>GURU2</device>
      </match>
    </host>
    <protocols>
      <protocol type="BleFrame"/>
      <protocol type="SerialFrame"/>
      <protocol type="CRC32Protocol"/>
      <protocol type="NSP"/>
    </protocols>
    <options>
      <firmware>
        <bootupdate>true</bootupdate>
      </firmware>
    </options>
  </device>
  
  <device name="Suunto D5" type="Guru" model="Guru3">
      <port type="BLE">
          <match>
              <type>BLE</type>
          </match>
      </port>
      <host type="NspHost">
          <match>
              <device>GURU3</device>
          </match>
      </host>
      <protocols>
          <protocol type="BleFrame"/>
          <protocol type="SerialFrame"/>
          <protocol type="CRC32Protocol"/>
          <protocol type="NSP"/>
      </protocols>
      <options>
          <firmware>
              <bootupdate>true</bootupdate>
          </firmware>
      </options>
  </device>
  
  <device name="Suunto Ambit3 Peak" type="Emu" model="Emu">
    <port type="HID">
      <driver>BluebirdDriver</driver>
      <match>
        <vid>0x1493</vid>
        <pid>0x001b</pid>
      </match>
    </port>
    <host type="NspHost">
      <match>
        <device>Emu</device>
        <swVersion op="gte">1.5.36</swVersion>
      </match>
    </host>
    <protocols>
      <protocol type="UsbFrame"/>
      <protocol type="NSP"/>
    </protocols>
    <options>
      <trainingprogram/>
      <firmware>
        <bootupdate>true</bootupdate>
      </firmware>
      <custommodeconfig>
        <intervaltimer>true</intervaltimer>
        <ruleconfig>true</ruleconfig>
        <custommodeid>true</custommodeid>
        <usehrlimits>true</usehrlimits>
        <autoscrolling>true</autoscrolling>
        <rulestoresize>200000</rulestoresize>
        <rulestorelocation>600000</rulestorelocation>
        <displaymode>true</displaymode>
        <navigationselection>true</navigationselection>
      </custommodeconfig>
      <log type="PMEM" version="2.0">
		<distancesources>
			<source>
				<id>0</id>
				<name>Bikepod</name>
			</source>
			<source>
				<id>1</id>
				<name>Footpod</name>
			</source>
			<source>
				<id>2</id>
				<name>Gps</name>
			</source>
			<source>
				<id>3</id>
				<name>Wrist</name>
			</source>
			<source>
				<id>4</id>
				<name>Indoorswimming</name>
			</source>
			<source>
				<id>5</id>
				<name>Outdoorswimming</name>
			</source>
		</distancesources>
        <event>
        </event>
        <training>
          <periodic>
            <sample>
              <id>1</id>
              <name>Latitude</name>
              <type>int32</type>
              <value>
                <unit>degrees</unit>
                <scale>0.0000001</scale>
                <limit>
                  <min>-90</min>
                  <max>90</max>
                </limit>
              </value>
            </sample>
            <sample>
              <id>2</id>
              <name>Longitude</name>
              <type>int32</type>
              <value>
                <unit>degrees</unit>
                <scale>0.0000001</scale>
                <limit>
                  <min>-180</min>
                  <max>180</max>
                </limit>
              </value>
            </sample>
            <sample>
              <id>3</id>
              <name>Distance</name>
              <type>uint32</type>
              <value>
                <unit>meters</unit>
              </value>
            </sample>
            <sample>
              <id>4</id>
              <name>Speed</name>
              <type>uint16</type>
              <value>
                <scale>0.01</scale>
                <unit>m/s</unit>
                <ignoreif>65535</ignoreif>
              </value>
            </sample>
            <sample>
              <id>5</id>
              <name>HR</name>
              <type>uint8</type>
              <value>
                <unit>bpm</unit>
                <ignoreif>255</ignoreif>
              </value>
            </sample>
            <sample>
              <id>6</id>
              <name>Time</name>
              <type>uint32</type>
              <value>
                <unit>msec</unit>
              </value>
            </sample>
            <sample>
              <id>7</id>
              <name>GPSSpeed</name>
              <type>uint16</type>
              <value>
                <unit>m/s</unit>
                <scale>0.01</scale>
              </value>
            </sample>
            <sample>
              <id>10</id>
              <name>EHPE</name>
              <type>uint32</type>
              <value>
                <unit>meters</unit>
                <scale>0.01</scale>
              </value>
            </sample>
            <sample>
              <id>11</id>
              <name>EVPE</name>
              <type>uint32</type>
              <value>
                <unit>meters</unit>
                <scale>0.01</scale>
              </value>
            </sample>
            <sample>
              <id>12</id>
              <name>Altitude</name>
              <type>int16</type>
              <value>
                <unit>meters</unit>
                <limit>
                  <min>-1000</min>
                  <max>15000</max>
                </limit>
              </value>
            </sample>
            <sample>
              <id>13</id>
              <name>AbsPressure</name>
              <type>uint16</type>
              <value>
                <unit>hpa</unit>
                <scale>0.1</scale>
              </value>
            </sample>
            <sample>
              <id>14</id>
              <name>EnergyConsumption</name>
              <type>uint16</type>
              <value>
                <unit>hcal/min</unit>
              </value>
            </sample>
            <sample>
              <id>15</id>
              <name>Temperature</name>
              <type>int16</type>
              <value>
                <unit>celsius</unit>
                <scale>0.1</scale>
                <limit>
                  <min>-100</min>
                  <max>100</max>
                </limit>
              </value>
            </sample>
            <sample>
              <id>16</id>
              <name>BatteryCharge</name>
              <type>uint8</type>
              <value>
                <unit>percent</unit>
              </value>
            </sample>
            <sample>
              <id>17</id>
              <name>GPSAltitude</name>
              <type>uint32</type>
              <value>
                <unit>meters</unit>
                <scale>0.01</scale>
                <limit>
                  <min>-1000</min>
                  <max>15000</max>
                </limit>
              </value>
            </sample>
            <sample>
              <id>18</id>
              <name>GPSHeading</name>
              <type>uint16</type>
              <value>
                <unit>degrees</unit>
                <scale>0.01</scale>
                <limit>
                  <min>0</min>
                  <max>360</max>
                </limit>
                <ignoreif>65535</ignoreif>
              </value>
            </sample>
            <sample>
              <id>19</id>
              <name>GpsHDOP</name>
              <type>uint8</type>
              <value>
                <ignoreif>255</ignoreif>
              </value>
            </sample>
            <sample>
              <id>20</id>
              <name>GpsVDOP</name>
              <type>uint8</type>
              <value>
                <ignoreif>255</ignoreif>
              </value>
            </sample>
            <sample>
              <id>21</id>
              <name>WristCadence</name>
              <type>uint16</type>
              <value>
                <unit>rpm</unit>
                <ignoreif>65535</ignoreif>
              </value>
            </sample>
            <sample>
              <id>22</id>
              <name>SNR</name>
              <type>buffer</type>
              <length>16</length>
            </sample>
            <sample>
              <id>23</id>
              <name>NumberOfSatellites</name>
              <type>uint8</type>
              <value>
                <ignoreif>255</ignoreif>
              </value>
            </sample>
            <sample>
              <id>24</id>
              <name>SeaLevelPressure</name>
              <type>int16</type>
              <value>
                <unit>hpa</unit>
                <scale>0.1</scale>
              </value>
            </sample>
            <sample>
              <id>25</id>
              <name>VerticalSpeed</name>
              <type>int16</type>
              <value>
                <unit>m/s</unit>
                <scale>0.01</scale>
              </value>
            </sample>
            <sample>
              <id>26</id>
              <name>Cadence</name>
              <type>uint8</type>
              <value>
                <unit>rpm</unit>
                <ignoreif>255</ignoreif>
              </value>
            </sample>
            <sample>
              <id>31</id>
              <name>BikePower</name>
              <type>uint16</type>
              <value>
                <unit>watt</unit>
              </value>
            </sample>
            <sample>
              <id>32</id>
              <name>SwimmingStrokeCount</name>
              <type>uint32</type>
            </sample>
            <sample>
                <id>40</id>
                <name>RelativePerformanceLevel</name>
                <type>int8</type>
                <value>
                    <limit>
                        <min>0.80</min>
                        <max>1.20</max>
                    </limit>
                    <ignoreif>-127</ignoreif>
                    <scale>0.01</scale>
                    <offset>100</offset>
                    <precision>2</precision>
                </value>
            </sample>
          </periodic>
          <episodic>
            <sample>
              <id>1</id>
              <name>GPS test</name>
              <type>buffer</type>
              <length>16</length>
            </sample>
            <sample>
              <id>2</id>
              <name>gpsdata</name>
              <type>buffer</type>
              <length>?</length>
            </sample>
            <sample>
              <id>3</id>
              <name>accdata</name>
              <type>buffer</type>
              <length>?</length>
            </sample>
            <sample>
              <id>4</id>
              <name>logpause</name>
              <type>buffer</type>
              <length>0</length>
            </sample>
            <sample>
              <id>5</id>
              <name>logrestart</name>
              <type>buffer</type>
              <length>0</length>
            </sample>
            <sample>
              <id>6</id>
              <name>ibidata</name>
              <type>buffer</type>
              <length>64</length>
            </sample>
            <sample>
              <id>7</id>
              <name>ttff</name>
              <type>uint16</type>
              <value>
                <unit>seconds</unit>
                <scale>0.1</scale>
              </value>
            </sample>
            <sample>
              <id>8</id>
              <name>distancesource</name>
              <type>uint8</type>
              <value>
                <ignoreif>255</ignoreif>
              </value>
            </sample>
            <sample>
              <id>9</id>
              <name>lapinfo</name>
              <type>buffer</type>
              <length>23</length>
            </sample>
            <sample>
              <id>10</id>
              <name>gpsuserdata</name>
              <type>buffer</type>
              <length>17</length>
            </sample>
            <sample>
              <id>11</id>
              <name>gpstestdata</name>
              <type>buffer</type>
              <length>61</length>
            </sample>
            <sample>
              <id>12</id>
              <name>timeref</name>
              <type>buffer</type>
              <length>14</length>
            </sample>
            <sample>
              <id>19</id>
              <name>ruledata</name>
              <type>buffer</type>
              <length>9</length>
            </sample>
          </episodic>
        </training>
      </log>
    </options>
  </device>

  <device name="Suunto Ambit3 Sport" type="Emu" model="Finch">
    <port type="HID">
      <driver>BluebirdDriver</driver>
      <match>
        <vid>0x1493</vid>
        <pid>0x001c</pid>
      </match>
    </port>
    <host type="NspHost">
      <match>
        <device>Finch</device>
        <swVersion op="gte">1.5.36</swVersion>
      </match>
    </host>
    <protocols>
      <protocol type="UsbFrame"/>
      <protocol type="NSP"/>
    </protocols>
    <options>
      <trainingprogram/>
      <firmware>
        <bootupdate>true</bootupdate>
      </firmware>
      <custommodeconfig>
        <intervaltimer>true</intervaltimer>
        <ruleconfig>true</ruleconfig>
        <custommodeid>true</custommodeid>
        <usehrlimits>true</usehrlimits>
        <autoscrolling>true</autoscrolling>
        <rulestoresize>200000</rulestoresize>
        <rulestorelocation>600000</rulestorelocation>
        <displaymode>true</displaymode>
        <navigationselection>true</navigationselection>
      </custommodeconfig>
      <log type="PMEM" version="2.0">
		<distancesources>
			<source>
				<id>0</id>
				<name>Bikepod</name>
			</source>
			<source>
				<id>1</id>
				<name>Footpod</name>
			</source>
			<source>
				<id>2</id>
				<name>Gps</name>
			</source>
			<source>
				<id>3</id>
				<name>Wrist</name>
			</source>
			<source>
				<id>4</id>
				<name>Indoorswimming</name>
			</source>
			<source>
				<id>5</id>
				<name>Outdoorswimming</name>
			</source>
		</distancesources>
        <event>
        </event>
        <training>
          <periodic>
            <sample>
              <id>1</id>
              <name>Latitude</name>
              <type>int32</type>
              <value>
                <unit>degrees</unit>
                <scale>0.0000001</scale>
                <limit>
                  <min>-90</min>
                  <max>90</max>
                </limit>
              </value>
            </sample>
            <sample>
              <id>2</id>
              <name>Longitude</name>
              <type>int32</type>
              <value>
                <unit>degrees</unit>
                <scale>0.0000001</scale>
                <limit>
                  <min>-180</min>
                  <max>180</max>
                </limit>
              </value>
            </sample>
            <sample>
              <id>3</id>
              <name>Distance</name>
              <type>uint32</type>
              <value>
                <unit>meters</unit>
              </value>
            </sample>
            <sample>
              <id>4</id>
              <name>Speed</name>
              <type>uint16</type>
              <value>
                <scale>0.01</scale>
                <unit>m/s</unit>
                <ignoreif>65535</ignoreif>
              </value>
            </sample>
            <sample>
              <id>5</id>
              <name>HR</name>
              <type>uint8</type>
              <value>
                <unit>bpm</unit>
                <ignoreif>255</ignoreif>
              </value>
            </sample>
            <sample>
              <id>6</id>
              <name>Time</name>
              <type>uint32</type>
              <value>
                <unit>msec</unit>
              </value>
            </sample>
            <sample>
              <id>7</id>
              <name>GPSSpeed</name>
              <type>uint16</type>
              <value>
                <unit>m/s</unit>
                <scale>0.01</scale>
              </value>
            </sample>
            <sample>
              <id>10</id>
              <name>EHPE</name>
              <type>uint32</type>
              <value>
                <unit>meters</unit>
                <scale>0.01</scale>
              </value>
            </sample>
            <sample>
              <id>11</id>
              <name>EVPE</name>
              <type>uint32</type>
              <value>
                <unit>meters</unit>
                <scale>0.01</scale>
              </value>
            </sample>
            <sample>
              <id>12</id>
              <name>Altitude</name>
              <type>int16</type>
              <value>
                <unit>meters</unit>
                <limit>
                  <min>-1000</min>
                  <max>15000</max>
                </limit>
              </value>
            </sample>
            <sample>
              <id>13</id>
              <name>AbsPressure</name>
              <type>uint16</type>
              <value>
                <unit>hpa</unit>
                <scale>0.1</scale>
              </value>
            </sample>
            <sample>
              <id>14</id>
              <name>EnergyConsumption</name>
              <type>uint16</type>
              <value>
                <unit>hcal/min</unit>
              </value>
            </sample>
            <sample>
              <id>15</id>
              <name>Temperature</name>
              <type>int16</type>
              <value>
                <unit>celsius</unit>
                <scale>0.1</scale>
                <limit>
                  <min>-100</min>
                  <max>100</max>
                </limit>
              </value>
            </sample>
            <sample>
              <id>16</id>
              <name>BatteryCharge</name>
              <type>uint8</type>
              <value>
                <unit>percent</unit>
              </value>
            </sample>
            <sample>
              <id>17</id>
              <name>GPSAltitude</name>
              <type>uint32</type>
              <value>
                <unit>meters</unit>
                <scale>0.01</scale>
                <limit>
                  <min>-1000</min>
                  <max>15000</max>
                </limit>
              </value>
            </sample>
            <sample>
              <id>18</id>
              <name>GPSHeading</name>
              <type>uint16</type>
              <value>
                <unit>degrees</unit>
                <scale>0.01</scale>
                <limit>
                  <min>0</min>
                  <max>360</max>
                </limit>
                <ignoreif>65535</ignoreif>
              </value>
            </sample>
            <sample>
              <id>19</id>
              <name>GpsHDOP</name>
              <type>uint8</type>
              <value>
                <ignoreif>255</ignoreif>
              </value>
            </sample>
            <sample>
              <id>20</id>
              <name>GpsVDOP</name>
              <type>uint8</type>
              <value>
                <ignoreif>255</ignoreif>
              </value>
            </sample>
            <sample>
              <id>23</id>
              <name>NumberOfSatellites</name>
              <type>uint8</type>
              <value>
                <ignoreif>255</ignoreif>
              </value>
            </sample>
            <sample>
              <id>24</id>
              <name>SeaLevelPressure</name>
              <type>int16</type>
              <value>
                <unit>hpa</unit>
                <scale>0.1</scale>
              </value>
            </sample>
            <sample>
              <id>25</id>
              <name>VerticalSpeed</name>
              <type>int16</type>
              <value>
                <unit>m/s</unit>
                <scale>0.01</scale>
              </value>
            </sample>
            <sample>
              <id>26</id>
              <name>Cadence</name>
              <type>uint8</type>
              <value>
                <unit>rpm</unit>
                <ignoreif>255</ignoreif>
              </value>
            </sample>
            <sample>
              <id>31</id>
              <name>BikePower</name>
              <type>uint16</type>
              <value>
                <unit>watt</unit>
              </value>
            </sample>
            <sample>
              <id>32</id>
              <name>SwimmingStrokeCount</name>
              <type>uint32</type>
            </sample>
            <sample>
              <id>40</id>
              <name>RelativePerformanceLevel</name>
              <type>int8</type>
              <value>
                <limit>
                  <min>0.80</min>
                  <max>1.20</max>
                </limit>
                <ignoreif>-127</ignoreif>
                <scale>0.01</scale>
                <offset>100</offset>
                <precision>2</precision>
              </value>
            </sample>
          </periodic>
          <episodic>
            <sample>
              <id>1</id>
              <name>GPS test</name>
              <type>buffer</type>
              <length>16</length>
            </sample>
            <sample>
              <id>2</id>
              <name>gpsdata</name>
              <type>buffer</type>
              <length>?</length>
            </sample>
            <sample>
              <id>3</id>
              <name>accdata</name>
              <type>buffer</type>
              <length>?</length>
            </sample>
            <sample>
              <id>4</id>
              <name>logpause</name>
              <type>buffer</type>
              <length>0</length>
            </sample>
            <sample>
              <id>5</id>
              <name>logrestart</name>
              <type>buffer</type>
              <length>0</length>
            </sample>
            <sample>
              <id>6</id>
              <name>ibidata</name>
              <type>buffer</type>
              <length>64</length>
            </sample>
            <sample>
              <id>7</id>
              <name>ttff</name>
              <type>uint16</type>
              <value>
                <unit>seconds</unit>
                <scale>0.1</scale>
              </value>
            </sample>
            <sample>
              <id>8</id>
              <name>distancesource</name>
              <type>uint8</type>
              <value>
                <ignoreif>255</ignoreif>
              </value>
            </sample>
            <sample>
              <id>9</id>
              <name>lapinfo</name>
              <type>buffer</type>
              <length>23</length>
            </sample>
            <sample>
              <id>10</id>
              <name>gpsuserdata</name>
              <type>buffer</type>
              <length>17</length>
            </sample>
            <sample>
              <id>11</id>
              <name>gpstestdata</name>
              <type>buffer</type>
              <length>61</length>
            </sample>
            <sample>
              <id>12</id>
              <name>timeref</name>
              <type>buffer</type>
              <length>14</length>
            </sample>
            <sample>
              <id>19</id>
              <name>ruledata</name>
              <type>buffer</type>
              <length>9</length>
            </sample>
          </episodic>
        </training>
      </log>
    </options>
  </device>

  <device name="Suunto Ambit3 Run" type="Emu" model="Ibisbill">
    <port type="HID">
        <driver>BluebirdDriver</driver>
        <match>
            <vid>0x1493</vid>
            <pid>0x001e</pid>
        </match>
    </port>
    <host type="NspHost">
        <match>
            <device>Ibisbill</device>
            <swVersion op="gte">1.5.36</swVersion>
        </match>
    </host>
    <protocols>
        <protocol type="UsbFrame"/>
        <protocol type="NSP"/>
    </protocols>
    <options>
        <trainingprogram/>
        <firmware>
            <bootupdate>true</bootupdate>
        </firmware>
        <custommodeconfig>
            <intervaltimer>true</intervaltimer>
            <ruleconfig>true</ruleconfig>
            <custommodeid>true</custommodeid>
            <usehrlimits>true</usehrlimits>
            <autoscrolling>true</autoscrolling>
            <rulestoresize>200000</rulestoresize>
            <rulestorelocation>600000</rulestorelocation>
            <displaymode>true</displaymode>
            <navigationselection>true</navigationselection>
        </custommodeconfig>
        <log type="PMEM" version="2.0">
            <distancesources>
                <source>
                    <id>0</id>
                    <name>Bikepod</name>
                </source>
                <source>
                    <id>1</id>
                    <name>Footpod</name>
                </source>
                <source>
                    <id>2</id>
                    <name>Gps</name>
                </source>
                <source>
                    <id>3</id>
                    <name>Wrist</name>
                </source>
                <source>
                    <id>4</id>
                    <name>Indoorswimming</name>
                </source>
                <source>
                    <id>5</id>
                    <name>Outdoorswimming</name>
                </source>
            </distancesources>
            <event>
            </event>
            <training>
                <periodic>
                    <sample>
                        <id>1</id>
                        <name>Latitude</name>
                        <type>int32</type>
                        <value>
                            <unit>degrees</unit>
                            <scale>0.0000001</scale>
                            <limit>
                                <min>-90</min>
                                <max>90</max>
                            </limit>
                        </value>
                    </sample>
                    <sample>
                        <id>2</id>
                        <name>Longitude</name>
                        <type>int32</type>
                        <value>
                            <unit>degrees</unit>
                            <scale>0.0000001</scale>
                            <limit>
                                <min>-180</min>
                                <max>180</max>
                            </limit>
                        </value>
                    </sample>
                    <sample>
                        <id>3</id>
                        <name>Distance</name>
                        <type>uint32</type>
                        <value>
                            <unit>meters</unit>
                        </value>
                    </sample>
                    <sample>
                        <id>4</id>
                        <name>Speed</name>
                        <type>uint16</type>
                        <value>
                            <scale>0.01</scale>
                            <unit>m/s</unit>
                            <ignoreif>65535</ignoreif>
                        </value>
                    </sample>
                    <sample>
                        <id>5</id>
                        <name>HR</name>
                        <type>uint8</type>
                        <value>
                            <unit>bpm</unit>
                            <ignoreif>255</ignoreif>
                        </value>
                    </sample>
                    <sample>
                        <id>6</id>
                        <name>Time</name>
                        <type>uint32</type>
                        <value>
                            <unit>msec</unit>
                        </value>
                    </sample>
                    <sample>
                        <id>7</id>
                        <name>GPSSpeed</name>
                        <type>uint16</type>
                        <value>
                            <unit>m/s</unit>
                            <scale>0.01</scale>
                        </value>
                    </sample>
                    <sample>
                        <id>8</id>
                        <name>WristAccSpeed</name>
                        <type>uint16</type>
                        <value>
                            <unit>m/s</unit>
                            <scale>0.01</scale>
                        </value>
                    </sample>
                    <sample>
                        <id>9</id>
                        <name>BikePodSpeed</name>
                        <type>uint16</type>
                        <value>
                            <unit>m/s</unit>
                            <scale>0.01</scale>
                        </value>
                    </sample>
                    <sample>
                        <id>10</id>
                        <name>EHPE</name>
                        <type>uint32</type>
                        <value>
                            <unit>meters</unit>
                            <scale>0.01</scale>
                        </value>
                    </sample>
                    <sample>
                        <id>11</id>
                        <name>EVPE</name>
                        <type>uint32</type>
                        <value>
                            <unit>meters</unit>
                            <scale>0.01</scale>
                        </value>
                    </sample>
                    <sample>
                        <id>12</id>
                        <name>Altitude</name>
                        <type>int16</type>
                        <value>
                            <unit>meters</unit>
                            <limit>
                                <min>-1000</min>
                                <max>15000</max>
                            </limit>
                        </value>
                    </sample>
                    <sample>
                        <id>13</id>
                        <name>AbsPressure</name>
                        <type>uint16</type>
                        <value>
                            <unit>hpa</unit>
                            <scale>0.1</scale>
                        </value>
                    </sample>
                    <sample>
                        <id>14</id>
                        <name>EnergyConsumption</name>
                        <type>uint16</type>
                        <value>
                            <unit>hcal/min</unit>
                        </value>
                    </sample>
                    <sample>
                        <id>15</id>
                        <name>Temperature</name>
                        <type>int16</type>
                        <value>
                            <unit>celsius</unit>
                            <scale>0.1</scale>
                            <limit>
                                <min>-100</min>
                                <max>100</max>
                            </limit>
                        </value>
                    </sample>
                    <sample>
                        <id>16</id>
                        <name>BatteryCharge</name>
                        <type>uint8</type>
                        <value>
                            <unit>percent</unit>
                        </value>
                    </sample>
                    <sample>
                        <id>17</id>
                        <name>GPSAltitude</name>
                        <type>uint32</type>
                        <value>
                            <unit>meters</unit>
                            <scale>0.01</scale>
                            <limit>
                                <min>-1000</min>
                                <max>15000</max>
                            </limit>
                        </value>
                    </sample>
                    <sample>
                        <id>18</id>
                        <name>GPSHeading</name>
                        <type>uint16</type>
                        <value>
                            <unit>degrees</unit>
                            <scale>0.01</scale>
                            <limit>
                                <min>0</min>
                                <max>360</max>
                            </limit>
                            <ignoreif>65535</ignoreif>
                        </value>
                    </sample>
                    <sample>
                        <id>19</id>
                        <name>GpsHDOP</name>
                        <type>uint8</type>
                        <value>
                            <ignoreif>255</ignoreif>
                        </value>
                    </sample>
                    <sample>
                        <id>20</id>
                        <name>GpsVDOP</name>
                        <type>uint8</type>
                        <value>
                            <ignoreif>255</ignoreif>
                        </value>
                    </sample>
                    <sample>
                        <id>21</id>
                        <name>WristCadence</name>
                        <type>uint16</type>
                        <value>
                            <unit>rpm</unit>
                            <ignoreif>65535</ignoreif>
                        </value>
                    </sample>
                    <sample>
                        <id>22</id>
                        <name>SNR</name>
                        <type>buffer</type>
                        <length>16</length>
                    </sample>
                    <sample>
                        <id>23</id>
                        <name>NumberOfSatellites</name>
                        <type>uint8</type>
                        <value>
                            <ignoreif>255</ignoreif>
                        </value>
                    </sample>
                    <sample>
                        <id>24</id>
                        <name>SeaLevelPressure</name>
                        <type>int16</type>
                        <value>
                            <unit>hpa</unit>
                            <scale>0.1</scale>
                        </value>
                    </sample>
                    <sample>
                        <id>25</id>
                        <name>VerticalSpeed</name>
                        <type>int16</type>
                        <value>
                            <unit>m/s</unit>
                            <scale>0.01</scale>
                        </value>
                    </sample>
                    <sample>
                        <id>26</id>
                        <name>Cadence</name>
                        <type>uint8</type>
                        <value>
                            <unit>rpm</unit>
                            <ignoreif>255</ignoreif>
                        </value>
                    </sample>
                    <sample>
                        <id>31</id>
                        <name>BikePower</name>
                        <type>uint16</type>
                        <value>
                            <unit>watt</unit>
                        </value>
                    </sample>
                    <sample>
                        <id>32</id>
                        <name>SwimmingStrokeCount</name>
                        <type>uint32</type>
                    </sample>
                    <sample>
                        <id>40</id>
                        <name>RelativePerformanceLevel</name>
                        <type>int8</type>
                        <value>
                            <limit>
                                <min>0.80</min>
                                <max>1.20</max>
                            </limit>
                            <ignoreif>-127</ignoreif>
                            <scale>0.01</scale>
                            <offset>100</offset>
                            <precision>2</precision>
                        </value>
                    </sample>
                </periodic>
                <episodic>
                    <sample>
                        <id>1</id>
                        <name>GPS test</name>
                        <type>buffer</type>
                        <length>16</length>
                    </sample>
                    <sample>
                        <id>2</id>
                        <name>gpsdata</name>
                        <type>buffer</type>
                        <length>?</length>
                    </sample>
                    <sample>
                        <id>3</id>
                        <name>accdata</name>
                        <type>buffer</type>
                        <length>?</length>
                    </sample>
                    <sample>
                        <id>4</id>
                        <name>logpause</name>
                        <type>buffer</type>
                        <length>0</length>
                    </sample>
                    <sample>
                        <id>5</id>
                        <name>logrestart</name>
                        <type>buffer</type>
                        <length>0</length>
                    </sample>
                    <sample>
                        <id>6</id>
                        <name>ibidata</name>
                        <type>buffer</type>
                        <length>64</length>
                    </sample>
                    <sample>
                        <id>7</id>
                        <name>ttff</name>
                        <type>uint16</type>
                        <value>
                            <unit>seconds</unit>
                            <scale>0.1</scale>
                        </value>
                    </sample>
                    <sample>
                        <id>8</id>
                        <name>distancesource</name>
                        <type>uint8</type>
                        <value>
                            <ignoreif>255</ignoreif>
                        </value>
                    </sample>
                    <sample>
                        <id>9</id>
                        <name>lapinfo</name>
                        <type>buffer</type>
                        <length>23</length>
                    </sample>
                    <sample>
                        <id>10</id>
                        <name>gpsuserdata</name>
                        <type>buffer</type>
                        <length>17</length>
                    </sample>
                    <sample>
                        <id>11</id>
                        <name>gpstestdata</name>
                        <type>buffer</type>
                        <length>61</length>
                    </sample>
                    <sample>
                        <id>12</id>
                        <name>timeref</name>
                        <type>buffer</type>
                        <length>14</length>
                    </sample>
                    <sample>
                        <id>19</id>
                        <name>ruledata</name>
                        <type>buffer</type>
                        <length>9</length>
                    </sample>
                </episodic>
            </training>
        </log>
    </options>
  </device>

  <device name="Suunto Ambit3 BSL" type="Emu" model="Emu">
    <options>
      <firmware>
        <upload>true</upload>
      </firmware>
    </options>
    <port type="HID">
      <driver>BluebirdDriver</driver>
      <match>
        <vid>0x1493</vid>
        <pid>0x001b</pid>
      </match>
    </port>
    <host type="NspHost">
      <match>
        <device>BSL</device>
      </match>
    </host>
    <protocols>
      <protocol type="UsbFrame"/>
      <protocol type="NSP"/>
    </protocols>
  </device>

  <device name="Suunto Ambit3 S BSL" type="Emu" model="Finch">
    <options>
      <firmware>
        <upload>true</upload>
      </firmware>
    </options>
    <port type="HID">
      <driver>BluebirdDriver</driver>
      <match>
        <vid>0x1493</vid>
        <pid>0x001c</pid>
      </match>
    </port>
    <host type="NspHost">
      <match>
        <device>BSL</device>
      </match>
    </host>
    <protocols>
      <protocol type="UsbFrame"/>
      <protocol type="NSP"/>
    </protocols>
  </device>

  <device name="Suunto Ambit3 R BSL" type="Emu" model="Ibisbill">
    <options>
        <firmware>
            <upload>true</upload>
        </firmware>
    </options>
    <port type="HID">
        <driver>BluebirdDriver</driver>
        <match>
            <vid>0x1493</vid>
            <pid>0x001e</pid>
        </match>
    </port>
    <host type="NspHost">
        <match>
            <device>BSL</device>
        </match>
    </host>
    <protocols>
        <protocol type="UsbFrame"/>
        <protocol type="NSP"/>
    </protocols>
  </device>

  <device name="Suunto EON Steel" type="Guru" model="Guru">
    <port type="HID">
      <driver>GenericHID</driver>
      <match>
        <vid>0x1493</vid>
        <pid>0x0030</pid>
      </match>
    </port>
    <host type="NspHost">
      <match>
        <device>GURU</device>
      </match>
    </host>
    <protocols>
      <protocol type="NSP"/>
    </protocols>
  </device>

  <device name="Suunto EON Steel BSL" type="Guru" model="Guru">
    <port type="HID">
      <driver>GenericHID</driver>
      <match>
        <vid>0x1493</vid>
        <pid>0x0031</pid>
      </match>
    </port>
    <host type="NspHost">
      <match>
        <device>GURUBSL</device>
      </match>
    </host>
    <protocols>
      <protocol type="NSP"/>
    </protocols>
  </device>

  <device name="Suunto D5" type="Guru" model="Guru">
      <port type="HID">
          <driver>GenericHID</driver>
          <match>
              <vid>0x1493</vid>
              <pid>0x0035</pid>
          </match>
      </port>
      <host type="NspHost">
          <match>
              <device>GURU3</device>
          </match>
      </host>
      <protocols>
          <protocol type="NSP"/>
      </protocols>
  </device>

  <device name="Suunto EON Core" type="Guru" model="Guru">
    <port type="HID">
      <driver>GenericHID</driver>
      <match>
        <vid>0x1493</vid>
        <pid>0x0033</pid>
      </match>
    </port>
    <host type="NspHost">
      <match>
        <device>GURU2</device>
      </match>
    </host>
    <protocols>
      <protocol type="NSP"/>
    </protocols>
  </device>

  <device name="Suunto EON Core BSL" type="Guru"  model="Guru">
    <port type="HID">
      <driver>GenericHID</driver>
      <match>
        <vid>0x1493</vid>
        <pid>0x0033</pid>
      </match>
    </port>
    <host type="NspHost">
      <match>
        <device>GURU2BSL</device>
      </match>
    </host>
    <protocols>
      <protocol type="NSP"/>
    </protocols>
  </device>

  <device name="Suunto Traverse" type="Emu" model="Jabiru">
    <port type="HID">
        <driver>BluebirdDriver</driver>
        <match>
            <vid>0x1493</vid>
            <pid>0x002b</pid>
        </match>
    </port>
    <host type="NspHost">
        <match>
            <device>Jabiru</device>
        </match>
    </host>
    <protocols>
        <protocol type="UsbFrame"/>
        <protocol type="NSP"/>
    </protocols>
    <options>
        <trainingprogram/>
        <firmware>
            <bootupdate>true</bootupdate>
        </firmware>
        <custommodeconfig>
            <intervaltimer>true</intervaltimer>
            <ruleconfig>true</ruleconfig>
            <custommodeid>true</custommodeid>
            <usehrlimits>true</usehrlimits>
            <autoscrolling>true</autoscrolling>
            <rulestoresize>200000</rulestoresize>
            <rulestorelocation>600000</rulestorelocation>
            <displaymode>true</displaymode>
            <navigationselection>true</navigationselection>
            <traverseDisplays>
                <cardinalCompass>
                    <match>
                        <swVersion op="gte">1.8.0</swVersion>
                    </match>
                </cardinalCompass>
                <altiGraph>
                    <match>
                        <swVersion op="gte">1.5.0</swVersion>
                    </match>
                </altiGraph>
                <moonCalendar>
                    <match>
                        <swVersion op="gte">1.9.0</swVersion>
                    </match>
                </moonCalendar>
                <huntingFishing>
                    <match>
                        <swVersion op="gte">1.9.0</swVersion>
                    </match>
                </huntingFishing>
            </traverseDisplays>
        </custommodeconfig>
        <log type="PMEM" version="2.0">
            <distancesources>
                <source>
                    <id>0</id>
                    <name>Bikepod</name>
                </source>
                <source>
                    <id>1</id>
                    <name>Footpod</name>
                </source>
                <source>
                    <id>2</id>
                    <name>Gps</name>
                </source>
                <source>
                    <id>3</id>
                    <name>Wrist</name>
                </source>
                <source>
                    <id>4</id>
                    <name>Indoorswimming</name>
                </source>
                <source>
                    <id>5</id>
                    <name>Outdoorswimming</name>
                </source>
            </distancesources>
            <event>
            </event>
            <training>
                <periodic>
                    <sample>
                        <id>1</id>
                        <name>Latitude</name>
                        <type>int32</type>
                        <value>
                            <unit>degrees</unit>
                            <scale>0.0000001</scale>
                            <limit>
                                <min>-90</min>
                                <max>90</max>
                            </limit>
                        </value>
                    </sample>
                    <sample>
                        <id>2</id>
                        <name>Longitude</name>
                        <type>int32</type>
                        <value>
                            <unit>degrees</unit>
                            <scale>0.0000001</scale>
                            <limit>
                                <min>-180</min>
                                <max>180</max>
                            </limit>
                        </value>
                    </sample>
                    <sample>
                        <id>3</id>
                        <name>Distance</name>
                        <type>uint32</type>
                        <value>
                            <unit>meters</unit>
                        </value>
                    </sample>
                    <sample>
                        <id>4</id>
                        <name>Speed</name>
                        <type>uint16</type>
                        <value>
                            <scale>0.01</scale>
                            <unit>m/s</unit>
                            <ignoreif>65535</ignoreif>
                        </value>
                    </sample>
                    <sample>
                        <id>5</id>
                        <name>HR</name>
                        <type>uint8</type>
                        <value>
                            <unit>bpm</unit>
                            <ignoreif>255</ignoreif>
                        </value>
                    </sample>
                    <sample>
                        <id>6</id>
                        <name>Time</name>
                        <type>uint32</type>
                        <value>
                            <unit>msec</unit>
                        </value>
                    </sample>
                    <sample>
                        <id>7</id>
                        <name>GPSSpeed</name>
                        <type>uint16</type>
                        <value>
                            <unit>m/s</unit>
                            <scale>0.01</scale>
                        </value>
                    </sample>
                    <sample>
                        <id>8</id>
                        <name>WristAccSpeed</name>
                        <type>uint16</type>
                        <value>
                            <unit>m/s</unit>
                            <scale>0.01</scale>
                        </value>
                    </sample>
                    <sample>
                        <id>9</id>
                        <name>BikePodSpeed</name>
                        <type>uint16</type>
                        <value>
                            <unit>m/s</unit>
                            <scale>0.01</scale>
                        </value>
                    </sample>
                    <sample>
                        <id>10</id>
                        <name>EHPE</name>
                        <type>uint32</type>
                        <value>
                            <unit>meters</unit>
                            <scale>0.01</scale>
                        </value>
                    </sample>
                    <sample>
                        <id>11</id>
                        <name>EVPE</name>
                        <type>uint32</type>
                        <value>
                            <unit>meters</unit>
                            <scale>0.01</scale>
                        </value>
                    </sample>
                    <sample>
                        <id>12</id>
                        <name>Altitude</name>
                        <type>int16</type>
                        <value>
                            <unit>meters</unit>
                            <limit>
                                <min>-1000</min>
                                <max>15000</max>
                            </limit>
                        </value>
                    </sample>
                    <sample>
                        <id>13</id>
                        <name>AbsPressure</name>
                        <type>uint16</type>
                        <value>
                            <unit>hpa</unit>
                            <scale>0.1</scale>
                        </value>
                    </sample>
                    <sample>
                        <id>14</id>
                        <name>EnergyConsumption</name>
                        <type>uint16</type>
                        <value>
                            <unit>hcal/min</unit>
                        </value>
                    </sample>
                    <sample>
                        <id>15</id>
                        <name>Temperature</name>
                        <type>int16</type>
                        <value>
                            <unit>celsius</unit>
                            <scale>0.1</scale>
                            <limit>
                                <min>-100</min>
                                <max>100</max>
                            </limit>
                        </value>
                    </sample>
                    <sample>
                        <id>16</id>
                        <name>BatteryCharge</name>
                        <type>uint8</type>
                        <value>
                            <unit>percent</unit>
                        </value>
                    </sample>
                    <sample>
                        <id>17</id>
                        <name>GPSAltitude</name>
                        <type>uint32</type>
                        <value>
                            <unit>meters</unit>
                            <scale>0.01</scale>
                            <limit>
                                <min>-1000</min>
                                <max>15000</max>
                            </limit>
                        </value>
                    </sample>
                    <sample>
                        <id>18</id>
                        <name>GPSHeading</name>
                        <type>uint16</type>
                        <value>
                            <unit>degrees</unit>
                            <scale>0.01</scale>
                            <limit>
                                <min>0</min>
                                <max>360</max>
                            </limit>
                            <ignoreif>65535</ignoreif>
                        </value>
                    </sample>
                    <sample>
                        <id>19</id>
                        <name>GpsHDOP</name>
                        <type>uint8</type>
                        <value>
                            <ignoreif>255</ignoreif>
                        </value>
                    </sample>
                    <sample>
                        <id>20</id>
                        <name>GpsVDOP</name>
                        <type>uint8</type>
                        <value>
                            <ignoreif>255</ignoreif>
                        </value>
                    </sample>
                    <sample>
                        <id>21</id>
                        <name>WristCadence</name>
                        <type>uint16</type>
                        <value>
                            <unit>rpm</unit>
                            <ignoreif>65535</ignoreif>
                        </value>
                    </sample>
                    <sample>
                        <id>22</id>
                        <name>SNR</name>
                        <type>buffer</type>
                        <length>16</length>
                    </sample>
                    <sample>
                        <id>23</id>
                        <name>NumberOfSatellites</name>
                        <type>uint8</type>
                        <value>
                            <ignoreif>255</ignoreif>
                        </value>
                    </sample>
                    <sample>
                        <id>24</id>
                        <name>SeaLevelPressure</name>
                        <type>int16</type>
                        <value>
                            <unit>hpa</unit>
                            <scale>0.1</scale>
                        </value>
                    </sample>
                    <sample>
                        <id>25</id>
                        <name>VerticalSpeed</name>
                        <type>int16</type>
                        <value>
                            <unit>m/s</unit>
                            <scale>0.01</scale>
                        </value>
                    </sample>
                    <sample>
                        <id>26</id>
                        <name>Cadence</name>
                        <type>uint8</type>
                        <value>
                            <unit>rpm</unit>
                            <ignoreif>255</ignoreif>
                        </value>
                    </sample>
                    <sample>
                        <id>31</id>
                        <name>BikePower</name>
                        <type>uint16</type>
                        <value>
                            <unit>watt</unit>
                        </value>
                    </sample>
                    <sample>
                        <id>32</id>
                        <name>SwimmingStrokeCount</name>
                        <type>uint32</type>
                    </sample>
                </periodic>
                <episodic>
                    <sample>
                        <id>1</id>
                        <name>GPS test</name>
                        <type>buffer</type>
                        <length>16</length>
                    </sample>
                    <sample>
                        <id>2</id>
                        <name>gpsdata</name>
                        <type>buffer</type>
                        <length>?</length>
                    </sample>
                    <sample>
                        <id>3</id>
                        <name>accdata</name>
                        <type>buffer</type>
                        <length>?</length>
                    </sample>
                    <sample>
                        <id>4</id>
                        <name>logpause</name>
                        <type>buffer</type>
                        <length>0</length>
                    </sample>
                    <sample>
                        <id>5</id>
                        <name>logrestart</name>
                        <type>buffer</type>
                        <length>0</length>
                    </sample>
                    <sample>
                        <id>6</id>
                        <name>ibidata</name>
                        <type>buffer</type>
                        <length>64</length>
                    </sample>
                    <sample>
                        <id>7</id>
                        <name>ttff</name>
                        <type>uint16</type>
                        <value>
                            <unit>seconds</unit>
                            <scale>0.1</scale>
                        </value>
                    </sample>
                    <sample>
                        <id>8</id>
                        <name>distancesource</name>
                        <type>uint8</type>
                        <value>
                            <ignoreif>255</ignoreif>
                        </value>
                    </sample>
                    <sample>
                        <id>9</id>
                        <name>lapinfo</name>
                        <type>buffer</type>
                        <length>23</length>
                    </sample>
                    <sample>
                        <id>10</id>
                        <name>gpsuserdata</name>
                        <type>buffer</type>
                        <length>17</length>
                    </sample>
                    <sample>
                        <id>11</id>
                        <name>gpstestdata</name>
                        <type>buffer</type>
                        <length>61</length>
                    </sample>
                    <sample>
                        <id>12</id>
                        <name>timeref</name>
                        <type>buffer</type>
                        <length>14</length>
                    </sample>
                    <sample>
                        <id>19</id>
                        <name>ruledata</name>
                        <type>buffer</type>
                        <length>9</length>
                    </sample>
                </episodic>
            </training>
        </log>
        <glonass>
            <download/>
        </glonass>
    </options>
  </device>

  <device name="Suunto Traverse BSL" type="Emu" model="Jabiru">
    <options>
        <firmware>
            <upload>true</upload>
        </firmware>
    </options>
    <port type="HID">
        <driver>BluebirdDriver</driver>
        <match>
            <vid>0x1493</vid>
            <pid>0x002b</pid>
        </match>
    </port>
    <host type="NspHost">
        <match>
            <device>BSL</device>
        </match>
    </host>
    <protocols>
        <protocol type="UsbFrame"/>
        <protocol type="NSP"/>
    </protocols>
  </device>

  <device name="Suunto Traverse Alpha" type="Emu" model="Loon">
    <port type="HID">
        <driver>BluebirdDriver</driver>
        <match>
            <vid>0x1493</vid>
            <pid>0x002d</pid>
        </match>
    </port>
    <host type="NspHost">
        <match>
            <device>Loon</device>
        </match>
    </host>
    <protocols>
        <protocol type="UsbFrame"/>
        <protocol type="NSP"/>
    </protocols>
    <options>
        <trainingprogram/>
        <firmware>
            <bootupdate>true</bootupdate>
        </firmware>
        <custommodeconfig>
            <intervaltimer>true</intervaltimer>
            <ruleconfig>true</ruleconfig>
            <custommodeid>true</custommodeid>
            <usehrlimits>true</usehrlimits>
            <autoscrolling>true</autoscrolling>
            <rulestoresize>200000</rulestoresize>
            <rulestorelocation>600000</rulestorelocation>
            <displaymode>true</displaymode>
            <navigationselection>true</navigationselection>
            <traverseDisplays>
                <cardinalCompass>
                    <match>
                        <swVersion op="gte">1.8.0</swVersion>
                    </match>
                </cardinalCompass>
                <altiGraph>
                    <match>
                        <swVersion op="gte">1.5.0</swVersion>
                    </match>
                </altiGraph>
                <moonCalendar>
                    <match>
                        <swVersion op="gte">1.9.0</swVersion>
                    </match>
                </moonCalendar>
                <huntingFishing>
                    <match>
                        <swVersion op="gte">1.9.0</swVersion>
                    </match>
                </huntingFishing>
            </traverseDisplays>
        </custommodeconfig>
        <log type="PMEM" version="2.0">
            <distancesources>
                <source>
                    <id>0</id>
                    <name>Bikepod</name>
                </source>
                <source>
                    <id>1</id>
                    <name>Footpod</name>
                </source>
                <source>
                    <id>2</id>
                    <name>Gps</name>
                </source>
                <source>
                    <id>3</id>
                    <name>Wrist</name>
                </source>
                <source>
                    <id>4</id>
                    <name>Indoorswimming</name>
                </source>
                <source>
                    <id>5</id>
                    <name>Outdoorswimming</name>
                </source>
            </distancesources>
            <event>
            </event>
            <training>
                <periodic>
                    <sample>
                        <id>1</id>
                        <name>Latitude</name>
                        <type>int32</type>
                        <value>
                            <unit>degrees</unit>
                            <scale>0.0000001</scale>
                            <limit>
                                <min>-90</min>
                                <max>90</max>
                            </limit>
                        </value>
                    </sample>
                    <sample>
                        <id>2</id>
                        <name>Longitude</name>
                        <type>int32</type>
                        <value>
                            <unit>degrees</unit>
                            <scale>0.0000001</scale>
                            <limit>
                                <min>-180</min>
                                <max>180</max>
                            </limit>
                        </value>
                    </sample>
                    <sample>
                        <id>3</id>
                        <name>Distance</name>
                        <type>uint32</type>
                        <value>
                            <unit>meters</unit>
                        </value>
                    </sample>
                    <sample>
                        <id>4</id>
                        <name>Speed</name>
                        <type>uint16</type>
                        <value>
                            <scale>0.01</scale>
                            <unit>m/s</unit>
                            <ignoreif>65535</ignoreif>
                        </value>
                    </sample>
                    <sample>
                        <id>5</id>
                        <name>HR</name>
                        <type>uint8</type>
                        <value>
                            <unit>bpm</unit>
                            <ignoreif>255</ignoreif>
                        </value>
                    </sample>
                    <sample>
                        <id>6</id>
                        <name>Time</name>
                        <type>uint32</type>
                        <value>
                            <unit>msec</unit>
                        </value>
                    </sample>
                    <sample>
                        <id>7</id>
                        <name>GPSSpeed</name>
                        <type>uint16</type>
                        <value>
                            <unit>m/s</unit>
                            <scale>0.01</scale>
                        </value>
                    </sample>
                    <sample>
                        <id>8</id>
                        <name>WristAccSpeed</name>
                        <type>uint16</type>
                        <value>
                            <unit>m/s</unit>
                            <scale>0.01</scale>
                        </value>
                    </sample>
                    <sample>
                        <id>9</id>
                        <name>BikePodSpeed</name>
                        <type>uint16</type>
                        <value>
                            <unit>m/s</unit>
                            <scale>0.01</scale>
                        </value>
                    </sample>
                    <sample>
                        <id>10</id>
                        <name>EHPE</name>
                        <type>uint32</type>
                        <value>
                            <unit>meters</unit>
                            <scale>0.01</scale>
                        </value>
                    </sample>
                    <sample>
                        <id>11</id>
                        <name>EVPE</name>
                        <type>uint32</type>
                        <value>
                            <unit>meters</unit>
                            <scale>0.01</scale>
                        </value>
                    </sample>
                    <sample>
                        <id>12</id>
                        <name>Altitude</name>
                        <type>int16</type>
                        <value>
                            <unit>meters</unit>
                            <limit>
                                <min>-1000</min>
                                <max>15000</max>
                            </limit>
                        </value>
                    </sample>
                    <sample>
                        <id>13</id>
                        <name>AbsPressure</name>
                        <type>uint16</type>
                        <value>
                            <unit>hpa</unit>
                            <scale>0.1</scale>
                        </value>
                    </sample>
                    <sample>
                        <id>14</id>
                        <name>EnergyConsumption</name>
                        <type>uint16</type>
                        <value>
                            <unit>hcal/min</unit>
                        </value>
                    </sample>
                    <sample>
                        <id>15</id>
                        <name>Temperature</name>
                        <type>int16</type>
                        <value>
                            <unit>celsius</unit>
                            <scale>0.1</scale>
                            <limit>
                                <min>-100</min>
                                <max>100</max>
                            </limit>
                        </value>
                    </sample>
                    <sample>
                        <id>16</id>
                        <name>BatteryCharge</name>
                        <type>uint8</type>
                        <value>
                            <unit>percent</unit>
                        </value>
                    </sample>
                    <sample>
                        <id>17</id>
                        <name>GPSAltitude</name>
                        <type>uint32</type>
                        <value>
                            <unit>meters</unit>
                            <scale>0.01</scale>
                            <limit>
                                <min>-1000</min>
                                <max>15000</max>
                            </limit>
                        </value>
                    </sample>
                    <sample>
                        <id>18</id>
                        <name>GPSHeading</name>
                        <type>uint16</type>
                        <value>
                            <unit>degrees</unit>
                            <scale>0.01</scale>
                            <limit>
                                <min>0</min>
                                <max>360</max>
                            </limit>
                            <ignoreif>65535</ignoreif>
                        </value>
                    </sample>
                    <sample>
                        <id>19</id>
                        <name>GpsHDOP</name>
                        <type>uint8</type>
                        <value>
                            <ignoreif>255</ignoreif>
                        </value>
                    </sample>
                    <sample>
                        <id>20</id>
                        <name>GpsVDOP</name>
                        <type>uint8</type>
                        <value>
                            <ignoreif>255</ignoreif>
                        </value>
                    </sample>
                    <sample>
                        <id>21</id>
                        <name>WristCadence</name>
                        <type>uint16</type>
                        <value>
                            <unit>rpm</unit>
                            <ignoreif>65535</ignoreif>
                        </value>
                    </sample>
                    <sample>
                        <id>22</id>
                        <name>SNR</name>
                        <type>buffer</type>
                        <length>16</length>
                    </sample>
                    <sample>
                        <id>23</id>
                        <name>NumberOfSatellites</name>
                        <type>uint8</type>
                        <value>
                            <ignoreif>255</ignoreif>
                        </value>
                    </sample>
                    <sample>
                        <id>24</id>
                        <name>SeaLevelPressure</name>
                        <type>int16</type>
                        <value>
                            <unit>hpa</unit>
                            <scale>0.1</scale>
                        </value>
                    </sample>
                    <sample>
                        <id>25</id>
                        <name>VerticalSpeed</name>
                        <type>int16</type>
                        <value>
                            <unit>m/s</unit>
                            <scale>0.01</scale>
                        </value>
                    </sample>
                    <sample>
                        <id>26</id>
                        <name>Cadence</name>
                        <type>uint8</type>
                        <value>
                            <unit>rpm</unit>
                            <ignoreif>255</ignoreif>
                        </value>
                    </sample>
                    <sample>
                        <id>31</id>
                        <name>BikePower</name>
                        <type>uint16</type>
                        <value>
                            <unit>watt</unit>
                        </value>
                    </sample>
                    <sample>
                        <id>32</id>
                        <name>SwimmingStrokeCount</name>
                        <type>uint32</type>
                    </sample>
                </periodic>
                <episodic>
                    <sample>
                        <id>1</id>
                        <name>GPS test</name>
                        <type>buffer</type>
                        <length>16</length>
                    </sample>
                    <sample>
                        <id>2</id>
                        <name>gpsdata</name>
                        <type>buffer</type>
                        <length>?</length>
                    </sample>
                    <sample>
                        <id>3</id>
                        <name>accdata</name>
                        <type>buffer</type>
                        <length>?</length>
                    </sample>
                    <sample>
                        <id>4</id>
                        <name>logpause</name>
                        <type>buffer</type>
                        <length>0</length>
                    </sample>
                    <sample>
                        <id>5</id>
                        <name>logrestart</name>
                        <type>buffer</type>
                        <length>0</length>
                    </sample>
                    <sample>
                        <id>6</id>
                        <name>ibidata</name>
                        <type>buffer</type>
                        <length>64</length>
                    </sample>
                    <sample>
                        <id>7</id>
                        <name>ttff</name>
                        <type>uint16</type>
                        <value>
                            <unit>seconds</unit>
                            <scale>0.1</scale>
                        </value>
                    </sample>
                    <sample>
                        <id>8</id>
                        <name>distancesource</name>
                        <type>uint8</type>
                        <value>
                            <ignoreif>255</ignoreif>
                        </value>
                    </sample>
                    <sample>
                        <id>9</id>
                        <name>lapinfo</name>
                        <type>buffer</type>
                        <length>23</length>
                    </sample>
                    <sample>
                        <id>10</id>
                        <name>gpsuserdata</name>
                        <type>buffer</type>
                        <length>17</length>
                    </sample>
                    <sample>
                        <id>11</id>
                        <name>gpstestdata</name>
                        <type>buffer</type>
                        <length>61</length>
                    </sample>
                    <sample>
                        <id>12</id>
                        <name>timeref</name>
                        <type>buffer</type>
                        <length>14</length>
                    </sample>
                    <sample>
                        <id>19</id>
                        <name>ruledata</name>
                        <type>buffer</type>
                        <length>9</length>
                    </sample>
                    <sample>
                        <id>32</id>
                        <name>shot</name>
                        <type>buffer</type>
                        <length>8</length>
                        <location>
                            <latitude>
                                <type>int32</type>
                                <value>
                                    <unit>degrees</unit>
                                    <scale>0.0000001</scale>
                                    <limit>
                                        <min>-90</min>
                                        <max>90</max>
                                    </limit>
                                    <ignoreif>2147483647</ignoreif>
                                </value>
                            </latitude>
                            <longitude>
                                <type>int32</type>
                                <value>
                                    <unit>degrees</unit>
                                    <scale>0.0000001</scale>
                                    <limit>
                                        <min>-180</min>
                                        <max>180</max>
                                    </limit>
                                    <ignoreif>2147483647</ignoreif>
                                </value>
                            </longitude>
                        </location>
                    </sample>
                    <sample>
                        <id>33</id>
                        <name>catch</name>
                        <type>buffer</type>
                        <length>16</length>
                        <location>
                            <latitude>
                                <type>int32</type>
                                <value>
                                    <unit>degrees</unit>
                                    <scale>0.0000001</scale>
                                    <limit>
                                        <min>-90</min>
                                        <max>90</max>
                                    </limit>
                                    <ignoreif>2147483647</ignoreif>
                                </value>
                            </latitude>
                            <longitude>
                                <type>int32</type>
                                <value>
                                    <unit>degrees</unit>
                                    <scale>0.0000001</scale>
                                    <limit>
                                        <min>-180</min>
                                        <max>180</max>
                                    </limit>
                                    <ignoreif>2147483647</ignoreif>
                                </value>
                            </longitude>
                        </location>
                    </sample>
                </episodic>
            </training>
        </log>
        <glonass>
            <download/>
        </glonass>
    </options>
  </device>

  <device name="Suunto Traverse Alpha BSL" type="Emu" model="Loon">
    <options>
        <firmware>
            <upload>true</upload>
        </firmware>
    </options>
    <port type="HID">
        <driver>BluebirdDriver</driver>
        <match>
            <vid>0x1493</vid>
            <pid>0x002d</pid>
        </match>
    </port>
    <host type="NspHost">
        <match>
            <device>BSL</device>
        </match>
    </host>
    <protocols>
        <protocol type="UsbFrame"/>
        <protocol type="NSP"/>
    </protocols>
  </device>

  <device name="Suunto Ambit3 Vertical" type="Emu" model="Kaka">
    <port type="HID">
      <driver>BluebirdDriver</driver>
      <match>
        <vid>0x1493</vid>
        <pid>0x002c</pid>
      </match>
    </port>
    <host type="NspHost">
      <match>
        <device>Kaka</device>
      </match>
    </host>
    <protocols>
      <protocol type="UsbFrame"/>
      <protocol type="NSP"/>
    </protocols>
    <options>
      <trainingprogram/>
      <firmware>
        <bootupdate>true</bootupdate>
      </firmware>
      <custommodeconfig>
        <intervaltimer>true</intervaltimer>
        <ruleconfig>true</ruleconfig>
        <custommodeid>true</custommodeid>
        <usehrlimits>true</usehrlimits>
        <autoscrolling>true</autoscrolling>
        <rulestoresize>200000</rulestoresize>
        <rulestorelocation>600000</rulestorelocation>
        <displaymode>true</displaymode>
        <navigationselection>true</navigationselection>
      </custommodeconfig>
      <log type="PMEM" version="2.0">
        <distancesources>
          <source>
            <id>0</id>
            <name>Bikepod</name>
          </source>
          <source>
            <id>1</id>
            <name>Footpod</name>
          </source>
          <source>
            <id>2</id>
            <name>Gps</name>
          </source>
          <source>
            <id>3</id>
            <name>Wrist</name>
          </source>
          <source>
            <id>4</id>
            <name>Indoorswimming</name>
          </source>
          <source>
            <id>5</id>
            <name>Outdoorswimming</name>
          </source>
        </distancesources>
        <event>
        </event>
        <training>
          <periodic>
            <sample>
              <id>1</id>
              <name>Latitude</name>
              <type>int32</type>
              <value>
                <unit>degrees</unit>
                <scale>0.0000001</scale>
                <limit>
                  <min>-90</min>
                  <max>90</max>
                </limit>
              </value>
            </sample>
            <sample>
              <id>2</id>
              <name>Longitude</name>
              <type>int32</type>
              <value>
                <unit>degrees</unit>
                <scale>0.0000001</scale>
                <limit>
                  <min>-180</min>
                  <max>180</max>
                </limit>
              </value>
            </sample>
            <sample>
              <id>3</id>
              <name>Distance</name>
              <type>uint32</type>
              <value>
                <unit>meters</unit>
              </value>
            </sample>
            <sample>
              <id>4</id>
              <name>Speed</name>
              <type>uint16</type>
              <value>
                <scale>0.01</scale>
                <unit>m/s</unit>
                <ignoreif>65535</ignoreif>
              </value>
            </sample>
            <sample>
              <id>5</id>
              <name>HR</name>
              <type>uint8</type>
              <value>
                <unit>bpm</unit>
                <ignoreif>255</ignoreif>
              </value>
            </sample>
            <sample>
              <id>6</id>
              <name>Time</name>
              <type>uint32</type>
              <value>
                <unit>msec</unit>
              </value>
            </sample>
            <sample>
              <id>7</id>
              <name>GPSSpeed</name>
              <type>uint16</type>
              <value>
                <unit>m/s</unit>
                <scale>0.01</scale>
              </value>
            </sample>
            <sample>
              <id>8</id>
              <name>WristAccSpeed</name>
              <type>uint16</type>
              <value>
                <unit>m/s</unit>
                <scale>0.01</scale>
              </value>
            </sample>
            <sample>
              <id>9</id>
              <name>BikePodSpeed</name>
              <type>uint16</type>
              <value>
                <unit>m/s</unit>
                <scale>0.01</scale>
              </value>
            </sample>
            <sample>
              <id>10</id>
              <name>EHPE</name>
              <type>uint32</type>
              <value>
                <unit>meters</unit>
                <scale>0.01</scale>
              </value>
            </sample>
            <sample>
              <id>11</id>
              <name>EVPE</name>
              <type>uint32</type>
              <value>
                <unit>meters</unit>
                <scale>0.01</scale>
              </value>
            </sample>
            <sample>
              <id>12</id>
              <name>Altitude</name>
              <type>int16</type>
              <value>
                <unit>meters</unit>
                <limit>
                  <min>-1000</min>
                  <max>15000</max>
                </limit>
              </value>
            </sample>
            <sample>
              <id>13</id>
              <name>AbsPressure</name>
              <type>uint16</type>
              <value>
                <unit>hpa</unit>
                <scale>0.1</scale>
              </value>
            </sample>
            <sample>
              <id>14</id>
              <name>EnergyConsumption</name>
              <type>uint16</type>
              <value>
                <unit>hcal/min</unit>
              </value>
            </sample>
            <sample>
              <id>15</id>
              <name>Temperature</name>
              <type>int16</type>
              <value>
                <unit>celsius</unit>
                <scale>0.1</scale>
                <limit>
                  <min>-100</min>
                  <max>100</max>
                </limit>
              </value>
            </sample>
            <sample>
              <id>16</id>
              <name>BatteryCharge</name>
              <type>uint8</type>
              <value>
                <unit>percent</unit>
              </value>
            </sample>
            <sample>
              <id>17</id>
              <name>GPSAltitude</name>
              <type>uint32</type>
              <value>
                <unit>meters</unit>
                <scale>0.01</scale>
                <limit>
                  <min>-1000</min>
                  <max>15000</max>
                </limit>
              </value>
            </sample>
            <sample>
              <id>18</id>
              <name>GPSHeading</name>
              <type>uint16</type>
              <value>
                <unit>degrees</unit>
                <scale>0.01</scale>
                <limit>
                  <min>0</min>
                  <max>360</max>
                </limit>
                <ignoreif>65535</ignoreif>
              </value>
            </sample>
            <sample>
              <id>19</id>
              <name>GpsHDOP</name>
              <type>uint8</type>
              <value>
                <ignoreif>255</ignoreif>
              </value>
            </sample>
            <sample>
              <id>20</id>
              <name>GpsVDOP</name>
              <type>uint8</type>
              <value>
                <ignoreif>255</ignoreif>
              </value>
            </sample>
            <sample>
              <id>21</id>
              <name>WristCadence</name>
              <type>uint16</type>
              <value>
                <unit>rpm</unit>
                <ignoreif>65535</ignoreif>
              </value>
            </sample>
            <sample>
              <id>22</id>
              <name>SNR</name>
              <type>buffer</type>
              <length>16</length>
            </sample>
            <sample>
              <id>23</id>
              <name>NumberOfSatellites</name>
              <type>uint8</type>
              <value>
                <ignoreif>255</ignoreif>
              </value>
            </sample>
            <sample>
              <id>24</id>
              <name>SeaLevelPressure</name>
              <type>int16</type>
              <value>
                <unit>hpa</unit>
                <scale>0.1</scale>
              </value>
            </sample>
            <sample>
              <id>25</id>
              <name>VerticalSpeed</name>
              <type>int16</type>
              <value>
                <unit>m/s</unit>
                <scale>0.01</scale>
              </value>
            </sample>
            <sample>
              <id>26</id>
              <name>Cadence</name>
              <type>uint8</type>
              <value>
                <unit>rpm</unit>
                <ignoreif>255</ignoreif>
              </value>
            </sample>
            <sample>
              <id>31</id>
              <name>BikePower</name>
              <type>uint16</type>
              <value>
                <unit>watt</unit>
              </value>
            </sample>
            <sample>
              <id>32</id>
              <name>SwimmingStrokeCount</name>
              <type>uint32</type>
            </sample>
			      <sample>
              <id>40</id>
              <name>RelativePerformanceLevel</name>
              <type>int8</type>
              <value>
                <limit>
                  <min>0.80</min>
                  <max>1.20</max>
                </limit>
                <ignoreif>-127</ignoreif>
                <scale>0.01</scale>
                <offset>100</offset>
                <precision>2</precision>
              </value>
			      </sample>
          </periodic>
          <episodic>
            <sample>
              <id>1</id>
              <name>GPS test</name>
              <type>buffer</type>
              <length>16</length>
            </sample>
            <sample>
              <id>2</id>
              <name>gpsdata</name>
              <type>buffer</type>
              <length>?</length>
            </sample>
            <sample>
              <id>3</id>
              <name>accdata</name>
              <type>buffer</type>
              <length>?</length>
            </sample>
            <sample>
              <id>4</id>
              <name>logpause</name>
              <type>buffer</type>
              <length>0</length>
            </sample>
            <sample>
              <id>5</id>
              <name>logrestart</name>
              <type>buffer</type>
              <length>0</length>
            </sample>
            <sample>
              <id>6</id>
              <name>ibidata</name>
              <type>buffer</type>
              <length>64</length>
            </sample>
            <sample>
              <id>7</id>
              <name>ttff</name>
              <type>uint16</type>
              <value>
                <unit>seconds</unit>
                <scale>0.1</scale>
              </value>
            </sample>
            <sample>
              <id>8</id>
              <name>distancesource</name>
              <type>uint8</type>
              <value>
                <ignoreif>255</ignoreif>
              </value>
            </sample>
            <sample>
              <id>9</id>
              <name>lapinfo</name>
              <type>buffer</type>
              <length>23</length>
            </sample>
            <sample>
              <id>10</id>
              <name>gpsuserdata</name>
              <type>buffer</type>
              <length>17</length>
            </sample>
            <sample>
              <id>11</id>
              <name>gpstestdata</name>
              <type>buffer</type>
              <length>61</length>
            </sample>
            <sample>
              <id>12</id>
              <name>timeref</name>
              <type>buffer</type>
              <length>14</length>
            </sample>
            <sample>
              <id>19</id>
              <name>ruledata</name>
              <type>buffer</type>
              <length>9</length>
            </sample>
          </episodic>
        </training>
      </log>
      <glonass>
        <download/>
      </glonass>
    </options>
  </device>

  <device name="Suunto Ambit3 Vertical BSL" type="Emu" model="Kaka">
    <options>
      <firmware>
        <upload>true</upload>
      </firmware>
    </options>
    <port type="HID">
      <driver>BluebirdDriver</driver>
      <match>
        <vid>0x1493</vid>
        <pid>0x002c</pid>
      </match>
    </port>
    <host type="NspHost">
      <match>
        <device>BSL</device>
      </match>
    </host>
    <protocols>
      <protocol type="UsbFrame"/>
      <protocol type="NSP"/>
    </protocols>
  </device>

</devices>
