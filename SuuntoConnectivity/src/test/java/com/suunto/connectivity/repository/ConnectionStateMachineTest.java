package com.suunto.connectivity.repository;

import android.bluetooth.BluetoothAdapter;
import android.content.Context;
import android.content.SharedPreferences;
import android.os.Handler;
import androidx.annotation.Nullable;
import com.suunto.connectivity.firmware.FirmwareInformationInterface;
import com.suunto.connectivity.hooks.OnDeviceConnectedHook;
import com.suunto.connectivity.ngBleManager.NgBleManager;
import static com.suunto.connectivity.repository.PairingState.Paired;
import com.suunto.connectivity.repository.stateMachines.base.CurrentState;
import com.suunto.connectivity.repository.stateMachines.base.State;
import com.suunto.connectivity.repository.stateMachines.base.StateMachineImplementation;
import com.suunto.connectivity.repository.stateMachines.base.Trigger;
import static com.suunto.connectivity.repository.stateMachines.connectionStateMachine.ConnectionInstabilityState.RECONNECT_WAIT_MINUTES;
import com.suunto.connectivity.repository.stateMachines.connectionStateMachine.ConnectionStateMachine;
import com.suunto.connectivity.repository.stateMachines.connectionStateMachine.InitialConnectingState;
import com.suunto.connectivity.repository.stateMachines.connectionStateMachine.ReconnectState;
import static com.suunto.connectivity.repository.stateMachines.connectionStateMachine.ReconnectState.MAX_COUNTED_CONNECT_ATTEMPTS;
import static com.suunto.connectivity.repository.stateMachines.connectionStateMachine.ReconnectState.MAX_TOTAL_CONNECT_ATTEMPTS;
import com.suunto.connectivity.repository.stateMachines.connectionStateMachine.States;
import com.suunto.connectivity.repository.stateMachines.connectionStateMachine.Triggers;
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoBtDevice;
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoDeviceType;
import com.suunto.connectivity.suuntoconnectivity.utils.BtStateMonitor;
import com.suunto.connectivity.suuntoconnectivity.utils.ConnectMetadata;
import com.suunto.connectivity.suuntoconnectivity.utils.ConnectReason;
import com.suunto.connectivity.sync.SynchronizerStorage;
import com.suunto.connectivity.sync.WatchSynchronizer;
import com.suunto.connectivity.util.SupportedDevices;
import com.suunto.connectivity.util.workqueue.WorkQueue;
import com.suunto.connectivity.watch.WatchBt;
import com.suunto.connectivity.watch.WatchState;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;
import okhttp3.OkHttpClient;
import static org.junit.Assert.assertEquals;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import static org.mockito.ArgumentMatchers.any;
import org.mockito.Mock;
import org.mockito.Mockito;
import static org.mockito.Mockito.when;
import org.mockito.junit.MockitoJUnit;
import org.mockito.junit.MockitoRule;
import org.mockito.stubbing.Answer;
import rx.Completable;
import rx.Scheduler;
import rx.Single;
import rx.Subscription;
import rx.functions.Func1;
import rx.observers.TestSubscriber;
import rx.plugins.RxJavaHooks;
import rx.schedulers.TestScheduler;
import rx.subjects.BehaviorSubject;
import rx.subjects.PublishSubject;
import rx.subjects.Subject;

public class ConnectionStateMachineTest {

    @Rule
    public MockitoRule mockitoRule = MockitoJUnit.rule();

    @Mock
    private Context context;

    @Mock
    private WatchBt watchBt;

    @Mock
    private BtStateMonitor btStateMonitor;

    @Mock
    private SuuntoBtDevice suuntoBtDevice;

    @Mock
    private ConnectionAnalytics analytics;

    @Mock
    ConnectSuccessRateCounter connectSuccessRateCounter;

    @Mock
    private ConnectionAnalyticsSequence connectionAnalyticsSequence;

    @Mock
    private BluetoothAdapter bluetoothAdapter;

    @Mock
    private SynchronizerStorage synchronizerStorage;

    @Mock
    private FirmwareInformationInterface firmwareInformationInterface;

    @Mock
    RepositoryConfiguration repositoryConfiguration;

    @Mock
    private SharedPreferences suuntoServicePreferences;

    @Mock
    private WatchSynchronizer watchSynchronizer;

    @Mock
    private OnDeviceConnectedHook onDeviceConnectedHook;

    @Mock
    private OkHttpClient okHttpClient;

    @Mock
    private Handler handler;

    private Subject<WatchState, WatchState> watchStateSubject;

    private PublishSubject<String> connectSubject = PublishSubject.create();
    private List<Trigger> allTriggers = getAllTriggers();
    private PublishSubject<String> tempConnectSubject;
    private WorkQueue testWorkQueue;
    /**
     * States assumed when firing Triggers.ForceUnpair.
     */
    private final List<States> forceUnpairedStates = new ArrayList<>(
        Arrays.asList(States.ForceUnpair, States.Unpairing, States.NoDevice));

    /**
     * States assumed when firing Triggers.Unpaired.
     */
    private final List<States> unpairedStates = new ArrayList<>(
        Arrays.asList(States.Unpairing, States.NoDevice));

    @Before
    public void setUp() {
        SharedPreferences.Editor mockEditor = Mockito.mock(SharedPreferences.Editor.class);
        WatchState watchState
            = WatchState.builder().connectionState(WatchState.ConnectionState.CONNECTED).build();
        watchStateSubject = BehaviorSubject.create(watchState).toSerialized();
        when(watchBt.getSuuntoBtDevice()).thenReturn(suuntoBtDevice);
        when(watchBt.disconnect()).thenReturn(Completable.complete());
        when(watchBt.destroyBleDevice()).thenReturn(Completable.complete());
        when(watchBt.unpair()).thenReturn(Completable.complete());
        when(watchBt.unpairPairDeviceFromPhone()).thenReturn(Completable.complete());
        when(watchBt.getStateChangeObservable()).thenReturn(watchStateSubject);
        when(watchBt.getWatchSynchronizer()).thenReturn(watchSynchronizer);
        when(suuntoBtDevice.getDeviceType()).thenReturn(SuuntoDeviceType.Suunto9);
        when(analytics.createNewSequence(any())).thenReturn(connectionAnalyticsSequence);
        when(analytics.getConnectSuccessRateCounter()).thenReturn(connectSuccessRateCounter);
        when(connectionAnalyticsSequence.getWatchPairingState()).thenReturn(PairingState.Unpaired);
        when(bluetoothAdapter.isEnabled()).thenReturn(true);
        when(suuntoServicePreferences.edit()).thenReturn(mockEditor);
        when(handler.post(any())).thenAnswer(invocation -> {
            Runnable runnable = invocation.getArgument(0);
            runnable.run();
            return true;
        });
    }

    /**
     * Generate a lite containing all triggers used in Connection State Machine.
     *
     * @return List of all triggers.
     */
    private List<Trigger> getAllTriggers() {
        Trigger[] triggers1 = Triggers.values();
        Trigger[] triggers2 = ReconnectState.ExitTriggers.values();
        Trigger[] triggers3 = InitialConnectingState.EntryTriggers.values();
        Trigger[] triggers4 = InitialConnectingState.ExitTriggers.values();
        List<Trigger> triggers = new ArrayList<>();
        triggers.addAll(Arrays.asList(triggers1));
        triggers.addAll(Arrays.asList(triggers2));
        triggers.addAll(Arrays.asList(triggers3));
        triggers.addAll(Arrays.asList(triggers4));
        return triggers;
    }

    /**
     * Return current state.
     *
     * @param stateMachine State machine.
     * @return State.
     */
    private State getState(ConnectionStateMachine stateMachine) {
        StateMachineImplementation internalMachine =
            stateMachine.getStateMachine();
        return internalMachine.getCurrentStateObservable()
            .first()
            .toSingle()
            .toBlocking()
            .value()
            .getState();
    }

    private void fireSynchronous(ConnectionStateMachine stateMachine, Trigger trigger) {
        doFireSynchronous(stateMachine, trigger, true, null, null);
    }

    private <TArg> void fireSynchronous(ConnectionStateMachine stateMachine, Trigger trigger,
        TArg argument, @Nullable Class<TArg> classe) {
        doFireSynchronous(stateMachine, trigger, true, argument, classe);
    }

    private void fireSynchronous(ConnectionStateMachine stateMachine,
        Trigger trigger,
        Boolean expectedValue) {
        doFireSynchronous(stateMachine, trigger, expectedValue, null, null);
    }

    /**
     * Fire trigger to state machine synchronously and verify that fire success result is expected.
     *
     * @param stateMachine State
     * @param trigger Trigger
     * @param expectedValue Expected trigger success value. True/False.
     * @param argument Trigger argument.
     * @param classe Argument class.
     * @param <TArg> Argument template class.
     */
    private <TArg> void doFireSynchronous(ConnectionStateMachine stateMachine,
        Trigger trigger,
        Boolean expectedValue,
        @Nullable TArg argument, @Nullable Class<TArg> classe) {
        StateMachineImplementation internalMachine =
            stateMachine.getStateMachine();

        TestSubscriber<Boolean> testSubscriber = new TestSubscriber<>();
        if (argument == null) {
            internalMachine.fireSingle(trigger).subscribe(testSubscriber);
        } else {
            internalMachine.fireSingle(trigger, argument, classe).subscribe(testSubscriber);
        }
        testSubscriber.awaitTerminalEvent();
        testSubscriber.assertValue(expectedValue);
    }

    /**
     * Monitor state changes.
     *
     * @param testSubscriber Test subscriber.
     * @param stateMachine State machine.
     * @param assumedStateNames Assumed state changes.
     */
    private Subscription subscribeStateChanges(
        TestSubscriber<List<CurrentState>> testSubscriber,
        ConnectionStateMachine stateMachine,
        List<String> assumedStateNames) {
        StateMachineImplementation internalMachine =
            stateMachine.getStateMachine();

        internalMachine.getCurrentStateObservable()
            // Skip the first one because it is the initial state.
            .skip(1)
            .buffer(assumedStateNames.size())
            .first()
            .toSingle()
            .subscribe(testSubscriber);
        return testSubscriber;
    }

    /**
     * Verify there is no state changes on triggers which should not cause state change.
     *
     * @param stateMachine State machine.
     * @param triggersCausingStateChange Triggers which should cause state change.
     */
    private void verifyNoStateChanges(ConnectionStateMachine stateMachine,
        List<Trigger> triggersCausingStateChange) {
        State originalState = getState(stateMachine);

        for (Trigger trigger : allTriggers) {
            if (!triggersCausingStateChange.contains(trigger)) {
                TestSubscriber<List<CurrentState>> testSubscriber = new TestSubscriber<>();
                Subscription stateChangesSubscription = subscribeStateChanges
                    (testSubscriber, stateMachine,
                        Collections.singletonList(originalState.getStateName()));
                // Fire trigger.
                fireSynchronous(stateMachine, trigger, false);
                assertEquals(0, testSubscriber.getOnNextEvents().size());
                assertEquals(getState(stateMachine), originalState);
                // Assert that workqueue is empty.
                assertEquals(0, testWorkQueue.size());
                stateChangesSubscription.unsubscribe();
            }
        }
    }

    /**
     * Verify that trigger generates state changes in correct order.
     *
     * @param stateMachine State machine.
     * @param trigger Trigger
     * @param assumedStateNames List of assumed state names in order they should appear.
     * @param argument Trigger argument.
     * @param classe Argument class type.
     * @param <TArg> Argument template class.
     */
    private <TArg> void doVerifyStateChange(
        ConnectionStateMachine stateMachine, Trigger trigger,
        List<String> assumedStateNames, @Nullable TArg argument, @Nullable Class<TArg> classe) {
        TestSubscriber<List<CurrentState>> testSubscriber = new TestSubscriber<>();

        subscribeStateChanges(testSubscriber, stateMachine, assumedStateNames);
        if (argument != null) {
            doFireSynchronous(stateMachine, trigger, true, argument, classe);
        } else {
            doFireSynchronous(stateMachine, trigger, true, null, null);
        }
        testSubscriber.awaitTerminalEvent(2, TimeUnit.SECONDS);
        List<List<CurrentState>> onNextEvents = testSubscriber.getOnNextEvents();
        // Assert that there are correct state changes.
        assertEquals(1, onNextEvents.size());

        int counter = 0;
        for (CurrentState state : onNextEvents.get(0)) {
            assertEquals(state.getState().getStateName(), assumedStateNames.get(counter));
            counter++;
        }
        // Assert that workqueue is empty.
        assertEquals(0, testWorkQueue.size());

        stateMachine.onDestroy();
    }

    private <TArg> void verifyStateChange(
        ConnectionStateMachine stateMachine,
        Trigger trigger,
        States assumedState,
        @Nullable TArg argument,
        @Nullable Class<TArg> classe) {
        doVerifyStateChange(stateMachine, trigger,
            Collections.singletonList(assumedState.name()),
            argument,
            classe);
    }

    private void verifyStateChange(
        ConnectionStateMachine stateMachine,
        Trigger trigger,
        States assumedState) {
        verifyStateChange(stateMachine, trigger, assumedState, null, null);
    }

    private void verifyStateChange(
        ConnectionStateMachine stateMachine,
        Trigger trigger,
        List<States> assumedStates) {
        List<String> assumedStateNames = new ArrayList<>();
        for (States state : assumedStates) {
            assumedStateNames.add(state.name());
        }
        doVerifyStateChange(stateMachine, trigger, assumedStateNames, null, null);
    }

    private ConnectionStateMachine stateMachineToNoDeviceState() {
        testWorkQueue = new WorkQueue(handler);
        return new ConnectionStateMachine(watchBt, btStateMonitor, context, analytics,
            testWorkQueue, bluetoothAdapter, new SupportedDevices(), synchronizerStorage,
            firmwareInformationInterface, repositoryConfiguration, suuntoServicePreferences,
            onDeviceConnectedHook, okHttpClient);
    }

    @Test(timeout = 10000)
    public void noDeviceStateTest() {
        ConnectionStateMachine stateMachine = stateMachineToNoDeviceState();
        List<Trigger> triggers = new ArrayList<>();
        triggers.add(InitialConnectingState.EntryTriggers.Connect);
        triggers.add(Triggers.ServiceStartConnect);
        triggers.add(Triggers.ForceUnpair);
        triggers.add(Triggers.Disconnected);
        triggers.add(Triggers.BToff);
        verifyNoStateChanges(stateMachine, triggers);

        when(watchBt.connect(any())).thenReturn(connectSubject.first().toSingle());

        verifyStateChange(stateMachine, InitialConnectingState.EntryTriggers.Connect,
            States.InitialConnecting, Paired, PairingState.class);

        stateMachine = stateMachineToNoDeviceState();
        ConnectMetadata connectMetadata = new ConnectMetadata(ConnectReason.ServiceStarting, true);
        verifyStateChange(stateMachine, Triggers.ServiceStartConnect,
            States.Reconnecting, connectMetadata, ConnectMetadata.class);

        stateMachine = stateMachineToNoDeviceState();
        verifyStateChange(stateMachine, Triggers.ForceUnpair,
            forceUnpairedStates);

        stateMachine = stateMachineToNoDeviceState();
        verifyStateChange(stateMachine, Triggers.Disconnected,
            States.NoDevice);

        stateMachine = stateMachineToNoDeviceState();
        verifyStateChange(stateMachine, Triggers.BToff,
            States.NoDevice);
    }

    private ConnectionStateMachine stateMachineToInitialConnectState() {
        ConnectionStateMachine stateMachine = stateMachineToNoDeviceState();
        fireSynchronous(stateMachine, InitialConnectingState.EntryTriggers.Connect,
            Paired, PairingState.class);
        return stateMachine;
    }

    /**
     * Verifies that state machine doesn't have tasks in the workqueue and state is correct.
     *
     * @param connectionStateMachine State machine.
     * @param assumesState Correct end state.
     */
    @SuppressWarnings("")
    private void assertCorrectEndState(ConnectionStateMachine connectionStateMachine,
        States assumesState) {
        assertEquals(getState(connectionStateMachine).getStateName(), assumesState.name());
        assertEquals(0, testWorkQueue.size());
    }

    @Test(timeout = 10000)
    public void initialConnectStateTest() {
        when(watchBt.connect(any())).thenReturn(connectSubject.first().toSingle());
        ConnectionStateMachine stateMachine = stateMachineToInitialConnectState();
        List<Trigger> triggers = new ArrayList<>();
        triggers.add(InitialConnectingState.ExitTriggers.Connected);
        triggers.add(InitialConnectingState.ExitTriggers.ConnectFailed);
        triggers.add(Triggers.ForceUnpair);
        triggers.add(Triggers.StateMachineDestroy);

        verifyNoStateChanges(stateMachine, triggers);

        verifyStateChange(stateMachine, InitialConnectingState.ExitTriggers.ConnectFailed,
            States.NoDevice);

        stateMachine = stateMachineToInitialConnectState();
        verifyStateChange(stateMachine, InitialConnectingState.ExitTriggers.Connected,
            States.Connected);

        stateMachine = stateMachineToInitialConnectState();
        verifyStateChange(stateMachine, Triggers.ForceUnpair,
            forceUnpairedStates);

        stateMachine = stateMachineToInitialConnectState();
        verifyStateChange(stateMachine, Triggers.StateMachineDestroy,
            States.NoDevice);

        // Test connect.
        stateMachine = stateMachineToInitialConnectState();
        TestSubscriber<List<CurrentState>> testSubscriber = new TestSubscriber<>();
        subscribeStateChanges(testSubscriber, stateMachine,
            Collections.singletonList(States.Connected.name()));
        connectSubject.onNext("11.22.33.44");
        testSubscriber.awaitTerminalEvent(2, TimeUnit.SECONDS);
        assertCorrectEndState(stateMachine, States.Connected);
        stateMachine.onDestroy();

        // Test connect fail.
        PublishSubject<String> connect = PublishSubject.create();
        when(watchBt.connect(any())).thenReturn(connect.first().toSingle());
        stateMachine = stateMachineToInitialConnectState();
        testSubscriber = new TestSubscriber<>();
        subscribeStateChanges(testSubscriber, stateMachine,
            Collections.singletonList(States.NoDevice.name()));
        connect.onError(new Exception());
        testSubscriber.awaitTerminalEvent(2, TimeUnit.SECONDS);
        assertCorrectEndState(stateMachine, States.NoDevice);
        stateMachine.onDestroy();
    }

    private void addBtOffAndUnpairTriggers(List<Trigger> triggers) {
        triggers.add(Triggers.BToff);
        triggers.add(Triggers.Unpaired);
        triggers.add(Triggers.ForceUnpair);
    }

    private ConnectionStateMachine stateMachineToReconnectState() {
        ConnectionStateMachine stateMachine = stateMachineToNoDeviceState();
        ConnectMetadata connectMetadata = new ConnectMetadata(ConnectReason.ServiceStarting, true);
        fireSynchronous(stateMachine, Triggers.ServiceStartConnect, connectMetadata,
            ConnectMetadata.class);
        return stateMachine;
    }

    @Test(timeout = 10000)
    public void reconnectStateTest() {
        when(watchBt.connect(any())).thenReturn(connectSubject.first().toSingle());
        ConnectionStateMachine stateMachine = stateMachineToReconnectState();
        List<Trigger> triggers = new ArrayList<>();
        triggers.add(Triggers.Connected);
        triggers.add(Triggers.Disconnected);
        triggers.add(ReconnectState.ExitTriggers.ReconnectFailed);
        triggers.add(Triggers.LegacyDeviceRefreshAlarm);
        triggers.add(Triggers.ConnectionInstabilityDetected);
        triggers.add(Triggers.ConnectReset);
        triggers.add(Triggers.StateMachineDestroy);
        addBtOffAndUnpairTriggers(triggers);
        verifyNoStateChanges(stateMachine, triggers);

        verifyStateChange(stateMachine, Triggers.Connected, States.Connected);

        stateMachine = stateMachineToReconnectState();
        verifyStateChange(stateMachine, Triggers.Disconnected, States.Reconnecting);

        stateMachine = stateMachineToReconnectState();
        ConnectMetadata connectMetadata = new ConnectMetadata(ConnectReason.ServiceStarting, true);
        verifyStateChange(stateMachine, ReconnectState.ExitTriggers.ReconnectFailed,
            States.Reconnecting, connectMetadata, ConnectMetadata.class);

        stateMachine = stateMachineToReconnectState();
        verifyStateChange(stateMachine, Triggers.LegacyDeviceRefreshAlarm,
            States.Reconnecting);

        stateMachine = stateMachineToReconnectState();
        verifyStateChange(stateMachine, Triggers.ConnectionInstabilityDetected,
            States.ConnectionProblem);

        stateMachine = stateMachineToReconnectState();
        Integer resetTime = 10;
        verifyStateChange(stateMachine, Triggers.ConnectReset,
            States.ResetConnectionState, resetTime, Integer.class);

        stateMachine = stateMachineToReconnectState();
        verifyStateChange(stateMachine, Triggers.StateMachineDestroy,
            States.NoDevice);

        // Bt off and unpair tests.

        stateMachine = stateMachineToReconnectState();
        verifyStateChange(stateMachine, Triggers.BToff,
            States.BtOff);

        stateMachine = stateMachineToReconnectState();
        verifyStateChange(stateMachine, Triggers.Unpaired,
            unpairedStates);

        stateMachine = stateMachineToReconnectState();
        verifyStateChange(stateMachine, Triggers.ForceUnpair,
            forceUnpairedStates);
    }

    private PublishSubject<String> createTempPublishSubject() {
        tempConnectSubject = PublishSubject.create();
        return tempConnectSubject;
    }

    @Test(timeout = 10000)
    public void reconnectStateFunctionalTests() {
        final int waitForReconnectSeconds = 15;
        // Use test scheduler as computation scheduler in order to prevent timeout wait.
        TestScheduler testScheduler = new TestScheduler();
        Func1<Scheduler, Scheduler> computationSchedulerFunc = RxJavaHooks
            .getOnComputationScheduler();
        RxJavaHooks.setOnComputationScheduler(scheduler -> testScheduler);

        // Test connect.
        when(watchBt.connect(any())).thenReturn(connectSubject.first().toSingle());
        ConnectionStateMachine stateMachine = stateMachineToReconnectState();
        TestSubscriber<List<CurrentState>> testSubscriber = new TestSubscriber<>();
        subscribeStateChanges(testSubscriber, stateMachine,
            Collections.singletonList(States.Connected.name()));
        testScheduler.advanceTimeBy(waitForReconnectSeconds, TimeUnit.SECONDS);
        connectSubject.onNext("11.22.33.44");
        testSubscriber.awaitTerminalEvent(2, TimeUnit.SECONDS);
        assertCorrectEndState(stateMachine, States.Connected);
        stateMachine.onDestroy();

        // Test connect fail. Should re-enter to Reconnecting state.
        when(watchBt.connect(any())).thenAnswer(
            (Answer<Single<String>>) invocation -> createTempPublishSubject().first().toSingle());
        stateMachine = stateMachineToReconnectState();
        testSubscriber = new TestSubscriber<>();
        subscribeStateChanges(testSubscriber, stateMachine,
            Collections.singletonList(States.Reconnecting.name()));
        testScheduler.advanceTimeBy(waitForReconnectSeconds, TimeUnit.SECONDS);
        tempConnectSubject.onError(new Exception());
        testSubscriber.awaitTerminalEvent(2, TimeUnit.SECONDS);
        assertCorrectEndState(stateMachine, States.Reconnecting);

        // Test MAX_COUNTED_CONNECT_ATTEMPTS
        for (int i = 2; i < MAX_COUNTED_CONNECT_ATTEMPTS; i++) {
            testSubscriber = new TestSubscriber<>();
            subscribeStateChanges(testSubscriber, stateMachine,
                Collections.singletonList(States.Reconnecting.name()));
            testScheduler.advanceTimeBy(waitForReconnectSeconds, TimeUnit.SECONDS);
            tempConnectSubject.onError(new Exception());
            testSubscriber.awaitTerminalEvent(2, TimeUnit.SECONDS);
            assertCorrectEndState(stateMachine, States.Reconnecting);
        }

        // Test >MAX_COUNTED_CONNECT_ATTEMPTS
        testScheduler.advanceTimeBy(waitForReconnectSeconds, TimeUnit.SECONDS);
        testSubscriber = new TestSubscriber<>();
        subscribeStateChanges(testSubscriber, stateMachine,
            Collections.singletonList(States.ConnectionProblem.name()));
        tempConnectSubject.onError(new Exception());
        testSubscriber.awaitTerminalEvent(2, TimeUnit.SECONDS);
        assertCorrectEndState(stateMachine, States.ConnectionProblem);
        stateMachine.onDestroy();

        // Test MAX_TOTAL_CONNECT_ATTEMPTS
        stateMachine = stateMachineToReconnectState();
        for (int i = 1; i < MAX_TOTAL_CONNECT_ATTEMPTS; i++) {
            testSubscriber = new TestSubscriber<>();
            subscribeStateChanges(testSubscriber, stateMachine,
                Collections.singletonList(States.Reconnecting.name()));
            testScheduler.advanceTimeBy(waitForReconnectSeconds, TimeUnit.SECONDS);
            tempConnectSubject.onError(new NgBleManager.BleConnectionLostError());
            testSubscriber.awaitTerminalEvent(2, TimeUnit.SECONDS);
            assertCorrectEndState(stateMachine, States.Reconnecting);
        }

        // Test >MAX_TOTAL_CONNECT_ATTEMPTS
        testSubscriber = new TestSubscriber<>();
        subscribeStateChanges(testSubscriber, stateMachine,
            Collections.singletonList(States.ConnectionProblem.name()));
        testScheduler.advanceTimeBy(waitForReconnectSeconds, TimeUnit.SECONDS);
        tempConnectSubject.onError(new NgBleManager.BleConnectionLostError());
        testSubscriber.awaitTerminalEvent(2, TimeUnit.SECONDS);
        assertCorrectEndState(stateMachine, States.ConnectionProblem);

        stateMachine.onDestroy();

        // Set computation scheduler back to original.
        RxJavaHooks.setOnComputationScheduler(computationSchedulerFunc);
    }

    private ConnectionStateMachine stateMachineToConnectionInstabilityState() {
        ConnectionStateMachine stateMachine = stateMachineToNoDeviceState();
        ConnectMetadata connectMetadata = new ConnectMetadata(ConnectReason.ServiceStarting, true);
        fireSynchronous(stateMachine, Triggers.ServiceStartConnect, connectMetadata,
            ConnectMetadata.class);
        fireSynchronous(stateMachine, Triggers.ConnectionInstabilityDetected);
        return stateMachine;
    }

    @Test(timeout = 10000)
    public void connectionInstabilityStateTest() {
        when(watchBt.connect(any())).thenReturn(connectSubject.first().toSingle());
        ConnectionStateMachine stateMachine = stateMachineToConnectionInstabilityState();
        List<Trigger> triggers = new ArrayList<>();
        triggers.add(InitialConnectingState.EntryTriggers.Connect);
        triggers.add(Triggers.ConnectionInstabilityDelayPassed);
        triggers.add(Triggers.ConnectionInstabilityCleared);
        triggers.add(Triggers.ConnectReset);
        triggers.add(Triggers.StateMachineDestroy);
        addBtOffAndUnpairTriggers(triggers);
        verifyNoStateChanges(stateMachine, triggers);

        verifyStateChange(stateMachine, Triggers.ConnectionInstabilityDelayPassed,
            States.Reconnecting);

        stateMachine = stateMachineToConnectionInstabilityState();
        verifyStateChange(stateMachine, Triggers.ConnectionInstabilityCleared,
            States.Reconnecting);

        stateMachine = stateMachineToConnectionInstabilityState();
        Integer resetTime = 10;
        verifyStateChange(stateMachine, Triggers.ConnectReset,
            States.ResetConnectionState, resetTime, Integer.class);

        stateMachine = stateMachineToConnectionInstabilityState();
        verifyStateChange(stateMachine, Triggers.StateMachineDestroy,
            States.NoDevice);

        // Bt off and unpair tests.

        stateMachine = stateMachineToConnectionInstabilityState();
        verifyStateChange(stateMachine, Triggers.BToff,
            States.BtOff);

        stateMachine = stateMachineToConnectionInstabilityState();
        verifyStateChange(stateMachine, Triggers.Unpaired,
            unpairedStates);

        stateMachine = stateMachineToConnectionInstabilityState();
        verifyStateChange(stateMachine, Triggers.ForceUnpair,
            forceUnpairedStates);

        // Use test scheduler as computation scheduler in order to prevent timeout wait.
        TestScheduler testScheduler = new TestScheduler();
        Func1<Scheduler, Scheduler> computationSchedulerFunc = RxJavaHooks
            .getOnComputationScheduler();
        RxJavaHooks.setOnComputationScheduler(scheduler -> testScheduler);

        /*
         * Test instability timeout. Reconnection should happen after timeout.
         */
        stateMachine = stateMachineToConnectionInstabilityState();
        TestSubscriber<List<CurrentState>> testSubscriber = new TestSubscriber<>();
        subscribeStateChanges(testSubscriber, stateMachine,
            Collections.singletonList(States.Reconnecting.name()));
        testScheduler.advanceTimeBy(RECONNECT_WAIT_MINUTES + 1, TimeUnit.MINUTES);
        testSubscriber.awaitTerminalEvent(2, TimeUnit.SECONDS);
        assertCorrectEndState(stateMachine, States.Reconnecting);

        stateMachine.onDestroy();
        RxJavaHooks.setOnComputationScheduler(computationSchedulerFunc);
    }

    private ConnectionStateMachine stateMachineToResetConnectionState() {
        ConnectionStateMachine stateMachine = stateMachineToReconnectState();
        Integer resetTime = 10;
        fireSynchronous(stateMachine, Triggers.ConnectReset, resetTime, Integer.class);
        return stateMachine;
    }

    @Test(timeout = 10000)
    public void resetConnectionStateTest() {
        when(watchBt.connect(any())).thenReturn(connectSubject.first().toSingle());
        ConnectionStateMachine stateMachine = stateMachineToResetConnectionState();
        List<Trigger> triggers = new ArrayList<>();
        triggers.add(Triggers.ConnectionResetDelayPassed);
        triggers.add(Triggers.StateMachineDestroy);
        addBtOffAndUnpairTriggers(triggers);

        verifyNoStateChanges(stateMachine, triggers);

        verifyStateChange(stateMachine, Triggers.ConnectionResetDelayPassed,
            States.Reconnecting);

        stateMachine = stateMachineToResetConnectionState();
        verifyStateChange(stateMachine, Triggers.StateMachineDestroy,
            States.NoDevice);

        // Bt off and unpair tests.

        stateMachine = stateMachineToResetConnectionState();
        verifyStateChange(stateMachine, Triggers.BToff,
            States.BtOff);

        stateMachine = stateMachineToResetConnectionState();
        verifyStateChange(stateMachine, Triggers.Unpaired,
            unpairedStates);

        stateMachine = stateMachineToResetConnectionState();
        verifyStateChange(stateMachine, Triggers.ForceUnpair,
            forceUnpairedStates);
    }

    private ConnectionStateMachine stateMachineToForceUnpairState() {
        ConnectionStateMachine stateMachine = stateMachineToReconnectState();
        fireSynchronous(stateMachine, Triggers.ForceUnpair);
        return stateMachine;
    }

    @Test(timeout = 10000)
    public void forceUnpairStateTest() {
        PublishSubject<Boolean> unpairSubject = PublishSubject.create();
        when(watchBt.unpair()).thenReturn(unpairSubject.first().toCompletable());
        when(watchBt.connect(any())).thenReturn(connectSubject.first().toSingle());
        ConnectionStateMachine stateMachine = stateMachineToForceUnpairState();
        List<Trigger> triggers = new ArrayList<>();
        triggers.add(Triggers.ForceUnpaired);
        triggers.add(Triggers.StateMachineDestroy);
        triggers.add(Triggers.BToff);

        verifyNoStateChanges(stateMachine, triggers);

        verifyStateChange(stateMachine, Triggers.BToff,
            States.ForceUnpair);

        // Verify States.NoDevice after unpair.
        stateMachine = stateMachineToForceUnpairState();
        List<String> expectedStates = new ArrayList<>(
            Arrays.asList(
                States.Unpairing.name(),
                States.NoDevice.name()));
        TestSubscriber<List<CurrentState>> testSubscriber = new TestSubscriber<>();
        subscribeStateChanges(testSubscriber, stateMachine, expectedStates);
        unpairSubject.onNext(true);
        testSubscriber.awaitTerminalEvent(2, TimeUnit.SECONDS);
        assertCorrectEndState(stateMachine, States.NoDevice);
        stateMachine.onDestroy();
        when(watchBt.unpair()).thenReturn(Completable.complete());
    }

    private ConnectionStateMachine stateMachineToConnectedState() {
        ConnectionStateMachine stateMachine = stateMachineToReconnectState();
        fireSynchronous(stateMachine, Triggers.Connected);
        return stateMachine;
    }

    @Test(timeout = 10000)
    public void connectedStateTest() {
        when(watchBt.connect(any())).thenReturn(connectSubject.first().toSingle());
        ConnectionStateMachine stateMachine = stateMachineToConnectedState();
        List<Trigger> triggersCausingStateChange = new ArrayList<>();
        triggersCausingStateChange.add(Triggers.BTon);
        triggersCausingStateChange.add(Triggers.ConnectReset);
        triggersCausingStateChange.add(Triggers.StateMachineDestroy);
        triggersCausingStateChange.add(Triggers.Disconnected);
        triggersCausingStateChange.add(Triggers.AncsStateChanged);
        addBtOffAndUnpairTriggers(triggersCausingStateChange);

        verifyNoStateChanges(stateMachine, triggersCausingStateChange);

        verifyStateChange(stateMachine, Triggers.BTon,
            States.Reconnecting);

        stateMachine = stateMachineToConnectedState();
        Integer resetTime = 10;
        verifyStateChange(stateMachine, Triggers.ConnectReset,
            States.ResetConnectionState, resetTime, Integer.class);

        stateMachine = stateMachineToConnectedState();
        verifyStateChange(stateMachine, Triggers.StateMachineDestroy,
            States.NoDevice);

        stateMachine = stateMachineToConnectedState();
        verifyStateChange(stateMachine, Triggers.Disconnected,
            States.Reconnecting);

        stateMachine = stateMachineToConnectedState();
        verifyStateChange(stateMachine, Triggers.AncsStateChanged,
            States.Reconnecting);

        // Bt off and unpair tests.

        stateMachine = stateMachineToResetConnectionState();
        verifyStateChange(stateMachine, Triggers.BToff,
            States.BtOff);

        stateMachine = stateMachineToResetConnectionState();
        verifyStateChange(stateMachine, Triggers.Unpaired,
            unpairedStates);

        stateMachine = stateMachineToResetConnectionState();
        verifyStateChange(stateMachine, Triggers.ForceUnpair,
            forceUnpairedStates);
    }

    private ConnectionStateMachine stateMachineToBtOffSState() {
        ConnectionStateMachine stateMachine = stateMachineToReconnectState();
        fireSynchronous(stateMachine, Triggers.BToff);
        return stateMachine;
    }

    @Test(timeout = 10000)
    public void btOffStateTest() {
        when(watchBt.connect(any())).thenReturn(connectSubject.first().toSingle());
        ConnectionStateMachine stateMachine = stateMachineToBtOffSState();
        List<Trigger> triggers = new ArrayList<>();
        triggers.add(Triggers.BTon);
        triggers.add(Triggers.Unpaired);
        triggers.add(Triggers.ForceUnpair);
        triggers.add(Triggers.Disconnected);
        triggers.add(Triggers.BToff);
        triggers.add(Triggers.StateMachineDestroy);
        addBtOffAndUnpairTriggers(triggers);

        verifyNoStateChanges(stateMachine, triggers);

        verifyStateChange(stateMachine, Triggers.BTon,
            States.Reconnecting);

        stateMachine = stateMachineToBtOffSState();
        verifyStateChange(stateMachine, Triggers.Unpaired,
            unpairedStates);

        stateMachine = stateMachineToResetConnectionState();
        verifyStateChange(stateMachine, Triggers.ForceUnpair,
            forceUnpairedStates);

        stateMachine = stateMachineToBtOffSState();
        verifyStateChange(stateMachine, Triggers.Disconnected,
            States.BtOff);

        stateMachine = stateMachineToBtOffSState();
        verifyStateChange(stateMachine, Triggers.BToff,
            States.BtOff);

        stateMachine = stateMachineToBtOffSState();
        verifyStateChange(stateMachine, Triggers.StateMachineDestroy,
            States.NoDevice);
    }

    @Test(timeout = 10000)
    public void unPairingStateTest() {
        when(watchBt.connect(any())).thenReturn(connectSubject.first().toSingle());
        ConnectionStateMachine stateMachine = stateMachineToReconnectState();

        /*
         * Test that firing "Unpaired" causes state machine to to transit states
         * Reconnecting -> Unpairing -> NoDevice
         */
        List<String> expectedStates = new ArrayList<>(
            Arrays.asList(
                States.Unpairing.name(),
                States.NoDevice.name()));
        TestSubscriber<List<CurrentState>> testSubscriber = new TestSubscriber<>();
        subscribeStateChanges(testSubscriber, stateMachine, expectedStates);
        fireSynchronous(stateMachine, Triggers.Unpaired);
        testSubscriber.awaitTerminalEvent(2, TimeUnit.SECONDS);
        assertCorrectEndState(stateMachine, States.NoDevice);
        stateMachine.onDestroy();
    }
}
