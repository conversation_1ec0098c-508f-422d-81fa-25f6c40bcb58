package com.suunto.connectivity.suuntoconnectivity.utils;

import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothGatt;
import android.bluetooth.BluetoothManager;
import android.bluetooth.BluetoothProfile;

import com.suunto.connectivity.suuntoconnectivity.ble.event.BleGattConnectionStateChangedEvent;
import com.suunto.connectivity.suuntoconnectivity.ble.event.BleGattServerConnectionStateChangedEvent;

import org.greenrobot.eventbus.EventBus;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ConnectionStateMonitorTest {

    @Mock
    private BluetoothManager bluetoothManager;

    @Mock
    private BluetoothGatt gatt1;

    @Mock
    private BluetoothGatt gatt2;

    @Mock
    private BluetoothDevice device1;

    @Mock
    private BluetoothDevice device2;

    private ConnectionStateMonitor connectionStateMonitor;

    @Before
    public void setUp() {
        connectionStateMonitor = new ConnectionStateMonitor(
                bluetoothManager, EventBus.getDefault());

        when(gatt1.getDevice()).thenReturn(device1);
        when(gatt2.getDevice()).thenReturn(device2);

        when(bluetoothManager.getConnectionState(device1, BluetoothProfile.GATT))
                .thenReturn(BluetoothProfile.STATE_CONNECTED);
        when(bluetoothManager.getConnectionState(device1, BluetoothProfile.GATT_SERVER))
                .thenReturn(BluetoothProfile.STATE_DISCONNECTED);
        when(bluetoothManager.getConnectionState(device2, BluetoothProfile.GATT))
                .thenReturn(BluetoothProfile.STATE_DISCONNECTED);
        when(bluetoothManager.getConnectionState(device2, BluetoothProfile.GATT_SERVER))
                .thenReturn(BluetoothProfile.STATE_CONNECTED);
    }

    @After
    public void tearDown() {
        connectionStateMonitor.onDestroy();
    }

    @Test
    public void connectionStateFromGattEvent() {
        // Connection state is captured from gatt connection state event
        EventBus.getDefault().post(new BleGattConnectionStateChangedEvent(
                gatt1, BluetoothGatt.GATT_SUCCESS, BluetoothProfile.STATE_CONNECTED));
        EventBus.getDefault().post(new BleGattConnectionStateChangedEvent(
                gatt2, BluetoothGatt.GATT_SUCCESS, BluetoothProfile.STATE_DISCONNECTED));
        assertEquals(BluetoothProfile.STATE_CONNECTED,
                connectionStateMonitor.getConnectionState(device1, BluetoothProfile.GATT));
        assertEquals(BluetoothProfile.STATE_DISCONNECTED,
                connectionStateMonitor.getConnectionState(device2, BluetoothProfile.GATT));
        verify(bluetoothManager, never()).getConnectionState(device1, BluetoothGatt.GATT);
        verify(bluetoothManager, never()).getConnectionState(device2, BluetoothProfile.GATT);

        EventBus.getDefault().post(new BleGattConnectionStateChangedEvent(
                gatt1, BluetoothGatt.GATT_SUCCESS, BluetoothProfile.STATE_DISCONNECTED));
        EventBus.getDefault().post(new BleGattConnectionStateChangedEvent(
                gatt2, BluetoothGatt.GATT_SUCCESS, BluetoothProfile.STATE_CONNECTED));
        assertEquals(BluetoothProfile.STATE_DISCONNECTED,
                connectionStateMonitor.getConnectionState(device1, BluetoothProfile.GATT));
        assertEquals(BluetoothProfile.STATE_CONNECTED,
                connectionStateMonitor.getConnectionState(device2, BluetoothProfile.GATT));
        verify(bluetoothManager, never()).getConnectionState(device1, BluetoothProfile.GATT);
        verify(bluetoothManager, never()).getConnectionState(device2, BluetoothGatt.GATT);
    }

    @Test
    public void connectionStateFromGattServerEvent() {
        // Connection state is captured from gatt server connection state event
        EventBus.getDefault().post(new BleGattServerConnectionStateChangedEvent(
                device1, BluetoothGatt.GATT_SUCCESS, BluetoothProfile.STATE_CONNECTED));
        EventBus.getDefault().post(new BleGattServerConnectionStateChangedEvent(
                device2, BluetoothGatt.GATT_SUCCESS, BluetoothProfile.STATE_DISCONNECTED));
        assertEquals(BluetoothProfile.STATE_CONNECTED,
                connectionStateMonitor.getConnectionState(device1, BluetoothProfile.GATT_SERVER));
        assertEquals(BluetoothProfile.STATE_DISCONNECTED,
                connectionStateMonitor.getConnectionState(device2, BluetoothProfile.GATT_SERVER));
        verify(bluetoothManager, never()).getConnectionState(device1, BluetoothGatt.GATT_SERVER);
        verify(bluetoothManager, never()).getConnectionState(device2, BluetoothGatt.GATT_SERVER);

        EventBus.getDefault().post(new BleGattServerConnectionStateChangedEvent(
                device1, BluetoothGatt.GATT_SUCCESS, BluetoothProfile.STATE_DISCONNECTED));
        EventBus.getDefault().post(new BleGattServerConnectionStateChangedEvent(
                device2, BluetoothGatt.GATT_SUCCESS, BluetoothProfile.STATE_CONNECTED));
        assertEquals(BluetoothProfile.STATE_DISCONNECTED,
                connectionStateMonitor.getConnectionState(device1, BluetoothProfile.GATT_SERVER));
        assertEquals(BluetoothProfile.STATE_CONNECTED,
                connectionStateMonitor.getConnectionState(device2, BluetoothProfile.GATT_SERVER));
        verify(bluetoothManager, never()).getConnectionState(device1, BluetoothGatt.GATT_SERVER);
        verify(bluetoothManager, never()).getConnectionState(device2, BluetoothGatt.GATT_SERVER);
    }

    @Test
    public void fallBackToBluetoothManager() {
        assertEquals(BluetoothProfile.STATE_CONNECTED,
                connectionStateMonitor.getConnectionState(device1, BluetoothProfile.GATT));
        verify(bluetoothManager).getConnectionState(device1, BluetoothProfile.GATT);
        assertEquals(BluetoothProfile.STATE_DISCONNECTED,
                connectionStateMonitor.getConnectionState(device1, BluetoothProfile.GATT_SERVER));
        verify(bluetoothManager).getConnectionState(device1, BluetoothProfile.GATT_SERVER);

        assertEquals(BluetoothProfile.STATE_DISCONNECTED,
                connectionStateMonitor.getConnectionState(device2, BluetoothProfile.GATT));
        verify(bluetoothManager).getConnectionState(device2, BluetoothProfile.GATT);
        assertEquals(BluetoothProfile.STATE_CONNECTED,
                connectionStateMonitor.getConnectionState(device2, BluetoothProfile.GATT_SERVER));
        verify(bluetoothManager).getConnectionState(device2, BluetoothProfile.GATT_SERVER);
    }

}
