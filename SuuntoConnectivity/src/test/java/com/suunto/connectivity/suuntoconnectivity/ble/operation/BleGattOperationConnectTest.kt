package com.suunto.connectivity.suuntoconnectivity.ble.operation

import android.bluetooth.BluetoothDevice
import android.bluetooth.BluetoothGatt
import android.bluetooth.BluetoothGattCallback
import android.bluetooth.BluetoothProfile
import android.content.Context
import com.google.common.truth.Truth.assertThat
import com.suunto.connectivity.suuntoconnectivity.ble.event.BleGattConnectionStateChangedEvent
import com.suunto.connectivity.suuntoconnectivity.ble.exception.GattConnectException
import com.suunto.connectivity.suuntoconnectivity.utils.MockHandlerProvider
import com.suunto.connectivity.util.workqueue.QueueOperation
import org.greenrobot.eventbus.EventBus
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.mockito.ArgumentCaptor
import org.mockito.kotlin.any
import org.mockito.kotlin.mock
import org.mockito.kotlin.never
import org.mockito.kotlin.spy
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import java.util.concurrent.Semaphore

class BleGattOperationConnectTest {

    private val context = mock<Context>()
    private val bluetoothDevice = mock<BluetoothDevice>()
    private val bluetoothGatt = mock<BluetoothGatt>()
    private val bluetoothGattCallback = mock<BluetoothGattCallback>()
    private lateinit var operationConnect: BleGattOperationConnect
    private val semaphore = Semaphore(0)
    private val originalHandlerProvider = QueueOperation.handlerProvider
    private val handlerProvider = MockHandlerProvider()
    private val handlerDelayController = handlerProvider.handlerDelayController

    @Before
    fun setUp() {
        QueueOperation.handlerProvider = handlerProvider
        whenever(bluetoothGatt.device).thenReturn(bluetoothDevice)
    }

    @After
    fun tearDown() {
        QueueOperation.handlerProvider = originalHandlerProvider
    }

    @Test
    fun connectSuccess() {
        whenever(
            bluetoothDevice.connectGatt(any(), any(), any(), any())
        ).thenReturn(bluetoothGatt)
        operationConnect = spy(
            BleGattOperationConnect(
                context,
                bluetoothDevice,
                bluetoothGattCallback,
                false
            )
        )
        operationConnect.setQueueBlockingSemaphore(semaphore)
        operationConnect.run()
        assertThat(operationConnect.state).isEqualTo(QueueOperation.STATE_RUNNING)
        verify(operationConnect, never()).onCompleted(any())

        // Operation is running, queue is not released.
        assertThat(semaphore.tryAcquire()).isFalse()
        EventBus.getDefault().post(
            BleGattConnectionStateChangedEvent(
                bluetoothGatt,
                BluetoothGatt.GATT_SUCCESS,
                BluetoothProfile.STATE_CONNECTED
            )
        )
        assertThat(operationConnect.state).isEqualTo(QueueOperation.STATE_FINISHED)
        verify(operationConnect).onCompleted(bluetoothGatt)
    }

    @Test
    fun autoConnectSuccess() {
        whenever(
            bluetoothDevice.connectGatt(any(), any(), any(), any())
        ).thenReturn(bluetoothGatt)
        operationConnect = spy(
            BleGattOperationConnect(
                context,
                bluetoothDevice,
                bluetoothGattCallback,
                true
            )
        )
        operationConnect.setQueueBlockingSemaphore(semaphore)
        operationConnect.run()
        assertThat(operationConnect.state).isEqualTo(QueueOperation.STATE_RUNNING)
        verify(operationConnect, never()).onCompleted(any())

        // Operation is still running, but the queue is released.
        assertThat(semaphore.tryAcquire()).isTrue()
        EventBus.getDefault().post(
            BleGattConnectionStateChangedEvent(
                bluetoothGatt,
                BluetoothGatt.GATT_SUCCESS,
                BluetoothProfile.STATE_CONNECTED
            )
        )
        assertThat(operationConnect.state).isEqualTo(QueueOperation.STATE_FINISHED)
        verify(operationConnect).onCompleted(bluetoothGatt)
    }

    @Test
    fun bluetoothGattNull() {
        whenever(bluetoothDevice.connectGatt(any(), any(), any(), any()))
            .thenReturn(null)
        operationConnect = spy(
            BleGattOperationConnect(
                context,
                bluetoothDevice,
                bluetoothGattCallback,
                true
            )
        )
        operationConnect.run()
        val argument = ArgumentCaptor.forClass(GattConnectException::class.java)
        assertThat(operationConnect.state).isEqualTo(QueueOperation.STATE_FINISHED)
        verify(operationConnect).onError(argument.capture())
        assertThat(argument.value.message).isEqualTo(BleOperation.OPERATION_START_EXCEPTION_MESSAGE)
    }

    @Test
    fun connectFailure() {
        whenever(bluetoothDevice.connectGatt(any(), any(), any(), any()))
            .thenReturn(bluetoothGatt)

        operationConnect = spy(
            BleGattOperationConnect(
                context,
                bluetoothDevice,
                bluetoothGattCallback,
                false
            )
        )
        operationConnect.run()
        EventBus.getDefault().post(
            BleGattConnectionStateChangedEvent(
                bluetoothGatt,
                BluetoothGatt.GATT_FAILURE,
                BluetoothProfile.STATE_DISCONNECTED
            )
        )

        // onError is not called immediately when disconnected event is received
        verify(operationConnect, never()).onError(any())

        // onError is called after a delay
        handlerDelayController.runAll()
        verify(operationConnect).onError(any<GattConnectException>())
    }

    @Test
    fun connectSuccessBogusDisconnectedEvent() {
        whenever(bluetoothDevice.connectGatt(any(), any(), any(), any()))
            .thenReturn(bluetoothGatt)
        operationConnect = spy(
            BleGattOperationConnect(
                context,
                bluetoothDevice,
                bluetoothGattCallback,
                false
            )
        )
        operationConnect.run()
        EventBus.getDefault().post(
            BleGattConnectionStateChangedEvent(
                bluetoothGatt,
                BluetoothGatt.GATT_FAILURE,
                BluetoothProfile.STATE_DISCONNECTED
            )
        )
        verify(operationConnect, never()).onError(any())
        EventBus.getDefault().post(
            BleGattConnectionStateChangedEvent(
                bluetoothGatt,
                BluetoothGatt.GATT_SUCCESS,
                BluetoothProfile.STATE_CONNECTED
            )
        )
        assertThat(operationConnect.state).isEqualTo(QueueOperation.STATE_FINISHED)
        verify(operationConnect).onCompleted(bluetoothGatt)
    }
}
