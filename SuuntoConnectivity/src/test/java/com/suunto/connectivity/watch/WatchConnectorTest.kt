package com.suunto.connectivity.watch

import android.bluetooth.BluetoothGatt
import com.google.common.truth.Truth.assertThat
import com.google.gson.GsonBuilder
import com.stt.android.testutils.HandlerDelayController
import com.suunto.connectivity.ngBleManager.NgBleManager
import com.suunto.connectivity.ngBleManager.NgBleManager.BleConnectionLostError
import com.suunto.connectivity.ngBleManager.NgBleManager.StartDataNotifyError
import com.suunto.connectivity.sdsmanager.MdsRx
import com.suunto.connectivity.sdsmanager.SdsBleAddressMap
import com.suunto.connectivity.sdsmanager.model.MdsConnectedDevice
import com.suunto.connectivity.sdsmanager.model.MdsConnectingDevice
import com.suunto.connectivity.sdsmanager.model.MdsDeviceInfo
import com.suunto.connectivity.suuntoconnectivity.SuuntoConnectivityClient
import com.suunto.connectivity.suuntoconnectivity.ble.exception.BluetoothDiscoverException
import com.suunto.connectivity.suuntoconnectivity.ble.exception.BluetoothWaitBondingException
import com.suunto.connectivity.suuntoconnectivity.ble.exception.GattConnectException
import com.suunto.connectivity.suuntoconnectivity.ble.operation.BluetoothOperationWaitBonding
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoBtDevice
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoBtDeviceImpl
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoDeviceType
import com.suunto.connectivity.suuntoconnectivity.utils.BtStateMonitor
import com.suunto.connectivity.suuntoconnectivity.utils.BtStateMonitor.BtEvent
import com.suunto.connectivity.suuntoconnectivity.utils.ConnectMetadata
import com.suunto.connectivity.suuntoconnectivity.utils.ConnectReason
import com.suunto.connectivity.sync.SyncState
import com.suunto.connectivity.util.RxSchedulersOverrideRule
import com.suunto.connectivity.util.workqueue.WorkQueue
import com.suunto.connectivity.watch.WatchConnector.MdsConnectTimeoutError
import com.suunto.connectivity.watch.WatchConnector.MdsFailedToConnectError
import io.reactivex.Observable
import org.json.JSONException
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import rx.Completable
import rx.Scheduler
import rx.Single
import rx.Subscription
import rx.functions.Action1
import rx.internal.schedulers.TrampolineScheduler
import rx.observers.TestSubscriber
import rx.plugins.RxJavaHooks
import rx.schedulers.TestScheduler
import rx.subjects.PublishSubject
import java.util.concurrent.ExecutionException
import java.util.concurrent.Semaphore
import java.util.concurrent.TimeUnit
import java.util.concurrent.atomic.AtomicBoolean

class WatchConnectorTest {

    @JvmField
    @Rule
    val mOverrideRule = RxSchedulersOverrideRule()

    private val bluetoothAdapter = mock<BluetoothAdapterWrapper>()
    private val sdsBleAddressMap = mock<SdsBleAddressMap>()
    private val ngBleManager = mock<NgBleManager>()
    private val mdsRx = mock<MdsRx>()
    private val suuntoConnectivityClient = mock<SuuntoConnectivityClient>()
    private val btStateMonitor = mock<BtStateMonitor>()

    private val watchStateHolder = WatchStateHolder()
    private val gson = GsonBuilder().create()
    private val connectedDevicePublisher = PublishSubject.create<MdsConnectedDevice>()
    private val connectingDevicePublisher = PublishSubject.create<MdsConnectingDevice>()
    private val testSubscriber = TestSubscriber<WatchState>()
    private val connectQueue = createWorkQueueContainer()

    private val initialConnectMetadata =
        ConnectMetadata(ConnectReason.InitialConnect, true)

    private val reconnectConnectMetadata =
        ConnectMetadata(ConnectReason.WatchDisconnected, true)

    private lateinit var watchConnector: WatchConnector

    private val rxSchedulerProvider = object : RxSchedulerProvider {
        override fun io(): Scheduler {
            return TrampolineScheduler.INSTANCE
        }

        override fun computation(): Scheduler {
            return TrampolineScheduler.INSTANCE
        }

        override fun mainThread(): Scheduler {
            return TrampolineScheduler.INSTANCE
        }
    }

    @Before
    fun setUp() {
        whenever(mdsRx.deviceConnectionObservable()).thenReturn(connectedDevicePublisher)
        whenever(btStateMonitor.btEventObservable(any())).thenReturn(
            Observable.never()
        )
        whenever(bluetoothAdapter.checkBluetoothAddress(any())).thenReturn(true)

        watchConnector = WatchConnector(
            sdsBleAddressMap,
            ngBleManager,
            mdsRx,
            suuntoConnectivityClient,
            bluetoothAdapter,
            connectQueue.workQueue,
            btStateMonitor,
            rxSchedulerProvider
        )

        watchStateHolder.stateChangeObservable.subscribe(testSubscriber)
    }

    @Test
    fun initialization() {
        val watchState = getStateChange()
        assertThat(watchState.connectionState).isEqualTo(WatchState.ConnectionState.DISCONNECTED)
        assertThat(watchState.syncState.state).isEqualTo(SyncState.NOT_SYNCING)
    }

    @Test
    fun connectWatchAlreadyConnected() {
        watchStateHolder.setConnectionState(WatchState.ConnectionState.CONNECTED)
        val subscriber = TestSubscriber<String>()
        connectWatch(NG_WATCH, watchStateHolder, false, subscriber, true)
        assertThat(getSingleValue(subscriber)).isEqualTo(NG_WATCH_MAC)
    }

    @Test
    @Throws(ExecutionException::class, InterruptedException::class, JSONException::class)
    fun connectNgWatch() {
        whenever(mdsRx.connectingDevicesObservable()).thenReturn(connectingDevicePublisher)
        val counter = SubscriptionCounter()
        val completable = Completable
            .complete()
            .doOnSubscribe(counter)
        whenever(ngBleManager.directConnect(any(), any(), any()))
            .thenReturn(completable)
        val deviceBleConnectionLostPusblisher = PublishSubject.create<String>()
        whenever(ngBleManager.bleConnected(any())).thenReturn(Completable.complete())
        whenever(ngBleManager.bleConnectionLostError(any()))
            .thenReturn(deviceBleConnectionLostPusblisher.first())

        // Start connecting to NG watch
        val subscriber = TestSubscriber<String>()
        whenever(ngBleManager.isBleOn).thenReturn(Single.just(false))
        connectWatch(NG_WATCH, watchStateHolder, false, subscriber, true)
        for (throwable in subscriber.onErrorEvents) {
            throwable.printStackTrace()
        }

        // NgBleManager connect is called and it returns success
        verify(ngBleManager).directConnect(any(), eq(NG_WATCH.deviceType), any())
        assertThat(counter.count).isEqualTo(1)
        subscriber.assertNoValues()
        subscriber.assertNoTerminalEvent()

        // Connection state changes to CONNECTING
        var watchState = getStateChange()
        assertThat(watchState.connectionState).isEqualTo(WatchState.ConnectionState.CONNECTING)
        subscriber.assertNoValues()
        subscriber.assertNoTerminalEvent()

        // onDeviceConnected is called, current watch is updated and connect completes
        connectedDevicePublisher.onNext(
            createConnectedDevice(
                NG_WATCH_SERIAL,
                NG_WATCH_FIRMWARE,
                NG_WATCH_VARIANT,
                true
            )
        )
        watchState = getStateChange()
        assertThat(watchState.connectionState).isEqualTo(WatchState.ConnectionState.CONNECTED)
        assertThat(getSingleValue(subscriber)).isEqualTo(NG_WATCH_MAC)
    }

    @Throws(JSONException::class)
    private fun connectNgWatchFailBLEDuringConnect(disconnectBLEBeforeConnect: Boolean) {
        val counter = SubscriptionCounter()
        val completable = Completable
            .complete()
            .doOnSubscribe(counter)
        val connectionLostError = BleConnectionLostError()
        whenever(ngBleManager.directConnect(any(), any(), any()))
            .thenReturn(completable)
        val deviceBleConnectionLostPusblisher = PublishSubject.create<String>()
        whenever(ngBleManager.bleConnected(any())).thenReturn(Completable.complete())
        whenever(ngBleManager.bleConnectionLostError(any()))
            .thenReturn(deviceBleConnectionLostPusblisher.first())

        // connectWatch emits connectionLostError, because
        // ble connection is lost before connection is established.
        val subscriber = TestSubscriber<String>()
        whenever(ngBleManager.isBleOn).thenReturn(Single.just(false))
        connectWatch(NG_WATCH, watchStateHolder, false, subscriber, true)
        if (disconnectBLEBeforeConnect) {
            // Emit device connection lost.
            deviceBleConnectionLostPusblisher.onError(connectionLostError)
            subscriber.assertNoValues()
            subscriber.assertError(connectionLostError)
        } else {
            // onDeviceConnected is called, current watch is updated and connect completes
            connectedDevicePublisher.onNext(
                createConnectedDevice(
                    NG_WATCH_SERIAL,
                    NG_WATCH_FIRMWARE,
                    NG_WATCH_VARIANT,
                    true
                )
            )
            // Emit device connection lost.
            deviceBleConnectionLostPusblisher.onError(connectionLostError)
            subscriber.assertValueCount(1)
            subscriber.assertNoErrors()
        }
        if (disconnectBLEBeforeConnect) {
            assertThat(counter.count).isEqualTo(2)
        } else {
            assertThat(counter.count).isEqualTo(1)
        }

        // Ensure watch is correct watch state.
        val watchState = getStateChange()
        if (disconnectBLEBeforeConnect) {
            assertThat(watchState.connectionState).isEqualTo(WatchState.ConnectionState.DISCONNECTED)
        } else {
            assertThat(watchState.connectionState).isEqualTo(WatchState.ConnectionState.CONNECTED)
        }
        subscriber.assertTerminalEvent()
    }

    @Test
    fun connectNgWatchFailMdsHandShakeBleOff() {
        whenever(mdsRx.connectingDevicesObservable()).thenReturn(connectingDevicePublisher)
        doConnectNgWatchFailMdsHandShake(false)
    }

    @Test
    fun connectNgWatchFailMdsHandShakeBleOn() {
        whenever(mdsRx.connectingDevicesObservable()).thenReturn(connectingDevicePublisher)
        doConnectNgWatchFailMdsHandShake(true)
    }

    private fun doConnectNgWatchFailMdsHandShake(bleOn: Boolean) {
        val counter = SubscriptionCounter()
        val completable = Completable
            .complete()
            .doOnSubscribe(counter)
        whenever(ngBleManager.directConnect(any(), any(), any()))
            .thenReturn(completable)
        val deviceBleConnectionLostPusblisher = PublishSubject.create<String>()
        whenever(ngBleManager.bleConnected(any())).thenReturn(Completable.complete())
        whenever(ngBleManager.bleConnectionLostError(any()))
            .thenReturn(deviceBleConnectionLostPusblisher.first())
        whenever(ngBleManager.isBleOn).thenReturn(Single.just(bleOn))

        // connectWatch emits connectionLostError, because
        // MDS handshake fails
        val subscriber = TestSubscriber<String>()
        whenever(ngBleManager.isBleOn).thenReturn(Single.just(false))
        connectWatch(NG_WATCH, watchStateHolder, false, subscriber, bleOn)
        val aboutToConnect = gson.fromJson(
            MDS_CONNECTING_DEVICE_ABOUTTOCONNECT_JSON,
            MdsConnectingDevice::class.java
        )
        val failedToConnect = gson.fromJson(
            MDS_CONNECTING_DEVICE_FAILED_JSON,
            MdsConnectingDevice::class.java
        )

        // AboutToConnect should not cause errors.
        connectingDevicePublisher.onNext(aboutToConnect)
        subscriber.assertNoErrors()
        connectingDevicePublisher.onNext(failedToConnect)

        // AboutToConnect should not cause errors.
        connectingDevicePublisher.onNext(aboutToConnect)
        subscriber.assertNoErrors()
        connectingDevicePublisher.onNext(failedToConnect)
        subscriber.awaitTerminalEvent()
        subscriber.assertNoValues()
        subscriber.assertError(MdsFailedToConnectError::class.java)
        val errors = subscriber.onErrorEvents
        val throwable = errors.iterator().next()
        assertThat(throwable).isInstanceOf(MdsFailedToConnectError::class.java)
        assertThat(counter.count).isEqualTo(2)

        // Ensure watch is correct watch state.
        val watchState = getStateChange()
        assertThat(watchState.connectionState).isEqualTo(WatchState.ConnectionState.DISCONNECTED)
    }

    @Test
    fun connectNgWatchTimeoutMdsConnect() {
        whenever(mdsRx.connectingDevicesObservable()).thenReturn(connectingDevicePublisher)
        // Use test scheduler as computation scheduler in order to prevent timeout wait.
        val testScheduler = TestScheduler()
        val computationSchedulerFunc = RxJavaHooks
            .getOnComputationScheduler()
        RxJavaHooks.setOnComputationScheduler { testScheduler }
        val counter = SubscriptionCounter()
        val completable = Completable
            .complete()
            .doOnSubscribe(counter)
        whenever(ngBleManager.directConnect(any(), any(), any())).thenReturn(completable)
        whenever(ngBleManager.bleConnected(any())).thenReturn(
            Completable.complete()
        )
        whenever(ngBleManager.bleConnectionLostError(any())).thenReturn(rx.Observable.never())
        whenever(ngBleManager.isBleOn).thenReturn(Single.just(true))

        // connectWatch emits connectionLostError, because
        // MDS handshake fails
        val subscriber = TestSubscriber<String>()
        whenever(ngBleManager.isBleOn).thenReturn(Single.just(false))
        connectWatch(NG_WATCH, watchStateHolder, false, subscriber, true)
        val aboutToConnect = gson.fromJson(
            MDS_CONNECTING_DEVICE_ABOUTTOCONNECT_JSON,
            MdsConnectingDevice::class.java
        )

        // AboutToConnect should not cause errors.
        connectingDevicePublisher.onNext(aboutToConnect)
        subscriber.assertNoErrors()
        testScheduler.advanceTimeBy(
            WatchConnector.MDS_SHORT_CONNECTION_TIMEOUT_SECONDS.toLong(),
            TimeUnit.SECONDS
        )
        subscriber.assertNoErrors()
        connectingDevicePublisher.onNext(aboutToConnect)
        testScheduler.advanceTimeBy(
            WatchConnector.MDS_CONNECTION_TIMEOUT_SECONDS.toLong(),
            TimeUnit.SECONDS
        )
        subscriber.awaitTerminalEvent()
        subscriber.assertNoValues()
        subscriber.assertError(MdsConnectTimeoutError::class.java)
        // There is a retry on connect when mds fails to connect.
        assertThat(counter.count).isEqualTo(2)

        // Ensure watch is correct watch state.
        val watchState = getStateChange()
        assertThat(watchState.connectionState).isEqualTo(WatchState.ConnectionState.DISCONNECTED)
        // Set computation scheduler back to original.
        RxJavaHooks.setOnComputationScheduler(computationSchedulerFunc)
    }

    @Test
    fun connectNgWatchFailMDSHandShakeLocationIsOn() {
        whenever(mdsRx.connectingDevicesObservable()).thenReturn(connectingDevicePublisher)

        // Set location on.
        val counter = SubscriptionCounter()
        val completable = Completable
            .complete()
            .doOnSubscribe(counter)
        whenever(ngBleManager.directConnect(any(), any(), any())).thenReturn(completable)
        val deviceBleConnectionLostPublisher = PublishSubject.create<String>()
        whenever(ngBleManager.bleConnected(any())).thenReturn(Completable.complete())
        whenever(ngBleManager.bleConnectionLostError(any()))
            .thenReturn(deviceBleConnectionLostPublisher.first())
        whenever(ngBleManager.isBleOn).thenReturn(Single.just(true))

        // connectWatch emits connectionLostError, because
        // MDS handshake fails
        val subscriber = TestSubscriber<String>()
        whenever(ngBleManager.isBleOn).thenReturn(Single.just(false))
        connectWatch(NG_WATCH, watchStateHolder, true, subscriber, true)
        val failedToConnect = gson.fromJson(
            MDS_CONNECTING_DEVICE_FAILED_JSON,
            MdsConnectingDevice::class.java
        )
        connectingDevicePublisher.onNext(failedToConnect)
        subscriber.awaitTerminalEvent()
        subscriber.assertNoValues()
        subscriber.assertError(MdsFailedToConnectError::class.java)
        assertThat(counter.count).isEqualTo(1)

        // Ensure watch is correct watch state.
        val watchState = getStateChange()
        assertThat(watchState.connectionState).isEqualTo(WatchState.ConnectionState.DISCONNECTED)
    }

    @Test
    @Throws(JSONException::class)
    fun connectNgWatchFailBLEDuringConnect() {
        whenever(mdsRx.connectingDevicesObservable()).thenReturn(connectingDevicePublisher)
        connectNgWatchFailBLEDuringConnect(true)
        connectNgWatchFailBLEDuringConnect(false)
    }

    @Test
    @Throws(JSONException::class)
    fun deviceDetailsAfterConnect() {
        whenever(mdsRx.connectingDevicesObservable()).thenReturn(connectingDevicePublisher)
        val counter = SubscriptionCounter()
        val completable = Completable
            .complete()
            .doOnSubscribe(counter)
        whenever(ngBleManager.directConnect(any(), any(), any())).thenReturn(completable)
        val deviceBleConnectionLostPublisher = PublishSubject.create<String>()
        whenever(ngBleManager.bleConnected(any())).thenReturn(Completable.complete())
        whenever(ngBleManager.bleConnectionLostError(any()))
            .thenReturn(deviceBleConnectionLostPublisher.first())

        // Start connecting to NG watch
        val subscriber = TestSubscriber<String>()
        whenever(ngBleManager.isBleOn).thenReturn(Single.just(false))
        connectWatch(NG_WATCH, watchStateHolder, false, subscriber, true)
        subscriber.assertNoErrors()

        // onDeviceConnected is called, current watch is updated and connect completes
        connectedDevicePublisher.onNext(
            createConnectedDevice(
                NG_WATCH_SERIAL,
                NG_WATCH_FIRMWARE,
                NG_WATCH_VARIANT,
                true
            )
        )
        val deviceInfo = getStateChange().deviceInfo
        assertThat(deviceInfo).isNotNull()
        assertThat(deviceInfo!!.serial).isEqualTo(NG_WATCH_SERIAL)
        assertThat(deviceInfo.variant).isEqualTo(NG_WATCH_VARIANT)
        assertThat(deviceInfo.swVersion).isEqualTo(NG_WATCH_FIRMWARE)
    }

    @Test
    fun connectFailure() {
        whenever(mdsRx.connectingDevicesObservable()).thenReturn(connectingDevicePublisher)
        val counter = SubscriptionCounter()
        val completable = Completable
            .error(RuntimeException())
            .doOnSubscribe(counter)
        whenever(ngBleManager.bleConnected(any())).thenReturn(Completable.complete())
        whenever(ngBleManager.directConnect(any(), any(), any())).thenReturn(completable)

        // Start connecting to legacy watch
        val subscriber = TestSubscriber<String>()
        whenever(ngBleManager.isBleOn).thenReturn(Single.just(false))
        connectWatch(NG_WATCH, watchStateHolder, false, subscriber, true)

        // Connect Completable should be subscribed one time
        assertThat(counter.count).isEqualTo(1)
        val watchState = getStateChange()
        assertThat(watchState.connectionState).isEqualTo(WatchState.ConnectionState.DISCONNECTED)
        subscriber.assertError(Throwable::class.java)
    }

    private fun errorsCausingConnectRetryNg(): List<Exception> =
        listOf(
            GattConnectException(""),
            StartDataNotifyError(Exception()),
            BluetoothDiscoverException(""),
            BleConnectionLostError(),
            BluetoothWaitBondingException(BluetoothOperationWaitBonding.GATT_CLIENT_CONNECTION_LOST)
        )

    @Test
    fun directInitialConnectRetryFailTwiceTestsNg() {
        // Should retry.
        for (exception in errorsCausingConnectRetryNg()) {
            doDirectInitialConnectRetryTestNg(exception, true)
        }
        // No retry
        doDirectInitialConnectRetryTestNg(Exception("Some exception"), false)
        doDirectInitialConnectRetryTestNg(
            BluetoothWaitBondingException(BluetoothGatt.GATT_INSUFFICIENT_AUTHENTICATION),
            false
        )
    }

    @Test
    fun directInitialConnectRetryTestsSecondSucceededNg() {
        // Should retry.
        for (exception in errorsCausingConnectRetryNg()) {
            connectSuccessfullyAfterRetry(exception)
        }
    }

    @Test
    fun testNoRetryWhenPairingStartedButNeverPaired() {
        whenever(btStateMonitor.btEventObservable(any())).thenReturn(
            Observable.just(BtEvent.DEVICE_PAIRING)
        )
        // Should not retry.
        for (exception in errorsCausingConnectRetryNg()) {
            doDirectInitialConnectRetryTestNg(exception, false)
        }
    }

    private fun doDirectInitialConnectRetryTestNg(e: Exception?, retry: Boolean) {
        whenever(mdsRx.connectingDevicesObservable()).thenReturn(connectingDevicePublisher)
        val counter = SubscriptionCounter()
        val completable = Completable
            .error(e)
            .doOnSubscribe(counter)
        whenever(ngBleManager.bleConnected(any())).thenReturn(
            Completable.complete()
        )
        whenever(ngBleManager.directConnect(any(), any(), any())).thenReturn(completable)

        // Start connecting to legacy watch
        val subscriber = TestSubscriber<String>()
        whenever(ngBleManager.isBleOn).thenReturn(Single.just(false))
        connectWatch(NG_WATCH, watchStateHolder, false, subscriber, true)

        // Connect Completable should be subscribed 2 times.
        if (retry) {
            assertThat(counter.count).isEqualTo(2)
        } else {
            assertThat(counter.count).isEqualTo(1)
        }
        val watchState = getStateChange()
        assertThat(watchState.connectionState).isEqualTo(WatchState.ConnectionState.DISCONNECTED)
        subscriber.assertError(Throwable::class.java)
    }

    private fun directConnectResult(secondConnect: AtomicBoolean, e: Exception?): Completable {
        return if (secondConnect.get()) {
            Completable.complete()
        } else {
            secondConnect.set(true)
            Completable.error(e)
        }
    }

    private fun connectSuccessfullyAfterRetry(e: Exception?) {
        val secondConnect = AtomicBoolean(false)
        whenever(mdsRx.connectingDevicesObservable()).thenReturn(connectingDevicePublisher)
        val deviceBleConnectionLostPublisher = PublishSubject.create<String>()
        whenever(ngBleManager.bleConnectionLostError(any()))
            .thenReturn(deviceBleConnectionLostPublisher.first())
        whenever(ngBleManager.bleConnected(any())).thenReturn(Completable.complete())
        whenever(ngBleManager.directConnect(any(), any(), any()))
            .thenAnswer { directConnectResult(secondConnect, e) }

        // Start connecting to legacy watch
        val subscriber = TestSubscriber<String>()
        whenever(ngBleManager.isBleOn).thenReturn(Single.just(false))
        connectWatch(NG_WATCH, watchStateHolder, false, subscriber, true)
        try {
            connectedDevicePublisher.onNext(
                createConnectedDevice(
                    NG_WATCH_SERIAL,
                    NG_WATCH_FIRMWARE,
                    NG_WATCH_VARIANT,
                    true
                )
            )
        } catch (jsonException: JSONException) {
            jsonException.printStackTrace()
        }
        val watchState = getStateChange()
        assertThat(watchState.connectionState).isEqualTo(WatchState.ConnectionState.CONNECTED)
        subscriber.assertCompleted()
    }

    private fun <T> getSingleValue(subscriber: TestSubscriber<T>): T {
        val values = subscriber.onNextEvents
        assertThat(values.size).isEqualTo(1)
        subscriber.assertCompleted()
        subscriber.assertNoErrors()
        return values.first()
    }

    private fun getStateChange(): WatchState {
        val stateChanges = testSubscriber.onNextEvents
        testSubscriber.assertNoTerminalEvent()
        return stateChanges[stateChanges.size - 1]
    }

    private fun createConnectedDevice(
        serial: String,
        swVersion: String,
        variant: String,
        connected: Boolean
    ): MdsConnectedDevice {
        val deviceInfo = MdsDeviceInfo(
            swVersion = swVersion,
            productName = "",
            variant = variant,
            hwCompatibilityId = "",
            serial = serial,
            sw = "",
            hw = "",
            productVersion = null,
            manufacturerName = "",
            additionalVersionInfoExtension = null,
            description = "",
            capabilities = null
        )

        return MdsConnectedDevice(
            isConnected = connected,
            serial = serial,
            connection = null,
            deviceInfo = deviceInfo
        )
    }

    private class SubscriptionCounter : Action1<Subscription?> {
        var count = 0
            private set

        override fun call(subscription: Subscription?) {
            count++
        }
    }

    /**
     * Workqueue related helper functions
     */
    private fun connectWatch(
        suuntoBtDevice: SuuntoBtDevice,
        watchStateHolder: WatchStateHolder,
        reconnecting: Boolean,
        testSubscriber: TestSubscriber<String>,
        bleAfterConnect: Boolean
    ) {
        watchConnector.connectWatch(
            suuntoBtDevice,
            watchStateHolder,
            if (reconnecting) reconnectConnectMetadata else initialConnectMetadata
        )
            .subscribe(testSubscriber)
        waitWorkQueue(connectQueue)
    }

    internal inner class WorkQueueContainer(
        val workQueue: WorkQueue,
        val handlerDelayController: HandlerDelayController,
        val semaphore: Semaphore
    )

    // Create a queue with a semaphore for tracking when the queue has run the operation.
    private fun createWorkQueueContainer(): WorkQueueContainer {
        val runSemaphore = Semaphore(0)
        val handlerDelayController = HandlerDelayController()
        val workQueue: WorkQueue = WorkQueue(
            handler = handlerDelayController.handler,
            runAfterEachOperation = { runSemaphore.release() },
        )
        return WorkQueueContainer(workQueue, handlerDelayController, runSemaphore)
    }

    private fun waitWorkQueue(workQueue: WorkQueueContainer): Boolean {
        // Make sure that main thread gets to process posted messages.
        workQueue.handlerDelayController.runAll()
        val result = try {
            workQueue.semaphore.tryAcquire(1, TimeUnit.SECONDS)
        } catch (ex: InterruptedException) {
            false
        }
        // Make sure that main thread gets to process posted messages.
        workQueue.handlerDelayController.runAll()
        return result
    }

    companion object {
        private const val NG_WATCH_MAC = "AA:BB:CC:DD:EE:FF"
        private const val NG_WATCH_VARIANT = "Brighton"
        private const val NG_WATCH_SERIAL = "016311400010"
        private const val NG_WATCH_FIRMWARE = "1.1.24"
        private const val NG_WATCH_BLE_ADV_NAME = "Spartan Sport $NG_WATCH_SERIAL"

        private const val MDS_CONNECTING_DEVICE_FAILED_JSON = "{" +
            "\"Address\":\"\"," +
            "\"Serial\":\"\"," +
            "\"State\":\"FailedToConnect\"" +
            "}"

        private const val MDS_CONNECTING_DEVICE_ABOUTTOCONNECT_JSON = "{" +
            "\"Address\":\"\"," +
            "\"Serial\":\"\"," +
            "\"State\":\"AboutToConnect\"" +
            "}"

        private val NG_WATCH: SuuntoBtDevice = SuuntoBtDeviceImpl(
            NG_WATCH_BLE_ADV_NAME,
            NG_WATCH_SERIAL,
            NG_WATCH_MAC,
            SuuntoDeviceType.SpartanSport,
            true
        )
    }
}
