package com.stt.android.diveplanner.ui.divesummary

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import com.stt.android.diveplanner.data.DivePlansDataSource
import com.stt.android.diveplanner.utils.RouterScreen.DivePlanSummary.ARG_DIVE_PLAN_ID
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
class DivePlanSummaryViewModel @Inject constructor(
    savedStateHandle: SavedStateHandle,
    private val divePlansDataSource: DivePlansDataSource
) : ViewModel() {

    private val divePlanId: String? = savedStateHandle[ARG_DIVE_PLAN_ID]

    var divePlanUiState by mutableStateOf(
        divePlanId?.let { divePlansDataSource.findOrNull(it) }
    )
        private set

    fun updateTitle(title: String) =
        divePlanUiState?.let { divePlansDataSource.updateTitle(it, title) }

    fun updateDescription(description: String?) =
        divePlanUiState?.let { divePlansDataSource.updateDescription(it, description) }
}
