package com.stt.android.diveplanner.ui.common

import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.Text
import androidx.compose.material.TextField
import androidx.compose.runtime.Composable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType

@Composable
private fun <T : Number> DiveNumberPickerDialog(
    initialValue: T?,
    unit: String?,
    placeholder: String?,
    onValueChanged: (String) -> Unit,
    onDialogDismiss: () -> Unit,
    isValid: (String) -> Boolean
) {
    var value by rememberSaveable { mutableStateOf(initialValue?.toString() ?: "") }
    val isValidAsInt by remember(value) {
        derivedStateOf {
            isValid(value)
        }
    }
    DiveAlertDialog(
        onConfirmButtonClick = { onValueChanged(value) },
        isConfirmButtonEnabled = isValidAsInt,
        onDismissRequest = onDialogDismiss,
        onDismissButtonClicked = onDialogDismiss,
    ) {
        TextField(
            value = value,
            placeholder = placeholder?.let { { Text(text = placeholder) } },
            onValueChange = { value = it },
            trailingIcon = unit?.let { { Text(text = unit) } },
            keyboardOptions = KeyboardOptions.Default.copy(
                keyboardType = KeyboardType.Number,
                imeAction = ImeAction.Done
            ),
            keyboardActions = KeyboardActions(
                onDone = { if (isValidAsInt) onValueChanged(value) }
            ),
            singleLine = true,
            maxLines = 1,
            isError = value.isNotBlank() && !isValidAsInt
        )
    }
}

// This is used as a wrapper for any composable that needs to open a number picker dialog
@Composable
fun NumberPickerListItem(
    value: Int?,
    onValueChange: (Int) -> Unit,
    unit: String? = null,
    placeholder: String? = null,
    content: @Composable (openDialog: () -> Unit) -> Unit
) {
    var openDialog by rememberSaveable { mutableStateOf(false) }
    content {
        openDialog = true
    }
    if (openDialog) {
        DiveNumberPickerDialog(
            initialValue = value,
            unit = unit,
            placeholder = placeholder,
            onValueChanged = {
                onValueChange(it.toInt())
                openDialog = false
            },
            onDialogDismiss = {
                openDialog = false
            },
            isValid = {
                val intValue = it.toIntOrNull()
                if (intValue == null) {
                    false
                } else {
                    intValue > 0 // TODO: add min/max later
                }
            },
        )
    }
}

@Composable
fun FloatPickerListItem(
    value: Float?,
    onValueChange: (Float) -> Unit,
    unit: String? = null,
    placeholder: String? = null,
    content: @Composable (openDialog: () -> Unit) -> Unit
) {
    var openDialog by rememberSaveable { mutableStateOf(false) }
    content {
        openDialog = true
    }
    if (openDialog) {
        DiveNumberPickerDialog(
            initialValue = value,
            unit = unit,
            placeholder = placeholder,
            onValueChanged = {
                onValueChange(it.toFloat())
                openDialog = false
            },
            onDialogDismiss = {
                openDialog = false
            },
            isValid = {
                val floatValue = it.toFloatOrNull()
                if (floatValue == null) {
                    false
                } else {
                    floatValue > 0.0f // TODO: add min/max later
                }
            },
        )
    }
}
