package com.stt.android.domain.diveplanner

import com.stt.android.divingalgorithm.jnilib.DivingAlgorithmLib
import com.stt.android.domain.diveplanner.entities.DivePlan
import com.stt.android.domain.diveplanner.entities.DivePlanCNS
import com.stt.android.domain.diveplanner.entities.DivePlanDepth
import com.stt.android.domain.diveplanner.entities.DivePlanDuration
import com.stt.android.domain.diveplanner.entities.DivePlanGas
import com.stt.android.domain.diveplanner.entities.DivePlanGasPressure
import com.stt.android.domain.diveplanner.entities.DivePlanGasVolume
import com.stt.android.domain.diveplanner.entities.DivePlanLevel
import com.stt.android.domain.diveplanner.entities.DivePlanOTU
import com.stt.android.domain.diveplanner.entities.DivePlanOxygenToxicity
import com.stt.android.domain.diveplanner.entities.DivePlanToxicity
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.util.UUID
import javax.inject.Inject

class DivePlansUseCase
@Inject constructor(
    private val divingAlgorithmLib: DivingAlgorithmLib,
) {
    suspend fun createDivePlan(): String? = withContext(Dispatchers.IO) {
        try {
            divingAlgorithmLib.stringFromJNI() // TODO this is just for testing the JNI
        } catch (e: Exception) {
            Timber.w(e, "Failed to create a dive plan: ${e.message}")
            null
        }
    }

    suspend fun getDivePlans(): List<DivePlan> = withContext(Dispatchers.IO) {
        try {
            // TODO Dummy data for now
            val list = List(12) {
                DivePlan(
                    id = UUID.randomUUID().toString(),
                    title = "Plan ${it + 1}",
                    levels = listOf(
                        DivePlanLevel(
                            depth = DivePlanDepth(30.0 * it),
                            duration = DivePlanDuration(30.0 * it),
                        ),
                        DivePlanLevel(
                            depth = DivePlanDepth(50.0 * it),
                            duration = DivePlanDuration(20.0 * it),
                        )
                    ),
                    gases = listOf(
                        DivePlanGas(
                            type = "NX",
                            availableVolume = DivePlanGasVolume(20.0),
                            requiredVolume = DivePlanGasVolume(19.0),
                            startingPressure = DivePlanGasPressure(150.0),
                            remainingPressure = DivePlanGasPressure(160.0),
                        )
                    ),
                    oxygenToxicity = DivePlanOxygenToxicity(
                        startToxicity = DivePlanToxicity(
                            otu = DivePlanOTU(22),
                            cns = DivePlanCNS(15)
                        ),
                        endToxicity = DivePlanToxicity(otu = DivePlanOTU(22), cns = DivePlanCNS(15))
                    )
                )
            }
            list
        } catch (e: Exception) {
            Timber.w(e, "Failed to get dive plan templates: ${e.message}")
            listOf()
        }
    }
}
