package com.stt.android.suuntoplusstore.itemdetail

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.slideInHorizontally
import androidx.compose.animation.slideOutHorizontally
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Icon
import androidx.compose.material.IconButton
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Scaffold
import androidx.compose.material.ScaffoldState
import androidx.compose.material.rememberScaffoldState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import com.stt.android.compose.markdown.RunningGuideMarkdown
import com.stt.android.compose.modifiers.narrowContent
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.alertColorDarker
import com.stt.android.compose.theme.lightGrey
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.widgets.ExpandingFloatingActionButton
import com.stt.android.core.utils.EventThrottler
import com.stt.android.core.utils.onClick
import com.stt.android.device.domain.WatchAndNetworkNotificationState
import com.stt.android.device.domain.WatchAndNetworkNotificationStateComposable
import com.stt.android.device.suuntoplusdetails.note.SuuntoPlusDetailsNote
import com.stt.android.domain.workout.ActivityType
import com.stt.android.suuntoplus.SuuntoPlusItemLabel
import com.stt.android.suuntoplusstore.R
import com.stt.android.suuntoplusstore.features.SuuntoPlusStoreFeatureDetails
import com.stt.android.suuntoplusstore.guides.SuuntoPlusStoreGuideDetails
import com.stt.android.suuntoplusstore.ui.SuuntoPlusStoreFullScreenLoading
import com.stt.android.suuntoplusstore.ui.SuuntoPlusStoreTopBar
import kotlinx.collections.immutable.persistentListOf
import com.stt.android.R as BaseR
import com.stt.android.device.R as DeviceR

@Composable
fun SuuntoPlusStoreItemDetailScreen(
    title: String,
    showLoadingIndicator: Boolean,
    watchAndNetworkNotificationState: WatchAndNetworkNotificationState,
    enableLibraryActions: Boolean,
    enableFloatingButton: Boolean,
    showShareAction: Boolean,
    showAddToLibraryAction: Boolean,
    showRemoveFromLibraryAction: Boolean,
    addToWatchButtonText: String?,
    removeFromWatchButtonText: String?,
    showReportButton: Boolean,
    onAddToWatch: () -> Unit,
    onAddToLibrary: () -> Unit,
    onRemoveFromWatch: () -> Unit,
    onRemoveFromLibrary: () -> Unit,
    onReportClick: () -> Unit,
    onShareClick: () -> Unit,
    onUpPressed: () -> Unit,
    modifier: Modifier = Modifier,
    scaffoldState: ScaffoldState = rememberScaffoldState(),
    onCustomToastActionClick: () -> Unit = {},
    showFloatingButton: Boolean = true,
    content: @Composable ColumnScope.() -> Unit = {},
) {
    val eventThrottler = remember { EventThrottler() }

    Scaffold(
        scaffoldState = scaffoldState,
        topBar = {
            SuuntoPlusStoreTopBar(
                titleText = title,
                onUpPressed = onUpPressed,
                actions = {
                    if (showShareAction) {
                        IconButton(
                            enabled = !showLoadingIndicator,
                            onClick = eventThrottler.onClick(onShareClick)
                        ) {
                            Icon(
                                painter = painterResource(id = BaseR.drawable.share_outline),
                                contentDescription = stringResource(id = BaseR.string.share),
                                tint = if (!showLoadingIndicator) {
                                    MaterialTheme.colors.onSurface
                                } else {
                                    MaterialTheme.colors.lightGrey
                                }
                            )
                        }
                    }

                    if (showReportButton) {
                        IconButton(
                            enabled = enableLibraryActions,
                            onClick = onReportClick
                        ) {
                            Icon(
                                painter = painterResource(id = BaseR.drawable.waypoint_flag_outline),
                                contentDescription = stringResource(id = BaseR.string.report),
                                tint = if (enableLibraryActions) {
                                    MaterialTheme.colors.onSurface
                                } else {
                                    MaterialTheme.colors.lightGrey
                                }
                            )
                        }
                    }

                    if (showAddToLibraryAction) {
                        IconButton(
                            enabled = enableLibraryActions,
                            onClick = onAddToLibrary
                        ) {
                            Icon(
                                painter = painterResource(id = DeviceR.drawable.save_bookmark_outline),
                                contentDescription = stringResource(
                                    id = R.string.suunto_plus_store_item_detail_screen_add_feature_to_library_button_label
                                ),
                                tint = if (enableLibraryActions) {
                                    MaterialTheme.colors.primary
                                } else {
                                    MaterialTheme.colors.lightGrey
                                }
                            )
                        }
                    }

                    if (showRemoveFromLibraryAction) {
                        IconButton(
                            enabled = enableLibraryActions,
                            onClick = onRemoveFromLibrary
                        ) {
                            Icon(
                                painter = painterResource(id = DeviceR.drawable.save_bookmark_fill),
                                contentDescription = stringResource(
                                    id = BaseR.string.suunto_plus_remove_sports_app_button
                                ),
                                tint = if (enableLibraryActions) {
                                    MaterialTheme.colors.primary
                                } else {
                                    MaterialTheme.colors.lightGrey
                                }
                            )
                        }
                    }
                }
            )
        },
        modifier = modifier
    ) { internalPadding ->
        val scrollState = rememberScrollState()
        val expandButton = remember {
            derivedStateOf { scrollState.value == 0 }
        }

        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(internalPadding)
                .narrowContent()
                .background(MaterialTheme.colors.surface)
        ) {
            if (showLoadingIndicator) {
                SuuntoPlusStoreFullScreenLoading()
            } else {
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .verticalScroll(scrollState)
                ) {
                    content()
                }
            }

            if (showFloatingButton) {
                val showAddToWatchFloatingActionButton = addToWatchButtonText != null
                val showRemoveToWatchFloatingActionButton = removeFromWatchButtonText != null
                AnimatedVisibility(
                    visible = showAddToWatchFloatingActionButton || showRemoveToWatchFloatingActionButton,
                    enter = slideInHorizontally { it },
                    exit = slideOutHorizontally { it },
                    modifier = Modifier
                        .align(Alignment.BottomEnd)
                        .padding(
                            bottom = MaterialTheme.spacing.large,
                            end = MaterialTheme.spacing.medium
                        )
                ) {
                    ExpandingFloatingActionButton(
                        drawableResource = if (showAddToWatchFloatingActionButton) DeviceR.drawable.transfer_to_watch_outline else DeviceR.drawable.remove_from_watch_outline,
                        text = addToWatchButtonText ?: removeFromWatchButtonText ?: "",
                        expanded = expandButton.value,
                        enabled = enableFloatingButton,
                        contentColor = if (showAddToWatchFloatingActionButton) MaterialTheme.colors.onSurface else MaterialTheme.colors.alertColorDarker,
                        onClick = {
                            if (showAddToWatchFloatingActionButton) {
                                onAddToWatch()
                            } else {
                                onRemoveFromWatch()
                            }
                        }
                    )
                }
            }

            WatchAndNetworkNotificationStateComposable(
                state = watchAndNetworkNotificationState,
                modifier = Modifier
                    .align(Alignment.TopCenter)
                    .fillMaxWidth()
                    .padding(
                        top = MaterialTheme.spacing.medium,
                        start = MaterialTheme.spacing.large,
                        end = MaterialTheme.spacing.large,
                    ),
                onCustomToastActionClick = onCustomToastActionClick,
                watchSyncingMessage = stringResource(BaseR.string.sport_mode_watch_syncing),
                watchBusyMessage = stringResource(BaseR.string.suunto_plus_sync_watch_busy),
                noInternetConnectionMessage = stringResource(BaseR.string.no_internet_connection_error),
                watchDisconnectedMessage = stringResource(BaseR.string.sport_mode_watch_disconnected)
            )
        }

    }
}

@Preview
@Composable
private fun SuuntoPlusStoreItemFeatureDetailPreview() {
    AppTheme {
        SuuntoPlusStoreItemDetailScreen(
            title = "Burner",
            showLoadingIndicator = false,
            showReportButton = true,
            onAddToWatch = {},
            onAddToLibrary = {},
            onRemoveFromWatch = {},
            onRemoveFromLibrary = {},
            onReportClick = {},
            onUpPressed = {},
            watchAndNetworkNotificationState = WatchAndNetworkNotificationState.DEFAULT,
            enableLibraryActions = true,
            enableFloatingButton = true,
            showAddToLibraryAction = true,
            showRemoveFromLibraryAction = false,
            addToWatchButtonText = stringResource(id = DeviceR.string.suunto_plus_floating_action_button_install_on_watch),
            removeFromWatchButtonText = null,
            showShareAction = true,
            onShareClick = {},
        ) {
            SuuntoPlusStoreFeatureDetails(
                title = "Burner",
                description = "Long description",
                bannerImageUrl = "",
                richDescription = null,
                labels = persistentListOf(SuuntoPlusItemLabel("Sports app"), SuuntoPlusItemLabel("Lorem ipsum")),
                notes = persistentListOf(SuuntoPlusDetailsNote.MaxFeatureLimitReachedNote(false)),
                learnMoreUrl = "https://foo.bar/",
                onLearnMore = {},
                watchPreviewImageUrl = "https://suuntopluspluginsdev.blob.core.windows.net/feature-banners/zzbrnr01.jpg",
                onNoteAction = { action ->
                    throw UnsupportedOperationException("No note action $action supported")
                },
                onTermsClick = {},
                modifier = Modifier.fillMaxWidth(),
                autoTranslate = false,
                showTranslatedText = true,
                onClickCallback = null,
            )
        }
    }
}

@Preview
@Composable
private fun SuuntoPlusStoreItemGuideDetailPreview() {
    AppTheme {
        SuuntoPlusStoreItemDetailScreen(
            title = "Burner",
            showLoadingIndicator = false,
            showReportButton = false,
            onAddToWatch = {},
            onAddToLibrary = {},
            onRemoveFromWatch = {},
            onRemoveFromLibrary = {},
            onReportClick = {},
            onUpPressed = {},
            watchAndNetworkNotificationState = WatchAndNetworkNotificationState.DEFAULT,
            enableLibraryActions = true,
            enableFloatingButton = true,
            showAddToLibraryAction = false,
            showRemoveFromLibraryAction = true,
            addToWatchButtonText = stringResource(id = DeviceR.string.suunto_plus_floating_action_button_install_on_watch),
            removeFromWatchButtonText = null,
            showShareAction = false,
            onShareClick = {},
        ) {
            SuuntoPlusStoreGuideDetails(
                title = "Running Form drills",
                description = "Long description",
                richDescription = RunningGuideMarkdown,
                backgroundUrl = "https://suuntopluspluginsdev.blob.core.windows.net/guide-icons/57949619c7cd556bc347cd987b816217.png",
                labels = persistentListOf(
                    SuuntoPlusItemLabel("Sports app"),
                    SuuntoPlusItemLabel.forActivityType(activityType = ActivityType.TRAIL_RUNNING),
                    SuuntoPlusItemLabel("Lorem ipsum"),
                ),
                notes = persistentListOf(SuuntoPlusDetailsNote.MaxFeatureLimitReachedNote(false)),
                learnMoreUrl = null,
                onLearnMore = {},
                onNoteAction = { action ->
                    throw UnsupportedOperationException("No note action $action supported")
                },
                modifier = Modifier.fillMaxWidth(),
                workoutItem = null
            )
        }
    }
}

@Preview
@Composable
private fun SuuntoPlusStoreDetailsLoadingIndicatorPreview() {
    AppTheme {
        SuuntoPlusStoreItemDetailScreen(
            title = "Burner",
            showLoadingIndicator = true,
            showReportButton = true,
            onAddToWatch = {},
            onAddToLibrary = {},
            onRemoveFromWatch = {},
            onRemoveFromLibrary = {},
            onUpPressed = {},
            onReportClick = {},
            enableLibraryActions = false,
            enableFloatingButton = false,
            showAddToLibraryAction = false,
            showRemoveFromLibraryAction = false,
            addToWatchButtonText = null,
            removeFromWatchButtonText = null,
            watchAndNetworkNotificationState = WatchAndNetworkNotificationState.DEFAULT,
            showShareAction = false,
            onShareClick = {},
        ) {
            SuuntoPlusStoreGuideDetails(
                title = "",
                description = null,
                richDescription = null,
                backgroundUrl = null,
                labels = persistentListOf(),
                notes = persistentListOf(),
                learnMoreUrl = null,
                onLearnMore = {},
                onNoteAction = {},
                modifier = Modifier.fillMaxWidth(),
                workoutItem = null
            )
        }
    }
}
