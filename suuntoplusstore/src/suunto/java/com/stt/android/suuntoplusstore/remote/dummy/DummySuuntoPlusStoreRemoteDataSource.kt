package com.stt.android.suuntoplusstore.remote.dummy

import com.stt.android.device.remote.suuntoplusguide.SuuntoPlusStoreRemoteGuide
import com.stt.android.device.remote.suuntoplusguide.SuuntoPlusStoreRemoteItemCategory
import com.stt.android.suuntoplusstore.remote.SuuntoPlusStoreFeatureId
import com.stt.android.suuntoplusstore.remote.SuuntoPlusStoreGuideId
import com.stt.android.suuntoplusstore.remote.SuuntoPlusStoreRemoteCategory
import com.stt.android.suuntoplusstore.remote.SuuntoPlusStoreRemoteDataSource
import com.stt.android.suuntoplusstore.remote.SuuntoPlusStoreRemoteFeature
import com.stt.android.suuntoplusstore.remote.SuuntoPlusStoreRemoteFeatureContainer
import com.stt.android.suuntoplusstore.remote.SuuntoPlusStoreRemoteFeatureOwner
import com.stt.android.suuntoplusstore.remote.SuuntoPlusStoreRemoteGuideContainer
import com.stt.android.suuntoplusstore.remote.SuuntoPlusStoreRemoteHomeCategory
import com.stt.android.suuntoplusstore.remote.SuuntoPlusStoreRemoteHomeCategoryItemTypes
import com.stt.android.suuntoplusstore.remote.SuuntoPlusStoreRemoteHomeContainer
import com.stt.android.suuntoplusstore.remote.SuuntoPlusStoreRemoteItemReference
import com.stt.android.suuntoplusstore.remote.SuuntoPlusStoreRemoteMultiItemTypeCategory
import com.suunto.connectivity.capabilities.SuuntoWatchCapabilities
import com.stt.android.device.remote.suuntoplusguide.SuuntoPlusStoreRemoteItemType
import com.stt.android.device.remote.suuntoplusguide.SuuntoPlusStoreRemoteWatchCompatibility
import javax.inject.Inject

@Suppress("SpellCheckingInspection")
class DummySuuntoPlusStoreRemoteDataSource @Inject constructor() : SuuntoPlusStoreRemoteDataSource {
    override suspend fun fetchHome(variantName: String, watchCapabilities: String): SuuntoPlusStoreRemoteHomeContainer {
        return SuuntoPlusStoreRemoteHomeContainer(
            categories = listOf(
                SuuntoPlusStoreRemoteHomeCategory(
                    id = categoryId,
                    title = categoryName,
                    itemTypes = itemTypes
                )
            ),
            items = features.values + guides.values
        )
    }

    override suspend fun searchHome(keyword: String, variantName: String, watchCapabilities: String): SuuntoPlusStoreRemoteHomeContainer {
        return SuuntoPlusStoreRemoteHomeContainer(
            categories = listOf(
                SuuntoPlusStoreRemoteHomeCategory(
                    id = categoryId,
                    title = categoryName,
                    itemTypes = itemTypes
                )
            ),
            items = features.values + guides.values
        )
    }

    override suspend fun fetchFeatures(watchCapabilities: String): SuuntoPlusStoreRemoteFeatureContainer {
        return SuuntoPlusStoreRemoteFeatureContainer(
            categories = listOf(
                SuuntoPlusStoreRemoteCategory(
                    id = categoryId,
                    title = categoryName,
                    itemRefs = itemTypes.first {
                        it.type == SuuntoPlusStoreRemoteItemType.FEATURE.jsonName
                    }.itemRefs,
                )
            ),
            items = features.values.toList()
        )
    }

    override suspend fun fetchFeature(
        featureId: SuuntoPlusStoreFeatureId,
        watchCapabilities: SuuntoWatchCapabilities,
        variantName: String
    ): SuuntoPlusStoreRemoteFeature {
        return features[featureId.id]!!
    }

    override suspend fun addFeatureToLibrary(
        featureId: SuuntoPlusStoreFeatureId,
        addToWatch: Boolean
    ) {
        /* no-op */
    }

    override suspend fun removeFeatureFromLibrary(featureId: SuuntoPlusStoreFeatureId) {
        /* no-op */
    }

    override suspend fun fetchGuides(watchCapabilities: SuuntoWatchCapabilities): SuuntoPlusStoreRemoteGuideContainer {
        return SuuntoPlusStoreRemoteGuideContainer(
            categories = listOf(
                SuuntoPlusStoreRemoteCategory(
                    id = categoryId,
                    title = categoryName,
                    itemRefs = itemTypes.first {
                        it.type == SuuntoPlusStoreRemoteItemType.GUIDE.jsonName
                    }.itemRefs,
                )
            ),
            items = guides.values.toList()
        )
    }

    override suspend fun fetchGuide(
        guideId: SuuntoPlusStoreGuideId,
        watchCapabilities: SuuntoWatchCapabilities
    ): SuuntoPlusStoreRemoteGuide {
        return guides[guideId.id]!!
    }

    override suspend fun addGuideToWatch(guideId: SuuntoPlusStoreGuideId) {
        /* no-op */
    }

    override suspend fun removeGuideFromLibrary(guideId: SuuntoPlusStoreGuideId) {
        /* no-op */
    }

    override suspend fun fetchCategory(id: String, variantName: String, watchCapabilities: String): SuuntoPlusStoreRemoteMultiItemTypeCategory {
        return SuuntoPlusStoreRemoteMultiItemTypeCategory(
            id = categoryId,
            title = categoryName,
            description =
            "Keep track on surrounding environment and weather while enjoying outdoors.",
            bannerImageUrl = "https://s3-alpha-sig.figma.com/img/cbe7/65ba/39c361bacabc0ab16f671ea1996e8eb2?Expires=1660521600&Signature=MSacWxe91LnpQ0veqPp73YsKrxf3j-gxht1uqC1DWLnhE2NmEmSOYBl-GTyReqSchzS1JK03XR-PbZ0ExF57jFPief0Xz1VONfbojX3x2LD303BFiS7CZ6AhHNjbwKz8yVzg9tIXYw-OzDje0e78lcX4Cq1P2iGhM0SUOJpdN5w-jVoZq68ScF2zb0HN-kHRw-2tALCLFgtdDfTxtdYe5o1ffhoAaguolnwKCkC2cjVGwgne7hpQc8MLBNzOuC2K-NBQuD8yVt~DeqLgWKnMQZdocLSEcIHwBV2LX2Phon0P9e0dcXtobhGH0DopQ4x3W8Ew3488JHKoluc3xo3oRA__&Key-Pair-Id=APKAINTVSUGEWH5XD5UA",
            itemTypes = itemTypes,
            items = features.values + guides.values,
        )
    }

    override suspend fun fetchGuideJsonZip(guideId: SuuntoPlusStoreGuideId): ByteArray {
        return ByteArray(0)
    }

    companion object {
        private val features = mapOf(
            "zzbrnr01" to SuuntoPlusStoreRemoteFeature(
                type = "sportsapp",
                title = "Burner",
                subTitle = "Fat & carbs burned",
                id = "zzbrnr01",
                description = """The relation between fat and carbs burned by exercising depends 
                    |on your intensity. The Burner shows the percent of energy burned from fat 
                    |and the current energy consumption converted into grams of fat and 
                    |carbohydrates that you consume per hour.
                """.trimMargin(),
                richDescription = null,
                labels = listOf("NEW"),
                tileBannerUrl = "https://suuntopluspluginsdev.blob.core.windows.net/feature-banners/default.png",
                iconUrl = "https://suuntopluspluginsdev.blob.core.windows.net/feature-icons/zzbrnr01.png",
                owner = SuuntoPlusStoreRemoteFeatureOwner(
                    title = "Suunto",
                    logoUrl = "https://aspartnercontent.blob.core.windows.net/partner-content/suunto/sports-app-owner-logo.png"
                ),
                categories = listOf(
                    SuuntoPlusStoreRemoteItemCategory(
                        id = "training-physiology",
                        title = "Training Physiology"
                    )
                ),
                libraryStatus = "NOT_ADDED",
                watchCompatibility = SuuntoPlusStoreRemoteWatchCompatibility(
                    compatible = true,
                    incompatibilityNote = null,
                ),
                addToWatch = null,
                localizedRichText = "",
                localizedRichTextAutomatically = false
            ),
            "zzclmb01" to SuuntoPlusStoreRemoteFeature(
                type = "sportsapp",
                title = "Climb",
                subTitle = "Hill Training",
                id = "zzclmb01",
                description = """Keep track of your effort while hiking up a mountain, rock 
                    |climbing, doing hill repeats in your backyard or running up stairs. Climbs 
                    |can be viewed afterwards in Suunto app.
                """.trimMargin(),
                richDescription = null,
                labels = emptyList(),
                tileBannerUrl = "https://suuntopluspluginsdev.blob.core.windows.net/feature-banners/zzclmb01.png",
                iconUrl = "https://suuntopluspluginsdev.blob.core.windows.net/feature-icons/zzclmb01.png",
                owner = SuuntoPlusStoreRemoteFeatureOwner(
                    title = "Suunto",
                    logoUrl = "https://aspartnercontent.blob.core.windows.net/partner-content/suunto/sports-app-owner-logo.png"
                ),
                categories = listOf(
                    SuuntoPlusStoreRemoteItemCategory(
                        id = "training-physiology",
                        title = "Training Physiology"
                    )
                ),
                libraryStatus = "NOT_ADDED",
                watchCompatibility = SuuntoPlusStoreRemoteWatchCompatibility(
                    compatible = true,
                    incompatibilityNote = null,
                ),
                addToWatch = null,
                localizedRichText = "",
                localizedRichTextAutomatically = false
            ),
            "zzghst01" to SuuntoPlusStoreRemoteFeature(
                type = "sportsapp",
                title = "Ghost runner",
                subTitle = "Running pacer",
                id = "zzghst01",
                description =
                "Catch the virtual ghost runner to practice your pacing or just for fun.",
                richDescription = null,
                labels = emptyList(),
                tileBannerUrl = "https://suuntopluspluginsdev.blob.core.windows.net/feature-banners/zzghst01.png",
                iconUrl = "https://suuntopluspluginsdev.blob.core.windows.net/feature-icons/zzghst01.png",
                owner = SuuntoPlusStoreRemoteFeatureOwner(
                    title = "Suunto",
                    logoUrl = "https://aspartnercontent.blob.core.windows.net/partner-content/suunto/sports-app-owner-logo.png"
                ),
                categories = listOf(
                    SuuntoPlusStoreRemoteItemCategory(
                        id = "training-physiology",
                        title = "Training Physiology"
                    )
                ),
                libraryStatus = "ADDED",
                watchCompatibility = SuuntoPlusStoreRemoteWatchCompatibility(
                    compatible = true,
                    incompatibilityNote = null,
                ),
                addToWatch = null,
                localizedRichText = "",
                localizedRichTextAutomatically = false
            ),
            "zzloop01" to SuuntoPlusStoreRemoteFeature(
                type = "sportsapp",
                title = "Loop",
                subTitle = "Lap Timer (Pace)",
                id = "zzloop01",
                description = """Automatic location-based lap insights help you analyze the stats
                    | for each loop. Excellent feature when running on a track. Get the stats 
                    | from each loop automatically to see how you were able to pace during the 
                    | exercise afterwards in Suunto app.
                """.trimMargin(),
                richDescription = null,
                labels = emptyList(),
                tileBannerUrl = "https://suuntopluspluginsdev.blob.core.windows.net/feature-banners/default.png",
                iconUrl = "https://suuntopluspluginsdev.blob.core.windows.net/feature-icons/zzloop01.png",
                owner = SuuntoPlusStoreRemoteFeatureOwner(
                    title = "Suunto",
                    logoUrl = "https://aspartnercontent.blob.core.windows.net/partner-content/suunto/sports-app-owner-logo.png"
                ),
                categories = listOf(
                    SuuntoPlusStoreRemoteItemCategory(
                        id = "training-physiology",
                        title = "Training Physiology"
                    )
                ),
                libraryStatus = "ADDED",
                watchCompatibility = SuuntoPlusStoreRemoteWatchCompatibility(
                    compatible = true,
                    incompatibilityNote = null,
                ),
                addToWatch = null,
                localizedRichText = "",
                localizedRichTextAutomatically = false
            ),
            "zzlopo01" to SuuntoPlusStoreRemoteFeature(
                type = "sportsapp",
                title = "Loop",
                subTitle = "Lap Timer (Power)",
                id = "zzlopo01",
                description = """Automatic location-based lap insights help you analyze the stats
                    | for each loop. Excellent feature when racing on a cyclocross or XC-MTB race
                    |  course in a closed loop. Get the stats from each loop automatically to see
                    |   how you were able to pace during the race afterwards in Suunto app."""
                    .trimMargin(),
                richDescription = null,
                labels = emptyList(),
                tileBannerUrl = "https://suuntopluspluginsdev.blob.core.windows.net/feature-banners/default.png",
                iconUrl = "https://suuntopluspluginsdev.blob.core.windows.net/feature-icons/zzlopo01.png",
                owner = SuuntoPlusStoreRemoteFeatureOwner(
                    title = "Suunto",
                    logoUrl = "https://aspartnercontent.blob.core.windows.net/partner-content/suunto/sports-app-owner-logo.png"
                ),
                categories = listOf(
                    SuuntoPlusStoreRemoteItemCategory(
                        id = "training-physiology",
                        title = "Training Physiology"
                    )
                ),
                libraryStatus = "ADDED",
                watchCompatibility = SuuntoPlusStoreRemoteWatchCompatibility(
                    compatible = true,
                    incompatibilityNote = null,
                ),
                addToWatch = null,
                localizedRichText = "",
                localizedRichTextAutomatically = false
            ),
            "zzlosp01" to SuuntoPlusStoreRemoteFeature(
                type = "sportsapp",
                title = "Loop",
                subTitle = "Lap Timer (Speed)",
                id = "zzlosp01",
                description = """Automatic location-based lap insights help you analyze the stats
                    | for each loop. Excellent feature when cross-country skiing the same loop. 
                    | Get the stats from each loop automatically to see how you were able to pace
                    |  during the exercise afterwards in Suunto app.
                """.trimMargin(),
                richDescription = null,
                labels = emptyList(),
                tileBannerUrl = "https://suuntopluspluginsdev.blob.core.windows.net/feature-banners/default.png",
                iconUrl = "https://suuntopluspluginsdev.blob.core.windows.net/feature-icons/zzlosp01.png",
                owner = SuuntoPlusStoreRemoteFeatureOwner(
                    title = "Suunto",
                    logoUrl = "https://aspartnercontent.blob.core.windows.net/partner-content/suunto/sports-app-owner-logo.png"
                ),
                categories = listOf(
                    SuuntoPlusStoreRemoteItemCategory(
                        id = "training-physiology",
                        title = "Training Physiology"
                    )
                ),
                libraryStatus = "ADDED",
                watchCompatibility = SuuntoPlusStoreRemoteWatchCompatibility(
                    compatible = true,
                    incompatibilityNote = null,
                ),
                addToWatch = null,
                localizedRichText = "",
                localizedRichTextAutomatically = false
            ),
            "zzredb01" to SuuntoPlusStoreRemoteFeature(
                type = "sportsapp",
                title = "Red Bull",
                subTitle = "X-Alps",
                id = "zzredb01",
                description = """The Red Bull X-Alps SuuntoPlus™ feature is a handy tool to keep 
                    |track of your vertical speed. It helps the hike and fly community to see, 
                    |feel and hear how fast they are climbing.
                """.trimMargin(),
                richDescription = null,
                labels = emptyList(),
                tileBannerUrl = "https://suuntopluspluginsdev.blob.core.windows.net/feature-banners/default.png",
                iconUrl = "https://suuntopluspluginsdev.blob.core.windows.net/feature-icons/zzredb01.png",
                owner = SuuntoPlusStoreRemoteFeatureOwner(
                    title = "Red Bull",
                    logoUrl = "https://aspartnercontent.blob.core.windows.net/partner-content/redbull/sports-app-owner-logo.png"
                ),
                categories = listOf(
                    SuuntoPlusStoreRemoteItemCategory(
                        id = "training-physiology",
                        title = "Training Physiology"
                    )
                ),
                libraryStatus = "ADDED",
                watchCompatibility = SuuntoPlusStoreRemoteWatchCompatibility(
                    compatible = true,
                    incompatibilityNote = null,
                ),
                addToWatch = null,
                localizedRichText = "",
                localizedRichTextAutomatically = false
            ),
            "zzvari01" to SuuntoPlusStoreRemoteFeature(
                type = "sportsapp",
                title = "Variometer",
                subTitle = "Hike & Fly",
                id = "zzvari01",
                description = """The Variometer feature is a handy tool to keep track of your 
                    |vertical speed. It helps the hike and fly community to see, feel and hear 
                    |how fast they are climbing.
                """.trimMargin(),
                richDescription = null,
                labels = emptyList(),
                tileBannerUrl = "https://suuntopluspluginsdev.blob.core.windows.net/feature-banners/default.png",
                iconUrl = "https://suuntopluspluginsdev.blob.core.windows.net/feature-icons/zzvari01.png",
                owner = SuuntoPlusStoreRemoteFeatureOwner(
                    title = "Suunto",
                    logoUrl = "https://aspartnercontent.blob.core.windows.net/partner-content/suunto/sports-app-owner-logo.png"
                ),
                categories = listOf(
                    SuuntoPlusStoreRemoteItemCategory(
                        id = "training-physiology",
                        title = "Training Physiology"
                    )
                ),
                libraryStatus = "ADDED",
                watchCompatibility = SuuntoPlusStoreRemoteWatchCompatibility(
                    compatible = true,
                    incompatibilityNote = null,
                ),
                addToWatch = null,
                localizedRichText = "",
                localizedRichTextAutomatically = false
            ),
            "zzsafe01" to SuuntoPlusStoreRemoteFeature(
                type = "sportsapp",
                title = "Safe",
                subTitle = "My Location",
                id = "zzsafe01",
                description = """Quick access to your GPS coordinates and other key information 
                    |of your current location as well as your starting point. Useful when sharing
                    | your location with a friend, when you need to radio for help or navigating 
                    | back to your car.
                """.trimMargin(),
                richDescription = null,
                labels = emptyList(),
                tileBannerUrl = "https://suuntopluspluginsdev.blob.core.windows.net/feature-banners/zzsafe01.png",
                iconUrl = "https://suuntopluspluginsdev.blob.core.windows.net/feature-icons/zzsafe01.png",
                owner = SuuntoPlusStoreRemoteFeatureOwner(
                    title = "Suunto",
                    logoUrl = "https://aspartnercontent.blob.core.windows.net/partner-content/suunto/sports-app-owner-logo.png"
                ),
                categories = listOf(
                    SuuntoPlusStoreRemoteItemCategory(
                        id = "training-physiology",
                        title = "Training Physiology"
                    )
                ),
                libraryStatus = "ADDED",
                watchCompatibility = SuuntoPlusStoreRemoteWatchCompatibility(
                    compatible = true,
                    incompatibilityNote = null,
                ),
                addToWatch = null,
                localizedRichText = "",
                localizedRichTextAutomatically = false
            ),
            "zzspri01" to SuuntoPlusStoreRemoteFeature(
                type = "sportsapp",
                title = "Sprint",
                subTitle = "Running Pace",
                id = "zzspri01",
                description = """Track sprints automatically while running to see the number, 
                    |distance, and time of each interval during your training session. Sprints 
                    |are identified based on the changes of your running pace. Sprints can be 
                    |viewed afterwards in Suunto app.
                """.trimMargin(),
                richDescription = null,
                labels = emptyList(),
                tileBannerUrl = "https://suuntopluspluginsdev.blob.core.windows.net/feature-banners/default.png",
                iconUrl = "https://suuntopluspluginsdev.blob.core.windows.net/feature-icons/zzspri01.png",
                owner = SuuntoPlusStoreRemoteFeatureOwner(
                    title = "Suunto",
                    logoUrl = "https://aspartnercontent.blob.core.windows.net/partner-content/suunto/sports-app-owner-logo.png"
                ),
                categories = listOf(
                    SuuntoPlusStoreRemoteItemCategory(
                        id = "training-physiology",
                        title = "Training Physiology"
                    )
                ),
                libraryStatus = "ADDED",
                watchCompatibility = SuuntoPlusStoreRemoteWatchCompatibility(
                    compatible = true,
                    incompatibilityNote = null,
                ),
                addToWatch = null,
                localizedRichText = "",
                localizedRichTextAutomatically = false
            ),
            "zzsprp01" to SuuntoPlusStoreRemoteFeature(
                type = "sportsapp",
                title = "Sprint",
                subTitle = "Cycling Power",
                id = "zzsprp01",
                description = "Track sprints automatically while cycling to see the number, " +
                    "distance, and time of each interval during your training session. Sprints " +
                    "are identified based on the changes of your cycling power. Sprints can be viewed afterwards in Suunto app.",
                richDescription = null,
                labels = emptyList(),
                tileBannerUrl = "https://suuntopluspluginsdev.blob.core.windows.net/feature-banners/default.png",
                iconUrl = "https://suuntopluspluginsdev.blob.core.windows.net/feature-icons/zzsprp01.png",
                owner = SuuntoPlusStoreRemoteFeatureOwner(
                    title = "Suunto",
                    logoUrl = "https://aspartnercontent.blob.core.windows.net/partner-content/suunto/sports-app-owner-logo.png"
                ),
                categories = listOf(
                    SuuntoPlusStoreRemoteItemCategory(
                        id = "training-physiology",
                        title = "Training Physiology"
                    )
                ),
                libraryStatus = "ADDED",
                watchCompatibility = SuuntoPlusStoreRemoteWatchCompatibility(
                    compatible = true,
                    incompatibilityNote = null,
                ),
                addToWatch = null,
                localizedRichText = "",
                localizedRichTextAutomatically = false
            ),
            "zzstra01" to SuuntoPlusStoreRemoteFeature(
                type = "sportsapp",
                title = "Strava",
                subTitle = "Relative Effort",
                id = "zzstra01",
                description = """Get specific about how much work goes into your activities with 
                    |real-time Relative Effort. Whether your activity is slow and steady or short
                    | and strenuous, Relative Effort gives you a personalized measurement based 
                    | on your heart rate zones, so you can adapt your effort on the go. Once 
                    | you're done, sync your activity to Strava and get more of your workout with
                    |  Strava Summit: deep dive into performance data, see how much progress 
                    |  you’ve made overall and where you are in your training cycle – whether 
                    |  you’re peaking, maintaining or recovering.
                """.trimMargin(),
                richDescription = null,
                labels = emptyList(),
                tileBannerUrl = "https://suuntopluspluginsdev.blob.core.windows.net/feature-banners/default.png",
                iconUrl = "https://suuntopluspluginsdev.blob.core.windows.net/feature-icons/zzstra01.png",
                owner = SuuntoPlusStoreRemoteFeatureOwner(
                    title = "Suunto",
                    logoUrl = "https://aspartnercontent.blob.core.windows.net/partner-content/suunto/sports-app-owner-logo.png"
                ),
                categories = listOf(
                    SuuntoPlusStoreRemoteItemCategory(
                        id = "training-physiology",
                        title = "Training Physiology"
                    )
                ),
                libraryStatus = "ADDED",
                watchCompatibility = SuuntoPlusStoreRemoteWatchCompatibility(
                    compatible = true,
                    incompatibilityNote = null,
                ),
                addToWatch = null,
                localizedRichText = "",
                localizedRichTextAutomatically = false
            ),
            "zztrph01" to SuuntoPlusStoreRemoteFeature(
                type = "sportsapp",
                title = "TrainingPeaks",
                subTitle = "Heart Rate",
                id = "zztrph01",
                description = """Helps to understand the workout effort and training stress in 
                    |real time with Heart Rate Training Stress Score® (TSS (hr)).
                """.trimMargin(),
                richDescription = null,
                labels =
                emptyList(),
                tileBannerUrl = "https://suuntopluspluginsdev.blob.core.windows.net/feature-banners/default.png",
                iconUrl = "https://suuntopluspluginsdev.blob.core.windows.net/feature-icons/zztrph01.png",
                owner = SuuntoPlusStoreRemoteFeatureOwner(
                    title = "Suunto",
                    logoUrl = "https://aspartnercontent.blob.core.windows.net/partner-content/suunto/sports-app-owner-logo.png"
                ),
                categories = listOf(
                    SuuntoPlusStoreRemoteItemCategory(
                        id = "training-physiology",
                        title = "Training Physiology"
                    )
                ),
                libraryStatus = "ADDED",
                watchCompatibility = SuuntoPlusStoreRemoteWatchCompatibility(
                    compatible = true,
                    incompatibilityNote = null,
                ),
                addToWatch = null,
                localizedRichText = "",
                localizedRichTextAutomatically = false
            ),
            "zztrpp01" to SuuntoPlusStoreRemoteFeature(
                type = "sportsapp",
                title = "TrainingPeaks",
                subTitle = "Cycling Power",
                id = "zztrpp01",
                description = """Helps to understand your riding effort in real time with 
                    |Normalized Power® (NP), Training Stress Score® (TSS) and Intensity Factor® 
                    |(IF).
                """.trimMargin(),
                richDescription = null,
                labels = emptyList(),
                tileBannerUrl = "https://suuntopluspluginsdev.blob.core.windows.net/feature-banners/default.png",
                iconUrl = "https://suuntopluspluginsdev.blob.core.windows.net/feature-icons/zztrpp01.png",
                owner = SuuntoPlusStoreRemoteFeatureOwner(
                    title = "Suunto",
                    logoUrl = "https://aspartnercontent.blob.core.windows.net/partner-content/suunto/sports-app-owner-logo.png"
                ),
                categories = listOf(
                    SuuntoPlusStoreRemoteItemCategory(
                        id = "training-physiology",
                        title = "Training Physiology"
                    )
                ),
                libraryStatus = "ADDED",
                watchCompatibility = SuuntoPlusStoreRemoteWatchCompatibility(
                    compatible = true,
                    incompatibilityNote = null,
                ),
                addToWatch = null,
                localizedRichText = "",
                localizedRichTextAutomatically = false
            ),
            "zztrpr01" to SuuntoPlusStoreRemoteFeature(
                type = "sportsapp",
                title = "TrainingPeaks",
                subTitle = "Running Pace",
                id = "zztrpr01",
                description = """Helps to understand your pacing in real time while running with 
                    | Intensity Factor® (IF), Running Training Stress Score® (TSS (r)) and 
                    |Normalized Graded Pace™ (NGP).
                """.trimMargin(),
                richDescription = null,
                labels = emptyList(),
                tileBannerUrl = "https://suuntopluspluginsdev.blob.core.windows.net/feature-banners/default.png",
                iconUrl = "https://suuntopluspluginsdev.blob.core.windows.net/feature-icons/zztrpr01.png",
                owner = SuuntoPlusStoreRemoteFeatureOwner(
                    title = "Suunto",
                    logoUrl = "https://aspartnercontent.blob.core.windows.net/partner-content/suunto/sports-app-owner-logo.png"
                ),
                categories = listOf(
                    SuuntoPlusStoreRemoteItemCategory(
                        id = "training-physiology",
                        title = "Training Physiology"
                    )
                ),
                libraryStatus = "ADDED",
                watchCompatibility = SuuntoPlusStoreRemoteWatchCompatibility(
                    compatible = true,
                    incompatibilityNote = null,
                ),
                addToWatch = null,
                localizedRichText = "",
                localizedRichTextAutomatically = false
            ),
            "zzweth01" to SuuntoPlusStoreRemoteFeature(
                type = "sportsapp",
                title = "Weather",
                subTitle = "Insights",
                id = "zzweth01",
                description = """Follow context-based weather insights and warnings during your 
                    |outdoor activity. Keep alert with storm alarms, measure water temperature or
                    | notice if your hike is going past the sunset.
                """.trimMargin(),
                richDescription = null,
                labels = emptyList(),
                tileBannerUrl = "https://suuntopluspluginsdev.blob.core.windows.net/feature-banners/default.png",
                iconUrl = "https://suuntopluspluginsdev.blob.core.windows.net/feature-icons/zzweth01.png",
                owner = SuuntoPlusStoreRemoteFeatureOwner(
                    title = "Suunto",
                    logoUrl = "https://aspartnercontent.blob.core.windows.net/partner-content/suunto/sports-app-owner-logo.png"
                ),
                categories = listOf(
                    SuuntoPlusStoreRemoteItemCategory(
                        id = "training-physiology",
                        title = "Training Physiology"
                    )
                ),
                libraryStatus = "ADDED",
                watchCompatibility = SuuntoPlusStoreRemoteWatchCompatibility(
                    compatible = true,
                    incompatibilityNote = null,
                ),
                addToWatch = null,
                localizedRichText = "",
                localizedRichTextAutomatically = false
            ),
        )

        private val guides = mapOf(
            "escrmo10" to SuuntoPlusStoreRemoteGuide(
                type = "guide",
                title = "3x10min有氧跑",
                subTitle = null,
                id = "escrmo10",
                description = "有氧段落跑，不要跑到让身体感觉到压力和疲劳，尽量多的用鼻子呼吸。",
                richDescription = null,
                labels = listOf("NEW"),
                iconUrl = "https://suuntopluspluginsdev.blob.core.windows.net/guide-icons/ebe14ce2ec6987a53021cdb6bf16bed7.png",
                backgroundUrl = "https://suuntopluspluginsdev.blob.core.windows.net/guide-icons/ebe14ce2ec6987a53021cdb6bf16bed7.png",
                activities = listOf(1, 10, 22, 21, 42),
                libraryStatus = "ADDED",
                categories = listOf(
                    SuuntoPlusStoreRemoteItemCategory(
                        id = "training-physiology",
                        title = "Training Physiology"
                    )
                ),
                watchCompatibility = SuuntoPlusStoreRemoteWatchCompatibility(
                    compatible = true,
                    incompatibilityNote = null,
                ),
            ),
            "vrgj83me" to SuuntoPlusStoreRemoteGuide(
                type = "guide",
                title = "40min有氧轻松跑",
                subTitle = null,
                id = "vrgj83me",
                description = "有氧轻松跑，轻松体感为第一训练要素。",
                richDescription = null,
                labels = listOf("NEW"),
                iconUrl = "https://suuntopluspluginsdev.blob.core.windows.net/guide-icons/ebe14ce2ec6987a53021cdb6bf16bed7.png",
                backgroundUrl = "https://suuntopluspluginsdev.blob.core.windows.net/guide-icons/ebe14ce2ec6987a53021cdb6bf16bed7.png",
                activities = emptyList(),
                libraryStatus = "ADDED",
                categories = listOf(
                    SuuntoPlusStoreRemoteItemCategory(
                        id = "training-physiology",
                        title = "Training Physiology"
                    )
                ),
                watchCompatibility = SuuntoPlusStoreRemoteWatchCompatibility(
                    compatible = true,
                    incompatibilityNote = null,
                ),
            ),
            "b3r7p52q" to SuuntoPlusStoreRemoteGuide(
                type = "guide",
                title = "12 Strength Exercises",
                subTitle = "12 strength exercises",
                id = "b3r7p52q",
                description = "These 12 exercises will boost your vertical power and endurance!",
                richDescription = null,
                labels = listOf("NEW"),
                iconUrl = "https://suuntopluspluginsdev.blob.core.windows.net/guide-icons/57949619c7cd556bc347cd987b816217.png",
                backgroundUrl = "https://suuntopluspluginsdev.blob.core.windows.net/guide-icons/57949619c7cd556bc347cd987b816217.png",
                activities = listOf(23),
                readMoreUrl = "https://www.suunto.com/sports/News-Articles-container-page/12-high-intensity-strength-training-exercises-for-endurance-athletes/",
                libraryStatus = "ADDED",
                categories = listOf(
                    SuuntoPlusStoreRemoteItemCategory(
                        id = "training-physiology",
                        title = "Training Physiology"
                    )
                ),
                watchCompatibility = SuuntoPlusStoreRemoteWatchCompatibility(
                    compatible = true,
                    incompatibilityNote = null,
                ),
            ),
            "emacak9d" to SuuntoPlusStoreRemoteGuide(
                type = "guide",
                title = "Running Form Drills",
                subTitle = "8 Essential drills",
                id = "emacak9d",
                description = """Drills to enhance your running economy. These drills will help 
                    |you to run more efficiently!
                """.trimMargin(),
                richDescription = null,
                labels = listOf("NEW"),
                iconUrl = "https://suuntopluspluginsdev.blob.core.windows.net/guide-icons/57949619c7cd556bc347cd987b816217.png",
                backgroundUrl = "https://suuntopluspluginsdev.blob.core.windows.net/guide-icons/57949619c7cd556bc347cd987b816217.png",
                activities = listOf(1, 2, 3),
                readMoreUrl = "https://www.suunto.com/sports/News-Articles-container-page/8-essential-running-form-drills/",
                libraryStatus = "ADDED",
                categories = listOf(
                    SuuntoPlusStoreRemoteItemCategory(
                        id = "training-physiology",
                        title = "Training Physiology"
                    )
                ),
                watchCompatibility = SuuntoPlusStoreRemoteWatchCompatibility(
                    compatible = true,
                    incompatibilityNote = null,
                ),
            ),
            "yorq7rbf" to SuuntoPlusStoreRemoteGuide(
                type = "guide",
                title = "Juoksu3",
                subTitle = "Just temperature",
                id = "yorq7rbf",
                description = "Guide with temperature",
                richDescription = null,
                labels = listOf("NEW"),
                iconUrl = "https://suuntopluspluginsdev.blob.core.windows.net/guide-icons/42353fcf953b041974d3831b85849b83.png",
                backgroundUrl = "https://suuntopluspluginsdev.blob.core.windows.net/guide-icons/42353fcf953b041974d3831b85849b83.png",
                activities = listOf(1),
                readMoreUrl = "https://bnc // .lt/JVxd/Z3Y12w9bOq?user=3568728&athlete=3568728&workout=1339465921&op=viewWorkout&env=uat",
                libraryStatus = "ADDED",
                categories = listOf(
                    SuuntoPlusStoreRemoteItemCategory(
                        id = "training-physiology",
                        title = "Training Physiology"
                    )
                ),
                watchCompatibility = SuuntoPlusStoreRemoteWatchCompatibility(
                    compatible = true,
                    incompatibilityNote = null,
                ),
            ),
            "zco8n856" to SuuntoPlusStoreRemoteGuide(
                type = "guide",
                title = "Swim Drills",
                subTitle = "Swim technique",
                id = "zco8n856",
                description = """These five technique drills will help you to have better form in
                    | your swims.Read first the post by suunto, then use the Swim drill guide to 
                    | remember each drill 
                """.trimMargin(),
                richDescription = null,
                labels = listOf("NEW"),
                iconUrl = "https://suuntopluspluginsdev.blob.core.windows.net/guide-icons/57949619c7cd556bc347cd987b816217.png",
                backgroundUrl = "https://suuntopluspluginsdev.blob.core.windows.net/guide-icons/57949619c7cd556bc347cd987b816217.png",
                activities = listOf(2),
                readMoreUrl = "https://www.suunto.com/sports/News-Articles-container-page/Swimming-Drills/",
                libraryStatus = "ADDED",
                categories = emptyList(),
                watchCompatibility = SuuntoPlusStoreRemoteWatchCompatibility(
                    compatible = false,
                    incompatibilityNote = "Requires something",
                ),
            )
        )

        const val categoryId = "training-physiology"
        const val categoryName = "Training Physiology"
        private val itemTypes = listOf(
            SuuntoPlusStoreRemoteHomeCategoryItemTypes(
                type = SuuntoPlusStoreRemoteItemType.FEATURE.jsonName,
                itemRefs = features.values.filter { item ->
                    item.categories.orEmpty().any { it.id == categoryId }
                }.map {
                    SuuntoPlusStoreRemoteItemReference(it.id, it.type)
                }
            ),
            SuuntoPlusStoreRemoteHomeCategoryItemTypes(
                type = SuuntoPlusStoreRemoteItemType.GUIDE.jsonName,
                itemRefs = guides.values.filter { item ->
                    item.categories.orEmpty().any { it.id == categoryId }
                }.map {
                    SuuntoPlusStoreRemoteItemReference(it.id, it.type)
                }

            ),
        )
    }
}
