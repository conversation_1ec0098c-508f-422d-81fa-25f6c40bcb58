package com.stt.android.suuntoplusstore.ui

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.bodySmall
import com.stt.android.compose.theme.spacing
import com.stt.android.suuntoplusstore.R

@Composable
internal fun SuuntoPlusStoreBannerBadge(modifier: Modifier = Modifier) {
    Box(
        modifier = modifier
            .clip(RoundedCornerShape(4.dp))
            .background(MaterialTheme.colors.onSurface)
            .padding(all = MaterialTheme.spacing.small)
    ) {
        Text(
            text = stringResource(R.string.suunto_plus_store_banner_badge),
            color = MaterialTheme.colors.surface,
            style = MaterialTheme.typography.bodySmall
        )
    }
}

@Preview
@Composable
private fun PreviewSuuntoPlusStoreBannerBadge() {
    AppTheme {
        SuuntoPlusStoreBannerBadge()
    }
}
