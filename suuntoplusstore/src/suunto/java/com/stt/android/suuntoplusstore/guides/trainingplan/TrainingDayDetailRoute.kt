package com.stt.android.suuntoplusstore.guides.trainingplan

import androidx.compose.material.rememberScaffoldState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalLifecycleOwner
import androidx.compose.ui.res.stringResource
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.LifecycleOwner
import com.stt.android.common.viewstate.ViewState
import com.stt.android.compose.lifecycle.DisposableEffectOnLifecycleStart
import com.stt.android.device.domain.WatchAndNetworkNotificationViewModel
import com.stt.android.device.suuntoplusguide.trainingplan.TrainingDayDetails
import com.stt.android.suuntoplusstore.LibraryOperationViewState
import com.stt.android.suuntoplusstore.R
import com.stt.android.suuntoplusstore.itemdetail.SuuntoPlusStoreItemDetailScreen
import com.stt.android.suuntoplusstore.ui.SuuntoPlusStoreErrorDialog
import com.stt.android.suuntoplusstore.ui.SuuntoPlusStoreFullScreenLoading
import java.util.Locale
import com.stt.android.device.R as DeviceR

@Composable
fun TrainingDayDetailRoute(
    onDismiss: () -> Unit,
    onNavigateToLibrary: () -> Unit,
    onShareLink: (id: String) -> Unit,
    modifier: Modifier = Modifier,
    detailsScreenViewModel: TrainingDayDetailScreenViewModel = hiltViewModel(),
    watchAndNetworkNotificationViewModel: WatchAndNetworkNotificationViewModel = hiltViewModel(),
    lifecycleOwner: LifecycleOwner = LocalLifecycleOwner.current,
) {
    val detailScreenViewState by detailsScreenViewModel.detailScreenViewState.collectAsState()
    val libraryOperationViewState by detailsScreenViewModel.libraryOperationViewState.collectAsState()
    val watchAndNetworkNotificationState by watchAndNetworkNotificationViewModel.watchAndNetworkNotificationState.collectAsState()

    DisposableEffectOnLifecycleStart(lifecycleOwner) { detailsScreenViewModel.refresh() }

    val scaffoldState = rememberScaffoldState()

    val toastData = libraryOperationViewState
        ?.takeUnless { watchAndNetworkNotificationState.watchSyncing }
        ?.toastData()

    LaunchedEffect(toastData) {
        val message = toastData?.message ?: return@LaunchedEffect
        watchAndNetworkNotificationViewModel.showToastMessage(
            message = message,
            actionText = toastData.actionText
        )
    }


    SuuntoPlusStoreItemDetailScreen(
        title = stringResource(
            com.stt.android.device.R.string.suunto_plus_store_training_plan_day,
            detailScreenViewState.data?.workoutDay?.dayNumber ?: 1
        ),
        showLoadingIndicator = detailScreenViewState.isLoading(),
        watchAndNetworkNotificationState = watchAndNetworkNotificationState,
        onCustomToastActionClick = onNavigateToLibrary,
        enableLibraryActions = false,
        enableFloatingButton = false,
        showAddToLibraryAction = false,
        showRemoveFromLibraryAction = false,
        addToWatchButtonText = null,
        removeFromWatchButtonText = null,
        onAddToWatch = { throw NotImplementedError("Add-to-watch not supported for training day") },
        onAddToLibrary = { throw NotImplementedError("Add-to-library not supported for training day") },
        onRemoveFromWatch = { throw NotImplementedError("Remove-from-watch not supported for training day") },
        onRemoveFromLibrary = { throw NotImplementedError("Remove-from-library not supported for training day") },
        showReportButton = false,
        onReportClick = {},
        onUpPressed = onDismiss,
        scaffoldState = scaffoldState,
        showShareAction = detailScreenViewState.data?.showShareAction ?: false,
        onShareClick = {
            detailScreenViewState.data?.shareId?.let {
                onShareLink(it)
            }
        },
        modifier = modifier,
    ) {
        val data = detailScreenViewState.data
        when {
            detailScreenViewState is ViewState.Loaded && data != null ->
                TrainingDayDetails(
                    workoutDay = data.workoutDay,
                    formatter = detailsScreenViewModel.getSimGuideMessagesFormatter()
                )

            detailScreenViewState.isLoading() ->
                SuuntoPlusStoreFullScreenLoading()

            detailScreenViewState.isFailure() ->
                SuuntoPlusStoreErrorDialog(
                    onDismiss = onDismiss,
                    titleRes = (detailScreenViewState as? ViewState.Error)?.errorEvent?.errorStringRes,
                    onRetry = { detailsScreenViewModel.refresh() },
                )
        }
    }

    libraryOperationViewState?.let { viewState ->
        when (viewState.state) {
            LibraryOperationViewState.State.IN_PROGRESS -> SuuntoPlusStoreFullScreenLoading()
            LibraryOperationViewState.State.ERROR -> {
                SuuntoPlusStoreErrorDialog(
                    onDismiss = { detailsScreenViewModel.clearLibraryOperationState() },
                    titleRes = viewState.errorEvent?.errorStringRes
                )
            }

            else -> {}
        }
    }
}

@Composable
private fun LibraryOperationViewState.toastData(): LibraryOperationToast {
    val message: String?
    val actionText: String?
    when (state) {
        LibraryOperationViewState.State.SUCCESS -> {
            message = when (operation) {
                LibraryOperationViewState.Operation.ADD_TO_WATCH ->
                    stringResource(
                        DeviceR.string.suunto_plus_guide_or_sports_app_added_to_watch,
                        itemName
                    )

                LibraryOperationViewState.Operation.REMOVE_FROM_WATCH ->
                    stringResource(
                        DeviceR.string.suunto_plus_guide_or_sports_app_removed_from_watch,
                        itemName
                    )

                else -> throw IllegalArgumentException("Invalid operation for guide $itemName: $operation")
            }

            actionText = if (operation != LibraryOperationViewState.Operation.REMOVE_FROM_WATCH) {
                stringResource(id = R.string.suunto_plus_store_view_my_library_snackbar_action)
                    .uppercase(Locale.getDefault())
            } else {
                null
            }
        }

        else -> {
            message = null
            actionText = null
        }
    }

    return LibraryOperationToast(
        message = message,
        actionText = actionText
    )
}
