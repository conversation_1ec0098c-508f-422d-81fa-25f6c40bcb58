package com.stt.android.domain.sml

import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import com.stt.android.core.R
import com.stt.android.domain.sml.reader.SmlFactory
import com.stt.android.sim.Marks.SetPointMarkType

interface SmlEvent {
    val data: SmlEventData
}

/**
 * [elapsed]: From workout start event. Not available before first start (or resume if no start) event.
 * [duration]: From workout start to this event without pauses. Not available before first start/resume event,
 * while workout is paused, or after stop.
 */
interface SmlEventData {
    val timestamp: Long
    val elapsed: Long? // From workout start event. Not available before first start (or resume if no start) event.
    val duration: Long? // Not available before first start/resume event, while workout is paused, or after stop.
}

interface DiveEvent : SmlEvent {
    val stringRes: Int?
    val descriptionStringRes: Int?
    val iconRes: Int
    val iconSmallRes: Int
    val text: String

    companion object {
        const val DEEP_STOP_DEPTH = 20f
    }
}

interface TraverseEvent : SmlEvent {
    val stringRes: Int?
    val iconRes: Int
    val location: Location?
}

data class UnknownEvent(
    override val data: SmlEventData
) : SmlEvent, SmlEventData by data

data class ActivityEvent(
    override val data: SmlEventData,
    val activityType: Int,
    val name: String?,
    val customModeId: String?
) : SmlEvent, SmlEventData by data

data class ShotEvent(
    override val data: SmlEventData,
    override val location: Location?,
    @StringRes override val stringRes: Int = R.string.workout_values_headline_shot_count,
    @DrawableRes override val iconRes: Int = R.drawable.ic_mark_shot,
) : TraverseEvent, SmlEventData by data

data class CatchEvent(
    override val data: SmlEventData,
    val count: Int,
    val description: CatchMarkType,
    val type: Int,
    override val location: Location?,
    @StringRes override val stringRes: Int = R.string.workout_values_headline_catch_count,
    @DrawableRes override val iconRes: Int = R.drawable.ic_mark_fish,
) : TraverseEvent, SmlEventData by data

data class Location(
    val latitude: Double, // in degrees
    val longitude: Double // in degrees
)

data class NotifyEvent(
    override val data: SmlEventData,
    val type: NotifyMarkType,
    val active: Boolean? = null,
    @StringRes override val stringRes: Int? = type.stringRes,
    @StringRes override val descriptionStringRes: Int? = type.descriptionStringRes,
    @DrawableRes override val iconRes: Int = type.iconRes,
    @DrawableRes override val iconSmallRes: Int = type.iconSmallRes,
    override val text: String = type.value,
    val optionalData: Any? = null
) : DiveEvent, SmlEventData by data

data class StateEvent(
    override val data: SmlEventData,
    val type: StateMarkType,
    val active: Boolean? = null,
    @StringRes override val stringRes: Int? = type.stringRes,
    @StringRes override val descriptionStringRes: Int? = type.descriptionStringRes,
    @DrawableRes override val iconRes: Int = type.iconRes,
    @DrawableRes override val iconSmallRes: Int = type.iconSmallRes,
    override val text: String = type.value,
    val optionalData: Any? = null
) : DiveEvent, SmlEventData by data

data class WarningEvent(
    override val data: SmlEventData,
    val type: WarningMarkType,
    val active: Boolean? = null,
    @StringRes override val stringRes: Int? = type.stringRes,
    @StringRes override val descriptionStringRes: Int? = type.descriptionStringRes,
    @DrawableRes override val iconRes: Int = type.iconRes,
    @DrawableRes override val iconSmallRes: Int = type.iconSmallRes,
    override val text: String = type.value,
    val optionalData: Any? = null
) : DiveEvent, SmlEventData by data

data class AlarmEvent(
    override val data: SmlEventData,
    val type: AlarmMarkType,
    val active: Boolean? = null,
    @StringRes override val stringRes: Int? = type.stringRes,
    @StringRes override val descriptionStringRes: Int? = type.descriptionStringRes,
    @DrawableRes override val iconRes: Int = type.iconRes,
    @DrawableRes override val iconSmallRes: Int = type.iconSmallRes,
    override val text: String = type.value
) : DiveEvent, SmlEventData by data

data class ErrorEvent(
    override val data: SmlEventData,
    val type: ErrorMarkType,
    @StringRes override val stringRes: Int? = type.stringRes,
    @StringRes override val descriptionStringRes: Int? = type.descriptionStringRes,
    @DrawableRes override val iconRes: Int = type.iconRes,
    @DrawableRes override val iconSmallRes: Int = type.iconSmallRes,
    override val text: String = type.value
) : DiveEvent, SmlEventData by data

data class BookmarkEvent(
    override val data: SmlEventData,
    val name: String? = null,
    val screenshot: String? = null,
    @StringRes override val stringRes: Int? = R.string.event_bookmark,
    @StringRes override val descriptionStringRes: Int? = null,
    @DrawableRes override val iconRes: Int = R.drawable.dive_event_green_bookmark,
    @DrawableRes override val iconSmallRes: Int = R.drawable.dive_event_green_generic_small,
    override val text: String = name ?: ""
) : DiveEvent, SmlEventData by data

data class GasSwitchEvent(
    override val data: SmlEventData,
    val gasNumber: Int,
    val previousGasNumber: Int?,
    val previousGasName: String?,
    @StringRes override val stringRes: Int? = R.string.event_gas_switch,
    @StringRes override val descriptionStringRes: Int? = null,
    @DrawableRes override val iconRes: Int = R.drawable.dive_event_blue_gas_mixing,
    @DrawableRes override val iconSmallRes: Int = R.drawable.dive_event_blue_generic_small,
    override val text: String = "" // Empty by default, will contain the gas name
) : DiveEvent, SmlEventData by data

data class SetPointEvent(
    override val data: SmlEventData,
    val type: SetPointMarkType?,
    val automatic: Boolean?,
    val po2: Float,
    val depth: Float?,
    @StringRes override val stringRes: Int? = R.string.event_setpoint_switch,
    @StringRes override val descriptionStringRes: Int? = null,
    @DrawableRes override val iconRes: Int = R.drawable.dive_event_green_gas_mixing,
    @DrawableRes override val iconSmallRes: Int = R.drawable.dive_event_green_generic_small,
    override val text: String = ""
) : DiveEvent, SmlEventData by data

data class DiveTimerEvent(
    override val data: SmlEventData,
    val active: Boolean?,
    val time: Float?,
    @StringRes override val stringRes: Int? = null,
    @StringRes override val descriptionStringRes: Int? = null,
    @DrawableRes override val iconRes: Int = R.drawable.dive_event_green_dive_timer,
    @DrawableRes override val iconSmallRes: Int = R.drawable.dive_event_green_generic_small,
    override val text: String = "" // Empty by default
) : DiveEvent, SmlEventData by data

data class GasEditEvent(
    override val data: SmlEventData,
    val insertGasNumber: Int?,
    val removeGasNumber: Int?,
    @StringRes override val stringRes: Int? = null,
    @StringRes override val descriptionStringRes: Int? = null,
    @DrawableRes override val iconRes: Int = R.drawable.dive_event_blue_gas_edit,
    @DrawableRes override val iconSmallRes: Int = R.drawable.dive_event_blue_generic_small,
    override val text: String = "" // Empty by default
) : DiveEvent, SmlEventData by data

data class OoamEvent(
    override val data: SmlEventData,
    val type : OoamMarkType,
    @StringRes override val stringRes: Int? = type.stringRes,
    @StringRes override val descriptionStringRes: Int? = type.descriptionStringRes,
    @DrawableRes override val iconRes: Int = type.iconRes,
    @DrawableRes override val iconSmallRes: Int = type.iconSmallRes,
    override val text: String = ""
) : DiveEvent, SmlEventData by data

data class RecordingStatusEvent(
    override val data: SmlEventData,
    val type: RecordingStatusEventType
) : SmlEvent, SmlEventData by data

data class IntervalEvent(
    override val data: SmlEventData,
    val type: IntervalEventType
) : SmlEvent, SmlEventData by data

data class SwimmingEvent(
    override val data: SmlEventData,
    val type: String,
) : SmlEvent, SmlEventData by data

data class VerticalLapEvent(
    override val data: SmlEventData,
    val type: VerticalLapEventType
) : SmlEvent, SmlEventData by data

enum class CatchMarkType {
    BIGGAME,
    SMALLGAME,
    BIRD,
    FISH,
    UNKNOWN;

    companion object {
        fun from(value: Int?): CatchMarkType =
            entries.firstOrNull { it.ordinal == value } ?: UNKNOWN
    }
}

enum class RecordingStatusEventType {
    Start,
    Stop,
    Pause,
    Resume;

    fun isAllowedAfter(previous: RecordingStatusEventType): Boolean {
        return when (previous) {
            Start -> listOf(
                Pause,
                Stop
            ).contains(this)
            Pause -> listOf(
                Resume,
                Stop
            ).contains(this)
            Resume -> listOf(
                Pause,
                Stop
            ).contains(this)
            Stop -> false
        }
    }
}

enum class IntervalEventType {
    Interval,
    Recovery
}

enum class VerticalLapEventType {
    Downhill,
    Unknown
}

enum class NotifyMarkType(
    val value: String,
    val ngSpecificValue: String? = null,
    @StringRes val stringRes: Int? = null,
    @StringRes val descriptionStringRes: Int? = null,
    @DrawableRes val iconRes: Int = R.drawable.dive_event_green_generic,
    @DrawableRes val iconSmallRes: Int = R.drawable.dive_event_green_generic_small
) {
    NO_FLY_TIME(
        value = "NoFly Time" // not shown in UI
    ),
    DEPTH(
        value = "Depth",
        stringRes = R.string.event_notify_depth,
        descriptionStringRes = R.string.event_notify_depth_description,
        iconRes = R.drawable.dive_event_green_generic,
        iconSmallRes = R.drawable.dive_event_green_generic_small
    ),
    SURFACE_TIME(
        value = "Surface Time",
        stringRes = R.string.event_notify_surface_time,
        descriptionStringRes = R.string.event_notify_surface_time_description,
        iconRes = R.drawable.dive_event_green_state,
        iconSmallRes = R.drawable.dive_event_green_generic_small
    ),
    TISSUE_LEVEL(
        value = "Tissue Level" // not shown in UI
    ),
    DECO(
        value = "Deco",
        ngSpecificValue = "Ndl exceeded",
        stringRes = R.string.event_notify_deco,
        descriptionStringRes = R.string.event_notify_deco_description,
        iconRes = R.drawable.dive_event_green_generic,
        iconSmallRes = R.drawable.dive_event_green_generic_small
    ),
    DECO_WINDOW(
        value = "Deco Window",
        ngSpecificValue = "At Deco Stop",
        stringRes = R.string.event_notify_deco_window,
        descriptionStringRes = R.string.event_notify_deco_window_description,
        iconRes = R.drawable.dive_event_green_decompression_stop,
        iconSmallRes = R.drawable.dive_event_green_generic_small
    ),
    GAS_AVAILABLE(
        "Gas Available" // not shown in UI
    ),
    SET_POINT_SWITCH(
        value = "SetPoint Switch",
        stringRes = R.string.event_notify_setpoint_switch,
        descriptionStringRes = R.string.event_notify_setpoint_switch_description,
        iconRes = R.drawable.dive_event_green_gas_mixing,
        iconSmallRes = R.drawable.dive_event_green_generic_small
    ),
    SAFETY_STOP_AHEAD(
        value = "Safety Stop Ahead" // not shown in  UI
    ),
    SAFETY_STOP_BROKEN(
        value = "Safety Stop Broken",
        stringRes = R.string.event_notify_safety_stop_broken,
        descriptionStringRes = R.string.event_notify_safety_stop_broken_description,
        iconRes = R.drawable.dive_event_yellow_mandatory_safety_stop,
        iconSmallRes = R.drawable.dive_event_yellow_generic_small
    ),
    SAFETY_STOP(
        value = "Safety Stop",
        ngSpecificValue = "At Safety Stop",
        stringRes = R.string.event_notify_safety_stop,
        descriptionStringRes = R.string.event_notify_safety_stop_description,
        iconRes = R.drawable.dive_event_green_mandatory_safety_stop,
        iconSmallRes = R.drawable.dive_event_green_generic_small
    ),
    DEEP_STOP_AHEAD(
        value = "Deep Stop Ahead",
        stringRes = R.string.event_notify_deep_stop_ahead,
        descriptionStringRes = R.string.event_notify_deep_stop_ahead_description,
        iconRes = R.drawable.dive_event_green_generic,
        iconSmallRes = R.drawable.dive_event_green_generic_small
    ),
    DEEP_STOP(
        value = "Deep Stop",
        ngSpecificValue = "At Deep Stop",
        stringRes = R.string.event_notify_deep_stop,
        descriptionStringRes = R.string.event_notify_deep_stop_description,
        iconRes = R.drawable.dive_event_green_generic,
        iconSmallRes = R.drawable.dive_event_green_generic_small
    ),
    DILUENT_HYPOXIA(
        value = "Diluent Hypoxia",
        stringRes = R.string.event_notify_diluent_hypoxia,
        descriptionStringRes = R.string.event_notify_diluent_hypoxia_description,
        iconRes = R.drawable.dive_event_yellow_po2,
        iconSmallRes = R.drawable.dive_event_yellow_generic_small
    ),
    BELOW_FLOOR("Below Floor"), // not used in eon devices
    AIR_TIME(
        value = "Air Time",
        stringRes = R.string.event_notify_air_time,
        descriptionStringRes = R.string.event_notify_air_time_description,
        iconRes = R.drawable.dive_event_yellow_time,
        iconSmallRes = R.drawable.dive_event_yellow_generic_small
    ),
    TANK_PRESSURE(
        value = "Tank Pressure",
        ngSpecificValue = "User Tank Pressure",
        stringRes = R.string.event_notify_tank_pressure,
        descriptionStringRes = R.string.event_notify_tank_pressure_description,
        iconRes = R.drawable.dive_event_yellow_tank_pressure,
        iconSmallRes = R.drawable.dive_event_yellow_generic_small
    ),
    CCR_O2_TANK_PRESSURE(
        value = "CCR O2 Tank Pressure",
        stringRes = R.string.event_notify_tank_pressure,
        descriptionStringRes = R.string.event_notify_tank_pressure_description,
        iconRes = R.drawable.dive_event_yellow_tank_pressure,
        iconSmallRes = R.drawable.dive_event_yellow_generic_small
    ),
    DECO_BROKEN_ACKNOWLEDGED(
        value = "Deco Broken Acknowledged",
        stringRes = R.string.event_notify_missed_deco_ack,
        descriptionStringRes = R.string.event_notify_missed_deco_ack_description,
        iconRes = R.drawable.dive_event_yellow_decompression_stop,
        iconSmallRes = R.drawable.dive_event_yellow_generic_small
    ),
    DIVE_TIME(
        value = "Dive Time",
        stringRes = R.string.event_notify_dive_time,
        descriptionStringRes = R.string.event_notify_dive_time_description,
        iconRes = R.drawable.dive_event_green_generic,
        iconSmallRes = R.drawable.dive_event_green_generic_small
    ),
    USER_NDL(
        value = "User Ndl",
        stringRes = R.string.event_notify_user_ndl,
        descriptionStringRes = R.string.event_notify_user_ndl_description,
        iconRes = R.drawable.dive_event_ndl,
        iconSmallRes = R.drawable.dive_event_yellow_generic_small
    ),
    UNKNOWN_TYPE("Unknown Type");

    companion object {
        fun from(value: String?): NotifyMarkType =
            entries.firstOrNull { it.value == value || it.ngSpecificValue == value }
                ?: UNKNOWN_TYPE
    }
}

enum class StateMarkType(
    val value: String,
    val ngSpecificValue: String? = null,
    @StringRes val stringRes: Int? = null,
    @StringRes val descriptionStringRes: Int? = null,
    @DrawableRes val iconRes: Int = R.drawable.dive_event_green_state,
    @DrawableRes val iconSmallRes: Int = R.drawable.dive_event_green_generic_small
) {
    WET_OUTSIDE("Wet Outside"),
    BELOW_WET_ACTIVATION_DEPTH("Below Wet Activation Depth"),
    BELOW_SURFACE("Below Surface"),
    DIVE_ACTIVE(
        value = "Dive Active",
        stringRes = R.string.event_dive_active
        // descriptionStringRes, iconRes and iconSmallRes determined dynamically
    ),
    SURFACE_CALCULATION("Surface Calculation"),
    TANK_PRESSURE_AVAILABLE("Tank pressure available"),
    CLOSED_CIRCUIT_MODE(
        value = "Closed Circuit Mode",
        stringRes = R.string.event_closed_circuit_mode
        // descriptionStringRes, iconRes and iconSmallRes determined dynamically
    ),
    DECO(
        value = "Deco",
        ngSpecificValue = "Ndl exceeded",
        stringRes = R.string.event_notify_deco,
        descriptionStringRes = R.string.event_notify_deco_description,
        iconRes = R.drawable.dive_event_green_generic,
        iconSmallRes = R.drawable.dive_event_green_generic_small
    ),
    DECO_WINDOW(
        value = "Deco Window",
        ngSpecificValue = "At Deco Stop",
        stringRes = R.string.event_notify_deco_window,
        descriptionStringRes = R.string.event_notify_deco_window_description,
        iconRes = R.drawable.dive_event_green_decompression_stop,
        iconSmallRes = R.drawable.dive_event_green_generic_small
    ),
    SAFETY_STOP(
        value = "Safety Stop",
        ngSpecificValue = "At Safety Stop",
        stringRes = R.string.event_notify_safety_stop,
        descriptionStringRes = R.string.event_notify_safety_stop_description,
        iconRes = R.drawable.dive_event_green_mandatory_safety_stop,
        iconSmallRes = R.drawable.dive_event_green_generic_small
    ),
    DEEP_STOP_AHEAD(
        value = "Deep Stop Ahead",
        stringRes = R.string.event_notify_deep_stop_ahead,
        descriptionStringRes = R.string.event_notify_deep_stop_ahead_description,
        iconRes = R.drawable.dive_event_green_generic,
        iconSmallRes = R.drawable.dive_event_green_generic_small
    ),
    DEEP_STOP(
        value = "Deep Stop",
        ngSpecificValue = "At Deep Stop",
        stringRes = R.string.event_notify_deep_stop,
        descriptionStringRes = R.string.event_notify_deep_stop_description,
        iconRes = R.drawable.dive_event_green_generic,
        iconSmallRes = R.drawable.dive_event_green_generic_small
    ),
    UNKNOWN_TYPE("Unknown Type");
    companion object {
        fun from(value: String?): StateMarkType =
            StateMarkType.entries.firstOrNull { it.value == value || it.ngSpecificValue == value }
                ?: UNKNOWN_TYPE
    }
}

enum class WarningMarkType(
    val value: String,
    val ngSpecificValue: String? = null,
    @StringRes val stringRes: Int? = null,
    @StringRes val descriptionStringRes: Int? = null,
    @DrawableRes val iconRes: Int = R.drawable.dive_event_yellow_generic,
    @DrawableRes val iconSmallRes: Int = R.drawable.dive_event_yellow_generic_small
) {
    ICD_PENALTY(
        value = "ICD Penalty",
        stringRes = R.string.event_warning_icd_penalty,
        descriptionStringRes = R.string.event_warning_icd_penalty_description,
        iconRes = R.drawable.dive_event_red_generic,
        iconSmallRes = R.drawable.dive_event_red_generic_small
    ),
    DEEP_STOP_PENALTY(
        value = "Deep Stop Penalty",
        stringRes = R.string.event_warning_deep_stop_penalty,
        descriptionStringRes = R.string.event_warning_deep_stop_penalty_description,
        iconRes = R.drawable.dive_event_yellow_generic,
        iconSmallRes = R.drawable.dive_event_yellow_generic_small
    ),
    MANDATORY_SAFETY_STOP(
        value = "Mandatory Safety Stop",
        stringRes = R.string.event_warning_mandatory_safety_stop,
        descriptionStringRes = R.string.event_warning_mandatory_safety_stop_description,
        iconRes = R.drawable.dive_event_yellow_mandatory_safety_stop,
        iconSmallRes = R.drawable.dive_event_yellow_generic_small
    ),
    OLF80(value = "OLF80%"), // not used in eon devices
    OLF100(value = "OLF100%"), // not used in eon devices
    CNS80(
        value = "CNS80%",
        stringRes = R.string.event_warning_cns80,
        descriptionStringRes = R.string.event_warning_cns80_description,
        iconRes = R.drawable.dive_event_yellow_generic,
        iconSmallRes = R.drawable.dive_event_yellow_generic_small
    ),

    // Error icons used for OTU300
    CNS100(
        value = "CNS100%",
        stringRes = R.string.event_warning_cns100,
        descriptionStringRes = R.string.event_warning_cns100_description,
        iconRes = R.drawable.dive_event_red_generic,
        iconSmallRes = R.drawable.dive_event_red_generic_small
    ),
    OTU250(
        value = "OTU250",
        stringRes = R.string.event_warning_otu250,
        descriptionStringRes = R.string.event_warning_otu250_description,
        iconRes = R.drawable.dive_event_yellow_generic,
        iconSmallRes = R.drawable.dive_event_yellow_generic_small
    ),
    OTU300(
        value = "OTU300",
        stringRes = R.string.event_warning_otu300,
        descriptionStringRes = R.string.event_warning_otu300_description,
        iconRes = R.drawable.dive_event_red_generic, // Error icons used for OTU300
        iconSmallRes = R.drawable.dive_event_red_generic_small
    ),
    AIR_TIME(
        value = "Air Time",
        stringRes = R.string.event_warning_air_time,
        descriptionStringRes = R.string.event_warning_air_time_description,
        iconRes = R.drawable.dive_event_red_time,
        iconSmallRes = R.drawable.dive_event_red_generic_small
    ),
    MAX_DEPTH(
        value = "Max.Depth",
        stringRes = R.string.event_warning_max_depth,
        descriptionStringRes = R.string.event_warning_max_depth_description,
        iconRes = R.drawable.dive_event_yellow_max_depth,
        iconSmallRes = R.drawable.dive_event_yellow_generic_small
    ),
    TANK_PRESSURE(
        value = "Tank Pressure",
        stringRes = R.string.event_warning_tank_pressure,
        descriptionStringRes = R.string.event_warning_tank_pressure_description,
        iconRes = R.drawable.dive_event_red_tank_pressure,
        iconSmallRes = R.drawable.dive_event_red_generic_small
    ),
    CCR_O2_TANK_PRESSURE(
        value = "CCR O2 Tank Pressure",
        stringRes = R.string.event_warning_tank_pressure,
        descriptionStringRes = R.string.event_warning_tank_pressure_description,
        iconRes = R.drawable.dive_event_red_tank_pressure,
        iconSmallRes = R.drawable.dive_event_red_generic_small
    ),
    CEILING_BROKEN(
        value = "Ceiling Broken" // not shown in UI
    ),
    DEEP_STOP_BROKEN(
        value = "Deep Stop Broken" // not shown in UI
    ),
    SAFETY_STOP_BROKEN(
        value = "Safety Stop Broken" // not shown in UI
    ),
    PO2_HIGH(
        value = "PO2 High",
        stringRes = R.string.event_warning_po2_high,
        descriptionStringRes = R.string.event_warning_po2_high_description,
        iconRes = R.drawable.dive_event_yellow_po2,
        iconSmallRes = R.drawable.dive_event_yellow_generic_small
    ),
    DECO_BROKEN(
        value = "Deco Broken",
        stringRes = R.string.event_warning_deco_broken,
        descriptionStringRes = R.string.event_warning_deco_broken_description,
        iconRes = R.drawable.dive_event_red_decompression_stop,
        iconSmallRes = R.drawable.dive_event_red_generic_small
    ),
    MINI_LOCK(
        value = "Algorithm Minilocked",
        stringRes = R.string.event_warning_mini_lock,
        descriptionStringRes = R.string.event_warning_mini_lock_description,
        iconRes = R.drawable.dive_event_red_triangle,
        iconSmallRes = R.drawable.dive_event_red_triangle_small
    ),
    /* NG Dive events */
    DEPTH(
        value = "Depth",
        stringRes = R.string.event_notify_depth,
        descriptionStringRes = R.string.event_notify_depth_description,
        iconRes = R.drawable.dive_event_green_generic,
        iconSmallRes = R.drawable.dive_event_green_generic_small
    ),
    DIVE_TIME(
        value = "Dive Time",
        stringRes = R.string.event_notify_dive_time,
        descriptionStringRes = R.string.event_notify_dive_time_description,
        iconRes = R.drawable.dive_event_green_generic,
        iconSmallRes = R.drawable.dive_event_green_generic_small
    ),
    USER_TANK_PRESSURE(
        value = "User Tank Pressure",
        stringRes = R.string.event_notify_tank_pressure,
        descriptionStringRes = R.string.event_notify_tank_pressure_description,
        iconRes = R.drawable.dive_event_yellow_tank_pressure,
        iconSmallRes = R.drawable.dive_event_yellow_generic_small
    ),
    USER_NDL(
        value = "User Ndl",
        stringRes = R.string.event_notify_user_ndl,
        descriptionStringRes = R.string.event_notify_user_ndl_description,
        iconRes = R.drawable.dive_event_ndl,
        iconSmallRes = R.drawable.dive_event_yellow_generic_small
    ),
    NO_DECO_TIME(
        value = "NoDecoTime",
        stringRes = R.string.event_warning_no_deco_time,
        descriptionStringRes = R.string.event_warning_no_deco_time_description,
        iconRes = R.drawable.dive_event_ndl,
        iconSmallRes = R.drawable.dive_event_yellow_generic_small
    ),
    UNKNOWN_TYPE("Unknown Type");

    companion object {
        fun from(value: String?): WarningMarkType =
            entries.firstOrNull { it.value == value || it.ngSpecificValue == value } ?: UNKNOWN_TYPE
    }
}

enum class AlarmMarkType(
    val value: String,
    @StringRes val stringRes: Int? = null,
    @StringRes val descriptionStringRes: Int? = null,
    @DrawableRes val iconRes: Int = R.drawable.dive_event_red_generic,
    @DrawableRes val iconSmallRes: Int = R.drawable.dive_event_red_generic_small
) {
    MANDATORY_SAFETY_STOP_BROKEN(
        value = "Mandatory Safety Stop Broken",
        stringRes = R.string.event_alarm_mandatory_safety_stop_broken,
        descriptionStringRes = R.string.event_alarm_mandatory_safety_stop_broken_description,
        iconRes = R.drawable.dive_event_red_mandatory_safety_stop,
        iconSmallRes = R.drawable.dive_event_red_generic_small
    ),
    ASCENT_SPEED(
        value = "Ascent Speed",
        stringRes = R.string.event_alarm_ascent_speed,
        descriptionStringRes = R.string.event_alarm_ascent_speed_description,
        iconRes = R.drawable.dive_event_red_ascent_speed,
        iconSmallRes = R.drawable.dive_event_red_generic_small
    ),
    DILUENT_HYPEROXIA(
        value = "Diluent Hyperoxia",
        stringRes = R.string.event_alarm_diluent_hyperoxia,
        descriptionStringRes = R.string.event_alarm_diluent_hyperoxia_description,
        iconRes = R.drawable.dive_event_red_po2,
        iconSmallRes = R.drawable.dive_event_red_generic_small
    ),
    VIOLATED_DEEP_STOP(
        value = "Violated Deep Stop" // not shown in UI
    ),
    CEILING_BROKEN(
        value = "Ceiling Broken",
        stringRes = R.string.event_alarm_ceiling_broken,
        descriptionStringRes = R.string.event_alarm_ceiling_broken_description,
        iconRes = R.drawable.dive_event_red_decompression_stop,
        iconSmallRes = R.drawable.dive_event_red_generic_small
    ),
    PO2_HIGH(
        value = "PO2 High",
        stringRes = R.string.event_alarm_po2_high,
        descriptionStringRes = R.string.event_alarm_po2_high_description,
        iconRes = R.drawable.dive_event_red_po2,
        iconSmallRes = R.drawable.dive_event_red_generic_small
    ),
    PO2_LOW(
        value = "PO2 Low",
        stringRes = R.string.event_alarm_po2_low,
        descriptionStringRes = R.string.event_alarm_po2_low_description,
        iconRes = R.drawable.dive_event_red_po2,
        iconSmallRes = R.drawable.dive_event_red_generic_small
    ),
    STORM(
        value = "Storm" // not used in eon devices
    ),
    /* NG Dive events */
    OTU300(
        value = "OTU300",
        stringRes = R.string.event_warning_otu300,
        descriptionStringRes = R.string.event_warning_otu300_description,
        iconRes = R.drawable.dive_event_red_generic, // Error icons used for OTU300
        iconSmallRes = R.drawable.dive_event_red_generic_small
    ),
    CNS100(
        value = "CNS100%",
        stringRes = R.string.event_warning_cns100,
        descriptionStringRes = R.string.event_warning_cns100_description,
        iconRes = R.drawable.dive_event_red_generic,
        iconSmallRes = R.drawable.dive_event_red_generic_small
    ),
    DECO_BROKEN(
        value = "Deco Stop Broken",
        stringRes = R.string.event_warning_deco_broken,
        descriptionStringRes = R.string.event_warning_deco_broken_description,
        iconRes = R.drawable.dive_event_red_decompression_stop,
        iconSmallRes = R.drawable.dive_event_red_generic_small
    ),
    TANK_PRESSURE(
        value = "Tank Pressure",
        stringRes = R.string.event_warning_tank_pressure,
        descriptionStringRes = R.string.event_warning_tank_pressure_description,
        iconRes = R.drawable.dive_event_red_tank_pressure,
        iconSmallRes = R.drawable.dive_event_red_generic_small
    ),
    UNKNOWN_TYPE("Unknown Type");

    companion object {
        fun from(value: String?): AlarmMarkType =
            entries.firstOrNull { it.value == value } ?: UNKNOWN_TYPE
    }
}

enum class ErrorMarkType(
    val value: String,
    @StringRes val stringRes: Int? = null,
    @StringRes val descriptionStringRes: Int? = null,
    @DrawableRes val iconRes: Int = R.drawable.dive_event_red_triangle,
    @DrawableRes val iconSmallRes: Int = R.drawable.dive_event_red_triangle_small
) {
    CEILING_BROKEN(
        value = "Ceiling Broken",
        stringRes = R.string.event_error_ceiling_broken,
        descriptionStringRes = R.string.event_error_ceiling_broken_description,
        iconRes = R.drawable.dive_event_red_mini_lock,
        iconSmallRes = R.drawable.dive_event_red_generic_small
    ),
    UNKNOWN_TYPE("Unknown Type");

    companion object {
        fun from(value: String): ErrorMarkType =
            entries.firstOrNull { it.value == value } ?: UNKNOWN_TYPE
    }
}

enum class OoamMarkType(
    val value: String,
    @StringRes val stringRes: Int? = null,
    @StringRes val descriptionStringRes: Int? = null,
    @DrawableRes val iconRes: Int = R.drawable.dive_event_red_triangle,
    @DrawableRes val iconSmallRes: Int = R.drawable.dive_event_red_triangle_small
) {
    CEILING_BROKEN(
        value = "Ceiling broken",
        stringRes = R.string.event_out_of_algorithm_model_generic_title,
        descriptionStringRes = R.string.event_out_of_algorithm_model_ceiling_broken_description
    ),
    GENERIC_WARNING(
        value = "Generic warning",
        stringRes = R.string.event_out_of_algorithm_model_generic_title,
        descriptionStringRes = R.string.event_out_of_algorithm_model_generic_description
    );

    companion object {
        fun from(value: String): OoamMarkType =
            entries.firstOrNull { it.value == value } ?: GENERIC_WARNING
    }
}

fun SmlEventData.withModifiedElapsed(elapsed: Long): SmlEventData =
    SmlFactory.SmlEventDataInternal(
        timestamp = this.timestamp,
        elapsed = elapsed,
        duration = this.duration
    )
