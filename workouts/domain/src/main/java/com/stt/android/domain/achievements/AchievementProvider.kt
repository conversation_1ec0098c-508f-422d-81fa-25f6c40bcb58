package com.stt.android.domain.achievements

import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.core.domain.workouts.CoreActivityType
import com.stt.android.data.toEpochMilli
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.domain.workouts.WorkoutHeaderDataSource
import com.stt.android.utils.CalendarProvider
import java.time.Instant
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.YearMonth
import java.time.ZoneId
import java.time.temporal.ChronoUnit
import java.util.Locale
import javax.inject.Inject
import kotlin.math.min

class AchievementProvider @Inject constructor(
    private val workoutHeaderDataSource: WorkoutHeaderDataSource,
    private val achievementAnalytics: AchievementAnalytics,
    private val calendarProvider: CalendarProvider
) {

    suspend fun calculateAchievements(newWorkout: WorkoutHeader): Achievement? {
        if (newWorkout.key.isNullOrBlank()) {
            return null
        }

        val zoneId = ZoneId.systemDefault()
        val newWorkoutLocalDate = Instant.ofEpochMilli(newWorkout.startTime).atZone(zoneId).toLocalDate()
        val firstDayOfTheYear = newWorkoutLocalDate.withDayOfYear(1)
        val startOfYearMillis = firstDayOfTheYear.atStartOfDay().atZone(zoneId).toEpochMilli()
        val firstDayOfMonth = newWorkoutLocalDate.withDayOfMonth(1)
        val startOfMonthMillis = firstDayOfMonth.atStartOfDay().atZone(zoneId).toEpochMilli()
        val dayOfWeek = calendarProvider.getDayOfWeekField()
        val startOfCurrentWeekMillis =
            newWorkoutLocalDate.with(dayOfWeek, 1).atStartOfDay(zoneId).toEpochMilli()
        val startOfPreviousWeekMillis =
            newWorkoutLocalDate.with(dayOfWeek, 1).atStartOfDay(zoneId).minusWeeks(1).toEpochMilli()
        val endOfPreviousWeekMillis =
            newWorkoutLocalDate.with(dayOfWeek, 7).atTime(LocalTime.MAX).atZone(zoneId).minusWeeks(1)
                .toEpochMilli()

        val totalActivityTypeCount = workoutHeaderDataSource.loadActivityTypeCount(newWorkout.activityTypeId)
        val personalBestAchievements = mutableListOf<PersonalBestAchievement>()
        // Personal Bests are only shown for workouts with distance > 0.1km.
        val distanceThreshold = 100
        if (newWorkout.totalDistance > distanceThreshold) {
            personalBestAchievements.addAll(
                calculatePersonalBests(
                    newWorkout = newWorkout,
                    startOfYearMillis = startOfYearMillis,
                    startOfMonthMillis = startOfMonthMillis,
                    totalActivityTypeCount = totalActivityTypeCount
                )
            )
        }

        // Only save cumulative achievements from one priority group
        val cumulativeAchievements = mutableListOf<CumulativeAchievement>()

        // Data for cumulative achievements priority 1
        val activityTypeCountThisYear = workoutHeaderDataSource.loadActivityTypeCountInPeriod(
            newWorkout.activityTypeId,
            startOfYearMillis,
            newWorkout.startTime
        )
        val activityCountThisYear = workoutHeaderDataSource.loadActivityCountInPeriod(
            startOfYearMillis,
            newWorkout.startTime
        )
        val totalActivityCount = workoutHeaderDataSource.loadTotalActivityCount()
        val activityTypeCountThisWeek = workoutHeaderDataSource.loadActivityTypeCountInPeriod(
            newWorkout.activityTypeId,
            startOfCurrentWeekMillis,
            newWorkout.startTime
        )
        val activityTypeCountPreviousWeek = workoutHeaderDataSource.loadActivityTypeCountInPeriod(
            newWorkout.activityTypeId,
            startOfPreviousWeekMillis,
            endOfPreviousWeekMillis
        )

        // Calculate New activities-category achievement, only 1 shown if needed
        val newActivitiesAchievement = calculateNewActivitiesAchievements(
            totalActivityTypeCount = totalActivityTypeCount,
            activityTypeCountThisYear = activityTypeCountThisYear,
            newWorkout = newWorkout,
            startOfMonth = startOfMonthMillis
        )
        newActivitiesAchievement?.let { cumulativeAchievements.add(it) }

        cumulativeAchievements.addAll(
            processPriority1CumulativeAchievements(
                activityTypeCountThisYear = activityTypeCountThisYear,
                activityCountThisYear = activityCountThisYear,
                totalActivityCount = totalActivityCount,
                activityTypeCountThisWeek = activityTypeCountThisWeek,
                activityTypeCountPreviousWeek = activityTypeCountPreviousWeek,
                newWorkout = newWorkout
            )
        )

        if (cumulativeAchievements.isEmpty()) {
            // Data for cumulative achievements priority 2
            val activityCountThisWeek = workoutHeaderDataSource.loadActivityCountInPeriod(
                startOfCurrentWeekMillis,
                newWorkout.startTime
            )
            val activityCountPreviousWeek = workoutHeaderDataSource.loadActivityCountInPeriod(
                startOfPreviousWeekMillis,
                endOfPreviousWeekMillis
            )
            processPriority2CumulativeAchievement(
                activityCountPreviousWeek = activityCountPreviousWeek,
                activityCountCurrentWeek = activityCountThisWeek,
                newWorkout = newWorkout
            )?.let {
                cumulativeAchievements.add(it)
            }
        }

        if (cumulativeAchievements.isEmpty()) {
            // Data for cumulative achievements priority 3
            val activityTypeCountThisMonth = workoutHeaderDataSource.loadActivityTypeCountInPeriod(
                newWorkout.activityTypeId,
                startOfMonthMillis,
                newWorkout.startTime
            )
            processPriority3CumulativeAchievement(
                activityTypeCountThisMonth = activityTypeCountThisMonth,
                newWorkout = newWorkout
            )?.let {
                cumulativeAchievements.add(
                    it
                )
            }
        }

        // Add Early bird-achievement if in time range
        if (isBetweenEarlyBirdRange(newWorkout)) {
            sendAchievementReachedEvent(AnalyticsPropertyValue.AchievementType.EARLY_BIRD, newWorkout)
            cumulativeAchievements.add(createEarlyBirdAchievement())
        }

        // No reason to create achievement if both lists are empty
        if (cumulativeAchievements.isEmpty() && personalBestAchievements.isEmpty()) {
            return null
        }

        return Achievement(
            workoutKey = newWorkout.key,
            activityType = newWorkout.activityTypeId,
            timestamp = newWorkout.startTime,
            personalBestAchievements = personalBestAchievements,
            cumulativeAchievements = cumulativeAchievements
        )
    }

    private fun isBetweenEarlyBirdRange(newWorkout: WorkoutHeader): Boolean {
        val newWorkoutLocalTime =
            Instant.ofEpochMilli(newWorkout.startTime).atZone(ZoneId.systemDefault()).toLocalTime()

        // Time range for Early bird-achievement
        val start = LocalTime.of(4, 0)
        val end = LocalTime.of(8, 0)

        return !newWorkoutLocalTime.isBefore(start) && newWorkoutLocalTime.isBefore(end)
    }

    private fun createEarlyBirdAchievement(): CumulativeAchievement {
        return CumulativeAchievement(
            description = EARLY_BIRD,
            activityCounts = ActivityCounts(
                timeCategory = DAY,
                currentCount = 0,
                previousCount = 0,
                firstInCount = null,
                activityCount = 0,
                activityTypeCount = 0
            )
        )
    }

    private fun processPriority1CumulativeAchievements(
        activityTypeCountThisYear: Long,
        activityCountThisYear: Long,
        totalActivityCount: Long,
        activityTypeCountThisWeek: Long,
        activityTypeCountPreviousWeek: Long,
        newWorkout: WorkoutHeader
    ): List<CumulativeAchievement> {
        val cumulativeAchievements = mutableListOf<CumulativeAchievement>()

        // create Nth activityType this year achievement
        // 5,10,20,30,40 etc
        if (activityTypeCountThisYear == 5L || activityTypeCountThisYear % 10L == 0L) {
            val activityTypeCountThisYearAchievement = CumulativeAchievement(
                description = ACTIVITY_TYPE_ONLY,
                activityCounts = ActivityCounts(
                    timeCategory = YEAR,
                    currentCount = 0,
                    previousCount = 0,
                    firstInCount = null, // First in n months, weeks, etc.
                    activityCount = 0,
                    activityTypeCount = activityTypeCountThisYear.toInt()
                )
            )
            sendAchievementReachedEvent(AnalyticsPropertyValue.AchievementType.CUMULATIVE_THIS_YEAR_ACTIVITY_TYPE, newWorkout)
            cumulativeAchievements.add(activityTypeCountThisYearAchievement)
        }

        // create Nth activity this year achievement
        // 5,10,20,30,40 etc
        // shown if more than one activity type in a year
        if (activityCountThisYear != activityTypeCountThisYear && (activityCountThisYear == 5L || activityCountThisYear % 10L == 0L)) {
            val activityCountThisYearAchievement = CumulativeAchievement(
                description = ACTIVITY_ONLY,
                activityCounts = ActivityCounts(
                    timeCategory = YEAR,
                    currentCount = 0,
                    previousCount = 0,
                    firstInCount = null,
                    activityCount = activityCountThisYear.toInt(),
                    activityTypeCount = 0
                )
            )
            sendAchievementReachedEvent(AnalyticsPropertyValue.AchievementType.CUMULATIVE_THIS_YEAR, newWorkout)
            cumulativeAchievements.add(activityCountThisYearAchievement)
        }

        // create Nth activity all time achievement
        // 25,50,75,100 etc
        // Not shown if user's first year
        if (activityCountThisYear != totalActivityCount && (totalActivityCount % 25L == 0L)) {
            val activityCountAllTimeAchievement = CumulativeAchievement(
                description = ACTIVITY_ONLY,
                activityCounts = ActivityCounts(
                    timeCategory = ALL_TIME,
                    currentCount = 0,
                    previousCount = 0,
                    firstInCount = null,
                    activityCount = totalActivityCount.toInt(),
                    activityTypeCount = 0
                )
            )
            sendAchievementReachedEvent(AnalyticsPropertyValue.AchievementType.CUMULATIVE_ALL_TIME, newWorkout)
            cumulativeAchievements.add(activityCountAllTimeAchievement)
        }

        // create Nth activitytype this week, M more than last week achievement
        if ((activityTypeCountPreviousWeek > 0) && (activityTypeCountThisWeek - activityTypeCountPreviousWeek > 0)) {
            val moreThanLastWeekAchievement = CumulativeAchievement(
                description = ACTIVITY_TYPE_ONLY,
                activityCounts = ActivityCounts(
                    timeCategory = WEEK,
                    currentCount = activityTypeCountThisWeek.toInt(),
                    previousCount = activityTypeCountPreviousWeek.toInt(),
                    firstInCount = null,
                    activityCount = 0,
                    activityTypeCount = 0
                )
            )
            sendAchievementReachedEvent(AnalyticsPropertyValue.AchievementType.CUMULATIVE_WEEK_ACTIVITY_TYPE, newWorkout)
            cumulativeAchievements.add(moreThanLastWeekAchievement)
        }

        return cumulativeAchievements
    }

    private fun processPriority2CumulativeAchievement(
        activityCountPreviousWeek: Long,
        activityCountCurrentWeek: Long,
        newWorkout: WorkoutHeader
    ): CumulativeAchievement? {
        // create Nth activity this week, M more than last week achievement
        if ((activityCountPreviousWeek > 0) && (activityCountCurrentWeek - activityCountPreviousWeek > 0)) {
            sendAchievementReachedEvent(AnalyticsPropertyValue.AchievementType.CUMULATIVE_WEEK, newWorkout)
            return CumulativeAchievement(
                description = ACTIVITY_ONLY,
                activityCounts = ActivityCounts(
                    timeCategory = WEEK,
                    currentCount = activityCountCurrentWeek.toInt(),
                    previousCount = activityCountPreviousWeek.toInt(),
                    firstInCount = null,
                    activityCount = 0,
                    activityTypeCount = 0
                )
            )
        }
        return null
    }

    private fun processPriority3CumulativeAchievement(
        activityTypeCountThisMonth: Long,
        newWorkout: WorkoutHeader
    ): CumulativeAchievement? {
        // create Nth activityType this month achievement
        if (activityTypeCountThisMonth > 1) {
            sendAchievementReachedEvent(AnalyticsPropertyValue.AchievementType.CUMULATIVE_MONTH_ACTIVITY_TYPE, newWorkout)
            return CumulativeAchievement(
                description = ACTIVITY_TYPE_ONLY,
                activityCounts = ActivityCounts(
                    timeCategory = MONTH,
                    currentCount = 0,
                    previousCount = 0,
                    firstInCount = null,
                    activityCount = 0,
                    activityTypeCount = activityTypeCountThisMonth.toInt()
                )
            )
        }
        return null
    }

    private suspend fun calculatePersonalBests(newWorkout: WorkoutHeader, startOfYearMillis: Long, startOfMonthMillis: Long, totalActivityTypeCount: Long): List<PersonalBestAchievement> {
        val personalBestAchievements = mutableListOf<PersonalBestAchievement>()
        // If first activityType then create "This activity sets your personal bests for this sport" -achievement
        if (totalActivityTypeCount == 1L) {
            return listOf(
                PersonalBestAchievement(
                    timeCategory = ALL_TIME,
                    valueCategory = FIRST_ACTIVITY_OF_TYPE,
                    since = 0
                )
            )
        }

        if (totalActivityTypeCount > 0) {
            processFarthestAchievementForWorkout(
                newWorkout,
                startOfMonthMillis,
                startOfYearMillis
            )?.let {
                personalBestAchievements.add(it)
            }

            processFastestAchievementForWorkout(
                newWorkout,
                startOfMonthMillis,
                startOfYearMillis
            )?.let {
                personalBestAchievements.add(it)
            }
        }

        return personalBestAchievements
    }

    private suspend fun processFastestAchievementForWorkout(
        workout: WorkoutHeader,
        startOfMonth: Long,
        startOfYear: Long
    ): PersonalBestAchievement? {
        val isFastestAllTime = calculateFastestWorkoutInPeriod(workout, 0)
        if (isFastestAllTime) {
            sendAchievementReachedEvent(AnalyticsPropertyValue.AchievementType.FASTEST, workout)
            return PersonalBestAchievement(
                ALL_TIME,
                FASTEST_ACTIVITY,
                0
            )
        }

        val isFastestThisYear = calculateFastestWorkoutInPeriod(workout, startOfYear)
        if (isFastestThisYear) {
            sendAchievementReachedEvent(AnalyticsPropertyValue.AchievementType.FASTEST_THIS_YEAR, workout)
            return PersonalBestAchievement(
                YEAR,
                FASTEST_ACTIVITY,
                0
            )
        }

        val isFastestThisMonth = calculateFastestWorkoutInPeriod(workout, startOfMonth)
        if (isFastestThisMonth) {
            sendAchievementReachedEvent(AnalyticsPropertyValue.AchievementType.FASTEST_THIS_MONTH, workout)
            return PersonalBestAchievement(
                MONTH,
                FASTEST_ACTIVITY,
                0
            )
        }
        return null
    }

    private suspend fun calculateFastestWorkoutInPeriod(
        newWorkout: WorkoutHeader,
        since: Long
    ): Boolean {
        val fastestWorkoutOfActivityType =
            workoutHeaderDataSource.loadFastestOfActivityType(
                newWorkout.activityTypeId,
                since,
                newWorkout.startTime
            )
        return fastestWorkoutOfActivityType?.let {
            newWorkout.avgSpeed > it.avgSpeed
        } ?: false
    }

    private suspend fun processFarthestAchievementForWorkout(
        workout: WorkoutHeader,
        startOfMonth: Long,
        startOfYear: Long
    ): PersonalBestAchievement? {
        val isFarthestAllTime = calculateFarthestWorkoutInPeriod(workout, 0)
        if (isFarthestAllTime) {
            sendAchievementReachedEvent(AnalyticsPropertyValue.AchievementType.FURTHEST, workout)
            return PersonalBestAchievement(
                ALL_TIME,
                FARTHEST_ACTIVITY,
                0
            )
        }

        val isFarthestThisYear = calculateFarthestWorkoutInPeriod(workout, startOfYear)
        if (isFarthestThisYear) {
            sendAchievementReachedEvent(AnalyticsPropertyValue.AchievementType.FURTHEST_THIS_YEAR, workout)
            return PersonalBestAchievement(
                YEAR,
                FARTHEST_ACTIVITY,
                0
            )
        }

        val isFarthestThisMonth = calculateFarthestWorkoutInPeriod(workout, startOfMonth)
        if (isFarthestThisMonth) {
            sendAchievementReachedEvent(AnalyticsPropertyValue.AchievementType.FURTHEST_THIS_MONTH, workout)
            return PersonalBestAchievement(
                MONTH,
                FARTHEST_ACTIVITY,
                0
            )
        }
        return null
    }

    suspend fun calculateRecordsForTrailRunning(): List<RecordItem> =
        buildList {
            val trailRunningId = CoreActivityType.TRAIL_RUNNING.id
            addLongestDistance(this, trailRunningId)
            addAll(
                listOf(
                    RecordItem(
                        personalRecordType = PersonalRecordType.FASTEST_PACE,
                        workoutHeader = workoutHeaderDataSource.loadFastestPaceOfActivityTypeInPeriod(
                            trailRunningId,
                            0,
                            Long.MAX_VALUE
                        )
                    ),
                    RecordItem(
                        personalRecordType = PersonalRecordType.HIGHEST_CLIMB,
                        workoutHeader = workoutHeaderDataSource.loadHighestClimbOfActivityType(
                            trailRunningId,
                            0,
                            Long.MAX_VALUE
                        )
                    ),
                    RecordItem(
                        personalRecordType = PersonalRecordType.HIGHEST_ALTITUDE,
                        workoutHeader = workoutHeaderDataSource.loadHighestAltitudeOfActivityType(
                            trailRunningId,
                            0,
                            Long.MAX_VALUE
                        )
                    )
                )
            )
        }

    suspend fun calculateRecordsForCycling(): List<RecordItem> =
        buildList {
            val cyclingId = CoreActivityType.CYCLING.id
            addLongestDistance(this, cyclingId)

            add(
                RecordItem(
                    personalRecordType = PersonalRecordType.HIGHEST_SPEED,
                    workoutHeader = workoutHeaderDataSource.loadFastestOfActivityType(
                        cyclingId,
                        0,
                        Long.MAX_VALUE
                    )
                )
            )
        }

    suspend fun calculateRecordsForRunning(): List<RecordItem> =
        buildList {
            val runningId = CoreActivityType.RUNNING.id
            addMarathonRecords(
                this,
                LongDistanceRunningRange.entries,
                runningId
            )

            addLongestDistance(this, runningId)

            add(
                RecordItem(
                    personalRecordType = PersonalRecordType.FASTEST_PACE,
                    workoutHeader = workoutHeaderDataSource.loadFastestPaceOfActivityTypeInPeriod(
                        runningId,
                        0,
                        Long.MAX_VALUE
                    )
                )
            )
        }

    private suspend fun addLongestDistance(
        recordItems: MutableList<RecordItem>,
        activityTypeId: Int
    ) {
        recordItems.add(
            RecordItem(
                personalRecordType = PersonalRecordType.LONGEST_DISTANCE,
                workoutHeader = workoutHeaderDataSource.loadFarthestOfActivityType(
                    activityTypeId,
                    0,
                    Long.MAX_VALUE
                )
            )
        )
    }

    private suspend fun addMarathonRecords(
        recordItems: MutableList<RecordItem>,
        longDistanceRunningRanges: List<LongDistanceRunningRange>,
        activityTypeId: Int
    ) {
        val recordTypeMap = mapOf(
            LongDistanceRunningRange.RECORD_5_KM to PersonalRecordType.RUNNING_5_KM,
            LongDistanceRunningRange.RECORD_10_KM to PersonalRecordType.RUNNING_10_KM,
            LongDistanceRunningRange.RECORD_HALF_MARATHON_KM to PersonalRecordType.MARATHON_HALF_MARATHON_KM,
            LongDistanceRunningRange.RECORD_FULL_MARATHON_KM to PersonalRecordType.MARATHON_FULL_MARATHON_KM
        )

        longDistanceRunningRanges.forEach { range ->
            recordTypeMap[range]?.let { personalRecordType ->
                recordItems.add(
                    RecordItem(
                        personalRecordType = personalRecordType,
                        workoutHeader = findWorkoutHeaderResult(range, activityTypeId)
                    )
                )
            }
        }
    }

    private suspend fun findWorkoutHeaderResult(it: LongDistanceRunningRange, activityTypeId: Int) =
        workoutHeaderDataSource.loadShortestTimeOfActivityTypeWithDistanceRange(
            activityTypeId,
            it.distanceRange,
            0,
            Long.MAX_VALUE
        )

    private suspend fun calculateFarthestWorkoutInPeriod(
        newWorkout: WorkoutHeader,
        since: Long
    ): Boolean {
        val farthestWorkoutOfActivityType =
            workoutHeaderDataSource.loadFarthestOfActivityType(
                newWorkout.activityTypeId,
                since,
                newWorkout.startTime
            )

        return farthestWorkoutOfActivityType?.let {
            val marginToExceed = min(0.02 * it.totalDistance, 500.0)
            newWorkout.totalDistance > (it.totalDistance + marginToExceed)
        } ?: false
    }

    private suspend fun calculateNewActivitiesAchievements(
        totalActivityTypeCount: Long,
        activityTypeCountThisYear: Long,
        newWorkout: WorkoutHeader,
        startOfMonth: Long
    ): CumulativeAchievement? {
        // 1. First Running. Great!
        if (totalActivityTypeCount == 1L) {
            sendAchievementReachedEvent(AnalyticsPropertyValue.AchievementType.FIRST_OF_ACTIVITY_TYPE, newWorkout)
            return CumulativeAchievement(
                description = FIRST_ACTIVITY_TYPE,
                activityCounts = ActivityCounts(
                    timeCategory = ALL_TIME,
                    currentCount = 0,
                    previousCount = 0,
                    firstInCount = null,
                    activityCount = 0,
                    activityTypeCount = 0
                )
            )
        }

        // 2. First Running This Year!
        if (activityTypeCountThisYear == 1L) {
            sendAchievementReachedEvent(AnalyticsPropertyValue.AchievementType.FIRST_THIS_YEAR, newWorkout)
            return CumulativeAchievement(
                description = FIRST_ACTIVITY_TYPE,
                activityCounts = ActivityCounts(
                    timeCategory = YEAR,
                    currentCount = 0,
                    previousCount = 0,
                    firstInCount = null,
                    activityCount = 0,
                    activityTypeCount = 0
                )
            )
        }

        val latestWorkoutOfActivityType =
            workoutHeaderDataSource.loadLatestOfActivityType(
                newWorkout.activityTypeId,
                newWorkout.startTime
            )

        latestWorkoutOfActivityType?.let {
            val newWorkoutTime = Instant.ofEpochMilli(newWorkout.startTime)
            val previousWorkoutTime = Instant.ofEpochMilli(latestWorkoutOfActivityType.startTime)
            val daysBetween = ChronoUnit.DAYS.between(previousWorkoutTime, newWorkoutTime)
            val monthsBetween = getMonthsBetween(previousWorkoutTime, newWorkoutTime)

            // 3. First Running in N Months!
            if (daysBetween > 61) {
                val firstInMonthsText = String.format(
                    Locale.US,
                    AnalyticsPropertyValue.AchievementType.FIRST_IN_N_MONTHS,
                    monthsBetween.toInt()
                )
                sendAchievementReachedEvent(firstInMonthsText, newWorkout)
                return CumulativeAchievement(
                    description = FIRST_ACTIVITY_TYPE,
                    activityCounts = ActivityCounts(
                        timeCategory = MONTH,
                        currentCount = 0,
                        previousCount = 0,
                        firstInCount = monthsBetween.toInt(),
                        activityCount = 0,
                        activityTypeCount = 0
                    )
                )
            }

            // 4. First Running in N Weeks
            if (daysBetween > 21) {
                val weeks = daysBetween / 7
                val firstInWeeksText = String.format(
                    Locale.US,
                    AnalyticsPropertyValue.AchievementType.FIRST_IN_N_WEEKS,
                    weeks.toInt()
                )
                sendAchievementReachedEvent(firstInWeeksText, newWorkout)
                return CumulativeAchievement(
                    description = FIRST_ACTIVITY_TYPE,
                    activityCounts = ActivityCounts(
                        timeCategory = WEEK,
                        currentCount = 0,
                        previousCount = 0,
                        firstInCount = weeks.toInt(),
                        activityCount = 0,
                        activityTypeCount = 0
                    )
                )
            }
        }

        // 5. First Running this month
        val activityTypeCountThisMonth = workoutHeaderDataSource.loadActivityTypeCountInPeriod(
            newWorkout.activityTypeId,
            startOfMonth,
            newWorkout.startTime
        )
        if (activityTypeCountThisMonth == 1L) {
            sendAchievementReachedEvent(AnalyticsPropertyValue.AchievementType.FIRST_THIS_MONTH, newWorkout)
            return CumulativeAchievement(
                description = FIRST_ACTIVITY_TYPE,
                activityCounts = ActivityCounts(
                    timeCategory = MONTH,
                    currentCount = 0,
                    previousCount = 0,
                    firstInCount = null,
                    activityCount = 0,
                    activityTypeCount = 1
                )
            )
        }
        return null
    }

    private fun getMonthsBetween(previousWorkoutTime: Instant, newWorkoutTime: Instant): Long {
        val localDateTimeNewWorkout = LocalDateTime.ofInstant(
            newWorkoutTime,
            ZoneId.systemDefault()
        )
        val localDateTimePreviousWorkout = LocalDateTime.ofInstant(
            previousWorkoutTime,
            ZoneId.systemDefault()
        )
        return ChronoUnit.MONTHS.between(
            YearMonth.from(localDateTimePreviousWorkout.toLocalDate()),
            YearMonth.from(localDateTimeNewWorkout.toLocalDate())
        )
    }

    private fun sendAchievementReachedEvent(achievementType: String, workout: WorkoutHeader) {
        achievementAnalytics.trackAchievementReachedEvent(achievementType, workout)
    }

    companion object {
        // Achievement time category
        const val ALL_TIME = 0
        const val YEAR = 1
        const val WEEK = 2
        const val MONTH = 3
        const val DAY = 4

        // Achievement value category
        const val FIRST_ACTIVITY_OF_TYPE = 0
        const val FARTHEST_ACTIVITY = 1
        const val FASTEST_ACTIVITY = 2

        // Achievement description category
        const val ACTIVITY_TYPE_ONLY = 0
        const val ACTIVITY_ONLY = 1
        const val ACTIVITY_AND_TYPE = 2
        const val TRAINING_DAY = 3
        const val TRAINING_WEEK = 4
        const val FIRST_ACTIVITY_TYPE = 5
        const val EARLY_BIRD = 6
    }
}
