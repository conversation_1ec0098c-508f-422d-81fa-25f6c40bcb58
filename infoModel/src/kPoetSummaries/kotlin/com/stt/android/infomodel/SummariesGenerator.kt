@file:JvmName("SummariesGenerator")

package com.stt.android.infomodel

import com.squareup.kotlinpoet.AnnotationSpec
import com.squareup.kotlinpoet.ClassName
import com.squareup.kotlinpoet.CodeBlock
import com.squareup.kotlinpoet.FileSpec
import com.squareup.kotlinpoet.FunSpec
import com.squareup.kotlinpoet.ParameterizedTypeName.Companion.parameterizedBy
import com.squareup.kotlinpoet.PropertySpec
import com.squareup.kotlinpoet.TypeSpec
import com.squareup.kotlinpoet.asClassName
import com.squareup.kotlinpoet.asTypeName
import com.squareup.moshi.Json
import com.squareup.moshi.Moshi
import com.squareup.moshi.Types
import com.squareup.moshi.kotlin.reflect.KotlinJsonAdapterFactory
import java.io.FileInputStream
import java.nio.file.Path
import java.nio.file.Paths

private const val GENERATED_FOLDER_PATH = "com.stt.android.infomodel"
const val PATH_TO_ACTIVITY_SUMMARIES_JSON = "src/kPoetSummaries/resources/activitySummaries.json"
const val PATH_TO_ACTIVITY_SUMMARY_GROUPS_JSON =
    "src/kPoetSummaries/resources/activitySummaryGroups.json"
const val PATH_TO_CATEGORIES_JSON = "src/kPoetSummaries/resources/categories.json"
const val PATH_TO_ITEMS_DESCRIPTION_JSON = "src/kPoetSummaries/resources/itemDescriptions.json"

// List matches the distance summary items in WorkoutValueFactory
private val distanceItemList = listOf(
    "Distance",
    "NauticalDistance",
    "SwimDistance",
    "DiveDistance"
)

private val ascentItemList = listOf(
    "AscentAltitude",
)

fun main(args: Array<String>) {
    val outputDirPath: Path = Paths.get(args[0])
    generateActivitySummaries(outputDir = outputDirPath)
    println("ActivitySummaries generated")
    generateCategories(outputDir = outputDirPath)
    println("SummaryCategory generated")
    generateSummaryGroups(outputDir = outputDirPath)
    println("SummaryGroups generated")
    generateItemDescriptions(outputDir = outputDirPath)
    println("ItemDescriptions generated")
}

private val moshi: Moshi = Moshi.Builder()
    .add(KotlinJsonAdapterFactory())
    .build()

private val summaryItemFromKey: (String) -> SummaryItem = { item: String ->
    SummaryItem.values().firstOrNull { it.key == item }
        ?: throw Exception("Cannot find item $item")
}

private data class ActivitySummaryJson(
    @Json(name = "Activities") val activities: List<String>,
    @Json(name = "Items") val items: List<String>,
    @Json(name = "Graphs") val graphs: List<String>,
    @Json(name = "AnalysisGraphs") val analysisGraphs: List<String>,
    @Json(name = "IntervalLaps") val intervalLaps: List<String>,
    @Json(name = "ManualLaps") val manualLaps: List<String>,
    @Json(name = "DistanceAutoLaps") val distanceAutoLaps: List<String>,
    @Json(name = "DurationAutoLaps") val durationAutoLaps: List<String>,
    @Json(name = "DownhillLaps") val downhillLaps: List<String>,
    @Json(name = "DiveAutoLaps") val diveAutoLaps: List<String>
)

private data class SummaryCategoryJson(
    @Json(name = "category") val category: String,
    @Json(name = "items") val items: List<String>
)

private data class ItemDescriptionData(
    @Json(name = "items") val items: List<String>,
    @Json(name = "titleResId") val titleResId: String?,
    @Json(name = "subtitleResId") val subtitleResId: String?,
    @Json(name = "textResId") val textResId: String,
    @Json(name = "imageResId") val imageResId: String?,
    @Json(name = "url") val url: String?,
    @Json(name = "urlText") val urlText: String?,
    @Json(name = "secondaryTextResId") val secondaryTextResId: String?
)

private data class ActivitySummaryGroupJson(
    @Json(name = "Name") val name: String,
    @Json(name = "Items") val items: List<String>,
    @Json(name = "Highlight") val highlight: List<String>,
)

private fun generateActivitySummaries(
    summaryGraphsPath: String = PATH_TO_ACTIVITY_SUMMARIES_JSON,
    outputDir: Path
) {
    val json = readJsonToString(summaryGraphsPath)
    val type = Types.newParameterizedType(List::class.java, ActivitySummaryJson::class.java)

    val mapActivity = { activity: String ->
        ActivityMapping.values().firstOrNull { it.key == activity }
            ?: throw Exception("Cannot find activity $activity")
    }
    val mapSummaryGraph = { graph: String ->
        SummaryGraph.values().firstOrNull { it.key == graph }
            ?: throw Exception("Cannot find graph $graph")
    }

    val activitySummariesJson = moshi.adapter<List<ActivitySummaryJson>>(type)
        .fromJson(json)!!

    val distinct = activitySummariesJson.hasNoDuplicatesInFields()
    if (!distinct) {
        throw Exception("Duplicate items found in ActivitySummaryJson.")
    }

    val fallbackSummary = activitySummariesJson.first {
        it.activities == listOf("Fallback")
    }.let {
        ActivitySummary(
            activities = listOf(),
            items = it.items.map(summaryItemFromKey),
            graphs = it.graphs.map(mapSummaryGraph),
            analysisGraphs = it.analysisGraphs.map(mapSummaryGraph),
            intervalLaps = it.intervalLaps.map(summaryItemFromKey),
            manualLaps = it.manualLaps.map(summaryItemFromKey),
            distanceAutoLaps = it.distanceAutoLaps.map(summaryItemFromKey),
            durationAutoLaps = it.durationAutoLaps.map(summaryItemFromKey),
            downhillLaps = it.downhillLaps.map(summaryItemFromKey),
            diveAutoLaps = it.diveAutoLaps.map(summaryItemFromKey),
            hasDistance = true,
            hasAscent = it.items.intersect(ascentItemList).isNotEmpty()
        )
    }
    
    /**
     * this mapping of string to enums ensures we don't have items in json files that
     * are not mapped to a enum value
     */
    val activitySummaries: List<ActivitySummary> =
        activitySummariesJson
            // getting rid of fallback item
            .filterNot { it.activities == listOf("Fallback") }
            .map {
                ActivitySummary(
                    activities = it.activities.map(mapActivity),
                    items = it.items.map(summaryItemFromKey),
                    graphs = it.graphs.map(mapSummaryGraph),
                    analysisGraphs = it.analysisGraphs.map(mapSummaryGraph),
                    intervalLaps = it.intervalLaps.map(summaryItemFromKey),
                    manualLaps = it.manualLaps.map(summaryItemFromKey),
                    distanceAutoLaps = it.distanceAutoLaps.map(summaryItemFromKey),
                    durationAutoLaps = it.durationAutoLaps.map(summaryItemFromKey),
                    downhillLaps = it.downhillLaps.map(summaryItemFromKey),
                    diveAutoLaps = it.diveAutoLaps.map(summaryItemFromKey),
                    hasDistance = it.items.intersect(distanceItemList).isNotEmpty(),
                    hasAscent = it.items.intersect(ascentItemList).isNotEmpty()
                )
            }

    val itemsFile = FileSpec.builder(GENERATED_FOLDER_PATH, "ActivitySummariesUtils")
    itemsFile.addAnnotation(
        AnnotationSpec.builder(JvmName::class)
            .addMember("name = %S", "ActivitySummariesUtils")
            .build())



    val activityMap = mutableMapOf<ActivityMapping, Int>()

    itemsFile.addProperty(
        PropertySpec
            .builder("ActivitySummary_fallback", ActivitySummary::class)
            .delegate(initiateActivitySummary(fallbackSummary))
            .build()
    )
    activitySummaries.forEachIndexed { index, activitySummary ->
        itemsFile.addProperty(
            PropertySpec
                .builder("ActivitySummary_$index", ActivitySummary::class)
                .delegate(initiateActivitySummary(activitySummary))
                .build()
        )
        activitySummary.activities.forEach { am ->
            activityMap[am]?.let { throw Exception("Activity $am has multiple summary definitions") }
            activityMap[am] = index
        }
    }

    itemsFile.addFunction(
        FunSpec.builder("getActivitySummaryForActivity")
            .returns(ActivitySummary::class)
            .addParameter("activityMapping", ActivityMapping::class.asTypeName().copy(nullable = true))
            .addCode("""
                |return when (activityMapping) {
                |    ${ActivityMapping.values()
                .joinToString("\n|    ") {
                    val summaryForActivity = activityMap[it]
                    if (summaryForActivity != null ) {
                        "ActivityMapping.$it -> ActivitySummary_$summaryForActivity"
                    } else {
                        "ActivityMapping.$it -> ActivitySummary_fallback"
                    }
                }}
                |    else -> ActivitySummary_fallback }
                |""".trimMargin()
                )
            .build()
    )

    val generatedItems = itemsFile.build()
    generatedItems.writeTo(outputDir)
}

val joinActivity = { it: ActivityMapping ->
    "${ActivityMapping::class.simpleName}.$it"
}
val joinSummaryItems = { it: SummaryItem ->
    "${SummaryItem::class.simpleName}.$it"
}
val joinSummaryGraphs = { it: SummaryGraph ->
    "${SummaryGraph::class.simpleName}.$it"
}

private fun List<ActivitySummaryJson>.hasNoDuplicatesInFields(): Boolean {
    forEachIndexed { index, json ->
        val duplicates = json.findDuplicateFields()
        if (duplicates.isNotEmpty()) {
            println("Duplicate items found in ActivitySummaryJson at activities: ${json.activities.joinToString()}:")
            duplicates.forEach { (field, values) ->
                println("'$field' has duplicates: ${values.joinToString()}")
            }
            return false
        }
    }
    return true
}

private fun ActivitySummaryJson.findDuplicateFields(): Map<String, Set<String>> {
    fun findDuplicates(list: List<String>): Set<String> =
        list.groupingBy { it }.eachCount().filter { it.value > 1 }.keys

    return buildMap {
        findDuplicates(activities).takeIf { it.isNotEmpty() }?.let { put("activities", it) }
        findDuplicates(items).takeIf { it.isNotEmpty() }?.let { put("items", it) }
        findDuplicates(graphs).takeIf { it.isNotEmpty() }?.let { put("graphs", it) }
        findDuplicates(analysisGraphs).takeIf { it.isNotEmpty() }?.let { put("analysisGraphs", it) }
        findDuplicates(intervalLaps).takeIf { it.isNotEmpty() }?.let { put("intervalLaps", it) }
        findDuplicates(manualLaps).takeIf { it.isNotEmpty() }?.let { put("manualLaps", it) }
        findDuplicates(distanceAutoLaps).takeIf { it.isNotEmpty() }?.let { put("distanceAutoLaps", it) }
        findDuplicates(durationAutoLaps).takeIf { it.isNotEmpty() }?.let { put("durationAutoLaps", it) }
        findDuplicates(downhillLaps).takeIf { it.isNotEmpty() }?.let { put("downhillLaps", it) }
        findDuplicates(diveAutoLaps).takeIf { it.isNotEmpty() }?.let { put("diveAutoLaps", it) }
    }
}

private fun initiateActivitySummary(activitySummary: ActivitySummary): String =
    """
    |lazy { ActivitySummary(
    |activities = listOf(${activitySummary.activities
        .joinToString(", ", transform = joinActivity)}),
    |items = listOf(${activitySummary.items
        .joinToString(", ", transform = joinSummaryItems)}),
    |graphs = listOf(${activitySummary.graphs
        .joinToString(", ", transform = joinSummaryGraphs)}),
    |analysisGraphs = listOf(${activitySummary.analysisGraphs
        .joinToString(", ", transform = joinSummaryGraphs)}),
    |intervalLaps = listOf(${activitySummary.intervalLaps
        .joinToString(", ", transform = joinSummaryItems)}),
    |manualLaps = listOf(${activitySummary.manualLaps
        .joinToString(", ", transform = joinSummaryItems)}),
    |distanceAutoLaps = listOf(${activitySummary.distanceAutoLaps
        .joinToString(", ", transform = joinSummaryItems)}),
    |durationAutoLaps = listOf(${activitySummary.durationAutoLaps
        .joinToString(", ", transform = joinSummaryItems)}),
    |downhillLaps = listOf(${activitySummary.downhillLaps
        .joinToString(", ", transform = joinSummaryItems)}),
    |diveAutoLaps = listOf(${activitySummary.diveAutoLaps
        .joinToString(", ", transform = joinSummaryItems)}),
    |hasDistance = ${activitySummary.hasDistance},
    |hasAscent = ${activitySummary.hasAscent})
    |}""".trimMargin()

private fun generateCategories(
    path: String = PATH_TO_CATEGORIES_JSON,
    outputDir: Path
) {
    val json = readJsonToString(path)
    val type = Types.newParameterizedType(List::class.java, SummaryCategoryJson::class.java)
    val categoryList = moshi.adapter<List<SummaryCategoryJson>>(type).fromJson(json)!!
        .map { SummaryCategoryJson(it.category, it.items.sorted()) }
        .sortedBy { it.category }

    val categoryFile = FileSpec.builder(GENERATED_FOLDER_PATH, "SummaryCategory")
    categoryFile.addAnnotation(AnnotationSpec.builder(JvmName::class)
        .addMember("name = %S", "SummaryCategoryUtils")
        .build())

    val enumType = TypeSpec.enumBuilder("SummaryCategory")
        .primaryConstructor(
            FunSpec.constructorBuilder()
                .addParameter("key", String::class)
                .build()
        )
        .addProperty(
            PropertySpec.builder("key", String::class)
                .initializer("key")
                .build()
        )
    for (category in categoryList) {
        enumType.addEnumConstant(category.category.uppercase(),
            TypeSpec.anonymousClassBuilder()
                .addSuperclassConstructorParameter("%S", category.category)
                .build())
    }
    categoryFile.addType(enumType.build())

    categoryFile.addProperty(
        PropertySpec.builder("SummaryCategoryMap",
            Map::class.asClassName().parameterizedBy(
                ClassName.bestGuess("com.stt.android.infomodel.SummaryCategory"),
                List::class.asClassName().parameterizedBy(
                    SummaryItem::class.java.asClassName()
                )
            )
        ).initializer(
            """
            |linkedMapOf(
            |${
            categoryList.joinToString(",\n|") {
                "SummaryCategory.${it.category.uppercase()} to listOf(${it.items.joinToString(", ") { item -> "SummaryItem.${summaryItemOf(item)}" }})"
            }
            })""".trimMargin()
        ).build()
    )

    val generatedItems = categoryFile.build()
    generatedItems.writeTo(outputDir)

}

private fun generateSummaryGroups(
    path: String = PATH_TO_ACTIVITY_SUMMARY_GROUPS_JSON,
    outputDir: Path
) {
    val json = readJsonToString(path)
    val type = Types.newParameterizedType(List::class.java, ActivitySummaryGroupJson::class.java)
    val summaryGroups = moshi.adapter<List<ActivitySummaryGroupJson>>(type).fromJson(json)!!
        .map { ActivitySummaryGroupJson(name = it.name, items = it.items, highlight = it.highlight) }

    val summaryGroupFile = FileSpec.builder(GENERATED_FOLDER_PATH, "SummaryGroup")
    summaryGroupFile.addAnnotation(AnnotationSpec.builder(JvmName::class)
        .addMember("name = %S", "SummaryGroupUtils")
        .build())

    val enumType = TypeSpec.enumBuilder("SummaryGroup")
        .primaryConstructor(
            FunSpec.constructorBuilder()
                .addParameter("key", String::class)
                .build()
        )
        .addProperty(
            PropertySpec.builder("key", String::class)
                .initializer("key")
                .build()
        )
    for (group in summaryGroups) {
        enumType.addEnumConstant(group.name.uppercase(),
            TypeSpec.anonymousClassBuilder()
                .addSuperclassConstructorParameter("%S", group.name)
                .build())
    }
    summaryGroupFile.addType(enumType.build())

    summaryGroupFile.addProperty(
        PropertySpec.builder("SummaryGroupItemMap",
            Map::class.asClassName().parameterizedBy(
                ClassName.bestGuess("com.stt.android.infomodel.SummaryGroup"),
                List::class.asClassName().parameterizedBy(
                    SummaryItem::class.java.asClassName()
                )
            )
        ).initializer(
            """
            |linkedMapOf(
            |${
            summaryGroups.joinToString(",\n|") {
                "SummaryGroup.${it.name.uppercase()} to listOf(${it.items.joinToString(", ") { item -> "SummaryItem.${summaryItemOf(item)}" }})"
            }
            })""".trimMargin()
        ).build()
    )

    summaryGroupFile.addProperty(
        PropertySpec.builder("SummaryGroupHighlightMap",
            Map::class.asClassName().parameterizedBy(
                ClassName.bestGuess("com.stt.android.infomodel.SummaryGroup"),
                List::class.asClassName().parameterizedBy(
                    SummaryItem::class.java.asClassName()
                )
            )
        ).initializer(
            """
            |linkedMapOf(
            |${
            summaryGroups.joinToString(",\n|") {
                "SummaryGroup.${it.name.uppercase()} to listOf(${it.highlight.joinToString(", ") { item -> "SummaryItem.${summaryItemOf(item)}" }})"
            }
            })""".trimMargin()
        ).build()
    )

    val generatedItems = summaryGroupFile.build()
    generatedItems.writeTo(outputDir)

}

fun generateItemDescriptions(
    path: String = PATH_TO_ITEMS_DESCRIPTION_JSON,
    outputDir: Path
) {
    val json = readJsonToString(path)
    val type = Types.newParameterizedType(Map::class.java, String::class.java, ItemDescriptionData::class.java)
    val map = moshi.adapter<Map<String, ItemDescriptionData>>(type).fromJson(json)!!

    val descriptionsFile = FileSpec.builder(GENERATED_FOLDER_PATH, "ItemDescriptionUtils")
    descriptionsFile.addAnnotation(
        AnnotationSpec.builder(JvmName::class)
            .addMember("name = %S", "ItemDescriptionUtils")
            .build())

    val itemDescriptions = map.entries.map {
        val desc = it.value
        PropertySpec.builder(it.key, SummaryItemDescription::class)
            .delegate(
                CodeBlock.of(
                    "lazy { %L(%S, %S, %S, %S, %S, %S, %S) }",
                    SummaryItemDescription::class.simpleName,
                    desc.titleResId,
                    desc.subtitleResId,
                    desc.textResId,
                    desc.imageResId,
                    desc.url,
                    desc.urlText,
                    desc.secondaryTextResId
                )
            )
            .build()

    }

    descriptionsFile.addType(
        TypeSpec.objectBuilder("ItemDescriptionUtils")
            .addProperties(itemDescriptions)
            .addFunction(
                FunSpec.builder("getDescriptionForItem")
                    .returns(SummaryItemDescription::class.asTypeName().copy(nullable = true))
                    .addAnnotation(JvmStatic::class)
                    .addParameter("item", SummaryItem::class)
                    .addCode(CodeBlock.builder()
                        .beginControlFlow("return when(item)")
                        .apply {
                            map.entries.forEach { entry ->
                                entry.value.items.forEach { item ->
                                    val summaryItem = SummaryItem.values().first { it.key == item }
                                    addStatement("%T.%L -> %L", SummaryItem::class, summaryItem, entry.key)
                                }
                            }
                            addStatement("else -> null")
                        }
                        .endControlFlow()
                        .build())
                    .build()
            )
            .build()
    )

    val generatedItems = descriptionsFile.build()
    generatedItems.writeTo(outputDir)
}

private fun readJsonToString(path: String) = FileInputStream(path).bufferedReader().use { it.readText() }
