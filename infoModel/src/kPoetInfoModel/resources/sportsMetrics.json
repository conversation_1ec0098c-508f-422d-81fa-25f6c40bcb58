[{"groupId": "SPJ", "groupName": "TXT_OTHER", "activities": [1, 22, 83, 11, 2, 85, 21, 53, 52, 102, 57, 55, 69, 23, 77, 51, 3, 0, 70, 29, 60, 54, 73, 58, 13, 10, 30, 34, 36, 103, 9, 17, 115], "values": [{"valueId": "SPJ001", "valuePhrase": "TXT_BATTERY", "metricUnit": "%", "exampleStr": "90", "imperialUnit": "%", "imperialExampleStr": "90", "activities": [1, 22, 83, 11, 2, 85, 21, 53, 52, 102, 57, 55, 69, 23, 77, 51, 3, 0, 70, 29, 60, 54, 73, 58, 13, 10, 30, 34, 36, 103, 9, 17, 115]}, {"valueId": "SPJ002", "valuePhrase": "TXT_AVG_GROUND_CONTACT_TIME_BALANCE", "metricUnit": "%", "exampleStr": "50-50", "imperialUnit": "%", "imperialExampleStr": "50-50", "activities": [1, 22, 83, 53, 103]}, {"valueId": "SPJ003", "valuePhrase": "TXT_SWOLF", "metricUnit": "", "exampleStr": "652", "imperialUnit": "", "imperialExampleStr": "652", "activities": [21]}], "manualLapValues": [{"valueId": "SPJ002_1", "valuePhrase": "TXT_MANUAL_LAP_AVG_GROUND_CONTACT_TIME_BALANCE", "metricUnit": "%", "exampleStr": "50-50", "imperialUnit": "%", "imperialExampleStr": "50-50", "activities": [1, 22, 83, 53, 103]}], "autoLapValues": [{"valueId": "SPJ002_2", "valuePhrase": "TXT_AUTO_LAP_AVG_GROUND_CONTACT_TIME_BALANCE", "metricUnit": "%", "exampleStr": "50-50", "imperialUnit": "%", "imperialExampleStr": "50-50", "activities": [1, 22, 83, 53, 103]}], "intervalValues": [{"valueId": "SPJ002_3", "valuePhrase": "TXT_INTERVAL_AVG_GROUND_CONTACT_TIME_BALANCE", "metricUnit": "%", "exampleStr": "50-50", "imperialUnit": "%", "imperialExampleStr": "50-50", "activities": [1, 22, 83, 53, 103]}], "currentLapValues": [{"valueId": "SPJ003_4", "valuePhrase": "TXT_CURRENT_SEGMENT_SWOLF", "metricUnit": "", "exampleStr": "652", "imperialUnit": "", "imperialExampleStr": "652", "activities": [21]}]}, {"groupId": "SPH", "groupName": "TXT_ENVIRONMENT", "activities": [0, 1, 2, 3, 10, 11, 13, 21, 22, 29, 30, 53, 60, 83, 70, 85, 103, 9], "values": [{"valueId": "SPH001", "valuePhrase": "TXT_TEMPERATURE", "metricUnit": "℃", "exampleStr": "23", "imperialUnit": "℉", "imperialExampleStr": "77", "activities": [0, 1, 2, 3, 10, 11, 13, 21, 22, 29, 30, 53, 60, 83, 70, 85, 103, 9]}, {"valueId": "SPH002", "valuePhrase": "TXT_SUNRISE", "metricUnit": "", "exampleStr": "06:00", "imperialUnit": "", "imperialExampleStr": "06:00", "activities": [0, 1, 2, 3, 10, 11, 13, 22, 29, 30, 60, 83, 70, 85, 103, 9]}, {"valueId": "SPH003", "valuePhrase": "TXT_SUNSET", "metricUnit": "", "exampleStr": "18:00", "imperialUnit": "", "imperialExampleStr": "18:00", "activities": [0, 1, 2, 3, 10, 11, 13, 22, 29, 30, 60, 83, 70, 85, 103, 9]}, {"valueId": "SPH004", "valuePhrase": "TXT_SEA_LEVEL_PRESSURE", "metricUnit": "hPa", "exampleStr": "1013", "imperialUnit": "inHg", "imperialExampleStr": "29.92", "activities": [0, 1, 2, 3, 10, 11, 13, 22, 29, 30, 60, 83, 70, 103, 9]}], "autoLapValues": [], "manualLapValues": [], "intervalValues": [], "currentLapValues": []}, {"groupId": "SPF", "groupName": "TXT_PHYSIOLOGY", "activities": [1, 22, 83, 11, 2, 85, 21, 53, 52, 102, 57, 55, 69, 23, 77, 51, 3, 0, 70, 29, 60, 54, 10, 73, 58, 13, 30, 34, 36, 103, 9, 17, 115], "values": [{"valueId": "SPF001", "valuePhrase": "TXT_TOTAL_ACTIVITY_ENERGY_EXPENDITURE", "metricUnit": "kcal", "exampleStr": "356", "imperialUnit": "kcal", "imperialExampleStr": "356", "activities": [1, 22, 83, 11, 2, 53, 52, 102, 57, 55, 69, 23, 77, 51, 3, 0, 70, 29, 60, 54, 10, 73, 58, 13, 30, 34, 36, 103, 9, 17, 115]}, {"valueId": "SPF002", "valuePhrase": "TXT_TOTAL_ENERGY_EXPENDITURE", "metricUnit": "kcal", "exampleStr": "500", "imperialUnit": "kcal", "imperialExampleStr": "500", "activities": [1, 22, 83, 11, 2, 53, 52, 102, 57, 55, 69, 23, 77, 51, 3, 0, 70, 29, 60, 54, 10, 73, 58, 13, 30, 34, 36, 103, 9, 17, 115]}, {"valueId": "SPF003", "valuePhrase": "TXT_FAT_EXPENDITURE_RATE", "metricUnit": "g/h", "exampleStr": "23", "imperialUnit": "oz/h", "imperialExampleStr": "12", "activities": [1, 22, 83, 11, 2, 53, 52, 102, 57, 55, 69, 23, 77, 51, 3, 0, 70, 29, 60, 54, 10, 73, 58, 13, 30, 34, 36, 103, 9, 17, 115]}, {"valueId": "SPF004", "valuePhrase": "TXT_CARBOHYDRATE_EXPENDITURE_RATE", "metricUnit": "g/h", "exampleStr": "75", "imperialUnit": "oz/h", "imperialExampleStr": "21", "activities": [1, 22, 83, 11, 2, 53, 52, 102, 57, 55, 69, 23, 77, 51, 3, 0, 70, 29, 60, 54, 10, 73, 58, 13, 30, 34, 36, 103, 9, 17, 115]}, {"valueId": "SPF005", "valuePhrase": "TXT_FAT_EXPENDITURE", "metricUnit": "g", "exampleStr": "23", "imperialUnit": "oz", "imperialExampleStr": "12", "activities": [1, 22, 83, 11, 2, 53, 52, 102, 57, 55, 69, 23, 77, 51, 3, 0, 70, 29, 60, 54, 10, 73, 58, 13, 30, 34, 36, 103, 9, 17, 115]}, {"valueId": "SPF006", "valuePhrase": "TXT_CARBOHYDRATE_EXPENDITURE", "metricUnit": "g", "exampleStr": "23", "imperialUnit": "oz", "imperialExampleStr": "12", "activities": [1, 22, 83, 11, 2, 53, 52, 102, 57, 55, 69, 23, 77, 51, 3, 0, 70, 29, 60, 54, 10, 73, 58, 13, 30, 34, 36, 103, 9, 17, 115]}, {"valueId": "SPF007", "valuePhrase": "TXT_FAT_OXIDATION_PERCENTAGE", "metricUnit": "%", "exampleStr": "30", "imperialUnit": "%", "imperialExampleStr": "30", "activities": [1, 22, 83, 11, 2, 53, 52, 102, 57, 55, 69, 23, 77, 51, 3, 0, 70, 29, 60, 54, 10, 73, 58, 13, 30, 34, 36, 103, 9, 17, 115]}, {"valueId": "SPF008", "valuePhrase": "TXT_TRAINING_STRESS_SCORE", "metricUnit": "TSS", "exampleStr": "23", "imperialUnit": "TSS", "imperialExampleStr": "23", "activities": [1, 22, 83, 11, 2, 85, 21, 53, 52, 102, 57, 55, 69, 23, 77, 51, 3, 0, 70, 29, 60, 54, 10, 73, 58, 13, 30, 34, 36, 103, 9, 17, 115]}], "autoLapValues": [], "manualLapValues": [], "intervalValues": [], "currentLapValues": []}, {"groupId": "SPI", "groupName": "TXT_POWER", "activities": [1, 22, 103, 2, 52, 10], "values": [{"valueId": "SPI001", "valuePhrase": "TXT_RUNNING_POWER", "metricUnit": "W", "exampleStr": "548", "imperialUnit": "W", "imperialExampleStr": "548", "activities": [1, 22, 103]}, {"valueId": "SPI002", "valuePhrase": "TXT_AVG_RUNNING_POWER", "metricUnit": "W", "exampleStr": "548", "imperialUnit": "W", "imperialExampleStr": "548", "activities": [1, 22, 103]}, {"valueId": "SPI003", "valuePhrase": "TXT_RUNNING_POWER_3S", "metricUnit": "W", "exampleStr": "548", "imperialUnit": "W", "imperialExampleStr": "548", "activities": [1, 22, 103]}, {"valueId": "SPI004", "valuePhrase": "TXT_RUNNING_POWER_10S", "metricUnit": "W", "exampleStr": "548", "imperialUnit": "W", "imperialExampleStr": "548", "activities": [1, 22, 103]}, {"valueId": "SPI005", "valuePhrase": "TXT_RUNNING_POWER_30S", "metricUnit": "W", "exampleStr": "548", "imperialUnit": "W", "imperialExampleStr": "548", "activities": [1, 22, 103]}, {"valueId": "SPI006", "valuePhrase": "TXT_CYCLING_POWER", "metricUnit": "W", "exampleStr": "548", "imperialUnit": "W", "imperialExampleStr": "548", "activities": [2, 52, 10]}, {"valueId": "SPI007", "valuePhrase": "TXT_AVG_CYCLING_POWER", "metricUnit": "W", "exampleStr": "548", "imperialUnit": "W", "imperialExampleStr": "548", "activities": [2, 52, 10]}, {"valueId": "SPI008", "valuePhrase": "TXT_CYCLING_POWER_3S", "metricUnit": "W", "exampleStr": "548", "imperialUnit": "W", "imperialExampleStr": "548", "activities": [2, 52, 10]}, {"valueId": "SPI009", "valuePhrase": "TXT_CYCLING_POWER_10S", "metricUnit": "W", "exampleStr": "548", "imperialUnit": "W", "imperialExampleStr": "548", "activities": [2, 52, 10]}, {"valueId": "SPI010", "valuePhrase": "TXT_CYCLING_POWER_30S", "metricUnit": "W", "exampleStr": "548", "imperialUnit": "W", "imperialExampleStr": "548", "activities": [2, 52, 10]}], "manualLapValues": [{"valueId": "SPI002_1", "valuePhrase": "TXT_MANUAL_LAP_AVG_RUNNING_POWER", "metricUnit": "W", "exampleStr": "548", "imperialUnit": "W", "imperialExampleStr": "548", "activities": [1, 22, 103]}, {"valueId": "SPI007_1", "valuePhrase": "TXT_MANUAL_LAP_AVG_CYCLING_POWER", "metricUnit": "W", "exampleStr": "548", "imperialUnit": "W", "imperialExampleStr": "548", "activities": [2, 52, 10]}], "autoLapValues": [{"valueId": "SPI002_2", "valuePhrase": "TXT_AUTO_LAP_AVG_RUNNING_POWER", "metricUnit": "W", "exampleStr": "548", "imperialUnit": "W", "imperialExampleStr": "548", "activities": [1, 22, 103]}, {"valueId": "SPI007_2", "valuePhrase": "TXT_AUTO_LAP_AVG_CYCLING_POWER", "metricUnit": "W", "exampleStr": "548", "imperialUnit": "W", "imperialExampleStr": "548", "activities": [2, 52, 10]}], "intervalValues": [{"valueId": "SPI002_3", "valuePhrase": "TXT_INTERVAL_AVG_RUNNING_POWER", "metricUnit": "W", "exampleStr": "548", "imperialUnit": "W", "imperialExampleStr": "548", "activities": [1, 22, 103]}, {"valueId": "SPI007_3", "valuePhrase": "TXT_INTERVAL_AVG_CYCLING_POWER", "metricUnit": "W", "exampleStr": "548", "imperialUnit": "W", "imperialExampleStr": "548", "activities": [2, 52, 10]}], "currentLapValues": []}, {"groupId": "SPD", "groupName": "TXT_VERTICAL", "activities": [1, 22, 83, 11, 2, 53, 3, 0, 70, 29, 60, 10, 13, 30, 103, 9, 115], "values": [{"valueId": "SPD001", "valuePhrase": "TXT_ASCENT", "metricUnit": "m", "exampleStr": "548", "imperialUnit": "ft", "imperialExampleStr": "548", "activities": [1, 22, 83, 11, 2, 3, 0, 70, 29, 60, 10, 13, 30, 103, 9, 115]}, {"valueId": "SPD002", "valuePhrase": "TXT_DESCENT", "metricUnit": "m", "exampleStr": "345", "imperialUnit": "ft", "imperialExampleStr": "345", "activities": [1, 22, 83, 11, 2, 3, 0, 70, 29, 60, 10, 13, 30, 103, 9, 115]}, {"valueId": "SPD003", "valuePhrase": "TXT_ALTITUDE", "metricUnit": "m", "exampleStr": "4356", "imperialUnit": "ft", "imperialExampleStr": "4356", "activities": [1, 22, 83, 11, 2, 3, 0, 70, 29, 60, 10, 13, 30, 103, 9, 115]}, {"valueId": "SPD004", "valuePhrase": "TXT_MAX_ALTITUDE", "metricUnit": "m", "exampleStr": "4356", "imperialUnit": "ft", "imperialExampleStr": "4356", "activities": [1, 22, 83, 11, 2, 3, 0, 70, 29, 60, 10, 13, 30, 103, 9, 115]}, {"valueId": "SPD005", "valuePhrase": "TXT_MIN_ALTITUDE", "metricUnit": "m", "exampleStr": "2346", "imperialUnit": "ft", "imperialExampleStr": "2346", "activities": [1, 22, 83, 11, 2, 3, 0, 70, 29, 60, 10, 13, 30, 103, 9, 115]}, {"valueId": "SPD006", "valuePhrase": "TXT_VERTICAL_OSCILLATION", "metricUnit": "cm", "exampleStr": "5", "imperialUnit": "in", "imperialExampleStr": "2", "activities": [1, 22, 53, 103]}, {"valueId": "SPD007", "valuePhrase": "TXT_AVG_VERTICAL_OSCILLATION", "metricUnit": "cm", "exampleStr": "8", "imperialUnit": "in", "imperialExampleStr": "3", "activities": [1, 22, 53, 103]}], "manualLapValues": [{"valueId": "SPD001_1", "valuePhrase": "TXT_MANUAL_LAP_ASCENT", "metricUnit": "m", "exampleStr": "548", "imperialUnit": "ft", "imperialExampleStr": "548", "activities": [1, 22, 83, 11, 2, 3, 0, 70, 29, 60, 10, 13, 30, 103, 9, 115]}, {"valueId": "SPD002_1", "valuePhrase": "TXT_MANUAL_LAP_DESCENT", "metricUnit": "m", "exampleStr": "345", "imperialUnit": "ft", "imperialExampleStr": "345", "activities": [1, 22, 83, 11, 2, 3, 0, 70, 29, 60, 10, 13, 30, 103, 9, 115]}, {"valueId": "SPD004_1", "valuePhrase": "TXT_MANUAL_LAP_MAX_ALTITUDE", "metricUnit": "m", "exampleStr": "4356", "imperialUnit": "ft", "imperialExampleStr": "4356", "activities": [1, 22, 83, 11, 2, 3, 0, 70, 29, 60, 10, 13, 30, 103, 9, 115]}, {"valueId": "SPD005_1", "valuePhrase": "TXT_MANUAL_LAP_MIN_ALTITUDE", "metricUnit": "m", "exampleStr": "2346", "imperialUnit": "ft", "imperialExampleStr": "2346", "activities": [1, 22, 83, 11, 2, 3, 0, 70, 29, 60, 10, 13, 30, 103, 9, 115]}, {"valueId": "SPD007_1", "valuePhrase": "TXT_MANUAL_LAP_AVG_VERTICAL_OSCILLATION", "metricUnit": "cm", "exampleStr": "8", "imperialUnit": "in", "imperialExampleStr": "3", "activities": [1, 22, 53, 103]}], "autoLapValues": [{"valueId": "SPD001_2", "valuePhrase": "TXT_AUTO_LAP_ASCENT", "metricUnit": "m", "exampleStr": "548", "imperialUnit": "ft", "imperialExampleStr": "548", "activities": [1, 22, 83, 11, 2, 3, 0, 70, 29, 60, 10, 13, 30, 103, 9, 115]}, {"valueId": "SPD002_2", "valuePhrase": "TXT_AUTO_LAP_DESCENT", "metricUnit": "m", "exampleStr": "345", "imperialUnit": "ft", "imperialExampleStr": "345", "activities": [1, 22, 83, 11, 2, 3, 0, 70, 29, 60, 10, 13, 30, 103, 9, 115]}, {"valueId": "SPD004_2", "valuePhrase": "TXT_AUTO_LAP_MAX_ALTITUDE", "metricUnit": "m", "exampleStr": "4356", "imperialUnit": "ft", "imperialExampleStr": "4356", "activities": [1, 22, 83, 11, 2, 3, 0, 70, 29, 60, 10, 13, 30, 103, 9, 115]}, {"valueId": "SPD005_2", "valuePhrase": "TXT_AUTO_LAP_MIN_ALTITUDE", "metricUnit": "m", "exampleStr": "2346", "imperialUnit": "ft", "imperialExampleStr": "2346", "activities": [1, 22, 83, 11, 2, 3, 0, 70, 29, 60, 10, 13, 30, 103, 9, 115]}, {"valueId": "SPD007_2", "valuePhrase": "TXT_AUTO_LAP_AVG_VERTICAL_OSCILLATION", "metricUnit": "cm", "exampleStr": "8", "imperialUnit": "in", "imperialExampleStr": "3", "activities": [1, 22, 53, 103]}], "intervalValues": [{"valueId": "SPD001_3", "valuePhrase": "TXT_INTERVAL_ASCENT", "metricUnit": "m", "exampleStr": "548", "imperialUnit": "ft", "imperialExampleStr": "548", "activities": [1, 22, 83, 11, 2, 3, 0, 70, 29, 60, 10, 13, 30, 103, 9, 115]}, {"valueId": "SPD002_3", "valuePhrase": "TXT_INTERVAL_DESCENT", "metricUnit": "m", "exampleStr": "345", "imperialUnit": "ft", "imperialExampleStr": "345", "activities": [1, 22, 83, 11, 2, 3, 0, 70, 29, 60, 10, 13, 30, 103, 9, 115]}, {"valueId": "SPD004_3", "valuePhrase": "TXT_INTERVAL_MAX_ALTITUDE", "metricUnit": "m", "exampleStr": "4356", "imperialUnit": "ft", "imperialExampleStr": "4356", "activities": [1, 22, 83, 11, 2, 3, 0, 70, 29, 60, 10, 13, 30, 103, 9, 115]}, {"valueId": "SPD005_3", "valuePhrase": "TXT_INTERVAL_MIN_ALTITUDE", "metricUnit": "m", "exampleStr": "2346", "imperialUnit": "ft", "imperialExampleStr": "2346", "activities": [1, 22, 83, 11, 2, 3, 0, 70, 29, 60, 10, 13, 30, 103, 9, 115]}, {"valueId": "SPD007_3", "valuePhrase": "TXT_INTERVAL_AVG_VERTICAL_OSCILLATION", "metricUnit": "cm", "exampleStr": "8", "imperialUnit": "in", "imperialExampleStr": "3", "activities": [1, 22, 53, 103]}], "currentLapValues": []}, {"groupId": "SPA", "groupName": "TXT_HEART_RATE", "activities": [1, 22, 83, 11, 2, 53, 52, 102, 57, 55, 58, 69, 23, 77, 51, 3, 0, 70, 29, 60, 10, 13, 30, 34, 36, 103, 54, 73, 9, 17, 115], "values": [{"valueId": "SPA001", "valuePhrase": "TXT_HEART_RATE", "metricUnit": "bpm", "exampleStr": "125", "imperialUnit": "bpm", "imperialExampleStr": "125", "activities": [1, 22, 83, 11, 2, 53, 52, 102, 57, 55, 58, 69, 23, 77, 51, 3, 0, 70, 29, 60, 10, 13, 30, 34, 36, 103, 54, 73, 9, 17, 115]}, {"valueId": "SPA002", "valuePhrase": "TXT_AVG_HEART_RATE", "metricUnit": "bpm", "exampleStr": "130", "imperialUnit": "bpm", "imperialExampleStr": "130", "activities": [1, 22, 83, 11, 2, 53, 52, 102, 57, 55, 58, 69, 23, 77, 51, 3, 0, 70, 29, 60, 10, 13, 30, 34, 36, 103, 54, 73, 9, 17, 115]}, {"valueId": "SPA003", "valuePhrase": "TXT_MAX_HEART_RATE", "metricUnit": "bpm", "exampleStr": "150", "imperialUnit": "bpm", "imperialExampleStr": "150", "activities": [1, 22, 83, 11, 2, 53, 52, 102, 57, 55, 58, 69, 23, 77, 51, 3, 0, 70, 29, 60, 10, 13, 30, 34, 36, 103, 54, 73, 9, 17, 115]}, {"valueId": "SPA004", "valuePhrase": "TXT_MIN_HEART_RATE", "metricUnit": "bpm", "exampleStr": "110", "imperialUnit": "bpm", "imperialExampleStr": "110", "activities": [1, 22, 83, 11, 2, 53, 52, 102, 57, 55, 58, 69, 23, 77, 51, 3, 0, 70, 29, 60, 10, 13, 30, 34, 36, 103, 54, 73, 9, 17, 115]}], "manualLapValues": [{"valueId": "SPA002_1", "valuePhrase": "TXT_MANUAL_LAP_AVG_HEART_RATE", "metricUnit": "bpm", "exampleStr": "130", "imperialUnit": "bpm", "imperialExampleStr": "130", "activities": [1, 22, 83, 11, 2, 53, 52, 102, 57, 55, 58, 69, 23, 77, 51, 3, 0, 70, 29, 60, 10, 13, 30, 34, 36, 103, 54, 73, 9, 17, 115]}, {"valueId": "SPA003_1", "valuePhrase": "TXT_MANUAL_LAP_MAX_HEART_RATE", "metricUnit": "bpm", "exampleStr": "150", "imperialUnit": "bpm", "imperialExampleStr": "150", "activities": [1, 22, 83, 11, 2, 53, 52, 102, 57, 55, 58, 69, 23, 77, 51, 3, 0, 70, 29, 60, 10, 13, 30, 34, 36, 103, 54, 73, 9, 17, 115]}, {"valueId": "SPA004_1", "valuePhrase": "TXT_MANUAL_LAP_MIN_HEART_RATE", "metricUnit": "bpm", "exampleStr": "110", "imperialUnit": "bpm", "imperialExampleStr": "110", "activities": [1, 22, 83, 11, 2, 53, 52, 102, 57, 55, 58, 69, 23, 77, 51, 3, 0, 70, 29, 60, 10, 13, 30, 34, 36, 103, 54, 73, 9, 17, 115]}], "autoLapValues": [{"valueId": "SPA002_2", "valuePhrase": "TXT_AUTO_LAP_AVG_HEART_RATE", "metricUnit": "bpm", "exampleStr": "130", "imperialUnit": "bpm", "imperialExampleStr": "130", "activities": [1, 22, 83, 11, 2, 53, 52, 102, 57, 55, 58, 69, 23, 77, 51, 3, 0, 70, 29, 60, 10, 13, 30, 34, 36, 103, 54, 73, 9, 17, 115]}, {"valueId": "SPA003_2", "valuePhrase": "TXT_AUTO_LAP_MAX_HEART_RATE", "metricUnit": "bpm", "exampleStr": "150", "imperialUnit": "bpm", "imperialExampleStr": "150", "activities": [1, 22, 83, 11, 2, 53, 52, 102, 57, 55, 58, 69, 23, 77, 51, 3, 0, 70, 29, 60, 10, 13, 30, 34, 36, 103, 54, 73, 9, 17, 115]}, {"valueId": "SPA004_2", "valuePhrase": "TXT_AUTO_LAP_MIN_HEART_RATE", "metricUnit": "bpm", "exampleStr": "110", "imperialUnit": "bpm", "imperialExampleStr": "110", "activities": [1, 22, 83, 11, 2, 53, 52, 102, 57, 55, 58, 69, 23, 77, 51, 3, 0, 70, 29, 60, 10, 13, 30, 34, 36, 103, 54, 73, 9, 17, 115]}], "intervalValues": [{"valueId": "SPA002_3", "valuePhrase": "TXT_INTERVAL_AVG_HEART_RATE", "metricUnit": "bpm", "exampleStr": "130", "imperialUnit": "bpm", "imperialExampleStr": "130", "activities": [1, 22, 83, 11, 2, 53, 52, 102, 57, 55, 58, 69, 23, 77, 51, 3, 0, 70, 29, 60, 10, 13, 30, 34, 36, 103, 54, 73, 9, 17, 115]}, {"valueId": "SPA003_3", "valuePhrase": "TXT_INTERVAL_MAX_HEART_RATE", "metricUnit": "bpm", "exampleStr": "150", "imperialUnit": "bpm", "imperialExampleStr": "150", "activities": [1, 22, 83, 11, 2, 53, 52, 102, 57, 55, 58, 69, 23, 77, 51, 3, 0, 70, 29, 60, 10, 13, 30, 34, 36, 103, 54, 73, 9, 17, 115]}, {"valueId": "SPA004_3", "valuePhrase": "TXT_INTERVAL_MIN_HEART_RATE", "metricUnit": "bpm", "exampleStr": "110", "imperialUnit": "bpm", "imperialExampleStr": "110", "activities": [1, 22, 83, 11, 2, 53, 52, 102, 57, 55, 58, 69, 23, 77, 51, 3, 0, 70, 29, 60, 10, 13, 30, 34, 36, 103, 54, 73, 9, 17, 115]}], "currentLapValues": []}, {"groupId": "SPG", "groupName": "TXT_COUNT", "activities": [1, 22, 83, 11, 85, 21, 53, 102, 57, 55, 0, 70, 60, 103, 115], "values": [{"valueId": "SPG001", "valuePhrase": "TXT_STEPS", "metricUnit": "", "exampleStr": "5625", "imperialUnit": "", "imperialExampleStr": "5625", "activities": [1, 22, 83, 11, 53, 0, 70, 60, 103, 115]}, {"valueId": "SPG002", "valuePhrase": "TXT_SWIM_STROKES", "metricUnit": "", "exampleStr": "652", "imperialUnit": "", "imperialExampleStr": "652", "activities": [85, 21]}, {"valueId": "SPG003", "valuePhrase": "TXT_JUMPS", "metricUnit": "", "exampleStr": "200", "imperialUnit": "", "imperialExampleStr": "200", "activities": [102]}, {"valueId": "SPG004", "valuePhrase": "TXT_MAX_CONTINUOUS_JUMPS", "metricUnit": "", "exampleStr": "200", "imperialUnit": "", "imperialExampleStr": "200", "activities": [102]}, {"valueId": "SPG005", "valuePhrase": "TXT_ROWING_STROKES", "metricUnit": "", "exampleStr": "242", "imperialUnit": "", "imperialExampleStr": "242", "activities": [57]}, {"valueId": "SPG006", "valuePhrase": "TXT_CROSSTRAINER_STEPS", "metricUnit": "", "exampleStr": "600", "imperialUnit": "", "imperialExampleStr": "600", "activities": [55]}], "manualLapValues": [{"valueId": "SPG001_1", "valuePhrase": "TXT_MANUAL_LAP_STEPS", "metricUnit": "", "exampleStr": "5625", "imperialUnit": "", "imperialExampleStr": "5625", "activities": [1, 22, 83, 11, 53, 0, 70, 60, 103, 115]}, {"valueId": "SPG002_1", "valuePhrase": "TXT_MANUAL_LAP_SWIM_STROKES", "metricUnit": "", "exampleStr": "652", "imperialUnit": "", "imperialExampleStr": "652", "activities": [85, 21]}, {"valueId": "SPG003_1", "valuePhrase": "TXT_MANUAL_LAP_JUMPS", "metricUnit": "", "exampleStr": "200", "imperialUnit": "", "imperialExampleStr": "200", "activities": [102]}, {"valueId": "SPG005_1", "valuePhrase": "TXT_MANUAL_LAP_ROWING_STROKES", "metricUnit": "", "exampleStr": "242", "imperialUnit": "", "imperialExampleStr": "242", "activities": [57]}, {"valueId": "SPG006_1", "valuePhrase": "TXT_MANUAL_LAP_CROSSTRAINER_STEPS", "metricUnit": "", "exampleStr": "600", "imperialUnit": "", "imperialExampleStr": "600", "activities": [55]}], "autoLapValues": [{"valueId": "SPG001_2", "valuePhrase": "TXT_AUTO_LAP_STEPS", "metricUnit": "", "exampleStr": "5625", "imperialUnit": "", "imperialExampleStr": "5625", "activities": [1, 22, 83, 11, 53, 0, 70, 60, 103, 115]}, {"valueId": "SPG002_2", "valuePhrase": "TXT_AUTO_LAP_SWIM_STROKES", "metricUnit": "", "exampleStr": "652", "imperialUnit": "", "imperialExampleStr": "652", "activities": [85, 21]}, {"valueId": "SPG003_2", "valuePhrase": "TXT_AUTO_LAP_JUMPS", "metricUnit": "", "exampleStr": "200", "imperialUnit": "", "imperialExampleStr": "200", "activities": [102]}, {"valueId": "SPG005_2", "valuePhrase": "TXT_AUTO_LAP_ROWING_STROKES", "metricUnit": "", "exampleStr": "242", "imperialUnit": "", "imperialExampleStr": "242", "activities": [57]}, {"valueId": "SPG006_2", "valuePhrase": "TXT_AUTO_LAP_CROSSTRAINER_STEPS", "metricUnit": "", "exampleStr": "600", "imperialUnit": "", "imperialExampleStr": "600", "activities": [55]}], "intervalValues": [{"valueId": "SPG001_3", "valuePhrase": "TXT_INTERVAL_STEPS", "metricUnit": "", "exampleStr": "5625", "imperialUnit": "", "imperialExampleStr": "5625", "activities": [1, 22, 83, 11, 53, 0, 70, 60, 103, 115]}, {"valueId": "SPG002_3", "valuePhrase": "TXT_INTERVAL_SWIM_STROKES", "metricUnit": "", "exampleStr": "652", "imperialUnit": "", "imperialExampleStr": "652", "activities": [85, 21]}, {"valueId": "SPG003_3", "valuePhrase": "TXT_INTERVAL_JUMPS", "metricUnit": "", "exampleStr": "200", "imperialUnit": "", "imperialExampleStr": "200", "activities": [102]}, {"valueId": "SPG005_3", "valuePhrase": "TXT_INTERVAL_ROWING_STROKES", "metricUnit": "", "exampleStr": "242", "imperialUnit": "", "imperialExampleStr": "242", "activities": [57]}, {"valueId": "SPG006_3", "valuePhrase": "TXT_INTERVAL_CROSSTRAINER_STEPS", "metricUnit": "", "exampleStr": "600", "imperialUnit": "", "imperialExampleStr": "600", "activities": [55]}], "currentLapValues": [{"valueId": "SPG002_4", "valuePhrase": "TXT_CURRENT_SEGMENT_SWIM_STROKES", "metricUnit": "", "exampleStr": "652", "imperialUnit": "", "imperialExampleStr": "652", "activities": [21]}]}, {"groupId": "SPK", "groupName": "TXT_RHYTHM", "activities": [1, 22, 85, 21, 53, 102, 57, 55, 103, 9], "values": [{"valueId": "SPK019", "valuePhrase": "TXT_CADENCE", "metricUnit": "spm", "exampleStr": "30", "imperialUnit": "spm", "imperialExampleStr": "30", "activities": [1, 22, 53, 103, 9]}, {"valueId": "SPK020", "valuePhrase": "TXT_AVG_CADENCE", "metricUnit": "spm", "exampleStr": "30", "imperialUnit": "spm", "imperialExampleStr": "30", "activities": [1, 22, 53, 103, 9]}, {"valueId": "SPK021", "valuePhrase": "TXT_MAX_CADENCE", "metricUnit": "spm", "exampleStr": "30", "imperialUnit": "spm", "imperialExampleStr": "30", "activities": [1, 22, 53, 103, 9]}, {"valueId": "SPK022", "valuePhrase": "TXT_AVG_SWIM_STROKE_RATE", "metricUnit": "spm", "exampleStr": "30", "imperialUnit": "spm", "imperialExampleStr": "30", "activities": [85, 21]}, {"valueId": "SPK026", "valuePhrase": "TXT_JUMP_ROPE_CADENCE", "metricUnit": "spm", "exampleStr": "30", "imperialUnit": "spm", "imperialExampleStr": "30", "activities": [102]}, {"valueId": "SPK027", "valuePhrase": "TXT_AVG_JUMP_ROPE_CADENCE", "metricUnit": "spm", "exampleStr": "30", "imperialUnit": "spm", "imperialExampleStr": "30", "activities": [102]}, {"valueId": "SPK028", "valuePhrase": "TXT_ROWING_STROKE_RATE", "metricUnit": "spm", "exampleStr": "30", "imperialUnit": "spm", "imperialExampleStr": "30", "activities": [57]}, {"valueId": "SPK029", "valuePhrase": "TXT_AVG_ROWING_STROKE_RATE", "metricUnit": "spm", "exampleStr": "30", "imperialUnit": "spm", "imperialExampleStr": "30", "activities": [57]}, {"valueId": "SPK030", "valuePhrase": "TXT_CROSSTRAINER_CADENCE", "metricUnit": "spm", "exampleStr": "30", "imperialUnit": "spm", "imperialExampleStr": "30", "activities": [55]}, {"valueId": "SPK031", "valuePhrase": "TXT_AVG_CROSSTRAINER_CADENCE", "metricUnit": "spm", "exampleStr": "30", "imperialUnit": "spm", "imperialExampleStr": "30", "activities": [55]}], "manualLapValues": [{"valueId": "SPK020_1", "valuePhrase": "TXT_MANUAL_LAP_AVG_CADENCE", "metricUnit": "spm", "exampleStr": "30", "imperialUnit": "spm", "imperialExampleStr": "30", "activities": [1, 22, 53, 103, 9]}, {"valueId": "SPK021_1", "valuePhrase": "TXT_MANUAL_LAP_MAX_CADENCE", "metricUnit": "spm", "exampleStr": "30", "imperialUnit": "spm", "imperialExampleStr": "30", "activities": [1, 22, 53, 103, 9]}, {"valueId": "SPK022_1", "valuePhrase": "TXT_MANUAL_LAP_AVG_SWIM_STROKE_RATE", "metricUnit": "spm", "exampleStr": "30", "imperialUnit": "spm", "imperialExampleStr": "30", "activities": [85, 21]}, {"valueId": "SPK027_1", "valuePhrase": "TXT_MANUAL_LAP_AVG_JUMP_ROPE_CADENCE", "metricUnit": "spm", "exampleStr": "30", "imperialUnit": "spm", "imperialExampleStr": "30", "activities": [102]}, {"valueId": "SPK029_1", "valuePhrase": "TXT_MANUAL_LAP_AVG_ROWING_STROKE_RATE", "metricUnit": "spm", "exampleStr": "30", "imperialUnit": "spm", "imperialExampleStr": "30", "activities": [57]}, {"valueId": "SPK031_1", "valuePhrase": "TXT_MANUAL_LAP_AVG_CROSSTRAINER_CADENCE", "metricUnit": "spm", "exampleStr": "30", "imperialUnit": "spm", "imperialExampleStr": "30", "activities": [55]}], "autoLapValues": [{"valueId": "SPK020_2", "valuePhrase": "TXT_AUTO_LAP_AVG_CADENCE", "metricUnit": "spm", "exampleStr": "30", "imperialUnit": "spm", "imperialExampleStr": "30", "activities": [1, 22, 53, 103, 9]}, {"valueId": "SPK021_2", "valuePhrase": "TXT_AUTO_LAP_MAX_CADENCE", "metricUnit": "spm", "exampleStr": "30", "imperialUnit": "spm", "imperialExampleStr": "30", "activities": [1, 22, 53, 103, 9]}, {"valueId": "SPK022_2", "valuePhrase": "TXT_AUTO_LAP_AVG_SWIM_STROKE_RATE", "metricUnit": "spm", "exampleStr": "30", "imperialUnit": "spm", "imperialExampleStr": "30", "activities": [85, 21]}, {"valueId": "SPK027_2", "valuePhrase": "TXT_AUTO_LAP_AVG_JUMP_ROPE_CADENCE", "metricUnit": "spm", "exampleStr": "30", "imperialUnit": "spm", "imperialExampleStr": "30", "activities": [102]}, {"valueId": "SPK029_2", "valuePhrase": "TXT_AUTO_LAP_AVG_ROWING_STROKE_RATE", "metricUnit": "spm", "exampleStr": "30", "imperialUnit": "spm", "imperialExampleStr": "30", "activities": [57]}, {"valueId": "SPK031_2", "valuePhrase": "TXT_AUTO_LAP_AVG_CROSSTRAINER_CADENCE", "metricUnit": "spm", "exampleStr": "30", "imperialUnit": "spm", "imperialExampleStr": "30", "activities": [55]}], "intervalValues": [{"valueId": "SPK020_3", "valuePhrase": "TXT_INTERVAL_AVG_CADENCE", "metricUnit": "spm", "exampleStr": "30", "imperialUnit": "spm", "imperialExampleStr": "30", "activities": [1, 22, 53, 103, 9]}, {"valueId": "SPK021_3", "valuePhrase": "TXT_INTERVAL_MAX_CADENCE", "metricUnit": "spm", "exampleStr": "30", "imperialUnit": "spm", "imperialExampleStr": "30", "activities": [1, 22, 53, 103, 9]}, {"valueId": "SPK022_3", "valuePhrase": "TXT_INTERVAL_AVG_SWIM_STROKE_RATE", "metricUnit": "spm", "exampleStr": "30", "imperialUnit": "spm", "imperialExampleStr": "30", "activities": [85, 21]}, {"valueId": "SPK027_3", "valuePhrase": "TXT_INTERVAL_AVG_JUMP_ROPE_CADENCE", "metricUnit": "spm", "exampleStr": "30", "imperialUnit": "spm", "imperialExampleStr": "30", "activities": [102]}, {"valueId": "SPK029_3", "valuePhrase": "TXT_INTERVAL_AVG_ROWING_STROKE_RATE", "metricUnit": "spm", "exampleStr": "30", "imperialUnit": "spm", "imperialExampleStr": "30", "activities": [57]}, {"valueId": "SPK031_3", "valuePhrase": "TXT_INTERVAL_AVG_CROSSTRAINER_CADENCE", "metricUnit": "spm", "exampleStr": "30", "imperialUnit": "spm", "imperialExampleStr": "30", "activities": [55]}], "currentLapValues": [{"valueId": "SPK022_4", "valuePhrase": "TXT_CURRENT_SEGMENT_AVG_SWIM_STROKE_RATE", "metricUnit": "spm", "exampleStr": "30", "imperialUnit": "spm", "imperialExampleStr": "30", "activities": [21]}]}, {"groupId": "SPC", "groupName": "TXT_DISTANCE", "activities": [1, 22, 83, 11, 2, 85, 21, 53, 3, 0, 70, 29, 60, 10, 30, 103, 9], "values": [{"valueId": "SPC001", "valuePhrase": "TXT_DISTANCE", "metricUnit": "km", "exampleStr": "5.48", "imperialUnit": "mi", "imperialExampleStr": "5.48", "activities": [1, 22, 83, 11, 2, 85, 3, 0, 70, 29, 60, 10, 30, 103, 9]}, {"valueId": "SPC002", "valuePhrase": "TXT_3D_DISTANCE", "metricUnit": "km", "exampleStr": "5.48", "imperialUnit": "mi", "imperialExampleStr": "5.48", "activities": [1, 22, 83, 11, 2, 3, 0, 70, 29, 60, 10, 13, 30, 9]}, {"valueId": "SPC003", "valuePhrase": "TXT_TREADMILL_DISTANCE", "metricUnit": "km", "exampleStr": "5.48", "imperialUnit": "mi", "imperialExampleStr": "5.48", "activities": [53]}, {"valueId": "SPC004", "valuePhrase": "TXT_AVG_STRIDE", "metricUnit": "m", "exampleStr": "1.0", "imperialUnit": "ft", "imperialExampleStr": "3.3", "activities": [1, 22, 83, 103]}, {"valueId": "SPC005", "valuePhrase": "TXT_SWIM_DISTANCE", "metricUnit": "m", "exampleStr": "548", "imperialUnit": "yd", "imperialExampleStr": "548", "activities": [21]}, {"valueId": "SPC006", "valuePhrase": "TXT_AVG_DISTANCE_PER_STROKE", "metricUnit": "m", "exampleStr": "2.23", "imperialUnit": "yd", "imperialExampleStr": "2.23", "activities": [85]}], "manualLapValues": [{"valueId": "SPC001_1", "valuePhrase": "TXT_MANUAL_LAP_DISTANCE", "metricUnit": "km", "exampleStr": "5.48", "imperialUnit": "mi", "imperialExampleStr": "5.48", "activities": [1, 22, 83, 11, 2, 85, 3, 0, 70, 29, 60, 10, 30, 103, 9]}, {"valueId": "SPC002_1", "valuePhrase": "TXT_MANUAL_LAP_3D_DISTANCE", "metricUnit": "km", "exampleStr": "5.48", "imperialUnit": "mi", "imperialExampleStr": "5.48", "activities": [1, 22, 83, 11, 2, 3, 0, 70, 29, 60, 10, 13, 30, 9]}, {"valueId": "SPC003_1", "valuePhrase": "TXT_MANUAL_LAP_TREADMILL_DISTANCE", "metricUnit": "km", "exampleStr": "5.48", "imperialUnit": "mi", "imperialExampleStr": "5.48", "activities": [53]}, {"valueId": "SPC004_1", "valuePhrase": "TXT_MANUAL_LAP_AVG_STRIDE", "metricUnit": "m", "exampleStr": "1.0", "imperialUnit": "ft", "imperialExampleStr": "3.3", "activities": [1, 22, 83, 103]}, {"valueId": "SPC005_1", "valuePhrase": "TXT_MANUAL_LAP_SWIM_DISTANCE", "metricUnit": "m", "exampleStr": "548", "imperialUnit": "yd", "imperialExampleStr": "548", "activities": [21]}, {"valueId": "SPC006_1", "valuePhrase": "TXT_MANUAL_LAP_AVG_DISTANCE_PER_STROKE", "metricUnit": "m", "exampleStr": "2.23", "imperialUnit": "yd", "imperialExampleStr": "2.23", "activities": [85]}], "autoLapValues": [{"valueId": "SPC001_2", "valuePhrase": "TXT_AUTO_LAP_DISTANCE", "metricUnit": "km", "exampleStr": "5.48", "imperialUnit": "mi", "imperialExampleStr": "5.48", "activities": [1, 22, 83, 11, 2, 85, 3, 0, 70, 29, 60, 10, 30, 103, 9]}, {"valueId": "SPC002_2", "valuePhrase": "TXT_AUTO_LAP_3D_DISTANCE", "metricUnit": "km", "exampleStr": "5.48", "imperialUnit": "mi", "imperialExampleStr": "5.48", "activities": [1, 22, 83, 11, 2, 3, 0, 70, 29, 60, 10, 13, 30, 9]}, {"valueId": "SPC003_2", "valuePhrase": "TXT_AUTO_LAP_TREADMILL_DISTANCE", "metricUnit": "km", "exampleStr": "5.48", "imperialUnit": "mi", "imperialExampleStr": "5.48", "activities": [53]}, {"valueId": "SPC004_2", "valuePhrase": "TXT_AUTO_LAP_AVG_STRIDE", "metricUnit": "m", "exampleStr": "1.0", "imperialUnit": "ft", "imperialExampleStr": "3.3", "activities": [1, 22, 83, 103]}, {"valueId": "SPC005_2", "valuePhrase": "TXT_AUTO_LAP_SWIM_DISTANCE", "metricUnit": "m", "exampleStr": "548", "imperialUnit": "yd", "imperialExampleStr": "548", "activities": [21]}, {"valueId": "SPC006_2", "valuePhrase": "TXT_AUTO_LAP_AVG_DISTANCE_PER_STROKE", "metricUnit": "m", "exampleStr": "2.23", "imperialUnit": "yd", "imperialExampleStr": "2.23", "activities": [85]}], "intervalValues": [{"valueId": "SPC001_3", "valuePhrase": "TXT_INTERVAL_DISTANCE", "metricUnit": "km", "exampleStr": "5.48", "imperialUnit": "mi", "imperialExampleStr": "5.48", "activities": [1, 22, 83, 11, 2, 85, 3, 0, 70, 29, 60, 10, 30, 103, 9]}, {"valueId": "SPC002_3", "valuePhrase": "TXT_INTERVAL_3D_DISTANCE", "metricUnit": "km", "exampleStr": "5.48", "imperialUnit": "mi", "imperialExampleStr": "5.48", "activities": [1, 22, 83, 11, 2, 3, 0, 70, 29, 60, 10, 13, 30, 9]}, {"valueId": "SPC003_3", "valuePhrase": "TXT_INTERVAL_TREADMILL_DISTANCE", "metricUnit": "km", "exampleStr": "5.48", "imperialUnit": "mi", "imperialExampleStr": "5.48", "activities": [53]}, {"valueId": "SPC004_3", "valuePhrase": "TXT_INTERVAL_AVG_STRIDE", "metricUnit": "m", "exampleStr": "1.0", "imperialUnit": "ft", "imperialExampleStr": "3.3", "activities": [1, 22, 83, 103]}, {"valueId": "SPC005_3", "valuePhrase": "TXT_INTERVAL_SWIM_DISTANCE", "metricUnit": "m", "exampleStr": "548", "imperialUnit": "yd", "imperialExampleStr": "548", "activities": [21]}, {"valueId": "SPC006_3", "valuePhrase": "TXT_INTERVAL_AVG_DISTANCE_PER_STROKE", "metricUnit": "m", "exampleStr": "2.23", "imperialUnit": "yd", "imperialExampleStr": "2.23", "activities": [85]}], "currentLapValues": []}, {"groupId": "SPB", "groupName": "TXT_DURATION_AND_TIME", "activities": [1, 22, 83, 11, 2, 85, 21, 53, 52, 102, 57, 55, 69, 23, 77, 51, 3, 0, 70, 29, 60, 54, 10, 73, 58, 13, 30, 34, 36, 103, 9, 17, 115], "values": [{"valueId": "SPB001", "valuePhrase": "TXT_DURATION", "metricUnit": "", "exampleStr": "2:45'8", "imperialUnit": "", "imperialExampleStr": "2:45'8", "activities": [1, 22, 83, 11, 2, 85, 21, 53, 52, 102, 57, 55, 69, 23, 77, 51, 3, 0, 70, 29, 60, 54, 10, 73, 58, 13, 30, 34, 36, 103, 9, 17, 115]}, {"valueId": "SPB002", "valuePhrase": "TXT_TIMEOFDAY", "metricUnit": "", "exampleStr": "15:46", "imperialUnit": "", "imperialExampleStr": "15:46", "activities": [1, 22, 83, 11, 2, 85, 21, 53, 52, 102, 57, 55, 69, 23, 77, 51, 3, 0, 70, 29, 60, 54, 10, 73, 58, 13, 30, 34, 36, 103, 9, 17, 115]}, {"valueId": "SPB003", "valuePhrase": "TXT_ASCENT_TIME", "metricUnit": "", "exampleStr": "2:45'8", "imperialUnit": "", "imperialExampleStr": "2:45'8", "activities": [1, 22, 83, 11, 2, 3, 0, 70, 29, 60, 10, 13, 30, 103, 9]}, {"valueId": "SPB004", "valuePhrase": "TXT_DESCENT_TIME", "metricUnit": "", "exampleStr": "2:45'8", "imperialUnit": "", "imperialExampleStr": "2:45'8", "activities": [1, 22, 83, 11, 2, 3, 0, 70, 29, 60, 10, 13, 30, 103, 9]}, {"valueId": "SPB005", "valuePhrase": "TXT_GROUND_CONTACT_TIME", "metricUnit": "ms", "exampleStr": "548", "imperialUnit": "ms", "imperialExampleStr": "548", "activities": [1, 22, 53, 103]}, {"valueId": "SPB006", "valuePhrase": "TXT_AVG_GROUND_CONTACT_TIME", "metricUnit": "ms", "exampleStr": "546", "imperialUnit": "ms", "imperialExampleStr": "546", "activities": [1, 22, 53, 83, 103]}], "manualLapValues": [{"valueId": "SPB001_1", "valuePhrase": "TXT_MANUAL_LAP_DURATION", "metricUnit": "", "exampleStr": "2:45'8", "imperialUnit": "", "imperialExampleStr": "2:45'8", "activities": [1, 22, 83, 11, 2, 85, 21, 53, 52, 102, 57, 55, 69, 23, 77, 51, 3, 0, 70, 29, 60, 54, 10, 73, 58, 13, 30, 34, 36, 103, 9, 17, 115]}, {"valueId": "SPB003_1", "valuePhrase": "TXT_MANUAL_LAP_ASCENT_TIME", "metricUnit": "", "exampleStr": "2:45'8", "imperialUnit": "", "imperialExampleStr": "2:45'8", "activities": [1, 22, 83, 11, 2, 3, 0, 70, 29, 60, 10, 13, 30, 103, 9]}, {"valueId": "SPB004_1", "valuePhrase": "TXT_MANUAL_LAP_DESCENT_TIME", "metricUnit": "", "exampleStr": "2:45'8", "imperialUnit": "", "imperialExampleStr": "2:45'8", "activities": [1, 22, 83, 11, 2, 3, 0, 70, 29, 60, 10, 13, 30, 103, 9]}, {"valueId": "SPB006_1", "valuePhrase": "TXT_MANUAL_LAP_AVG_GROUND_CONTACT_TIME", "metricUnit": "ms", "exampleStr": "546", "imperialUnit": "ms", "imperialExampleStr": "546", "activities": [1, 22, 53, 83, 103]}], "autoLapValues": [{"valueId": "SPB001_2", "valuePhrase": "TXT_AUTO_LAP_DURATION", "metricUnit": "", "exampleStr": "2:45'8", "imperialUnit": "", "imperialExampleStr": "2:45'8", "activities": [1, 22, 83, 11, 2, 85, 21, 53, 52, 102, 57, 55, 69, 23, 77, 51, 3, 0, 70, 29, 60, 54, 10, 73, 58, 13, 30, 34, 36, 103, 9, 17, 115]}, {"valueId": "SPB003_2", "valuePhrase": "TXT_AUTO_LAP_ASCENT_TIME", "metricUnit": "", "exampleStr": "2:45'8", "imperialUnit": "", "imperialExampleStr": "2:45'8", "activities": [1, 22, 83, 11, 2, 3, 0, 70, 29, 60, 10, 13, 30, 103, 9]}, {"valueId": "SPB004_2", "valuePhrase": "TXT_AUTO_LAP_DESCENT_TIME", "metricUnit": "", "exampleStr": "2:45'8", "imperialUnit": "", "imperialExampleStr": "2:45'8", "activities": [1, 22, 83, 11, 2, 3, 0, 70, 29, 60, 10, 13, 30, 103, 9]}, {"valueId": "SPB006_2", "valuePhrase": "TXT_AUTO_LAP_AVG_GROUND_CONTACT_TIME", "metricUnit": "ms", "exampleStr": "546", "imperialUnit": "ms", "imperialExampleStr": "546", "activities": [1, 22, 53, 83, 103]}], "intervalValues": [{"valueId": "SPB001_3", "valuePhrase": "TXT_INTERVAL_DURATION", "metricUnit": "", "exampleStr": "2:45'8", "imperialUnit": "", "imperialExampleStr": "2:45'8", "activities": [1, 22, 83, 11, 2, 85, 21, 53, 52, 102, 57, 55, 69, 23, 77, 51, 3, 0, 70, 29, 60, 54, 10, 73, 58, 13, 30, 34, 36, 103, 9, 17, 115]}, {"valueId": "SPB003_3", "valuePhrase": "TXT_INTERVAL_ASCENT_TIME", "metricUnit": "", "exampleStr": "2:45'8", "imperialUnit": "", "imperialExampleStr": "2:45'8", "activities": [1, 22, 83, 11, 2, 3, 0, 70, 29, 60, 10, 13, 30, 103, 9]}, {"valueId": "SPB004_3", "valuePhrase": "TXT_INTERVAL_DESCENT_TIME", "metricUnit": "", "exampleStr": "2:45'8", "imperialUnit": "", "imperialExampleStr": "2:45'8", "activities": [1, 22, 83, 11, 2, 3, 0, 70, 29, 60, 10, 13, 30, 103, 9]}, {"valueId": "SPB006_3", "valuePhrase": "TXT_INTERVAL_AVG_GROUND_CONTACT_TIME", "metricUnit": "ms", "exampleStr": "546", "imperialUnit": "ms", "imperialExampleStr": "546", "activities": [1, 22, 53, 83, 103]}], "currentLapValues": [{"valueId": "SPB001_4", "valuePhrase": "TXT_CURRENT_SEGMENT_DURATION", "metricUnit": "", "exampleStr": "2:45'8", "imperialUnit": "", "imperialExampleStr": "2:45'8", "activities": [21]}]}, {"groupId": "SPE", "groupName": "TXT_SPEED_PACE_INTERVAL", "activities": [1, 22, 83, 11, 2, 85, 3, 0, 70, 29, 60, 10, 13, 30, 103, 9, 115], "values": [{"valueId": "SPE001", "valuePhrase": "TXT_PACE", "metricUnit": "/km", "exampleStr": "03'40", "imperialUnit": "/mi", "imperialExampleStr": "12'30", "activities": [1, 22, 83, 11, 2, 0, 70, 29, 60, 10, 103, 9]}, {"valueId": "SPE002", "valuePhrase": "TXT_AVG_PACE", "metricUnit": "/km", "exampleStr": "03'40", "imperialUnit": "/mi", "imperialExampleStr": "12'30", "activities": [1, 22, 83, 11, 0, 70, 29, 60, 103, 9]}, {"valueId": "SPE003", "valuePhrase": "TXT_MAX_PACE", "metricUnit": "/km", "exampleStr": "03'40", "imperialUnit": "/mi", "imperialExampleStr": "12'30", "activities": [1, 22, 83, 11, 0, 70, 29, 60, 103, 9]}, {"valueId": "SPE004", "valuePhrase": "TXT_NORMALIZED_GRADED_PACE", "metricUnit": "/km", "exampleStr": "03'40", "imperialUnit": "/mi", "imperialExampleStr": "12'30", "activities": [1, 22, 83, 11, 0, 70, 29, 60, 103, 9]}, {"valueId": "SPE005", "valuePhrase": "TXT_AVG_NORMALIZED_GRADED_PACE", "metricUnit": "/km", "exampleStr": "03'40", "imperialUnit": "/mi", "imperialExampleStr": "12'30", "activities": [1, 22, 83, 11, 0, 70, 29, 60, 103, 9]}, {"valueId": "SPE006", "valuePhrase": "TXT_MAX_NORMALIZED_GRADED_PACE", "metricUnit": "/km", "exampleStr": "03'40", "imperialUnit": "/mi", "imperialExampleStr": "12'30", "activities": [1, 22, 83, 11, 0, 70, 29, 60, 103, 9]}, {"valueId": "SPE007", "valuePhrase": "TXT_SPEED", "metricUnit": "km/h", "exampleStr": "25.00", "imperialUnit": "mi/h", "imperialExampleStr": "5.32", "activities": [1, 22, 83, 11, 2, 85, 0, 70, 29, 60, 10, 103, 9]}, {"valueId": "SPE008", "valuePhrase": "TXT_AVG_SPEED", "metricUnit": "km/h", "exampleStr": "25.00", "imperialUnit": "mi/h", "imperialExampleStr": "5.32", "activities": [1, 22, 83, 11, 2, 85, 0, 70, 29, 60, 10, 103, 9]}, {"valueId": "SPE009", "valuePhrase": "TXT_MAX_SPEED", "metricUnit": "km/h", "exampleStr": "25.00", "imperialUnit": "mi/h", "imperialExampleStr": "5.32", "activities": [1, 22, 83, 11, 2, 85, 0, 70, 29, 60, 10, 103, 9]}, {"valueId": "SPE010", "valuePhrase": "TXT_ASCENT_SPEED", "metricUnit": "m/h", "exampleStr": "25", "imperialUnit": "ft/h", "imperialExampleStr": "75", "activities": [1, 22, 83, 11, 2, 0, 70, 29, 60, 10, 103, 9, 115]}, {"valueId": "SPE011", "valuePhrase": "TXT_AVG_ASCENT_SPEED", "metricUnit": "m/h", "exampleStr": "25", "imperialUnit": "ft/h", "imperialExampleStr": "75", "activities": [1, 22, 83, 11, 2, 0, 70, 29, 60, 10, 103, 9, 115]}, {"valueId": "SPE012", "valuePhrase": "TXT_MAX_ASCENT_SPEED", "metricUnit": "m/h", "exampleStr": "25", "imperialUnit": "ft/h", "imperialExampleStr": "75", "activities": [1, 22, 83, 11, 2, 0, 70, 29, 60, 10, 103, 9, 115]}, {"valueId": "SPE013", "valuePhrase": "TXT_DESCENT_SPEED", "metricUnit": "m/h", "exampleStr": "25", "imperialUnit": "ft/h", "imperialExampleStr": "75", "activities": [1, 22, 83, 11, 2, 0, 70, 29, 60, 10, 103, 9, 115]}, {"valueId": "SPE014", "valuePhrase": "TXT_AVG_DESCENT_SPEED", "metricUnit": "m/h", "exampleStr": "25", "imperialUnit": "ft/h", "imperialExampleStr": "75", "activities": [1, 22, 83, 11, 2, 0, 70, 29, 60, 10, 103, 9, 115]}, {"valueId": "SPE015", "valuePhrase": "TXT_MAX_DESCENT_SPEED", "metricUnit": "m/h", "exampleStr": "25", "imperialUnit": "ft/h", "imperialExampleStr": "75", "activities": [1, 22, 83, 11, 2, 0, 70, 29, 60, 10, 103, 9, 115]}, {"valueId": "SPE016", "valuePhrase": "TXT_DOWNHILL_SPEED", "metricUnit": "m/h", "exampleStr": "25", "imperialUnit": "ft/h", "imperialExampleStr": "75", "activities": [3, 13, 30]}, {"valueId": "SPE017", "valuePhrase": "TXT_AVG_DOWNHILL_SPEED", "metricUnit": "m/h", "exampleStr": "25", "imperialUnit": "ft/h", "imperialExampleStr": "75", "activities": [3, 13, 30]}, {"valueId": "SPE018", "valuePhrase": "TXT_MAX_DOWNHILL_SPEED", "metricUnit": "m/h", "exampleStr": "25", "imperialUnit": "ft/h", "imperialExampleStr": "75", "activities": [3, 13, 30]}, {"valueId": "SPE023", "valuePhrase": "TXT_SWIM_PACE", "metricUnit": "/100m", "exampleStr": "05'00", "imperialUnit": "/100yd", "imperialExampleStr": "05'00", "activities": [85]}, {"valueId": "SPE024", "valuePhrase": "TXT_AVG_SWIM_PACE", "metricUnit": "/100m", "exampleStr": "05'00", "imperialUnit": "/100yd", "imperialExampleStr": "05'00", "activities": [85]}, {"valueId": "SPE025", "valuePhrase": "TXT_AVG_POOL_SWIM_PACE", "metricUnit": "/100m", "exampleStr": "05'00", "imperialUnit": "/100yd", "imperialExampleStr": "05'00", "activities": [21]}], "manualLapValues": [{"valueId": "SPE002_1", "valuePhrase": "TXT_MANUAL_LAP_AVG_PACE", "metricUnit": "/km", "exampleStr": "03'40", "imperialUnit": "/mi", "imperialExampleStr": "12'30", "activities": [1, 22, 83, 11, 0, 70, 29, 60, 103, 9]}, {"valueId": "SPE003_1", "valuePhrase": "TXT_MANUAL_LAP_MAX_PACE", "metricUnit": "/km", "exampleStr": "03'40", "imperialUnit": "/mi", "imperialExampleStr": "12'30", "activities": [1, 22, 83, 11, 0, 70, 29, 60, 103, 9]}, {"valueId": "SPE005_1", "valuePhrase": "TXT_MANUAL_LAP_AVG_NORMALIZED_GRADED_PACE", "metricUnit": "/km", "exampleStr": "03'40", "imperialUnit": "/mi", "imperialExampleStr": "12'30", "activities": [1, 22, 83, 11, 0, 70, 29, 60, 103, 9]}, {"valueId": "SPE006_1", "valuePhrase": "TXT_MANUAL_LAP_MAX_NORMALIZED_GRADED_PACE", "metricUnit": "/km", "exampleStr": "03'40", "imperialUnit": "/mi", "imperialExampleStr": "12'30", "activities": [1, 22, 83, 11, 0, 70, 29, 60, 103, 9]}, {"valueId": "SPE008_1", "valuePhrase": "TXT_MANUAL_LAP_AVG_SPEED", "metricUnit": "km/h", "exampleStr": "25.00", "imperialUnit": "mi/h", "imperialExampleStr": "5.32", "activities": [1, 22, 83, 11, 2, 85, 0, 70, 29, 60, 10, 103, 9]}, {"valueId": "SPE009_1", "valuePhrase": "TXT_MANUAL_LAP_MAX_SPEED", "metricUnit": "km/h", "exampleStr": "25.00", "imperialUnit": "mi/h", "imperialExampleStr": "5.32", "activities": [1, 22, 83, 11, 2, 85, 0, 70, 29, 60, 10, 103, 9]}, {"valueId": "SPE011_1", "valuePhrase": "TXT_MANUAL_LAP_AVG_ASCENT_SPEED", "metricUnit": "m/h", "exampleStr": "25", "imperialUnit": "ft/h", "imperialExampleStr": "75", "activities": [1, 22, 83, 11, 2, 0, 70, 29, 60, 10, 103, 9, 115]}, {"valueId": "SPE012_1", "valuePhrase": "TXT_MANUAL_LAP_MAX_ASCENT_SPEED", "metricUnit": "m/h", "exampleStr": "25", "imperialUnit": "ft/h", "imperialExampleStr": "75", "activities": [1, 22, 83, 11, 2, 0, 70, 29, 60, 10, 103, 9, 115]}, {"valueId": "SPE014_1", "valuePhrase": "TXT_MANUAL_LAP_AVG_DESCENT_SPEED", "metricUnit": "m/h", "exampleStr": "25", "imperialUnit": "ft/h", "imperialExampleStr": "75", "activities": [1, 22, 83, 11, 2, 0, 70, 29, 60, 10, 103, 9, 115]}, {"valueId": "SPE015_1", "valuePhrase": "TXT_MANUAL_LAP_MAX_DESCENT_SPEED", "metricUnit": "m/h", "exampleStr": "25", "imperialUnit": "ft/h", "imperialExampleStr": "75", "activities": [1, 22, 83, 11, 2, 0, 70, 29, 60, 10, 103, 9, 115]}, {"valueId": "SPE017_1", "valuePhrase": "TXT_MANUAL_LAP_AVG_DOWNHILL_SPEED", "metricUnit": "m/h", "exampleStr": "25", "imperialUnit": "ft/h", "imperialExampleStr": "75", "activities": [3, 13, 30]}, {"valueId": "SPE018_1", "valuePhrase": "TXT_MANUAL_LAP_MAX_DOWNHILL_SPEED", "metricUnit": "m/h", "exampleStr": "25", "imperialUnit": "ft/h", "imperialExampleStr": "75", "activities": [3, 13, 30]}, {"valueId": "SPE024_1", "valuePhrase": "TXT_MANUAL_LAP_AVG_SWIM_PACE", "metricUnit": "/100m", "exampleStr": "05'00", "imperialUnit": "/100yd", "imperialExampleStr": "05'00", "activities": [85]}, {"valueId": "SPE025_1", "valuePhrase": "TXT_MANUAL_LAP_AVG_POOL_SWIM_PACE", "metricUnit": "/100m", "exampleStr": "05'00", "imperialUnit": "/100yd", "imperialExampleStr": "05'00", "activities": [21]}], "autoLapValues": [{"valueId": "SPE002_2", "valuePhrase": "TXT_AUTO_LAP_AVG_PACE", "metricUnit": "/km", "exampleStr": "03'40", "imperialUnit": "/mi", "imperialExampleStr": "12'30", "activities": [1, 22, 83, 11, 0, 70, 29, 60, 103, 9]}, {"valueId": "SPE003_2", "valuePhrase": "TXT_AUTO_LAP_MAX_PACE", "metricUnit": "/km", "exampleStr": "03'40", "imperialUnit": "/mi", "imperialExampleStr": "12'30", "activities": [1, 22, 83, 11, 0, 70, 29, 60, 103, 9]}, {"valueId": "SPE005_2", "valuePhrase": "TXT_AUTO_LAP_AVG_NORMALIZED_GRADED_PACE", "metricUnit": "/km", "exampleStr": "03'40", "imperialUnit": "/mi", "imperialExampleStr": "12'30", "activities": [1, 22, 83, 11, 0, 70, 29, 60, 103, 9]}, {"valueId": "SPE006_2", "valuePhrase": "TXT_AUTO_LAP_MAX_NORMALIZED_GRADED_PACE", "metricUnit": "/km", "exampleStr": "03'40", "imperialUnit": "/mi", "imperialExampleStr": "12'30", "activities": [1, 22, 83, 11, 0, 70, 29, 60, 103, 9]}, {"valueId": "SPE008_2", "valuePhrase": "TXT_AUTO_LAP_AVG_SPEED", "metricUnit": "km/h", "exampleStr": "25.00", "imperialUnit": "mi/h", "imperialExampleStr": "5.32", "activities": [1, 22, 83, 11, 2, 85, 0, 70, 29, 60, 10, 103, 9]}, {"valueId": "SPE009_2", "valuePhrase": "TXT_AUTO_LAP_MAX_SPEED", "metricUnit": "km/h", "exampleStr": "25.00", "imperialUnit": "mi/h", "imperialExampleStr": "5.32", "activities": [1, 22, 83, 11, 2, 85, 0, 70, 29, 60, 10, 103, 9]}, {"valueId": "SPE011_2", "valuePhrase": "TXT_AUTO_LAP_AVG_ASCENT_SPEED", "metricUnit": "m/h", "exampleStr": "25", "imperialUnit": "ft/h", "imperialExampleStr": "75", "activities": [1, 22, 83, 11, 2, 0, 70, 29, 60, 10, 103, 9, 115]}, {"valueId": "SPE012_2", "valuePhrase": "TXT_AUTO_LAP_MAX_ASCENT_SPEED", "metricUnit": "m/h", "exampleStr": "25", "imperialUnit": "ft/h", "imperialExampleStr": "75", "activities": [1, 22, 83, 11, 2, 0, 70, 29, 60, 10, 103, 9, 115]}, {"valueId": "SPE014_2", "valuePhrase": "TXT_AUTO_LAP_AVG_DESCENT_SPEED", "metricUnit": "m/h", "exampleStr": "25", "imperialUnit": "ft/h", "imperialExampleStr": "75", "activities": [1, 22, 83, 11, 2, 0, 70, 29, 60, 10, 103, 9, 115]}, {"valueId": "SPE015_2", "valuePhrase": "TXT_AUTO_LAP_MAX_DESCENT_SPEED", "metricUnit": "m/h", "exampleStr": "25", "imperialUnit": "ft/h", "imperialExampleStr": "75", "activities": [1, 22, 83, 11, 2, 0, 70, 29, 60, 10, 103, 9, 115]}, {"valueId": "SPE017_2", "valuePhrase": "TXT_AUTO_LAP_AVG_DOWNHILL_SPEED", "metricUnit": "m/h", "exampleStr": "25", "imperialUnit": "ft/h", "imperialExampleStr": "75", "activities": [3, 13, 30]}, {"valueId": "SPE018_2", "valuePhrase": "TXT_AUTO_LAP_MAX_DOWNHILL_SPEED", "metricUnit": "m/h", "exampleStr": "25", "imperialUnit": "ft/h", "imperialExampleStr": "75", "activities": [3, 13, 30]}, {"valueId": "SPE024_2", "valuePhrase": "TXT_AUTO_LAP_AVG_SWIM_PACE", "metricUnit": "/100m", "exampleStr": "05'00", "imperialUnit": "/100yd", "imperialExampleStr": "05'00", "activities": [85]}, {"valueId": "SPE025_2", "valuePhrase": "TXT_AUTO_LAP_AVG_POOL_SWIM_PACE", "metricUnit": "/100m", "exampleStr": "05'00", "imperialUnit": "/100yd", "imperialExampleStr": "05'00", "activities": [21]}], "intervalValues": [{"valueId": "SPE002_3", "valuePhrase": "TXT_INTERVAL_AVG_PACE", "metricUnit": "/km", "exampleStr": "03'40", "imperialUnit": "/mi", "imperialExampleStr": "12'30", "activities": [1, 22, 83, 11, 0, 70, 29, 60, 103, 9]}, {"valueId": "SPE003_3", "valuePhrase": "TXT_INTERVAL_MAX_PACE", "metricUnit": "/km", "exampleStr": "03'40", "imperialUnit": "/mi", "imperialExampleStr": "12'30", "activities": [1, 22, 83, 11, 0, 70, 29, 60, 103, 9]}, {"valueId": "SPE005_3", "valuePhrase": "TXT_INTERVAL_AVG_NORMALIZED_GRADED_PACE", "metricUnit": "/km", "exampleStr": "03'40", "imperialUnit": "/mi", "imperialExampleStr": "12'30", "activities": [1, 22, 83, 11, 0, 70, 29, 60, 103, 9]}, {"valueId": "SPE006_3", "valuePhrase": "TXT_INTERVAL_MAX_NORMALIZED_GRADED_PACE", "metricUnit": "/km", "exampleStr": "03'40", "imperialUnit": "/mi", "imperialExampleStr": "12'30", "activities": [1, 22, 83, 11, 0, 70, 29, 60, 103, 9]}, {"valueId": "SPE008_3", "valuePhrase": "TXT_INTERVAL_AVG_SPEED", "metricUnit": "km/h", "exampleStr": "25.00", "imperialUnit": "mi/h", "imperialExampleStr": "5.32", "activities": [1, 22, 83, 11, 2, 85, 0, 70, 29, 60, 10, 103, 9]}, {"valueId": "SPE009_3", "valuePhrase": "TXT_INTERVAL_MAX_SPEED", "metricUnit": "km/h", "exampleStr": "25.00", "imperialUnit": "mi/h", "imperialExampleStr": "5.32", "activities": [1, 22, 83, 11, 2, 85, 0, 70, 29, 60, 10, 103, 9]}, {"valueId": "SPE011_3", "valuePhrase": "TXT_INTERVAL_AVG_ASCENT_SPEED", "metricUnit": "m/h", "exampleStr": "25", "imperialUnit": "ft/h", "imperialExampleStr": "75", "activities": [1, 22, 83, 11, 2, 0, 70, 29, 60, 10, 103, 9, 115]}, {"valueId": "SPE012_3", "valuePhrase": "TXT_INTERVAL_MAX_ASCENT_SPEED", "metricUnit": "m/h", "exampleStr": "25", "imperialUnit": "ft/h", "imperialExampleStr": "75", "activities": [1, 22, 83, 11, 2, 0, 70, 29, 60, 10, 103, 9, 115]}, {"valueId": "SPE014_3", "valuePhrase": "TXT_INTERVAL_AVG_DESCENT_SPEED", "metricUnit": "m/h", "exampleStr": "25", "imperialUnit": "ft/h", "imperialExampleStr": "75", "activities": [1, 22, 83, 11, 2, 0, 70, 29, 60, 10, 103, 9, 115]}, {"valueId": "SPE015_3", "valuePhrase": "TXT_INTERVAL_MAX_DESCENT_SPEED", "metricUnit": "m/h", "exampleStr": "25", "imperialUnit": "ft/h", "imperialExampleStr": "75", "activities": [1, 22, 83, 11, 2, 0, 70, 29, 60, 10, 103, 9, 115]}, {"valueId": "SPE017_3", "valuePhrase": "TXT_INTERVAL_AVG_DOWNHILL_SPEED", "metricUnit": "m/h", "exampleStr": "25", "imperialUnit": "ft/h", "imperialExampleStr": "75", "activities": [3, 13, 30]}, {"valueId": "SPE018_3", "valuePhrase": "TXT_INTERVAL_MAX_DOWNHILL_SPEED", "metricUnit": "m/h", "exampleStr": "25", "imperialUnit": "ft/h", "imperialExampleStr": "75", "activities": [3, 13, 30]}, {"valueId": "SPE024_3", "valuePhrase": "TXT_INTERVAL_AVG_SWIM_PACE", "metricUnit": "/100m", "exampleStr": "05'00", "imperialUnit": "/100yd", "imperialExampleStr": "05'00", "activities": [85]}, {"valueId": "SPE025_3", "valuePhrase": "TXT_INTERVAL_AVG_POOL_SWIM_PACE", "metricUnit": "/100m", "exampleStr": "05'00", "imperialUnit": "/100yd", "imperialExampleStr": "05'00", "activities": [21]}], "currentLapValues": [{"valueId": "SPE025_4", "valuePhrase": "TXT_CURRENT_SEGMENT_AVG_POOL_SWIM_PACE", "metricUnit": "/100m", "exampleStr": "05'00", "imperialUnit": "/100yd", "imperialExampleStr": "05'00", "activities": [21]}]}]