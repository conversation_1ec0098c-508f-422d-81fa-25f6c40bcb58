plugins {
    id 'stt.android.plugin.library'
    id "stt.android.plugin.moshi"
    alias libs.plugins.androidx.benchmark
}

android {
    namespace 'com.stt.android.benchmark'

    defaultConfig {
        versionCode 1
        versionName "1.0"
        testApplicationId "com.stt.android.test"
    }

    testBuildType = "release"

    buildTypes {
        debug {
            proguardFile "benchmark-proguard-rules.pro"
        }
        release {
            isDefault = true
        }
    }

    sourceSets {
        androidTest {
            resources.srcDirs += ['src/androidTest/resources']
        }
    }
    buildFeatures.buildConfig = true
}

dependencies {
    androidTestImplementation libs.androidx.benchmark.junit4

    implementation project(Deps.workoutsDomain)
    implementation project(Deps.timeline)
    implementation project(Deps.core)
}
