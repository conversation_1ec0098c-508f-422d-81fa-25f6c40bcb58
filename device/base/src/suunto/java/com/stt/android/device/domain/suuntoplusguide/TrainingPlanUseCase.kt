package com.stt.android.device.domain.suuntoplusguide

import com.stt.android.device.datasource.WatchSerialDataSource
import com.stt.android.device.datasource.suuntoplusguide.SuuntoWatchCapabilityStore
import com.stt.android.device.datasource.suuntoplusguide.TrainingPlansLocalDataSource
import com.suunto.connectivity.capabilities.SuuntoWatchCapabilities
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.toImmutableList
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.map
import javax.inject.Inject

class TrainingPlanUseCase
@Inject constructor(
    private val trainingPlansLocalDataSource: TrainingPlansLocalDataSource,
    private val watchSerialDataSource: WatchSerialDataSource,
    private val capabilityStore: SuuntoWatchCapabilityStore,
) {
    fun getTrainingPlanAndWatchStatusById(id: TrainingPlanId): Flow<TrainingPlanAndWatchStatus?> {
        val serial = watchSerialDataSource.getCurrentWatchSerial()
            ?: return flow { throw IllegalStateException("Cannot get plan: Missing watch serial") }

        val capabilityFlow: Flow<SuuntoWatchCapabilities?> =
            flow { emit(capabilityStore.loadCapabilitiesWithAugmentedScreenSize(serial)) }

        return capabilityFlow.flatMapLatest { capabilities ->
            if (capabilities == null || !capabilities.areSuuntoPlusGuidesSupported) {
                // SuuntoPlus™ guides not supported by watch. Return NOT_SUPPORTED for all guides.
                trainingPlansLocalDataSource.findByIdAsFlow(id)
                    .map { plan ->
                        TrainingPlanAndWatchStatus(
                            plan = plan,
                            watchStatus = SuuntoPlusPluginStatus.NOT_SUPPORTED
                        )
                    }
            } else {
                trainingPlansLocalDataSource.findTrainingPlanWithWatchStatus(serial, id)
                    .map {
                        TrainingPlanAndWatchStatus(
                            plan = it.first,
                            watchStatus = it.second ?: SuuntoPlusPluginStatus.UNKNOWN
                        )
                    }
            }
        }
    }

    suspend fun updatePinnedState(planId: TrainingPlanId, pinned: Boolean) {
        trainingPlansLocalDataSource.updatePinnedState(planId, pinned)
    }

    suspend fun deletePlan(planId: TrainingPlanId) {
        trainingPlansLocalDataSource.softDelete(planId)
    }

    fun listPlans(): Flow<TrainingPlanContainerInGuideList> =
        watchSerialDataSource.getCurrentWatchSerialAsFlow()
            .flatMapLatest { serial ->
                if (serial != null) {
                    trainingPlansLocalDataSource.listTrainingPlansWithStatus(serial)
                        .map { plansWithState ->
                            TrainingPlanContainerInGuideList(
                                plansInWatch = plansWithState.filteredByStateAndSorted(
                                    SuuntoPlusPluginStatus.IN_WATCH
                                ),
                                plansNotInWatch = plansWithState.filteredByStateAndSorted(
                                    SuuntoPlusPluginStatus.DOWNLOADED,
                                    SuuntoPlusPluginStatus.DOWNLOADING,
                                    SuuntoPlusPluginStatus.INSTALLING,
                                    SuuntoPlusPluginStatus.WATCH_FULL,
                                    SuuntoPlusPluginStatus.UNKNOWN
                                ),
                                plansNotSupported = plansWithState.filteredByStateAndSorted(
                                    SuuntoPlusPluginStatus.NOT_SUPPORTED,
                                ),
                            )
                        }
                } else {
                    flowOf(TrainingPlanContainerInGuideList(persistentListOf(), persistentListOf(), persistentListOf()))
                }
            }
}

private fun List<TrainingPlanWithStatus>.filteredByStateAndSorted(vararg states: SuuntoPlusPluginStatus) =
    filter { (_, planState) ->
        planState in states
    }.sorted()
        .toImmutableList()

private fun List<TrainingPlanWithStatus>.sorted(): List<TrainingPlanWithStatus> = sortedBy { (plan, _) ->
    // Sort primarily using priorityIndex but fall back to using modified timestamp if
    // priorityIndex is null. This will make sure guides that for some reason have no
    // priorityIndex will still be installed, albeit with a very low priority.
    plan.priorityIndex?.toLong() ?: plan.modifiedMillis
}

data class TrainingPlanAndWatchStatus(
    val plan: TrainingPlan?,
    val watchStatus: SuuntoPlusPluginStatus
)

data class TrainingPlanContainerInGuideList(
    val plansInWatch: ImmutableList<TrainingPlanWithStatus>,
    val plansNotInWatch: ImmutableList<TrainingPlanWithStatus>,
    val plansNotSupported: ImmutableList<TrainingPlanWithStatus>,
)

fun ImmutableList<TrainingPlanWithStatus>.convertGuides(): List<SuuntoPlusGuideWithStatus>{
    return this.map {
        SuuntoPlusGuideWithStatus(
            guide = it.plan.toSuuntoPlusGuide(),
            status = it.status
        )
    }
}

fun TrainingPlan.toSuuntoPlusGuide() =
    SuuntoPlusGuide(
        id = SuuntoPlusGuideId(id.id),
        catalogueId = catalogueId,
        modifiedMillis = modifiedMillis,
        name = name,
        owner = owner,
        ownerId = ownerId,
        date = startDate,
        endDate = endDate,
        url = url,
        iconUrl = iconUrl,
        backgroundUrl = "",
        description = description,
        subTitle = subTitle,
        richDescription = richDescription,
        priorityIndex = priorityIndex,
        activityIds = activityIds,
        pinned = pinned,
        remoteSyncErrorCode = remoteSyncErrorCode,
        watchSyncErrorCode = watchSyncErrorCode,
        planId = planId
    )
