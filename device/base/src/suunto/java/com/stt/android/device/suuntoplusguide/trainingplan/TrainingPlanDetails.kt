package com.stt.android.device.suuntoplusguide.trainingplan

import androidx.compose.animation.animateContentSize
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.ClickableText
import androidx.compose.material.Divider
import androidx.compose.material.Icon
import androidx.compose.material.Surface
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material.icons.filled.KeyboardArrowUp
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateMapOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.mapSaver
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.snapshots.SnapshotStateMap
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathEffect
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.stt.android.R
import com.stt.android.compose.component.SuuntoActivityIcon
import com.stt.android.compose.markdown.WorkoutPlanMarkdown
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.darkGrey
import com.stt.android.compose.theme.lightGrey
import com.stt.android.compose.theme.material3.body
import com.stt.android.compose.theme.material3.bodyBold
import com.stt.android.compose.theme.material3.bodyMegaBold
import com.stt.android.compose.theme.material3.bodyXLargeBold
import com.stt.android.compose.theme.nearBlack
import com.stt.android.compose.theme.nearWhite
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.util.rememberEventThrottler
import com.stt.android.core.domain.workouts.CoreActivityType
import com.stt.android.device.suuntoplusdetails.SuuntoPlusItemDetailsDescription
import com.stt.android.device.suuntoplusdetails.SuuntoPlusItemDetailsHeader
import com.stt.android.device.suuntoplusdetails.note.SuuntoPlusDetailsNote
import com.stt.android.device.suuntoplusdetails.note.SuuntoPlusItemDetailNotes
import com.stt.android.domain.workout.ActivityType
import com.stt.android.suuntoplus.SuuntoPlusItemLabel
import com.stt.android.suuntoplus.ui.SuuntoPlusItemDetailsScreenLabels
import com.stt.android.device.remote.suuntoplusguide.TrainingDay
import com.stt.android.device.remote.suuntoplusguide.TrainingExtendedDomain
import com.stt.android.device.remote.suuntoplusguide.TrainingGuideDomain
import com.stt.android.device.remote.suuntoplusguide.TrainingPlanWeek
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.toImmutableList

@Composable
fun TrainingPlanDetails(
    title: String,
    description: String?,
    richDescription: String?,
    labels: ImmutableList<SuuntoPlusItemLabel>,
    backgroundUrl: String?,
    onHowToUseClick: () -> Unit,
    notes: ImmutableList<SuuntoPlusDetailsNote>,
    fileModificationTime: String?,
    onNoteAction: (SuuntoPlusDetailsNote.NoteButtonAction) -> Unit,
    weeks: ImmutableList<TrainingPlanWeek>,
    onTrainingDayClicked: (weekId: Int, dayId: Int, extends: Map<String, TrainingExtendedDomain>) -> Unit,
    onRestDayClicked: (dayId: Int) -> Unit,
    modifier: Modifier = Modifier,
) {
    val eventThrottler = rememberEventThrottler()
    Column(modifier = modifier) {
        SuuntoPlusItemDetailsHeader(
            bannerImageUrl = backgroundUrl,
            title = title,
            showWatchPreviewOnly = false,
            bannerHeight = 0.dp
        )

        val horizontalPadding = Modifier.padding(horizontal = MaterialTheme.spacing.medium)

        SuuntoPlusItemDetailsScreenLabels(
            labels = labels,
            modifier = horizontalPadding.padding(vertical = MaterialTheme.spacing.medium)
        )

        SuuntoPlusItemDetailsDescription(
            description = description,
            richDescription = richDescription,
            expandable = false,
            modifier = horizontalPadding
        )

        ClickableText(
            text = AnnotatedString(stringResource(R.string.suunto_plus_store_training_plan_how_to_use)),
            style = MaterialTheme.typography.bodyBold.copy(
                color = androidx.compose.material.MaterialTheme.colors.primary
            ),
            onClick = { eventThrottler.processEvent { onHowToUseClick() } },
            modifier = Modifier.padding(horizontal = MaterialTheme.spacing.medium, vertical = MaterialTheme.spacing.large)
        )

        Text(
            text = fileModificationTime?.let { stringResource(com.stt.android.device.R.string.suunto_plus_store_training_plan_updated_time, it) }.orEmpty(),
            style = MaterialTheme.typography.body,
            modifier = Modifier.padding(start = MaterialTheme.spacing.medium)
        )

        Spacer(modifier = Modifier.height(MaterialTheme.spacing.small))
        SuuntoPlusItemDetailNotes(
            notes = notes,
            onNoteAction = onNoteAction,
            modifier = horizontalPadding
        )
        Spacer(modifier = Modifier.height(MaterialTheme.spacing.small))

        Divider(color = MaterialTheme.colorScheme.nearWhite, thickness = MaterialTheme.spacing.small)

        WorkoutPlanScreen(weeks, onTrainingDayClicked, onRestDayClicked)
    }
}

@Preview
@Composable
private fun SuuntoPlusStoreGuideDetailsPreview() {
    AppTheme {
        Surface {
            TrainingPlanDetails(
                title = "Running Form drills",
                description = "Long description",
                richDescription = WorkoutPlanMarkdown,
                backgroundUrl = "https://suuntopluspluginsdev.blob.core.windows.net/guide-icons/57949619c7cd556bc347cd987b816217.png",
                labels = persistentListOf(
                    SuuntoPlusItemLabel.forActivityType(activityType = ActivityType.TRAIL_RUNNING),
                    SuuntoPlusItemLabel("Suunto guide"),
                    SuuntoPlusItemLabel("Training plan"),
                ),
                notes = persistentListOf(SuuntoPlusDetailsNote.MaxFeatureLimitReachedNote(false)),
                fileModificationTime = "2025/05/12 20:33",
                onHowToUseClick = {},
                onNoteAction = { action ->
                    throw UnsupportedOperationException("No note action $action supported")
                },
                modifier = Modifier.fillMaxWidth(),
                weeks = dummyWeeks(),
                onTrainingDayClicked = { _, _, _ -> },
                onRestDayClicked = { _ -> }
            )
        }
    }
}

@Composable
fun WorkoutPlanScreen(
    workoutWeeks: List<TrainingPlanWeek>,
    onTrainingDayClicked: (weekId: Int, dayId: Int, extends: Map<String, TrainingExtendedDomain>) -> Unit,
    onRestDayClicked: (dayId: Int) -> Unit,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    val expandedWeekStates = rememberSaveable(
        saver = mapSaver(
            save = { it.entries.associate { entry -> entry.key.toString() to entry.value } },
            restore = { restoredMap ->
                restoredMap.entries.associate { it.key.toInt() to it.value as Boolean }.toMutableStateMap()
            }
        )
    ) {
        mutableStateMapOf()
    }
    Column(
        modifier = modifier
            .fillMaxSize()
    ) {
        Text(
            text = stringResource(com.stt.android.device.R.string.suunto_plus_store_training_plan_title),
            style = MaterialTheme.typography.bodyMegaBold,
            modifier = Modifier.padding(start = MaterialTheme.spacing.medium, top = MaterialTheme.spacing.medium)
        )
        Text(
            text = context.resources.getQuantityString(com.stt.android.device.R.plurals.suunto_plus_store_training_plan_week_count, workoutWeeks.count(), workoutWeeks.count()),
            style = MaterialTheme.typography.body,
            color = MaterialTheme.colorScheme.darkGrey,
            modifier = Modifier.padding(start = MaterialTheme.spacing.medium, top = MaterialTheme.spacing.xsmall)
        )

        Spacer(modifier = Modifier.height(MaterialTheme.spacing.smaller))

        Divider(color = MaterialTheme.colorScheme.lightGrey, thickness = 1.dp)

        workoutWeeks.forEachIndexed { index, week ->
            val expanded = expandedWeekStates[index] ?: false

            WorkoutWeekItem(
                week = week,
                expanded = expanded,
                onExpandToggle = { expandedWeekStates[index] = !expanded},
                onTrainingDayClicked = onTrainingDayClicked,
                onRestDayClicked = onRestDayClicked
            )

            Divider(color = MaterialTheme.colorScheme.lightGrey, thickness = 1.dp)
        }
    }
}

@Composable
fun WorkoutWeekItem(
    week: TrainingPlanWeek,
    expanded: Boolean,
    onExpandToggle: () -> Unit,
    onTrainingDayClicked: (weekId: Int, dayId: Int, extends: Map<String, TrainingExtendedDomain>) -> Unit,
    onRestDayClicked: (dayId: Int) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .animateContentSize()
            .padding(vertical = MaterialTheme.spacing.medium)
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier
                .fillMaxWidth()
                .clickable(
                    interactionSource = remember { MutableInteractionSource() }, indication = null
                ) {
                    onExpandToggle()
                }
        ) {
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = stringResource(com.stt.android.device.R.string.suunto_plus_store_training_plan_week, week.index),
                    style = MaterialTheme.typography.bodyXLargeBold,
                    modifier = Modifier.padding(start = MaterialTheme.spacing.medium, top = MaterialTheme.spacing.xsmall)
                )
                Text(
                    text = stringResource(com.stt.android.device.R.string.suunto_plus_store_training_plan_workout, week.count),
                    style = MaterialTheme.typography.bodySmall.copy(color = MaterialTheme.colorScheme.darkGrey),
                    modifier = Modifier.padding(start = MaterialTheme.spacing.medium, top = MaterialTheme.spacing.xsmall)
                )
            }

            Row(verticalAlignment = Alignment.CenterVertically, modifier = Modifier.padding(end = MaterialTheme.spacing.medium)) {
                Text(
                    text = week.totalTime,
                    style = MaterialTheme.typography.bodyBold
                )
                Icon(
                    imageVector = if (expanded) Icons.Default.KeyboardArrowUp else Icons.Default.KeyboardArrowDown,
                    contentDescription = null,
                    modifier = Modifier
                        .size(20.dp)
                )
            }
        }

        if (expanded) {
            Spacer(modifier = Modifier.height(MaterialTheme.spacing.small))
            week.trainingDays.forEach { day ->
                WorkoutDayItem(day, onTrainingDayClicked = {
                    onTrainingDayClicked.invoke(week.index, day.index, day.guides.associateBy(
                        keySelector = { it.guidesId },
                        valueTransform = { it.trainingExtendedDomain }
                    ))
                }, onRestDayClicked = onRestDayClicked)
            }
        }
    }
}


@Composable
fun WorkoutDayItem(
    day: TrainingDay,
    onTrainingDayClicked: () -> Unit,
    onRestDayClicked: (dayId: Int) -> Unit,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .padding(
                horizontal = MaterialTheme.spacing.medium, vertical = MaterialTheme.spacing.xsmall
            )
    ) {
        Box(
            modifier = Modifier
                .wrapContentWidth()
                .height(52.dp),
            contentAlignment = Alignment.Center
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = stringResource(com.stt.android.device.R.string.suunto_plus_store_training_plan_day, day.index),
                    style = MaterialTheme.typography.body.copy(fontWeight = FontWeight.Bold),
                    modifier = Modifier.padding(end = MaterialTheme.spacing.xxsmall)
                )
                DashedDivider(
                    modifier = Modifier.width(16.dp),
                    color = MaterialTheme.colorScheme.nearBlack,
                    thickness = 1.dp,
                    dashLength = 10f,
                    gapLength = 4f
                )
            }
        }

        Column(
            modifier = Modifier
                .fillMaxWidth()
                .weight(1f)
                .clip(RoundedCornerShape(MaterialTheme.spacing.medium))
                .border(
                    width = 1.dp,
                    color = MaterialTheme.colorScheme.lightGrey,
                    shape = RoundedCornerShape(MaterialTheme.spacing.medium)
                )
                .background(Color.White)
                .fillMaxWidth()
        ) {
            if (day.guides.isNotEmpty()) {
                day.guides.forEachIndexed { index, workoutItem ->
                    WorkoutItemRow(workoutItem, onTrainingDayClicked)

                    if (index != day.guides.lastIndex) {
                        Divider(color = MaterialTheme.colorScheme.lightGrey, thickness = 1.dp)
                    }
                }
            } else {
                RestDayRow(onRestDayClicked = {
                    onRestDayClicked.invoke(day.index)
                })
            }
        }
    }
}

@Composable
private fun RestDayRow(onRestDayClicked: () -> Unit, modifier: Modifier = Modifier) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = modifier
            .padding(
                horizontal = MaterialTheme.spacing.smaller, vertical = MaterialTheme.spacing.medium
            )
            .fillMaxWidth()
            .height(20.dp)
            .clickable(
                interactionSource = remember { MutableInteractionSource() },
                indication = null
            ) {
                onRestDayClicked.invoke()
            }
    ) {
        Icon(
            painter = painterResource(com.stt.android.core.R.drawable.ic_rest),
            contentDescription = null,
            tint = colorResource(R.color.recovery_state_recovering),
            modifier = Modifier.size(24.dp)
        )

        Spacer(modifier = Modifier.width(MaterialTheme.spacing.smaller))

        Text(
            text = stringResource(com.stt.android.device.R.string.suunto_plus_store_training_plan_resting_day),
            style = MaterialTheme.typography.bodyMedium,
            modifier = Modifier.weight(1f)
        )
    }
}

@Composable
private fun WorkoutItemRow(trainingGuideDomain: TrainingGuideDomain, onTrainingDayClicked: () -> Unit, modifier: Modifier = Modifier) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.SpaceBetween,
        modifier = modifier
            .padding(
                horizontal = MaterialTheme.spacing.smaller, vertical = MaterialTheme.spacing.small
            )
            .fillMaxWidth()
            .height(34.dp)
            .clickable(
                interactionSource = remember { MutableInteractionSource() },
                indication = null
            ) { onTrainingDayClicked.invoke() }
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.weight(1f)
        ) {
            trainingGuideDomain.activities?.firstOrNull()?.let {
                SuuntoActivityIcon(
                    iconRes = CoreActivityType.valueOf(it).icon,
                    tint = colorResource(CoreActivityType.valueOf(it).color),
                    background = Color.Transparent,
                )
                Spacer(modifier = Modifier.width(MaterialTheme.spacing.small))
            }

            trainingGuideDomain.title?.let {
                Text(
                    text = it,
                    style = MaterialTheme.typography.bodyMedium,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    modifier = Modifier.weight(1f)
                )
            }
        }


        Text(
            text = trainingGuideDomain.trainingExtendedDomain.duration.orEmpty(),
            style = MaterialTheme.typography.bodyMedium,
            maxLines = 1,
            overflow = TextOverflow.Ellipsis,
            modifier = Modifier.padding(start = MaterialTheme.spacing.small)
        )
    }
}

@Composable
fun DashedDivider(
    modifier: Modifier = Modifier,
    thickness: Dp = 1.dp,
    dashLength: Float = 10f,
    gapLength: Float = 10f,
    color: Color = MaterialTheme.colorScheme.lightGrey,
) {
    Canvas(
        modifier = modifier
            .fillMaxWidth()
            .height(thickness)
    ) {
        val pathEffect = PathEffect.dashPathEffect(floatArrayOf(dashLength, gapLength), 0f)
        drawLine(
            color = color,
            start = androidx.compose.ui.geometry.Offset(0f, 0f),
            end = androidx.compose.ui.geometry.Offset(size.width, 0f),
            strokeWidth = thickness.toPx(),
            pathEffect = pathEffect
        )
    }
}

fun dummyWeeks(): ImmutableList<TrainingPlanWeek> {
    return listOf(
        TrainingPlanWeek(
            count = 1,
            totalTime = "200 min",
            index = 1,
            trainingDays = listOf(
                TrainingDay(
                    1,
                    listOf(
                        TrainingGuideDomain(
                            listOf(20),
                            1,
                            TrainingExtendedDomain("5000m", "30min", 40, com.stt.android.core.R.string.km),
                            "sadasdsad",
                            1,
                            "Workout 1"
                        ),
                        TrainingGuideDomain(
                            listOf(20),
                            1,
                            TrainingExtendedDomain("10000m", "50min", 60, com.stt.android.core.R.string.km),
                            "sadasdsad",
                            1,
                            "Workout 2"
                        ),
                    ).toImmutableList()
                ),
                TrainingDay(
                    2,
                    emptyList<TrainingGuideDomain>().toImmutableList()
                ),
                TrainingDay(
                    3,
                    listOf(
                        TrainingGuideDomain(
                            listOf(20),
                            1,
                            TrainingExtendedDomain("5000m", "30min", 40, com.stt.android.core.R.string.km),
                            "sadasdsad",
                            1,
                            "Workout 1"
                        ),
                        TrainingGuideDomain(
                            listOf(20),
                            1,
                            TrainingExtendedDomain("10000m", "50min", 60, com.stt.android.core.R.string.km),
                            "sadasdsad",
                            1,
                            "Workout 2"
                        ),
                    ).toImmutableList()
                ),
                TrainingDay(
                    4,
                    emptyList<TrainingGuideDomain>().toImmutableList()
                ),
                TrainingDay(
                    5,
                    listOf(
                        TrainingGuideDomain(
                            listOf(20),
                            1,
                            TrainingExtendedDomain("5000m", "30min", 40, com.stt.android.core.R.string.km),
                            "sadasdsad",
                            1,
                            "Workout 1"
                        ),
                    ).toImmutableList()
                ),
                TrainingDay(
                    6,
                    listOf(
                        TrainingGuideDomain(
                            listOf(20),
                            1,
                            TrainingExtendedDomain("5000m", "30min", 40, com.stt.android.core.R.string.km),
                            "sadasdsad",
                            1,
                            "Workout 1"
                        ),
                        TrainingGuideDomain(
                            listOf(20),
                            1,
                            TrainingExtendedDomain("10000m", "50min", 60, com.stt.android.core.R.string.km),
                            "sadasdsad",
                            1,
                            "Workout 2"
                        ),
                    ).toImmutableList()
                ),
                TrainingDay(
                    7,
                    listOf(
                        TrainingGuideDomain(
                            listOf(20),
                            1,
                            TrainingExtendedDomain("10000m", "50min", 60, com.stt.android.core.R.string.km),
                            "sadasdsad",
                            1,
                            "Workout 2"
                        ),
                    ).toImmutableList()
                )
            ).toImmutableList()
        ),
        TrainingPlanWeek(
            count = 2,
            totalTime = "200 min",
            index = 2,
            trainingDays = listOf(
                TrainingDay(
                    1,
                    listOf(
                        TrainingGuideDomain(
                            listOf(20),
                            1,
                            TrainingExtendedDomain("5000m", "30min", 40, com.stt.android.core.R.string.km),
                            "sadasdsad",
                            1,
                            "Workout 1"
                        ),
                        TrainingGuideDomain(
                            listOf(20),
                            1,
                            TrainingExtendedDomain("10000m", "50min", 60, com.stt.android.core.R.string.km),
                            "sadasdsad",
                            1,
                            "Workout 2"
                        ),
                    ).toImmutableList()
                ),
                TrainingDay(
                    2,
                    emptyList<TrainingGuideDomain>().toImmutableList()
                )
            ).toImmutableList()
        )
    ).toImmutableList()
}

@Preview(showBackground = true)
@Composable
private fun WorkoutDayCardPreview() {
    AppTheme {
        Surface {
            WorkoutPlanScreen(dummyWeeks(), onTrainingDayClicked = {_,_,_ -> }, onRestDayClicked = {})
        }
    }
}

fun <K, V> Map<K, V>.toMutableStateMap(): SnapshotStateMap<K, V> {
    val map = mutableStateMapOf<K, V>()
    map.putAll(this)
    return map
}
