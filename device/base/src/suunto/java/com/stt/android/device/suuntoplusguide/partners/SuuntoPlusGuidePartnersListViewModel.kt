package com.stt.android.device.suuntoplusguide.partners

import androidx.lifecycle.LiveData
import com.airbnb.epoxy.IdUtils
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.common.viewstate.LoadingStateViewModel
import com.stt.android.device.datasource.suuntoplusguide.IsSuuntoPlusGuidePartnerListChangedUseCase
import com.stt.android.domain.connectedservices.FetchPartnerServiceListUseCase
import com.stt.android.domain.connectedservices.ServiceMetadata
import com.stt.android.domain.di.IoThread
import com.stt.android.domain.di.MainThread
import com.stt.android.ui.utils.SingleLiveEvent
import dagger.hilt.android.lifecycle.HiltViewModel
import io.reactivex.Scheduler
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

@HiltViewModel
class SuuntoPlusGuidePartnersListViewModel @Inject constructor(
    private val fetchPartnerServiceListUseCase: FetchPartnerServiceListUseCase,
    private val isSuuntoPlusGuidePartnerListChangedUseCase: IsSuuntoPlusGuidePartnerListChangedUseCase,
    @IoThread ioThread: Scheduler,
    @MainThread mainThread: Scheduler,
    coroutinesDispatchers: CoroutinesDispatchers
) : LoadingStateViewModel<SuuntoPlusGuidePartnersListContainer>(
    ioThread,
    mainThread,
    coroutinesDispatchers
) {
    val onPartnerClicked: LiveData<ServiceMetadata>
        get() = _onPartnerClicked
    private val _onPartnerClicked = SingleLiveEvent<ServiceMetadata>()

    private suspend fun updateServices() {
        notifyLoading(viewState.value?.data)

        try {
            val partners = fetchPartnerServiceListUseCase.fetchAvailableServices()
                .serviceMetadata
                .filter { it.supportsGuides() }
                .map { it.toListItem() }

            notifyDataLoaded(
                SuuntoPlusGuidePartnersListContainer(
                    connectedPartners = partners.filter { it.isConnected },
                    otherPartners = partners.filter { !it.isConnected },
                    onClick = ::handleClick
                )
            )
        } catch (e: Exception) {
            Timber.w(e, "Failed to fetch available services")
            notifyError(e)
        }
    }

    fun reloadIfNeeded() {
        val loading = viewState.value?.isLoading() ?: false
        val initialLoad = viewState.value == null

        // If viewState is null, then this is the first call. Otherwise reload list only if
        // isPartnerListChanged flag is set.
        if (initialLoad || (!loading && isSuuntoPlusGuidePartnerListChangedUseCase.isPartnerListChanged())) {
            isSuuntoPlusGuidePartnerListChangedUseCase.clearPartnerListChanged()
            retryLoading()
        }
    }

    private fun handleClick(serviceMetadata: ServiceMetadata) {
        _onPartnerClicked.value = serviceMetadata
    }

    override fun retryLoading() {
        launch {
            updateServices()
        }
    }

    private fun ServiceMetadata.toListItem() = Partner(
        listItemId = IdUtils.hashString64Bit(partnerUrl),
        iconUrl = iconImageUrl,
        title = localization.viewTitle,
        isConnected = isConnected,
        serviceMetadata = this,
    )
}

fun ServiceMetadata.supportsGuides() = "SUUNTOPLUS_GUIDES" in clientScopes
