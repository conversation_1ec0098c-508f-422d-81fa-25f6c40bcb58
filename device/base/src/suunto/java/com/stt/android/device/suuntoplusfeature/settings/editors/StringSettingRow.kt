package com.stt.android.device.suuntoplusfeature.settings.editors

import androidx.compose.animation.animateContentSize
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.relocation.BringIntoViewRequester
import androidx.compose.foundation.relocation.bringIntoViewRequester
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.material.TextField
import androidx.compose.material.TextFieldDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.tooling.preview.Preview
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.bodySmall
import com.stt.android.compose.theme.spacing
import com.stt.android.device.R

@OptIn(ExperimentalFoundationApi::class)
@Composable
internal fun StringSettingRow(
    name: String,
    initialValue: String?,
    nonce: Int, // Reset text field to initialValue when nonce changes
    maxStringLengthBytes: Int?,
    mandatoryMessage: String?,
    onSettingChange: (newValue: String) -> Unit,
    enabled: Boolean,
    modifier: Modifier = Modifier
) {
    var value by rememberSaveable(nonce) {
        mutableStateOf(initialValue.orEmpty())
    }

    val bringIntoViewRequester = remember { BringIntoViewRequester() }
    val resources = LocalContext.current.resources
    val errorText by remember {
        derivedStateOf {
            if (value.isEmpty() && mandatoryMessage != null) {
                mandatoryMessage.ifEmpty { resources.getString(R.string.suunto_plus_number_validation_required_value) }
            } else {
                null
            }
        }
    }

    if (errorText != null) {
        LaunchedEffect(Unit) {
            // Scroll to make sure error text is visible on the screen when appearing
            bringIntoViewRequester.bringIntoView()
        }
    }

    Column(
        modifier = modifier
            .fillMaxWidth()
            .animateContentSize()
            .bringIntoViewRequester(bringIntoViewRequester)
    ) {
        TextField(
            label = {
                Text(text = name)
            },
            value = value,
            onValueChange = { newValue ->
                if (maxStringLengthBytes == null || newValue.encodeToByteArray().size <= maxStringLengthBytes) {
                    value = newValue
                    onSettingChange(newValue)
                }
            },
            isError = errorText != null,
            singleLine = true,
            colors = TextFieldDefaults.textFieldColors(
                backgroundColor = Color.Unspecified,
                unfocusedIndicatorColor = Color.Unspecified
            ),
            enabled = enabled,
            modifier = Modifier.fillMaxWidth()
        )

        Box(
            modifier = Modifier
                .padding(horizontal = MaterialTheme.spacing.medium)
                .animateContentSize()
        ) {
            errorText?.let {
                Text(
                    text = it,
                    color = MaterialTheme.colors.error,
                    style = MaterialTheme.typography.bodySmall,
                    modifier = Modifier.padding(vertical = MaterialTheme.spacing.small)
                )
            }
        }
    }
}

@Preview
@Composable
private fun StringSettingRowUnfocusedPreview() {
    AppTheme {
        Surface {
            StringSettingRow(
                name = "Sensor ID",
                initialValue = "abcd",
                nonce = 0,
                mandatoryMessage = null,
                maxStringLengthBytes = 40,
                onSettingChange = {},
                enabled = true
            )
        }
    }
}

@Preview
@Composable
private fun StringSettingRowMissingMandatoryValuePreview() {
    AppTheme {
        Surface {
            StringSettingRow(
                name = "Sensor ID",
                initialValue = null,
                nonce = 0,
                mandatoryMessage = "Please fill the serial of your sensor thingie here",
                maxStringLengthBytes = 40,
                onSettingChange = {},
                enabled = true
            )
        }
    }
}
