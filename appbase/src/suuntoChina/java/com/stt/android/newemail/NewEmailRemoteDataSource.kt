package com.stt.android.newemail

import javax.inject.Inject

class NewEmailRemoteDataSource @Inject constructor(private val newEmailApi: NewEmailApi) :
    NewEmailDataSource {

    override suspend fun changeEmail(email: String, token: String): <PERSON><PERSON><PERSON> {
        return newEmailApi.changeEmail(email, token).payloadOrThrow()
    }

    override suspend fun sendEmailVerificationCode(email: String): Any {
        return newEmailApi.sendEmailVerificationCode(email).payloadOrThrow()
    }

    override suspend fun checkEmailVerificationCode(email: String, pin: String): String {
        return newEmailApi.checkEmailVerificationCode(email, pin)
            .payloadOrThrow()
    }

    override suspend fun bindEmail(email: String, newPassword: String, token: String): UserInfoFromChina? {
        return newEmailApi.bindEmail(email, newPassword, token).payloadOrThrow()
    }
}
