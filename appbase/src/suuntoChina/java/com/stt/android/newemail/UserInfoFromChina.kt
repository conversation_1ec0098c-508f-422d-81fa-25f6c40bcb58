package com.stt.android.newemail

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass
@JsonClass(generateAdapter = true)
data class UserInfoFromChina(
    @<PERSON><PERSON>(name = "username")
    val userName: String?,
    @<PERSON><PERSON>(name = "userKey")
    val userKey: String?,
    @<PERSON><PERSON>(name = "createdDate")
    val createdDate: Long?,
    @<PERSON><PERSON>(name = "lastModified")
    val lastModified: Long?,
    @<PERSON><PERSON>(name = "lastLogin")
    val lastLogin: Long?,
    @<PERSON><PERSON>(name = "realName")
    val realName: String?,
    @<PERSON><PERSON>(name = "gender")
    val gender: String?,
    @<PERSON><PERSON>(name = "uuid")
    val uuid: String?,
    @<PERSON><PERSON>(name = "sessionkey")
    val sessionkey: String?,
    @<PERSON><PERSON>(name = "email")
    val email: String?,
    @<PERSON><PERSON>(name = "facebookConnected")
    val facebookConnected: Boolean?,
    @<PERSON><PERSON>(name = "twitterConnected")
    val twitterConnected: Boolean?,
    @<PERSON>son(name = "key")
    val key: String?,
    @Json(name = "defaultBinaryStorageLocation")
    val defaultBinaryStorageLocation: String?,
    @<PERSON><PERSON>(name = "currentBlobStorageLocation")
    val currentBlobStorageLocation: String?,
    @Json(name = "emarsysId")
    val emarsysId: String?,
    @Json(name = "country")
    val country: String?,
    @Json(name = "watchUserKey")
    val watchUserKey: String?,
    @Json(name = "phoneNumber")
    val phoneNumber: String?,
)
