package com.stt.android.newemail

import com.squareup.moshi.Moshi
import com.stt.android.remote.AuthProvider
import com.stt.android.remote.BaseUrl
import com.stt.android.remote.SharedOkHttpClient
import com.stt.android.remote.UserAgent
import com.stt.android.remote.di.BrandOkHttpConfigFactory
import com.stt.android.remote.di.RestApiFactory
import dagger.Binds
import dagger.Module
import dagger.Provides
import okhttp3.OkHttpClient

@Module
abstract class NewEmailModule {

    @Binds
    abstract fun bindNewEmailRemoteDataSource(
        newEmailRemoteDataSource: NewEmailRemoteDataSource
    ): NewEmailDataSource

    companion object {

        @Provides
        fun provideNewEmail(
            @SharedOkHttpClient sharedClient: OkHttpClient,
            @BaseUrl baseUrl: String,
            @UserAgent userAgent: String,
            authProvider: AuthProvider,
            moshi: <PERSON><PERSON>
        ): NewEmailApi {
            return RestApiFactory.buildRestApi(
                sharedClient,
                baseUrl,
                NewEmailApi::class.java,
                BrandOkHttpConfigFactory.getStOkHttpConfig(authProvider, userAgent),
                moshi
            )
        }
    }
}
