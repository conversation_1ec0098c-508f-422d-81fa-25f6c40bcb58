package com.stt.android.newemail

import android.app.Activity
import android.content.Intent
import androidx.compose.material.Surface
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.google.android.material.snackbar.Snackbar
import com.stt.android.R
import com.stt.android.compose.base.BaseFragment
import com.stt.android.compose.theme.AppTheme
import com.stt.android.extensions.hideKeyboard
import com.stt.android.resetpassword.BaseContentBody
import com.stt.android.utils.CustomTabsUtils
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber

@AndroidEntryPoint
class FragmentNewEmailVerifyCode : BaseFragment() {
    private val viewModel: FragmentBindEmailVerifyCodeViewModel by viewModels()
    private val newEmailVerifyCodeArgs: FragmentNewEmailVerifyCodeArgs by navArgs()

    @Composable
    override fun SetContentView() {
        BindEmailVerifyCodeScreen()
    }

    @Composable
    private fun BindEmailVerifyCodeScreen() {
        val uiState = viewModel.uiState
        val commonUIState by viewModel.commonUIState.collectAsState()
        LaunchedEffect(uiState.changeEmailSuccess) {
            uiState.changeEmailSuccess?.let {
                Timber.d("token: $it")
                Snackbar.make(
                    requireView(),
                    R.string.change_successful,
                    Snackbar.LENGTH_LONG
                ).apply {
                    addCallback(object : Snackbar.Callback() {
                        override fun onDismissed(transientBottomBar: Snackbar?, event: Int) {
                            activity?.setResult(Activity.RESULT_OK)
                            activity?.finish()
                        }
                    })
                    show()
                }
            }
        }

        BaseContentBody(
            viewModel = viewModel,
            onBackClick = {
                findNavController().popBackStack()
            }
        ) {
            ContentBody(
                commonUIState.isLoading,
                uiState.verifyCode,
                uiState.verifyCodeIsExpired,
                newEmailVerifyCodeArgs.email,
                onInputVerifyCode = {
                    viewModel.inputVerifyCode(it)
                },
                onVerificationCodeClicked = {
                    viewModel.checkVerificationCode(
                        newEmailVerifyCodeArgs.email,
                        uiState.verifyCode,
                        true
                    )
                    activity?.hideKeyboard()
                },
                onResendClicked = {
                    viewModel.sendEmailVerificationCode(
                        newEmailVerifyCodeArgs.email,
                    )
                },
                onContactClicked = {
                    activity?.apply {
                        CustomTabsUtils.launchCustomTab(
                            this,
                            getString(R.string.contact_support_link_suunto)
                        )
                    }
                }
            )
        }
    }
}

@Preview
@Composable
private fun BindEmailVerifyCodeScreenPreview() {
    AppTheme {
        Surface {
            ContentBody()
        }
    }
}

@Composable
private fun ContentBody(
    isLoading: Boolean = false,
    verifyCode: String = "",
    verifyCodeIsExpired: Boolean = false,
    email: String = "",
    onInputVerifyCode: (String) -> Unit = {},
    onVerificationCodeClicked: () -> Unit = {},
    onResendClicked: () -> Unit = {},
    onContactClicked: () -> Unit = {},
) {
    BindEmailVerifyCodeBody(
        isLoading,
        verifyCode,
        verifyCodeIsExpired = verifyCodeIsExpired,
        email,
        onInputVerifyCode = onInputVerifyCode,
        onVerificationCodeClicked = onVerificationCodeClicked,
        onResendClicked = onResendClicked,
        onContactClicked = onContactClicked
    )
}
