package com.stt.android.newemail

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.viewModelScope
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.compose.base.BaseViewModel
import com.stt.android.controllers.UserSettingsController
import com.stt.android.exceptions.remote.ClientError
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

@HiltViewModel
class FragmentBindEmailViewModel @Inject constructor(
    private val userSettingsController: UserSettingsController,
    private val newEmailRepository: NewEmailRepository,
    private val dispatchers: CoroutinesDispatchers
) : BaseViewModel() {

    var uiState by mutableStateOf(BindEmailUIState())

    private val updateEmailHandler = CoroutineExceptionHandler { _, throwable ->
        when (throwable) {
            is ClientError.Conflict -> {
                uiState = uiState.copy(emailError = EmailError.EmailHasBeenRegistered)
                loadingEnd()
            }

            is ClientError.BadRequest -> {
                uiState = uiState.copy(emailError = EmailError.EmailHasNotChanged)
                loadingEnd()
            }

            else -> {
                loadingEndWithFail(throwable)
            }
        }
        Timber.w(throwable, "message: ${throwable.message}")
    }

    fun sendEmailVerificationCode(email: String) {
        when {
            !isEmailValid(email) -> {
                uiState = uiState.copy(emailError = EmailError.EmailIsError)
            }
            userSettingsController.settings.email == email -> {
                uiState = uiState.copy(emailError = EmailError.EmailHasNotChanged)
            }
            else -> {
                loadingStart()
                viewModelScope.launch(dispatchers.io + updateEmailHandler) {
                    newEmailRepository.sendEmailVerificationCode(email)
                    uiState = uiState.copy(sendEmailVerificationCodeSuccess = true)
                    loadingEnd()
                }
            }
        }
    }

    fun inputEmail(email: String) {
        uiState = uiState.copy(
            email = email,
            emailError = null,
        )
    }

    fun clearSendEmailVerificationCodeSuccess() {
        uiState = uiState.copy(sendEmailVerificationCodeSuccess = null)
    }
}

data class BindEmailUIState(
    val email: String = "",
    val sendEmailVerificationCodeSuccess: Boolean? = null,
    val emailError: EmailError? = null,
)
