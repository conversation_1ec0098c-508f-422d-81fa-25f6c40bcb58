package com.stt.android.newemail

import androidx.compose.material.Surface
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import com.stt.android.R
import com.stt.android.compose.base.BaseFragment
import com.stt.android.compose.theme.AppTheme
import com.stt.android.resetpassword.BaseContentBody
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class FragmentNewEmail : BaseFragment() {
    private val viewModel: FragmentBindEmailViewModel by viewModels()

    @Composable
    override fun SetContentView() {
        BindEmailScreen()
    }

    @Composable
    private fun BindEmailScreen() {
        val uiState = viewModel.uiState
        val commonUIState by viewModel.commonUIState.collectAsState()
        LaunchedEffect(uiState.sendEmailVerificationCodeSuccess) {
            uiState.sendEmailVerificationCodeSuccess?.let {
                findNavController().navigate(
                    FragmentNewEmailDirections.actionNewEmailVerifyCode().setEmail(uiState.email)
                )
                viewModel.clearSendEmailVerificationCodeSuccess()
            }
        }

        BaseContentBody(
            viewModel = viewModel,
            onBackClick = {
                activity?.finish()
            }
        ) {
            ContentBody(
                commonUIState.isLoading,
                uiState.email,
                uiState.emailError,
                onInputEmail = {
                    viewModel.inputEmail(it)
                },
                onBtnClicked = { email ->
                    viewModel.sendEmailVerificationCode(email)
                }
            )
        }
    }
}

@Preview
@Composable
private fun BindEmailScreenPreview() {
    AppTheme {
        Surface {
            ContentBody()
        }
    }
}

@Composable
@OptIn(ExperimentalComposeUiApi::class)
private fun ContentBody(
    isLoading: Boolean = false,
    email: String = "",
    emailError: EmailError? = null,
    onInputEmail: (String) -> Unit = {},
    onBtnClicked: (String) -> Unit = {}
) {
    val keyboardController = LocalSoftwareKeyboardController.current
    BindEmailBody(
        title = stringResource(id = R.string.new_email_address),
        isLoading = isLoading,
        email = email,
        emailError = emailError,
        onInputEmail = onInputEmail,
        onBtnClicked = {
            onBtnClicked.invoke(email)
            keyboardController?.hide()
        }
    )
}
