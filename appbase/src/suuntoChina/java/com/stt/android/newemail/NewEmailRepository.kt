package com.stt.android.newemail

import com.stt.android.home.settings.PhoneNumberUtil.PHONE_REGION_CHINA
import com.stt.android.resetpassword.ResetPasswordDataSource
import javax.inject.Inject

class NewEmailRepository @Inject constructor(
    private val newEmailDataSource: NewEmailDataSource,
    private val resetPasswordDataSource: ResetPasswordDataSource
) {
    suspend fun sendEmailVerificationCode(email: String): Any =
        newEmailDataSource.sendEmailVerificationCode(email)

    suspend fun checkEmailVerificationCode(email: String, pin: String): String =
        newEmailDataSource.checkEmailVerificationCode(email, pin)

    suspend fun changeEmail(email: String, token: String): Boolean =
        newEmailDataSource.changeEmail(email, token)

    suspend fun bindEmail(email: String, newPassword: String, token: String): UserInfoFromChina? {
        return newEmailDataSource.bindEmail(email, newPassword, token)
    }

    suspend fun changePassword(
        phoneNumberVerificationToken: String,
        phoneNumber: String,
        newPassword: String
    ): Boolean {
        val fullPhoneNumber = "$PHONE_REGION_CHINA$phoneNumber"
        return resetPasswordDataSource.changePassword(
            phoneNumberVerificationToken,
            fullPhoneNumber,
            newPassword
        )
    }
}
