package com.stt.android.newemail

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.outlined.Email
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import com.stt.android.R
import com.stt.android.compose.modifiers.clickableThrottleFirst
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.bodyBold
import com.stt.android.compose.theme.bodyLargeBold
import com.stt.android.compose.theme.nearBlack
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.widgets.LoadingContent
import com.stt.android.compose.widgets.PrimaryButton
import com.stt.android.compose.widgets.TextFieldInputWithError

@Preview
@Composable
private fun BindEmailBodyPreview() {
    AppTheme {
        Surface {
            BindEmailBody(
                title = "this is title",
                isLoading = false,
                email = "<EMAIL>",
                onInputEmail = {},
                onBtnClicked = {}
            )
        }
    }
}

@Composable
fun BindEmailBody(
    title: String,
    isLoading: Boolean,
    email: String,
    onInputEmail: (String) -> Unit,
    onBtnClicked: () -> Unit,
    modifier: Modifier = Modifier,
    showSkip: Boolean = false,
    emailError: EmailError? = null,
    onSkipClicked: (() -> Unit)? = null,
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Top,
        modifier = modifier
            .fillMaxSize()
            .verticalScroll(rememberScrollState())
    ) {
        Spacer(modifier = Modifier.height(MaterialTheme.spacing.xlarge))
        Text(
            text = title,
            style = MaterialTheme.typography.bodyLargeBold,
            color = MaterialTheme.colors.nearBlack,
            textAlign = TextAlign.Center,
            modifier = Modifier.padding(bottom = MaterialTheme.spacing.xxxxlarge)
        )

        TextFieldInputWithError(
            currentText = email,
            placeholderText = stringResource(id = R.string.email),
            onChanged = { onInputEmail.invoke(it) },
            keyboardType = KeyboardType.Email,
            onActionDone = {
                if (email.isNotEmpty()) {
                    onBtnClicked.invoke()
                }
            },
            prefixImageVector = Icons.Outlined.Email,
            errorMessage = emailError?.let { stringResource(id = it.resId) } ?: "",
        )

        PrimaryButton(
            enabled = email.isNotEmpty(),
            onClick = {
                onBtnClicked.invoke()
            },
            text = stringResource(R.string.continue_str),
            modifier = Modifier
                .fillMaxWidth()
                .padding(
                    horizontal = MaterialTheme.spacing.medium,
                    vertical = MaterialTheme.spacing.medium
                )
        )

        if (showSkip) {
            Spacer(modifier = Modifier.weight(1f))

            Text(
                stringResource(R.string.skip_str).uppercase(),
                style = MaterialTheme.typography.bodyBold,
                color = MaterialTheme.colors.primary,
                modifier = Modifier
                    .padding(bottom = MaterialTheme.spacing.xlarge)
                    .clickableThrottleFirst {
                        onSkipClicked?.invoke()
                    }
            )
        }
    }
    LoadingContent(isLoading)
}
