package com.stt.android.newemail

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.viewModelScope
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.base.BaseHandlerViewModel
import com.stt.android.controllers.UserSettingsController
import com.stt.android.exceptions.remote.STTError
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class FragmentBindEmailVerifyCodeViewModel @Inject constructor(
    private val newEmailRepository: NewEmailRepository,
    private val dispatchers: CoroutinesDispatchers,
    private val userSettingsController: UserSettingsController
) : BaseHandlerViewModel() {

    var uiState by mutableStateOf(BindEmailVerifyCodeUIState())
    fun inputVerifyCode(verifyCode: String) {
        uiState = uiState.copy(
            verifyCode = verifyCode,
            verifyCodeIsExpired = false,
        )
    }

    fun checkVerificationCode(email: String, verifyCode: String, isModify: Boolean = false) {
        loadingStart()
        viewModelScope.launch(
            dispatchers.io + getCheckVerificationCodeFailedHandler {
                uiState = uiState.copy(verifyCodeIsExpired = true)
            }
        ) {
            val emailVerificationToken = newEmailRepository.checkEmailVerificationCode(email, verifyCode)
            uiState = if (isModify) {
                val result = newEmailRepository.changeEmail(email, emailVerificationToken)
                if (result) {
                    userSettingsController.storeSettings(
                        userSettingsController.settings.copyWithEmail(
                            email,
                            false
                        )
                    )
                    uiState.copy(changeEmailSuccess = true)
                } else {
                    throw STTError.ModifyEmailFailed()
                }
            } else {
                uiState.copy(
                    emailVerifyToken = emailVerificationToken,
                )
            }
            loadingEnd()
        }
    }

    fun sendEmailVerificationCode(email: String) {
        loadingStart()
        viewModelScope.launch(dispatchers.io + exceptionHandler) {
            newEmailRepository.sendEmailVerificationCode(email)
            loadingEnd()
        }
    }
}

data class BindEmailVerifyCodeUIState(
    val verifyCode: String = "",
    val verifyCodeIsExpired: Boolean = false,
    val emailVerifyToken: String? = null,
    val changeEmailSuccess: Boolean? = null,
)
