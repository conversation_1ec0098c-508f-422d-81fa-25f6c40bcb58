package com.stt.android.newemail

import com.stt.android.remote.response.AskoResponse
import retrofit2.http.POST
import retrofit2.http.Query

interface NewEmailApi {
    @POST("auth/email/send")
    suspend fun sendEmailVerificationCode(@Query("email") email: String): AskoResponse<Any>

    @POST("auth/email/verify")
    suspend fun checkEmailVerificationCode(
        @Query("email") email: String,
        @Query("pin") pin: String
    ): AskoResponse<String>

    @POST("change/email")
    suspend fun changeEmail(@Query("email") email: String, @Query("token") token: String): AskoResponse<Boolean>

    @POST("bind/email")
    suspend fun bindEmail(
        @Query("email") email: String,
        @Query("newPassword") newPassword: String,
        @Query("token") token: String
    ): AskoResponse<UserInfoFromChina?>
}
