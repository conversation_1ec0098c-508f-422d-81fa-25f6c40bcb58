package com.stt.android.social.userprofile

import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.UserSettingsController
import timber.log.Timber
import java.security.MessageDigest
import java.util.UUID
import javax.inject.Inject

class CustomerServiceBuilder @Inject constructor(
    private val userSettingsController: UserSettingsController,
    private val currentUserController: CurrentUserController
) {

    fun buildUrlParams(imUserKey: String): Map<String, String?> = buildMap {
        runCatching {
            val userKey = currentUserController.currentUser.key
            val username = currentUserController.currentUser.username
            val userPhoneNumber = userSettingsController.settings.phoneNumber
            val userEmail = userSettingsController.settings.email

            val nonce = UUID.randomUUID().toString()
            val timestamp = System.currentTimeMillis().toString()
            val signature = with(MessageDigest.getInstance("SHA-1")) {
                update("nonce=$nonce&timestamp=$timestamp&web_token=$userKey&$imUserKey".toByteArray())
                digest().toHexString().uppercase()
            }
            put("language", "zh-cn")
            put("channel", "App")
            put("c_name", username)
            put("c_phone", userPhoneNumber)
            put("c_email", userEmail)
            put("c_cf_unionid", userKey)
            put("nonce", nonce)
            put("timestamp", timestamp)
            put("web_token", userKey)
            put("signature", signature)
            put("encryption_algorithm", "SHA1")
            put("customer_token", userPhoneNumber)
        }.onFailure {
            Timber.w(it, "Fail to build service url params.")
        }
    }

    fun buildFullUrl(host: String, params: Map<String, String?>) = with(StringBuilder(host)) {
        params.entries.filter { !it.value.isNullOrEmpty() }
            .forEach { (key, value) -> append("&").append(key).append("=").append(value) }
        toString()
    }
}
