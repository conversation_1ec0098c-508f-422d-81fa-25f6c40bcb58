package com.stt.android.workoudata

import com.stt.android.data.source.local.workout.LocalWeChatWorkoutData
import com.stt.android.data.source.local.workout.WeChatWorkoutDataDao
import com.stt.android.data.workout.toNewLocalWorkoutData
import com.stt.android.data.workout.toNewWorkoutData
import com.stt.android.domain.workouts.WeChatWorkoutData
import com.stt.android.domain.workouts.WeChatWorkoutDataSource
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.domain.workouts.extensions.SummaryExtension
import javax.inject.Inject

class WeChatWorkoutDataSourceImpl @Inject constructor(
    private val dao: WeChatWorkoutDataDao
) : WeChatWorkoutDataSource {

    override fun store(workoutHeader: WorkoutHeader, extension: SummaryExtension) {
        dao.insertWorkoutData(toNewLocalWorkoutData(workoutHeader, extension))
    }

    override suspend fun findUnSyncData(): List<WeChatWorkoutData> {
        return dao.findUnSyncWorkout().map { toNewWorkoutData(it) }
    }

    override suspend fun updateStatus(id: Int, sync: Boolean) {
        dao.updateSyncStatus(
            id,
            if (sync) LocalWeChatWorkoutData.STATUS_SYNCED else LocalWeChatWorkoutData.STATUS_NOT_SYNCED
        )
    }

    override suspend fun findSyncedData(): List<WeChatWorkoutData> {
        return dao.findSyncedWorkout().map { toNewWorkoutData(it) }
    }

    override suspend fun deleteSynced() {
        dao.deleteSynced()
    }

    override fun deleteAll() {
        dao.deleteAll()
    }
}
