package com.stt.android.utils

import android.content.Context
import android.graphics.Bitmap
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.ui.tasks.BaseWorkoutImageFilesHelper
import kotlinx.coroutines.withContext
import java.io.File
import javax.inject.Inject

class WorkoutImageFilesHelper @Inject constructor(
    context: Context,
    dispatchers: CoroutinesDispatchers
) : BaseWorkoutImageFilesHelper(context, dispatchers) {
    override suspend fun saveBitmapToJpegFile(bitmap: Bitmap, dest: File): String =
        withContext(dispatchers.io) {
            CompressImageUtils.compressImageToLimitedSize(bitmap, dest)
            FileUtils.calculateMd5(dest.absolutePath)
        }
}
