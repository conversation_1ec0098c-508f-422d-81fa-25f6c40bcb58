package com.stt.android.locationinfo

import com.stt.android.analytics.FetchLocationInfo
import com.stt.android.analytics.FetchRemoteLocationInfoRepository
import com.stt.android.domain.mapbox.FetchLocationNameUseCase
import com.stt.android.domain.mapbox.Place
import com.stt.android.usecases.location.LastKnownLocationUseCase
import me.majiajie.tinypinyin.Pinyin
import javax.inject.Inject

class FetchLocationInfoUseCase @Inject constructor(
    locationUseCase: LastKnownLocationUseCase,
    fetchLocationNameUseCase: FetchLocationNameUseCase,
    fetchRemoteLocationInfoRepository: FetchRemoteLocationInfoRepository,
) : FetchLocationInfo(
    locationUseCase,
    fetchLocationNameUseCase,
    fetchRemoteLocationInfoRepository
) {

    override fun convertPlace(place: Place?): Place? = place?.copy(
        city = convertToPinyin(place.city),
        province = convertToPinyin(place.province),
        country = convertToPinyin(place.country)
    )

    private fun convertToPinyin(value: String?): String? = value?.let {
        if (it.isNotEmpty() && Pinyin.isChinese(it.first())) Pinyin.toPinyin(it, "") else it
    }
}
