package com.stt.android.workouts.sharepreview

import androidx.lifecycle.SavedStateHandle
import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.analytics.FirebaseAnalyticsTracker
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.DiveExtensionDataModel
import com.stt.android.controllers.FitnessExtensionDataModel
import com.stt.android.controllers.IntensityExtensionDataModel
import com.stt.android.controllers.JumpRopeExtensionDataModel
import com.stt.android.controllers.PicturesController
import com.stt.android.controllers.SlopeSkiDataModel
import com.stt.android.controllers.SummaryExtensionDataModel
import com.stt.android.controllers.SwimmingExtensionDataModel
import com.stt.android.controllers.WorkoutHeaderController
import com.stt.android.data.workout.WorkoutHeaderRepository
import com.stt.android.data.workout.pictures.PictureFileRepository
import com.stt.android.domain.achievements.GetAchievementUseCase
import com.stt.android.domain.sml.FetchSmlUseCase
import com.stt.android.exceptions.ReviewImageFailedException
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.models.MapSelectionModel
import com.stt.android.multimedia.sportie.SportieHelper
import com.stt.android.ui.controllers.WorkoutDataLoaderController
import com.stt.android.ui.tasks.BitmapLoadAndResizer
import com.stt.android.ui.tasks.LoadAndResizeResult
import com.stt.android.utils.WorkoutImageFilesHelper
import com.stt.android.workouts.SyncSingleWorkoutUseCase
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
class WorkoutSharePreviewViewModel @Inject constructor(
    savedStateHandle: SavedStateHandle,
    slopeSkiDataModel: SlopeSkiDataModel,
    diveExtensionDataModel: DiveExtensionDataModel,
    summaryExtensionDataModel: SummaryExtensionDataModel,
    fitnessExtensionDataModel: FitnessExtensionDataModel,
    intensityExtensionDataModel: IntensityExtensionDataModel,
    picturesController: PicturesController,
    dataLoaderController: WorkoutDataLoaderController,
    swimmingExtensionDataModel: SwimmingExtensionDataModel,
    fetchSmlUseCase: FetchSmlUseCase,
    workoutHeaderController: WorkoutHeaderController,
    syncSingleWorkoutUseCase: SyncSingleWorkoutUseCase,
    infoModelFormatter: InfoModelFormatter,
    workoutImageFilesHelper: WorkoutImageFilesHelper,
    bitmapLoadAndResizer: BitmapLoadAndResizer,
    getAchievementUseCase: GetAchievementUseCase,
    mapSelectionModel: MapSelectionModel,
    firebaseAnalyticsTracker: FirebaseAnalyticsTracker,
    workoutHeaderRepository: WorkoutHeaderRepository,
    amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker,
    coroutinesDispatchers: CoroutinesDispatchers,
    sportieHelper: SportieHelper,
    private val sharedImageReviewRepository: SharedImageReviewRepository,
    private val pictureFileRepository: PictureFileRepository,
    jumpRopeExtensionDataModel: JumpRopeExtensionDataModel,
) : BaseWorkoutSharePreviewViewModel(
    savedStateHandle,
    slopeSkiDataModel,
    diveExtensionDataModel,
    summaryExtensionDataModel,
    fitnessExtensionDataModel,
    intensityExtensionDataModel,
    picturesController,
    dataLoaderController,
    swimmingExtensionDataModel,
    fetchSmlUseCase,
    workoutHeaderController,
    syncSingleWorkoutUseCase,
    infoModelFormatter,
    workoutImageFilesHelper,
    bitmapLoadAndResizer,
    getAchievementUseCase,
    mapSelectionModel,
    firebaseAnalyticsTracker,
    workoutHeaderRepository,
    amplitudeAnalyticsTracker,
    jumpRopeExtensionDataModel,
    coroutinesDispatchers,
    sportieHelper,
) {

    override suspend fun reviewImage(result: LoadAndResizeResult) {
        if (!sharedImageReviewRepository.getImageReviewResult(
                pictureFileRepository.getPictureFile(
                    result.name
                )
            )
        ) {
            throw ReviewImageFailedException()
        }
    }
}
