package com.stt.android.sharing

import android.app.Activity
import android.content.ContentResolver
import android.content.Intent
import android.net.Uri
import com.stt.android.R
import com.stt.android.ShareTargetIdConstant.DOU_YIN_APP_ID
import com.stt.android.ShareTargetIdConstant.TARGET_WECHAT
import com.stt.android.ShareTargetIdConstant.TARGET_WECHAT_MOMENTS
import com.stt.android.ShareTargetIdConstant.WEI_BO_APP_ID
import com.stt.android.ShareTargetIdConstant.WE_CHAT_APP_ID
import com.stt.android.ShareTargetIdConstant.XIAO_HONG_SHU_APP_ID
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.annualreport.ShareErrorType
import com.stt.android.common.coroutines.CoroutineViewModel
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.multimedia.MediaStoreUtils
import com.stt.android.multimedia.MediaType
import com.stt.android.sharingplatform.DouYinAPI
import com.stt.android.sharingplatform.ShareResultHandler
import com.stt.android.sharingplatform.SharingResultState
import com.stt.android.sharingplatform.WeChatAPI
import com.stt.android.sharingplatform.WeiboAPI
import com.stt.android.sharingplatform.XhsAPI
import com.stt.android.workouts.sharepreview.customshare.ShareTarget
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.toImmutableList
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import timber.log.Timber
import java.io.FileOutputStream
import java.util.UUID
import javax.inject.Inject

/**
 * TODO: improve sending analysis data, make it more generic
 */
@HiltViewModel
class AppSharingViewModel @Inject constructor(
    private val weChatAPI: WeChatAPI,
    private val weiboAPI: WeiboAPI,
    private val douYinAPI: DouYinAPI,
    private val xhsAPI: XhsAPI,
    private val appSharingAnalysis: AppSharingAnalysisImpl,
    coroutinesDispatchers: CoroutinesDispatchers
) : CoroutineViewModel(coroutinesDispatchers) {

    private val _shareErrorEvent = MutableSharedFlow<ShareErrorType>()
    val shareErrorEvent = _shareErrorEvent.asSharedFlow()


    private val _shareTargets = MutableStateFlow<ImmutableList<ShareTarget>>(persistentListOf())
    val shareTargets = _shareTargets.asStateFlow()

    fun setShareResultForWeibo(intent: Intent, shareResultHandler: ShareResultHandler) {
        weiboAPI.onShareResult(intent, shareResultHandler)
    }

    private fun hasThirdPlatform(appId: String, activity: Activity): Boolean {
        return when (appId) {
            WE_CHAT_APP_ID -> weChatAPI.appInstalled()
            WEI_BO_APP_ID -> weiboAPI.appInstalled()
            DOU_YIN_APP_ID -> douYinAPI.supportedSharing(activity)
            XIAO_HONG_SHU_APP_ID -> xhsAPI.supportedShareNote(activity)
            else -> false
        }
    }

    /**
     * wechat moments only support single image
     * dou yin, xiao hong shu don't support sharing link
     */
    fun loadTargets(sharingType: SharingType, singleImage: Boolean = false) {
        val shareTargets = mutableListOf<ShareTarget>().apply {
            when (sharingType) {
                SharingType.MEDIA -> {
                    add(ShareTarget.SaveToMedia)
                    if (singleImage) {
                        add(
                            ShareTarget.CustomTarget(
                                WE_CHAT_APP_ID,
                                TARGET_WECHAT,
                                R.drawable.share_wechat,
                                R.string.we_chat
                            )
                        )
                        add(
                            ShareTarget.CustomTarget(
                                WE_CHAT_APP_ID,
                                TARGET_WECHAT_MOMENTS,
                                R.drawable.share_wechat_moments,
                                R.string.we_chat_moments
                            )
                        )
                    }
                    add(
                        ShareTarget.CustomTarget(
                            XIAO_HONG_SHU_APP_ID,
                            "",
                            R.drawable.icon_xhs,
                            R.string.xiao_hong_shu_title
                        )
                    )
                    add(
                        ShareTarget.CustomTarget(
                            DOU_YIN_APP_ID,
                            "",
                            R.drawable.icon_douyin,
                            R.string.douyin_title
                        )
                    )
                }

                SharingType.LINK -> {
                    add(
                        ShareTarget.CustomTarget(
                            WE_CHAT_APP_ID,
                            TARGET_WECHAT,
                            R.drawable.share_wechat,
                            R.string.we_chat
                        )
                    )
                    add(
                        ShareTarget.CustomTarget(
                            WE_CHAT_APP_ID,
                            TARGET_WECHAT_MOMENTS,
                            R.drawable.share_wechat_moments,
                            R.string.we_chat_moments
                        )
                    )
                }

            }
            add(
                ShareTarget.CustomTarget(
                    WEI_BO_APP_ID,
                    "",
                    R.drawable.icon_weibo,
                    R.string.weibo
                )
            )
            add(ShareTarget.DelegateToOS)
        }
        _shareTargets.value = shareTargets.toImmutableList()
    }

    fun share(
        sharingInfo: SharingInfo,
        shareTarget: ShareTarget,
        shareResultHandler: ShareResultHandler,
        activity: Activity
    ) {
        when (sharingInfo.sharingType) {
            SharingType.LINK -> {
                when (shareTarget) {
                    is ShareTarget.DelegateToOS -> {
                        SharingToSystemHelper.shareToSystem(
                            activity, sharingInfo, AnalysisData(
                                eventName = AnalyticsEvent.H5SHARE,
                                analyticsProperties = appSharingAnalysis.geSharingLinkAnalysis(
                                    sharingInfo.shareSourceName
                                )
                            )
                        )
                    }

                    is ShareTarget.CustomTarget -> handleCustomShareTargetForLink(
                        activity,
                        sharingInfo,
                        shareTarget,
                        shareResultHandler,
                    )

                    else -> {
                        Timber.w("Custom target not supported share link: $shareTarget")
                        shareResultHandler.invoke(SharingResultState.Cancel)
                    }
                }
                appSharingAnalysis.senAnalysisForShareLink(
                    shareTarget,
                    sharingInfo.shareSourceName
                )
            }

            SharingType.MEDIA -> {
                when (shareTarget) {
                    is ShareTarget.SaveToMedia -> saveMedia(
                        activity.contentResolver,
                        sharingInfo.resourceUris,
                        sharingInfo.mediaType,
                    )

                    is ShareTarget.DelegateToOS -> SharingToSystemHelper.shareToSystem(
                        activity, sharingInfo,
                        AnalysisData(
                            eventName = AnalyticsEvent.H5SHARE,
                            analyticsProperties = appSharingAnalysis.geSharingMediaAnalysis(
                                sharingInfo.shareSourceName,
                                sharingInfo.mediaType == MediaType.VIDEO,
                                imageCount = sharingInfo.resourceUris.size,
                                imageIndexes = sharingInfo.resourcesIndexes
                            )
                        )
                    )

                    is ShareTarget.CustomTarget -> handleCustomShareTargetForMedia(
                        activity,
                        sharingInfo,
                        shareTarget,
                        shareResultHandler,
                    )
                }
                appSharingAnalysis.sendAnalysisForShareMedia(
                    shareTarget,
                    sharingInfo.mediaType == MediaType.VIDEO,
                    sharingInfo.shareSourceName,
                    imageCount = sharingInfo.resourceUris.size,
                    imageIndexes = sharingInfo.resourcesIndexes
                )
            }
        }
    }

    fun handleCustomShareTargetForMedia(
        activity: Activity,
        sharingInfo: SharingInfo,
        shareTarget: ShareTarget.CustomTarget,
        shareResultHandler: ShareResultHandler,
    ) {
        if (sharingInfo.resourceUris.isEmpty()) {
            _shareErrorEvent.tryEmit(ShareErrorType.CANCEL)
            Timber.w("share resourceUris is empty, can't share")
            return
        }
        try {
            when (shareTarget.appId) {
                WE_CHAT_APP_ID -> {
                    activity.grantUriPermission(
                        WE_CHAT_APP_ID,
                        sharingInfo.resourceUris.first(),
                        Intent.FLAG_GRANT_READ_URI_PERMISSION
                    )
                    weChatAPI.setShareResultHandler(shareResultHandler)
                    weChatAPI.shareImage(
                        sharingInfo.resourceUris.first(),
                        shareTarget.targetId == TARGET_WECHAT_MOMENTS
                    )
                }

                WEI_BO_APP_ID -> {
                    if (sharingInfo.resourceUris.size > WEIBO_IMAGE_LIMIT_COUNT) {
                        _shareErrorEvent.tryEmit(ShareErrorType.WEIBO_OVER_IMAGE_COUNT)
                    } else {
                        weiboAPI.shareImage(
                            imageUris = sharingInfo.resourceUris,
                            hashTags = sharingInfo.hashTags,
                            activity = activity
                        )
                    }
                }

                DOU_YIN_APP_ID -> {
                    douYinAPI.setShareResultHandler(shareResultHandler)
                    douYinAPI.share(
                        mediaUris = sharingInfo.resourceUris,
                        activity = activity,
                        hashTags = sharingInfo.hashTags,
                        video = sharingInfo.mediaType == MediaType.VIDEO
                    )
                }

                XIAO_HONG_SHU_APP_ID -> {
                    xhsAPI.setShareResultHandler(shareResultHandler)
                    xhsAPI.share(
                        paths = sharingInfo.resourceUris,
                        context = activity,
                        hashTags = sharingInfo.hashTags,
                        video = sharingInfo.mediaType == MediaType.VIDEO
                    )
                }

                else -> {
                    Timber.w("Custom target not supported: $shareTarget")
                }
            }
        } catch (e: Throwable) {
            Timber.e(e, "Error during shareMediaToCustomTarget to ${shareTarget.appId}")
        }
    }

    fun saveMedia(contentResolver: ContentResolver, resourceUri: List<Uri>?, mediaType: MediaType) {
        launch {
            try {
                resourceUri?.forEach { imageUri ->
                    val displayName = UUID.randomUUID().toString().take(8)
                    MediaStoreUtils.saveMediaToMediaStore(
                        contentResolver,
                        if (mediaType == MediaType.VIDEO) displayName.plus(".mp4")
                        else displayName.plus(".jpg"),
                        mediaType
                    ) { fileDescriptor ->
                        FileOutputStream(fileDescriptor.fileDescriptor).use { outputStream ->
                            contentResolver.openInputStream(imageUri).use {
                                outputStream.write(it?.readBytes())
                            }
                        }
                    }
                }
            } catch (e: Exception) {
                Timber.w(e, "save image to media failed")
            }
        }
    }

    fun handleCustomShareTargetForLink(
        activity: Activity,
        sharingInfo: SharingInfo,
        shareTarget: ShareTarget.CustomTarget,
        shareResultHandler: ShareResultHandler,
    ) {
        val shareLinkInfo = sharingInfo.shareLinkInfo
        if (shareLinkInfo == null || shareLinkInfo.linkUrl.isEmpty()) {
            _shareErrorEvent.tryEmit(ShareErrorType.CANCEL)
            Timber.w("share linkUrl is empty, can't share")
            return
        }
        if (!hasThirdPlatform(shareTarget.appId, activity)) {
            _shareErrorEvent.tryEmit(ShareErrorType.NOT_INSTALL_PLATFORM)
            Timber.w("don't install third platform, can't share")
            return
        }
        try {
            when (shareTarget.appId) {
                WE_CHAT_APP_ID -> {
                    weChatAPI.setShareResultHandler(shareResultHandler)
                    weChatAPI.shareLink(
                        shareLinkInfo.title,
                        shareLinkInfo.description,
                        shareLinkInfo.linkUrl,
                        shareTarget.targetId == TARGET_WECHAT_MOMENTS,
                        shareLinkInfo.iconBitmap,
                    )
                }

                WEI_BO_APP_ID -> {
                    weiboAPI.shareLink(
                        shareLinkInfo.title,
                        shareLinkInfo.description,
                        shareLinkInfo.linkUrl,
                        activity,
                        shareLinkInfo.iconBitmap,
                        sharingInfo.hashTags,
                    )
                }

                else -> {
                    Timber.w("Custom target not supported share link: $shareTarget")
                }
            }
        } catch (e: Throwable) {
            Timber.e(e, "Error during shareImageToCustomTarget to ${shareTarget.appId}")
        }
    }

    fun removeShareCallback() {
        douYinAPI.setShareResultHandler(null)
        weChatAPI.setShareResultHandler(null)
        weiboAPI.setShareResultHandler(null)
        xhsAPI.setShareResultHandler(null)
    }

    companion object {
        const val WEIBO_IMAGE_LIMIT_COUNT = 9
    }
}
