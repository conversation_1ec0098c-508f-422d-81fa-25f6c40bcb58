package com.stt.android.sharing

import android.content.SharedPreferences
import com.stt.android.ShareTargetIdConstant.DOU_YIN_APP_ID
import com.stt.android.ShareTargetIdConstant.TARGET_WECHAT_MOMENTS
import com.stt.android.ShareTargetIdConstant.WEI_BO_APP_ID
import com.stt.android.ShareTargetIdConstant.WE_CHAT_APP_ID
import com.stt.android.ShareTargetIdConstant.XIAO_HONG_SHU_APP_ID
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.AnalyticsEventProperty
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.di.SuuntoSharedPrefs
import com.stt.android.eventtracking.EventTracker
import com.stt.android.locationinfo.FetchLocationInfoUseCase
import com.stt.android.workouts.sharepreview.customshare.ShareTarget
import javax.inject.Inject

class AppSharingAnalysisImpl @Inject constructor(
    fetchLocationInfoUseCase: FetchLocationInfoUseCase,
    @SuuntoSharedPrefs
    private val sharedPreferences: SharedPreferences,
    private val tracker: EventTracker
) : AppSharingAnalysis(fetchLocationInfoUseCase, sharedPreferences) {
    private val analysisPublicProperties = hashMapOf<String, Any>()

    override fun sendAnalysisForShareMedia(
        shareTarget: ShareTarget,
        shareVideo: Boolean,
        shareSourceName: String,
        imageCount: Int,
        imageIndexes: List<Int>
    ) {
        tracker.trackEvent(AnalyticsEvent.H5SHARE, hashMapOf<String, Any>().apply {
            putAll(analysisPublicProperties)
            put(
                AnalyticsEventProperty.H5LINK_NAME,
                shareSourceName
            )
            put(
                AnalyticsEventProperty.SHARE_TYPE,
                if (shareVideo)
                    AnalyticsPropertyValue.ShareType.VIDEO
                else
                    AnalyticsPropertyValue.ShareType.IMAGE
            )
            if (!shareVideo) {
                put(AnalyticsEventProperty.NUMBER_OF_IMAGES, imageCount)
                put(AnalyticsEventProperty.TYPES_OF_IMAGES, imageIndexes)
            }
            put(
                AnalyticsEventProperty.SHARE_TARGET, when (shareTarget) {
                    is ShareTarget.CustomTarget -> getShareTargetName(shareTarget)
                    is ShareTarget.SaveToMedia -> AnalyticsPropertyValue.ShareTargetName.SAVE
                    else -> ""
                }
            )
        })
    }

    override fun senAnalysisForShareLink(
        shareTarget: ShareTarget,
        shareSourceName: String
    ) {
        tracker.trackEvent(AnalyticsEvent.H5SHARE, hashMapOf<String, Any>().apply {
            putAll(analysisPublicProperties)
            put(
                AnalyticsEventProperty.H5LINK_NAME,
                shareSourceName
            )
            put(
                AnalyticsEventProperty.SHARE_TYPE,
                AnalyticsPropertyValue.ShareType.LINK
            )
            put(
                AnalyticsEventProperty.SHARE_TARGET, when (shareTarget) {
                    is ShareTarget.CustomTarget -> getShareTargetName(shareTarget)
                    else -> "unknown"
                }
            )
        })
    }

    override fun geSharingMediaAnalysis(
        shareSourceName: String,
        shareVideo: Boolean,
        imageCount: Int,
        imageIndexes: List<Int>
    ): Map<String, Any> {
        return hashMapOf<String, Any>().apply {
            putAll(analysisPublicProperties)
            put(
                AnalyticsEventProperty.H5LINK_NAME,
                shareSourceName
            )
            put(
                AnalyticsEventProperty.SHARE_TYPE,
                if (shareVideo)
                    AnalyticsPropertyValue.ShareType.VIDEO
                else
                    AnalyticsPropertyValue.ShareType.IMAGE
            )
            if (!shareVideo) {
                put(AnalyticsEventProperty.NUMBER_OF_IMAGES, imageCount)
                put(AnalyticsEventProperty.TYPES_OF_IMAGES, imageIndexes)
            }
        }
    }

    override fun geSharingLinkAnalysis(
        shareSourceName: String
    ): Map<String, Any> {
        return hashMapOf<String, Any>().apply {
            putAll(analysisPublicProperties)
            put(
                AnalyticsEventProperty.H5LINK_NAME,
                shareSourceName
            )
            put(
                AnalyticsEventProperty.SHARE_TYPE,
                AnalyticsPropertyValue.ShareType.LINK
            )

        }
    }

    private fun getShareTargetName(shareTarget: ShareTarget.CustomTarget): String {
        return when (shareTarget.appId) {
            WE_CHAT_APP_ID -> if (shareTarget.targetId == TARGET_WECHAT_MOMENTS) AnalyticsPropertyValue.ShareTargetName.MOMENT else AnalyticsPropertyValue.ShareTargetName.WECHAT
            WEI_BO_APP_ID -> AnalyticsPropertyValue.ShareTargetName.WEIBO
            DOU_YIN_APP_ID -> AnalyticsPropertyValue.ShareTargetName.DOUYIN
            XIAO_HONG_SHU_APP_ID -> AnalyticsPropertyValue.ShareTargetName.REDBOOK
            else -> ""
        }
    }
}
