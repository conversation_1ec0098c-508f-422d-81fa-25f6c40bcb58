package com.stt.android.ui.fragments.login.terms

import android.annotation.SuppressLint
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import com.stt.android.R
import dagger.hilt.android.AndroidEntryPoint

@Suppress("DEPRECATION")
@Deprecated("Move to session module and refactor")
@AndroidEntryPoint
class TermsActivity : AppCompatActivity(), LegacyBaseTermsFragment.Listener {

    override fun onProceed() {
        finish()
    }

    override fun onErrorDialogDismissed() {
        // Not used here
    }

    @SuppressLint("MissingSuperCall")
    @Deprecated("Deprecated in Java")
    override fun onBackPressed() {
        finishAffinity()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_terms)
        openTermsFragment()
    }

    private fun openTermsFragment() {
        val termsUpdatedFragment = TermsUpdatedFragment.newInstance()
        supportFragmentManager.beginTransaction()
            .replace(R.id.fragmentContainer, termsUpdatedFragment, TermsUpdatedFragment.FRAGMENT_TAG)
            .addToBackStack(null)
            .commit()
    }
}
