package com.stt.android.annualreport

import android.Manifest
import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.Rect
import android.os.Build
import android.os.Bundle
import android.view.View
import android.view.WindowManager
import android.webkit.ConsoleMessage
import android.webkit.JavascriptInterface
import android.webkit.WebChromeClient
import android.webkit.WebResourceError
import android.webkit.WebResourceRequest
import android.webkit.WebSettings
import android.webkit.WebView
import android.webkit.WebViewClient
import androidx.activity.addCallback
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.core.content.FileProvider
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.WindowInsetsControllerCompat
import androidx.core.view.isVisible
import androidx.lifecycle.lifecycleScope
import com.google.android.material.snackbar.Snackbar
import com.stt.android.R
import com.stt.android.analytics.MessageSource
import com.stt.android.databinding.ActivityAnnualReportBinding
import com.stt.android.utils.CustomTabsUtils
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import timber.log.Timber
import java.io.File

@AndroidEntryPoint
class AnnualReportActivity : AppCompatActivity() {
    private var previewAnnualReport = false
    private val viewModel: AnnualReportViewModel by viewModels()
    private lateinit var binding: ActivityAnnualReportBinding
    private var showRecordVideoInterruptMessage = false
    private var recordVideoStopped = false
    private var shareVideo = false
    private var imageDataJson = ""
    private val storagePermissions = listOf(
        Manifest.permission.READ_EXTERNAL_STORAGE,
        Manifest.permission.WRITE_EXTERNAL_STORAGE
    ).toTypedArray()

    private val previewAnnualReportLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) {
        if (it.resultCode == PREVIEW_ANNUAL_REPORT_RESULT_CODE) {
            showRecordVideoInterruptMessage = true
        }
    }

    private val requestStoragePermissionLauncher =
        registerForActivityResult(
            ActivityResultContracts.RequestMultiplePermissions()
        ) { permissionsResult: Map<String, Boolean> ->
            // xiaomi 8, only can apply read permission, write permission always is false
            if (permissionsResult.values.any { it }) {
                if (shareVideo) viewModel.previewAnnualReport() else shareImage()
            } else {
                Snackbar.make(
                    binding.root,
                    getString(if (shareVideo) R.string.storage_permission_rationale_video else R.string.storage_permission_rationale_share),
                    Snackbar.LENGTH_SHORT
                ).show()
            }
        }

    private fun applyStoragePermissions() {
        when {
            ContextCompat.checkSelfPermission(
                this,
                Manifest.permission.WRITE_EXTERNAL_STORAGE
            ) == PackageManager.PERMISSION_GRANTED -> {
                if (shareVideo) viewModel.previewAnnualReport() else shareImage()
            }

            ActivityCompat.shouldShowRequestPermissionRationale(
                this, Manifest.permission.READ_EXTERNAL_STORAGE
            ) -> {
                Snackbar.make(
                    binding.root,
                    getString(if (shareVideo) R.string.storage_permission_rationale_video else R.string.storage_permission_rationale_share),
                    Snackbar.LENGTH_SHORT
                ).setAction(R.string.ok) {
                    requestStoragePermissionLauncher.launch(
                        storagePermissions
                    )
                }.show()
            }

            else -> {
                requestStoragePermissionLauncher.launch(
                    storagePermissions
                )
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityAnnualReportBinding.inflate(layoutInflater)
        setContentView(binding.root)
        fullScreen()
        previewAnnualReport = intent.getBooleanExtra(PREVIEW_KEY, false)
        if (previewAnnualReport) {
            showRecordVideoInterruptMessage = false
            recordVideoStopped = false
        }
        initAnnualReportWebView(previewAnnualReport)
        initEvent()
        onBackPressedDispatcher.addCallback {
            if (!previewAnnualReport || recordVideoStopped) finish()
        }
        intent.getStringExtra(KEY_ANNUAL_REPORT_URL)?.apply {
            if (!previewAnnualReport)
                viewModel.initAnnualAnalysis(
                    targetUrl = this,
                    messageSource = MessageSource.entries.find {
                        it.name == intent.getStringExtra(
                            KEY_MESSAGE_SOURCE
                        )
                    } ?: MessageSource.INBOX
                )
            binding.annualReportWebView.loadUrl(this)
        } ?: run {
            Timber.w("annual report url is empty, can't open it")
            finish()
        }
    }

    private fun fullScreen() {
        val windowInsetsController =
            WindowCompat.getInsetsController(window, window.decorView)
        windowInsetsController.hide(WindowInsetsCompat.Type.systemBars())
        windowInsetsController.systemBarsBehavior =
            WindowInsetsControllerCompat.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE
        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
    }

    private fun initMp4Encoder() {
        val cacheFile = File(getExternalFilesDir(null), ANNUAL_REPORT_VIDEO_NAME)
        viewModel.initMp4Encoder(cacheFile.absolutePath, getWebViewRect())
    }

    private fun getWebViewRect(): Rect {
        val webViewRect = Rect()
        binding.annualReportWebView.getGlobalVisibleRect(webViewRect)
        return webViewRect
    }

    private fun initEvent() {
        lifecycleScope.launch {
            launch {
                viewModel.annualReportBaseData.collect {
                    if (it == null) {
                        Timber.w("Annual report get base data failed")
                    } else {
                        setBaseDataToH5(it)
                    }
                }
            }
            launch {
                viewModel.shareImagesEvent.collect {
                    val imageFileUris = it.map { file ->
                        FileProvider.getUriForFile(
                            this@AnnualReportActivity,
                            "${packageName}.FileProvider",
                            file
                        )
                    }
                    ShareAnnualReportActivity.startNewIntent(
                        this@AnnualReportActivity,
                        imageFileUris
                    )
                }
            }
            launch {
                viewModel.backEvent.collect {
                    if (it) {
                        onBackPressedDispatcher.onBackPressed()
                    }
                }
            }
            launch {
                viewModel.previewAnnualReportEvent.collect {
                    previewAnnualReportLauncher.launch(
                        intent.putExtra(PREVIEW_KEY, true)
                    )
                }
            }
            launch {
                viewModel.shareVideo.collect {
                    recordVideoStopped = true
                    viewModel.mergeAudioAndVideo(
                        File(getExternalFilesDir(null), ANNUAL_REPORT_VIDEO_NAME).absolutePath,
                        assets.openFd(ANNUAL_REPORT_AUDIO_FILE_NAME),
                        File(
                            getExternalFilesDir(null),
                            ANNUAL_REPORT_VIDEO_WITH_AUDIO_NAME
                        ).absolutePath
                    ) {
                        showShareVideoDialog()
                    }
                }
            }

            launch {
                viewModel.handleSharedImageLoading.collect {
                    binding.progressBar.isVisible = it
                }
            }
        }
    }

    private fun showShareVideoDialog() {
        val videoUri = FileProvider.getUriForFile(
            this,
            "${packageName}.FileProvider",
            File(getExternalFilesDir(null), ANNUAL_REPORT_VIDEO_WITH_AUDIO_NAME)
        )
        AnnualReportShareTargetsDialogFragment.newAnnualReportShareTargetsDialogFragment(
            listOf(videoUri),
            true
        ).show(supportFragmentManager, "shareAnnualReport")
    }

    @SuppressLint("SetJavaScriptEnabled", "ClickableViewAccessibility")
    private fun initAnnualReportWebView(preview: Boolean) {
        if (preview)
            binding.annualReportWebView.setOnTouchListener { _, _ -> return@setOnTouchListener true }
        binding.annualReportWebView.settings.apply {
            cacheMode = WebSettings.LOAD_NO_CACHE
            domStorageEnabled = true
            javaScriptEnabled = true
            setSupportZoom(false)
            displayZoomControls = false
        }
        binding.annualReportWebView.setDownloadListener { url, _, _, _, _ ->
            // if download url isn't UPGRADE_URL, also can jump to system browser, but this way will have a blank page in app
            CustomTabsUtils.launchCustomTab(this, url)
        }
        binding.annualReportWebView.webChromeClient = object : WebChromeClient() {
            override fun onProgressChanged(view: WebView?, newProgress: Int) {
                super.onProgressChanged(view, newProgress)
                if (isDestroyed) return
                if (newProgress == 100) {
                    binding.progressBar.visibility = View.GONE
                }
            }

            @SuppressLint("BinaryOperationInTimber")
            override fun onConsoleMessage(consoleMessage: ConsoleMessage?): Boolean {
                return super.onConsoleMessage(consoleMessage)
            }
        }
        binding.annualReportWebView.webViewClient = object : WebViewClient() {
            override fun shouldOverrideUrlLoading(
                view: WebView?,
                request: WebResourceRequest?
            ): Boolean {
                // avoid show a blank page, so jump to browser directly when click upgrade link
                request?.url?.toString()?.let { url->
                    if (url == UPGRADE_URL)
                        CustomTabsUtils.launchCustomTab(this@AnnualReportActivity, url)
                    return true
                }
                return super.shouldOverrideUrlLoading(view, request)
            }
            override fun onPageFinished(view: WebView?, url: String?) {
                super.onPageFinished(view, url)
                if (isDestroyed) return
                Timber.d("webView page finished")
            }

            override fun onReceivedError(
                view: WebView?,
                request: WebResourceRequest?,
                error: WebResourceError?
            ) {
                super.onReceivedError(view, request, error)
                if (isDestroyed) return
                binding.progressBar.visibility = View.GONE
                Timber.w("webView load error:${error?.description}")
            }
        }
    }

    private fun startRecordVideo() {
        initMp4Encoder()
        viewModel.startEncoder(window)
    }

    private fun stopRecordVideo() {
        viewModel.stopEncoder()
    }

    private fun setBaseDataToH5(baseData: String) {
        val method = "javascript:getAnnualReportUserInfo(\'$baseData\')"
        binding.annualReportWebView.evaluateJavascript(method) {
            // do nothing
        }
    }

    @JavascriptInterface
    fun js_annualReport(param: String) {
        Timber.d("image data: $param")
        if (param == BACK_METHOD_NAME) {
            viewModel.back()
        } else {
            viewModel.sendAnnualReportAnalysisForH5Click(H5ClickEventType.IMAGE)
            imageDataJson = param
            shareVideo = false
            // On API 29 and above we don't need any extra permissions for basic image sharing
            if (Build.VERSION.SDK_INT <= 29) {
                applyStoragePermissions()
            } else {
                shareImage()
            }
        }
    }

    private fun shareImage() {
        val cacheFileDir = getExternalFilesDir(null)
        if (cacheFileDir != null && imageDataJson.isNotEmpty()) {
            viewModel.shareAnnualReport(cacheFileDir, imageDataJson)
        } else {
            Timber.w("getExternalFilesDir failed or image data is empty")
        }
    }

    @JavascriptInterface
    fun js_annualReport() {
        // js get the sessionKey and other data
        viewModel.getAnnualReportBaseData(previewAnnualReport)
    }

    @JavascriptInterface
    fun js_shareVideo() {
        Timber.d("js call app: share video")
        shareVideo = true
        // On API 29 and above we don't need any extra permissions for basic image sharing
        if (Build.VERSION.SDK_INT <= 29) {
            applyStoragePermissions()
        } else {
            viewModel.previewAnnualReport()
        }
        viewModel.sendAnnualReportAnalysisForH5Click(H5ClickEventType.VIDEO)
    }

    @JavascriptInterface
    fun js_recordVideo(param: String) {
        Timber.d("js call app: record video $param")
        if (param == RECORD_VIDEO_START) {
            startRecordVideo()
        } else if (param == RECORD_VIDEO_STOP) {
            stopRecordVideo()
        }
    }

    @JavascriptInterface
    fun js_eventTrack(param: String) {
        Timber.w("js_eventTack:$param")
        if (param == EVENT_LOTTERY) {
            viewModel.sendAnnualReportAnalysisForH5Click(H5ClickEventType.LOTTERY)
        } else {
            // page number
            viewModel.sendAnnualReportAnalysisForH5Exposure(param)
        }
    }

    override fun onStop() {
        super.onStop()
        if (previewAnnualReport) {
            if (!recordVideoStopped) {
                setResult(PREVIEW_ANNUAL_REPORT_RESULT_CODE)
            }
            finish()
        }
        binding.annualReportWebView.removeJavascriptInterface(JAVASCRIPT_INTERFACE_NAME)
    }

    override fun onResume() {
        super.onResume()
        if (showRecordVideoInterruptMessage) {
            Snackbar.make(
                binding.root,
                getString(R.string.record_video_interrupt),
                Snackbar.LENGTH_LONG
            ).show()
            showRecordVideoInterruptMessage = false
        }
        binding.annualReportWebView.addJavascriptInterface(this, JAVASCRIPT_INTERFACE_NAME)
    }

    companion object {
        private const val BACK_METHOD_NAME = "goBackApp"
        private const val RECORD_VIDEO_START = "start"
        private const val RECORD_VIDEO_STOP = "stop"
        private const val PREVIEW_KEY = "preview"
        private const val ANNUAL_REPORT_VIDEO_NAME = "annual_report.mp4"
        private const val ANNUAL_REPORT_VIDEO_WITH_AUDIO_NAME = "annual_report_with_audio.mp4"
        private const val ANNUAL_REPORT_AUDIO_FILE_NAME = "annual_report.aac"
        private const val PREVIEW_ANNUAL_REPORT_RESULT_CODE = 9999
        private const val KEY_ANNUAL_REPORT_URL =
            "com.stt.android.annualreport.KEY_ANNUAL_REPORT_URL"
        private const val EVENT_LOTTERY = "Lottery"
        private const val KEY_MESSAGE_SOURCE = "com.stt.android.annualreport.KEY_MESSAGE_SOURCE"
        private const val JAVASCRIPT_INTERFACE_NAME = "js_android"
        private const val UPGRADE_URL = "https://download.suunto.cn/"

        fun startNewIntent(context: Context, url: String, source: String) {
            context.startActivity(Intent(context, AnnualReportActivity::class.java).apply {
                putExtra(KEY_ANNUAL_REPORT_URL, url)
                putExtra(KEY_MESSAGE_SOURCE, source)
            })
        }
    }
}
