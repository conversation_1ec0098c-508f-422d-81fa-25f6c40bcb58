package com.stt.android.annualreport

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import com.stt.android.R
import com.stt.android.databinding.ActivityShareAnnualReportBinding
import com.stt.android.ui.utils.ThrottlingOnClickListener
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber
import javax.inject.Inject

@AndroidEntryPoint
class ShareAnnualReportActivity : AppCompatActivity() {
    @Inject
    lateinit var shareAnnualReportController: ShareAnnualReportController
    private lateinit var binding: ActivityShareAnnualReportBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityShareAnnualReportBinding.inflate(layoutInflater)
        setContentView(binding.root)
        initToolbar()
        initRv()
        binding.shareImageBtn.setOnClickListener(ThrottlingOnClickListener {
            // share
            val shareImageUris = getShareImageUris()
            if (shareImageUris.isNullOrEmpty()) {
                Timber.w("didn't select share image")
                return@ThrottlingOnClickListener
            }
            AnnualReportShareTargetsDialogFragment.newAnnualReportShareTargetsDialogFragment(
                resourceUris = shareImageUris,
                imageIndexes = getShareImageIndexes()
            )
                .show(supportFragmentManager, "AnnualReportShareTargetsDialogFragment")
        })
        selectFirstItem()
    }

    private fun initRv() {
        binding.shareImageRv.adapter = shareAnnualReportController.adapter
        val imageUris = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            intent.getParcelableArrayListExtra(URIS_KEY, Uri::class.java)
        } else {
            intent.getParcelableArrayListExtra(URIS_KEY)
        }
        imageUris?.let {
            val data = arrayListOf<ShareImageData>()
            it.forEachIndexed { index, uri ->
                data.add(ShareImageData(uri, false, ::handleRadioButtonClick))
            }
            shareAnnualReportController.setData(data)
        }
    }

    private fun selectFirstItem() {
        binding.root.postDelayed({
            val firstItem = shareAnnualReportController.currentData?.first()
            firstItem?.let {
                handleRadioButtonClick(it.imageUri)
            }
        }, 500)
    }

    private fun getShareImageUris(): List<Uri>? {
        return shareAnnualReportController.currentData?.filter {
            it.checked
        }?.map { it.imageUri }
    }

    private fun getShareImageIndexes(): List<Int> {
        val imageIndexes = arrayListOf<Int>()
        shareAnnualReportController.currentData?.forEachIndexed { index, shareImageData ->
            if (shareImageData.checked)
                imageIndexes.add(index + 1)
        }
        return imageIndexes
    }

    private fun handleRadioButtonClick(imageUri: Uri) {
        val updateData = shareAnnualReportController.currentData?.map {
            if (it.imageUri == imageUri) {
                it.copy(checked = !it.checked)
            } else {
                it
            }
        }
        shareAnnualReportController.setData(updateData)
        setShareButtonEnableState()
    }

    private fun setShareButtonEnableState() {
        val enabled = shareAnnualReportController.currentData?.any {
            it.checked
        }
        binding.shareImageBtn.isEnabled = enabled ?: false
    }

    private fun initToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.apply { title = getString(R.string.share) }
        binding.toolbar.setNavigationOnClickListener {
            onBackPressed()
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        val fragments = supportFragmentManager.fragments
        if (fragments.isNotEmpty()) {
            for (item in fragments) {
                item.onActivityResult(requestCode, resultCode, data)
            }
        }
    }

    companion object {
        private const val URIS_KEY = "uri_key"
        fun startNewIntent(context: Context, shareUris: List<Uri>) {
            val intent = Intent(context, ShareAnnualReportActivity::class.java)
            intent.putParcelableArrayListExtra(URIS_KEY, ArrayList(shareUris))
            context.startActivity(intent)
        }
    }
}
