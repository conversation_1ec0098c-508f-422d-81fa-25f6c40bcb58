package com.stt.android.annualreport

import android.app.Activity
import android.content.ContentResolver
import android.content.Intent
import android.net.Uri
import com.stt.android.R
import com.stt.android.ShareTargetIdConstant.DOU_YIN_APP_ID
import com.stt.android.ShareTargetIdConstant.TARGET_WECHAT
import com.stt.android.ShareTargetIdConstant.TARGET_WECHAT_MOMENTS
import com.stt.android.ShareTargetIdConstant.WEI_BO_APP_ID
import com.stt.android.ShareTargetIdConstant.WE_CHAT_APP_ID
import com.stt.android.ShareTargetIdConstant.XIAO_HONG_SHU_APP_ID
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.common.viewstate.LoadingStateViewModel
import com.stt.android.domain.di.IoThread
import com.stt.android.domain.di.MainThread
import com.stt.android.multimedia.MediaStoreUtils
import com.stt.android.multimedia.MediaType
import com.stt.android.sharingplatform.DouYinAPI
import com.stt.android.sharingplatform.ShareResultHandler
import com.stt.android.sharingplatform.WeChatAPI
import com.stt.android.sharingplatform.WeiboAPI
import com.stt.android.sharingplatform.XhsAPI
import com.stt.android.workouts.sharepreview.customshare.ShareTarget
import com.stt.android.workouts.sharepreview.customshare.TargetEpoxyContainer
import com.stt.android.workouts.sharepreview.customshare.WorkoutShareLinkTargets
import dagger.hilt.android.lifecycle.HiltViewModel
import io.reactivex.Scheduler
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.launch
import timber.log.Timber
import java.io.FileOutputStream
import java.util.UUID
import javax.inject.Inject

@HiltViewModel
class AnnualReportShareTargetsViewModel @Inject constructor(
    private val weChatAPI: WeChatAPI,
    private val weiboAPI: WeiboAPI,
    private val douYinAPI: DouYinAPI,
    private val xhsAPI: XhsAPI,
    private val annualReportAnalysis: AnnualReportAnalysis,
    @IoThread ioThread: Scheduler,
    @MainThread mainThread: Scheduler,
    coroutinesDispatchers: CoroutinesDispatchers
) : LoadingStateViewModel<WorkoutShareLinkTargets>(ioThread, mainThread, coroutinesDispatchers) {

    private val _shareTargetEvent = MutableSharedFlow<ShareTarget>()
    val shareTargetEvent = _shareTargetEvent.asSharedFlow()

    private val _shareErrorEvent = MutableSharedFlow<ShareErrorType>()
    val shareErrorEvent = _shareErrorEvent.asSharedFlow()

    override fun retryLoading() {
        // do nothing
    }

    fun setShareResultForWeibo(intent: Intent, shareResultHandler: ShareResultHandler) {
        weiboAPI.onShareResult(intent, shareResultHandler)
    }

    fun hasThirdPlatform(appId: String, activity: Activity): Boolean {
        return when (appId) {
            WE_CHAT_APP_ID -> weChatAPI.appInstalled()
            WEI_BO_APP_ID -> weiboAPI.appInstalled()
            DOU_YIN_APP_ID -> douYinAPI.supportedSharing(activity)
            XIAO_HONG_SHU_APP_ID -> xhsAPI.supportedShareNote(activity)
            else -> false
        }
    }

    /**
     * wechat/moments/douyin only support single image
     */
    fun loadTargets(singleImage: Boolean) {
        val platformTargets = mutableListOf<TargetEpoxyContainer>()
        platformTargets.apply {
            platformTargets.add(
                TargetEpoxyContainer(
                    UUID.randomUUID().toString(),
                    ShareTarget.SaveToMedia,
                    ::onTargetSelected
                )
            )
            if (singleImage) {
                add(
                    TargetEpoxyContainer(
                        UUID.randomUUID().toString(), ShareTarget.CustomTarget(
                            WE_CHAT_APP_ID,
                            TARGET_WECHAT,
                            R.drawable.share_wechat,
                            R.string.we_chat
                        ), ::onTargetSelected
                    )
                )
                add(
                    TargetEpoxyContainer(
                        UUID.randomUUID().toString(), ShareTarget.CustomTarget(
                            WE_CHAT_APP_ID,
                            TARGET_WECHAT_MOMENTS,
                            R.drawable.share_wechat_moments,
                            R.string.we_chat_moments
                        ), ::onTargetSelected
                    )
                )
            }
            add(
                TargetEpoxyContainer(
                    UUID.randomUUID().toString(), ShareTarget.CustomTarget(
                        XIAO_HONG_SHU_APP_ID,
                        "",
                        R.drawable.icon_xhs,
                        R.string.xiao_hong_shu_title
                    ), ::onTargetSelected
                )
            )
            add(
                TargetEpoxyContainer(
                    UUID.randomUUID().toString(), ShareTarget.CustomTarget(
                        DOU_YIN_APP_ID,
                        "",
                        R.drawable.icon_douyin,
                        R.string.douyin_title
                    ), ::onTargetSelected
                )
            )
            add(
                TargetEpoxyContainer(
                    UUID.randomUUID().toString(), ShareTarget.CustomTarget(
                        WEI_BO_APP_ID,
                        "",
                        R.drawable.icon_weibo,
                        R.string.weibo
                    ), ::onTargetSelected
                )
            )

            add(
                TargetEpoxyContainer(
                    UUID.randomUUID().toString(),
                    ShareTarget.DelegateToOS,
                    ::onTargetSelected
                )
            )
        }
        notifyDataLoaded(WorkoutShareLinkTargets(platformTargets))
    }

    private fun onTargetSelected(target: ShareTarget) {
        launch {
            _shareTargetEvent.emit(target)
        }
    }

    fun handleCustomShareTarget(
        shareTarget: ShareTarget.CustomTarget,
        resourceUri: List<Uri>,
        activity: Activity,
        shareResultHandler: ShareResultHandler,
        hashTags: List<String> = emptyList(),
        video: Boolean = false
    ) {
        try {
            when (shareTarget.appId) {
                WE_CHAT_APP_ID -> {
                    activity.grantUriPermission(
                        WE_CHAT_APP_ID,
                        resourceUri.first(),
                        Intent.FLAG_GRANT_READ_URI_PERMISSION
                    )
                    weChatAPI.setShareResultHandler(shareResultHandler)
                    weChatAPI.shareImage(
                        resourceUri.first(),
                        shareTarget.targetId == TARGET_WECHAT_MOMENTS
                    )
                }

                WEI_BO_APP_ID -> {
                    if (resourceUri.size > WEIBO_IMAGE_LIMIT_COUNT) {
                        launch {
                            _shareErrorEvent.emit(ShareErrorType.WEIBO_OVER_IMAGE_COUNT)
                        }
                    } else {
                        weiboAPI.shareImage(
                            imageUris = resourceUri,
                            hashTags = hashTags,
                            activity = activity
                        )
                    }
                }

                DOU_YIN_APP_ID -> {
                    douYinAPI.setShareResultHandler(shareResultHandler)
                    douYinAPI.share(
                        mediaUris = resourceUri,
                        activity = activity,
                        hashTags = hashTags,
                        video = video
                    )
                }

                XIAO_HONG_SHU_APP_ID -> {
                    xhsAPI.setShareResultHandler(shareResultHandler)
                    xhsAPI.share(
                        paths = resourceUri,
                        context = activity,
                        hashTags = hashTags,
                        video = video
                    )
                }

                else -> {
                    Timber.w("Custom target not supported: $shareTarget")
                }
            }
        } catch (e: Throwable) {
            Timber.e(e, "Error during shareImageToCustomTarget to ${shareTarget.appId}")
        }
    }

    fun saveMedia(contentResolver: ContentResolver, resourceUri: List<Uri>?, mediaType: MediaType) {
        launch {
            try {
                resourceUri?.forEach { imageUri ->
                    val displayName = UUID.randomUUID().toString().take(8)
                    MediaStoreUtils.saveMediaToMediaStore(
                        contentResolver,
                        if (mediaType == MediaType.VIDEO) displayName.plus(".mp4")
                        else displayName.plus(".jpg"),
                        mediaType
                    ) { fileDescriptor ->
                        FileOutputStream(fileDescriptor.fileDescriptor).use { outputStream ->
                            contentResolver.openInputStream(imageUri).use {
                                outputStream.write(it?.readBytes())
                            }
                        }
                    }
                }
            } catch (e: Exception) {
                Timber.w(e, "save image to media failed")
            }
        }
    }

    fun shareToOS(
        contentResolver: ContentResolver,
        resourceUris: List<Uri>,
        mediaType: MediaType,
        startToOSShare: (intent: Intent) -> Unit
    ) {
        // When using system sharing, need to save it to MediaStore
        val mediaImageUris = arrayListOf<Uri>()
        resourceUris.forEach {
            val displayName = UUID.randomUUID().toString().take(8)
            mediaImageUris.add(
                MediaStoreUtils.saveMediaToMediaStore(
                    contentResolver,
                    if (mediaType == MediaType.VIDEO) displayName.plus(".mp4")
                    else displayName.plus(".jpg"),
                    mediaType
                ) { fileDescriptor ->
                    FileOutputStream(fileDescriptor.fileDescriptor).use { outputStream ->
                        contentResolver.openInputStream(it).use {
                            outputStream.write(it?.readBytes())
                        }
                    }
                })
        }
        val shareIntent = Intent()
        shareIntent.action = Intent.ACTION_SEND_MULTIPLE
        shareIntent.type = if (mediaType == MediaType.IMAGE) "image/jpg" else "video/mp4"
        shareIntent.putParcelableArrayListExtra(Intent.EXTRA_STREAM, mediaImageUris)
        startToOSShare.invoke(shareIntent)
    }

    fun sendAnnualReportAnalysis(
        shareTarget: ShareTarget,
        shareVideo: Boolean,
        imageCount: Int = 0,
        imageIndexes: List<Int> = emptyList()
    ) {
        annualReportAnalysis.sendAnnualReportAnalysisForSharing(
            shareTarget,
            shareVideo,
            imageCount,
            imageIndexes
        )
    }

    fun getAnnualReportSharingAnalysis(
        shareVideo: Boolean,
        imageCount: Int = 0,
        imageIndexes: List<Int>
    ): Map<String, Any> =
        annualReportAnalysis.getAnnualReportSharingAnalysis(shareVideo, imageCount, imageIndexes)

    fun removeShareCallback() {
        douYinAPI.setShareResultHandler(null)
        weChatAPI.setShareResultHandler(null)
        weiboAPI.setShareResultHandler(null)
        xhsAPI.setShareResultHandler(null)
    }

    companion object {
        const val WEIBO_IMAGE_LIMIT_COUNT = 9
    }
}
