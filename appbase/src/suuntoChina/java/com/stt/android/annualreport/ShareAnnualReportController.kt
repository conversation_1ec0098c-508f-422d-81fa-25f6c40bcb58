package com.stt.android.annualreport

import android.net.Uri
import android.widget.ImageView
import androidx.databinding.BindingAdapter
import com.airbnb.epoxy.TypedEpoxyController
import com.stt.android.shareAnnualReportItem
import javax.inject.Inject

data class ShareImageData(val imageUri: Uri, val checked: Boolean = false, val handleRadioButtonCheckState: handleRadioButtonCheckState)
typealias handleRadioButtonCheckState = (id: Uri) -> Unit

@BindingAdapter("imageUri")
fun loadImageByUri(imageView: ImageView, uri: Uri) {
    imageView.setImageURI(uri)
}
class ShareAnnualReportController @Inject constructor() :
    TypedEpoxyController<List<ShareImageData>>() {
    override fun buildModels(data: List<ShareImageData>?) {
        data?.let {
            it.forEach {
                shareAnnualReportItem {
                    id(it.imageUri.toString())
                    data(it)
                }
            }

        }
    }
}
