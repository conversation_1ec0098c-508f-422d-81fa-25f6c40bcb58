package com.stt.android.annualreport

import android.content.SharedPreferences
import com.stt.android.ShareTargetIdConstant.DOU_YIN_APP_ID
import com.stt.android.ShareTargetIdConstant.TARGET_WECHAT_MOMENTS
import com.stt.android.ShareTargetIdConstant.WEI_BO_APP_ID
import com.stt.android.ShareTargetIdConstant.WE_CHAT_APP_ID
import com.stt.android.ShareTargetIdConstant.XIAO_HONG_SHU_APP_ID
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.AnalyticsEventProperty
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.analytics.MessageSource
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.di.SuuntoSharedPrefs
import com.stt.android.eventtracking.EventTracker
import com.stt.android.locationinfo.FetchLocationInfoUseCase
import com.stt.android.utils.STTConstants
import com.stt.android.workouts.sharepreview.customshare.ShareTarget
import com.suunto.connectivity.suuntoconnectivity.device.AnalyticsDevicePropertyHelper
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class AnnualReportAnalysis @Inject constructor(
    private val fetchLocationInfoUseCase: FetchLocationInfoUseCase,
    @SuuntoSharedPrefs
    private val sharedPreferences: SharedPreferences,
    private val tracker: EventTracker
) {
    private val analysisPublicProperties = hashMapOf<String, Any>()

    suspend fun init(targetUrl: String, source: MessageSource) {
        analysisPublicProperties[AnalyticsEventProperty.MESSAGE_SOURCE] =
            getMessageSourceValue(source)
        analysisPublicProperties[AnalyticsEventProperty.H5LINK_NAME] =
            AnalyticsPropertyValue.H5LinkName.ANNUAL_REPORT_2024
        analysisPublicProperties[AnalyticsEventProperty.ANNUAL_REPORT_TARGET_URL] = targetUrl

        val watchVariantName: String = sharedPreferences.getString(
            STTConstants.SuuntoPreferences.KEY_SUUNTO_PAIRED_WATCH_MODEL,
            ""
        ).orEmpty()
        analysisPublicProperties[AnalyticsEventProperty.SUUNTO_WATCH_MODEL] =
            if (watchVariantName.isNotEmpty())
                AnalyticsDevicePropertyHelper.getWatchModelNameForVariantName(watchVariantName)
            else ""
        analysisPublicProperties[AnalyticsEventProperty.CITY] = runSuspendCatching {
            fetchLocationInfoUseCase.fetchLocationInfo()?.city
        }.onFailure {
            Timber.w(it, "get city failed")
        }.getOrNull().orEmpty()
    }

    fun sendAnnualReportAnalysisForSharing(
        shareTarget: ShareTarget,
        shareVideo: Boolean,
        imageCount: Int = 0,
        imageIndexes: List<Int>
    ) {
        tracker.trackEvent(AnalyticsEvent.H5SHARE, hashMapOf<String, Any>().apply {
            putAll(analysisPublicProperties)
            put(
                AnalyticsEventProperty.SHARE_TYPE,
                if (shareVideo)
                    AnalyticsPropertyValue.ShareType.VIDEO
                else
                    AnalyticsPropertyValue.ShareType.IMAGE
            )
            if (!shareVideo) {
                put(AnalyticsEventProperty.NUMBER_OF_IMAGES, imageCount)
                put(AnalyticsEventProperty.TYPES_OF_IMAGES, imageIndexes)
            }
            put(
                AnalyticsEventProperty.SHARE_TARGET, when (shareTarget) {
                    is ShareTarget.CustomTarget -> getShareTargetName(shareTarget)
                    is ShareTarget.SaveToMedia -> AnalyticsPropertyValue.ShareTargetName.SAVE
                    else -> ""
                }
            )
        })
    }

    fun getAnnualReportSharingAnalysis(
        shareVideo: Boolean,
        imageCount: Int = 0,
        imageIndexes: List<Int>
    ): Map<String, Any> {
        return hashMapOf<String, Any>().apply {
            putAll(analysisPublicProperties)
            put(
                AnalyticsEventProperty.SHARE_TYPE,
                if (shareVideo)
                    AnalyticsPropertyValue.ShareType.VIDEO
                else
                    AnalyticsPropertyValue.ShareType.IMAGE
            )
            if (!shareVideo) {
                put(AnalyticsEventProperty.NUMBER_OF_IMAGES, imageCount)
                put(AnalyticsEventProperty.TYPES_OF_IMAGES, imageIndexes)
            }
        }
    }

    fun sendAnnualReportAnalysisForH5Click(
        eventType: H5ClickEventType,
    ) {
        tracker.trackEvent(AnalyticsEvent.H5CLICK, hashMapOf<String, Any>().apply {
            putAll(analysisPublicProperties)
            put(
                AnalyticsEventProperty.H5_PAGE_NAME, when (eventType) {
                    H5ClickEventType.IMAGE, H5ClickEventType.VIDEO -> 13
                    H5ClickEventType.LOTTERY -> 14
                }
            )
            put(
                AnalyticsEventProperty.H5_CLICK_BUTTON_NAME, when (eventType) {
                    H5ClickEventType.IMAGE -> AnalyticsPropertyValue.H5ClickButtonName.IMAGE
                    H5ClickEventType.VIDEO -> AnalyticsPropertyValue.H5ClickButtonName.VIDEO
                    H5ClickEventType.LOTTERY -> AnalyticsPropertyValue.H5ClickButtonName.LOTTERY
                }
            )
        })
    }

    fun sendAnnualReportAnalysisForH5Exposure(
        pageNumber: String,
    ) {
        tracker.trackEvent(AnalyticsEvent.H5EXPOSURE, hashMapOf<String, Any>().apply {
            putAll(analysisPublicProperties)
            put(AnalyticsEventProperty.H5_PAGE_NAME, pageNumber)
        })
    }

    private fun getShareTargetName(shareTarget: ShareTarget.CustomTarget): String {
        return when (shareTarget.appId) {
            WE_CHAT_APP_ID -> if (shareTarget.targetId == TARGET_WECHAT_MOMENTS) AnalyticsPropertyValue.ShareTargetName.MOMENT else AnalyticsPropertyValue.ShareTargetName.WECHAT
            WEI_BO_APP_ID -> AnalyticsPropertyValue.ShareTargetName.WEIBO
            DOU_YIN_APP_ID -> AnalyticsPropertyValue.ShareTargetName.DOUYIN
            XIAO_HONG_SHU_APP_ID -> AnalyticsPropertyValue.ShareTargetName.REDBOOK
            else -> ""
        }
    }

    private fun getMessageSourceValue(messageSource: MessageSource): String {
        return when (messageSource) {
            MessageSource.EMAIL -> AnalyticsPropertyValue.MessageSource.EMAIL
            MessageSource.INBOX -> AnalyticsPropertyValue.MessageSource.INBOX
            MessageSource.PUSH -> AnalyticsPropertyValue.MessageSource.PUSH
            MessageSource.POPUP -> AnalyticsPropertyValue.MessageSource.POPUP
            MessageSource.INLINE -> AnalyticsPropertyValue.MessageSource.INLINE
            MessageSource.BANNER -> AnalyticsPropertyValue.MessageSource.BANNER
        }
    }
}

enum class H5ClickEventType {
    IMAGE, VIDEO, LOTTERY
}
