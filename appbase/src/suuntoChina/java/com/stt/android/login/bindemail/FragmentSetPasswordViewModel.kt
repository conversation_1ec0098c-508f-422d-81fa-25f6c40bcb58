package com.stt.android.login.bindemail

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.viewModelScope
import com.stt.android.base.BaseHandlerViewModel
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.CurrentUserController
import com.stt.android.domain.session.DomainUserSession
import com.stt.android.domain.session.EmailVerificationState
import com.stt.android.domain.user.UserSettingsDataSource
import com.stt.android.exceptions.remote.STTError
import com.stt.android.newemail.NewEmailRepository
import com.stt.android.newemail.UserInfoFromChina
import com.stt.android.resetpassword.SetPasswordError
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class FragmentSetPasswordViewModel @Inject constructor(
    private val newEmailRepository: NewEmailRepository,
    private val dispatchers: CoroutinesDispatchers,
    private val currentUserController: CurrentUserController,
    private val userSettingsDataSource: UserSettingsDataSource
) : BaseHandlerViewModel() {

    var uiState by mutableStateOf(SetPasswordUIState())

    fun inputNewPassword(newPassword: String) {
        uiState = uiState.copy(newPassword = newPassword, setPasswordError = null)
    }

    fun inputConfirmPassword(confirmPassword: String) {
        uiState = uiState.copy(confirmPassword = confirmPassword, setPasswordError = null)
    }

    fun setPassword(email: String, newPassword: String, confirmPassword: String, token: String) {
        checkNewPassword(newPassword, confirmPassword) {
            loadingStart()
            viewModelScope.launch(
                dispatchers.io + getChangePasswordFailedHandler {
                    uiState = uiState.copy(setPasswordError = SetPasswordError.InvalidPassword)
                }
            ) {
                val userInfoFromChina = newEmailRepository.bindEmail(email, newPassword, token)
                userInfoFromChina?.let {
                    val session =
                        DomainUserSession(
                            it.sessionkey,
                            it.watchUserKey,
                            it.facebookConnected ?: false,
                            EmailVerificationState.UNKNOWN,
                        )
                    val oldUser = currentUserController.currentUser.copy(session = session)
                    currentUserController.store(oldUser)
                    val settings = userSettingsDataSource.getUserSettings().copy(email = it.email)
                    userSettingsDataSource.saveUserSettings(settings)
                }
                uiState = uiState.copy(setPasswordResult = userInfoFromChina)
                loadingEnd()
            }
        }
    }

    fun resetPassword(newPassword: String, confirmPassword: String, token: String, phoneNumber: String) {
        checkNewPassword(newPassword, confirmPassword) {
            loadingStart()
            viewModelScope.launch(
                dispatchers.io + getChangePasswordFailedHandler {
                    uiState = uiState.copy(setPasswordError = SetPasswordError.InvalidPassword)
                }
            ) {
                val result = newEmailRepository.changePassword(token, phoneNumber, newPassword)
                if (!result) {
                    throw STTError.ModifyPasswordFailed()
                } else {
                    uiState = uiState.copy(resetPasswordResult = result)
                    loadingEnd()
                }
            }
        }
    }

    private fun checkNewPassword(newPassword: String, confirmPassword: String, checkSuccessCallback: () -> Unit) {
        when (newPassword) {
            confirmPassword -> {
                checkSuccessCallback.invoke()
            }
            else -> {
                uiState = uiState.copy(setPasswordError = SetPasswordError.TwoPasswordIsInconsistent)
            }
        }
    }
}

data class SetPasswordUIState(
    val newPassword: String = "",
    val confirmPassword: String = "",
    val setPasswordError: SetPasswordError? = null,
    val setPasswordResult: UserInfoFromChina? = null,
    val resetPasswordResult: Boolean? = null,
)
