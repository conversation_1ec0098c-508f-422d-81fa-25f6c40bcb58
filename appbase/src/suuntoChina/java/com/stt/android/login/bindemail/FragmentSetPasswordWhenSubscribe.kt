package com.stt.android.login.bindemail

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Scaffold
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.core.app.NavUtils
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.navArgs
import com.stt.android.R
import com.stt.android.compose.base.BaseFragment
import com.stt.android.compose.layout.ContentCenteringColumn
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.bodyXLargeBold
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.widgets.LoadingContent
import com.stt.android.compose.widgets.PrimaryButton
import com.stt.android.compose.widgets.TextFieldInputWithError
import com.stt.android.resetpassword.SetPasswordError
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber

@AndroidEntryPoint
class FragmentSetPasswordWhenSubscribe : BaseFragment() {

    private val viewModel: FragmentSetPasswordViewModel by viewModels()
    private val setPasswordWhenSubscribeArgs: FragmentSetPasswordWhenSubscribeArgs by navArgs()

    @Composable
    override fun SetContentView() {
        SetPasswordWhenSubscribeScreen()
    }

    @Composable
    private fun SetPasswordWhenSubscribeScreen() {
        val uiState = viewModel.uiState
        val commonUIState by viewModel.commonUIState.collectAsState()
        LaunchedEffect(uiState.setPasswordResult) {
            uiState.setPasswordResult?.let {
                Timber.d("setPasswordResult: $it")
                NavUtils.navigateUpFromSameTask(requireActivity())
            }
        }

        AppTheme {
            Scaffold { internalPadding ->
                ContentCenteringColumn(Modifier.padding(internalPadding)) {
                    Surface {
                        ContentBody(
                            isLoading = commonUIState.isLoading,
                            newPassword = uiState.newPassword,
                            confirmPassword = uiState.confirmPassword,
                            setPasswordError = uiState.setPasswordError,
                            onInputNewPassword = {
                                viewModel.inputNewPassword(it)
                            },
                            onSetPasswordWhenSubscribe = { newPassword, confirmPassword ->
                                viewModel.setPassword(
                                    setPasswordWhenSubscribeArgs.email,
                                    newPassword,
                                    confirmPassword,
                                    setPasswordWhenSubscribeArgs.token
                                )
                            },
                            onInputConfirmPassword = {
                                viewModel.inputConfirmPassword(it)
                            }
                        )
                    }
                }
            }
        }
    }
}

@Preview
@Composable
private fun SetPasswordWhenSubscribeScreenPreview() {
    AppTheme {
        Surface {
            ContentBody()
        }
    }
}

@OptIn(ExperimentalComposeUiApi::class)
@Composable
private fun ContentBody(
    isLoading: Boolean = false,
    newPassword: String = "",
    confirmPassword: String = "",
    setPasswordError: SetPasswordError? = null,
    onInputNewPassword: (String) -> Unit = {},
    onSetPasswordWhenSubscribe: (String, String) -> Unit = { _, _ -> },
    onInputConfirmPassword: (String) -> Unit = {},
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Top,
        modifier = Modifier
            .fillMaxSize()
            .verticalScroll(rememberScrollState())
    ) {
        Spacer(modifier = Modifier.height(MaterialTheme.spacing.xlarge))
        Text(
            stringResource(
                id = R.string.set_password_title
            ),
            style = MaterialTheme.typography.bodyXLargeBold,
            textAlign = TextAlign.Center,
        )
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(
                    top = MaterialTheme.spacing.xlarge
                ),
            horizontalArrangement = Arrangement.Center,
        ) {
            TextFieldInputWithError(
                currentText = newPassword,
                placeholderText = stringResource(id = R.string.input_new_password),
                onChanged = { onInputNewPassword.invoke(it) },
                errorMessage = setPasswordError?.let { stringResource(id = it.resId) } ?: "",
                keyboardType = KeyboardType.Password,
                isPassword = true
            )
        }

        TextFieldInputWithError(
            currentText = confirmPassword,
            placeholderText = stringResource(id = R.string.input_password_again),
            onChanged = { onInputConfirmPassword.invoke(it) },
            keyboardType = KeyboardType.Password,
            errorMessage = setPasswordError?.let { stringResource(id = it.resId) } ?: "",
            tipsText = stringResource(id = R.string.input_password_tips),
            isPassword = true
        )
        val keyboardController = LocalSoftwareKeyboardController.current
        PrimaryButton(
            onClick = {
                onSetPasswordWhenSubscribe.invoke(newPassword, confirmPassword)
                keyboardController?.hide()
            },
            backgroundColor = MaterialTheme.colors.primary,
            text = stringResource(R.string.continue_str),
            modifier = Modifier
                .fillMaxWidth()
                .padding(
                    horizontal = MaterialTheme.spacing.medium,
                    vertical = MaterialTheme.spacing.xlarge
                )
        )
    }
    LoadingContent(isLoading)
}
