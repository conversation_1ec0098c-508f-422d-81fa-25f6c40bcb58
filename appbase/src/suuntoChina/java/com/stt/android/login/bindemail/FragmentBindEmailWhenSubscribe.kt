package com.stt.android.login.bindemail

import androidx.compose.foundation.layout.padding
import androidx.compose.material.Scaffold
import androidx.compose.material.Surface
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import com.stt.android.R
import com.stt.android.compose.base.BaseFragment
import com.stt.android.compose.layout.ContentCenteringColumn
import com.stt.android.compose.theme.AppTheme
import com.stt.android.newemail.BindEmailBody
import com.stt.android.newemail.EmailError
import com.stt.android.newemail.FragmentBindEmailViewModel
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class FragmentBindEmailWhenSubscribe : BaseFragment() {

    private val viewModel: FragmentBindEmailViewModel by viewModels()

    @Composable
    override fun SetContentView() {
        BindEmailScreen()
    }

    @Composable
    private fun BindEmailScreen() {
        val uiState = viewModel.uiState
        val commonUIState by viewModel.commonUIState.collectAsState()
        LaunchedEffect(uiState.sendEmailVerificationCodeSuccess) {
            uiState.sendEmailVerificationCodeSuccess?.let {
                findNavController().navigate(
                    FragmentBindEmailWhenSubscribeDirections
                        .actionBindEmailVerifyCodeWhenSubscribe()
                        .setEmail(uiState.email)
                )
                viewModel.clearSendEmailVerificationCodeSuccess()
            }
        }

        AppTheme {
            Scaffold { internalPadding ->
                ContentCenteringColumn(Modifier.padding(internalPadding)) {
                    Surface {
                        ContentBody(
                            isLoading = commonUIState.isLoading,
                            email = uiState.email,
                            emailError = uiState.emailError,
                            onInputEmail = {
                                viewModel.inputEmail(it)
                            },
                            onBtnClicked = { email ->
                                viewModel.sendEmailVerificationCode(email)
                            },
                            onSkipClicked = {
                                activity?.finish()
                            }
                        )
                    }
                }
            }
        }
    }
}

@Preview
@Composable
private fun BindEmailScreenPreview() {
    AppTheme {
        Surface {
            ContentBody()
        }
    }
}

@OptIn(ExperimentalComposeUiApi::class)
@Composable
private fun ContentBody(
    isLoading: Boolean = false,
    email: String = "",
    emailError: EmailError? = null,
    onInputEmail: (String) -> Unit = {},
    onBtnClicked: (String) -> Unit = {},
    onSkipClicked: () -> Unit = {},
) {
    val keyboardController = LocalSoftwareKeyboardController.current
    BindEmailBody(
        stringResource(id = R.string.bind_email),
        isLoading,
        email,
        showSkip = true,
        emailError = emailError,
        onInputEmail = onInputEmail,
        onBtnClicked = {
            onBtnClicked.invoke(email)
            keyboardController?.hide()
        },
        onSkipClicked = onSkipClicked
    )
}
