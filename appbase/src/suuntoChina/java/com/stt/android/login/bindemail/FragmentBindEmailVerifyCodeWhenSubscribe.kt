package com.stt.android.login.bindemail

import androidx.compose.foundation.layout.padding
import androidx.compose.material.Scaffold
import androidx.compose.material.Surface
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.tooling.preview.Preview
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.stt.android.R
import com.stt.android.compose.base.BaseFragment
import com.stt.android.compose.layout.ContentCenteringColumn
import com.stt.android.compose.theme.AppTheme
import com.stt.android.newemail.BindEmailVerifyCodeBody
import com.stt.android.newemail.FragmentBindEmailVerifyCodeViewModel
import com.stt.android.utils.CustomTabsUtils
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber

@AndroidEntryPoint
class FragmentBindEmailVerifyCodeWhenSubscribe : BaseFragment() {

    private val viewModel: FragmentBindEmailVerifyCodeViewModel by viewModels()
    private val bindEmailVerifyCodeWhenSubscribeArgs: FragmentBindEmailVerifyCodeWhenSubscribeArgs by navArgs()

    @Composable
    override fun SetContentView() {
        BindEmailVerifyCodeScreen()
    }

    @Composable
    private fun BindEmailVerifyCodeScreen() {
        val uiState = viewModel.uiState
        val commonUIState by viewModel.commonUIState.collectAsState()
        LaunchedEffect(uiState.emailVerifyToken) {
            uiState.emailVerifyToken?.let {
                Timber.d("token: $it")
                findNavController().navigate(
                    FragmentBindEmailVerifyCodeWhenSubscribeDirections.actionSetPasswordWhenSubscribeFragment()
                        .setToken(it)
                        .setEmail(bindEmailVerifyCodeWhenSubscribeArgs.email)
                )
            }
        }

        AppTheme {
            Scaffold { internalPadding ->
                ContentCenteringColumn(Modifier.padding(internalPadding)) {
                    Surface {
                        ContentBody(
                            email = bindEmailVerifyCodeWhenSubscribeArgs.email,
                            isLoading = commonUIState.isLoading,
                            verifyCode = uiState.verifyCode,
                            verifyCodeIsExpired = uiState.verifyCodeIsExpired,
                            onInputVerifyCode = {
                                viewModel.inputVerifyCode(it)
                            },
                            onVerificationCodeClicked = { email, verifyCode ->
                                viewModel.checkVerificationCode(email, verifyCode)
                            },
                            onResendClicked = { email ->
                                viewModel.sendEmailVerificationCode(email)
                            },
                            onContactClicked = {
                                activity?.apply {
                                    CustomTabsUtils.launchCustomTab(
                                        this,
                                        getString(R.string.contact_support_link_suunto)
                                    )
                                }
                            }
                        )
                    }
                }
            }
        }
    }
}

@Preview
@Composable
private fun BindEmailVerifyCodeScreenPreview() {
    AppTheme {
        Surface {
            ContentBody()
        }
    }
}

@OptIn(ExperimentalComposeUiApi::class)
@Composable
private fun ContentBody(
    email: String = "",
    isLoading: Boolean = false,
    verifyCode: String = "",
    verifyCodeIsExpired: Boolean = false,
    onInputVerifyCode: (String) -> Unit = {},
    onVerificationCodeClicked: (String, String) -> Unit = { _, _ -> },
    onResendClicked: (String) -> Unit = {},
    onContactClicked: () -> Unit = {},
) {
    val keyboardController = LocalSoftwareKeyboardController.current
    BindEmailVerifyCodeBody(
        isLoading = isLoading,
        verifyCode = verifyCode,
        verifyCodeIsExpired = verifyCodeIsExpired,
        email = email,
        onInputVerifyCode = onInputVerifyCode,
        onVerificationCodeClicked = {
            onVerificationCodeClicked.invoke(email, verifyCode)
            keyboardController?.hide()
        },
        onResendClicked = {
            onResendClicked.invoke(email)
        },
        onContactClicked = onContactClicked
    )
}
