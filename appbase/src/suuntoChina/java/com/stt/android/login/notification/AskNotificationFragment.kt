package com.stt.android.login.notification

import androidx.navigation.NavDirections
import com.stt.android.login.signuplogindone.SignUpLoginDoneActivity
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class AskNotificationFragment : BaseAskNotificationFragment() {
    override fun navDirection(): NavDirections {
        val isNewUser = activity?.intent
            ?.getBooleanExtra(SignUpLoginDoneActivity.KEY_IS_NEW_USER, false)
            ?: false
        return AskNotificationFragmentDirections
            .actionAskNotificationFragmentToNewsletterSubscriptionFragment(isNewUser)
    }
}
