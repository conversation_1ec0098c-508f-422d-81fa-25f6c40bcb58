package com.stt.android.newphone

import android.content.Context
import android.content.Intent
import android.os.Bundle
import com.stt.android.R
import com.stt.android.newemail.BaseComposeActivity
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class NewPhoneChinaActivity : BaseComposeActivity() {

    companion object {
        private const val EXTRA_FROM_LOGIN = "EXTRA_FROM_LOGIN"
        fun newStartIntent(context: Context, fromLogin: Boolean = false): Intent {
            return Intent(context, NewPhoneChinaActivity::class.java).apply {
                putExtra(EXTRA_FROM_LOGIN, fromLogin)
            }
        }
    }

    override fun getNavGraph(): Int {
        return R.navigation.new_phone_nav_graph
    }

    override fun getStartDestinationArgs(): Bundle {
        val fromLogin = intent.getBooleanExtra(EXTRA_FROM_LOGIN, false)
        return FragmentNewPhoneArgs.Builder()
            .setFromLogin(fromLogin)
            .build()
            .toBundle()
    }

}
