package com.stt.android.newphone

import android.app.Activity
import android.content.Intent
import androidx.compose.material.Surface
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.tooling.preview.Preview
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.google.android.material.snackbar.Snackbar
import com.stt.android.R
import com.stt.android.compose.base.BaseFragment
import com.stt.android.compose.theme.AppTheme
import com.stt.android.resetpassword.BaseContentBody
import com.stt.android.resetpassword.CheckVerifyCodeBody
import com.stt.android.utils.CustomTabsUtils
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber

@AndroidEntryPoint
class FragmentNewPhoneVerifyCode : BaseFragment() {
    private val viewModel: FragmentNewPhoneVerifyCodeViewModel by viewModels()
    private val newPhoneVerifyCodeArgs: FragmentNewPhoneVerifyCodeArgs by navArgs()

    @Composable
    override fun SetContentView() {
        BindPhoneVerifyCodeScreen()
    }

    @Composable
    private fun BindPhoneVerifyCodeScreen() {
        val uiState = viewModel.uiState
        val commonUIState by viewModel.commonUIState.collectAsState()
        LaunchedEffect(uiState.changePhoneNumberSuccess) {
            uiState.changePhoneNumberSuccess?.let {
                Timber.d("change phone result: $it")
                if (newPhoneVerifyCodeArgs.fromLogin) {
                    setBindResult()
                } else {
                    Snackbar.make(
                        requireView(),
                        R.string.change_successful,
                        Snackbar.LENGTH_LONG
                    ).apply {
                        show()
                        addCallback(object : Snackbar.Callback() {
                            override fun onDismissed(transientBottomBar: Snackbar?, event: Int) {
                                setBindResult()
                            }
                        })
                    }
                }
            }
        }

        BaseContentBody(
            viewModel = viewModel,
            onBackClick = {
                findNavController().popBackStack()
            }
        ) {
            ContentBody(
                commonUIState.isLoading,
                newPhoneVerifyCodeArgs.phone,
                uiState.verifyCode,
                uiState.verifyCodeIsExpired,
                uiState.btnEnabled,
                onBtnClicked = { phoneNumber, verifyCode ->
                    viewModel.checkVerificationCode(
                        phoneNumber,
                        verifyCode
                    )
                },
                onInputVerifyCode = {
                    viewModel.inputVerifyCode(it)
                },
                onResendCallback = { phoneNumber ->
                    viewModel.sendPhoneVerificationCode(phoneNumber)
                },
                onContactClick = {
                    activity?.apply {
                        CustomTabsUtils.launchCustomTab(
                            this,
                            getString(R.string.contact_support_link_suunto)
                        )
                    }
                }
            )
        }
    }

    private fun setBindResult() {
        activity?.setResult(Activity.RESULT_OK)
        activity?.finish()
    }
}

@Preview
@Composable
private fun BindPhoneVerifyCodeScreenPreview() {
    AppTheme {
        Surface {
            ContentBody()
        }
    }
}

@Composable
@OptIn(ExperimentalComposeUiApi::class)
private fun ContentBody(
    isLoading: Boolean = false,
    phoneNumber: String = "",
    verifyCode: String = "",
    verifyCodeIsExpired: Boolean = false,
    btnEnabled: Boolean = false,
    onBtnClicked: (String, String) -> Unit = { _, _ -> },
    onInputVerifyCode: (String) -> Unit = {},
    onResendCallback: (String) -> Unit = {},
    onContactClick: () -> Unit = {},
) {
    val keyboardController = LocalSoftwareKeyboardController.current
    CheckVerifyCodeBody(
        isLoading,
        phoneNumber,
        verifyCode,
        verifyCodeIsExpired,
        btnEnabled = btnEnabled,
        onBtnClicked = {
            onBtnClicked.invoke(phoneNumber, verifyCode)
            keyboardController?.hide()
        },
        onInputVerifyCode = onInputVerifyCode,
        onResendCallback = {
            onResendCallback.invoke(phoneNumber)
        },
        onContactClick = onContactClick,
        onActionDone = {
            if (btnEnabled) {
                onBtnClicked.invoke(phoneNumber, verifyCode)
            }
        }
    )
}
