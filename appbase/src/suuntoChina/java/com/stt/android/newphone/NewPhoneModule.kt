package com.stt.android.newphone

import com.squareup.moshi.Moshi
import com.stt.android.remote.AuthProvider
import com.stt.android.remote.BaseUrl
import com.stt.android.remote.SharedOkHttpClient
import com.stt.android.remote.UserAgent
import com.stt.android.remote.di.BrandOkHttpConfigFactory
import com.stt.android.remote.di.RestApiFactory
import dagger.Binds
import dagger.Module
import dagger.Provides
import okhttp3.OkHttpClient

@Module
abstract class NewPhoneModule {

    @Binds
    abstract fun bindNewPhoneRemoteDataSource(
        newEmailRemoteDataSource: NewPhoneRemoteDataSource
    ): NewPhoneDataSource

    companion object {

        @Provides
        fun provideNewPhone(
            @SharedOkHttpClient sharedClient: OkHttpClient,
            @BaseUrl baseUrl: String,
            @UserAgent userAgent: String,
            authProvider: AuthProvider,
            moshi: <PERSON><PERSON>
        ): NewPhoneApi {
            return RestApiFactory.buildRestApi(
                sharedClient,
                baseUrl,
                NewPhoneApi::class.java,
                BrandOkHttpConfigFactory.getStOkHttpConfig(authProvider, userAgent),
                moshi
            )
        }
    }
}
