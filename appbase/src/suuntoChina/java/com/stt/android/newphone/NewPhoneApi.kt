package com.stt.android.newphone

import com.stt.android.remote.BaseRemoteApi
import com.stt.android.remote.response.AskoResponse
import retrofit2.http.Header
import retrofit2.http.POST
import retrofit2.http.Query

interface NewPhoneApi {
    @POST("update/phonenumber/sms")
    suspend fun sendPhoneVerificationCode(
        @Header(BaseRemoteApi.HEADER_TOTP_KEY) totp: String,
        @Query("phonenumber") email: String
    ): AskoResponse<Boolean>
}
