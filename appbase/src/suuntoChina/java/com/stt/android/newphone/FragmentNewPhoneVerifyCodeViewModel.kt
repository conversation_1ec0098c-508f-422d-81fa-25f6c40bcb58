package com.stt.android.newphone

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.viewModelScope
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.base.BaseHandlerViewModel
import com.stt.android.controllers.UserSettingsController
import com.stt.android.home.settings.PhoneNumberUtil
import com.stt.android.newemail.VERIFICATION_CODE_LENGTH
import com.stt.android.remote.AuthProvider
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class FragmentNewPhoneVerifyCodeViewModel @Inject constructor(
    private val newPhoneRepository: NewPhoneRepository,
    private val dispatchers: CoroutinesDispatchers,
    private val authProvider: AuthProvider,
    private val userSettingsController: UserSettingsController,
) : BaseHandlerViewModel() {

    var uiState by mutableStateOf(NewPhoneVerifyCodeUIState())
    fun inputVerifyCode(verifyCode: String) {
        uiState = uiState.copy(
            verifyCode = verifyCode,
            verifyCodeIsExpired = false,
            btnEnabled = verifyCode.length == VERIFICATION_CODE_LENGTH
        )
    }

    fun checkVerificationCode(phoneNumber: String, verifyCode: String) {
        if (phoneNumber.isNotEmpty() && verifyCode.isNotEmpty()) {
            uiState = uiState.copy(btnEnabled = false)
            loadingStart()
            viewModelScope.launch(
                dispatchers.io + getCheckVerificationCodeFailedHandler {
                    uiState = uiState.copy(verifyCodeIsExpired = true)
                }
            ) {
                val phoneVerificationToken = newPhoneRepository.checkPhoneVerificationCode(
                    authProvider.getSessionKey() ?: "",
                    phoneNumber,
                    verifyCode
                )
                newPhoneRepository.changePhoneNumber(phoneNumber, phoneVerificationToken)
                userSettingsController.storeSettings(
                    userSettingsController.settings.setPhoneNumber("${PhoneNumberUtil.PHONE_REGION_CHINA}$phoneNumber")
                )
                uiState = uiState.copy(changePhoneNumberSuccess = true)
                loadingEnd()
            }
        }
    }

    fun sendPhoneVerificationCode(phoneNumber: String) {
        if (phoneNumber.isNotEmpty()) {
            loadingStart()
            viewModelScope.launch(dispatchers.io + exceptionHandler) {
                newPhoneRepository.sendPhoneVerificationCode(authProvider.getSessionKey() ?: "", phoneNumber)
                loadingEnd()
            }
        }
    }

    override fun loadingEndWithFail(throwable: Throwable) {
        super.loadingEndWithFail(throwable)
        uiState = uiState.copy(btnEnabled = true)
    }
}

data class NewPhoneVerifyCodeUIState(
    val verifyCode: String = "",
    val changePhoneNumberSuccess: Boolean? = null,
    val verifyCodeIsExpired: Boolean = false,
    val btnEnabled: Boolean = false
)
