package com.stt.android.newphone

import com.stt.android.exceptions.remote.ClientError
import com.stt.android.exceptions.remote.STTError
import com.stt.android.remote.otp.GenerateOTPUseCase
import com.stt.android.remote.usersettings.RemoteSaveUserPhoneNumberRequest
import com.stt.android.remote.usersettings.UserSettingsRestApi
import javax.inject.Inject

class NewPhoneRemoteDataSource @Inject constructor(
    private val newPhoneApi: NewPhoneApi,
    private val userSettingsRestApi: UserSettingsRestApi,
    private val phoneNumberVerificationHelper: PhoneNumberVerificationHelper,
    private val generateOTPUseCase: GenerateOTPUseCase,
) : NewPhoneDataSource {

    override suspend fun changePhoneNumber(phone: String, token: String) {
        try {
            userSettingsRestApi.saveUserPhoneNumber(
                generateOTPUseCase.generateTOTP(),
                token,
                RemoteSaveUserPhoneNumberRequest(phone)
            )
        } catch (e: Exception) {
            if (e is ClientError.Conflict) {
                throw STTError.PhoneNumberAlreadyExists()
            } else {
                throw e
            }
        }
    }

    override suspend fun sendPhoneVerificationCode(userKey: String, phone: String): Any {
        return newPhoneApi.sendPhoneVerificationCode(userKey, phone).payloadOrThrow()
    }

    override suspend fun checkPhoneVerificationCode(
        userKey: String,
        phone: String,
        pin: String
    ): String {
        return phoneNumberVerificationHelper.verifyPhoneNumber(phone, pin)
    }

}
