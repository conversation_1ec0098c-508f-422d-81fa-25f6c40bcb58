package com.stt.android.newphone

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.viewModelScope
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.compose.base.BaseViewModel
import com.stt.android.exceptions.remote.ClientError
import com.stt.android.home.settings.PhoneNumberUtil.PHONE_NUMBER_LENGTH_CHINA
import com.stt.android.remote.AuthProvider
import com.stt.android.ui.tasks.LogoutTask
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

@HiltViewModel
class FragmentNewPhoneViewModel @Inject constructor(
    val savedStateHandle: SavedStateHandle,
    private val newPhoneRepository: NewPhoneRepository,
    private val dispatchers: CoroutinesDispatchers,
    private val authProvider: AuthProvider,
    private val logoutTask: LogoutTask
) : BaseViewModel() {

    var uiState by mutableStateOf(BindEmailUIState())

    private val updatePhoneNumberHandler = CoroutineExceptionHandler { _, throwable ->
        when (throwable) {
            is ClientError.BadRequest -> {
                uiState = uiState.copy(phoneNumberError = PhoneNumberError.PhoneNumberIsError)
                loadingEnd()
            }
            is ClientError.Conflict -> {
                uiState = uiState.copy(phoneNumberError = PhoneNumberError.PhoneNumberHasBeenRegistered)
                loadingEnd()
            }
            else -> {
                loadingEndWithFail(throwable)
            }
        }
        Timber.w(throwable, "message: ${throwable.message}")
    }

    fun sendPhoneVerificationCode(phoneNumber: String) {
        if (!isChinesePhoneNumberValid(phoneNumber)) {
            uiState = uiState.copy(phoneNumberError = PhoneNumberError.PhoneNumberIsError)
        } else {
            loadingStart()
            viewModelScope.launch(dispatchers.io + updatePhoneNumberHandler) {
                newPhoneRepository.sendPhoneVerificationCode(authProvider.getSessionKey() ?: "", phoneNumber)
                uiState = uiState.copy(sendPhoneVerificationCodeSuccess = true)
                loadingEnd()
            }
        }
    }

    fun inputPhoneNumber(phoneNumber: String) {
        uiState = uiState.copy(
            phoneNumber = phoneNumber,
            phoneNumberError = null,
            isBtnEnable = phoneNumber.length == PHONE_NUMBER_LENGTH_CHINA
        )
    }

    fun clearSendPhoneVerificationCodeSuccess() {
        uiState = uiState.copy(sendPhoneVerificationCodeSuccess = null)
    }

    fun logoutIfFromLogin() {
        // need logout if the user has not bound phone number from login
        if (FragmentNewPhoneArgs.fromSavedStateHandle(savedStateHandle).fromLogin) {
            GlobalScope.launch(Dispatchers.IO) {
                logoutTask.logout().subscribe (
                    { Timber.d("logging out success") },
                    { Timber.w(it,"logging out failed")} )
            }
        }
    }
}

data class BindEmailUIState(
    val phoneNumber: String = "",
    val sendPhoneVerificationCodeSuccess: Boolean? = null,
    val phoneNumberError: PhoneNumberError? = null,
    val isBtnEnable: Boolean = false,
)
