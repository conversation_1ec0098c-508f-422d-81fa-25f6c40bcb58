package com.stt.android.newphone

import com.stt.android.home.settings.PhoneNumberUtil.PHONE_REGION_CHINA
import javax.inject.Inject

class NewPhoneRepository @Inject constructor(
    private val newPhoneDataSource: NewPhoneDataSource
) {
    suspend fun sendPhoneVerificationCode(userKey: String, phone: String): Any {
        val fullPhoneNumber = "$PHONE_REGION_CHINA$phone"
        return newPhoneDataSource.sendPhoneVerificationCode(userKey, fullPhoneNumber)
    }

    suspend fun checkPhoneVerificationCode(userKey: String, phone: String, pin: String): String {
        val fullPhoneNumber = "$PHONE_REGION_CHINA$phone"
        return newPhoneDataSource.checkPhoneVerificationCode(userKey, fullPhoneNumber, pin)
    }

    suspend fun changePhoneNumber(phone: String, token: String) {
        val fullPhoneNumber = "$PHONE_REGION_CHINA$phone"
        return newPhoneDataSource.changePhoneNumber(fullPhoneNumber, token)
    }
}
