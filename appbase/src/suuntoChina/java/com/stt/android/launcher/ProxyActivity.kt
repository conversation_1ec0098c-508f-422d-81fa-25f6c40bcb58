package com.stt.android.launcher

import android.os.Bundle
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class ProxyActivity : BaseProxyActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        viewModel.startIcpRequestJob()
    }

    override fun isLoginToFacebookNeeded(): <PERSON><PERSON><PERSON> {
        return false
    }

    override fun loginToFacebook(): Boolean {
        return false
    }

    // the user should bind a phone number before logging in according to the local policy
    override fun userIsNotLoggedIn(): <PERSON><PERSON>an {
        return super.userIsNotLoggedIn() || userSettingsController.settings.phoneNumber.isNullOrEmpty()
    }
}
