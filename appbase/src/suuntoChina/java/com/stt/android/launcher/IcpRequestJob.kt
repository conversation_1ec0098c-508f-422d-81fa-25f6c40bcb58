package com.stt.android.launcher

import android.content.Context
import android.content.pm.ServiceInfo
import android.os.Build
import androidx.core.app.NotificationCompat
import androidx.work.Constraints
import androidx.work.CoroutineWorker
import androidx.work.ExistingWorkPolicy
import androidx.work.ForegroundInfo
import androidx.work.ListenableWorker
import androidx.work.NetworkType
import androidx.work.OneTimeWorkRequestBuilder
import androidx.work.OutOfQuotaPolicy
import androidx.work.WorkManager
import androidx.work.WorkerParameters
import com.stt.android.R
import com.stt.android.backgroundwork.CoroutineWorkerAssistedFactory
import com.stt.android.notifications.CHANNEL_ID_FOREGROUND_SYNC
import com.stt.android.remote.SharedOkHttpClient
import com.stt.android.utils.STTConstants
import okhttp3.OkHttpClient
import okhttp3.Request
import timber.log.Timber
import javax.inject.Inject

class IcpRequestJob(
    private val context: Context,
    params: WorkerParameters,
    private val okHttpClient: OkHttpClient
) : CoroutineWorker(context, params) {

    override suspend fun doWork(): Result = runCatching {
        val url = applicationContext.getString(R.string.icp_url)
        val request = Request.Builder().get().url(url).build()
        val response = okHttpClient.newCall(request).execute()
        Timber.d("icp response code: ${response.code}, body: ${response.body?.use { it.string() }}")
        Result.success()
    }.onFailure {
        Timber.w(it, "failed to request icp api.")
    }.getOrElse { Result.failure() }

    override suspend fun getForegroundInfo(): ForegroundInfo {
        val notificationId = STTConstants.NotificationIds.EXPEDITED_ICP_REQUEST
        val notification = createNotification()

        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            ForegroundInfo(
                notificationId,
                notification,
                ServiceInfo.FOREGROUND_SERVICE_TYPE_DATA_SYNC
            )
        } else {
            ForegroundInfo(
                notificationId,
                notification
            )
        }
    }

    private fun createNotification() =
        NotificationCompat.Builder(context, CHANNEL_ID_FOREGROUND_SYNC)
            .setSmallIcon(R.drawable.icon_notification)
            .setVisibility(NotificationCompat.VISIBILITY_PRIVATE)
            .setContentTitle(context.getString(R.string.notification_channel_foreground_sync))
            .setContentText(context.getString(R.string.notification_foreground_sync_content))
            .build()

    class Factory @Inject constructor(
        @SharedOkHttpClient private val okHttpClient: OkHttpClient
    ) : CoroutineWorkerAssistedFactory {

        override fun create(context: Context, params: WorkerParameters): ListenableWorker {
            return IcpRequestJob(
                context = context,
                params = params,
                okHttpClient = okHttpClient
            )
        }
    }

    companion object {

        @JvmStatic
        fun enqueue(workManager: WorkManager) {
            workManager.enqueueUniqueWork(
                IcpRequestJob::class.java.simpleName,
                ExistingWorkPolicy.KEEP,
                OneTimeWorkRequestBuilder<IcpRequestJob>()
                    .setConstraints(
                        Constraints.Builder().setRequiredNetworkType(NetworkType.CONNECTED).build()
                    )
                    .setExpedited(OutOfQuotaPolicy.RUN_AS_NON_EXPEDITED_WORK_REQUEST)
                    .build()
            )
        }
    }
}
