package com.stt.android.launcher

import com.stt.android.backgroundwork.CoroutineWorkerAssistedFactory
import com.stt.android.backgroundwork.WorkerKey
import com.stt.android.launcher.IcpRequestJob
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import dagger.multibindings.IntoMap

@Module
@InstallIn(SingletonComponent::class)
abstract class IcpRequestJobModule {

    @Binds
    @IntoMap
    @WorkerKey(IcpRequestJob::class)
    abstract fun bindFactory(factory: IcpRequestJob.Factory): CoroutineWorkerAssistedFactory
}
