package com.stt.android.findfriends.v2

import com.stt.android.findfriends.PhoneContactsData
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.ImmutableMap

data class PhoneContactsState(
    val loading: <PERSON><PERSON><PERSON>,
    val searching: <PERSON><PERSON><PERSON>,
    val keyword: String,
    val phoneContacts: ImmutableMap<String, ImmutableList<PhoneContactsData>>,
    val searchedPhoneContacts: ImmutableList<PhoneContactsData>,
)
