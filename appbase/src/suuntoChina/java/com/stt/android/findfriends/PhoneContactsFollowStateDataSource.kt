package com.stt.android.findfriends

import com.stt.android.remote.findfriends.PhoneContactFollowStateRestApi
import com.stt.android.remote.findfriends.PhoneContactFollowStateRestApiV2
import com.stt.android.remote.findfriends.RemoteFollowStateChangeData
import com.stt.android.remote.findfriends.RemoteUserFollowStateInfo
import javax.inject.Inject

class PhoneContactsFollowStateDataSource @Inject constructor(
    private val phoneContactFollowStateRestApi: PhoneContactFollowStateRestApi,
    private val phoneContactFollowStateRestApiV2: PhoneContactFollowStateRestApiV2,
) {
    suspend fun loadUserFollowState(phones: List<String>): List<RemoteUserFollowStateInfo>? {
        return phoneContactFollowStateRestApi.loadUserFollowStateV2(phones)
            .payloadNullableOrThrow()
    }

    suspend fun follow(userName: String): RemoteFollowStateChangeData? {
        return phoneContactFollowStateRestApiV2.follow(userName).payloadNullableOrThrow()
    }

    suspend fun unFollow(userName: String): RemoteFollowStateChangeData? {
        return phoneContactFollowStateRestApi.unFollow(userName).payloadNullableOrThrow()
    }
}
