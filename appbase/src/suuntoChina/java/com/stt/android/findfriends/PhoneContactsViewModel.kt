package com.stt.android.findfriends

import androidx.compose.runtime.State
import androidx.compose.runtime.mutableStateOf
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.stt.android.exceptions.remote.HttpException
import com.stt.android.exceptions.remote.UnknownNetworkError
import com.stt.android.findfriends.v2.PhoneContactsState
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.ImmutableMap
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.persistentMapOf
import kotlinx.collections.immutable.toImmutableList
import kotlinx.collections.immutable.toImmutableMap
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import me.majiajie.tinypinyin.Pinyin
import timber.log.Timber
import javax.inject.Inject

@HiltViewModel
class PhoneContactsViewModel @Inject constructor(
    private val phoneContactsRepository: PhoneContactsRepository
) : ViewModel() {
    private val _phoneContactsState =
        mutableStateOf<ImmutableMap<String, ImmutableList<PhoneContactsData>>>(
            persistentMapOf()
        )
    val phoneContactsState: State<ImmutableMap<String, ImmutableList<PhoneContactsData>>> =
        _phoneContactsState
    private var cacheAllPhoneContactsData: Map<String, ImmutableList<PhoneContactsData>> =
        emptyMap()
    private val _loadingState = mutableStateOf(true)
    val loadingState: State<Boolean> = _loadingState
    private val _errorException = mutableStateOf<HttpException?>(null)
    val errorException: State<HttpException?> = _errorException

    // For new phone contacts screen
    private var _phoneContactsStateFlow = MutableStateFlow(
        PhoneContactsState(
            loading = true,
            searching = false,
            keyword = "",
            phoneContacts = persistentMapOf(),
            searchedPhoneContacts = persistentListOf(),
        )
    )
    val phoneContactsStateFlow = _phoneContactsStateFlow.asStateFlow()

    fun loadPhoneContacts() {
        viewModelScope.launch {
            phoneContactsRepository.loadPhoneContacts().catch {
                Timber.d(it, "load phoneContacts fail:${it.message}")
            }.collect { data ->
                val phoneContactMap = data.groupBy {
                    getFirstLetterFromName(it.name)
                }.mapValues {
                    it.value.toImmutableList()
                }
                val value = getSortedMap(phoneContactMap)
                _loadingState.value = false
                _phoneContactsState.value = value.toImmutableMap()
                cacheAllPhoneContactsData = value
                _phoneContactsStateFlow.update {
                    it.copy(
                        loading = false,
                        phoneContacts = value.toImmutableMap(),
                    )
                }
            }
        }
    }

    private fun getSortedMap(data: Map<String, ImmutableList<PhoneContactsData>>): Map<String, ImmutableList<PhoneContactsData>> {
        return data.toSortedMap { o1, o2 ->
            if (o1 == "#") {
                1
            } else if (o2 == "#") {
                -1
            } else {
                o1.compareTo(o2)
            }
        }
    }

    private fun getFirstLetterFromName(name: String): String {
        val firstCharacter = name.first()
        return if (Pinyin.isChinese(firstCharacter)) {
            Pinyin.toPinyin(firstCharacter).first().toString()
        } else if (name.first().isLetter()) {
            name.first().uppercase()
        } else {
            "#"
        }
    }

    fun handleFollowState(item: PhoneContactsData) {
        item.userName?.let {
            viewModelScope.launch {
                try {
                    val followingState =
                        phoneContactsRepository.handleFollowState(item.followingState, it)
                    val phoneContactsMap = cacheAllPhoneContactsData.toMutableMap()
                    val firstLetterFromName = getFirstLetterFromName(item.name)
                    val phoneContactsDataList = phoneContactsMap[firstLetterFromName]
                    val newPhoneContactsDataList = phoneContactsDataList?.run {
                        map { phoneContactsData ->
                            if (phoneContactsData.name == item.name) {
                                phoneContactsData.copy(followingState = followingState)
                            } else {
                                phoneContactsData
                            }
                        }
                    }
                    newPhoneContactsDataList?.let { value ->
                        phoneContactsMap[firstLetterFromName] = value.toImmutableList()
                    }
                    _phoneContactsState.value = phoneContactsMap.toImmutableMap()
                    // update cache
                    cacheAllPhoneContactsData = phoneContactsMap
                    _phoneContactsStateFlow.update {
                        it.copy(
                            phoneContacts = phoneContactsMap.toImmutableMap(),
                            searchedPhoneContacts = it.searchedPhoneContacts.map { phoneContactsData ->
                                if (phoneContactsData.name == item.name) {
                                    phoneContactsData.copy(followingState = followingState)
                                } else {
                                    phoneContactsData
                                }
                            }.toImmutableList()
                        )
                    }
                } catch (e: Exception) {
                    Timber.w(e, "handle fail: ${e.message}")
                    if (e is HttpException) {
                        _errorException.value = e
                    } else {
                        _errorException.value = UnknownNetworkError(0, e.message ?: "")
                    }
                }
            }
        }
    }

    fun search(keyword: String) {
        viewModelScope.launch {
            val allPhoneContactsData = mutableListOf<PhoneContactsData>()
            cacheAllPhoneContactsData.values.forEach {
                allPhoneContactsData += it
            }
            val searchData = allPhoneContactsData.filter { data ->
                data.name.contains(keyword, true)
                    || data.phones.any { it.contains(keyword) }
                    || data.suuntoName.contains(keyword, true)
            }
            val phoneContactMap = searchData.groupBy {
                getFirstLetterFromName(it.name)
            }.mapValues { it.value.toImmutableList() }
            val value = getSortedMap(phoneContactMap)
            _phoneContactsState.value = value.toImmutableMap()
            _phoneContactsStateFlow.update {
                it.copy(
                    searching = true,
                    keyword = keyword,
                    searchedPhoneContacts = if (keyword.isBlank()) {
                        persistentListOf()
                    } else {
                        searchData.toImmutableList()
                    },
                )
            }
        }
    }

    fun cancelSearch() {
        _phoneContactsStateFlow.update {
            it.copy(
                searching = false,
                keyword = "",
                searchedPhoneContacts = persistentListOf(),
            )
        }
    }
}
