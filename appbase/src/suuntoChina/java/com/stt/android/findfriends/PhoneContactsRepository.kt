package com.stt.android.findfriends

import android.content.Context
import android.provider.ContactsContract
import com.stt.android.controllers.UserSettingsController
import com.stt.android.remote.findfriends.RemoteFollowState
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.map
import java.util.Locale
import javax.inject.Inject

class PhoneContactsRepository @Inject constructor(
    @ApplicationContext
    private val context: Context,
    private val phoneContactsFollowStateDataSource: PhoneContactsFollowStateDataSource,
    private val userSettingsController: UserSettingsController,
) {
    private val phoneContactsSelection: String =
        "${ContactsContract.CommonDataKinds.Phone.CONTACT_ID} = ?"

    fun loadPhoneContacts(): Flow<List<PhoneContactsData>> {
        return flow<List<PhoneContactsData>> {
            val cursor = context.contentResolver.query(
                ContactsContract.Contacts.CONTENT_URI,
                null,
                null,
                null,
                ContactsContract.Contacts.SORT_KEY_PRIMARY
            )
            val contactsData = arrayListOf<PhoneContactsData>()
            if (cursor != null) {
                while (cursor.moveToNext()) {
                    val idIndex = cursor.getColumnIndex(ContactsContract.Contacts._ID)
                    val nameIndex = cursor.getColumnIndex(ContactsContract.Contacts.DISPLAY_NAME)
                    if (idIndex != -1 && nameIndex != -1) {
                        val contactId = cursor.getString(idIndex)
                        val contactName = cursor.getString(nameIndex)
                        val phoneCursor = context.contentResolver.query(
                            ContactsContract.CommonDataKinds.Phone.CONTENT_URI,
                            null,
                            phoneContactsSelection,
                            arrayOf(contactId),
                            null
                        )
                        if (phoneCursor != null) {
                            val phoneIndex = phoneCursor.getColumnIndex(
                                ContactsContract.CommonDataKinds.Phone.NUMBER
                            )
                            if (phoneIndex != -1) {
                                val phonesList = arrayListOf<String>()
                                while (phoneCursor.moveToNext()) {
                                    val phone = phoneCursor.getString(phoneIndex)
                                    val newPhone = phone.replace(" ", "").replace("-", "")
                                    if (newPhone.matches(Regex("1\\d{10}"))) {
                                        phonesList.add(
                                            String.format(
                                                Locale.CHINA,
                                                "+86%s",
                                                newPhone
                                            )
                                        )
                                    }
                                }
                                if (phonesList.isNotEmpty()) {
                                    contactsData.add(PhoneContactsData(contactName, phonesList))
                                }
                            }
                            phoneCursor.close()
                        }
                    }
                }
                cursor.close()
            }
            emit(contactsData)
        }.map { data ->
            // remove own account
            val filterOwnAccountData = data.filterNot {
                it.phones.any { phone ->
                    phone == userSettingsController.settings.phoneNumber
                }
            }
            val allPhones = filterOwnAccountData.flatMap {
                it.phones
            }
            val loadUserFollowState =
                phoneContactsFollowStateDataSource.loadUserFollowState(allPhones)
            if (!loadUserFollowState.isNullOrEmpty()) {
                filterOwnAccountData.map { phoneContactsData ->
                    val findUserFollowSate = loadUserFollowState.find {
                        phoneContactsData.phones.any { phone -> phone == it.phone }
                    }
                    if (findUserFollowSate == null) {
                        phoneContactsData
                    } else {
                        phoneContactsData.copy(
                            followingState = getFollowState(
                                findUserFollowSate.followStatus
                            ),
                            headUrl = findUserFollowSate.headUrl,
                            suuntoName = findUserFollowSate.realName ?: "",
                            userName = findUserFollowSate.userName
                        )
                    }
                }
            } else {
                filterOwnAccountData
            }
        }.flowOn(Dispatchers.IO)
    }

    private fun getFollowState(followState: RemoteFollowState?): FollowingState {
        return when (followState) {
            RemoteFollowState.FOLLOWING -> FollowingState.Followed
            RemoteFollowState.PENDING, RemoteFollowState.REJECTED -> FollowingState.Requested
            RemoteFollowState.FRIENDS -> FollowingState.Friends
            else -> FollowingState.Unfollowed
        }
    }

    suspend fun handleFollowState(
        followingState: FollowingState,
        userName: String
    ): FollowingState {
        return when (followingState) {
            FollowingState.Followed, FollowingState.Friends -> {
                val followStatus =
                    phoneContactsFollowStateDataSource.unFollow(userName)?.followStatus
                getFollowState(followStatus)
            }

            FollowingState.Unfollowed, FollowingState.Requested -> {
                val followStatus = phoneContactsFollowStateDataSource.follow(userName)?.followStatus
                getFollowState(followStatus)
            }

            else -> {
                FollowingState.Unfollowed
            }
        }
    }
}
