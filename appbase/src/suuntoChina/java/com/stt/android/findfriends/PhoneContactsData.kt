package com.stt.android.findfriends

data class PhoneContactsData(
    val name: String,
    val phones: List<String>,
    val followingState: FollowingState = FollowingState.Unregistered,
    val suuntoName: String = "",
    val userName: String? = null,
    val headUrl: String? = null
) {
    fun followed() = followingState == FollowingState.Followed
}

sealed class FollowingState {
    object Followed : FollowingState()
    object Unfollowed : FollowingState()
    object Requested : FollowingState()
    object Unregistered : FollowingState()
    object Friends : FollowingState()
}
