package com.stt.android.findfriends.v2

import android.content.Context
import com.stt.android.R
import com.stt.android.findfriends.FollowingState
import com.stt.android.findfriends.PhoneContactsData
import com.stt.android.social.friends.Friend
import com.stt.android.social.friends.FriendStatus

fun PhoneContactsData.toFriend(context: Context): Friend {
    return Friend(
        username = userName ?: "",
        realName = name,
        profileDescription = if (suuntoName.isNotBlank()) {
            context.getString(R.string.suunto_username, suuntoName)
        } else {
            ""
        },
        profileImageUrl = headUrl ?: "",
        friendStatus = when (followingState) {
            FollowingState.Followed -> FriendStatus.FOLLOWING
            FollowingState.Friends -> FriendStatus.FRIEND
            else -> FriendStatus.FOLLOW
        },
    )
}

