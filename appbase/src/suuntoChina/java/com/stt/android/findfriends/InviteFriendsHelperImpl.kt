package com.stt.android.findfriends

import android.content.Context
import android.content.Intent
import com.stt.android.home.people.InviteFriendsHelper
import javax.inject.Inject

class InviteFriendsHelperImpl @Inject constructor() : InviteFriendsHelper {

    override fun showPhoneContact(): <PERSON>olean = true

    override fun toPhoneContactActivity(context: Context) {
        context.startActivity(Intent(context, PhoneContactsActivity::class.java))
    }

    override fun showUserName(): Boolean = true
}
