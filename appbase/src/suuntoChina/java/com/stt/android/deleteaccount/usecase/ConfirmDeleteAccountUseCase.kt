package com.stt.android.deleteaccount.usecase

import com.stt.android.data.deleteaccount.DeleteAccountRemoteDataSource
import com.stt.android.deleteaccount.DeleteAccountError
import com.stt.android.exceptions.BackendException
import com.stt.android.exceptions.remote.HttpException
import javax.inject.Inject

class ConfirmDeleteAccountUseCase @Inject constructor(
    private val deleteAccountRemoteDataSource: DeleteAccountRemoteDataSource
) {
    suspend operator fun invoke(token: String) {
        val deleted = deleteAccountRemoteDataSource.deleteAccount(token)
        if (!deleted) throw DeleteAccountError()
    }
}
