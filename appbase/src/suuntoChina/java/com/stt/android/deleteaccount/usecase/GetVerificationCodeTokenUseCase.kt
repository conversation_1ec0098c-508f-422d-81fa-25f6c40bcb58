package com.stt.android.deleteaccount.usecase

import com.stt.android.data.deleteaccount.DeleteAccountRemoteDataSource
import javax.inject.Inject

class GetVerificationCodeTokenUseCase @Inject constructor(
    private val deleteAccountRemoteDataSource: DeleteAccountRemoteDataSource
) {
    suspend operator fun invoke(phoneNumber: String, verificationCode: String): String {
        return deleteAccountRemoteDataSource.verifyVerificationCode(phoneNumber, verificationCode)
    }
}
