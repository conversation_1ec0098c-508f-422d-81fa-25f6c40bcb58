package com.stt.android.deleteaccount

import androidx.activity.compose.BackHandler
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.tooling.preview.Preview
import com.stt.android.R
import com.stt.android.compose.component.SuuntoIconButton
import com.stt.android.compose.modifiers.narrowContentWithBgColors
import com.stt.android.compose.newdesign.widgets.PrimaryButton
import com.stt.android.compose.newdesign.widgets.UnderlinedTextField
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.SuuntoIcons
import com.stt.android.compose.theme.material3.bodyBold
import com.stt.android.compose.theme.spacing

private const val VERIFICATION_CODE_LENGTH = 6

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DeleteAccountContent(
    phoneDisplay: String,
    deleteAccountState: DeleteAccountState,
    onBackClick: () -> Unit,
    onSendCodeClick: () -> Unit,
    onVerifyClick: () -> Unit,
    onInputVerificationCode: (String) -> Unit,
    onContactSupportClick: () -> Unit,
    modifier: Modifier = Modifier,
    snackbarHostState: SnackbarHostState = remember { SnackbarHostState() },
) {
    BackHandler {
        onBackClick()
    }

    val title = if (!deleteAccountState.verificationCodeSent) {
        stringResource(R.string.account_settings_delete_account_title)
    } else {
        stringResource(R.string.delete_account_enter_the_code)
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(text = title.uppercase())
                },
                navigationIcon = {
                    SuuntoIconButton(
                        icon = SuuntoIcons.ActionBack,
                        onClick = onBackClick,
                    )
                },
            )
        },
        snackbarHost = {
            SnackbarHost(hostState = snackbarHostState)
        },
        modifier = modifier,
    ) { paddingValues ->
        Surface(
            modifier = Modifier
                .padding(paddingValues)
                .narrowContentWithBgColors(
                    backgroundColor = MaterialTheme.colorScheme.surface,
                    outerBackgroundColor = MaterialTheme.colorScheme.background,
                )
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                modifier = Modifier
                    .fillMaxSize()
                    .verticalScroll(rememberScrollState())
                    .padding(vertical = MaterialTheme.spacing.medium),
            ) {

                if (!deleteAccountState.verificationCodeSent) {
                    DeleteAccountInitialContent(
                        phoneDisplay = phoneDisplay,
                        processing = deleteAccountState.backendProcessing,
                        onSendCodeClick = onSendCodeClick,
                    )
                } else {
                    DeleteAccountVerificationCodeSentContent(
                        phoneDisplay = phoneDisplay,
                        inputtedVerificationCode = deleteAccountState.inputtedVerificationCode,
                        countdownSeconds = deleteAccountState.remainSeconds,
                        actionsEnabled = !deleteAccountState.backendProcessing && !deleteAccountState.tooManyRequests,
                        onInputVerificationCode = onInputVerificationCode,
                        onVerifyClick = onVerifyClick,
                        onResendCodeClick = onSendCodeClick,
                        errorMessage = if (deleteAccountState.verificationCodeError) {
                            stringResource(R.string.delete_account_error_verification_code)
                        } else {
                            null
                        },
                    )
                }

                Spacer(
                    modifier = Modifier.weight(1f)
                )

                TextButton(
                    onClick = onContactSupportClick,
                    modifier = Modifier
                        .padding(vertical = MaterialTheme.spacing.large),
                ) {
                    Text(
                        text = stringResource(id = R.string.contact_support),
                        style = MaterialTheme.typography.bodyBold,
                        color = MaterialTheme.colorScheme.primary,
                    )
                }
            }
        }
    }
}

@Composable
private fun DeleteAccountInitialContent(
    phoneDisplay: String,
    processing: Boolean,
    onSendCodeClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier,
    ) {
        Text(
            text = buildStringWithPhone(
                stringResource(R.string.delete_account_description, phoneDisplay),
                phoneDisplay,
            ),
            style = MaterialTheme.typography.bodyLarge,
            color = MaterialTheme.colorScheme.onSurface,
            modifier = Modifier.padding(horizontal = MaterialTheme.spacing.medium),
        )

        PrimaryButton(
            enabled = !processing,
            onClick = onSendCodeClick,
            text = stringResource(R.string.delete_account_send_code),
            modifier = Modifier
                .fillMaxWidth()
                .padding(
                    horizontal = MaterialTheme.spacing.medium,
                    vertical = MaterialTheme.spacing.xxlarge,
                )
        )
    }
}

@Composable
private fun DeleteAccountVerificationCodeSentContent(
    phoneDisplay: String,
    inputtedVerificationCode: String,
    countdownSeconds: Int,
    actionsEnabled: Boolean,
    onInputVerificationCode: (String) -> Unit,
    onVerifyClick: () -> Unit,
    onResendCodeClick: () -> Unit,
    modifier: Modifier = Modifier,
    errorMessage: String? = null,
) {
    val localText = remember { mutableStateOf(TextFieldValue(inputtedVerificationCode)) }

    LaunchedEffect(inputtedVerificationCode) {
        if (inputtedVerificationCode != localText.value.text) {
            localText.value = TextFieldValue(inputtedVerificationCode)
        }
    }

    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = modifier,
    ) {
        Text(
            text = buildStringWithPhone(
                stringResource(R.string.delete_account_verification_code_description, phoneDisplay),
                phoneDisplay,
            ),
            style = MaterialTheme.typography.bodyLarge,
            color = MaterialTheme.colorScheme.onSurface,
            modifier = Modifier.padding(horizontal = MaterialTheme.spacing.medium),
        )

        UnderlinedTextField(
            label = stringResource(R.string.verification_code),
            value = localText.value.text,
            onValueChange = {
                localText.value = TextFieldValue(it)
                onInputVerificationCode(it)
            },
            modifier = Modifier.padding(top = MaterialTheme.spacing.large),
            enabled = actionsEnabled,
            errorTips = errorMessage,
            dividerHorizontalPadding = MaterialTheme.spacing.medium,
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
        )

        PrimaryButton(
            onClick = onVerifyClick,
            text = stringResource(R.string.verify_str),
            enabled = localText.value.text.length == VERIFICATION_CODE_LENGTH && errorMessage.isNullOrBlank() && actionsEnabled,
            modifier = Modifier
                .fillMaxWidth()
                .padding(
                    start = MaterialTheme.spacing.medium,
                    end = MaterialTheme.spacing.medium,
                    top = MaterialTheme.spacing.xlarge,
                    bottom = MaterialTheme.spacing.small,
                )
        )

        TextButton(
            enabled = countdownSeconds == 0 && actionsEnabled,
            onClick = onResendCodeClick,
        ) {
            Text(
                text = if (countdownSeconds > 0) {
                    stringResource(R.string.delete_account_resend_countdown, countdownSeconds)
                } else {
                    stringResource(R.string.delete_account_resend)
                }
            )
        }
    }
}

private fun buildStringWithPhone(text: String, phoneDisplay: String): AnnotatedString {
    return buildAnnotatedString {
        append(text)
        val startIndex = text.indexOf(phoneDisplay)
        addStyle(
            style = SpanStyle(fontWeight = FontWeight.Bold),
            startIndex,
            startIndex + phoneDisplay.length,
        )
    }
}

@Preview
@Composable
private fun DeleteAccountContentPreview() {
    M3AppTheme {
        DeleteAccountContent(
            phoneDisplay = "+86 ***********",
            deleteAccountState = DeleteAccountState(
                verificationCodeSent = false,
                remainSeconds = 0,
            ),
            onBackClick = {},
            onSendCodeClick = {},
            onInputVerificationCode = {},
            onVerifyClick = {},
            onContactSupportClick = {},
        )
    }
}

@Preview
@Composable
private fun DeleteAccountContentPreview2() {
    M3AppTheme {
        DeleteAccountContent(
            phoneDisplay = "+86 ***********",
            deleteAccountState = DeleteAccountState(
                verificationCodeSent = true,
                remainSeconds = 30,
                inputtedVerificationCode = "123429",
            ),
            onBackClick = {},
            onSendCodeClick = {},
            onInputVerificationCode = {},
            onVerifyClick = {},
            onContactSupportClick = {},
        )
    }
}
