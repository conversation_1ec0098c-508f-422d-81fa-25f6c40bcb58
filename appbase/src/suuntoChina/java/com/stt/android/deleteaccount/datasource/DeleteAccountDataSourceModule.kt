package com.stt.android.deleteaccount.datasource

import com.squareup.moshi.Moshi
import com.stt.android.remote.AuthProvider
import com.stt.android.remote.BaseUrl
import com.stt.android.remote.SharedOkHttpClient
import com.stt.android.remote.UserAgent
import com.stt.android.remote.deleteaccount.DeleteAccountRestApi
import com.stt.android.remote.di.BrandOkHttpConfigFactory
import com.stt.android.remote.di.RestApiFactory
import dagger.Module
import dagger.Provides
import okhttp3.OkHttpClient

@Module
abstract class DeleteAccountDataSourceModule {
    companion object {
        @Provides
        fun provideDeleteAccountRestApi(
            @SharedOkHttpClient sharedClient: OkHttpClient,
            @BaseUrl baseUrl: String,
            @UserAgent userAgent: String,
            authProvider: AuthProvider,
            moshi: Moshi
        ): DeleteAccountRestApi {
            return RestApiFactory.buildRestApi(
                sharedClient,
                baseUrl,
                DeleteAccountRestApi::class.java,
                BrandOkHttpConfigFactory.getStOkHttpConfig(authProvider, userAgent),
                moshi
            )
        }
    }
}
