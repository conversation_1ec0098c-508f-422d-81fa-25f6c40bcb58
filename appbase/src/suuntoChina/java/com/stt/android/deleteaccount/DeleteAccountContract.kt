package com.stt.android.deleteaccount

data class DeleteAccountState(
    val verificationCodeSent: <PERSON><PERSON>an,
    val remainSeconds: Int,
    val inputtedVerificationCode: String = "",
    val verificationCodeError: <PERSON>olean = false,
    val backendProcessing: <PERSON><PERSON><PERSON> = false,
    val tooManyRequests: <PERSON><PERSON><PERSON> = false,
)

sealed class DeleteAccountEvent {
    data object NetworkError : DeleteAccountEvent()
    data object TooManyRequestsError : DeleteAccountEvent()
    data object GeneralHttpError : DeleteAccountEvent()
    data class RequestConfirmDelete(val token: String) : DeleteAccountEvent()
}

class DeleteAccountError : Exception()
