package com.stt.android.di.search

import android.content.Context
import com.stt.android.maps.amap.search.AMapSearch
import com.stt.android.maps.search.SuuntoMapSearch
import dagger.Module
import dagger.Provides

@Module
abstract class MapSearchModule {

    companion object {
        @Provides
        fun provideSuuntoMapSearch(context: Context): SuuntoMapSearch {
            return AMapSearch(context)
        }
    }
}
