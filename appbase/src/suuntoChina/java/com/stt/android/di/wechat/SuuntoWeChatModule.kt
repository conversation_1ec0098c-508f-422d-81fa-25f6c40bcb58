package com.stt.android.di.wechat

import com.squareup.moshi.Moshi
import com.stt.android.WeChatDeepLinkHelper
import com.stt.android.domain.workouts.WeChatWorkoutDataSource
import com.stt.android.home.settings.connectedservices.WeChatConnectServiceHelper
import com.stt.android.remote.AuthProvider
import com.stt.android.remote.SharedOkHttpClient
import com.stt.android.remote.SuuntoCloudApiUrl
import com.stt.android.remote.UserAgent
import com.stt.android.remote.di.BrandOkHttpConfigFactory.getStOkHttpConfig
import com.stt.android.remote.di.RestApiFactory.buildRestApi
import com.stt.android.remote.wechat.WeChatSportRemoteAPIImpl
import com.stt.android.remote.wechat.WeChatSportRemoteApi
import com.stt.android.remote.wechat.WeChatSportRestAPI
import com.stt.android.watch.WeChatConnectionStateHelper
import com.stt.android.watch.background.WeChatDataSyncScheduler
import com.stt.android.wechat.WeChatConnectionStateHelperImpl
import com.stt.android.wechat.WeChatDeeplinkHelperImpl
import com.stt.android.wechat.sport.WeChatSportWeChatConnectServiceHelper
import com.stt.android.wechat.sport.data.WeChatSportDataSource
import com.stt.android.wechat.sport.data.WeChatSportDataSourceImpl
import com.stt.android.wechat.sync.WeChatDataSyncSchedulerImpl
import com.stt.android.workoudata.WeChatWorkoutDataSourceImpl
import dagger.Binds
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import okhttp3.OkHttpClient

@Module
@InstallIn(SingletonComponent::class)
abstract class SuuntoWeChatModule {
    companion object {

        @Provides
        fun provideWeChatSportRestAPI(
            @SharedOkHttpClient sharedClient: OkHttpClient,
            @SuuntoCloudApiUrl baseUrl: String,
            @UserAgent userAgent: String,
            authProvider: AuthProvider,
            moshi: Moshi
        ): WeChatSportRestAPI {
            return buildRestApi(
                sharedClient,
                baseUrl,
                WeChatSportRestAPI::class.java,
                getStOkHttpConfig(authProvider, userAgent),
                moshi
            )
        }
    }

    @Binds
    abstract fun bindWeChatSportRemoteAPI(weChatSportRemoteAPIImpl: WeChatSportRemoteAPIImpl): WeChatSportRemoteApi

    @Binds
    abstract fun bindWeChatSportHandConnectService(handleConnectServiceHelper: WeChatSportWeChatConnectServiceHelper): WeChatConnectServiceHelper

    @Binds
    abstract fun bindWeChatSportDatasource(weChatSportDataSourceImpl: WeChatSportDataSourceImpl): WeChatSportDataSource

    @Binds
    abstract fun bindWechatWorkoutDataSource(weChatWorkoutDataSourceImpl: WeChatWorkoutDataSourceImpl): WeChatWorkoutDataSource

    @Binds
    abstract fun bindWeChatDataSyncScheduler(weChatDataSyncSchedulerImpl: WeChatDataSyncSchedulerImpl): WeChatDataSyncScheduler

    @Binds
    abstract fun bindWeChatConnectionStateHelper(weChatConnectionStateHelperImpl: WeChatConnectionStateHelperImpl): WeChatConnectionStateHelper

    @Binds
    abstract fun bindWeChatDeepLinkHelper(weChatDeeplinkHelperImpl: WeChatDeeplinkHelperImpl): WeChatDeepLinkHelper
}
