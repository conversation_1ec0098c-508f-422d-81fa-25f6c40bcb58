package com.stt.android.di.inbox

import com.stt.android.annualreport.InboxMessageHandlerImpl
import com.stt.android.social.notifications.InboxMessageHandler
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@Module
@InstallIn(SingletonComponent::class)
abstract class InboxMessageHandlerModule {
    @Binds
    abstract fun bindInboxMessageHandler(inboxMessageHandlerImpl: InboxMessageHandlerImpl): InboxMessageHandler
}
