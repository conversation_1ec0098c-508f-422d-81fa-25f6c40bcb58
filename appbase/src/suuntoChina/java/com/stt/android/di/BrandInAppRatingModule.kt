package com.stt.android.di

import com.stt.android.inappreview.DummyInAppReviewTrigger
import com.stt.android.inappreview.InAppReviewTrigger
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@Module
@InstallIn(SingletonComponent::class)
abstract class BrandInAppRatingModule {
    @Binds
    abstract fun bindInAppReviewTrigger(inAppReviewTrigger: DummyInAppReviewTrigger): InAppReviewTrigger
}
