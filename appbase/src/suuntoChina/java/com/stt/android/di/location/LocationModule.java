package com.stt.android.di.location;

import android.content.Context;
import android.location.LocationManager;
import com.stt.android.maps.location.AndroidLocationSource;
import com.stt.android.maps.location.SuuntoLocationSource;
import dagger.Module;
import dagger.Provides;

@Module
public abstract class LocationModule {

    @Provides
    public static SuuntoLocationSource provideSuuntoLocationSource(Context context) {
        return new AndroidLocationSource(
            (LocationManager) context.getSystemService(Context.LOCATION_SERVICE));
    }
}
