package com.stt.android.di.activitydata

import com.stt.android.data.Local
import com.stt.android.data.Remote
import com.stt.android.data.activitydata.dailyvalues.ActivityDataDailyDataSource
import com.stt.android.data.activitydata.dailyvalues.ActivityDataDailyLocalDataSource
import com.stt.android.data.activitydata.dailyvalues.ActivityDataDailyRemoteDataSource
import com.stt.android.data.activitydata.goals.ActivityDataGoalDataSource
import com.stt.android.data.activitydata.goals.ActivityDataGoalLocalDataSource
import com.stt.android.data.activitydata.goals.ActivityDataGoalRemoteDataSource
import com.stt.android.data.activitydata.logout.ActivityDataHelper
import com.stt.android.data.activitydata.logout.ActivityDataHelperSyncAndDelete
import dagger.Binds
import dagger.Module

@Module
abstract class ActivityDataModule {

    @Binds
    @Local
    abstract fun bindActivityDataGoalLocalDataSource(
        activityDataGoalLocalDataSource: ActivityDataGoalLocalDataSource
    ): ActivityDataGoalDataSource

    @Binds
    @Remote
    abstract fun bindActivityDataGoalRemoteDataSource(
        activityDataGoalRemoteDataSource: ActivityDataGoalRemoteDataSource
    ): ActivityDataGoalDataSource

    @Binds
    @Local
    abstract fun bindActivityDataDailyLocalDataSource(
        activityDataDailyLocalDataSource: ActivityDataDailyLocalDataSource
    ): ActivityDataDailyDataSource

    @Binds
    @Remote
    abstract fun bindActivityDataDailyRemoteDataSource(
        activityDataDailyRemoteDataSource: ActivityDataDailyRemoteDataSource
    ): ActivityDataDailyDataSource

    @Binds
    abstract fun bindActivityDataHelper(
        activityDataHelperSyncAndDelete: ActivityDataHelperSyncAndDelete
    ): ActivityDataHelper
}
