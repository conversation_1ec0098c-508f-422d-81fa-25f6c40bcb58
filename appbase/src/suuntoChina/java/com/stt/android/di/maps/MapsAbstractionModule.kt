package com.stt.android.di.maps

import com.stt.android.maps.MapsProvider
import com.stt.android.maps.SuuntoMaps
import com.stt.android.maps.amap.AMapsProvider
import com.stt.android.maps.amap.AMapsProviderOptions
import dagger.Binds
import dagger.Module
import dagger.Provides
import dagger.multibindings.IntoSet
import javax.inject.Singleton

@Module(includes = [MapboxMapsAbstractionModule::class])
abstract class MapsAbstractionModule {

    @Binds
    @IntoSet
    abstract fun bindAMapsProvider(amapsProvider: AMapsProvider): MapsProvider

    companion object {

        @Provides
        fun provideAMapsOptions(): AMapsProviderOptions {
            return AMapsProviderOptions(emptyMap())
        }

        @Provides
        @Singleton
        fun provideSuuntoMaps(
            mapsProviders: Set<@JvmSuppressWildcards MapsProvider>
        ): SuuntoMaps {
            for (mapsProvider in mapsProviders) {
                SuuntoMaps.addProvider(mapsProvider)
            }
            SuuntoMaps.setDefaultProvider(AMapsProvider.NAME)
            return SuuntoMaps
        }
    }
}
