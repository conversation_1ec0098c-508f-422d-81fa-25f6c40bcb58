package com.stt.android.home.settings.accountsettings

import android.app.Activity
import android.os.Bundle
import androidx.activity.result.contract.ActivityResultContracts
import com.stt.android.common.ui.observeK
import com.stt.android.deleteaccount.DeleteAccountActivity
import com.stt.android.newemail.NewEmailActivity
import com.stt.android.newphone.NewPhoneChinaActivity
import com.stt.android.resetpassword.ResetPasswordChinaActivity
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class AccountSettingsChinaFragment : AccountSettingsFragment() {

    private val finishWithResultOkAfterChangeEmailListener =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
            if (result.resultCode == Activity.RESULT_OK) {
                viewModel.changeEmailSuccess()
            }
        }

    private val finishWithResultOkAfterChangePhoneListener =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
            if (result.resultCode == Activity.RESULT_OK) {
                viewModel.changePhoneSuccess()
            }
        }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        viewModel.onChangePhoneClicked.observeK(this) {
            activity?.apply {
                finishWithResultOkAfterChangePhoneListener.launch(NewPhoneChinaActivity.newStartIntent(this))
            }
        }
    }

    override fun resetPasswordOnClicked() {
        activity?.apply {
            startActivity(
                ResetPasswordChinaActivity.newStartIntent(this)
            )
        }
    }

    override fun deleteAccountOnClicked() {
        activity?.apply {
            startActivity(
                DeleteAccountActivity.newStartIntent(this)
            )
        }
    }

    override fun changeEmailOnClicked(email: String?) {
        activity?.apply {
            finishWithResultOkAfterChangeEmailListener.launch(NewEmailActivity.newStartIntent(this))
        }
    }
}
