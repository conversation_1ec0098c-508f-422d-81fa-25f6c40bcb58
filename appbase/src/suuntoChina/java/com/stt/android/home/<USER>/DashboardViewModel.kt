package com.stt.android.home.dashboardv2

import android.content.SharedPreferences
import androidx.lifecycle.viewModelScope
import com.stt.android.analytics.EmarsysAnalytics
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.FeedController
import com.stt.android.di.ExploreMapPreferences
import com.stt.android.domain.firstpairing.FirstPairingInfoUseCase
import com.stt.android.domain.terms.NeedAcceptTermsUseCase
import com.stt.android.home.dashboardv2.repository.DashboardConfigRepository
import com.stt.android.models.MapSelectionModel
import com.stt.android.refreshable.Refreshables
import com.stt.android.social.notifications.list.EmarsysInboxItem
import com.stt.android.social.notifications.list.EmarsysInboxSource
import com.stt.android.social.notifications.list.EmarsysInboxType
import com.stt.android.watch.forcedupdate.ShouldShowOnboardingUseCase
import com.stt.android.workouts.RecordWorkoutModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
internal class DashboardViewModel @Inject constructor(
    refreshables: Refreshables,
    dashboardConfigRepository: DashboardConfigRepository,
    currentUserController: CurrentUserController,
    emarsysAnalytics: EmarsysAnalytics,
    firstPairingInfoUseCase: FirstPairingInfoUseCase,
    shouldShowOnboardingUseCase: ShouldShowOnboardingUseCase,
    recordWorkoutModel: RecordWorkoutModel,
    mapSelectionModel: MapSelectionModel,
    @ExploreMapPreferences exploreMapPreferences: SharedPreferences,
    private val feedController: FeedController,
    private val coroutinesDispatchers: CoroutinesDispatchers,
    needAcceptTermsUseCase: NeedAcceptTermsUseCase,
) : BaseSuuntoDashboardViewModel(
    refreshables = refreshables,
    dashboardConfigRepository = dashboardConfigRepository,
    currentUserController = currentUserController,
    emarsysAnalytics = emarsysAnalytics,
    emarsysAppName = "app_china",
    firstPairingInfoUseCase = firstPairingInfoUseCase,
    shouldShowOnboardingUseCase = shouldShowOnboardingUseCase,
    coroutinesDispatchers = coroutinesDispatchers,
    recordWorkoutModel = recordWorkoutModel,
    mapSelectionModel = mapSelectionModel,
    exploreMapPreferences = exploreMapPreferences,
    needAcceptTermsUseCase = needAcceptTermsUseCase,
) {
    private val _showLocalAnnualReportFlow: MutableSharedFlow<EmarsysInboxItem> =
        MutableSharedFlow()
    val showLocalAnnualReportFlow: SharedFlow<EmarsysInboxItem> =
        _showLocalAnnualReportFlow.asSharedFlow()

    private var checkAnnalReportJob: Job? = null

    fun checkIfLocalAnnualReportIsNeeded() {
        checkAnnalReportJob?.cancel()
        checkAnnalReportJob = viewModelScope.launch(coroutinesDispatchers.io) {
            feedController.emarsysInboxItems
                .map { items ->
                    val hasEmarsysAnnualReport = items.any {
                        it.messageType == EmarsysInboxType.ANNUAL_REPORT && it.source == EmarsysInboxSource.EMARSYS
                    }
                    if (!hasEmarsysAnnualReport) {
                        items.firstOrNull {
                            it.messageType == EmarsysInboxType.ANNUAL_REPORT && it.source == EmarsysInboxSource.SUUNTO
                        }
                    } else null
                }
                .filterNotNull()
                .collect {
                    _showLocalAnnualReportFlow.emit(it)
                }
        }
    }
}
