package com.stt.android.workoutsettings

import android.Manifest
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.provider.Settings
import android.view.View
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.annotation.RequiresApi
import androidx.core.content.ContextCompat
import com.stt.android.R
import com.stt.android.common.ui.SimpleDialogFragment
import com.stt.android.common.ui.SimpleDialogFragment.Companion.newInstance
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber

@AndroidEntryPoint
internal class WorkoutSettingsFragment : BaseWorkoutSettingsFragment() {
    private var requestPermissionLauncher: ActivityResultLauncher<String>? = null
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        requestPermissionLauncher = registerForActivityResult(
            ActivityResultContracts.RequestPermission()
        ) { isGranted: Boolean? ->
            // reject and don't ask again. jump to system setting
            if (!isGranted!! && !shouldShowRequestPermissionRationale(Manifest.permission.ACTIVITY_RECOGNITION)) {
                openPermissionSetting()
            }
            Timber.d("ACTIVITY_RECOGNITION permission request result:%s", isGranted)
        }
        checkStepsPermission()
    }

    private fun openPermissionSetting() {
        val intent = Intent()
        intent.data = Uri.fromParts("package", requireActivity().packageName, null)
        intent.action = Settings.ACTION_APPLICATION_DETAILS_SETTINGS
        try {
            startActivity(intent)
        } catch (e: Exception) {
            Timber.w(e, "Could not open application default settings")
        }
    }

    private fun checkStepsPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            if (ContextCompat.checkSelfPermission(
                    requireContext(),
                    Manifest.permission.ACTIVITY_RECOGNITION
                ) == PackageManager.PERMISSION_DENIED
            ) {
                showStepsPermissionUseExplanation()
            }
        }
    }

    private fun showStepsPermissionUseExplanation() {
        if (parentFragmentManager.findFragmentByTag(ACTIVITY_RECOGNITION_PERMISSION_EXPLANATION_DIALOG_FRAGMENT_TAG) == null) {
            val explanationDialog = newInstance(
                getString(R.string.recognition_permission_explanation_content),
                getString(R.string.recognition_permission_explanation_title),
                getString(R.string.allow),
                getString(R.string.cancel),
                false
            )
            explanationDialog.setTargetFragment(
                this,
                ACTIVITY_RECOGNITION_PERMISSION_EXPLANATION_DIALOG_REQUEST_CODE
            )
            explanationDialog.show(
                parentFragmentManager,
                ACTIVITY_RECOGNITION_PERMISSION_EXPLANATION_DIALOG_FRAGMENT_TAG
            )
        }
    }

    @RequiresApi(api = Build.VERSION_CODES.Q)
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == ACTIVITY_RECOGNITION_PERMISSION_EXPLANATION_DIALOG_REQUEST_CODE) {
            if (resultCode == SimpleDialogFragment.RESULT_POSITIVE) {
                requestPermissionLauncher?.launch(Manifest.permission.ACTIVITY_RECOGNITION)
            }
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        requestPermissionLauncher?.unregister()
        parentFragmentManager.findFragmentByTag(ACTIVITY_RECOGNITION_PERMISSION_EXPLANATION_DIALOG_FRAGMENT_TAG)?.let {
            (it as? SimpleDialogFragment)?.dismiss()
        }
    }

    companion object {

        fun newInstance(): WorkoutSettingsFragment {
            return WorkoutSettingsFragment()
        }

        private const val ACTIVITY_RECOGNITION_PERMISSION_EXPLANATION_DIALOG_REQUEST_CODE = 1110
        private const val ACTIVITY_RECOGNITION_PERMISSION_EXPLANATION_DIALOG_FRAGMENT_TAG =
            "com.stt.android.workoutsettings.WorkoutSettingsFragment.PERMISSION_EXPLANATION_DIALOG"
    }
}
