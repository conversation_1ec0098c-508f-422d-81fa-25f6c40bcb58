package com.stt.android.resetpassword

import javax.inject.Inject

class ResetPasswordRepository @Inject constructor(private val resetPasswordDataSource: ResetPasswordDataSource) {
    suspend fun sendVerificationCodeForResetPassword(phoneNumber: String) {
        resetPasswordDataSource.sendVerificationCodeForResetPassword(phoneNumber)
    }

    suspend fun checkVerificationCodeForResetPassword(
        phoneNumber: String,
        pin: String
    ): String {
        return resetPasswordDataSource.checkVerificationCodeForResetPassword(phoneNumber, pin)
    }

    suspend fun changePassword(
        phoneNumberVerificationToken: String,
        phoneNumber: String,
        newPassword: String
    ): Bo<PERSON>an {
        return resetPasswordDataSource.changePassword(
            phoneNumberVerificationToken,
            phoneNumber,
            newPassword
        )
    }
}
