package com.stt.android.resetpassword

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.material.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import com.stt.android.R
import com.stt.android.compose.modifiers.clickableThrottleFirst
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.body
import com.stt.android.compose.theme.bodyBold
import com.stt.android.compose.theme.bodyXLargeBold
import com.stt.android.compose.theme.cloudyGrey
import com.stt.android.compose.theme.darkGrey
import com.stt.android.compose.theme.nearBlack
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.widgets.COUNT_DOWN_SECONDS
import com.stt.android.compose.widgets.ComposeCountDownTimer
import com.stt.android.compose.widgets.LoadingContent
import com.stt.android.compose.widgets.PrimaryButton
import com.stt.android.compose.widgets.TextFieldInputWithError
import com.stt.android.home.settings.PhoneNumberUtil
import com.stt.android.home.settings.PhoneNumberUtil.PHONE_REGION_CHINA
import com.stt.android.newemail.VERIFICATION_CODE_LENGTH

@Preview
@Composable
private fun CheckVerifyCodeBodyPreview() {
    AppTheme {
        Surface {
            CheckVerifyCodeBody(
                isLoading = false,
                phoneNumber = "",
                verifyCode = "",
                verifyCodeIsExpired = false,
                btnEnabled = false,
                onBtnClicked = {},
                onInputVerifyCode = {},
                onResendCallback = {},
                onContactClick = {},
            )
        }
    }
}

@Composable
fun CheckVerifyCodeBody(
    isLoading: Boolean,
    phoneNumber: String,
    verifyCode: String,
    verifyCodeIsExpired: Boolean,
    btnEnabled: Boolean,
    onBtnClicked: () -> Unit,
    onInputVerifyCode: (String) -> Unit,
    onResendCallback: () -> Unit,
    onContactClick: () -> Unit,
    modifier: Modifier = Modifier,
    withRegionCode: Boolean = true,
    onActionDone: (() -> Unit)? = null,
) {
    var timeLeft by rememberSaveable { mutableIntStateOf(COUNT_DOWN_SECONDS) }
    var resendEnable by rememberSaveable { mutableStateOf(false) }
    if (!resendEnable) {
        ComposeCountDownTimer(countDownSeconds = timeLeft, onTick = {
            timeLeft = it
        }, onDone = {
            timeLeft = 0
            resendEnable = true
        })
    }
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Top,
        modifier = modifier
            .fillMaxSize()
            .verticalScroll(rememberScrollState())
    ) {
        Spacer(modifier = Modifier.height(height = MaterialTheme.spacing.xlarge))

        Text(
            text = stringResource(id = R.string.enter_the_code_sent_by_sms),
            style = MaterialTheme.typography.bodyXLargeBold,
            textAlign = TextAlign.Center,
            color = MaterialTheme.colors.nearBlack,
        )

        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(
                    top = MaterialTheme.spacing.medium,
                    bottom = MaterialTheme.spacing.large
                ),
            horizontalArrangement = Arrangement.Center,
        ) {
            val regionCode = if (withRegionCode) PHONE_REGION_CHINA else ""
            val phoneNumberString = PhoneNumberUtil.maskPhoneNumberWithRegionCode("$regionCode$phoneNumber")
            val checkVerificationCodeSummary =
                stringResource(id = R.string.check_verification_code_summary_from_phone_new, phoneNumberString)
            val startIndex = checkVerificationCodeSummary.indexOf(phoneNumberString)
            val text = buildAnnotatedString {
                append(checkVerificationCodeSummary)
                addStyle(
                    style = SpanStyle(fontWeight = FontWeight.Bold),
                    startIndex,
                    startIndex + phoneNumberString.length
                )
            }
            Text(
                text = text,
                style = MaterialTheme.typography.body,
                textAlign = TextAlign.Center,
                color = MaterialTheme.colors.nearBlack,
            )
        }

        val errorMessage = when {
            verifyCodeIsExpired -> stringResource(id = R.string.verification_code_has_expired)
            else -> ""
        }
        TextFieldInputWithError(
            currentText = verifyCode,
            placeholderText = stringResource(id = R.string.verification_code),
            onChanged = {
                if (it.length <= VERIFICATION_CODE_LENGTH) {
                    onInputVerifyCode.invoke(it)
                }
            },
            onActionDone = onActionDone,
            errorMessage = errorMessage,
            keyboardType = KeyboardType.Number,
        )

        PrimaryButton(
            enabled = btnEnabled,
            onClick = {
                onBtnClicked.invoke()
            },
            text = stringResource(R.string.verify_str).uppercase(),
            modifier = Modifier
                .fillMaxWidth()
                .padding(
                    start = MaterialTheme.spacing.medium,
                    top = MaterialTheme.spacing.xlarge,
                    bottom = MaterialTheme.spacing.large,
                    end = MaterialTheme.spacing.medium,
                )
        )

        Text(
            text = stringResource(id = R.string.did_not_receive_code),
            style = MaterialTheme.typography.body,
            textAlign = TextAlign.Center,
            color = MaterialTheme.colors.nearBlack,
        )

        TextButton(
            enabled = resendEnable,
            onClick = {
                resendEnable = false
                timeLeft = COUNT_DOWN_SECONDS
                onResendCallback.invoke()
            },
            modifier = Modifier
                .padding(
                    horizontal = MaterialTheme.spacing.medium,
                    vertical = MaterialTheme.spacing.xlarge
                )
        ) {
            Text(
                text = stringResource(
                    id = R.string.resend_verification_code,
                    if (timeLeft <= 0) "" else "($timeLeft)"
                ),
                style = MaterialTheme.typography.bodyBold,
                textAlign = TextAlign.Center,
                color = if (resendEnable) MaterialTheme.colors.primary else MaterialTheme.colors.cloudyGrey,
            )
        }

        Spacer(
            modifier = Modifier
                .fillMaxWidth()
                .weight(1f)
        )

        TextButton(
            onClick = onContactClick,
            modifier = Modifier
                .padding(vertical = MaterialTheme.spacing.xxxxlarge)
        ) {
            Text(
                text = stringResource(id = R.string.contact_support),
                style = MaterialTheme.typography.bodyBold,
                textAlign = TextAlign.Center,
                color = MaterialTheme.colors.primary,
            )
        }
    }
    LoadingContent(isLoading)
}
