package com.stt.android.resetpassword

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.navArgs
import com.google.android.material.snackbar.Snackbar
import com.stt.android.R
import com.stt.android.compose.base.BaseFragment
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.bodyXLargeBold
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.widgets.LoadingContent
import com.stt.android.compose.widgets.PrimaryButton
import com.stt.android.compose.widgets.TextFieldInputWithError
import com.stt.android.launcher.BaseProxyActivity
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber

@AndroidEntryPoint
class FragmentResetPassword : BaseFragment() {

    private val viewModel: FragmentResetPasswordViewModel by viewModels()
    private val resetPasswordArgs: FragmentResetPasswordArgs by navArgs()

    @Composable
    override fun SetContentView() {
        ResetPasswordScreen()
    }

    @Composable
    private fun ResetPasswordScreen() {
        val uiState = viewModel.uiState
        val commonUIState by viewModel.commonUIState.collectAsState()
        LaunchedEffect(uiState.resetPasswordResult) {
            uiState.resetPasswordResult?.let {
                Timber.d("resetPasswordResult: $it")
                Snackbar.make(
                    requireView(),
                    R.string.change_successful,
                    Snackbar.LENGTH_LONG
                ).apply {
                    show()
                    addCallback(object : Snackbar.Callback() {
                        override fun onDismissed(transientBottomBar: Snackbar?, event: Int) {
                            activity?.apply {
                                startActivity(BaseProxyActivity.newStartIntentClearStack(this))
                                finish()
                            }
                        }
                    })
                }
            }
        }

        BaseContentBody(
            viewModel = viewModel,
            onBackClick = {
                activity?.finish()
            }
        ) {
            ContentBody(
                uiState.newPassword,
                uiState.confirmPassword,
                uiState.setPasswordError,
                commonUIState.isLoading,
                onInputNewPassword = {
                    viewModel.inputNewPassword(it)
                },
                onInputConfirmPassword = {
                    viewModel.inputConfirmPassword(it)
                },
                onResetPassword = { newPassword, confirmPassword ->
                    viewModel.resetPassword(
                        newPassword,
                        confirmPassword,
                        resetPasswordArgs.token
                    )
                }
            )
        }
    }
}

@Preview
@Composable
private fun ResetPasswordScreenPreview() {
    AppTheme {
        Surface {
            ContentBody()
        }
    }
}

@OptIn(ExperimentalComposeUiApi::class)
@Composable
private fun ContentBody(
    newPassword: String = "",
    confirmPassword: String = "",
    setPasswordError: SetPasswordError? = null,
    isLoading: Boolean = false,
    onInputNewPassword: (String) -> Unit = {},
    onInputConfirmPassword: (String) -> Unit = {},
    onResetPassword: (String, String) -> Unit = { _, _ -> },
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Top,
        modifier = Modifier
            .fillMaxSize()
            .padding(
                vertical = MaterialTheme.spacing.medium
            )
            .verticalScroll(rememberScrollState())
    ) {
        Text(
            stringResource(
                id = R.string.reset_password_title
            ),
            style = MaterialTheme.typography.bodyXLargeBold,
            textAlign = TextAlign.Center,
        )
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(
                    top = MaterialTheme.spacing.xlarge
                ),
            horizontalArrangement = Arrangement.Center,
        ) {
            TextFieldInputWithError(
                currentText = newPassword,
                placeholderText = stringResource(id = R.string.input_new_password),
                onChanged = { onInputNewPassword.invoke(it) },
                keyboardType = KeyboardType.Password,
                isPassword = true
            )
        }

        TextFieldInputWithError(
            currentText = confirmPassword,
            placeholderText = stringResource(id = R.string.input_password_again),
            onChanged = { onInputConfirmPassword.invoke(it) },
            keyboardType = KeyboardType.Password,
            errorMessage = setPasswordError?.let { stringResource(id = it.resId) } ?: "",
            tipsText = stringResource(id = R.string.input_password_tips),
            isPassword = true
        )
        val keyboardController = LocalSoftwareKeyboardController.current
        PrimaryButton(
            enabled = newPassword.isNotEmpty() && confirmPassword.isNotEmpty(),
            onClick = {
                onResetPassword.invoke(newPassword, confirmPassword)
                keyboardController?.hide()
            },
            text = stringResource(R.string.ok),
            modifier = Modifier
                .fillMaxWidth()
                .padding(
                    horizontal = MaterialTheme.spacing.medium,
                    vertical = MaterialTheme.spacing.large
                )
        )
    }
    LoadingContent(isLoading)
}
