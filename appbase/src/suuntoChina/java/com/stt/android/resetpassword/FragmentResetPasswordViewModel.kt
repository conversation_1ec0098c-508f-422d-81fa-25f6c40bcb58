package com.stt.android.resetpassword

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.viewModelScope
import com.stt.android.base.BaseHandlerViewModel
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.UserSettingsController
import com.stt.android.exceptions.remote.STTError
import com.stt.android.ui.tasks.LogoutTask
import dagger.hilt.android.lifecycle.HiltViewModel
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.rxkotlin.addTo
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class FragmentResetPasswordViewModel @Inject constructor(
    private val resetPasswordRepository: ResetPasswordRepository,
    private val dispatchers: CoroutinesDispatchers,
    private val logoutTask: LogoutTask,
    private val userSettingsController: UserSettingsController
) : BaseHandlerViewModel() {
    var uiState by mutableStateOf(ResetPasswordUIState())
    private val disposable = CompositeDisposable()

    fun inputNewPassword(newPassword: String) {
        uiState = uiState.copy(newPassword = newPassword, setPasswordError = null)
    }

    fun inputConfirmPassword(confirmPassword: String) {
        uiState = uiState.copy(confirmPassword = confirmPassword, setPasswordError = null)
    }

    fun resetPassword(newPassword: String, confirmPassword: String, token: String) {
        checkNewPassword(newPassword, confirmPassword) {
            loadingStart()
            viewModelScope.launch(
                dispatchers.io + getChangePasswordFailedHandler {
                    uiState = uiState.copy(setPasswordError = SetPasswordError.InvalidPassword)
                }
            ) {
                val result = resetPasswordRepository.changePassword(
                    token,
                    userSettingsController.settings.phoneNumber,
                    newPassword
                )
                if (result) {
                    logoutTask
                        .logout()
                        .subscribe {
                            loadingEnd()
                            uiState = uiState.copy(resetPasswordResult = result)
                        }.addTo(disposable)
                } else {
                    throw STTError.ModifyPasswordFailed()
                }
            }
        }
    }

    private fun checkNewPassword(newPassword: String, confirmPassword: String, checkSuccessCallback: () -> Unit) {
        when (newPassword) {
            confirmPassword -> {
                checkSuccessCallback.invoke()
            }
            else -> {
                uiState = uiState.copy(setPasswordError = SetPasswordError.TwoPasswordIsInconsistent)
            }
        }
    }

    override fun onCleared() {
        super.onCleared()
        disposable.clear()
    }
}

data class ResetPasswordUIState(
    val newPassword: String = "",
    val confirmPassword: String = "",
    val setPasswordError: SetPasswordError? = null,
    val resetPasswordResult: Boolean? = null,
)
