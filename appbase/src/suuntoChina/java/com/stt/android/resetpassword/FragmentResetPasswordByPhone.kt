package com.stt.android.resetpassword

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import com.stt.android.R
import com.stt.android.compose.base.BaseFragment
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.bodyLarge
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.widgets.LoadingContent
import com.stt.android.compose.widgets.PrimaryButton
import com.stt.android.controllers.UserSettingsController
import com.stt.android.home.settings.PhoneNumberUtil
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber
import javax.inject.Inject

@AndroidEntryPoint
class FragmentResetPasswordByPhone : BaseFragment() {

    private val viewModel: FragmentResetPasswordByPhoneViewModel by viewModels()

    @Inject
    lateinit var userSettingsController: UserSettingsController

    @Composable
    override fun SetContentView() {
        ResetPasswordByPhoneScreen()
    }

    @Composable
    private fun ResetPasswordByPhoneScreen() {
        val uiState = viewModel.uiState
        val commonUIState by viewModel.commonUIState.collectAsState()
        LaunchedEffect(uiState.resetPasswordByPhoneResult) {
            uiState.resetPasswordByPhoneResult?.let {
                Timber.d("resetPasswordByPhoneResult: $it")
                findNavController().navigate(
                    FragmentResetPasswordByPhoneDirections.actionResetPasswordByPhoneVerifyCode()
                )
                viewModel.clearResetPasswordByPhoneResult()
            }
        }

        BaseContentBody(
            viewModel = viewModel,
            onBackClick = {
                activity?.finish()
            }
        ) {
            ContentBody(
                commonUIState.isLoading,
                userSettingsController.settings.phoneNumber,
                onSendVerificationCode = {
                    viewModel.sendVerificationCode(userSettingsController.settings.phoneNumber)
                }
            )
        }
    }
}

@Preview
@Composable
private fun ResetPasswordByPhoneScreenPreview() {
    AppTheme {
        Surface {
            ContentBody()
        }
    }
}

@Composable
private fun ContentBody(
    isLoading: Boolean = false,
    phoneNumber: String = "",
    onSendVerificationCode: () -> Unit = {}
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Top,
        modifier = Modifier
            .fillMaxSize()
            .padding(
                horizontal = MaterialTheme.spacing.medium,
            )
            .verticalScroll(rememberScrollState())
    ) {
        Spacer(modifier = Modifier.height(MaterialTheme.spacing.xlarge))
        Text(
            stringResource(
                R.string.change_password_by_phone_summary,
                PhoneNumberUtil.maskPhoneNumberWithRegionCode(phoneNumber)
            ),
            style = MaterialTheme.typography.bodyLarge,
            textAlign = TextAlign.Center,
        )

        PrimaryButton(
            onClick = {
                onSendVerificationCode.invoke()
            },
            text = stringResource(R.string.account_settings_reset_password_title),
            modifier = Modifier
                .fillMaxWidth()
                .padding(
                    horizontal = MaterialTheme.spacing.medium,
                    vertical = MaterialTheme.spacing.xlarge
                )
        )
    }
    LoadingContent(isLoading)
}
