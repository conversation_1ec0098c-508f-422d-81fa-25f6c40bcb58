package com.stt.android.resetpassword

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.viewModelScope
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.compose.base.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

@HiltViewModel
class FragmentResetPasswordByPhoneViewModel @Inject constructor(
    private val resetPasswordRepository: ResetPasswordRepository,
    private val dispatchers: CoroutinesDispatchers,
) : BaseViewModel() {

    var uiState by mutableStateOf(ResetPasswordByPhoneUIState())

    fun sendVerificationCode(phoneNumber: String) {
        loadingStart()
        viewModelScope.launch(dispatchers.io) {
            kotlin.runCatching {
                resetPasswordRepository.sendVerificationCodeForResetPassword(phoneNumber)
                uiState = uiState.copy(resetPasswordByPhoneResult = true)
                loadingEnd()
            }.onFailure {
                loadingEndWithFail(it)
                Timber.e(it, "sendVerificationCode error: ${it.message}")
            }
        }
    }

    fun clearResetPasswordByPhoneResult() {
        uiState = uiState.copy(resetPasswordByPhoneResult = null)
    }
}

data class ResetPasswordByPhoneUIState(
    val resetPasswordByPhoneResult: Boolean? = null,
)
