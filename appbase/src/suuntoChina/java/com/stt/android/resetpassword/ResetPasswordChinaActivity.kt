package com.stt.android.resetpassword

import android.content.Context
import android.content.Intent
import com.stt.android.R
import com.stt.android.newemail.BaseComposeActivity
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class ResetPasswordChinaActivity : BaseComposeActivity() {

    companion object {
        fun newStartIntent(context: Context): Intent {
            return Intent(context, ResetPasswordChinaActivity::class.java)
        }
    }

    override fun getNavGraph(): Int {
        return R.navigation.reset_password_nav_graph
    }
}
