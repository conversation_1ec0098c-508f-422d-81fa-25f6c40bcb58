package com.stt.android.resetpassword

import com.squareup.moshi.Moshi
import com.stt.android.remote.AuthProvider
import com.stt.android.remote.BaseUrl
import com.stt.android.remote.SharedOkHttpClient
import com.stt.android.remote.UserAgent
import com.stt.android.remote.di.BrandOkHttpConfigFactory
import com.stt.android.remote.di.RestApiFactory
import dagger.Binds
import dagger.Module
import dagger.Provides
import okhttp3.OkHttpClient

@Module
abstract class ResetPasswordModule {
    @Binds
    abstract fun bindResetPasswordRemoteDataSource(
        resetPasswordRemoteDataSource: ResetPasswordRemoteDataSource
    ): ResetPasswordDataSource

    companion object {

        @Provides
        fun provideResetPassword(
            @SharedOkHttpClient sharedClient: OkHttpClient,
            @BaseUrl baseUrl: String,
            @UserAgent userAgent: String,
            authProvider: AuthProvider,
            moshi: <PERSON><PERSON>
        ): ResetPasswordByPhoneApi {
            return RestApiFactory.buildRestApi(
                sharedClient,
                baseUrl,
                ResetPasswordByPhoneApi::class.java,
                BrandOkHttpConfigFactory.getStOkHttpConfig(authProvider, userAgent),
                moshi
            )
        }
    }
}
