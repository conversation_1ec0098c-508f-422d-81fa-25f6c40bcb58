package com.stt.android.resetpassword

import com.stt.android.remote.response.AskoResponse
import retrofit2.http.Header
import retrofit2.http.POST
import retrofit2.http.Query

const val HEADER_PHONE_NUMBER_VERIFICATION = "X-Phone-Number-Verification"
interface ResetPasswordByPhoneApi {
    @POST("resetPassword/sms")
    suspend fun sendVerificationCodeForResetPassword(@Query("phoneNumber") phoneNumber: String): AskoResponse<Any>

    @POST("resetPassword/sms/verify")
    suspend fun checkVerificationCodeForResetPassword(
        @Query("phoneNumber") phoneNumber: String,
        @Query("pin") pin: String
    ): AskoResponse<String>

    @POST("changePassword/phone")
    suspend fun changePassword(
        @Header(HEADER_PHONE_NUMBER_VERIFICATION) phoneNumberVerificationToken: String,
        @Query("phoneNumber") phoneNumber: String,
        @Query("newPassword") newPassword: String
    ): AskoResponse<Boolean>
}
