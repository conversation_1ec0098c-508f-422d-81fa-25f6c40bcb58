package com.stt.android.resetpassword

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.viewModelScope
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.base.BaseHandlerViewModel
import com.stt.android.newemail.VERIFICATION_CODE_LENGTH
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class FragmentResetPasswordByPhoneVerifyCodeViewModel @Inject constructor(
    private val resetPasswordRepository: ResetPasswordRepository,
    private val dispatchers: CoroutinesDispatchers,
) : BaseHandlerViewModel() {
    var uiState by mutableStateOf(ResetPasswordByPhoneVerifyCodeUIState())

    fun inputVerifyCode(verifyCode: String) {
        uiState = uiState.copy(
            verifyCode = verifyCode,
            verifyCodeIsExpired = false,
            btnEnabled = verifyCode.length == VERIFICATION_CODE_LENGTH
        )
    }

    fun resendVerifyCode(phoneNumber: String) {
        if (phoneNumber.isNotEmpty()) {
            loadingStart()
            viewModelScope.launch(dispatchers.io + exceptionHandler) {
                resetPasswordRepository.sendVerificationCodeForResetPassword(phoneNumber)
                loadingEnd()
            }
        }
    }

    fun resetPassword(phoneNumber: String, pin: String) {
        if (pin.isNotEmpty() && phoneNumber.isNotEmpty()) {
            loadingStart()
            uiState = uiState.copy(btnEnabled = false)
            viewModelScope.launch(
                dispatchers.io + getCheckVerificationCodeFailedHandler {
                    uiState = uiState.copy(verifyCodeIsExpired = true)
                }
            ) {
                val phoneNumberVerificationToken = resetPasswordRepository.checkVerificationCodeForResetPassword(
                    phoneNumber,
                    pin
                )
                uiState = uiState.copy(
                    phoneNumberVerificationToken = phoneNumberVerificationToken,
                )
                loadingEnd()
            }
        }
    }

    override fun loadingEndWithFail(throwable: Throwable) {
        super.loadingEndWithFail(throwable)
        uiState = uiState.copy(btnEnabled = true)
    }
}

data class ResetPasswordByPhoneVerifyCodeUIState(
    val verifyCode: String = "",
    val phoneNumberVerificationToken: String? = null,
    val userKey: String = "",
    val verifyCodeIsExpired: Boolean = false,
    val btnEnabled: Boolean = false
)
