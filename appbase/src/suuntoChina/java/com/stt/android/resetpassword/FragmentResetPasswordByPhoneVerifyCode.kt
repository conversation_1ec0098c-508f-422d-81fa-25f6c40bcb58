package com.stt.android.resetpassword

import androidx.compose.material.Surface
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import com.stt.android.R
import com.stt.android.compose.base.BaseFragment
import com.stt.android.compose.theme.AppTheme
import com.stt.android.controllers.UserSettingsController
import com.stt.android.extensions.hideKeyboard
import com.stt.android.utils.CustomTabsUtils
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber
import javax.inject.Inject

@AndroidEntryPoint
class FragmentResetPasswordByPhoneVerifyCode : BaseFragment() {

    private val viewModel: FragmentResetPasswordByPhoneVerifyCodeViewModel by viewModels()

    @Inject
    lateinit var userSettingsController: UserSettingsController

    @Composable
    override fun SetContentView() {
        ResetPasswordByPhoneVerifyCodeScreen()
    }

    @Composable
    private fun ResetPasswordByPhoneVerifyCodeScreen() {
        val uiState = viewModel.uiState
        val commonUIState by viewModel.commonUIState.collectAsState()
        LaunchedEffect(uiState.phoneNumberVerificationToken) {
            uiState.phoneNumberVerificationToken?.let {
                Timber.d("phoneNumberVerificationToken: $it")

                findNavController().navigate(
                    FragmentResetPasswordByPhoneVerifyCodeDirections.actionResetPassword()
                        .setToken(it)
                )
            }
        }
        BaseContentBody(
            viewModel = viewModel,
            onBackClick = {
                findNavController().popBackStack()
            }
        ) {
            ContentBody(
                isLoading = commonUIState.isLoading,
                phoneNumber = userSettingsController.settings.phoneNumber,
                verifyCode = uiState.verifyCode,
                verifyCodeIsExpired = uiState.verifyCodeIsExpired,
                btnEnabled = uiState.btnEnabled,
                onBtnClicked = { phoneNumber, verifyCode ->
                    viewModel.resetPassword(phoneNumber, verifyCode)
                    activity?.hideKeyboard()
                },
                onInputVerifyCode = {
                    viewModel.inputVerifyCode(it)
                },
                onResendCallback = { phoneNumber ->
                    viewModel.resendVerifyCode(phoneNumber)
                },
                onContactClick = {
                    activity?.apply {
                        CustomTabsUtils.launchCustomTab(this, getString(R.string.contact_support_link_suunto))
                    }
                },
            )
        }
    }
}

@Preview
@Composable
private fun InputPhoneVerifyCodeScreenPreview() {
    AppTheme {
        Surface {
            ContentBody()
        }
    }
}

@Composable
private fun ContentBody(
    isLoading: Boolean = false,
    phoneNumber: String = "",
    verifyCode: String = "",
    verifyCodeIsExpired: Boolean = false,
    btnEnabled: Boolean = false,
    onBtnClicked: (String, String) -> Unit = { _, _ -> },
    onInputVerifyCode: (String) -> Unit = {},
    onResendCallback: (String) -> Unit = {},
    onContactClick: () -> Unit = {},
) {
    CheckVerifyCodeBody(
        isLoading = isLoading,
        phoneNumber = phoneNumber,
        verifyCode = verifyCode,
        verifyCodeIsExpired = verifyCodeIsExpired,
        btnEnabled = btnEnabled,
        withRegionCode = false,
        onBtnClicked = {
            onBtnClicked.invoke(phoneNumber, verifyCode)
        },
        onInputVerifyCode = onInputVerifyCode,
        onResendCallback = {
            onResendCallback.invoke(phoneNumber)
        },
        onContactClick = onContactClick,
        onActionDone = {
            if (btnEnabled) {
                onBtnClicked.invoke(phoneNumber, verifyCode)
            }
        }
    )
}
