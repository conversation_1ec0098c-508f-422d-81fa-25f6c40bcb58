package com.stt.android.resetpassword

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Icon
import androidx.compose.material.IconButton
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Scaffold
import androidx.compose.material.SnackbarHost
import androidx.compose.material.SnackbarHostState
import androidx.compose.material.Surface
import androidx.compose.material.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import com.stt.android.R
import com.stt.android.compose.base.BaseViewModel
import com.stt.android.compose.layout.ContentCenteringColumn
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.widgets.SnackBarTips
import com.stt.android.remote.interceptors.NetworkErrorUtil

@Preview
@Composable
private fun BaseContentBodyPreview() {
    AppTheme {
        Surface {
            ContentBody(
                onBackClick = {},
                snackbarHostState = SnackbarHostState(),
                exception = null,
                onDismissed = {},
                content = {}
            )
        }
    }
}

@Composable
fun BaseContentBody(
    viewModel: BaseViewModel,
    modifier: Modifier = Modifier,
    onBackClick: (() -> Unit)? = null,
    content: @Composable ColumnScope.() -> Unit
) {
    val commonUIState by viewModel.commonUIState.collectAsState()
    val snackbarHostState = remember { SnackbarHostState() }
    val exception = commonUIState.errorException
    val onDismiss = {
        viewModel.resetErrorMessage()
    }

    ContentBody(
        onBackClick = onBackClick,
        snackbarHostState = snackbarHostState,
        exception = exception,
        onDismissed = onDismiss,
        modifier = modifier,
        content = content,
    )
}

@Composable
private fun ContentBody(
    onBackClick: (() -> Unit)?,
    snackbarHostState: SnackbarHostState,
    exception: Throwable?,
    onDismissed: () -> Unit,
    modifier: Modifier = Modifier,
    content: @Composable() (ColumnScope.() -> Unit),
) {
    AppTheme {
        Scaffold(
            modifier = modifier,
            topBar = {
                TopAppBar(
                    backgroundColor = MaterialTheme.colors.surface,
                    contentColor = MaterialTheme.colors.onSurface
                ) {
                    Box(modifier = Modifier.fillMaxSize()) {
                        IconButton(
                            onClick = { onBackClick?.invoke() },
                            modifier = Modifier.align(Alignment.CenterStart)
                        ) {
                            Icon(
                                painter = painterResource(id = R.drawable.all_ic_arrow_left),
                                contentDescription = stringResource(R.string.back),
                                tint = Color.Unspecified
                            )
                        }

                        Image(
                            painter = painterResource(id = R.drawable.app_logo_small),
                            contentDescription = "logo",
                            modifier = Modifier.align(Alignment.Center)
                        )
                    }
                }
            },
            snackbarHost = {
                SnackbarHost(hostState = snackbarHostState)
            }
        ) { internalPadding ->
            ContentCenteringColumn(Modifier.padding(internalPadding)) {
                Surface {
                    exception?.let {
                        SnackBarTips(
                            message = NetworkErrorUtil.map(it).message ?: "UnKnown Error",
                            snackbarHostState = snackbarHostState,
                            onDismiss = { onDismissed.invoke() }
                        )
                    }
                    content()
                }
            }
        }
    }
}
