package com.stt.android.resetpassword

import javax.inject.Inject

class ResetPasswordRemoteDataSource @Inject constructor(private val resetPasswordByPhoneApi: ResetPasswordByPhoneApi) :
    ResetPasswordDataSource {
    override suspend fun sendVerificationCodeForResetPassword(phoneNumber: String): Any {
        return resetPasswordByPhoneApi.sendVerificationCodeForResetPassword(phoneNumber)
            .payloadOrThrow()
    }

    override suspend fun checkVerificationCodeForResetPassword(
        phoneNumber: String,
        pin: String
    ): String {
        return resetPasswordByPhoneApi.checkVerificationCodeForResetPassword(phoneNumber, pin)
            .payloadOrThrow()
    }

    override suspend fun changePassword(
        phoneNumberVerificationToken: String,
        phoneNumber: String,
        newPassword: String
    ): Boolean {
        return resetPasswordByPhoneApi.changePassword(
            phoneNumberVerificationToken,
            phoneNumber,
            newPassword
        ).payloadOrThrow()
    }
}
