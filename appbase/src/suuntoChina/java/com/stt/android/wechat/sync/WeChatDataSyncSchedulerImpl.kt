package com.stt.android.wechat.sync

import androidx.work.WorkManager
import com.stt.android.data.trenddata.WeChatTrendDataRemoteJob
import com.stt.android.remote.wechat.DeviceParams
import com.stt.android.remote.wechat.DeviceStatusType
import com.stt.android.remote.wechat.WeChatSportRemoteApi
import com.stt.android.watch.background.WeChatDataSyncScheduler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

class WeChatDataSyncSchedulerImpl @Inject constructor(
    private val weChatSportRemoteApi: WeChatSportRemoteApi
) : WeChatDataSyncScheduler {
    private val coroutineScope = CoroutineScope(Dispatchers.IO + SupervisorJob())

    override fun syncWechatTrendData(workManager: WorkManager) {
        WeChatTrendDataRemoteJob.enqueue(workManager)
    }

    override fun resetDevice(productModel: String, sn: String) {
        coroutineScope.launch {
            runCatching {
                val deviceInfoDetail = weChatSportRemoteApi.getDeviceInfoDetail(productModel, sn)
                if (deviceInfoDetail?.bindStatus() == DeviceStatusType.Bound) {
                    try {
                        weChatSportRemoteApi.resetDevice(
                            DeviceParams(productModel, sn)
                        )
                    } catch (e: Exception) {
                        Timber.w(e, "WeChat reset device fail: ${e.message}")
                    }
                }
            }.onFailure { Timber.w(it, "WeChat device info detail fail during resetDevice") }
        }
    }
}
