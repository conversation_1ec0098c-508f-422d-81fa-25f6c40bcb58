package com.stt.android.wechat.sport.data

import com.stt.android.remote.wechat.DeviceDetail
import com.stt.android.remote.wechat.DeviceParams
import com.stt.android.remote.wechat.RegisterWxState
import com.stt.android.remote.wechat.RemoteBoundDevicesData
import com.stt.android.remote.wechat.SyncDataToWeChat

interface WeChatSportDataSource {
    /**
     * The device binds temporary identity credentials
     * Wechat SDK requires ticket to call up WeRun mini program
     */
    suspend fun getWeChatTicket(productModel: String, sn: String): String?
    suspend fun getDeviceInfoDetail(productModel: String, sn: String): DeviceDetail?
    suspend fun bindUser(info: DeviceParams): Any?
    suspend fun syncDataToWeChat(data: SyncDataToWeChat): Any?
    suspend fun resetDevice(deviceParams: DeviceParams): Any?
    suspend fun supportWechatSport(productModel: String, sn: String): Boolean?
    suspend fun registerWx(deviceParams: DeviceParams): RegisterWxState?
    suspend fun getBoundDevice(): RemoteBoundDevicesData?
}
