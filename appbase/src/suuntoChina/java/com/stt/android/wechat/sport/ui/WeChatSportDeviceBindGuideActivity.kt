package com.stt.android.wechat.sport.ui

import androidx.activity.viewModels
import androidx.appcompat.widget.Toolbar
import com.stt.android.R
import com.stt.android.common.ui.observeNotNull
import com.stt.android.databinding.ActivityWechatSportDeviceBindGuideBinding
import com.stt.android.network.interfaces.ANetworkProvider
import com.stt.android.ui.utils.ThrottlingOnClickListener
import com.stt.android.wechat.sport.vm.WeChatDeviceBindGuideVM
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class WeChatSportDeviceBindGuideActivity : HandleWeChatConnectActivity() {
    override val viewModel: WeChatDeviceBindGuideVM by viewModels()

    private val viewDataBinding: ActivityWechatSportDeviceBindGuideBinding get() = requireBinding()

    override fun getToolbar(): Toolbar = viewDataBinding.wechatSportGuideToolbar
    override fun getToolbarTitle(): String = getString(R.string.we_chat)
    override fun getLayoutResId(): Int = R.layout.activity_wechat_sport_device_bind_guide

    override fun initView() {
        super.initView()
        viewDataBinding.wechatDeviceConnect.setOnClickListener(
            ThrottlingOnClickListener {
                if (ANetworkProvider.isOnline()) {
                    viewModel.getWatchConnectStatus()
                } else {
                    showSnackBar(getString(R.string.no_network_error))
                }
            }
        )
        viewModel.watchStatus.observeNotNull(this) {
            if (!it) showSnackBar(getString(R.string.watch_not_connected))
        }
        // when jump to wechat,finish this activity
        viewModel.connectWechatEvent.observe(this) {
            if (it) {
                finish()
            }
        }
    }
}
