package com.stt.android.wechat.sport

import android.content.SharedPreferences
import androidx.core.content.edit
import com.stt.android.di.WeChatPreferences
import com.stt.android.sharingplatform.WeChatAPI
import com.stt.android.utils.STTConstants
import javax.inject.Inject

class WeChatSportDeviceBindHelper @Inject constructor(
    private val weChatAPI: WeChatAPI,
    @WeChatPreferences private val weChatPreferences: SharedPreferences
) {

    private val bindPath = "pages/discover-new/discover-new?from=wxsport&&ticket="
    private val unbindPath = "pages/delete-devices/delete-devices?from=wxsport&&sdkIdList="

    fun jumpToWeChatDeviceBindOrUnbind(
        ticket: String? = null,
        sdkId: String? = null,
        bind: Boolean
    ) {
        // when jump to WeChat App, save need refresh when back
        weChatPreferences.edit {
            putBoolean(STTConstants.WeChatPreferences.KEY_REFRESH, true)
        }
        weChatAPI.jumpToWXLaunchMiniProgram(if (bind) bindPath + ticket else "$unbindPath[\"$sdkId\"]")
    }

    fun isWeChatAppInstalled(): Boolean {
        return weChatAPI.appInstalled()
    }
}
