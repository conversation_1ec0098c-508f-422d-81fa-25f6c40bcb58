package com.stt.android.wechat.sport.ui

import android.os.Bundle
import androidx.appcompat.widget.Toolbar
import androidx.databinding.ViewDataBinding
import com.google.android.material.snackbar.Snackbar
import com.stt.android.R
import com.stt.android.common.ui.ErrorEvent
import com.stt.android.common.ui.SimpleProgressDialogFragment
import com.stt.android.common.ui.ViewModelActivity2
import com.stt.android.common.ui.observeNotNull
import com.stt.android.wechat.sport.vm.BaseWeChatVM
import timber.log.Timber

/**
 * This  is the base ViewModelActivity of wechat
 */
abstract class WeChatDataBindingActivity : ViewModelActivity2() {
    protected abstract fun getToolbar(): Toolbar
    protected abstract fun getToolbarTitle(): String
    abstract override val viewModel: BaseWeChatVM
    private var loadingDialog: SimpleProgressDialogFragment? = null
    private val tag = "SimpleProgressDialogFragment"
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setToolbar()
        initView()
        if (showLoading()) {
            viewModel.loading.observe(this) {
                if (it) {
                    showLoadingDialog()
                } else {
                    hideLoadingDialog()
                }
            }
        }
        viewModel.errorEvent.observeNotNull(this) {
            showErrorSnackbar(ErrorEvent.get(it::class))
        }
    }

    protected open fun showLoading(): Boolean = true

    protected abstract fun initView()
    protected fun showSnackBar(message: String) {
        Snackbar.make(requireBinding<ViewDataBinding>().root, message, Snackbar.LENGTH_SHORT).show()
    }

    private fun showErrorSnackbar(errorEvent: ErrorEvent) {
        val snackbar = Snackbar.make(
            requireBinding<ViewDataBinding>().root,
            errorEvent.errorStringRes,
            if (errorEvent.canRetry) Snackbar.LENGTH_INDEFINITE else Snackbar.LENGTH_LONG
        )
        if (errorEvent.canRetry) {
            snackbar.setAction(R.string.retry_action) {
                Timber.v("Retry button clicked")
            }
            viewModel.retry()
        }
        snackbar.show()
    }

    protected fun showLoadingDialog(message: String = "") {
        if (loadingDialog != null) return
        loadingDialog = SimpleProgressDialogFragment.newInstance(message)
        loadingDialog?.show(supportFragmentManager, tag)
    }

    protected fun hideLoadingDialog() {
        loadingDialog?.dismissAllowingStateLoss()
        loadingDialog = null
    }

    protected fun setToolbar() {
        setSupportActionBar(getToolbar())
        supportActionBar?.apply {
            title = getToolbarTitle()
        }
        getToolbar().setNavigationOnClickListener {
            onBackPressed()
        }
    }
}
