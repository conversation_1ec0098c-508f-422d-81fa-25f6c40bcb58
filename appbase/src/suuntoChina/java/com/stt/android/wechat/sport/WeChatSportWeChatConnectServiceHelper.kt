package com.stt.android.wechat.sport

import android.content.Context
import android.content.Intent
import com.stt.android.home.settings.connectedservices.WeChatConnectServiceHelper
import com.stt.android.wechat.sport.ui.WeChatSportDeviceBindGuideActivity
import com.stt.android.wechat.sport.ui.WeChatSportDeviceBoundActivity
import javax.inject.Inject

class WeChatSportWeChatConnectServiceHelper @Inject constructor() : WeChatConnectServiceHelper {
    override fun showWeRunItem(): Boolean = true

    override fun toWeRunConnectGuidePage(context: Context) {
        // jump to guide Service
        val intent = Intent(context, WeChatSportDeviceBindGuideActivity::class.java)
        context.startActivity(intent)
    }

    override fun toWeRunConnectedPage(context: Context) {
        // jump to has bound service
        val intent = Intent(context, WeChatSportDeviceBoundActivity::class.java)
        context.startActivity(intent)
    }
}
