package com.stt.android.wechat

import com.stt.android.watch.WeChatConnectionStateHelper
import com.stt.android.wechat.sport.data.WeChatSportDataSource
import javax.inject.Inject

class WeChatConnectionStateHelperImpl @Inject constructor(
    private val weChatSportDataSource: WeChatSportDataSource,
) : WeChatConnectionStateHelper {

    override suspend fun supportedWeRun(variantName: String, sn: String): Boolean {
        return weChatSportDataSource.supportWechatSport(variantName, sn) ?: false
    }

    override suspend fun weChatConnectionState(): Bo<PERSON>an {
        return weChatSportDataSource.getBoundDevice()?.weChatDevices?.isNotEmpty() ?: false
    }
}
