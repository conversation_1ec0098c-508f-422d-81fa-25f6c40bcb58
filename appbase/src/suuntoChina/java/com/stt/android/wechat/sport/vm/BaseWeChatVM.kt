package com.stt.android.wechat.sport.vm

import androidx.lifecycle.LiveData
import androidx.lifecycle.viewModelScope
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.AnalyticsEventProperty
import com.stt.android.analytics.AnalyticsProperties
import com.stt.android.common.coroutines.CoroutinesDispatcherProvider
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.common.ui.RxViewModel
import com.stt.android.domain.di.IoThread
import com.stt.android.domain.di.MainThread
import com.stt.android.extensions.capitalize
import com.stt.android.remote.wechat.DeviceParams
import com.stt.android.remote.wechat.RegisterWxState
import com.stt.android.ui.utils.SingleLiveEvent
import com.stt.android.wechat.sport.WeChatSportDeviceBindHelper
import com.stt.android.wechat.sport.data.WeChatSportDataSource
import com.suunto.connectivity.repository.AnalyticsUtils
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoDeviceType
import io.reactivex.Scheduler
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.Locale

abstract class BaseWeChatVM(
    private val weChatSportDataSource: WeChatSportDataSource,
    private val weChatSportDeviceBindHelper: WeChatSportDeviceBindHelper,
    @IoThread ioThread: Scheduler,
    @MainThread mainThread: Scheduler,
    coroutinesDispatchers: CoroutinesDispatchers = CoroutinesDispatcherProvider()
) : RxViewModel(ioThread, mainThread, coroutinesDispatchers) {

    private val _errorEvent = SingleLiveEvent<Exception>()
    val errorEvent: LiveData<Exception> = _errorEvent
    val loading: LiveData<Boolean>
        get() = _loading
    private val _loading = SingleLiveEvent<Boolean>()
    private var finished = false

    private val _weChatAppInstallState = SingleLiveEvent<Boolean>()
    val weChatAppInstallState: LiveData<Boolean> = _weChatAppInstallState

    private val _registerWxState = SingleLiveEvent<RegisterWxState>()
    val registerWxState = _registerWxState

    private val _supportWechatSportStatus = SingleLiveEvent<Boolean>()
    val supportWechatSportStatus: LiveData<Boolean> = _supportWechatSportStatus

    private val _connectWechatEvent = SingleLiveEvent<Boolean>()
    val connectWechatEvent: LiveData<Boolean> = _connectWechatEvent

    private suspend fun bindDevice(productMode: String, sn: String) {
        if (weChatSportDeviceBindHelper.isWeChatAppInstalled().not()) {
            _weChatAppInstallState.postValue(false)
            return
        }
        val deviceParams = DeviceParams(productMode.capitalize(Locale.CHINA), sn)
        when (weChatSportDataSource.registerWx(deviceParams)) {
            RegisterWxState.Success -> {
                val newWeChatTicket = weChatSportDataSource.getWeChatTicket(productMode, sn)
                weChatSportDataSource.bindUser(deviceParams)
                weChatSportDeviceBindHelper.jumpToWeChatDeviceBindOrUnbind(
                    newWeChatTicket,
                    bind = true
                )
                // wait jump to wechatAPP success
                delay(500)
                _connectWechatEvent.postValue(true)
            }

            RegisterWxState.Pending -> _registerWxState.postValue(RegisterWxState.Pending)
            else -> _registerWxState.postValue(RegisterWxState.Fail)
        }
    }

    fun supportWechatSport(productModel: String, sn: String) {
        run {
            val supportWechatSport = weChatSportDataSource.supportWechatSport(
                productModel.capitalize(Locale.CHINA),
                sn
            )
            if (supportWechatSport == true) {
                bindDevice(productModel, sn)
            } else {
                _supportWechatSportStatus.postValue(false)
            }
        }
    }

    fun sendAnalyticsEvent(productModel: String) {
        val deviceType = SuuntoDeviceType.fromVariantName(productModel)
        if (deviceType != SuuntoDeviceType.Unrecognized) {
            val properties = AnalyticsProperties().apply {
                put(AnalyticsEventProperty.SUUNTO_WATCH_MODEL, deviceType.displayName)
            }
            AnalyticsUtils.sendAnyEvent(AnalyticsEvent.WECHAT_SPORT_BIND, properties)
        }
    }

    protected fun <T> run(dataFlow: MutableStateFlow<T>, block: suspend () -> T) {
        viewModelScope.launch {
            _loading.value = true
            try {
                withContext(Dispatchers.IO) {
                    dataFlow.emit(block.invoke())
                }
                _loading.value = false
            } catch (e: Exception) {
                _errorEvent.value = e
                _loading.value = false
            }
        }
    }

    protected fun run(block: suspend () -> Unit) {
        viewModelScope.launch {
            _loading.value = true
            try {
                withContext(Dispatchers.IO) {
                    block.invoke()
                }
                _loading.value = false
            } catch (e: Exception) {
                _errorEvent.value = e
                _loading.value = false
            }
        }
    }

    protected fun runNoLoading(block: suspend () -> Unit) {
        viewModelScope.launch {
            try {
                withContext(Dispatchers.IO) {
                    block.invoke()
                }
            } catch (e: Exception) {
                _errorEvent.value = e
            }
        }
    }

    protected fun runDelayLoading(block: suspend () -> Unit) {
        viewModelScope.launch {
            finished = false
            val async = async(Dispatchers.IO) {
                delay(500)
                if (finished.not()) _loading.postValue(true)
            }
            try {
                withContext(Dispatchers.IO) {
                    block.invoke()
                }
                finished = true
                _loading.value = false
            } catch (e: Exception) {
                _errorEvent.value = e
                _loading.value = false
            }
        }
    }

    open fun retry() {}
}
