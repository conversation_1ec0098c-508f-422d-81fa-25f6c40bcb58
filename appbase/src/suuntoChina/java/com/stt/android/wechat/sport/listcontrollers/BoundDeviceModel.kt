package com.stt.android.wechat.sport.listcontrollers

import android.widget.TextView
import com.airbnb.epoxy.EpoxyAttribute
import com.airbnb.epoxy.EpoxyModelClass
import com.airbnb.epoxy.EpoxyModelWithHolder
import com.google.android.material.switchmaterial.SwitchMaterial
import com.stt.android.R
import com.stt.android.common.KotlinEpoxyHolder
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoDeviceType
import java.util.Locale

@EpoxyModelClass
abstract class BoundDeviceModel : EpoxyModelWithHolder<BoundDeviceHolder>() {
    @EpoxyAttribute
    lateinit var boundDevice: BoundDevicesDataContainer

    override fun getDefaultLayout(): Int = R.layout.model_bound_device

    override fun bind(holder: BoundDeviceHolder) {
        super.bind(holder)
        val displayName = SuuntoDeviceType.fromVariantName(boundDevice.variantName).displayName
        holder.watchName.text = displayName
        holder.sn.text = String.format(Locale.CHINA, "%s%s", "SN:", boundDevice.watchSn)
        holder.switch.isChecked = boundDevice.reportToWxStatus()
        holder.controlConnect.apply {
            text = if (boundDevice.connected) {
                context.getString(R.string.wc_disconnect)
            } else {
                context.getString(R.string.wc_connect)
            }
        }
        holder.controlConnect.setOnClickListener {
            boundDevice.setConnectState(boundDevice)
        }
        holder.switch.setOnCheckedChangeListener { _, check ->
            boundDevice.onCheckedChange(boundDevice, check)
        }
    }
}

class BoundDeviceHolder : KotlinEpoxyHolder() {
    val watchName: TextView by bind(R.id.watch_name)
    val sn: TextView by bind(R.id.watch_sn)
    val controlConnect: TextView by bind(R.id.control_connect)
    val switch: SwitchMaterial by bind(R.id.switch_sync_data)
}
