package com.stt.android.wechat.sport.listcontrollers

import com.airbnb.epoxy.TypedEpoxyController
import com.stt.android.boundDeviceHead
import javax.inject.Inject

class BoundDeviceEpoxyController @Inject constructor() :
    TypedEpoxyController<List<BoundDevicesDataContainer>>() {

    override fun buildModels(data: List<BoundDevicesDataContainer>?) {
        boundDeviceHead {
            id("boundDeviceHead")
        }
        data?.forEach {
            boundDevice {
                id("boundDevice")
                boundDevice(it)
            }
        }
    }
}
