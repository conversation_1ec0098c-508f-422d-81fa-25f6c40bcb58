package com.stt.android.wechat.sport.listcontrollers

data class BoundDevicesDataContainer(
    val variantName: String,
    val watchSn: String,
    val sdkId: String = "",
    val reportToWxStatus: Int = 1,
    val connected: Boolean = true,
    val setConnectState: (boundDevice: BoundDevicesDataContainer) -> Unit,
    val onCheckedChange: (boundDevice: BoundDevicesDataContainer, check: Boolean) -> Unit
) {
    fun reportToWxStatus(): Boolean {
        return reportToWxStatus == 1
    }
}
