package com.stt.android.wechat.sport.vm

import android.text.TextUtils
import androidx.lifecycle.LiveData
import com.stt.android.domain.device.DeviceConnectionStateUseCase
import com.stt.android.domain.di.IoThread
import com.stt.android.domain.di.MainThread
import com.stt.android.remote.wechat.SyncDataToWeChat
import com.stt.android.ui.utils.SingleLiveEvent
import com.stt.android.utils.DeviceVariantNameConverter
import com.stt.android.wechat.sport.WeChatSportDeviceBindHelper
import com.stt.android.wechat.sport.data.WeChatSportDataSource
import com.stt.android.wechat.sport.listcontrollers.BoundDevicesDataContainer
import dagger.hilt.android.lifecycle.HiltViewModel
import io.reactivex.Scheduler
import kotlinx.coroutines.delay
import kotlinx.coroutines.reactive.awaitFirst
import timber.log.Timber
import javax.inject.Inject

@HiltViewModel
class WeChatDeviceBoundVM @Inject constructor(
    private val weChatSportDataSource: WeChatSportDataSource,
    private val weChatSportDeviceBindHelper: WeChatSportDeviceBindHelper,
    private val deviceConnectionStateUseCase: DeviceConnectionStateUseCase,
    @IoThread ioThread: Scheduler,
    @MainThread mainThread: Scheduler
) : BaseWeChatVM(
    weChatSportDataSource,
    weChatSportDeviceBindHelper,
    ioThread,
    mainThread
) {
    private val _unBindEvent = SingleLiveEvent<Boolean>()
    val unBindEvent: LiveData<Boolean> = _unBindEvent
    private val _hasBoundDevice = SingleLiveEvent<List<BoundDevicesDataContainer>>()
    val hasBoundDevice = _hasBoundDevice

    fun getBoundDevice() {
        runDelayLoading {
            val boundDeviceDataContainer = weChatSportDataSource.getBoundDevice()
            val boundDeviceDataList = arrayListOf<BoundDevicesDataContainer>()
            boundDeviceDataContainer?.apply {
                weChatDevices.forEach {
                    boundDeviceDataList.add(
                        BoundDevicesDataContainer(
                            it.productModel,
                            it.watchSN,
                            it.sdkId,
                            it.reportToWxStatus,
                            true,
                            ::setConnectState,
                            ::syncDataToWeChat
                        )
                    )
                }
            }
            try {
                val deviceInfo =
                    deviceConnectionStateUseCase.connectedWatchState().awaitFirst().deviceInfo
                // Whether the device exists
                val findDevice =
                    boundDeviceDataList.find { it.watchSn == deviceInfo?.serial }
                if (findDevice == null) {
                    deviceInfo?.let {
                        // to china variantName to global name  eg: MonzaC to Monza
                        val productModel = DeviceVariantNameConverter.toGlobalCapitalize(it.variantName)
                        boundDeviceDataList.add(
                            BoundDevicesDataContainer(
                                productModel,
                                it.serial,
                                connected = false,
                                setConnectState = ::setConnectState,
                                onCheckedChange = ::syncDataToWeChat
                            )
                        )
                    }
                }
            } catch (e: Exception) {
                Timber.w(e, "get current connected device:${e.message}")
            }
            _hasBoundDevice.postValue(boundDeviceDataList)
        }
    }

    private fun setConnectState(boundDevicesDataContainer: BoundDevicesDataContainer) {
        if (boundDevicesDataContainer.connected) {
            unBind(
                boundDevicesDataContainer.sdkId,
                boundDevicesDataContainer.variantName,
                boundDevicesDataContainer.watchSn
            )
        } else {
            supportWechatSport(
                boundDevicesDataContainer.variantName,
                boundDevicesDataContainer.watchSn
            )
        }
    }

    private fun syncDataToWeChat(
        boundDevicesDataContainer: BoundDevicesDataContainer,
        sync: Boolean
    ) {
        runNoLoading {
            weChatSportDataSource.syncDataToWeChat(
                SyncDataToWeChat(
                    boundDevicesDataContainer.variantName,
                    if (sync) 1 else 0,
                    boundDevicesDataContainer.watchSn
                )
            )
        }
    }

    private fun unBind(sdkId: String?, productModel: String, sn: String) {
        run {
            val paramSdkId = if (TextUtils.isEmpty(sdkId)) {
                weChatSportDataSource.getDeviceInfoDetail(productModel, sn)?.sdkId ?: ""
            } else {
                sdkId
            }

            weChatSportDeviceBindHelper.jumpToWeChatDeviceBindOrUnbind(
                sdkId = paramSdkId,
                bind = false
            )
            // wait for jump to wechat finish
            delay(500)
            _unBindEvent.postValue(true)
        }
    }
}
