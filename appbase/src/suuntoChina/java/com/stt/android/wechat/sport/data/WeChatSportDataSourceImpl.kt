package com.stt.android.wechat.sport.data

import com.stt.android.remote.wechat.DeviceDetail
import com.stt.android.remote.wechat.DeviceParams
import com.stt.android.remote.wechat.RegisterWxState
import com.stt.android.remote.wechat.RemoteBoundDevicesData
import com.stt.android.remote.wechat.SyncDataToWeChat
import com.stt.android.remote.wechat.WeChatSportRemoteApi
import com.stt.android.remote.wechat.WechatSupportType
import javax.inject.Inject

class WeChatSportDataSourceImpl @Inject constructor(
    private val remoteApi: WeChatSportRemoteApi
) : WeChatSportDataSource {
    override suspend fun getWeChatTicket(productModel: String, sn: String): String? {
        return remoteApi.getWeChatTicket(productModel, sn)
    }

    override suspend fun getDeviceInfoDetail(productModel: String, sn: String): DeviceDetail? {
        return remoteApi.getDeviceInfoDetail(productModel, sn)
    }

    override suspend fun bindUser(info: DeviceParams): Any? {
        return remoteApi.bindUser(info)
    }

    override suspend fun syncDataToWeChat(data: SyncDataToWeChat): Any? {
        return remoteApi.syncDataToWeChat(data)
    }

    override suspend fun resetDevice(deviceParams: DeviceParams): Any? {
        return remoteApi.resetDevice(deviceParams)
    }

    override suspend fun supportWechatSport(productModel: String, sn: String): Boolean? {
        return remoteApi.supportWechatSport(productModel, sn) == WechatSupportType.Supported
    }

    override suspend fun registerWx(deviceParams: DeviceParams): RegisterWxState? {
        return remoteApi.registerWx(deviceParams)
    }

    override suspend fun getBoundDevice(): RemoteBoundDevicesData? {
        return remoteApi.getBoundDevice()
    }
}
