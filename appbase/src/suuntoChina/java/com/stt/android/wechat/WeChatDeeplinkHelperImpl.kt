package com.stt.android.wechat

import android.content.Context
import android.content.Intent
import androidx.core.net.toUri
import com.stt.android.WeChatDeepLinkHelper
import com.stt.android.controllers.CurrentUserController
import com.stt.android.launcher.DeepLinkIntentBuilder
import com.stt.android.launcher.ProxyActivity
import java.util.Locale
import javax.inject.Inject

class WeChatDeeplinkHelperImpl @Inject constructor(
    private val deepLinkIntentBuilder: DeepLinkIntentBuilder,
    private val currentUserController: CurrentUserController,
) :
    WeChatDeepLinkHelper {

    override fun getIntent(
        context: Context,
        deepLink: String
    ): Intent {
        val fragmentOrPathParts = deepLinkIntentBuilder.getFragmentsOrPathParts(deepLink.toUri())
        val type = fragmentOrPathParts.getOrNull(1)?.lowercase(Locale.ROOT) ?: ""
        return runCatching {
            deepLinkIntentBuilder.getDeepLinkIntent(
                context = context,
                uri = deepLink.toUri(),
                currentUserController = currentUserController,
                fragmentOrPathParts = fragmentOrPathParts,
                type = type
            )
        }.getOrNull() ?: Intent(context, ProxyActivity::class.java)
    }
}
