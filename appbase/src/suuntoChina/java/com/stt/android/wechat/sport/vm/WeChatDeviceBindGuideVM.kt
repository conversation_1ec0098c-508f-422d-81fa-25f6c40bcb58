package com.stt.android.wechat.sport.vm

import androidx.lifecycle.LiveData
import com.stt.android.domain.di.IoThread
import com.stt.android.domain.di.MainThread
import com.stt.android.ui.utils.SingleLiveEvent
import com.stt.android.utils.DeviceVariantNameConverter
import com.stt.android.utils.toV2Flowable
import com.stt.android.watch.SuuntoWatchModel
import com.stt.android.wechat.sport.WeChatSportDeviceBindHelper
import com.stt.android.wechat.sport.data.WeChatSportDataSource
import dagger.hilt.android.lifecycle.HiltViewModel
import io.reactivex.Scheduler
import javax.inject.Inject

@HiltViewModel
class WeChatDeviceBindGuideVM @Inject constructor(
    weChatSportDataSource: WeChatSportDataSource,
    weChatSportDeviceBindHelper: WeChatSportDeviceBindHelper,
    private val suuntoWatchModel: SuuntoWatchModel,
    @IoThread ioThread: Scheduler,
    @MainThread mainThread: Scheduler
) : BaseWeChatVM(
    weChatSportDataSource,
    weChatSportDeviceBindHelper,
    ioThread,
    mainThread
) {
    private val _watchStatus = SingleLiveEvent<Boolean>()
    val watchStatus: LiveData<Boolean> = _watchStatus

    fun getWatchConnectStatus() {
        disposables.add(
            suuntoWatchModel.stateChangeObservable.toV2Flowable().firstOrError().subscribe({
                if (it.isConnected) {
                    val productModel = DeviceVariantNameConverter.toGlobalCapitalize(it.deviceInfo?.variant ?: "")
                    val sn = it.deviceInfo?.serial ?: ""
                    sendAnalyticsEvent(productModel)
                    supportWechatSport(productModel, sn)
                } else {
                    _watchStatus.postValue(false)
                }
            }, {
                _watchStatus.postValue(false)
            })
        )
    }

    override fun retry() {
        super.retry()
        getWatchConnectStatus()
    }
}
