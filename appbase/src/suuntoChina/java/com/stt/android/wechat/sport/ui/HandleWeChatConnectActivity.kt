package com.stt.android.wechat.sport.ui

import com.stt.android.R
import com.stt.android.common.ui.observeNotNull
import com.stt.android.remote.wechat.RegisterWxState
import com.stt.android.wechat.sport.vm.BaseWeChatVM

abstract class HandleWeChatConnectActivity : WeChatDataBindingActivity() {
    abstract override val viewModel: BaseWeChatVM

    override fun initView() {
        viewModel.weChatAppInstallState.observeNotNull(this) {
            showSnackBar(getString(R.string.please_install_wc))
        }
        viewModel.registerWxState.observe(this) {
            if (it == RegisterWxState.Pending) {
                showSnackBar(getString(R.string.wechat_sport_function_upgrade))
            } else if (it == RegisterWxState.Fail) {
                showSnackBar(getString(R.string.connect_wechat_fail))
            }
        }
        viewModel.supportWechatSportStatus.observeNotNull(this) {
            if (!it) {
                showSnackBar(getString(R.string.watch_not_support_wechat_sport))
            }
        }
    }
}
