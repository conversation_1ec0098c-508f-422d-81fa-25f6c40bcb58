package com.stt.android.wechat.sport.ui

import androidx.activity.viewModels
import androidx.appcompat.widget.Toolbar
import com.stt.android.R
import com.stt.android.common.ui.observeNotNull
import com.stt.android.databinding.ActivityWechatSportDeviceHasBoundBinding
import com.stt.android.social.userprofile.SupportActivity
import com.stt.android.ui.utils.EpoxyConditionalDividerItemDecoration
import com.stt.android.ui.utils.ThrottlingOnClickListener
import com.stt.android.utils.STTConstants.HelpShiftPublishId
import com.stt.android.wechat.sport.listcontrollers.BoundDeviceEpoxyController
import com.stt.android.wechat.sport.vm.WeChatDeviceBoundVM
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class WeChatSportDeviceBoundActivity : HandleWeChatConnectActivity() {

    @Inject
    lateinit var boundDeviceEpoxyController: BoundDeviceEpoxyController

    override val viewModel: WeChatDeviceBoundVM by viewModels()

    private val viewDataBinding: ActivityWechatSportDeviceHasBoundBinding get() = requireBinding()

    override fun getLayoutResId(): Int = R.layout.activity_wechat_sport_device_has_bound
    override fun getToolbar(): Toolbar = viewDataBinding.wechatSportHasBoundToolbar

    override fun getToolbarTitle(): String = getString(R.string.we_chat)

    override fun initView() {
        super.initView()
        initRV()
        viewModel.unBindEvent.observeNotNull(this) {
            // when unbind,need finish this page
            if (it) {
                finish()
            }
        }
        viewDataBinding.tvHelp.setOnClickListener(
            ThrottlingOnClickListener {
                SupportActivity.newIntent(
                    this,
                    HelpShiftPublishId.WECHAT_SYNC_HELP,
                    title = getString(R.string.settings_help)
                )
            }
        )
        viewModel.hasBoundDevice.observeNotNull(this) {
            boundDeviceEpoxyController.setData(it)
        }
        viewModel.getBoundDevice()
    }

    private fun initRV() {
        viewDataBinding.boundDeviceList.adapter = boundDeviceEpoxyController.adapter
        val dividerHeight = resources.getDimensionPixelSize(R.dimen.feed_card_divider)
        viewDataBinding.boundDeviceList.addItemDecoration(
            EpoxyConditionalDividerItemDecoration(
                drawDividerOver = false
            ) { item, _ ->
                if (resources.getBoolean(R.bool.wide_display) || item != null) {
                    dividerHeight
                } else {
                    null
                }
            }
        )
    }
}
