package com.stt.android.controllers

import android.content.Context
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.data.marketing.MarketingInboxRemoteDataSource
import com.stt.android.domain.notifications.GetNotificationsCountUseCase
import com.stt.android.domain.notifications.GetUnreadNotificationsCountUseCase
import com.stt.android.remote.marketing.RemoteLinkType
import com.stt.android.remote.marketing.RemoteMarketingInboxMessage
import com.stt.android.remote.marketing.RemoteMarketingInboxMessage.RemoteAction
import com.stt.android.social.notifications.list.EmarsysAction
import com.stt.android.social.notifications.list.EmarsysActionType
import com.stt.android.social.notifications.list.EmarsysInboxItem
import com.stt.android.social.notifications.list.EmarsysInboxSource
import com.stt.android.social.notifications.list.EmarsysInboxType
import com.stt.android.social.notifications.list.InboxMessageTag
import com.stt.android.utils.HuaweiUtils
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import timber.log.Timber
import java.time.Instant
import java.time.LocalDateTime
import java.time.ZoneId
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class FeedController @Inject constructor(
    @ApplicationContext context: Context,
    private val marketingInboxRemoteDataSource: MarketingInboxRemoteDataSource,
    private val dispatchers: CoroutinesDispatchers,
    getNotificationsCountUseCase: GetNotificationsCountUseCase,
    getUnreadNotificationsCountUseCase: GetUnreadNotificationsCountUseCase,
    huaweiUtils: HuaweiUtils,
) : BaseFeedController(
    context,
    getNotificationsCountUseCase,
    getUnreadNotificationsCountUseCase,
    huaweiUtils,
) {
    override val emarsysInboxItems: Flow<List<EmarsysInboxItem>> = combine(
        super.emarsysInboxItems,
        marketingInboxMessages,
    ) { items1, items2 -> items1 + items2 }

    private val marketingInboxMessages: Flow<List<EmarsysInboxItem>> get() = flow {
        val messages = runSuspendCatching {
            marketingInboxRemoteDataSource.getMarketingInboxes()
                .filter { !(it.expired || it.deleted) }
                .map { message ->
                    EmarsysInboxItem(
                        message.id,
                        message.title,
                        message.description,
                        message.imageUrl,
                        message.action?.toEmarsysAction()?.let { listOf(it) },
                        null,
                        null,
                        null,
                        message.tags,
                        message.emarsysInboxType,
                        TimeUnit.MILLISECONDS.toSeconds(message.receivedAt),
                        false,
                        formatReceivedDateTime(message.receivedAt),
                        EmarsysInboxSource.SUUNTO,
                    )
                }
        }.onFailure { e ->
            Timber.w(e, "Fetch marketing inbox messages failed")
        }
            .getOrNull()
            ?: emptyList()
        emit(messages)
    }.flowOn(dispatchers.io)

    private fun RemoteAction.toEmarsysAction() = linkType?.toEmarsysActionType()?.let { type ->
        // id is unused, it's fine to be empty.
        EmarsysAction("", title, type, link)
    }

    private fun RemoteLinkType.toEmarsysActionType() = when (this) {
        RemoteLinkType.EXTERNAL_URL -> EmarsysActionType.OPEN_EXTERNAL_URL
        RemoteLinkType.DEEPLINK -> EmarsysActionType.DEEP_LINK
        RemoteLinkType.H5 -> EmarsysActionType.H5
    }

    private val RemoteMarketingInboxMessage.emarsysInboxType: EmarsysInboxType
        get() = when {
            properties?.get("type") == "annualReport" -> EmarsysInboxType.ANNUAL_REPORT
            !imageUrl.isNullOrEmpty() -> EmarsysInboxType.BIG_IMAGE
            else -> EmarsysInboxType.TEXT
        }

    private val RemoteMarketingInboxMessage.expired: Boolean
        get() = LocalDateTime.ofInstant(
            Instant.ofEpochSecond(expiresAt),
            ZoneId.systemDefault(),
        ).isBefore(LocalDateTime.now())

    private val RemoteMarketingInboxMessage.deleted: Boolean
        get() = tags.any { it == InboxMessageTag.DELETED.tag }
}
