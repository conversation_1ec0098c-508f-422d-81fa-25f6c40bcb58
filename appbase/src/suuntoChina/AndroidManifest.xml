<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.stt.android">

    <uses-permission android:name="com.android.permission.GET_INSTALLED_APPS" />

    <application
        android:supportsRtl="false"
        tools:replace="android:supportsRtl">

        <activity
            android:name=".wechat.sport.ui.WeChatSportDeviceBindGuideActivity"
            android:theme="@style/WhiteTheme" />
        <activity
            android:name=".wechat.sport.ui.WeChatSportDeviceBoundActivity"
            android:theme="@style/WhiteTheme" />

        <activity
            android:name=".findfriends.PhoneContactsActivity"
            android:theme="@style/WhiteTheme"
            android:windowSoftInputMode="adjustResize" />

        <!-- IcpRequestJob adds dataSync to the ForegroundInfo -->
        <service android:name="androidx.work.impl.foreground.SystemForegroundService"
            android:foregroundServiceType="dataSync"
            tools:node="merge" />

        <activity
            android:label="@string/account_settings_reset_password_title"
            android:name="com.stt.android.resetpassword.ResetPasswordChinaActivity"
            android:theme="@style/WhiteTheme"/>
        <activity
            android:label="@string/account_settings_delete_account_title"
            android:name="com.stt.android.deleteaccount.DeleteAccountActivity"
            android:theme="@style/WhiteTheme"/>

        <activity
            android:label="@string/new_phone_number"
            android:name="com.stt.android.newphone.NewPhoneChinaActivity"
            android:theme="@style/WhiteTheme"/>
        <activity android:name=".annualreport.AnnualReportActivity"
            android:theme="@style/WhiteTheme"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan"
            tools:ignore="DiscouragedApi" />
        <activity android:name=".annualreport.ShareAnnualReportActivity"
            android:theme="@style/WhiteTheme"
            android:screenOrientation="portrait"
            tools:ignore="DiscouragedApi" />
    </application>
</manifest>
