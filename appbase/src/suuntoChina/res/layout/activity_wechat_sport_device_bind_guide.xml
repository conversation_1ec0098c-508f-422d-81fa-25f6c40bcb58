<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:maxWidth="@dimen/content_max_width">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/layout_appbar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.appcompat.widget.Toolbar
                android:id="@+id/wechat_sport_guide_toolbar"
                style="@style/Toolbar.Native"
                android:theme="@style/Toolbar.Native" />

        </com.google.android.material.appbar.AppBarLayout>

        <androidx.core.widget.NestedScrollView
            android:id="@+id/layout_info"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginBottom="@dimen/size_spacing_large"
            app:layout_constraintBottom_toTopOf="@+id/layout_wechat_device_connect"
            app:layout_constraintTop_toBottomOf="@+id/layout_appbar">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                >

                <ImageView
                    android:id="@+id/image_guide"
                    android:layout_width="match_parent"
                    android:layout_height="294dp"
                    android:layout_marginStart="57dp"
                    android:layout_marginEnd="57dp"
                    android:layout_marginTop="50dp"
                    android:src="@drawable/ic_wechat_sport_guide" />

                <TextView
                    android:id="@+id/wechat_tip"
                    style="@style/Body.Medium.Bold"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="30dp"
                    android:text="@string/connect_wc_sport"
                    android:layout_gravity="center_horizontal"/>

                <TextView
                    style="@style/Body.Larger"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="40dp"
                    android:layout_marginTop="20dp"
                    android:layout_marginEnd="40dp"
                    android:gravity="center"
                    android:lineSpacingExtra="4dp"
                    android:text="@string/wc_sport_desc"
                    />
            </LinearLayout>
        </androidx.core.widget.NestedScrollView>

        <LinearLayout
            android:id="@+id/layout_wechat_device_connect"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent">

            <Button
                android:id="@+id/wechat_device_connect"
                style="@style/Body.Medium.Bold"
                android:layout_width="match_parent"
                android:layout_height="@dimen/large_button_height"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="16dp"
                android:layout_marginBottom="40dp"
                android:background="@drawable/bg_wechat_action"
                android:text="@string/wc_connect"
                android:textColor="@color/white" />

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>


</layout>
