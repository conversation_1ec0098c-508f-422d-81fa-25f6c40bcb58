<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="@dimen/size_spacing_medium"
    android:background="@color/white">

    <ImageView
        android:id="@+id/iv_watch"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/icon_watch"
        app:layout_constraintBottom_toBottomOf="@id/watch_sn"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/watch_name"
        style="@style/Body.Larger"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/size_spacing_medium"
        app:layout_constraintStart_toEndOf="@+id/iv_watch"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Suunto Race" />

    <TextView
        android:id="@+id/watch_sn"
        style="@style/Body.Medium.Gray"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/size_spacing_medium"
        app:layout_constraintStart_toEndOf="@+id/iv_watch"
        app:layout_constraintTop_toBottomOf="@+id/watch_name"
        tools:text="SN:123456789" />

    <TextView
        android:id="@+id/control_connect"
        style="@style/Body.Medium.Bold"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/suunto_blue"
        app:layout_constraintBottom_toBottomOf="@id/watch_sn"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:padding="@dimen/size_spacing_medium"
        tools:text="解除连接" />

    <View
        android:id="@+id/divider"
        android:layout_width="match_parent"
        android:layout_height="@dimen/size_divider"
        android:background="@color/cloudy_gray"
        app:layout_constraintTop_toBottomOf="@id/iv_watch"
        android:layout_marginTop="@dimen/size_spacing_medium"
        />

        <TextView
            android:id="@+id/sync_data_title"
            style="@style/Body.Larger"
            android:layout_centerVertical="true"
            android:text="@string/sync_data_wc"
            app:layout_constraintTop_toBottomOf="@+id/divider"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_marginTop="@dimen/size_spacing_medium"
            />

        <com.google.android.material.switchmaterial.SwitchMaterial
            android:id="@+id/switch_sync_data"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:checked="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/sync_data_title"
            android:thumb="@drawable/bg_switch_thumb_selector"
            app:layout_constraintBottom_toBottomOf="@id/sync_hint"
            app:track="@drawable/bg_switch_track_selector" />

    <TextView
        android:id="@+id/sync_hint"
        style="@style/Body.Medium.DarkGray"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/size_spacing_xsmaller"
        app:layout_constraintStart_toStartOf="parent"
        android:text="@string/sync_data_wc_tips"
        app:layout_constraintTop_toBottomOf="@id/sync_data_title"
        app:layout_constraintEnd_toStartOf="@+id/switch_sync_data"
        android:layout_marginEnd="20dp"
   />

</androidx.constraintlayout.widget.ConstraintLayout>
