<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/common_bg"
        android:maxWidth="@dimen/content_max_width">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/layout_appbar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.appcompat.widget.Toolbar
                android:id="@+id/wechat_sport_has_bound_toolbar"
                style="@style/Toolbar.Native"
                android:theme="@style/Toolbar.Native" />

        </com.google.android.material.appbar.AppBarLayout>

        <com.stt.android.utils.EpoxyNonSharingRecyclerView
            android:id="@+id/bound_device_list"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:layout_constraintTop_toBottomOf="@id/layout_appbar"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_constraintBottom_toTopOf="@id/tv_help"
            />

        <TextView
            android:id="@+id/tv_help"
            style="@style/Body.Medium.Bold"
            android:layout_marginBottom="46dp"
            android:gravity="center"
            android:padding="@dimen/size_spacing_smaller"
            android:text="@string/help_data_not_sync"
            android:textColor="@color/suunto_blue"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />


    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
