<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    tools:context="com.stt.android.workouts.sharepreview.WorkoutSharePreviewActivity">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/rootContainer"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <com.stt.android.ui.utils.SmartViewPager
            android:id="@+id/imagePreviewViewPager"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toTopOf="@id/imagesIndicator"
            app:layout_constraintDimensionRatio="1:1"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0"
            app:layout_constraintVertical_chainStyle="packed"
            tools:background="@drawable/ic_default_profile_image_light" />

        <androidx.fragment.app.FragmentContainerView
            android:id="@+id/summary_map_snapshotter"
            android:name="com.stt.android.maps.MapSnapshotterFragment"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintStart_toStartOf="@id/imagePreviewViewPager"
            app:layout_constraintEnd_toEndOf="@id/imagePreviewViewPager"
            app:layout_constraintTop_toTopOf="@id/imagePreviewViewPager"
            app:layout_constraintBottom_toBottomOf="@id/imagePreviewViewPager" />

        <LinearLayout
            android:id="@+id/imagesIndicator"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/size_spacing_small"
            android:gravity="center_horizontal"
            android:orientation="horizontal"
            android:paddingTop="@dimen/smaller_padding"
            android:paddingBottom="@dimen/smaller_padding"
            android:visibility="gone"
            app:layout_constraintBottom_toTopOf="@id/add_photos_hint"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/imagePreviewViewPager"
            tools:background="@color/red"
            tools:height="@dimen/pager_bullet_size"
            tools:layout_width="100dp"
            tools:padding="@dimen/pager_bullet_padding"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/add_photos_hint"
            style="@style/Body.Medium.Gray"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/size_spacing_medium"
            android:text="@string/swipe_left_to_add_photos"
            app:layout_constraintBottom_toTopOf="@id/shareImageBtn"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/imagesIndicator" />

        <Button
            android:id="@+id/shareImageBtn"
            style="@style/Button.Wide"
            android:layout_width="0dp"
            android:layout_marginStart="@dimen/size_spacing_large"
            android:layout_marginTop="@dimen/size_spacing_xlarge"
            android:layout_marginEnd="@dimen/size_spacing_large"
            android:text="@string/share_photo"
            android:textColor="@color/color_text_share_primary_button"
            app:layout_constraintBottom_toTopOf="@id/shareSummaryLink"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:visibility="gone"
            app:layout_constraintTop_toBottomOf="@id/add_photos_hint" />

        <TextView
            android:id="@+id/shareSummaryLink"
            style="@style/HeaderLabel.Medium"
            android:layout_width="0dp"
            android:layout_height="@dimen/height_button"
            android:layout_marginStart="@dimen/size_spacing_large"
            android:layout_marginTop="@dimen/size_spacing_medium"
            android:layout_marginEnd="@dimen/size_spacing_large"
            android:gravity="center"
            android:text="@string/share_activity"
            app:layout_constraintBottom_toTopOf="@id/share3DLink"
            android:textColor="@color/color_text_disabled"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:visibility="gone"
            app:layout_constraintTop_toBottomOf="@id/shareImageBtn"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/share3DLink"
            style="@style/HeaderLabel.Medium"
            android:layout_width="0dp"
            android:layout_height="@dimen/height_button"
            android:layout_marginStart="@dimen/size_spacing_large"
            android:layout_marginTop="@dimen/size_spacing_small"
            android:layout_marginEnd="@dimen/size_spacing_large"
            android:layout_marginBottom="@dimen/size_spacing_medium"
            android:gravity="center"
            android:text="@string/share_3d_video_activity"
            android:textColor="@color/color_text_disabled"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:visibility="gone"
            app:layout_constraintTop_toBottomOf="@id/shareSummaryLink"
            app:layout_constraintBottom_toBottomOf="parent"
            tools:visibility="visible" />

        <View
            android:id="@+id/loadingSpinnerBg"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@color/sportie_loading_view_bg"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/imagePreviewViewPager"
            app:layout_constraintEnd_toEndOf="@id/imagePreviewViewPager"
            app:layout_constraintStart_toStartOf="@id/imagePreviewViewPager"
            app:layout_constraintTop_toTopOf="@id/imagePreviewViewPager" />

        <View
            android:id="@+id/loadingOverlay"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@color/sportie_loading_overlay"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="parent" />

        <ProgressBar
            android:id="@+id/loadingSpinner"
            style="@style/Widget.AppCompat.ProgressBar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/imagePreviewViewPager"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/imagePreviewViewPager" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
