<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <variable
            name="data"
            type="com.stt.android.annualreport.ShareImageData" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:paddingEnd="@dimen/size_spacing_xsmaller"
        android:onClickListener="@{(view) -> data.handleRadioButtonCheckState.invoke(data.imageUri)}">

        <ImageView
            android:id="@+id/item_image"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:imageUri="@{data.imageUri}"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:scaleType="fitStart" />

        <RadioButton
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintEnd_toEndOf="@id/item_image"
            app:layout_constraintTop_toTopOf="@id/item_image"
            android:checked="@{data.checked}"
            android:clickable="false" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>

