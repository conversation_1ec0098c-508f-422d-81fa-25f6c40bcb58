<?xml version="1.0" encoding="utf-8"?>
<layout>
    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="48dp"
        android:paddingBottom="80dp"
        android:background="@color/white"
        xmlns:app="http://schemas.android.com/apk/res-auto">

        <ImageView
            android:layout_width="211dp"
            android:layout_height="78dp"
            android:src="@drawable/ic_wechat_has_bound"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>

