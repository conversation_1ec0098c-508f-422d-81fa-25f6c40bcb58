<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.constraintlayout.widget.ConstraintLayout
        style="@style/RoundedCornerBottomSheetStyle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingBottom="@dimen/size_spacing_xlarge">

        <TextView
            android:id="@+id/bottomSheetShareTitle"
            style="@style/Body.Large.Bold"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/size_spacing_smaller"
            android:padding="@dimen/size_spacing_medium"
            android:text="@string/share"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


        <com.stt.android.utils.EpoxyNonSharingRecyclerView
            android:id="@+id/list"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/size_spacing_smaller"
            app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/bottomSheetShareTitle" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>

