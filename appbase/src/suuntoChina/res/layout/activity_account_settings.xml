<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?suuntoBackground">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/account_settings_appbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:elevation="@dimen/elevation_toolbar"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/account_settings_toolbar"
            style="@style/Toolbar.Native"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:theme="@style/Toolbar.Native" />
    </com.google.android.material.appbar.AppBarLayout>

    <androidx.fragment.app.FragmentContainerView
        android:id="@+id/fragment_container_view"
        android:name="com.stt.android.home.settings.accountsettings.AccountSettingsChinaFragment"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
