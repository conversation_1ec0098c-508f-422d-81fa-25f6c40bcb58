<?xml version="1.0" encoding="utf-8"?>
<androidx.preference.PreferenceScreen xmlns:android="http://schemas.android.com/apk/res/android"
                                      xmlns:stt="http://schemas.android.com/apk/res-auto"
                                      android:key="@string/preference_root">

    <androidx.preference.PreferenceCategory
        android:key="service_category"
        android:layout="@layout/preference_category_header"
        android:title="@string/settings_service">

        <!-- User settings -->
        <androidx.preference.PreferenceScreen
            android:key="@string/user_settings_category"
            android:layout="@layout/preference_item_in_settings"
            android:title="@string/settings_general_user_settings">
            <com.stt.android.home.settings.v2.EditUserProfilePreference
                android:key="@string/preference_new_user_profile"
                android:layout="@layout/preference_item_in_compose" />
            <Preference
                android:key="user_setting_title"
                android:layout="@layout/preference_item_in_settings_title"
                android:title="@string/settings_user_profile_guide_new_v2"
                stt:enabled="false" />
            <com.stt.android.home.settings.WeightDialogPreference
                android:dialogLayout="@layout/pref_dialog_custom"
                android:dialogTitle="@string/dialog_title_settings_general_user_settings_weight"
                android:inputType="numberDecimal"
                android:key="weight"
                android:layout="@layout/preference_item_in_settings"
                android:title="@string/settings_general_user_settings_weight"
                stt:dialogTitleOptionValues="@array/dialog_title_optionvalues_settings_general_user_settings_weight"
                stt:dialogTitleOptions="@array/dialog_title_options_settings_general_user_settings_weight"/>
            <com.stt.android.home.settings.HeightDialogPreference
                android:dialogLayout="@layout/preference_height_setting_dialog"
                android:dialogTitle="@string/dialog_title_settings_general_user_settings_height"
                android:inputType="numberDecimal"
                android:key="height"
                android:layout="@layout/preference_item_in_settings"
                android:title="@string/settings_general_user_settings_height" />
            <com.stt.android.home.settings.GenderPreference
                android:defaultValue="male"
                android:key="gender"
                android:layout="@layout/preference_item_in_settings"
                android:title="@string/settings_general_user_settings_gender"
                stt:titleSummaryListPreferenceEntries="@array/entries_settings_general_user_settings_gender"
                stt:titleSummaryListPreferenceEntryValues="@array/entryvalues_settings_general_user_settings_gender"/>
            <com.stt.android.home.settings.AgePreference
                android:dialogTitle="@string/birth_year"
                android:inputType="number"
                android:key="birth_date"
                android:layout="@layout/preference_item_in_settings"
                android:title="@string/birth_year"/>
            <com.stt.android.home.settings.MaxHeartPickerDialogPreference
                android:defaultValue="180"
                android:key="@string/user_max_heart_rate_setting"
                android:layout="@layout/preference_item_in_settings"
                android:summary="@string/max_bpm"
                android:title="@string/settings_general_user_settings_max_heart" />
            <com.stt.android.home.settings.RestHeartPickerDialogPreference
                android:defaultValue="50"
                android:key="@string/rest_heart_rate_setting"
                android:layout="@layout/preference_item_in_settings"
                android:summary="@string/max_bpm"
                android:title="@string/settings_general_user_settings_rest_heart" />
        </androidx.preference.PreferenceScreen>

        <!-- Account settings (reset password, delete account) -->
        <androidx.preference.Preference
            android:key="@string/account_settings"
            android:layout="@layout/preference_item_in_settings"
            android:title="@string/account_settings_title">
            <intent
                android:targetClass="com.stt.android.home.settings.accountsettings.AccountSettingsActivity"
                android:targetPackage="@string/appId"/>
        </androidx.preference.Preference>

        <Preference
            android:key="privacy"
            android:layout="@layout/preference_item_in_settings"
            android:title="@string/settings_privacy_setting">
            <intent
                android:targetClass="com.stt.android.ui.activities.settings.PrivacySettingsActivity"
                android:targetPackage="@string/appId"/>
        </Preference>
    </androidx.preference.PreferenceCategory>

    <androidx.preference.PreferenceCategory
        android:key="@string/activity_training_category"
        android:layout="@layout/preference_category_header"
        android:title="@string/settings_activity_training">

        <!-- hr zones settings -->
        <androidx.preference.PreferenceScreen
            android:key="@string/hr_intensity_zones"
            android:layout="@layout/preference_item_in_settings"
            android:order="1"
            android:title="@string/settings_general_hr_intensity_zones">
            <Preference
                android:key="@string/setting_key_hr_zones"
                android:layout="@layout/preference_item_in_settings"
                android:order="0"
                android:title="@string/hr_zones">
                <intent
                    android:targetClass="com.stt.android.home.settings.zones.hr.HrZonesTypeActivity"
                    android:targetPackage="@string/appId" />
            </Preference>

            <Preference
                android:key="@string/setting_key_pace_zones"
                android:layout="@layout/preference_item_in_zones"
                android:order="1"
                android:summary="@string/pace_zones_summary"
                android:title="@string/pace_zones">
                <intent
                    android:targetClass="com.stt.android.home.settings.zones.ZonesActivity"
                    android:targetPackage="@string/appId">
                    <extra
                        android:name="extra_title"
                        android:value="@string/running_pace_zones" />
                    <extra
                        android:name="extra_zone_type"
                        android:value="3" />
                </intent>
            </Preference>

            <Preference
                android:key="@string/setting_key_power_zones"
                android:layout="@layout/preference_item_in_zones"
                android:order="2"
                android:summary="@string/power_zones_summary"
                android:title="@string/power_zones">
                <intent
                    android:targetClass="com.stt.android.home.settings.zones.ZonesPowerListActivity"
                    android:targetPackage="@string/appId" />
            </Preference>
        </androidx.preference.PreferenceScreen>

        <!-- Heart rate belt settings -->
        <androidx.preference.PreferenceScreen
            android:key="@string/heart_rate_screen"
            android:layout="@layout/title_summary_preference"
            android:title="@string/settings_general_pair_hr_sensor"
            android:order="2">

            <androidx.preference.PreferenceCategory
                android:layout="@layout/preference_category_header"
                android:title="@string/settings_general_heart_rate_belt_connect">
                <Preference
                    android:layout="@layout/title_summary_preference"
                    android:summary="@string/settings_general_heart_rate_belt_connect_setup_summary"
                    android:title="@string/settings_general_heart_rate_belt">
                    <intent
                        android:targetClass="com.stt.android.ui.activities.SetupHeartRateBeltActivity"
                        android:targetPackage="@string/appId"/>
                </Preference>
            </androidx.preference.PreferenceCategory>

            <androidx.preference.PreferenceCategory
                android:layout="@layout/preference_category_header"
                android:title="@string/settings_general_heart_rate_belt_recording_options">
                <com.stt.android.home.settings.MaxHeartRatePreference
                    android:defaultValue="180"
                    android:dialogLayout="@layout/pref_dialog_custom"
                    android:key="@string/max_heart_rate_setting"
                    android:layout="@layout/heart_rate_preference"
                    android:summary="@string/settings_general_heart_rate_belt_recording_options_maximum_summary"
                    android:title="@string/settings_general_heart_rate_belt_recording_options_maximum"/>

            </androidx.preference.PreferenceCategory>

            <androidx.preference.PreferenceCategory
                android:layout="@layout/preference_category_header"
                android:title="@string/shop_online">
                <Preference
                    android:key="key_promo_suunto_sensor"
                    android:layout="@layout/promo_suunto_sensor_preference"
                    android:summary="@string/shop_hrm_summary"
                    android:title="@null">
                    <intent
                        android:action="android.intent.action.VIEW"
                        android:data="@string/shop_url_hr_sensor_settings"/>
                </Preference>
            </androidx.preference.PreferenceCategory>
        </androidx.preference.PreferenceScreen>

    </androidx.preference.PreferenceCategory>


    <androidx.preference.PreferenceCategory
        android:key="@string/outdoor_category"
        android:layout="@layout/preference_category_header"
        android:title="@string/settings_outdoor">

        <!-- What sensor use to get altitude -->
        <com.stt.android.home.settings.TitleListPreference
            android:defaultValue="gps"
            android:key="@string/altitude_source_preference"
            android:layout="@layout/title_summary_preference"
            android:title="@string/settings_altitude_source"
            stt:titleSummaryListPreferenceEntries="@array/entries_settings_altitude_source"
            stt:titleSummaryListPreferenceEntryValues="@array/entry_values_settings_altitude_source"/>


        <!-- Altitude offset -->
        <com.stt.android.home.settings.AltitudeOffsetDialogPreference
            android:defaultValue="0"
            android:dialogLayout="@layout/pref_dialog_custom"
            android:dialogTitle="@string/dialog_title_settings_general_altitude_offset"
            android:inputType="numberDecimal|numberSigned"
            android:key="@string/altitude_offset_preference"
            android:layout="@layout/preference_item_in_settings"
            android:title="@string/settings_general_altitude_offset"
            stt:dialogTitleOptionValues="@array/dialog_title_optionvalues_settings_general_altitude_offset"
            stt:dialogTitleOptions="@array/dialog_title_options_settings_general_altitude_offset"/>

    </androidx.preference.PreferenceCategory>

    <androidx.preference.PreferenceCategory
        android:key="@string/general_category"
        android:layout="@layout/preference_category_header"
        android:title="@string/settings_general">
        <com.stt.android.home.settings.MeasurementUnitPreference
            android:defaultValue="metric"
            android:key="measurement_unit"
            android:layout="@layout/preference_item_in_settings"
            android:title="@string/settings_general_measurement_unit"
            stt:titleSummaryListPreferenceEntries="@array/entries_settings_general_measurement_unit"
            stt:titleSummaryListPreferenceEntryValues="@array/entryvalues_settings_general_measurement_unit"/>

        <com.stt.android.home.settings.FirstDayOfWeekPreference
            android:defaultValue="monday"
            android:key="firstDayOfTheWeek"
            android:layout="@layout/preference_item_in_settings"
            android:title="@string/settings_general_first_day_of_the_week"
            stt:titleSummaryListPreferenceEntries="@array/entries_settings_general_first_day_of_the_week"
            stt:titleSummaryListPreferenceEntryValues="@array/entryvalues_settings_general_first_day_of_the_week"/>

        <!-- Cadence settings -->
        <androidx.preference.PreferenceScreen
            android:key="@string/cadence_screen"
            android:layout="@layout/preference_item_in_settings"
            android:summary="@string/settings_cadence_summary"
            android:title="@string/settings_cadence_title">
            <androidx.preference.PreferenceCategory
                android:layout="@layout/preference_category_header"
                android:title="@string/settings_cadence_connect">
                <Preference
                    android:layout="@layout/preference_item_in_settings"
                    android:summary="@string/settings_cadence_summary"
                    android:title="@string/settings_cadence_title">
                    <intent
                        android:targetClass="com.stt.android.ui.activities.SetupCadenceActivity"
                        android:targetPackage="@string/appId"/>
                </Preference>
            </androidx.preference.PreferenceCategory>

            <androidx.preference.PreferenceCategory
                android:layout="@layout/preference_category_header"
                android:title="@string/settings_cadence_sensor_settings">
                <com.stt.android.home.settings.WheelCircumferencePreference
                    android:defaultValue="1800"
                    android:dialogLayout="@layout/pref_dialog_custom"
                    android:dialogTitle="@string/settings_cadence_wheel_circumference_title"
                    android:inputType="number"
                    android:key="wheel_circumference"
                    android:layout="@layout/preference_item_in_settings"
                    android:summary="@string/settings_cadence_wheel_circumference_summary"
                    android:title="@string/settings_cadence_wheel_circumference_title"/>
                <com.stt.android.home.settings.TitleListPreference
                    android:defaultValue="cadence"
                    android:key="cadence_data_source"
                    android:layout="@layout/preference_item_in_settings"
                    android:summary="@string/settings_cadence_data_source_summary"
                    android:title="@string/settings_cadence_data_source_title"
                    stt:titleSummaryListPreferenceEntries="@array/entries_settings_general_cadence_data_source"
                    stt:titleSummaryListPreferenceEntryValues="@array/entryvalues_settings_general_cadence_data_source"/>
                <Preference
                    android:key="cadence_total_distance"
                    android:layout="@layout/preference_item_in_settings"
                    android:title="@string/distance"/>
            </androidx.preference.PreferenceCategory>
            <!-- Hide the link to online shop, until we have something to sell
            <androidx.preference.PreferenceCategory
                android:title="@string/shop_online"
                android:layout="@layout/preference_category_header">
                <Preference
                    android:summary="@string/shop_cadence_summary"
                    android:title="@string/order_now"
                    android:layout="@layout/preference_item_in_settings">
                    <intent
                        android:action="android.intent.action.VIEW"
                        android:data="https://shop.sports-tracker.com/products/speed-and-cadence.html" />
                </Preference>
            </androidx.preference.PreferenceCategory>
            -->
        </androidx.preference.PreferenceScreen>

        <!-- Menstrual cycle settings -->
        <androidx.preference.Preference
            android:key="@string/menstrual_cycle"
            android:layout="@layout/preference_item_in_settings"
            android:title="@string/settings_menstrual_cycle">
            <intent
                android:targetClass="com.stt.android.menstrualcycle.settings.MenstrualCycleSettingsActivity"
                android:targetPackage="@string/appId"/>
        </androidx.preference.Preference>

        <Preference
            android:key="tags_key"
            android:layout="@layout/preference_item_in_settings"
            android:title="@string/settings_tags">
            <intent
                android:targetClass="com.stt.android.ui.activities.settings.tags.TagsSettingsActivity"
                android:targetPackage="@string/appId" />
        </Preference>

        <com.stt.android.home.settings.TitleListPreference
            android:defaultValue="mapbox"
            android:key="@string/map_provider_preference"
            android:layout="@layout/preference_item_in_settings"
            android:title="@string/settings_general_map_provider"
            stt:titleSummaryListPreferenceEntries="@array/entries_settings_general_map_provider"
            stt:titleSummaryListPreferenceEntryValues="@array/entry_values_settings_general_map_provider"/>

        <!-- Backlight -->
        <com.stt.android.home.settings.TitleListPreference
            android:defaultValue="automatic"
            android:key="@string/screen_backlight_preference"
            android:layout="@layout/preference_item_in_settings"
            android:title="@string/settings_general_screen_backlight"
            stt:titleSummaryListPreferenceEntries="@array/entries_settings_general_screen_backlight"
            stt:titleSummaryListPreferenceEntryValues="@array/entryvalues_settings_general_screen_backlight"/>

        <Preference
            android:key="@string/power_management_key"
            android:layout="@layout/title_summary_preference"
            android:summary="@string/power_management_preference_summary"
            android:title="@string/power_management_preference">
            <intent
                android:targetClass="com.stt.android.ui.activities.settings.PowerManagementSettingsActivity"
                android:targetPackage="@string/appId"/>
        </Preference>

    </androidx.preference.PreferenceCategory>

    <androidx.preference.PreferenceCategory
        android:key="@string/notification_category"
        android:layout="@layout/preference_category_header"
        android:title="@string/settings_notification">

        <com.stt.android.home.settings.NotificationSettingsPreference
            android:layout="@layout/preference_item_in_settings"
            android:title="@string/settings_push_notifications"/>

    </androidx.preference.PreferenceCategory>

    <androidx.preference.PreferenceCategory
        android:key="@string/about_category"
        android:layout="@layout/preference_category_header"
        android:title="@string/settings_about_app">
        <Preference
            android:layout="@layout/preference_item_in_settings"
            android:order="1"
            android:title="@string/settings_other_blog">
            <intent
                android:action="android.intent.action.VIEW"
                android:data="@string/app_blog_url"/>
        </Preference>

        <Preference
            android:key="send_log"
            android:order="6"
            android:layout="@layout/preference_item_in_settings"
            android:title="@string/watch_menu_send_logs"/>

        <com.stt.android.home.settings.VersionPreference
            android:key="app_version"
            android:layout="@layout/app_version_preference"
            android:order="7"
            android:title="@string/settings_other_app_update"/>

        <com.stt.android.home.settings.CustomTabPreference
            android:key="service_terms"
            android:layout="@layout/preference_item_in_settings"
            android:order="8"
            android:title="@string/settings_service_terms"
            stt:targetUrl="@string/service_terms_url"
            stt:analyticsEvent="AboutServiceTerms" />

        <com.stt.android.home.settings.CustomTabPreference
            android:key="privacy_policy"
            android:layout="@layout/preference_item_in_settings"
            android:order="9"
            android:title="@string/settings_privacy_policy"
            stt:targetUrl="@string/privacy_policy_url"
            stt:analyticsEvent="AboutPrivacyPolicy" />

        <com.stt.android.home.settings.CustomTabPreference
            android:key="data_practices"
            android:layout="@layout/preference_item_in_settings"
            android:order="10"
            android:title="@string/settings_data_practices"
            stt:targetUrl="@string/data_practices_url"
            stt:analyticsEvent="AboutDataPractices" />

        <Preference
            android:key="license"
            android:layout="@layout/preference_item_in_settings"
            android:order="11"
            android:title="@string/settings_open_source_licenses_title">
            <intent
                android:targetClass="com.google.android.gms.oss.licenses.OssLicensesMenuActivity"
                android:targetPackage="@string/appId"/>
        </Preference>

        <Preference
            android:key="filing_number"
            android:layout="@layout/preference_item_in_settings"
            android:order="12"
            stt:enableCopying="true"
            android:summary="@string/settings_other_filing_number_summary"
            android:title="@string/settings_other_filing_number">
            <intent
                android:action="android.intent.action.VIEW"
                android:data="@string/settings_other_filing_number_url"/>
        </Preference>

        <com.stt.android.home.settings.LogoutPreference
            android:key="log_out"
            android:layout="@layout/preference_item_for_logout"
            android:order="12"
            android:title="@string/positive_button_settings_service_sign_out"/>

    </androidx.preference.PreferenceCategory>
</androidx.preference.PreferenceScreen>
