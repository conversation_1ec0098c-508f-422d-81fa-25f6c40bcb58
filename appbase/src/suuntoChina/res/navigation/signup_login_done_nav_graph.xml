<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/signup_login_done_graph.xml"
    app:startDestination="@id/welcomeFragment">

    <fragment
        android:id="@+id/welcomeFragment"
        android:name="com.stt.android.login.signuplogindone.WelcomeFragment"
        android:label="WelcomeFragment"
        tools:layout="@layout/fragment_welcome">
        <action
            android:id="@+id/action_welcomeFragment_to_requestPermissionFragment"
            app:destination="@id/requestPermissionFragment"
            app:enterAnim="@anim/slide_in_right_to_left"
            app:popEnterAnim="@anim/slide_in_left_to_right" />
        <action
            android:id="@+id/action_welcomeFragment_to_newsletterSubscriptionFragment"
            app:destination="@id/newsletterSubscriptionFragment"
            app:enterAnim="@anim/slide_in_right_to_left"
            app:popEnterAnim="@anim/slide_in_left_to_right" />
        <action
            android:id="@+id/action_welcomeFragment_to_askNotificationFragment"
            app:destination="@id/askNotificationFragment"
            app:enterAnim="@anim/slide_in_right_to_left"
            app:popEnterAnim="@anim/slide_in_left_to_right" />
    </fragment>
    <fragment
        android:id="@+id/requestPermissionFragment"
        android:name="com.stt.android.login.requestpermission.RequestPermissionFragment"
        android:label="RequestPermissionFragment"
        tools:layout="@layout/fragment_request_permission">
        <action
            android:id="@+id/action_requestPermissionFragment_to_newsletterSubscriptionFragment"
            app:destination="@id/newsletterSubscriptionFragment"
            app:enterAnim="@anim/slide_in_right_to_left"
            app:popEnterAnim="@anim/slide_in_left_to_right" />
        <action
            android:id="@+id/action_requestPermissionFragment_to_askNotificationFragment"
            app:destination="@id/askNotificationFragment"
            app:enterAnim="@anim/slide_in_right_to_left"
            app:popEnterAnim="@anim/slide_in_left_to_right" />
    </fragment>
    <fragment
        android:id="@+id/askNotificationFragment"
        android:name="com.stt.android.login.notification.AskNotificationFragment"
        android:label="AskNotificationFragment"
        tools:layout="@layout/fragment_ask_notification">
        <action
            android:id="@+id/action_askNotificationFragment_to_newsletterSubscriptionFragment"
            app:destination="@id/newsletterSubscriptionFragment"
            app:enterAnim="@anim/slide_in_right_to_left"
            app:popEnterAnim="@anim/slide_in_left_to_right" />
    </fragment>
    <fragment
        android:id="@+id/newsletterSubscriptionFragment"
        android:name="com.stt.android.login.newsletter.NewsletterSubscriptionChinaFragment"
        android:label="NewsletterSubscriptionFragment"
        tools:layout="@layout/fragment_newsletter_subscription" >
        <argument
            android:name="isSignUp"
            app:argType="boolean" />
        <action
            android:id="@+id/action_newsletterSubscriptionFragment_to_thankYouFragment"
            app:destination="@id/thankYouFragment"
            app:enterAnim="@anim/slide_in_right_to_left"
            app:popEnterAnim="@anim/slide_in_left_to_right" />

        <action
            android:id="@+id/action_bindEmailWhenSubscribe"
            app:destination="@id/bindEmailWhenSubscribe"
            app:enterAnim="@anim/slide_in_right_to_left"
            app:popEnterAnim="@anim/slide_in_left_to_right" />

        <action android:id="@+id/action_bindEmailVerifyCodeWhenSubscribe"
            app:destination="@id/bindEmailVerifyCodeWhenSubscribe"
            app:enterAnim="@anim/fade_in"
            app:exitAnim="@anim/fade_out" />

        <action android:id="@+id/action_setPasswordWhenSubscribeFragment"
            app:destination="@id/setPasswordWhenSubscribe"
            app:enterAnim="@anim/fade_in"
            app:exitAnim="@anim/fade_out" />
    </fragment>

    <fragment
        android:id="@+id/bindEmailWhenSubscribe"
        android:name="com.stt.android.login.bindemail.FragmentBindEmailWhenSubscribe"
        android:label="fragment_bind_email_when_subscribe">

        <action android:id="@+id/action_bindEmailVerifyCodeWhenSubscribe"
            app:destination="@id/bindEmailVerifyCodeWhenSubscribe"
            app:enterAnim="@anim/fade_in"
            app:exitAnim="@anim/fade_out" />

    </fragment>

    <fragment
        android:id="@+id/bindEmailVerifyCodeWhenSubscribe"
        android:name="com.stt.android.login.bindemail.FragmentBindEmailVerifyCodeWhenSubscribe"
        android:label="fragment_bind_email_verify_code_when_subscribe">

        <argument
            android:name="email"
            android:defaultValue=""
            app:argType="string"
            app:nullable="false" />

        <action android:id="@+id/action_setPasswordWhenSubscribeFragment"
            app:destination="@id/setPasswordWhenSubscribe"
            app:enterAnim="@anim/fade_in"
            app:exitAnim="@anim/fade_out" />

    </fragment>

    <fragment
        android:id="@+id/setPasswordWhenSubscribe"
        android:name="com.stt.android.login.bindemail.FragmentSetPasswordWhenSubscribe"
        android:label="fragment_set_password_when_subscribe">

        <argument
            android:name="email"
            android:defaultValue=""
            app:argType="string"
            app:nullable="false" />

        <argument
            android:name="token"
            android:defaultValue=""
            app:argType="string"
            app:nullable="false" />

    </fragment>
    <fragment
        android:id="@+id/thankYouFragment"
        android:name="com.stt.android.login.thankyou.ThankYouFragment"
        android:label="ThankYouFragment"
        tools:layout="@layout/fragment_thank_you" />
</navigation>
