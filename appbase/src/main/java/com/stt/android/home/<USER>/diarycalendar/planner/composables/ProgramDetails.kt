package com.stt.android.home.diary.diarycalendar.planner.composables

import androidx.annotation.ColorRes
import androidx.annotation.DrawableRes
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Button
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalInspectionMode
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.pluralStringResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import coil3.compose.AsyncImage
import coil3.request.ImageRequest
import coil3.request.crossfade
import com.stt.android.compose.component.SuuntoActivityIcon
import com.stt.android.compose.component.SuuntoIconButton
import com.stt.android.compose.markdown.MarkdownDocument
import com.stt.android.compose.markdown.appendPlainText
import com.stt.android.compose.modifiers.narrowContentWithBgColors
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.SuuntoIcons
import com.stt.android.compose.theme.dividerColor
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.material3.bodyLargeBold
import com.stt.android.compose.theme.material3.bodyXLargeBold
import com.stt.android.compose.theme.material3.header
import com.stt.android.compose.theme.spacing
import com.stt.android.core.domain.workouts.CoreActivityType
import com.stt.android.home.diary.diarycalendar.planner.composables.infosheets.InfoSheet
import com.stt.android.home.diary.diarycalendar.planner.models.AnsweredQuestion
import com.stt.android.home.diary.diarycalendar.planner.models.EventInfoUiState
import com.stt.android.home.diary.diarycalendar.planner.models.ProgramDetailsUiState
import com.stt.android.home.diary.diarycalendar.planner.models.Question
import com.stt.android.home.diary.diarycalendar.planner.models.fakeProgramDetailsUiState
import com.stt.android.workoutdetail.workoutvalues.WorkoutValuesGridItemData
import com.stt.android.workoutdetail.workoutvalues.composables.WorkoutValuesGrid
import com.stt.android.workoutdetail.workoutvalues.composables.WorkoutValuesGridItem
import com.stt.android.workouts.domain.R
import kotlinx.collections.immutable.ImmutableSet
import kotlinx.collections.immutable.persistentSetOf
import org.commonmark.node.Document
import org.commonmark.parser.Parser
import java.util.Locale
import com.stt.android.R as BaseR

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ProgramDetails(
    uiState: ProgramDetailsUiState,
    onPersonalizeThisProgramClick: () -> Unit,
    onBackClick: () -> Unit,
    modifier: Modifier = Modifier,
    category: String? = null,
) {
    Scaffold(
        modifier = modifier,
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = uiState.title.uppercase()
                    )
                },
                navigationIcon = {
                    SuuntoIconButton(
                        icon = SuuntoIcons.ActionBack,
                        onClick = onBackClick,
                    )
                }
            )
        }
    ) { internalPadding ->
        Surface(
            modifier = Modifier
                .padding(internalPadding)
                .narrowContentWithBgColors(
                    backgroundColor = MaterialTheme.colorScheme.surface,
                    outerBackgroundColor = MaterialTheme.colorScheme.background
                )
        ) {
            ProgramDetailsContent(
                title = uiState.title,
                description = uiState.description,
                richInfo = uiState.richInfo ?: "",
                durationInWeeks = uiState.numberOfWeeks,
                sports = uiState.sports,
                onPersonalizeThisProgramClick = onPersonalizeThisProgramClick,
                onDeletePlanClick = { },
                imageUrl = uiState.imageUrl,
                questions = uiState.questions,
                focus = uiState.focus,
                level = uiState.level,
                eventInfoUiState = uiState.eventInfoUiState,
                enablePersonalize = uiState.enablePersonalize,
                category = category,
            )
        }
    }
}

@Composable
fun ProgramDetailsContent(
    title: String,
    description: String,
    richInfo: String,
    durationInWeeks: Int,
    sports: ImmutableSet<CoreActivityType>,
    onPersonalizeThisProgramClick: () -> Unit,
    onDeletePlanClick: () -> Unit,
    modifier: Modifier = Modifier,
    imageUrl: String? = null,
    answers: List<AnsweredQuestion> = emptyList(),
    questions: List<Question> = emptyList(),
    isActive: Boolean? = null,
    focus: String? = null,
    level: String? = null,
    eventInfoUiState: EventInfoUiState? = null,
    enablePersonalize: Boolean? = null,
    category: String? = null,
) {
    Column(
        modifier = modifier
            .fillMaxSize()
    ) {
        Column(
            modifier = Modifier
                .weight(1f)
                .verticalScroll(rememberScrollState())
        ) {
            Program(
                title = title,
                description = description,
                richInfo = richInfo,
                durationInWeeks = durationInWeeks,
                sports = sports,
                onDeletePlanClick = onDeletePlanClick,
                imageUrl = imageUrl,
                answers = answers,
                questions = questions,
                isActive = isActive,
                focus = focus,
                level = level,
                category = category,
            )
            if (eventInfoUiState != null) {
                EventInfo(
                    eventInfoUiState = eventInfoUiState
                )
            }
        }
        if (enablePersonalize != null) {
            Box(
                modifier = Modifier
                    .height(80.dp)
                    .fillMaxWidth()
                    .padding(MaterialTheme.spacing.medium), contentAlignment = Alignment.Center
            ) {
                Button(
                    onClick = onPersonalizeThisProgramClick,
                    modifier = Modifier.fillMaxWidth(),
                    enabled = enablePersonalize,
                    shape = MaterialTheme.shapes.small,
                ) {
                    Text(
                        text = stringResource(BaseR.string.workout_planner_personalize).uppercase(
                            Locale.getDefault()
                        ),
                    )
                }
            }
        }
    }
}

@Composable
private fun EventInfo(eventInfoUiState: EventInfoUiState, modifier: Modifier = Modifier) {
    val items =
        listOfNotNull(
            eventInfoUiState.ascentValue?.let {
                val ascentUnitStr = eventInfoUiState.ascentUnit?.let { unit ->
                    stringResource(unit)
                } ?: ""
                WorkoutValuesGridItemData(
                    name = stringResource(BaseR.string.workout_planner_event_info_ascent),
                    value = "$it $ascentUnitStr"
                )
            },
            eventInfoUiState.date.let {
                WorkoutValuesGridItemData(
                    name = stringResource(BaseR.string.workout_planner_event_info_date),
                    value = formatLocalDate(it)
                )
            },
            eventInfoUiState.distance?.let {
                val distanceUnitStr = eventInfoUiState.distanceUnit?.let {
                    stringResource(it)
                } ?: ""
                WorkoutValuesGridItemData(
                    name = stringResource(BaseR.string.workout_planner_distance),
                    value = "${eventInfoUiState.distance} $distanceUnitStr"
                )
            },
            eventInfoUiState.terrain?.let {
                WorkoutValuesGridItemData(
                    name = stringResource(BaseR.string.workout_planner_event_info_terrain),
                    value = it
                )
            },
            eventInfoUiState.weather?.let {
                WorkoutValuesGridItemData(
                    name = stringResource(BaseR.string.workout_planner_event_info_weather),
                    value = it
                )
            }
        )
    val rows = (items.size + 1) / 2
    Column(modifier = modifier) {
        Text(
            text = stringResource(BaseR.string.workout_planner_event_info),
            style = MaterialTheme.typography.bodyXLargeBold,
            modifier = Modifier.padding(MaterialTheme.spacing.medium)
        )
        Spacer(modifier = Modifier.height(MaterialTheme.spacing.small))
        Text(
            text = eventInfoUiState.name,
            style = MaterialTheme.typography.bodyXLargeBold,
            modifier = Modifier.padding(MaterialTheme.spacing.medium)
        )
        RichDescription(
            description = eventInfoUiState.richInfo, // No description for event info
            richInfo = eventInfoUiState.richInfo,
            modifier = Modifier.padding(
                start = MaterialTheme.spacing.medium,
                end = MaterialTheme.spacing.medium,
                bottom = MaterialTheme.spacing.medium,
            )
        )
        WorkoutValuesGrid(
            items = items,
            rows = rows,
            onValueClicked = {}
        )
    }
}

@Composable
private fun Program(
    title: String,
    description: String,
    richInfo: String,
    durationInWeeks: Int,
    sports: ImmutableSet<CoreActivityType>,
    modifier: Modifier = Modifier,
    onDeletePlanClick: (() -> Unit)? = null,
    imageUrl: String? = null,
    answers: List<AnsweredQuestion> = emptyList(),
    questions: List<Question> = emptyList(),
    isActive: Boolean? = null,
    focus: String? = null,
    level: String? = null,
    category: String? = null,
) {
    Column(modifier = modifier.fillMaxWidth()) {
        if (imageUrl != null) {
            AsyncImage(
                model = ImageRequest.Builder(LocalContext.current)
                    .data(
                        if (LocalInspectionMode.current) R.drawable.activity_placeholder_cycling
                        else imageUrl
                    )
                    .crossfade(true)
                    .build(),
                contentDescription = null,
                placeholder = if (LocalInspectionMode.current) {
                    painterResource(R.drawable.activity_placeholder_cycling)
                } else {
                    null
                },
                modifier = Modifier
                    .height(190.dp)
                    .fillMaxWidth(),
                contentScale = ContentScale.Crop,
            )
        }
        Text(
            text = title,
            style = MaterialTheme.typography.bodyXLargeBold,
            modifier = Modifier.padding(MaterialTheme.spacing.medium)
        )
        RichDescription(
            description = description,
            richInfo = richInfo,
            modifier = Modifier.padding(MaterialTheme.spacing.medium)
        )
        if (isActive != null) {
            Row(
                modifier = Modifier.padding(
                    start = MaterialTheme.spacing.medium,
                    top = MaterialTheme.spacing.small,
                    end = MaterialTheme.spacing.small,
                    bottom = MaterialTheme.spacing.small
                ),
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Text(
                    text = stringResource(BaseR.string.workout_planner_plan),
                    modifier = Modifier.weight(1f),
                    style = MaterialTheme.typography.bodyXLargeBold,
                )
                TextButton(
                    onClick = { onDeletePlanClick?.invoke() },
                    enabled = isActive
                ) {
                    Text(
                        text = stringResource(BaseR.string.workout_planner_end_program).uppercase(),
                        style = MaterialTheme.typography.header,
                    )
                }
            }
        }
        HorizontalDivider(color = MaterialTheme.colorScheme.dividerColor)
        if (category != null) {
            WorkoutValuesGridItem(
                workoutValueGridItem = WorkoutValuesGridItemData(
                    name = stringResource(com.stt.android.R.string.workout_planner_category),
                    value = category,
                ),
                onValueClicked = {},
            )
            HorizontalDivider(color = MaterialTheme.colorScheme.dividerColor)
        }
        if (focus != null) {
            WorkoutValuesGridItem(
                workoutValueGridItem = WorkoutValuesGridItemData(
                    name = stringResource(com.stt.android.R.string.workout_planner_focus),
                    value = focus,
                ),
                onValueClicked = {},
            )
            HorizontalDivider(color = MaterialTheme.colorScheme.dividerColor)
        }
        if (level != null) {
            var showLevelInfoSheet by rememberSaveable { mutableStateOf(false) }
            WorkoutValuesGridItem(
                workoutValueGridItem = WorkoutValuesGridItemData(
                    name = stringResource(com.stt.android.R.string.workout_planner_level),
                    value = level,
                    showMoreInfoIcon = true,
                ),
                onValueClicked = { showLevelInfoSheet = true },
            )
            InfoSheet(
                sheetHtmlContent = BaseR.string.workout_planner_info_level,
                show = showLevelInfoSheet,
                onDismissRequest = { showLevelInfoSheet = false }
            )
            HorizontalDivider(color = MaterialTheme.colorScheme.dividerColor)
        }
        WorkoutValuesGridItem(
            workoutValueGridItem = WorkoutValuesGridItemData(
                name = stringResource(com.stt.android.R.string.workout_planner_duration),
                value = pluralStringResource(
                    com.stt.android.R.plurals.workout_planner_number_of_weeks,
                    durationInWeeks,
                    durationInWeeks,
                ),
            ),
            onValueClicked = {},
        )
        HorizontalDivider(color = MaterialTheme.colorScheme.dividerColor)
        ProgramSports(
            sports = sports,
            modifier = Modifier.padding(MaterialTheme.spacing.medium),
            isCustomizable = answers.isEmpty() && questions.any { it is Question.Sports },
        )
        if (answers.isNotEmpty()) {
            PersonalizedDetails(answeredQuestions = answers)
        } else {
            PersonalizationQuestions(questions = questions)
        }
    }
}

@Composable
private fun RichDescription(
    description: String?,
    richInfo: String?,
    modifier: Modifier = Modifier
) {
// Show rich text description, if available, or fall back to plain text description.
    val markdownDocument = remember(richInfo, description) {
        if (richInfo != null) {
            Parser.Builder().build().parse(richInfo)
        } else {
            // Plain text description as document without parsing for MD syntax
            Document().appendPlainText(description.orEmpty())
        }
    }

    MarkdownDocument(
        rootNode = markdownDocument,
        modifier = modifier
            .padding(top = MaterialTheme.spacing.small),
    )
}

@Composable
private fun ProgramSports(
    sports: ImmutableSet<CoreActivityType>,
    modifier: Modifier = Modifier,
    isCustomizable: Boolean = false
) {
    Column(
        modifier = modifier,
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.small)
    ) {
        Text(
            text = stringResource(com.stt.android.R.string.workout_planner_sports),
            style = MaterialTheme.typography.bodySmall
        )
        if (sports.isNotEmpty()) {
            sports.forEach {
                ProgramSportListItem(
                    name = stringResource(it.nameRes),
                    colorRes = it.color,
                    iconRes = it.icon
                )
            }
            if (isCustomizable) {
                Text(
                    text = stringResource(BaseR.string.workout_planner_customizable_sports),
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.secondary,
                )
            }
        } else {
            Column(verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.xsmall)) {
                Text(
                    text = stringResource(BaseR.string.workout_planner_choose_your_own),
                    style = MaterialTheme.typography.bodyLarge,
                )
                Text(
                    text = stringResource(BaseR.string.workout_planner_customizable_sports),
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.secondary,
                )
            }
        }
    }
}

@Composable
private fun ProgramSportListItem(
    name: String,
    @ColorRes colorRes: Int,
    @DrawableRes iconRes: Int,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier,
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.small)
    ) {
        SuuntoActivityIcon(
            iconRes = iconRes,
            tint = MaterialTheme.colorScheme.surface,
            background = colorResource(colorRes),
            iconSize = MaterialTheme.iconSizes.small,
        )
        Text(text = name, style = MaterialTheme.typography.bodyLargeBold)
    }
}

@Preview(showBackground = true)
@Composable
private fun ProgramSportsPreview() {
    M3AppTheme {
        Column(
            modifier = Modifier.padding(all = MaterialTheme.spacing.medium),
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.medium)
        ) {
            ProgramSports(
                sports = persistentSetOf(),
                isCustomizable = false,
            )
            HorizontalDivider(color = MaterialTheme.colorScheme.dividerColor)
            ProgramSports(
                sports = persistentSetOf(CoreActivityType.RUNNING, CoreActivityType.MOTOR_SPORTS),
                isCustomizable = false,
            )
            HorizontalDivider(color = MaterialTheme.colorScheme.dividerColor)
            ProgramSports(
                sports = persistentSetOf(CoreActivityType.RUNNING, CoreActivityType.MOTOR_SPORTS),
                isCustomizable = true,
            )
        }
    }
}

@Preview(showBackground = true, heightDp = 1920)
@Composable
private fun OngoingProgramDetailsPreview() {
    M3AppTheme {
        ProgramDetails(
            uiState = fakeProgramDetailsUiState,
            onBackClick = {},
            onPersonalizeThisProgramClick = {},
        )
    }
}
