package com.stt.android.home.dashboard.widget.suunto247

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.Immutable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawWithContent
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathEffect
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.ExperimentalTextApi
import androidx.compose.ui.text.TextLayoutResult
import androidx.compose.ui.text.TextMeasurer
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.drawText
import androidx.compose.ui.text.rememberTextMeasurer
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.tooling.preview.PreviewParameterProvider
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.stt.android.R
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.bodySmall
import com.stt.android.compose.theme.getDefaultFontFamily
import com.stt.android.compose.theme.lightGrey
import com.stt.android.compose.theme.spacing
import com.stt.android.domain.sleep.HrvGrade
import com.stt.android.domain.sleep.SleepHrv
import com.stt.android.domain.sleep.color
import com.stt.android.domain.sleep.hrvGrade
import com.stt.android.domain.sleep.hrvGradeColor
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf
import java.time.LocalDate
import kotlin.math.roundToInt

@Composable
fun SleepHrvGradeAndNormalRange(
    sleepHrv: SleepHrv?,
    modifier: Modifier = Modifier,
) {
    Box(modifier = modifier) {
        Column(modifier = Modifier.align(Alignment.BottomCenter)) {
            if (sleepHrv?.avg7DayHrv != null && sleepHrv.normalRange != null) {
                SleepHrvGradeLabel(sleepHrv)
            }
            SleepHrvNormalRangeBar(
                sleepHrv = sleepHrv,
            )
        }
    }
}

@Composable
private fun SleepHrvGradeLabel(
    sleepHrv: SleepHrv?,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier
            .wrapContentHeight()
            .padding(bottom = MaterialTheme.spacing.medium),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Box(
            modifier = Modifier
                .padding(start = 2.dp, end = MaterialTheme.spacing.small)
                .size(10.dp)
                .background(Color(sleepHrv.hrvGradeColor), CircleShape)
        )
        Text(
            text = stringResource(
                id = when (sleepHrv.hrvGrade) {
                    HrvGrade.NO_GRADE -> 0
                    HrvGrade.LOW -> R.string.sleep_hrv_avg_low
                    HrvGrade.IN_NORMAL_RANGE -> R.string.sleep_hrv_avg_in_normal_range
                    HrvGrade.HIGH -> R.string.sleep_hrv_avg_high
                }
            ),
            style = MaterialTheme.typography.bodySmall
        )
    }
}

@Composable
private fun SleepHrvNormalRangeBar(
    sleepHrv: SleepHrv?,
    modifier: Modifier = Modifier,
) {
    val normalRange = sleepHrv?.normalRange

    if (normalRange != null) {
        val span = normalRange.endInclusive - normalRange.start
        val selected = sleepHrv.avg7DayHrv?.roundToInt()?.toFloat()
        val selectedColor = Color(sleepHrv.hrvGradeColor)
        MultiSeriesProgress(
            modifier = modifier,
            items = persistentListOf(
                SeriesProgressItem(
                    min = normalRange.start - span,
                    max = normalRange.start,
                    color = Color(HrvGrade.LOW.color)
                ),
                SeriesProgressItem(
                    min = normalRange.start,
                    max = normalRange.endInclusive,
                    color = Color(HrvGrade.IN_NORMAL_RANGE.color)
                ),
                SeriesProgressItem(
                    min = normalRange.endInclusive,
                    max = normalRange.endInclusive + span,
                    color = Color(HrvGrade.HIGH.color)
                ),
            ),
            markers = persistentListOf(
                SeriesProgressMarker(
                    value = normalRange.start,
                    showLabel = true,
                    showLine = true
                ),
                SeriesProgressMarker(
                    value = normalRange.endInclusive,
                    showLabel = true,
                    showLine = true
                ),
            ),
            selectedMarker = selected,
            selectedMarkerColor = selectedColor,
        )
    }
}

@Composable
private fun MultiSeriesProgress(
    items: ImmutableList<SeriesProgressItem>,
    modifier: Modifier = Modifier,
    markers: ImmutableList<SeriesProgressMarker>? = null,
    selectedMarker: Float? = null,
    selectedMarkerColor: Color = Color.Unspecified,
) {
    val limit = run {
        val min = items.minBy { it.min }.min
        val max = items.maxBy { it.max }.max
        (min + max) / 2f
    }
    val left by remember(items) {
        mutableStateOf((items.minOfOrNull { it.min } ?: 0f).coerceAtMost(limit))
    }
    val right by remember(items) {
        mutableStateOf((items.maxOfOrNull { it.max } ?: 0f).coerceAtLeast(limit))
    }
    val range = right - left
    val hasMarkerLabels = markers?.any { it.showLabel } ?: false
    BoxWithConstraints(
        modifier = modifier
            .fillMaxWidth()
            .wrapContentHeight()
    ) {
        val progressBarHeight = 20.dp
        MultiSeriesProgressBar(
            items = items,
            markers = markers ?: persistentListOf(),
            left = left,
            range = range,
            modifier = Modifier
                // To ensure that we don't have big space when there is no selected marker or no marker labels to show
                .padding(
                    bottom = if (!hasMarkerLabels) 0.dp else 14.dp
                )
                .height(progressBarHeight)
        )

        selectedMarker?.let { selectedValue ->
            val markerSize = 17.dp
            // If the value of the selected marker is outside the min-max of tsb values, we coerce to the range min-max
            val offsetXRatio = ((selectedValue - left) / range).coerceIn(0f, 1f)
            val offsetX =
                (offsetXRatio * maxWidth.value - (markerSize.value / 2f))
                    .coerceAtLeast(0f)
                    .coerceAtMost(maxWidth.value - markerSize.value)
            Box {
                Box(
                    Modifier
                        .align(Alignment.TopCenter)
                        .offset(
                            offsetX.dp,
                            (progressBarHeight - markerSize) / 2f
                        )
                        .size(markerSize)
                        .background(color = Color.White, shape = CircleShape)

                ) {
                    Box(
                        Modifier
                            .size(markerSize)
                            .padding(2.dp)
                            .background(color = selectedMarkerColor, shape = CircleShape)
                    ) {
                    }
                }
            }
        }
    }
}

@OptIn(ExperimentalTextApi::class)
@Composable
private fun MultiSeriesProgressBar(
    items: ImmutableList<SeriesProgressItem>,
    left: Float,
    range: Float,
    markers: ImmutableList<SeriesProgressMarker>,
    modifier: Modifier = Modifier,
) {
    val hasData by remember(items) {
        derivedStateOf { items.any { it.hasValue } }
    }
    val textMeasurer = rememberTextMeasurer()
    val noDataBackgroundColor = MaterialTheme.colors.lightGrey

    Box(
        modifier = modifier
            .fillMaxWidth()
            .drawWithContent {
                drawContent()
                val canvasWidth = size.width
                for (marker in markers) {
                    val offsetX = (marker.value - left) / range * canvasWidth

                    multiSeriesProgressMarker(
                        marker = marker,
                        textMeasurer = textMeasurer,
                        text = marker.value
                            .roundToInt()
                            .toString(),
                        offsetX = offsetX
                    )
                }
            }
    ) {
        Canvas(
            modifier = Modifier
                .fillMaxWidth()
                .height(6.dp)
                .clip(RoundedCornerShape(4.dp))
                .align(Alignment.Center)
        ) {
            val canvasWidth = size.width
            val canvasHeight = size.height

            if (!hasData) {
                drawRect(
                    color = noDataBackgroundColor,
                    topLeft = Offset(0f, 0f),
                    size = Size(canvasWidth, canvasHeight)
                )
            } else {
                for (i in items.indices) {
                    val item = items[i]
                    val offsetX = canvasWidth * (item.min - left) / range
                    val width = canvasWidth * (item.max - item.min) / range
                    drawRect(
                        color = item.color,
                        topLeft = Offset(offsetX, 0f),
                        size = Size(width, canvasHeight)
                    )
                }
            }
        }
    }
}

@OptIn(ExperimentalTextApi::class)
private fun DrawScope.multiSeriesProgressMarker(
    marker: SeriesProgressMarker,
    textMeasurer: TextMeasurer,
    text: String,
    offsetX: Float
) {
    require(marker.showLine || marker.showLabel) {
        "Neither showLine and showLabel is true"
    }
    val canvasHeight = size.height

    if (marker.showLine) {
        drawLine(
            color = Color.Black,
            start = Offset(offsetX, 0f),
            end = Offset(offsetX, canvasHeight),
            pathEffect = PathEffect.dashPathEffect(floatArrayOf(6f, 6f), 0f),
            strokeWidth = 3f
        )
    }

    if (marker.showLabel) {
        val textLayoutResult: TextLayoutResult =
            textMeasurer.measure(
                text = AnnotatedString(text),
                style = TextStyle( // We can't use MaterialTheme.typography.bodyLarge inside DrawScope
                    fontSize = 12.sp,
                    fontFamily = getDefaultFontFamily()
                )
            )
        val textSize = textLayoutResult.size
        drawText(
            textMeasurer = textMeasurer,
            text = text,
            topLeft = Offset(
                x = offsetX - textSize.width / 2f,
                y = canvasHeight - 4.dp.value
            ),
            overflow = TextOverflow.Visible
        )
    }
}

@Immutable
private data class SeriesProgressMarker(
    val value: Float,
    val showLine: Boolean, // To draw a line to indicate where the value is actually located
    val showLabel: Boolean,
)

@Immutable
private data class SeriesProgressItem(
    val min: Float,
    val max: Float,
    val color: Color
) {
    init {
        require(min <= max) { "Invalid progress item" }
    }

    val hasValue: Boolean = min != 0f || max != 0f
}

@Composable
@Preview(showBackground = true)
private fun NormalRangeBarPreview(
    @PreviewParameter(NormalRangeBarParamsProvider::class) sleepHrv: SleepHrv
) {
    AppTheme {
        SleepHrvGradeAndNormalRange(sleepHrv = sleepHrv)
    }
}

private class NormalRangeBarParamsProvider : PreviewParameterProvider<SleepHrv> {
    override val values = sequenceOf(
        SleepHrv(
            date = LocalDate.now(),
            avgHrv = 55f,
            previousAvgHrv = 65f,
            avg7DayHrv = 55f,
            normalRange = 55f..75f,
            hasDataWithin60Days = true,
        ),
        SleepHrv(
            date = LocalDate.now(),
            avgHrv = 55f,
            previousAvgHrv = 65f,
            avg7DayHrv = 65f,
            normalRange = 55f..75f,
            hasDataWithin60Days = true,
        ),
        SleepHrv(
            date = LocalDate.now(),
            avgHrv = 55f,
            previousAvgHrv = 65f,
            avg7DayHrv = 75f,
            normalRange = 55f..75f,
            hasDataWithin60Days = true,
        ),
        SleepHrv(
            date = LocalDate.now(),
            avgHrv = 55f,
            previousAvgHrv = 65f,
            avg7DayHrv = 0f,
            normalRange = 55f..75f,
            hasDataWithin60Days = true,
        ),
        SleepHrv(
            date = LocalDate.now(),
            avgHrv = 55f,
            previousAvgHrv = 65f,
            avg7DayHrv = 280f,
            normalRange = 55f..75f,
            hasDataWithin60Days = true,
        )
    )
}
