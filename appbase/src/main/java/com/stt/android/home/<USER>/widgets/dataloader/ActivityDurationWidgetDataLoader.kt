package com.stt.android.home.dashboardv2.widgets.dataloader

import android.content.Context
import com.stt.android.R
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.UserSettingsController
import com.stt.android.controllers.WorkoutHeaderController
import com.stt.android.controllers.currentUserWorkoutUpdated
import com.stt.android.controllers.userSettings
import com.stt.android.domain.diary.toLocalDate
import com.stt.android.domain.user.UserSettings
import com.stt.android.domain.workout.ActivityType
import com.stt.android.home.dashboardv2.ui.widgets.common.formatWidgetDurationTitle
import com.stt.android.home.dashboardv2.widgets.ActivityDurationWidgetInfo
import com.stt.android.home.dashboardv2.widgets.Period
import com.stt.android.home.dashboardv2.widgets.WidgetType
import com.stt.android.home.diary.diarycalendar.activitygroups.ActivityTypeToGroupMapper
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.utils.iterator
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.distinctUntilChangedBy
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.onStart
import javax.inject.Inject
import kotlin.math.abs
import kotlin.math.roundToLong

internal class ActivityDurationWidgetDataLoader @Inject constructor(
    private val context: Context,
    private val currentUserController: CurrentUserController,
    private val userSettingsController: UserSettingsController,
    private val workoutHeaderController: WorkoutHeaderController,
    private val activityTypeToGroupMapper: ActivityTypeToGroupMapper,
    private val coroutinesDispatchers: CoroutinesDispatchers,
    private val infoModelFormatter: InfoModelFormatter,
) : WidgetDataLoader<ActivityDurationWidgetInfo>() {
    override suspend fun load(param: Param): WidgetData<ActivityDurationWidgetInfo> =
        WidgetData(
            widgetType = param.type,
            editMode = param.editMode,
            widgetInfo = combine(
                workoutHeaderController.currentUserWorkoutUpdated
                    .onStart { emit(Unit) },
                userSettingsController.userSettings()
                    .distinctUntilChangedBy(UserSettings::getFirstDayOfTheWeek),
            ) { _, _ -> loadWidgetInfo(param) }
                .flowOn(coroutinesDispatchers.io),
        )

    private fun loadWidgetInfo(param: Param): ActivityDurationWidgetInfo {
        val representativeActivityType = when (param.type) {
            WidgetType.DURATION_OF_DIVING_THIS_WEEK -> ActivityType.SNORKELING
            else -> throw IllegalStateException("Unsupported param: $param")
        }
        val period = Period.ThisWeek(userSettingsController)
        val workouts = fetchOwnWorkoutsByPeriodAndActivityGroup(
            workoutHeaderController = workoutHeaderController,
            activityTypeToGroupMapper = activityTypeToGroupMapper,
            currentUserController = currentUserController,
            period = period,
            activityGroup = activityTypeToGroupMapper.activityTypeToGroup(representativeActivityType),
        )
        val currentPeriodWorkoutsByDay = workouts.groupBy { it.startTime.toLocalDate() }
        val dailyWorkoutTimes = mutableListOf<Double>()
        (period.beginDate..period.endDate).iterator().forEach { day ->
            val workoutsForDay = currentPeriodWorkoutsByDay[day] ?: emptyList()
            dailyWorkoutTimes.add(workoutsForDay.sumOf { it.totalTime })
        }
        val currentPeriodTotalWorkoutSeconds = workouts.sumOf { it.totalTime }
        val (progresses, subtitle, subtitleIconRes) =
            formatPeriodChangeSubtitle(period, representativeActivityType, currentPeriodTotalWorkoutSeconds)
                .let { subtitlePair ->
                    Triple(
                        generateDailyBarProgresses(dailyWorkoutTimes),
                        subtitlePair.first,
                        subtitlePair.second,
                    )
                }

        val title = currentPeriodTotalWorkoutSeconds
            .coerceAtLeast(0.0)
            .formatWidgetDurationTitle(context)

        return ActivityDurationWidgetInfo(
            period = period,
            representativeActivityType = representativeActivityType,
            progresses = progresses,
            title = title,
            subtitle = subtitle,
            subtitleIconRes = subtitleIconRes
        )
    }

    private fun formatPeriodChangeSubtitle(
        period: Period,
        activityType: ActivityType,
        currentPeriodTotalWorkoutSeconds: Double
    ): Pair<String, Int?> {
        val (previousPeriodStartMillis, previousPeriodEndMillis) = period.previousPeriod
        val previousPeriodWorkouts = fetchWorkoutsByPeriod(
            workoutHeaderController,
            currentUserController.username,
            previousPeriodStartMillis,
            previousPeriodEndMillis
        ).filter {
            it.activityType.sameActivityGroupWith(activityTypeToGroupMapper, activityType)
        }
        val previousPeriodTotalWorkoutSeconds = previousPeriodWorkouts.sumOf { it.totalTime }

        val changeSinceLastPeriod = if (previousPeriodTotalWorkoutSeconds > 0) {
            currentPeriodTotalWorkoutSeconds - previousPeriodTotalWorkoutSeconds
        } else if (currentPeriodTotalWorkoutSeconds > 0) {
            currentPeriodTotalWorkoutSeconds
        } else {
            null
        }

        val formatedDuration = changeSinceLastPeriod?.let {
            infoModelFormatter.formatDuration(abs(it).roundToLong())
        } ?: return context.getString(R.string.widget_no_data_subtitle) to null
        val arrowRes =
            if (changeSinceLastPeriod >= 0) R.drawable.widget_up_arrow else R.drawable.widget_down_arrow
        return formatedDuration to arrowRes
    }
}
