package com.stt.android.home.dashboard

import android.content.SharedPreferences
import android.os.Bundle
import android.view.GestureDetector.SimpleOnGestureListener
import android.view.HapticFeedbackConstants
import android.view.MotionEvent
import android.view.View
import android.view.View.OnLayoutChangeListener
import androidx.core.content.edit
import androidx.core.view.GestureDetectorCompat
import androidx.core.view.updateLayoutParams
import androidx.fragment.app.activityViewModels
import androidx.recyclerview.widget.RecyclerView
import com.airbnb.epoxy.EpoxyItemSpacingDecorator
import com.github.xizzhu.simpletooltip.ToolTipView
import com.stt.android.R
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.analytics.AnalyticsPropertyValue.SuuntoDiaryGraphToggleSource.HOME_SCREEN_DASHBOARD_ACTIVITIES
import com.stt.android.analytics.AnalyticsPropertyValue.SuuntoDiaryGraphToggleSource.HOME_SCREEN_DASHBOARD_CALENDAR
import com.stt.android.analytics.AnalyticsPropertyValue.SuuntoDiaryGraphToggleSource.HOME_SCREEN_DASHBOARD_TOTAL_DURATION
import com.stt.android.common.ui.observeK
import com.stt.android.common.ui.observeNotNull
import com.stt.android.common.viewstate.ViewStateListFragment2
import com.stt.android.databinding.FragmentDashboardGridBinding
import com.stt.android.di.DiaryPagePreferences
import com.stt.android.domain.diary.models.GraphDataType
import com.stt.android.domain.workouts.tag.SuuntoTag
import com.stt.android.home.HomeActivityNavigator
import com.stt.android.home.dashboard.widget.SpacerWidgetView
import com.stt.android.home.dashboard.widget.WidgetType
import com.stt.android.home.dashboardnew.customization.SelectNewDashboardWidgetDialogFragment
import com.stt.android.home.diary.DiaryPagePreferencesKeys
import com.stt.android.home.diary.diarycalendar.DiaryCalendarListContainer
import com.stt.android.tags.TagsNavigator
import com.stt.android.tooltips.Tooltip
import java.lang.ref.WeakReference
import javax.inject.Inject

open class BaseDashboardGridFragment : ViewStateListFragment2<
    DashboardGridContainer,
    DashboardGridViewModel,
    >() {
    @Inject
    lateinit var homeActivityNavigator: HomeActivityNavigator

    @Inject
    lateinit var tagsNavigator: TagsNavigator

    @Inject
    @DiaryPagePreferences
    lateinit var diaryPagePreferences: SharedPreferences

    @Inject
    lateinit var dashboardAnalytics: DashboardAnalytics

    @Inject
    lateinit var tooltip: Tooltip

    override val layoutId = R.layout.fragment_dashboard_grid
    override val viewModel: DashboardGridViewModel by activityViewModels()

    private val binding: FragmentDashboardGridBinding get() = requireBinding()

    private val pagePosition: Int
        get() = arguments?.getInt(ARG_PAGE_POSITION) ?: 0

    private var toolTipViewDashboardCustomization: ToolTipView? = null

    private var longClickListener: RecyclerView.SimpleOnItemTouchListener? = null

    private var onLayoutChangeListener: OnLayoutChangeListener? = null

    private var longClickDetector: GestureDetectorCompat? = null

    private var gestureListener: SimpleOnGestureListener? = null
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        // Undo setHasFixedSize(true) setting in ViewStateListFragment to let RV calculate its
        // width based on its content in order to center the RV horizontally inside the Fragment.
        binding.list.setHasFixedSize(false)

        (controller as DashboardGridController).pagePosition = pagePosition

        binding.list.addItemDecoration(
            EpoxyItemSpacingDecorator(
                requireContext().resources.getDimensionPixelOffset(
                    R.dimen.dashboard_grid_spacing
                )
            )
        )

        val tooltipContainerRef = WeakReference(binding.tooltipContainer)
        onLayoutChangeListener = OnLayoutChangeListener{ _, left, _, right, _, _, _, _, _ ->
            tooltipContainerRef.get()?.updateLayoutParams {
                this.width = right - left
            }
        }
        binding.list.addOnLayoutChangeListener(onLayoutChangeListener)

        val listRef = WeakReference(binding.list)
        val isBindingAvailableRef = WeakReference(isBindingAvailable)
        gestureListener = object : SimpleOnGestureListener() {
            override fun onLongPress(e: MotionEvent) {
                if (isBindingAvailableRef.get() == true) {
                    listRef.get()?.performHapticFeedback(HapticFeedbackConstants.LONG_PRESS)
                    viewModel.openDashboardCustomizationMode()
                }
            }
        }
        gestureListener?.let {
            longClickDetector = GestureDetectorCompat(requireContext().applicationContext, it)
        }
        // Regular OnLongClickListener doesn't work properly with RecyclerViews, so
        // to detect a long press in the dashboard's background (or spacers that effectively
        // are part of the background), we need to manually detect long presses.
        // Even though this is an OnItemTouchListener, the onInterceptTouchEvent is called
        // on all touches to the RecyclerView even if they don't touch an item.
        longClickListener = object : RecyclerView.SimpleOnItemTouchListener() {
            override fun onInterceptTouchEvent(rv: RecyclerView, e: MotionEvent): Boolean {
                val child = rv.findChildViewUnder(e.x, e.y)
                if (child == null || child is SpacerWidgetView) {
                    longClickDetector?.onTouchEvent(e)
                }

                return false
            }
        }
        longClickListener?.let {
            binding.list.addOnItemTouchListener(it)
        }

        viewModel.totalDurationClickedEvent.observeK(viewLifecycleOwner) {
            it?.run { openDiaryCalendar(this, false, HOME_SCREEN_DASHBOARD_TOTAL_DURATION) }
        }
        viewModel.mapClickedEvent.observeK(viewLifecycleOwner) {
            requireActivity().startActivity(
                homeActivityNavigator.newStartIntentToExploreTab(
                    requireContext(),
                    false,
                    AnalyticsPropertyValue.MapScreenSource.HOME_SCREEN_DASHBOARD
                )
            )
        }
        viewModel.calendarClickedEvent.observeK(viewLifecycleOwner) {
            it?.run { openDiaryCalendar(this, false, HOME_SCREEN_DASHBOARD_CALENDAR) }
        }
        viewModel.chartClickedEvent.observeK(viewLifecycleOwner) {
            it?.run { openDiaryCalendar(this, true, HOME_SCREEN_DASHBOARD_ACTIVITIES) }
        }

        viewModel.trainingClickedEvent.observeK(viewLifecycleOwner) {
            requireActivity().startActivity(
                homeActivityNavigator.newStartIntentToDiaryWorkoutList(requireContext())
            )
        }

        viewModel.progressClickedEvent.observeK(viewLifecycleOwner) {
            requireActivity().startActivity(
                homeActivityNavigator.newStartIntentToDiaryProgressTab(requireContext())
            )
        }

        viewModel.sleepHrvClickedEvent.observeK(viewLifecycleOwner) {
            requireActivity().startActivity(
                homeActivityNavigator.newStartIntentToDiaryRecoveryTab(requireContext())
            )
        }

        viewModel.stepsClickedEvent.observeK(viewLifecycleOwner) {
            requireActivity().startActivity(
                homeActivityNavigator.newStartIntentToDiaryStepsTab(requireContext())
            )
        }

        viewModel.caloriesClickedEvent.observeK(viewLifecycleOwner) {
            requireActivity().startActivity(
                homeActivityNavigator.newStartIntentToDiaryCaloriesTab(requireContext())
            )
        }

        viewModel.commuteClickedEvent.observeK(viewLifecycleOwner) {
            tagsNavigator.openDiaryForTag(
                requireContext(),
                resources.getString(SuuntoTag.COMMUTE.nameRes)
            )
        }

        viewModel.ascentClickedEvent.observeK(viewLifecycleOwner) {
            requireActivity().startActivity(
                homeActivityNavigator.newStartIntentToDiaryWorkoutList(
                    context = requireContext(),
                    secondaryGraphType = GraphDataType.ASCENT
                )
            )
        }

        viewModel.openNewWidgetSelectionEvent
            .observeNotNull(viewLifecycleOwner) { (page, indexInPage) ->
                SelectNewDashboardWidgetDialogFragment.newInstance(
                    page,
                    indexInPage
                ).show(childFragmentManager, SELECT_NEW_WIDGET_DIALOG_TAG)
            }

        childFragmentManager.setFragmentResultListener(
            SelectNewDashboardWidgetDialogFragment.SELECTED_WIDGET_RESULT_KEY,
            viewLifecycleOwner
        ) { requestKey, result ->
            val page = result.getInt(SelectNewDashboardWidgetDialogFragment.SELECTED_WIDGET_TARGET_PAGE)
            val position = result.getInt(SelectNewDashboardWidgetDialogFragment.SELECTED_WIDGET_TARGET_POSITION_IN_PAGE)
            val widgetType = result.getSerializable(SelectNewDashboardWidgetDialogFragment.SELECTED_WIDGET_TYPE) as? WidgetType
                ?: return@setFragmentResultListener
            viewModel.setWidgetType(page, position, widgetType)
        }
    }

    override fun onResume() {
        super.onResume()

        // This is subscribed to / unsubscribed in onResume / onPause to make sure
        // the active fragment in the dashboard pager is handling the event
        viewModel.showCustomizationGuidanceEvent.observeNotNull(viewLifecycleOwner) {
            showCustomizationTooltip(it)
        }
    }

    override fun onPause() {
        super.onPause()

        viewModel.showCustomizationGuidanceEvent.removeObservers(viewLifecycleOwner)
        binding.tooltipContainer.removeAllViewsInLayout()
        clearUpTooltips()
    }

    private fun clearUpTooltips() {
        toolTipViewDashboardCustomization?.remove()
        toolTipViewDashboardCustomization = null
    }

    override fun onDestroyView() {
        longClickListener?.let {
            binding.list.removeOnItemTouchListener(it)
        }
        longClickListener = null
        longClickDetector = null
        gestureListener = null
        onLayoutChangeListener?.let {
            binding.list.removeOnLayoutChangeListener(it)
        }
        onLayoutChangeListener = null
        clearUpTooltips()
        super.onDestroyView()
    }

    protected fun openDiaryCalendar(
        granularity: DiaryCalendarListContainer.Granularity,
        showActivitiesList: Boolean,
        source: String
    ) {
        diaryPagePreferences.edit {
            putString(
                DiaryPagePreferencesKeys.DIARY_CALENDAR_LAST_USED_GRANULARITY,
                granularity.value
            )
        }

        requireActivity().startActivity(
            homeActivityNavigator.newStartIntentToDiaryCalendar(
                content = requireContext(),
                source = source,
                showActivitiesList = showActivitiesList
            )
        )
    }

    private fun showCustomizationTooltip(show: Boolean) {
        if (!show) {
            toolTipViewDashboardCustomization?.remove()
            return
        }

        // If we're already showing the tooltip then no need to do anything
        if (toolTipViewDashboardCustomization?.isAttachedToWindow == true) {
            return
        }

        val anchorView = binding.list.getChildAt(0)
        if (anchorView != null) {
            toolTipViewDashboardCustomization?.remove()
            toolTipViewDashboardCustomization =
                tooltip.showDashboardCustomizationTooltip(anchorView, binding.tooltipContainer)
        }
    }

    companion object {
        internal const val ARG_PAGE_POSITION = "com.stt.android.home.dashboard.PAGE_POSITION"

        private const val SELECT_NEW_WIDGET_DIALOG_TAG = "SELECT_NEW_WIDGET_DIALOG_TAG"
    }
}
