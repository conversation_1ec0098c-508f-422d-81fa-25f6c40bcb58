package com.stt.android.home.dashboardv2.widgets.dataloader

import com.stt.android.controllers.CurrentUserController
import com.stt.android.domain.workout.ActivityType
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.home.dashboardnew.commute.GetCommuteWorkoutHeadersUseCase
import com.stt.android.home.dashboardv2.widgets.CommuteWidgetInfo
import kotlinx.coroutines.flow.map
import java.time.LocalDate
import javax.inject.Inject

internal class CommuteWidgetDataLoader @Inject constructor(
    private val currentUserController: CurrentUserController,
    private val getCommuteWorkoutHeadersUseCase: GetCommuteWorkoutHeadersUseCase,
) : WidgetDataLoader<CommuteWidgetInfo>() {
    override suspend fun load(param: Param): WidgetData<CommuteWidgetInfo> {
        val username = currentUserController.username
        val today = LocalDate.now()
        val beginningOfMonth = today.withDayOfMonth(1)
        val getCommuteWorkoutParam = GetCommuteWorkoutHeadersUseCase.Params(username, beginningOfMonth, today)
        val flow = getCommuteWorkoutHeadersUseCase(getCommuteWorkoutParam)
            .map { workouts ->
                CommuteWidgetInfo(
                    totalCo2EmissionsSaving = workouts.sumOf(WorkoutHeader::co2EmissionsReduced),
                    activityCommuteList = getCommutePercentByActivity(workouts),
                )
            }

        return WidgetData(
            widgetType = param.type,
            editMode = param.editMode,
            widgetInfo = flow,
        )
    }

    private fun getCommutePercentByActivity(workouts: List<WorkoutHeader>): List<Pair<ActivityType?, Float>> {
        if (workouts.isEmpty()) return emptyList()
        val commutePairs = workouts.asSequence()
            .filter { it.co2EmissionsReduced > 0 }
            .groupBy { it.activityType }
            .map { (activityType, sameActivityWorkouts) ->
                activityType to sameActivityWorkouts.sumOf { it.co2EmissionsReduced }
            }
            .sortedByDescending { it.second }

        val sumCo2EmissionsReduced = commutePairs.sumOf { it.second }
        val commutePairList = mutableListOf<Pair<ActivityType?, Float>>()
        commutePairs.take(3).forEach { (activityType, co2EmissionsReduced) ->
            commutePairList.add(activityType to (co2EmissionsReduced / sumCo2EmissionsReduced).toFloat())
        }
        if (commutePairs.size > 3) {
            val othersSum = commutePairs.drop(3).sumOf { it.second }
            commutePairList.add(null to (othersSum / sumCo2EmissionsReduced).toFloat())
        }
        val defaultActivityTypes =
            listOf(ActivityType.CYCLING, ActivityType.RUNNING, ActivityType.WALKING)
        for (i in 0 until 3 - commutePairs.size) {
            val activityType = defaultActivityTypes.filterNot { activityType ->
                commutePairList.map { it.first }.contains(activityType)
            }.first()
            commutePairList.add(activityType to 0f)
        }
        return commutePairList
    }
}
