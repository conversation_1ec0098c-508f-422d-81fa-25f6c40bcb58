package com.stt.android.home.settings.wheel

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Divider
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.input.nestedscroll.nestedScroll
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import com.stt.android.R
import com.stt.android.compose.theme.bodyLargeBold
import com.stt.android.compose.theme.bottomSheetShape
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.util.rememberViewInteropNestedScrollConnection
import com.stt.android.compose.widgets.DraggableBottomSheetHandle
import com.stt.android.compose.widgets.PrimaryButton
import java.util.Locale
import kotlin.math.roundToInt

@Composable
fun WheelPickerBottomSheetContent(
    data: WheelFragmentData,
    onDoneClick: (indices: List<Int>) -> Unit,
    modifier: Modifier = Modifier,
    onDragging: (dragging: Boolean) -> Unit = {},
) {
    val wheelPickerIndices = remember {
        data.columns.map { mutableIntStateOf(it.defaultIndex) }
    }

    val unitList = when (data.changeUnitStrategy) {
        ChangeUnitStrategy.Never -> remember {
            data.columns.map { it.unit }
        }

        is ChangeUnitStrategy.BpmPercentage -> remember(wheelPickerIndices.getOrNull(0)?.intValue) {
            val minBpm = data.changeUnitStrategy.minBpm
            val maxBpm = data.changeUnitStrategy.maxBpm
            if (data.columns.none() || wheelPickerIndices.none() || maxBpm <= 0) {
                return@remember data.columns.map { it.unit }
            }
            val bpm = data.columns[0].textList[wheelPickerIndices[0].intValue].toInt()
            val percentage =
                (100f * (bpm - minBpm) / (maxBpm - minBpm)).roundToInt().coerceAtLeast(0)
            listOf("$percentage%")
        }
    }

    val wheelPickerData = remember(unitList) {
        WheelPickerData(
            columns = data.columns.mapIndexed { index, it ->
                WheelPickerColumn(
                    textList = it.textList,
                    defaultIndex = it.defaultIndex,
                    unit = unitList[index],
                    onSelect = { i ->
                        wheelPickerIndices[index].intValue = i
                    }
                )
            },
            disableDoneStrategy = if (data.autoCorrectIndices) {
                data.disableDoneStrategy
            } else {
                DisableDoneStrategy.Never
            }
        )
    }

    Column(
        modifier = modifier
            .nestedScroll(rememberViewInteropNestedScrollConnection())
            .clip(MaterialTheme.shapes.bottomSheetShape)
            .background(MaterialTheme.colors.surface)
    ) {
        DraggableBottomSheetHandle()

        Column(
            modifier = Modifier
                .verticalScroll(rememberScrollState())
                .fillMaxSize()
        ) {
            Text(
                text = data.title.uppercase(),
                style = MaterialTheme.typography.bodyLargeBold,
                modifier = Modifier
                    .padding(vertical = MaterialTheme.spacing.medium)
                    .align(Alignment.CenterHorizontally)
            )

            Divider()

            WheelPickerDataView(
                data = wheelPickerData,
                modifier = Modifier.height(dimensionResource(id = R.dimen.bottom_picker_height)),
                onDragging = onDragging,
            )

            PrimaryButton(
                text = stringResource(id = R.string.done).uppercase(Locale.getDefault()),
                onClick = {
                    val indices = wheelPickerIndices.map { it.intValue }
                    if (data.disableDoneStrategy.isValidIndices(indices)) {
                        onDoneClick(wheelPickerIndices.map { it.intValue })
                    }
                },
                enabled = data.autoCorrectIndices || data.disableDoneStrategy.isValidIndices(
                    wheelPickerIndices.map { it.intValue }),
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(
                        vertical = MaterialTheme.spacing.medium,
                        horizontal = MaterialTheme.spacing.large,
                    ),
            )
        }
    }
}

@Preview
@Composable
private fun WheelPickerBottomSheetContentPreview() {
    WheelPickerBottomSheetContent(
        data = WheelFragmentData(
            title = "Xxx",
            columns = listOf(
                WheelFragmentColumn(
                    textList = (0..100).map { "$it" },
                    defaultIndex = 10,
                    unit = "km"
                ),
                WheelFragmentColumn(
                    textList = (0..100).map { ".$it" },
                    defaultIndex = 10,
                    unit = "km"
                )
            )
        ),
        onDoneClick = {})
}
