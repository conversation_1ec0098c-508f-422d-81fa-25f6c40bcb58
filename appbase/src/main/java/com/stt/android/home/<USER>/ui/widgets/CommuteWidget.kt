package com.stt.android.home.dashboardv2.ui.widgets

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.CornerRadius
import androidx.compose.ui.geometry.RoundRect
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.stt.android.R
import com.stt.android.compose.component.SuuntoActivityIcon
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.spacing
import com.stt.android.domain.workout.ActivityType
import com.stt.android.home.dashboardv2.ui.widgets.common.CommonChartWidget
import com.stt.android.home.dashboardv2.ui.widgets.common.generateWidgetTitle
import com.stt.android.home.dashboardv2.widgets.CommuteWidgetInfo
import java.text.DecimalFormat
import java.text.DecimalFormatSymbols
import java.util.Locale
import kotlin.math.roundToInt
import com.stt.android.core.R as CR

@Composable
internal fun CommuteWidget(
    widgetInfo: CommuteWidgetInfo,
    editMode: Boolean,
    onClick: (() -> Unit)?,
    onLongClick: (() -> Unit)?,
    onRemoveClick: (() -> Unit)?,
    modifier: Modifier = Modifier,
) {
    val co2eEmissionsSum = remember { widgetInfo.totalCo2EmissionsSaving }
    CommonChartWidget(
        editMode = editMode,
        headerRes = R.string.dashboard_widget_title_commute,
        subheaderText = stringResource(R.string.this_month),
        iconRes = R.drawable.ic_commute_weather,
        colorRes = CR.color.st_outdoor_adventures,
        titleText = generateWidgetTitle(formatSavedEmissions(co2eEmissionsSum), stringResource(CR.string.kilograms)),
        subtitleText = stringResource(R.string.workout_values_co2_saved),
        onClick = onClick,
        onLongClick = onLongClick,
        onRemoveClick = onRemoveClick,
        modifier = modifier,
    ) {
        if (co2eEmissionsSum == 0.0) {
            CommuteEmptyList()
        } else {
            CommuteActivityList(
                activityCommuteList = widgetInfo.activityCommuteList,
            )
        }
    }
}

@Composable
internal fun CommuteActivityList(
    activityCommuteList: List<Pair<ActivityType?, Float>>,
    modifier: Modifier = Modifier
) {
    val activityIcons = remember { activityCommuteList }

    Column(
        modifier = modifier.fillMaxWidth()
    ) {
        LazyRow(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.xsmall),
        ) {
            itemsIndexed(activityIcons) { index, (type, _) ->
                SuuntoActivityIcon(
                    iconSize = MaterialTheme.iconSizes.small,
                    iconRes = type?.iconId ?: R.drawable.ic_activity_unspecified,
                    tint = MaterialTheme.colorScheme.onPrimary,
                    background = if (type?.colorId == null || activityCommuteList[index].second == 0f) {
                        MaterialTheme.colorScheme.background
                    } else {
                        colorResource(type.colorId)
                    }
                )
            }
        }

        Spacer(
            modifier = Modifier
                .height(MaterialTheme.spacing.small)
                .fillMaxWidth()
        )

        val density = LocalDensity.current
        val cornerRadius = with(density) { 2.dp.toPx() }
        val filteredList = activityCommuteList.filterNot { it.second == 0f }

        // Pre-compute colors outside Canvas
        val segmentColors = filteredList.map { (activityType, _) ->
            activityType?.colorId?.let { colorResource(it) } ?: MaterialTheme.colorScheme.background
        }

        Canvas(
            modifier = Modifier
                .height(8.dp)
                .fillMaxWidth()
        ) {
            if (filteredList.isEmpty()) return@Canvas

            var startX = 0f
            val barHeight = size.height

            filteredList.forEachIndexed { index, (_, percent) ->
                val segmentWidth = size.width * percent
                val backgroundColor = segmentColors[index]

                val isFirst = index == 0

                // Create rounded rectangle path for each segment
                // Every segment end has rounded corners, and from the second segment onwards,
                // the start should be inside the previous segment's rounded end
                val path = Path().apply {
                    addRoundRect(
                        RoundRect(
                            left = if (isFirst) startX else startX - cornerRadius,
                            top = 0f,
                            right = startX + segmentWidth,
                            bottom = barHeight,
                            topLeftCornerRadius = CornerRadius(if (isFirst) cornerRadius else 0f),
                            topRightCornerRadius = CornerRadius(cornerRadius),
                            bottomLeftCornerRadius = CornerRadius(if (isFirst) cornerRadius else 0f),
                            bottomRightCornerRadius = CornerRadius(cornerRadius)
                        )
                    )
                }

                drawPath(path = path, color = backgroundColor)
                startX += segmentWidth
            }
        }
    }
}

private fun formatSavedEmissions(savedCo2eEmissions: Double): String = when {
    savedCo2eEmissions == 0.0 -> "0"
    savedCo2eEmissions < 10 -> underTenFormatter.format(savedCo2eEmissions)
    savedCo2eEmissions < 100 -> underHundredFormatter.format(savedCo2eEmissions)
    else -> savedCo2eEmissions.roundToInt().toString()
}

@Composable
private fun CommuteEmptyList(
    modifier: Modifier = Modifier,
) {
    val activityTypes =
        remember { listOf(ActivityType.CYCLING, ActivityType.RUNNING, ActivityType.WALKING) }

    Column(
        modifier = modifier
    ) {
        LazyRow(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.xsmall),
        ) {
            items(activityTypes) { type ->
                SuuntoActivityIcon(
                    iconSize = MaterialTheme.iconSizes.small,
                    iconRes = type.iconId,
                    tint = MaterialTheme.colorScheme.onPrimary,
                    background = MaterialTheme.colorScheme.background
                )
            }
        }

        Spacer(
            modifier = Modifier
                .height(MaterialTheme.spacing.small)
                .fillMaxWidth()
        )

        Box(
            modifier = Modifier
                .height(8.dp)
                .fillMaxWidth()
                .clip(RoundedCornerShape(2.dp))
                .background(MaterialTheme.colorScheme.background)
        )
    }
}

@Preview
@Composable
internal fun CommuteEmptyListPreview() {
    M3AppTheme {
        CommuteEmptyList()
    }
}

@Preview
@Composable
internal fun CommuteActivityListPreview() {
    M3AppTheme {
        CommuteActivityList(
            activityCommuteList = listOf(
                Pair(ActivityType.CYCLING, 0.4f),
                Pair(ActivityType.RUNNING, 0.3f),
                Pair(ActivityType.WALKING, 0.3f)
            )
        )
    }
}

private val underTenFormatter =
    DecimalFormat("0.00", DecimalFormatSymbols.getInstance(Locale.US))
private val underHundredFormatter =
    DecimalFormat("#0.0", DecimalFormatSymbols.getInstance(Locale.US))
