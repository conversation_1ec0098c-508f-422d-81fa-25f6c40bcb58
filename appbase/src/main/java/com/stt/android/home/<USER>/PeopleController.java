package com.stt.android.home.people;

import android.content.Context;
import android.content.SharedPreferences;
import android.text.TextUtils;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.WorkerThread;
import com.j256.ormlite.dao.Dao;
import com.j256.ormlite.stmt.DeleteBuilder;
import com.j256.ormlite.stmt.QueryBuilder;
import com.stt.android.analytics.AmplitudeAnalyticsTracker;
import com.stt.android.analytics.AnalyticsEvent;
import com.stt.android.analytics.AnalyticsEventProperty;
import com.stt.android.analytics.AnalyticsProperties;
import com.stt.android.analytics.AnalyticsPropertyValue;
import com.stt.android.analytics.FirebaseAnalyticsTracker;
import com.stt.android.controllers.BackendController;
import com.stt.android.controllers.CurrentUserController;
import com.stt.android.domain.UserSession;
import com.stt.android.domain.android.DaysSinceInstallationUseCase;
import com.stt.android.domain.database.DatabaseHelper;
import com.stt.android.domain.user.FacebookToken;
import com.stt.android.domain.user.Reaction;
import com.stt.android.domain.user.User;
import com.stt.android.domain.user.UserSearchResult;
import com.stt.android.domain.user.follow.FollowCountSummary;
import com.stt.android.domain.user.follow.FollowUser;
import com.stt.android.exceptions.BackendException;
import com.stt.android.exceptions.InternalDataException;
import com.stt.android.follow.BackendFollowStatusChange;
import com.stt.android.follow.FollowDirection;
import com.stt.android.follow.FollowLists;
import com.stt.android.follow.FollowStatus;
import com.stt.android.follow.UserFollowStatus;
import com.stt.android.social.friends.Friend;
import com.stt.android.ui.utils.TextFormatter;
import com.stt.android.utils.STTConstants;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.TimeUnit;
import javax.inject.Inject;
import javax.inject.Named;
import javax.inject.Singleton;
import kotlin.Pair;
import rx.Completable;
import rx.Observable;
import rx.Single;
import rx.exceptions.Exceptions;
import rx.functions.Action1;
import rx.functions.Func1;
import rx.schedulers.Schedulers;
import rx.subjects.Subject;
import timber.log.Timber;

@Singleton
public class PeopleController {
    final CurrentUserController currentUserController;
    final BackendController backendController;
    @SuppressWarnings("WeakerAccess")
    final Dao<UserFollowStatus, String> followStatusDao;

    @SuppressWarnings("WeakerAccess")
    final Subject<UserFollowStatus, UserFollowStatus> followingSubject;
    @SuppressWarnings("WeakerAccess")
    final Subject<UserFollowStatus, UserFollowStatus> followersSubject;
    private final Context context;
    private final SharedPreferences sharedPreferences;
    private final DaysSinceInstallationUseCase daysSinceInstallationUseCase;
    private final FirebaseAnalyticsTracker firebaseAnalyticsTracker;

    private final AmplitudeAnalyticsTracker amplitudeAnalyticsTracker;

    @Inject
    public PeopleController(
        CurrentUserController currentUserController,
        BackendController backendController,
        DatabaseHelper helper,
        @Named("FOLLOWING") Subject<UserFollowStatus, UserFollowStatus> followingSubject,
        @Named("FOLLOWERS") Subject<UserFollowStatus, UserFollowStatus> followersSubject,
        Context context,
        SharedPreferences sharedPreferences,
        DaysSinceInstallationUseCase daysSinceInstallationUseCase,
        FirebaseAnalyticsTracker firebaseAnalyticsTracker,
        AmplitudeAnalyticsTracker amplitudeAnalyticsTracker
    ) {
        try {
            this.followStatusDao = helper.getDao(UserFollowStatus.class);
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
        this.currentUserController = currentUserController;
        this.backendController = backendController;
        this.followingSubject = followingSubject;
        this.followersSubject = followersSubject;
        this.context = context;
        this.sharedPreferences = sharedPreferences;
        this.daysSinceInstallationUseCase = daysSinceInstallationUseCase;
        this.firebaseAnalyticsTracker = firebaseAnalyticsTracker;
        this.amplitudeAnalyticsTracker = amplitudeAnalyticsTracker;
    }

    /**
     * Checks first from local DB and then backend if the user has any followers. It also
     * publishes the loaded follow status to {@link #followersSubject}.
     *
     * @return true if user has any followers.
     */
    @NonNull
    public Single<Boolean> hasFollowers() {
        Single<List<UserFollowStatus>> local =
            loadUserFollowStatusFromDb(FollowDirection.FOLLOWER).onErrorResumeNext(
                Single.just(Collections.<UserFollowStatus>emptyList()))
                .doOnSuccess(emitToSubject(followersSubject));

        Single<List<UserFollowStatus>> remote = loadFollowListsFromBackendAndCacheLocaly().map(
            new Func1<FollowLists, List<UserFollowStatus>>() {
                @Override
                public List<UserFollowStatus> call(FollowLists followLists) {
                    return followLists.getFollowers();
                }
            })
            .onErrorResumeNext(Single.just(Collections.<UserFollowStatus>emptyList()))
            .doOnSuccess(emitToSubject(followersSubject));

        return Single.zip(local, remote, (db, remote1) -> !db.isEmpty() || !remote1.isEmpty())
            .doOnSuccess(hasFollowers -> {
                // sending first time "has followers" event to analytics if needed
                String username = currentUserController.getUsername();
                String prefKey = STTConstants.MiscPreferences
                    .KEY_PEOPLE_HAS_FOLLOWER_ANALYTICS_EVENT_SENT + username;
                if (hasFollowers && !sharedPreferences.getBoolean(prefKey, false)) {
                    firebaseAnalyticsTracker.trackEvent(AnalyticsEvent.HAS_FOLLOWERS);
                    sharedPreferences.edit().putBoolean(prefKey, true).apply();
                }
            });
    }

    @NonNull
    private Action1<List<UserFollowStatus>> emitToSubject(
        final Subject<UserFollowStatus, UserFollowStatus> emittingSubject) {
        return new Action1<List<UserFollowStatus>>() {
            @Override
            public void call(List<UserFollowStatus> followStatuses) {
                // FIXME: What happens if local DB is empty but remote no? Should we show
                // empty state?
                for (int i = 0; i < followStatuses.size(); i++) {
                    UserFollowStatus userFollowStatus = followStatuses.get(i);
                    emittingSubject.onNext(userFollowStatus);
                }
            }
        };
    }

    /**
     * Load {@link UserFollowStatus} by user locally for both {@link FollowDirection#FOLLOWER}
     * and {@link FollowDirection#FOLLOWING} directions.
     */
    public void loadUfsByUserLocally(final User user) {
        Completable.fromAction(() -> {
            followersSubject.onNext(getUfsFromDbByUser(user, FollowDirection.FOLLOWER));
            followingSubject.onNext(getUfsFromDbByUser(user, FollowDirection.FOLLOWING));
        }).subscribeOn(Schedulers.io())
            .subscribe();
    }

    /**
     * Checks first from local DB and then backend if the user is following any user. It also
     * publishes the loaded follow status to {@link #followingSubject}.
     *
     * @return true if user is following any other users.
     */
    public Single<Boolean> hasFollowing() {
        Single<List<UserFollowStatus>> local =
            loadUserFollowStatusFromDb(FollowDirection.FOLLOWING).onErrorResumeNext(
                Single.just(Collections.<UserFollowStatus>emptyList()))
                .doOnSuccess(emitToSubject(followingSubject));

        Single<List<UserFollowStatus>> remote = loadFollowListsFromBackendAndCacheLocaly().map(
            new Func1<FollowLists, List<UserFollowStatus>>() {
                @Override
                public List<UserFollowStatus> call(FollowLists followLists) {
                    return followLists.getFollowings();
                }
            })
            .onErrorResumeNext(Single.just(Collections.<UserFollowStatus>emptyList()))
            .doOnSuccess(emitToSubject(followingSubject));

        return Single.zip(local, remote, (db, remote1) -> !db.isEmpty() || !remote1.isEmpty())
            .doOnSuccess(hasFollowing -> {
                // sending first time user follows others event to firebase analytics if needed
                String username = currentUserController.getUsername();
                String prefKey = STTConstants.MiscPreferences
                    .KEY_PEOPLE_FOLLOWS_ANALYTICS_EVENT_SENT + username;
                if (hasFollowing && !sharedPreferences.getBoolean(prefKey, false)) {
                    firebaseAnalyticsTracker.trackEvent(AnalyticsEvent.FOLLOWS_SOMEONE);
                    sharedPreferences.edit().putBoolean(prefKey, true).apply(); }
            });
    }

    @NonNull
    private Single<List<UserFollowStatus>> loadUserFollowStatusFromDb(
        final FollowDirection direction) {
        if (direction == FollowDirection.UNKNOWN) {
            return Single.error(new IllegalArgumentException("Unsupported UNKNOWN direction"));
        }
        return Single.fromCallable(() -> {
            QueryBuilder<UserFollowStatus, String> qb = followStatusDao.queryBuilder();
            qb.where().eq(UserFollowStatus.DbFields.DIRECTION, direction);
            return followStatusDao.query(qb.prepare());
        });
    }

    @WorkerThread
    @NonNull
    public UserFollowStatus getUfsFromDbByUser(@NonNull User user, @NonNull FollowDirection direction) {
        if (direction == FollowDirection.UNKNOWN) {
            throw new IllegalArgumentException("Unsupported UNKNOWN direction");
        }

        try {
            UserFollowStatus userFollowStatusFromDb = findUserFollowStatusFromDb(
                UserFollowStatus.createId(user.getUsername(), direction));
            return userFollowStatusFromDb != null ? userFollowStatusFromDb
                : userToUserFollowStatus(user, direction, context);
        } catch (InternalDataException e) {
            return userToUserFollowStatus(user, direction, context);
        }
    }

    @WorkerThread
    @NonNull
    private FollowStatus getFollowStatusFromDb(@NonNull String username, @NonNull FollowDirection direction) {
        if (direction == FollowDirection.UNKNOWN) {
            throw new IllegalArgumentException("Unsupported UNKNOWN direction");
        }

        try {
            UserFollowStatus userFollowStatusFromDb = findUserFollowStatusFromDb(
                UserFollowStatus.createId(username, direction));
            return userFollowStatusFromDb != null
                ? userFollowStatusFromDb.getStatus()
                : FollowStatus.UNFOLLOWING;
        } catch (InternalDataException e) {
            return FollowStatus.UNFOLLOWING;
        }
    }

    @NonNull
    public Single<FollowLists> loadFollowListsFromBackendAndCacheLocaly() {
        return Single.fromCallable(new Callable<FollowLists>() {
            @Override
            public FollowLists call() throws Exception {
                return backendController.getFollowersAndFollowingsV2(
                    currentUserController.getSession()).toFollowLists();
            }
        }).map(new Func1<FollowLists, FollowLists>() {
            @Override
            public FollowLists call(FollowLists followLists) {
                List<UserFollowStatus> followings = followLists.getFollowings();

                //Let's update currentUserFollowStatus for followers
                List<UserFollowStatus> updatedFollowers =
                    updateCurrentUserFollowStatusesForFollowers(followLists.getFollowers(),
                        followings);

                return new FollowLists(updatedFollowers, followings);
            }
        }).doOnSuccess(new Action1<FollowLists>() {
            @Override
            public void call(final FollowLists followLists) {
                try {
                    followStatusDao.callBatchTasks(new Callable<Void>() {
                        @Override
                        public Void call() throws Exception {

                            /*
                             * Clean already synced data so we don't have stalled data (e.g.
                             * update in another client/web/...)
                             */
                            DeleteBuilder<UserFollowStatus, String> qb =
                                followStatusDao.deleteBuilder();
                            qb.where().eq(UserFollowStatus.DbFields.LOCALLY_CHANGED, false);
                            followStatusDao.delete(qb.prepare());

                            // We should not overwrite the locally changed when getting
                            // things from backend but for now we do it
                            //List<UserFollowStatus> locallyChanged = followStatusDao
                            // .queryForEq(
                            //    UserFollowStatus.DbFields.LOCALLY_CHANGED, true);

                            // Updates the database followings based on what we got from backend
                            List<UserFollowStatus> followings = followLists.getFollowings();
                            for (UserFollowStatus following : followings) {
                                followStatusDao.createOrUpdate(following);
                            }

                            List<UserFollowStatus> followers = followLists.getFollowers();
                            for (UserFollowStatus follower : followers) {
                                followStatusDao.createOrUpdate(follower);
                            }
                            return null;
                        }
                    });
                } catch (Exception e) {
                    Timber.w(e, "Error updating UserFollowStatuses to DB");
                }
            }
        });
    }

    @SuppressWarnings("WeakerAccess")
    @WorkerThread
    List<UserFollowStatus> updateCurrentUserFollowStatusesForFollowers(
        List<UserFollowStatus> followers, List<UserFollowStatus> followings) {
        List<UserFollowStatus> updatedFollowers = new ArrayList<>();

        for (UserFollowStatus follower : followers) {
            //Let's filter out rejected users from followers
            if (follower.getStatus() != FollowStatus.REJECTED) {
                // For the followers list we need to figure out if the
                // current logged in user status (or if not found then we
                // default to UNFOLLOWING)
                FollowStatus currentUserFollowStatus = FollowStatus.UNFOLLOWING;
                for (UserFollowStatus following : followings) {
                    if (following.getUsername().equals(follower.getUsername())) {
                        currentUserFollowStatus = following.getStatus();
                        break;
                    }
                }
                UserFollowStatus updateFollowerStatus = follower.toBuilder()
                    .setCurrentUserFollowStatus(currentUserFollowStatus)
                    .build();
                updatedFollowers.add(updateFollowerStatus);
            }
        }
        return updatedFollowers;
    }

    @NonNull
    public Single<List<UserFollowStatus>> fetchPeopleSuggestions() {
        return Single.fromCallable(new Callable<List<UserSearchResult>>() {
            @Override
            public List<UserSearchResult> call() throws Exception {
                List<UserSearchResult> possibleFriends = new ArrayList<>();
                UserSession session = currentUserController.getSession();
                if (session != null) {
                    possibleFriends = backendController.findPossibleFriends(session);
                    filterOutCurrentUser(possibleFriends);
                }
                return possibleFriends;
            }
        }).map(userSearchResultsToUserFollowStatuses());
    }

    /**
     * Maps UserSearchResult to UserFollowStatuses found from db. If UserFollowStatus not found
     * create new one
     */
    @NonNull
    private Func1<List<UserSearchResult>, List<UserFollowStatus>>
    userSearchResultsToUserFollowStatuses() {
        return new Func1<List<UserSearchResult>, List<UserFollowStatus>>() {
            @Override
            public List<UserFollowStatus> call(List<UserSearchResult> userSearchResults) {
                List<UserFollowStatus> userSearchStatuses =
                    new ArrayList<>(userSearchResults.size());
                if (!userSearchResults.isEmpty()) {
                    for (UserSearchResult userSearchResult : userSearchResults) {
                        UserFollowStatus targetStatus = null;
                        try {
                            targetStatus = findUserFollowStatusFromDb(UserFollowStatus.createId(
                                userSearchResult.getUser().getUsername(),
                                FollowDirection.FOLLOWING));
                        } catch (InternalDataException e) {
                            //do nothing if status not found
                        }
                        //create new status for user
                        if (targetStatus == null) {
                            userSearchStatuses.add(
                                userToUserFollowStatus(userSearchResult.getUser(), FollowDirection.FOLLOWING, context)
                            );
                        } else {
                            userSearchStatuses.add(targetStatus);
                        }
                    }
                }
                return userSearchStatuses;
            }
        };
    }

    /**
     * @param query username or real name
     * @return Emits list of UserFollowStatuses based on a query
     */
    public Observable<List<UserFollowStatus>> fetchUserFollowStatusesWithQuery(final String query) {
        return searchUsers(query).map(userSearchResultsToUserFollowStatuses()).toObservable();
    }

    /**
     * @param query username
     */
    private Single<List<UserSearchResult>> searchUsers(final String query) {
        return Single.fromCallable(new Callable<List<UserSearchResult>>() {
            @Override
            public List<UserSearchResult> call() throws Exception {
                List<UserSearchResult> possibleFriends = new ArrayList<>();
                UserSession session = currentUserController.getSession();
                if (session != null) {
                    possibleFriends = backendController.findUsers(session, query);
                    filterOutCurrentUser(possibleFriends);
                }
                return possibleFriends;
            }
        });
    }

    @SuppressWarnings("WeakerAccess")
    void filterOutCurrentUser(List<UserSearchResult> userSearchResults) {
        // as of now, the server returns the user himself as a possible friend, so we filter that
        // out
        Iterator<UserSearchResult> i = userSearchResults.iterator();
        String currentUserName = currentUserController.getUsername();
        while (i.hasNext()) {
            UserSearchResult result = i.next();
            if (result.getUser().getUsername().equals(currentUserName)) {
                i.remove();
                break;
            }
        }
    }

    /**
     * Emits a list of facebook friends mapped to UserFollowStatus or empty list if no facebook
     * friends found
     */
    public Observable<List<UserFollowStatus>> fetchMappedFacebookFriends() {
        return fetchFacebookFriends().map(fbFriendsToUserFollowStatuses()).toObservable();
    }

    @NonNull
    public Single<List<User>> fetchFacebookFriends() {
        return Single.fromCallable(new Callable<List<User>>() {
            @Override
            public List<User> call() throws Exception {
                List<User> fbFriends = new ArrayList<>();
                UserSession session = currentUserController.getSession();
                if (session != null) {
                    fbFriends = backendController.fetchFacebookFriends(session);
                }
                return fbFriends;
            }
        });
    }

    /**
     * Check db for friend follow status. If follow status not found create one.
     * let's add only fb friends who we are NOT following. If user is still PENDING let's not
     * show him in {@link FindFbFriendsView#onFbFriendsLoaded}
     */
    @NonNull
    private Func1<List<User>, List<UserFollowStatus>> fbFriendsToUserFollowStatuses() {
        return new Func1<List<User>, List<UserFollowStatus>>() {
            @Override
            public List<UserFollowStatus> call(List<User> fbFriendsAsUsers) {
                List<UserFollowStatus> fbFriendStatuses = new ArrayList<>(fbFriendsAsUsers.size());
                if (!fbFriendsAsUsers.isEmpty()) {
                    for (User user : fbFriendsAsUsers) {
                        UserFollowStatus targetStatus = null;
                        try {
                            targetStatus = findUserFollowStatusFromDb(
                                UserFollowStatus.createId(user.getUsername(),
                                    FollowDirection.FOLLOWING));
                        } catch (InternalDataException e) {
                            Timber.d(e);
                        }
                        //UserFollowStatus not found let's create one
                        if (targetStatus == null) {
                            fbFriendStatuses.add(
                                userToUserFollowStatus(user, FollowDirection.FOLLOWING, context)
                            );
                        } else if (targetStatus.getCurrentUserFollowStatus()
                            == FollowStatus.UNFOLLOWING) {
                            //add only those whose status is UNFOLLOWING
                            fbFriendStatuses.add(targetStatus);
                        }
                    }
                }
                return fbFriendStatuses;
            }
        };
    }

    private UserFollowStatus findUserFollowStatusFromDb(String id) throws InternalDataException {
        try {
            return followStatusDao.queryForId(id);
        } catch (SQLException e) {
            throw new InternalDataException("Unable to fetch UserFollowStatus from DB", e);
        }
    }

    public Single<Boolean> validateFacebookToken() {
        return Single.fromCallable(new Callable<Boolean>() {
            @Override
            public Boolean call() throws Exception {
                UserSession session = currentUserController.getSession();
                if (session != null) {
                    FacebookToken fbToken = backendController.fetchFacebookToken(session);
                    return backendController.validateFacebookTokenForFindingFriends(fbToken);
                }
                return false;
            }
        });
    }

    /**
     * <b>NOTE</b>: This directly executes a request to backend, it doesn't save intermediate
     * states to local DB.
     *
     * @param originalUserFollowStatuses list of users to follow. Only those users whose {@link
     * UserFollowStatus#currentUserFollowStatus} is {@link FollowStatus#UNFOLLOWING} will be sent
     * with the request
     * @param delay milliseconds before actual request is fired. This is added so the user can
     * cancel the request within this time frame
     */
    public Completable followListOfPeopleWithDelay(
        final List<UserFollowStatus> originalUserFollowStatuses, long delay,
        final String viewSourceForAnalytics) {
        final String[] userNamesAsArray = userNamesAsArray(originalUserFollowStatuses);
        return Observable.just(userNamesAsArray)
            .delay(delay, TimeUnit.MILLISECONDS)
            .flatMap(followUsers())
            .map(backendFollowStatusesToUserFollowStatuses(originalUserFollowStatuses))
            .doOnNext(new Action1<List<UserFollowStatus>>() {
                @Override
                public void call(final List<UserFollowStatus> updatedStatuses) {
                    trackAddAllFbFriendsToAnalytics(viewSourceForAnalytics, updatedStatuses,
                        originalUserFollowStatuses.size());
                    try {
                        followStatusDao.callBatchTasks((Callable<Void>) () -> {
                            for (UserFollowStatus updatedStatus : updatedStatuses) {
                                //save backend response to db
                                followStatusDao.createOrUpdate(updatedStatus);
                                //notify subject
                                followingSubject.onNext(updatedStatus);
                            }
                            return null;
                        });
                    } catch (Exception e) {
                        Timber.w(e, "Error updating UserFollowStatuses to DB");
                    }
                }
            })
            .doOnError(new Action1<Throwable>() {
                @Override
                public void call(Throwable throwable) {
                    if (throwable instanceof BackendException) {
                        // empty list will trigger an error outcome
                        trackAddAllFbFriendsToAnalytics(viewSourceForAnalytics,
                            Collections.<UserFollowStatus>emptyList(),
                            originalUserFollowStatuses.size());
                    }
                }
            })
            .toCompletable();
    }

    private Func1<String[], Observable<List<BackendFollowStatusChange>>> followUsers() {
        return new Func1<String[], Observable<List<BackendFollowStatusChange>>>() {
            @Override
            public Observable<List<BackendFollowStatusChange>> call(final String[] userNames) {
                return Observable.fromCallable(new Callable<List<BackendFollowStatusChange>>() {
                    @Override
                    public List<BackendFollowStatusChange> call() throws Exception {
                        return backendController.followListOfUsersV2(
                            currentUserController.getSession(), userNames);
                    }
                });
            }
        };
    }

    /**
     * match original statuses to the ones from backend. We match them by username since we can't be
     * sure backend returns the list in same order
     */
    @NonNull
    private Func1<List<BackendFollowStatusChange>, List<UserFollowStatus>>
    backendFollowStatusesToUserFollowStatuses(
        final List<UserFollowStatus> originalUserFollowStatuses) {
        return new Func1<List<BackendFollowStatusChange>, List<UserFollowStatus>>() {

            @Override
            public List<UserFollowStatus> call(
                List<BackendFollowStatusChange> backendFollowStatusChanges) {
                List<UserFollowStatus> updatedStatuses =
                    new ArrayList<>(originalUserFollowStatuses.size());
                for (UserFollowStatus oldStatus : originalUserFollowStatuses) {
                    for (int i = 0; i < backendFollowStatusChanges.size(); i++) {
                        BackendFollowStatusChange backendFollowStatusChange =
                            backendFollowStatusChanges.get(i);
                        if (backendFollowStatusChange.getUsername()
                            .contentEquals(oldStatus.getUsername())) {
                            FollowStatus newStatus = backendFollowStatusChange.getStatus();
                            UserFollowStatus followingStatus;
                            //Todo show or notify failed statuses instead of reverting to previous.
                            if (newStatus != FollowStatus.FAILED) {
                                followingStatus = oldStatus.toBuilder()
                                    .setDirection(FollowDirection.FOLLOWING)
                                    .setStatus(newStatus)
                                    .setCurrentUserFollowStatus(newStatus)
                                    .build();
                            } else {
                                //if backend fails to change status of user revert to previous
                                // status
                                followingStatus = oldStatus;
                            }
                            updatedStatuses.add(followingStatus);
                            //remove and break to decrease iterations
                            backendFollowStatusChanges.remove(i);
                            break;
                        }
                    }
                }
                return updatedStatuses;
            }
        };
    }

    private String[] userNamesAsArray(List<UserFollowStatus> originalStatuses) {
        final String[] userNames = new String[originalStatuses.size()];
        for (int i = 0; i < originalStatuses.size(); i++) {
            userNames[i] = originalStatuses.get(i).getUsername();
        }
        return userNames;
    }

    /**
     * @param oldFollowStatuses whose statuses are fetched from db and notified to subject
     */
    public void getRecentFollowingStatusFromDbForStatuses(
        List<UserFollowStatus> oldFollowStatuses) {
        Observable.from(oldFollowStatuses)
            .flatMap(searchAndNotifyFollowingStatusFromDb())
            .subscribeOn(Schedulers.io())
            .subscribe();
    }

    public Completable acceptFollower(final UserFollowStatus originalUserFollowStatus) {
        return Observable.fromCallable(new Callable<BackendFollowStatusChange>() {
            @Override
            public BackendFollowStatusChange call() throws Exception {
                return backendController.acceptFollower(currentUserController.getSession(),
                    originalUserFollowStatus.getUsername());
            }
        })
            .map(updateAndNotifyFollowerChange(originalUserFollowStatus))
            .doOnNext(trackAcceptRejectEventsToAnalytics(AnalyticsEvent.FOLLOW_ACCEPTED))
            .doOnNext(saveToDbAction())
            .toCompletable();
    }

    public Completable rejectFollower(final UserFollowStatus originalUserFollowStatus) {
        return Observable.fromCallable(new Callable<BackendFollowStatusChange>() {
            @Override
            public BackendFollowStatusChange call() throws Exception {
                return backendController.rejectFollower(currentUserController.getSession(),
                    originalUserFollowStatus.getUsername());
            }
        })
            .map(updateAndNotifyFollowerChange(originalUserFollowStatus))
            .doOnNext(trackAcceptRejectEventsToAnalytics(AnalyticsEvent.FOLLOW_REJECTED))
            .doOnNext(saveToDbAction())
            .toCompletable();
    }

    public Completable revokeFollower(final UserFollowStatus originalUserFollowStatus) {
        boolean isFriends = originalUserFollowStatus.getCurrentUserFollowStatus() == FollowStatus.FRIENDS;
        return Observable.fromCallable(() ->
            backendController.revokeFollower(currentUserController.getSession(),
                originalUserFollowStatus.getUsername()))
            .map(updateAndNotifyFollowerChange(originalUserFollowStatus))
            .doOnNext(saveToDbAction())
            .doOnNext(this::trackRevokeFollowerAnalyticsEvent)
            .doOnNext(userFollowStatus -> {
                if (isFriends) {
                    UserFollowStatus followingStatus = originalUserFollowStatus.toBuilder()
                        .setDirection(FollowDirection.FOLLOWING)
                        .setStatus(FollowStatus.FOLLOWING)
                        .setCurrentUserFollowStatus(null)
                        .build();
                    try {
                        followStatusDao.createOrUpdate(followingStatus);
                    } catch (SQLException e) {
                        Timber.w(e, "Unable to store new state to DB");
                    }
                    followingSubject.onNext(followingStatus);
                }
            })
            .toCompletable();
    }

    public Completable revokeFollowersByUserFollowStatusIds(final List<String> userFollowStatusIds) {
        return Observable.just(userFollowStatusIds)
            .map((ids) -> {
                // Map list of IDs to list of UserFollowStatuses
                List<UserFollowStatus> res = new ArrayList<>();
                for (String id : ids) {
                    try {
                        UserFollowStatus s = findUserFollowStatusFromDb(id);
                        res.add(s);
                    } catch (InternalDataException e) {
                        Timber.w(e, "Unable to fetch UserFollowStatus by ID '%s'", id);
                    }
                }
                return res;
            })
            .flatMapIterable((userFollowStatuses) -> userFollowStatuses)
            .flatMapCompletable(this::revokeFollower)
            .toCompletable();
    }

    /**
     * <b>NOTE</b>: This directly executes a request to backend, it doesn't save intermediate
     * states to local DB.
     *
     * @param originalUserFollowStatus the user to follow from the current user point of view.
     * @return a {@link Completable}. That is, if the backend call goes well then the new
     * {@link UserFollowStatus} will be propagated through the right Subjects
     * ({@link #followingSubject} and/or {@link #followersSubject})
     */
    public Completable follow(final UserFollowStatus originalUserFollowStatus,
        final String viewSourceForAnalytics) {
        return Observable.fromCallable(new Callable<BackendFollowStatusChange>() {
            @Override
            public BackendFollowStatusChange call() throws Exception {
                return backendController.followUserV2(currentUserController.getSession(),
                    originalUserFollowStatus.getUsername());
            }
        })
            .map(updateAndNotifyFollowingChange(originalUserFollowStatus))
            .doOnNext(new Action1<UserFollowStatus>() {
                @Override
                public void call(UserFollowStatus followingStatus) {
                    trackFollowActionToAnalytics(followingStatus.getUsername(),
                        getAmplitudeOutcomeOfFollowAction(followingStatus.getStatus()),
                        viewSourceForAnalytics);
                }
            })
            .doOnNext(saveToDbAction())
            .flatMap(searchUpdateAndNotifyFollowerChange())
            .doOnNext(saveToDbAction())
            .doOnError(new Action1<Throwable>() {
                @Override
                public void call(Throwable throwable) {
                    if (throwable instanceof BackendException) {
                        trackFollowActionToAnalytics(originalUserFollowStatus.getUsername(),
                            AnalyticsPropertyValue.FollowUserOutcomeProperty.ERROR,
                            viewSourceForAnalytics);
                    }
                }
            })
            .toCompletable();
    }

    public Single<UserFollowStatus> followSimple(final UserFollowStatus originalUserFollowStatus) {
        return Single.fromCallable(new Callable<BackendFollowStatusChange>() {
            @Override
            public BackendFollowStatusChange call() throws Exception {
                return backendController.followUserV2(currentUserController.getSession(),
                    originalUserFollowStatus.getUsername());
            }
        }).map(new Func1<BackendFollowStatusChange, UserFollowStatus>() {
            @Override
            public UserFollowStatus call(BackendFollowStatusChange backendFollowStatusChange) {
                FollowStatus newStatus = backendFollowStatusChange.getStatus();
                UserFollowStatus followingStatus = originalUserFollowStatus.toBuilder()
                    .setDirection(FollowDirection.FOLLOWING)
                    .setStatus(newStatus)
                    .setCurrentUserFollowStatus(newStatus)
                    .build();

                saveToDbAction().call(followingStatus);

                try {
                    UserFollowStatus followerStatus = followStatusDao.queryForId(
                        UserFollowStatus.createId(followingStatus.getUsername(),
                            FollowDirection.FOLLOWER));
                    if (followerStatus != null) {
                        followerStatus = followerStatus.toBuilder()
                            .setCurrentUserFollowStatus(followingStatus.getStatus())
                            .build();
                        saveToDbAction().call(followerStatus);
                    }
                } catch (SQLException e) {
                    Timber.w(e, "Error updating follower status");
                }

                return followingStatus;
            }
        });
    }

    /**
     * <b>NOTE</b>: This directly executes a request to backend, it doesn't save intermediate
     * states to local DB.
     *
     * @param originalUserFollowStatus the user to unfollow from the current user point of view.
     * @return a {@link Completable}. That is, if the backend call goes well then the new
     * {@link UserFollowStatus} will be propagated through the right Subjects
     * ({@link #followingSubject} or {@link #followersSubject})
     */
    public Completable unfollow(final UserFollowStatus originalUserFollowStatus) {
        return Observable.fromCallable(new Callable<BackendFollowStatusChange>() {
            @Override
            public BackendFollowStatusChange call() throws Exception {
                return backendController.unFollow(currentUserController.getSession(),
                    originalUserFollowStatus.getUsername());
            }
        })
            .map(updateAndNotifyFollowingChange(originalUserFollowStatus))
            // We would want to delete the UNFOLLOWING for FOLLOW direction from DB but this
            // causes problem on UI updates. For example, when you unfollow from user profile and
            // go back to the list UI, the user follow status will not be removed from following
            // UI list.
            .doOnNext(saveToDbAction())
            .flatMap(searchUpdateAndNotifyFollowerChange())
            .doOnNext(saveToDbAction())
            .toCompletable();
    }

    public Single<UserFollowStatus> unfollowSimple(final UserFollowStatus originalUserFollowStatus) {
        return Single.fromCallable(new Callable<BackendFollowStatusChange>() {
            @Override
            public BackendFollowStatusChange call() throws Exception {
                return backendController.unFollow(currentUserController.getSession(),
                    originalUserFollowStatus.getUsername());
            }
        }).map(new Func1<BackendFollowStatusChange, UserFollowStatus>() {
            @Override
            public UserFollowStatus call(BackendFollowStatusChange backendFollowStatusChange) {
                FollowStatus newStatus = backendFollowStatusChange.getStatus();
                UserFollowStatus followingStatus = originalUserFollowStatus.toBuilder()
                    .setDirection(FollowDirection.FOLLOWING)
                    .setStatus(newStatus)
                    .setCurrentUserFollowStatus(newStatus)
                    .build();

                saveToDbAction().call(followingStatus);

                try {
                    UserFollowStatus followerStatus = followStatusDao.queryForId(
                        UserFollowStatus.createId(followingStatus.getUsername(),
                            FollowDirection.FOLLOWER));
                    if (followerStatus != null) {
                        followerStatus = followerStatus.toBuilder()
                            .setCurrentUserFollowStatus(followingStatus.getStatus())
                            .build();
                        saveToDbAction().call(followerStatus);
                    }
                } catch (SQLException e) {
                    Timber.w(e, "Error updating follower status");
                }

                return followingStatus;
            }
        });
    }

    /**
     * @param originalUserFollowStatus original status that updated by backend response
     * @return updated status
     */
    @NonNull
    private Func1<BackendFollowStatusChange, UserFollowStatus> updateAndNotifyFollowingChange(
        final UserFollowStatus originalUserFollowStatus) {
        return new Func1<BackendFollowStatusChange, UserFollowStatus>() {
            @Override
            public UserFollowStatus call(BackendFollowStatusChange backendFollowStatusChange) {
                // We need to at least update the status of the FOLLOWING direction
                FollowStatus newStatus = backendFollowStatusChange.getStatus();
                UserFollowStatus followingStatus = originalUserFollowStatus.toBuilder()
                    .setDirection(FollowDirection.FOLLOWING)
                    .setStatus(newStatus)
                    .setCurrentUserFollowStatus(newStatus)
                    .build();
                followingSubject.onNext(followingStatus);
                return followingStatus;
            }
        };
    }

    /**
     * @param viewSourceForAnalytics source view property for analytics event. As in which view
     * did follow action originate from
     * @param outcome The outcome property value of the follow request action
     * @param username username of the target user
     */
    private void trackFollowActionToAnalytics(String username, String outcome,
        String viewSourceForAnalytics) {
        if (!viewSourceForAnalytics.isEmpty()) {
            try {
                AnalyticsProperties analyticsProperties = new AnalyticsProperties()
                    .put(AnalyticsEventProperty.SOURCE, viewSourceForAnalytics)
                    // TODO target account type should be updated once Ambassadors are
                    // implemented
                    .put(AnalyticsEventProperty.TARGET_ACCOUNT_TYPE, "Normal")
                    .put(AnalyticsEventProperty.OUTCOME, outcome)
                    .put(AnalyticsEventProperty.DAYS_SINCE_FIRST_SESSION,
                        daysSinceInstallationUseCase.getDaysSinceInstallation()
                    )
                    .putYesNo(AnalyticsEventProperty.TARGET_FOLLOW_YOU, isFollower(username));
                amplitudeAnalyticsTracker.trackEvent(AnalyticsEvent.FOLLOW_USER, analyticsProperties);
                firebaseAnalyticsTracker.trackEvent(AnalyticsEvent.FOLLOW_USER, analyticsProperties);
            } catch (Exception e) {
                Timber.w(e, "Unable to fetch User Follow Status");
            }
        }
    }

    private void trackRevokeFollowerAnalyticsEvent(final UserFollowStatus userFollowStatus) {
        AnalyticsProperties analyticsProperties = new AnalyticsProperties().putYesNo(
            AnalyticsEventProperty.FOLLOWING_TARGET,
            userFollowStatus.getCurrentUserFollowStatus() == FollowStatus.FOLLOWING);
        amplitudeAnalyticsTracker.trackEvent(AnalyticsEvent.FOLLOWER_REMOVED, analyticsProperties);
        firebaseAnalyticsTracker.trackEvent(AnalyticsEvent.FOLLOWER_REMOVED, analyticsProperties);
    }

    private String getAmplitudeOutcomeOfFollowAction(FollowStatus status) {
        switch (status) {
            case FOLLOWING:
                return AnalyticsPropertyValue.FollowUserOutcomeProperty.FOLLOW;
            case PENDING:
                return AnalyticsPropertyValue.FollowUserOutcomeProperty.PENDING;
            case FAILED:
                return AnalyticsPropertyValue.FollowUserOutcomeProperty.ERROR;
            default:
                return null;
        }
    }

    /**
     * The user with given username is following the current user or not
     */
    public boolean isFollower(String username) throws SQLException {
        return followStatusDao.queryForId(
            UserFollowStatus.createId(username, FollowDirection.FOLLOWER)) != null;
    }

    /**
     * Track followAllFbFriends event to analytics.
     *
     * @param viewSourceForAnalytics source view property for analytics event. As in which view
     * the event was called
     * @param updatedStatuses the updated statuses returned by backend. If there is a network
     * error and backend doesn't update any {@link UserFollowStatus}, this will be empty list.
     * @param totalNrOfRequests Total number of {@link UserFollowStatus} sent to the backend.
     * This parameter is needed to get the number of failed updates in case of network error such
     * as backend exception where the updatedStatuses size is 0.
     */
    public void trackAddAllFbFriendsToAnalytics(String viewSourceForAnalytics,
        List<UserFollowStatus> updatedStatuses, int totalNrOfRequests) {
        //Track analytics events for only follow actions that have their viewSource defined
        if (!viewSourceForAnalytics.isEmpty()) {
            String outcome;
            int success = 0;
            for (UserFollowStatus updatedStatus : updatedStatuses) {
                if (!updatedStatus.getStatus().equals(FollowStatus.FAILED)) {
                    success++;
                }
            }
            if (success == totalNrOfRequests) {
                outcome = AnalyticsPropertyValue.AddAllFbFriendsOutcomeProperty.SUCCESS;
            } else if (success == 0) {
                outcome = AnalyticsPropertyValue.AddAllFbFriendsOutcomeProperty.ERROR;
            } else {
                outcome = AnalyticsPropertyValue.AddAllFbFriendsOutcomeProperty.PARTIAL;
            }
            AnalyticsProperties analyticsProperties = new AnalyticsProperties()
                .put(AnalyticsEventProperty.SOURCE, viewSourceForAnalytics)
                .put(AnalyticsEventProperty.OUTCOME, outcome)
                .put(AnalyticsEventProperty.SUCCESS, success)
                .put(AnalyticsEventProperty.FAIL, totalNrOfRequests - success)
                .put(AnalyticsEventProperty.TOTAL, totalNrOfRequests)
                .put(AnalyticsEventProperty.DAYS_SINCE_FIRST_SESSION,
                    daysSinceInstallationUseCase.getDaysSinceInstallation()
                );
            amplitudeAnalyticsTracker.trackEvent(AnalyticsEvent.FOLLOW_ALL_FB_FRIENDS, analyticsProperties);
            firebaseAnalyticsTracker.trackEvent(AnalyticsEvent.FOLLOW_ALL_FB_FRIENDS, analyticsProperties);
        }
    }

    /**
     * @param event see {@link AnalyticsEvent} to be tracked to analytics service
     */
    @NonNull
    private Action1<UserFollowStatus> trackAcceptRejectEventsToAnalytics(final String event) {
        return userFollowStatus -> {
            // TODO target account type should be updated once Ambassadors are implemented
            amplitudeAnalyticsTracker.trackEvent(event, AnalyticsEventProperty.FOLLOWER_ACCOUNT_TYPE, "Normal");
            firebaseAnalyticsTracker.trackEvent(event, AnalyticsEventProperty.FOLLOWER_ACCOUNT_TYPE, "Normal");
        };
    }

    /**
     * Find the FOLLOWER status from DB for the given <tt>followingStatus</tt>. If it's there
     * then it creates a new FOLLOWER status update with the same currentUserFollowStatus as the
     * <tt>followingStatus</tt>
     */
    @NonNull
    private Func1<UserFollowStatus, Observable<UserFollowStatus>>
    searchUpdateAndNotifyFollowerChange() {
        return new Func1<UserFollowStatus, Observable<UserFollowStatus>>() {
            @Override
            public Observable<UserFollowStatus> call(UserFollowStatus followingStatus) {
                try {
                    // Search if we've a FOLLOWER status for this same user
                    UserFollowStatus followerStatus = followStatusDao.queryForId(
                        UserFollowStatus.createId(followingStatus.getUsername(),
                            FollowDirection.FOLLOWER));
                    if (followerStatus != null) {
                        // Update the follower with the one in followingStatus
                        followerStatus = followerStatus.toBuilder()
                            .setCurrentUserFollowStatus(followingStatus.getStatus())
                            .build();
                        followersSubject.onNext(followerStatus);
                        return Observable.just(followerStatus);
                    }
                } catch (SQLException e) {
                    throw Exceptions.propagate(e);
                }
                return Observable.empty();
            }
        };
    }

    /**
     * Find the FOLLOWING status from DB for the given <tt>followingStatus</tt>. If it's there
     * then it notifies the subject about the status
     */
    @NonNull
    private Func1<UserFollowStatus, Observable<UserFollowStatus>>
    searchAndNotifyFollowingStatusFromDb() {
        return new Func1<UserFollowStatus, Observable<UserFollowStatus>>() {
            @Override
            public Observable<UserFollowStatus> call(UserFollowStatus followingStatus) {
                try {
                    // Search if we've a FOLLOWING status for this same user
                    UserFollowStatus followerStatus = followStatusDao.queryForId(
                        UserFollowStatus.createId(followingStatus.getUsername(),
                            FollowDirection.FOLLOWING));
                    if (followerStatus != null) {
                        // notify following subject status from db
                        followingSubject.onNext(followerStatus);
                        return Observable.just(followerStatus);
                    }
                } catch (SQLException e) {
                    throw Exceptions.propagate(e);
                }
                return Observable.empty();
            }
        };
    }

    @NonNull
    private Func1<BackendFollowStatusChange, UserFollowStatus> updateAndNotifyFollowerChange(
        final UserFollowStatus originalUserFollowStatus) {
        return new Func1<BackendFollowStatusChange, UserFollowStatus>() {
            @Override
            public UserFollowStatus call(BackendFollowStatusChange backendFollowStatusChange) {
                // We need to at least update the status of the FOLLOWER direction
                FollowStatus newStatus = backendFollowStatusChange.getStatus();
                UserFollowStatus followingStatus = originalUserFollowStatus.toBuilder()
                    .setDirection(FollowDirection.FOLLOWER)
                    .setStatus(newStatus)
                    .build();
                followersSubject.onNext(followingStatus);
                return followingStatus;
            }
        };
    }

    @NonNull
    private Action1<UserFollowStatus> saveToDbAction() {
        return new Action1<UserFollowStatus>() {
            @Override
            public void call(UserFollowStatus followingStatus) {
                try {
                    followStatusDao.createOrUpdate(followingStatus);
                } catch (SQLException e) {
                    // Saving to DB should not crash the actual action
                    Timber.w(e, "Unable to store new state to DB");
                }
            }
        };
    }

    @WorkerThread
    public void empty() throws InternalDataException {
        try {
            followStatusDao.deleteBuilder().delete();
        } catch (SQLException e) {
            throw new InternalDataException("Error emptying FollowUserStatus table", e);
        }
    }

    @WorkerThread
    public void updateUserFollowStatuses(final FollowLists fetchedUserFollowStatuses)
        throws InternalDataException {
        try {
            followStatusDao.callBatchTasks((Callable<Void>) () -> {
                // Empty the ufs database
                empty();

                List<UserFollowStatus> followings = fetchedUserFollowStatuses.getFollowings();

                for (UserFollowStatus following : followings) {
                    // Create following direction ufs db entries
                    followStatusDao.createOrUpdate(following);
                }
                //Let's update currentUserFollowStatus for followers
                List<UserFollowStatus> updatedFollowers =
                    updateCurrentUserFollowStatusesForFollowers(
                        fetchedUserFollowStatuses.getFollowers(), followings);

                for (UserFollowStatus follower : updatedFollowers) {
                    // Create follower direction ufs db entries
                    followStatusDao.createOrUpdate(follower);
                }
                return null;
            });
        } catch (Exception e) {
            throw new InternalDataException("Error creating FollowUserStatus entry", e);
        }
    }

    @WorkerThread
    @NonNull
    public List<UserFollowStatus> loadUfsListFromDbForReactedUsers(@NonNull List<Reaction> reactions) {
        if (reactions.isEmpty()) {
            return Collections.emptyList();
        }

        List<UserFollowStatus> ufsList = new ArrayList<>(reactions.size());
        for (Reaction reaction : reactions) {
            User dummyUser = User.minimalInfo("dummy", reaction.getUserName(),
                reaction.getUserRealOrUserName(), reaction.getUserProfilePictureUrl());
            ufsList.add(getUfsFromDbByUser(dummyUser, FollowDirection.FOLLOWING));
        }
        return ufsList;
    }

    @WorkerThread
    @NonNull
    public List<UserFollowStatus> loadUfsListFromDbForFollowUser(@NonNull List<FollowUser> followUsers) {
        if (followUsers.isEmpty()) {
            return Collections.emptyList();
        }

        List<UserFollowStatus> ufsList = new ArrayList<>(followUsers.size());
        for (FollowUser followUser : followUsers) {
            String username = followUser.getUsername();
            String realName = followUser.getRealName();
            String userOrRealName = TextUtils.isEmpty(realName) ? username : realName;
            User dummyUser = User.minimalInfo("dummy", followUser.getUsername(),
                userOrRealName, followUser.getProfileImageUrl(), followUser.getProfileDescription());
            ufsList.add(getUfsFromDbByUser(dummyUser, FollowDirection.FOLLOWING));
        }
        return ufsList;
    }

    public boolean isCurrentUserUfs(UserFollowStatus userFollowStatus) {
        return userFollowStatus.getUsername().equals(currentUserController.getUsername());
    }

    @WorkerThread
    public long loadPendingFollowRequestCount() throws InternalDataException {
        try {
            return followStatusDao.queryBuilder()
                .setCountOf(true)
                .where()
                .eq(UserFollowStatus.DbFields.DIRECTION, FollowDirection.FOLLOWER)
                .and()
                .eq(UserFollowStatus.DbFields.STATUS, FollowStatus.PENDING)
                .countOf();
        } catch (SQLException e) {
            throw new InternalDataException("Failed to load pending follow request count", e);
        }
    }

    /**
     * @return Summary of follow info for analytics
     * @throws InternalDataException
     */
    @WorkerThread
    public FollowCountSummary getFollowCountSummary() throws InternalDataException {
        try {
            List<FollowStatus> validStatuses = Arrays.asList(FollowStatus.FOLLOWING, FollowStatus.FRIENDS);

            long totalFollowings = followStatusDao.countOf(followStatusDao.queryBuilder()
                .setCountOf(true)
                .where()
                .eq(UserFollowStatus.DbFields.DIRECTION, FollowDirection.FOLLOWING)
                .and()
                .in(UserFollowStatus.DbFields.STATUS, validStatuses)
                .prepare());

            long totalFollowers = followStatusDao.countOf(followStatusDao.queryBuilder()
                .setCountOf(true)
                .where()
                .eq(UserFollowStatus.DbFields.DIRECTION, FollowDirection.FOLLOWER)
                .and()
                .in(UserFollowStatus.DbFields.STATUS, validStatuses)
                .prepare());

            return new FollowCountSummary((int) totalFollowers, (int) totalFollowings);
        } catch (SQLException e) {
            throw new InternalDataException(
                "Unable to fetch follow analytics summary from the local database: "
                    + e.getMessage(), e);
        }
    }


    /**
     * Check whether given user is a follower of the currently logged in user or is following the
     * currently logged in user.
     *
     * @param user User to check
     * @return Pair of isFollower and isFollowing Booleans
     */
    @WorkerThread
    @NonNull
    public Pair<Boolean, Boolean> getFollowRelationship(@NonNull User user) {
        return getFollowRelationship(user.getUsername());
    }

    @WorkerThread
    @NonNull
    private Pair<Boolean, Boolean> getFollowRelationship(@NonNull String username) {
        boolean isFollower = getFollowStatusFromDb(username, FollowDirection.FOLLOWER) == FollowStatus.FOLLOWING
            || getFollowStatusFromDb(username, FollowDirection.FOLLOWER) == FollowStatus.FRIENDS;
        boolean isFollowing = getFollowStatusFromDb(username, FollowDirection.FOLLOWING) == FollowStatus.FOLLOWING
            || getFollowStatusFromDb(username, FollowDirection.FOLLOWING) == FollowStatus.FRIENDS;
        return new Pair<>(isFollower, isFollowing);
    }

    @WorkerThread
    @NonNull
    public String getFollowRelationshipValueForAnalytics(@NonNull User user) {
        return getFollowRelationshipValueForAnalytics(user.getUsername());
    }

    @WorkerThread
    @NonNull
    public String getFollowRelationshipValueForAnalytics(@NonNull String username) {
        return getFollowRelationshipValueForAnalytics(getFollowRelationship(username));
    }

    @NonNull
    public String getFollowRelationshipValueForAnalytics(Pair<Boolean, Boolean> pair) {
        if (pair == null || pair.getFirst() == null || pair.getSecond() == null) {
            return AnalyticsEventProperty.NO_RELATIONSHIP;
        }
        boolean isFollower = pair.getFirst();
        boolean isFollowing = pair.getSecond();
        if (isFollower && isFollowing) {
            return AnalyticsEventProperty.FOLLOWING_EACH_OTHER;
        } else if (!isFollower && isFollowing) {
            return AnalyticsEventProperty.FOLLOWING_TARGET;
        } else if (isFollower) {
            return AnalyticsEventProperty.FOLLOWER_TARGET;
        } else {
            return AnalyticsEventProperty.NO_RELATIONSHIP;
        }
    }

    private static UserFollowStatus userToUserFollowStatus(
        User user,
        FollowDirection direction,
        @Nullable Context context
    ) {
        String description = user.getDescription();
        String descriptionText;
        if (description != null) {
            descriptionText = description;
        } else {
            String countryAndCity = null;
            if (context != null) {
                countryAndCity = TextFormatter.formatLocation(context, user.getCountry(), user.getCity());
            }
            descriptionText = countryAndCity;
        }
        return new UserFollowStatus(
            user.getUsername(),
            FollowStatus.UNFOLLOWING,
            user.getRealNameOrUsername(),
            descriptionText,
            user.getProfileImageUrl(),
            null,
            direction,
            FollowStatus.UNFOLLOWING,
            false
        );
    }

    /**
     * Loads user follow statuses for the specified direction from both local database and backend.
     * It merges the results from both sources, giving priority to backend data when there are conflicts.
     *
     * @param direction The direction to load follow statuses for (FOLLOWER or FOLLOWING)
     * @return A Single emitting the merged list of UserFollowStatus objects
     */
    @NonNull
    public Single<List<UserFollowStatus>> loadUserFollowStatuses(final FollowDirection direction) {
        if (direction == FollowDirection.UNKNOWN) {
            return Single.error(new IllegalArgumentException("Unsupported UNKNOWN direction"));
        }

        Single<List<UserFollowStatus>> local = loadUserFollowStatusFromDb(direction)
            .onErrorResumeNext(Single.just(Collections.emptyList()));

        Single<List<UserFollowStatus>> remote = loadFollowListsFromBackendAndCacheLocaly()
            .map(followLists -> {
                if (direction == FollowDirection.FOLLOWER) {
                    return followLists.getFollowers();
                } else {
                    return followLists.getFollowings();
                }
            })
            .onErrorResumeNext(Single.just(Collections.emptyList()));

        return Single.zip(local, remote, (localList, remoteList) -> {
            if (remoteList.isEmpty() && !localList.isEmpty()) {
                return localList;
            }
            
            if (!remoteList.isEmpty()) {
                return remoteList;
            }
            
            return Collections.emptyList();
        });
    }

    @WorkerThread
    @NonNull
    public UserFollowStatus loadUfsFromDbForFollowUser(@NonNull Friend friend) {
        return loadUfsFromDbByFriend(friend, FollowDirection.FOLLOWING);
    }

    @WorkerThread
    @NonNull
    public UserFollowStatus loadUfsFromDbForFollower(@NonNull Friend friend) {
        return loadUfsFromDbByFriend(friend, FollowDirection.FOLLOWER);
    }

    @WorkerThread
    @NonNull
    private UserFollowStatus loadUfsFromDbByFriend(@NonNull Friend friend, FollowDirection direction) {
        String username = friend.getUsername();
        String realName = friend.getRealName();
        String userOrRealName = TextUtils.isEmpty(realName) ? username : realName;
        User dummyUser = User.minimalInfo("dummy", friend.getUsername(),
            userOrRealName, friend.getProfileImageUrl(), friend.getProfileDescription());
        return getUfsFromDbByUser(dummyUser, direction);
    }
}
