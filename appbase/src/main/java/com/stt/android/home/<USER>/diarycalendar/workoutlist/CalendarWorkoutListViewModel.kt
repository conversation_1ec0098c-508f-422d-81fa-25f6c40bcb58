package com.stt.android.home.diary.diarycalendar.workoutlist

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.work.WorkManager
import com.amersports.formatter.unit.jscience.JScienceUnitConverter
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.UserSettingsController
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.user.MenstrualCycleRegularity
import com.stt.android.domain.workouts.GetWorkoutHeadersForDateUseCase
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.domain.workouts.WorkoutHeaderDataSource
import com.stt.android.home.diary.diarycalendar.workoutstats.WorkoutStat
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.menstrualcycle.CycleAction
import com.stt.android.menstrualcycle.LoggedFrom
import com.stt.android.menstrualcycle.MenstrualCycleAnalyticsUtils
import com.stt.android.menstrualcycle.MenstrualCyclePredictionJob
import com.stt.android.menstrualcycle.MenstrualCycleRemoteSyncJob
import com.stt.android.menstrualcycle.ShowCycleDayHelper
import com.stt.android.menstrualcycle.domain.DeleteDateFromMenstrualCycleUseCase
import com.stt.android.menstrualcycle.domain.FetchMenstrualCycleRegularityUseCase
import com.stt.android.menstrualcycle.domain.MenstrualCycleType
import com.stt.android.menstrualcycle.domain.ObservableHistoricalMenstrualCycleChangedUseCase
import com.stt.android.menstrualcycle.domain.ObservableMenstrualCycleListUseCase
import com.stt.android.menstrualcycle.domain.toCalc
import com.stt.android.menstrualcycle.domain.toDomain
import com.stt.android.utils.BrandFlavourConstants
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.drop
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import timber.log.Timber
import java.time.LocalDate
import java.time.ZoneId
import javax.inject.Inject

@HiltViewModel
class CalendarWorkoutListViewModel @Inject constructor(
    private val getWorkoutHeadersForDateUseCase: GetWorkoutHeadersForDateUseCase,
    private val workoutHeaderDataSource: WorkoutHeaderDataSource,
    private val currentUserController: CurrentUserController,
    private val infoModelFormatter: InfoModelFormatter,
    private val unitConverter: JScienceUnitConverter,
    private val observableMenstrualCycleListUseCase: ObservableMenstrualCycleListUseCase,
    private val deleteDateFromMenstrualCycleUseCase: DeleteDateFromMenstrualCycleUseCase,
    private val observableHistoricalMenstrualCycleChangedUseCase: ObservableHistoricalMenstrualCycleChangedUseCase,
    private val workManager: dagger.Lazy<WorkManager>,
    private val userSettingsController: UserSettingsController,
    private val fetchMenstrualCycleRegularityUseCase: FetchMenstrualCycleRegularityUseCase,
    private val menstrualCycleAnalyticsUtils: MenstrualCycleAnalyticsUtils,
    private val showCycleDayHelper: ShowCycleDayHelper,
    private val coroutinesDispatchers: CoroutinesDispatchers,
) : ViewModel() {
    private val _viewDataState: MutableStateFlow<CalendarWorkoutListViewData> =
        MutableStateFlow(CalendarWorkoutListViewData.Loading)
    val viewDataState: StateFlow<CalendarWorkoutListViewData> = _viewDataState.asStateFlow()

    val menstrualCycleRegularityChanged: SharedFlow<MenstrualCycleRegularity>
        get() = _menstrualCycleRegularityChanged.asSharedFlow()
    private val _menstrualCycleRegularityChanged = MutableSharedFlow<MenstrualCycleRegularity>()

    init {
        viewModelScope.launch {
            observableHistoricalMenstrualCycleChangedUseCase().drop(1).collect {
                MenstrualCyclePredictionJob.schedule(workManager.get())
            }
        }
    }

    fun loadWorkoutsByIds(workoutIds: List<Int>) {
        viewModelScope.launch(coroutinesDispatchers.io) {
            runSuspendCatching {
                val workouts = workoutHeaderDataSource.findByIds(workoutIds)
                    .sortedByDescending(WorkoutHeader::startTime)
                _viewDataState.update { current ->
                    (current as? CalendarWorkoutListViewData.Success)
                        ?.copy(workouts = workouts)
                        ?: CalendarWorkoutListViewData.Success(
                            statsAndPeriod = null,
                            workouts = workouts,
                        )
                }
            }.onFailure { e ->
                Timber.w(e, "Failed to load workouts by IDs")
                _viewDataState.value = CalendarWorkoutListViewData.Error
            }
        }
    }

    fun loadWorkoutsByDate(date: LocalDate) {
        viewModelScope.launch(coroutinesDispatchers.io) {
            val getMenstrualCycles = if (BrandFlavourConstants.PROVIDE_MC_FEATURE) {
                observableMenstrualCycleListUseCase(MenstrualCycleType.HISTORICAL)
            } else {
                flowOf(emptyList())
            }

            val getWorkouts = flow {
                val workouts = getWorkoutHeadersForDateUseCase(
                    GetWorkoutHeadersForDateUseCase.Params(
                        username = currentUserController.username,
                        date = date,
                        zoneId = ZoneId.systemDefault(),
                    )
                ).sortedByDescending(WorkoutHeader::startTime)
                emit(workouts)
            }
            getMenstrualCycles.combine(getWorkouts) { menstrualCycles, workouts ->
                val statsAndPeriod = CalendarWorkoutListStatsAndPeriodContainer(
                    stats = buildStats(
                        fromMenstrualCycleDays = showCycleDayHelper.getFromMenstrualCycleDays(menstrualCycles, date),
                        workouts = workouts,
                    ),
                    isPeriodDay = menstrualCycles.any { it.includedDates.contains(date) },
                    onDeletePeriodDay = { handleDeletePeriodDay(date) }
                )
                CalendarWorkoutListViewData.Success(
                    statsAndPeriod = statsAndPeriod,
                    workouts = workouts,
                )
            }.catch { e ->
                Timber.w(e, "Failed to load workouts by date")
                _viewDataState.value = CalendarWorkoutListViewData.Error
            }.collectLatest { success ->
                _viewDataState.value = success
            }
        }
    }

    private fun buildStats(
        fromMenstrualCycleDays: String?,
        workouts: List<WorkoutHeader>,
    ): List<WorkoutStat> = buildList {
        add(WorkoutStat.createActivityCountStat(workouts.size))
        add(
            WorkoutStat.createActivityDurationCountStat(
                infoModelFormatter,
                workouts.sumOf { it.totalTime })
        )
        add(
            WorkoutStat.createEnergyStat(
                infoModelFormatter,
                unitConverter,
                workouts.sumOf { it.energyConsumption })
        )
        fromMenstrualCycleDays?.let {
            add(WorkoutStat.createFromMenstrualCycleStartDateStat(it))
        }
    }

    private fun handleDeletePeriodDay(date: LocalDate) {
        viewModelScope.launch(coroutinesDispatchers.io) {
            val deletedDateResult = deleteDateFromMenstrualCycleUseCase.run(
                DeleteDateFromMenstrualCycleUseCase.Params(date)
            )

            val menstrualCycleSettings = userSettingsController.settings.getMenstrualCycleSetting()
            val regularityInSettings = menstrualCycleSettings?.cycleRegularity
            val newRegularity = fetchMenstrualCycleRegularityUseCase(
                FetchMenstrualCycleRegularityUseCase.Params(
                    menstrualCycleSettings?.cycleLength,
                    regularityInSettings?.toCalc()
                )
            )?.toDomain()
            if (newRegularity != null && regularityInSettings != newRegularity) {
                _menstrualCycleRegularityChanged.emit(newRegularity)
            }

            deletedDateResult?.let {
                menstrualCycleAnalyticsUtils.trackDeleted(
                    LoggedFrom.DAY_VIEW,
                    if (it.any { menstrualCycle -> menstrualCycle.includedDates.isNotEmpty() }) CycleAction.DELETE_DAY else CycleAction.DELETE_CYCLE,
                    newRegularity ?: MenstrualCycleRegularity.NOT_SURE
                )
            }

            MenstrualCycleRemoteSyncJob.schedule(workManager.get())
        }
    }
}
