package com.stt.android.home.dashboardv2.ui.widgets

import android.content.Context
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.DrawStyle
import androidx.compose.ui.graphics.drawscope.Fill
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.graphics.nativeCanvas
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.core.content.res.ResourcesCompat
import com.stt.android.R
import com.stt.android.compose.theme.cloudyGrey
import com.stt.android.home.diary.diarycalendar.bubbles.DiaryBubbleParameters
import com.stt.android.home.diary.diarycalendar.bubbles.DiaryBubbleType
import com.stt.android.menstrualcycle.domain.MenstrualDateType
import com.stt.android.menstrualcycle.domain.isNotFeatureHistory
import java.time.LocalDate
import kotlin.math.min

@Composable
internal fun DiaryBubble(
    modifier: Modifier = Modifier,
    bubbleType: DiaryBubbleType = DiaryBubbleType.NoBubbleType,
    startDate: LocalDate = LocalDate.now(),
    menstrualDateType: MenstrualDateType = MenstrualDateType.NOTHING,
    isDashboard: Boolean = false,
    drawDateEnabled: Boolean = false,
) {
    val context = LocalContext.current
    val backgroundColor = MaterialTheme.colorScheme.background
    val restDayBubbleRadius =
        context.resources.getDimension(R.dimen.dashboard_diary_calendar_rest_day_radius)
    val futureDateBubbleRadius =
        context.resources.getDimension(R.dimen.dashboard_diary_calendar_future_day_radius)
    val dateTextHeight = if (drawDateEnabled)
        context.resources.getDimension(R.dimen.text_size_medium) else 0f

    val borderRadiusRatio = if (isDashboard) {
        DASHBOARD_MENSTRUAL_DATE_BORDER_RADIUS_RATIO_OF_HEIGHT
    } else {
        MENSTRUAL_DATE_BORDER_RADIUS_RATIO_OF_HEIGHT
    }

    val restBubbleColor = if (menstrualDateType.isNotFeatureHistory()) {
        MaterialTheme.colorScheme.surface
    } else {
        MaterialTheme.colorScheme.cloudyGrey
    }

    val nearBlackColor = MaterialTheme.colorScheme.onSurface

    val verticalPadding = futureDateBubbleRadius * VERTICAL_PADDING_RATIO_OF_WIDTH

    Box(
        modifier = modifier
            .padding(vertical = 1.dp)
    ) {
        Canvas(modifier = Modifier.fillMaxSize()) {
            val width = size.width
            val height = size.height
            if (width == 0f || height == 0f) {
                return@Canvas
            }

            drawMenstrualDateType(
                drawScope = this,
                dateType = menstrualDateType,
                borderRadius = (height - verticalPadding) * borderRadiusRatio,
                strokeWidth = 4f,
                color = backgroundColor,
                left = 0F,
                top = verticalPadding / 2,
                right = width,
                bottom = height - verticalPadding / 2F
            )

            when (bubbleType) {
                is DiaryBubbleType.TrainingDayBubbleType -> {
                    drawTrainingDayBubble(
                        context,
                        parameters = bubbleType.activityGroupsBubbleParameters,
                        fullBubbleSize = min(height - dateTextHeight, width) / 2.0f,
                        restDayBubbleRadius = restDayBubbleRadius
                    )
                }

                is DiaryBubbleType.RestDayBubbleType -> {
                    drawBubble(
                        color = restBubbleColor,
                        radius = restDayBubbleRadius
                    )
                }

                is DiaryBubbleType.TodayBubbleType -> {
                    drawBubble(
                        color = nearBlackColor,
                        radius = restDayBubbleRadius
                    )
                }

                is DiaryBubbleType.FutureDateBubbleType -> {
                    if (bubbleType.activityGroupsBubbleParameters.isNotEmpty()) {
                        drawPlannedWorkoutDayBubble(
                            context,
                            parameters = bubbleType.activityGroupsBubbleParameters,
                            fullBubbleSize = min(height - dateTextHeight, width) / 2.0f,
                            restDayBubbleRadius = restDayBubbleRadius
                        )
                    } else {
                        drawBubble(
                            color = backgroundColor,
                            radius = futureDateBubbleRadius
                        )
                    }
                }

                is DiaryBubbleType.NoBubbleType -> {
                    // Do nothing
                }
            }

            if (drawDateEnabled) {
                val dateText = startDate.dayOfMonth.toString()
                drawDate(
                    date = dateText,
                    textSize = context.resources.getDimension(R.dimen.text_size_medium),
                    textColor = nearBlackColor,
                    verticalPadding = verticalPadding
                )
            }
        }
    }
}

private fun DrawScope.drawTrainingDayBubble(
    context: Context,
    parameters: List<DiaryBubbleParameters>,
    fullBubbleSize: Float,
    restDayBubbleRadius: Float
) {
    val largestBubbleForDay = parameters.maxByOrNull { it.radius }

    if (largestBubbleForDay != null && largestBubbleForDay.radius > 0.0f) {
        val bubbleScale = (restDayBubbleRadius / (largestBubbleForDay.radius * fullBubbleSize))
            .coerceAtLeast(1.0f)

        parameters.forEach { param ->
            val radiusInPixels = fullBubbleSize * param.radius * bubbleScale
            val bubbleColor =
                ResourcesCompat.getColor(context.resources, param.colorRes, context.theme)
            drawBubble(
                color = Color(bubbleColor),
                radius = radiusInPixels
            )
        }
    }
}

private fun DrawScope.drawPlannedWorkoutDayBubble(
    context: Context,
    parameters: List<DiaryBubbleParameters>,
    fullBubbleSize: Float,
    restDayBubbleRadius: Float
) {
    val largestBubbleForDay = parameters.maxByOrNull { it.radius }

    if (largestBubbleForDay != null && largestBubbleForDay.radius > 0.0f) {
        val bubbleScale = (restDayBubbleRadius / (largestBubbleForDay.radius * fullBubbleSize))
            .coerceAtLeast(1.0f)

        parameters.forEach { param ->
            val radiusInPixels = fullBubbleSize * param.radius * bubbleScale
            val bubbleColor =
                ResourcesCompat.getColor(context.resources, param.colorRes, context.theme)
            drawOutlinedBubble(
                color = Color(bubbleColor),
                radius = radiusInPixels
            )
        }
    }
}

private fun DrawScope.drawBubble(color: Color, radius: Float) {
    drawCircle(
        color = color,
        radius = radius,
        center = Offset(size.width / 2, size.height / 2)
    )
}

private fun DrawScope.drawOutlinedBubble(color: Color, radius: Float) {
    drawCircle(
        color = color,
        radius = radius,
        center = Offset(size.width / 2, size.height / 2),
        style = Stroke(width = 1.dp.toPx())
    )
}

private fun DrawScope.drawDate(
    date: String,
    textSize: Float,
    textColor: Color,
    verticalPadding: Float
) {
    val paint = android.graphics.Paint().apply {
        color = textColor.toArgb()
        this.textSize = textSize
        textAlign = android.graphics.Paint.Align.CENTER
        isAntiAlias = true
    }

    val centerX = size.width / 2
    val posY = verticalPadding + textSize

    drawContext.canvas.nativeCanvas.drawText(
        date,
        centerX,
        posY,
        paint
    )
}

fun drawMenstrualDateType(
    drawScope: DrawScope,
    dateType: MenstrualDateType,
    borderRadius: Float = 0f,
    strokeWidth: Float = 4f,
    color: Color,
    left: Float,
    top: Float,
    right: Float,
    bottom: Float
) {
    val paintStyle = when (dateType) {
        MenstrualDateType.IN_HISTORY_AFTER_TODAY,
        MenstrualDateType.IN_PREDICTION,
        MenstrualDateType.START_OF_PREDICTION,
        MenstrualDateType.END_OF_PREDICTION,
        MenstrualDateType.END_OF_HISTORY_AFTER_TODAY -> Stroke(strokeWidth)
        else -> Fill
    }

    val lineWidth = when (paintStyle) {
        is Stroke -> paintStyle.width
        is Fill -> 0f
    }
    with(drawScope) {
        when (dateType) {
            MenstrualDateType.IN_HISTORY_AFTER_TODAY,
            MenstrualDateType.IN_PREDICTION -> {
                drawLine(
                    color = color,
                    start = Offset(left, top + lineWidth / 2),
                    end = Offset(right, top + lineWidth / 2),
                    strokeWidth = strokeWidth,
                    cap = StrokeCap.Round
                )
                drawLine(
                    color = color,
                    start = Offset(left, bottom - lineWidth / 2),
                    end = Offset(right, bottom - lineWidth / 2),
                    strokeWidth = strokeWidth,
                    cap = StrokeCap.Round
                )
            }

            MenstrualDateType.IN_HISTORY_NORMAL -> {
                drawRect(
                    color = color,
                    topLeft = Offset(left, top),
                    size = Size(right - left, bottom - top),
                    style = paintStyle
                )
            }

            MenstrualDateType.START_OF_PREDICTION,
            MenstrualDateType.START_OF_HISTORY -> {
                drawStartMenstrualDateType(
                    left = left + lineWidth / 2,
                    top = top + lineWidth / 2,
                    right = right,
                    bottom = bottom - lineWidth / 2,
                    borderRadius = borderRadius,
                    color = color,
                    paintStyle = paintStyle
                )
            }

            MenstrualDateType.END_OF_PREDICTION,
            MenstrualDateType.END_OF_HISTORY_AFTER_TODAY,
            MenstrualDateType.END_OF_HISTORY_NORMAL -> {
                drawEndMenstrualDateType(
                    left = left,
                    top = top + lineWidth / 2,
                    right = right - lineWidth / 2,
                    bottom = bottom - lineWidth / 2,
                    borderRadius = borderRadius,
                    color = color,
                    paintStyle = paintStyle
                )
            }

            MenstrualDateType.BOTH_START_AND_END_IN_HISTORY,
            MenstrualDateType.BOTH_START_AND_END_IN_PREDICTION -> {
                drawStartAndEndMenstrualDateType(
                    left = left,
                    top = top + lineWidth / 2,
                    right = right - lineWidth / 2,
                    bottom = bottom - lineWidth / 2,
                    borderRadius = borderRadius,
                    color = color,
                    paintStyle = paintStyle
                )
            }

            MenstrualDateType.NOTHING -> {
                // Do nothing
            }
        }
    }
}

private fun DrawScope.drawStartMenstrualDateType(
    left: Float,
    top: Float,
    right: Float,
    bottom: Float,
    borderRadius: Float,
    color: Color,
    paintStyle: DrawStyle
) {
    val path = Path().apply {
        moveTo(right, bottom)
        lineTo(left + borderRadius, bottom)
        arcTo(
            rect = Rect(left, bottom - borderRadius * 2, left + borderRadius * 2, bottom),
            startAngleDegrees = 90f,
            sweepAngleDegrees = 90f,
            forceMoveTo = false
        )
        lineTo(left, top + borderRadius)
        arcTo(
            rect = Rect(left, top, left + borderRadius * 2, top + borderRadius * 2),
            startAngleDegrees = 180f,
            sweepAngleDegrees = 90f,
            forceMoveTo = false
        )
        lineTo(right, top)
    }
    drawPath(path = path, color = color, style = paintStyle)
}

private fun DrawScope.drawEndMenstrualDateType(
    left: Float,
    top: Float,
    right: Float,
    bottom: Float,
    borderRadius: Float,
    color: Color,
    paintStyle: DrawStyle
) {
    val path = Path().apply {
        moveTo(left, top)
        lineTo(right - borderRadius, top)
        arcTo(
            rect = Rect(right - borderRadius * 2, top, right, top + borderRadius * 2),
            startAngleDegrees = 270f,
            sweepAngleDegrees = 90f,
            forceMoveTo = false
        )
        lineTo(right, bottom - borderRadius)
        arcTo(
            rect = Rect(right - borderRadius * 2, bottom - borderRadius * 2, right, bottom),
            startAngleDegrees = 0f,
            sweepAngleDegrees = 90f,
            forceMoveTo = false
        )
        lineTo(left, bottom)
    }
    drawPath(path = path, color = color, style = paintStyle)
}

private fun DrawScope.drawStartAndEndMenstrualDateType(
    left: Float,
    top: Float,
    right: Float,
    bottom: Float,
    borderRadius: Float,
    color: Color,
    paintStyle: DrawStyle
) {
    val width = right - left
    val height = bottom - top
    val actualBorderRadius = minOf(borderRadius, height / 2, width / 2)

    val path = Path().apply {
        moveTo(left + actualBorderRadius, top)
        lineTo(right - actualBorderRadius, top)
        arcTo(
            rect = Rect(right - actualBorderRadius * 2, top, right, top + actualBorderRadius * 2),
            startAngleDegrees = 270f,
            sweepAngleDegrees = 90f,
            forceMoveTo = false
        )
        lineTo(right, bottom - actualBorderRadius)
        arcTo(
            rect = Rect(right - actualBorderRadius * 2, bottom - actualBorderRadius * 2, right, bottom),
            startAngleDegrees = 0f,
            sweepAngleDegrees = 90f,
            forceMoveTo = false
        )
        lineTo(left + actualBorderRadius, bottom)
        arcTo(
            rect = Rect(left, bottom - actualBorderRadius * 2, left + actualBorderRadius * 2, bottom),
            startAngleDegrees = 90f,
            sweepAngleDegrees = 90f,
            forceMoveTo = false
        )
        lineTo(left, top + actualBorderRadius)
        arcTo(
            rect = Rect(left, top, left + actualBorderRadius * 2, top + actualBorderRadius * 2),
            startAngleDegrees = 180f,
            sweepAngleDegrees = 90f,
            forceMoveTo = false
        )
        close()
    }
    drawPath(path = path, color = color, style = paintStyle)
}

// View is used into various layout of different sizes, so the ratio is used to control the size of drawing.
private const val MENSTRUAL_DATE_BORDER_RADIUS_RATIO_OF_HEIGHT = 1 / 4F
private const val DASHBOARD_MENSTRUAL_DATE_BORDER_RADIUS_RATIO_OF_HEIGHT = 1 / 2F
private const val VERTICAL_PADDING_RATIO_OF_WIDTH = 1 / 10F
