package com.stt.android.ui.utils;

import android.graphics.Canvas;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.TransitionDrawable;
import android.os.SystemClock;

/**
 * Fake infinite transition drawable taking advantage of {@code TransitionDrawable} capabilities.
 */
public class InfiniteTransitionDrawable extends TransitionDrawable {
    private int durationMillis;
    /**
     * Should we do the transition in reverse
     */
    private boolean reverseTransition = true;
    /**
     * It's the first draw of the transition
     */
    private boolean startingTransition = true;
    private long initialTime;

    public InfiniteTransitionDrawable(Drawable[] layers) {
        super(layers);
    }

    @Override
    public void startTransition(int durationMillis) {
        startingTransition = true;
        super.startTransition(durationMillis);
        this.durationMillis = durationMillis;
    }

    @Override
    public void reverseTransition(int duration) {
        startingTransition = true;
        super.reverseTransition(duration);
    }

    /**
     * Highly inspired by the way {@code TransitionDrawable#draw(Canvas)}
     * detects if the transition should be finished or not. If transition is
     * finished then it's restarted in opposite direction.
     */
    @Override
    public void draw(Canvas canvas) {
        if (!isVisible()) {
            return;
        }
        if (startingTransition) {
            // The first record the starting time
            initialTime = SystemClock.uptimeMillis();
            startingTransition = false;
        } else {
            // Figure out the time elapsed
            float normalized = (float) (SystemClock.uptimeMillis() - initialTime) / durationMillis;
            boolean done = normalized >= 1.0f;
            if (done) {
                // If the transition duration has expired then restart it
                if (reverseTransition) {
                    reverseTransition(durationMillis);
                    reverseTransition = false;
                } else {
                    reverseTransition = true;
                    startTransition(durationMillis);
                }
            }
        }
        super.draw(canvas);
        // Always invalidate as we want to constantly redraw
        invalidateSelf();
    }
}
