package com.stt.android.ui.adapters;

import android.content.Context;
import android.content.res.ColorStateList;
import android.content.res.Resources;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;
import com.stt.android.R;
import com.stt.android.data.TimeUtils;
import com.stt.android.domain.summaries.SummaryHighlightedProperty;
import com.stt.android.domain.summaries.SummaryTimeFrameUnit;
import com.stt.android.domain.summaries.WorkoutSummary;
import com.stt.android.domain.user.MeasurementUnit;
import com.stt.android.infomodel.SummaryItem;
import com.stt.android.mapping.InfoModelFormatter;
import com.stt.android.utils.CalendarProvider;
import com.stt.android.workouts.details.values.WorkoutValue;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.SortedSet;
import timber.log.Timber;

public class WorkoutSummariesListAdapter extends FilterableExpandableListAdapter<WorkoutSummary> {
    private SummaryHighlightedProperty summaryHighlightedProperty;
    private double absoluteMaxProgressValue;
    private SummaryTimeFrameUnit summaryTimeFrameUnit;
    /**
     * Used when comparing workout summary to create the proper time frame text. So we avoid
     * creating unnecessary
     * objects every time a new row is drawn.
     * <p><em>In theory this values might expire if the list being is displayed remains open during
     * a day/week change.
     * But we don't expect it to happen in practice. </em></p>
     */
    private final long todayTimestamp;
    /**
     * Used to keep track of the last week/month/... time frame. See {@link #todayTimestamp} and
     * {@link
     * #createSummaryTimeFrame(com.stt.android.domain.summaries.WorkoutSummary)} for more details
     */
    private long lastTimeFrameTimestamp;
    private final int activityTypeIconIds[] = new int[] {
        R.id.firstActivityIcon, R.id.secondActivityIcon, R.id.thirdActivityIcon
    };
    private final String kCal;
    private final Resources resources;
    private final MeasurementUnit measurementUnit;
    private final LayoutInflater inflater;
    private List<WorkoutSummariesContainer> workoutSummariesByYear;
    private final String[] months;
    @NonNull
    private final InfoModelFormatter infoModelFormatter;

    /**
     * Constructor
     *
     * @param context The current context.
     * @param summariesOrderedByTimeDesc The workout summariesOrderedByTimeDesc to represent in the
     * ListView,
     * most recent one first.
     */
    public WorkoutSummariesListAdapter(Context context,
        List<WorkoutSummary> summariesOrderedByTimeDesc, MeasurementUnit unit,
        SummaryHighlightedProperty summaryHighlightedProperty,
        SummaryTimeFrameUnit summaryTimeFrameUnit,
        CalendarProvider calendarProvider,
        @NonNull InfoModelFormatter infoModelFormatter) {
        super(context);
        Timber.d("WorkoutSummariesListAdapter: summariesOrderedByTimeDesc to show: %d",
            summariesOrderedByTimeDesc.size());
        resources = context.getResources();
        inflater = (LayoutInflater) context.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
        measurementUnit = unit;
        kCal = " " + context.getString(com.stt.android.core.R.string.kcal);
        months = resources.getStringArray(R.array.abbreviated_months);
        this.infoModelFormatter = infoModelFormatter;

        todayTimestamp = System.currentTimeMillis();

        this.summaryHighlightedProperty = summaryHighlightedProperty;
        setSummaryTimeFrameUnit(summaryTimeFrameUnit, calendarProvider);

        // Calculate necessary data that will be used to decide proper values in each row
        setWorkoutSummaries(summariesOrderedByTimeDesc);
    }

    public void setWorkoutSummaries(List<WorkoutSummary> summariesOrderedByTimeDesc) {
        setOriginalValues(summariesOrderedByTimeDesc);
        processSummaries(summariesOrderedByTimeDesc);
    }

    public void setSummaryTimeFrameUnit(SummaryTimeFrameUnit summaryTimeFrameUnit, CalendarProvider calendarProvider) {
        this.summaryTimeFrameUnit = summaryTimeFrameUnit;
        switch (summaryTimeFrameUnit) {
            case WEEKLY:
            default:
                // Calculate timestamp from one week ago
                Calendar lastWeekDate = calendarProvider.getCalendar();
                lastWeekDate.clear();
                lastWeekDate.setTimeInMillis(todayTimestamp);
                lastWeekDate.add(Calendar.DATE, -7);
                lastTimeFrameTimestamp = lastWeekDate.getTimeInMillis();
                break;
            case MONTHLY:
                // Calculate timestamp from one week ago
                Calendar lastMonthDate = calendarProvider.getCalendar();
                lastMonthDate.clear();
                lastMonthDate.setTimeInMillis(todayTimestamp);
                lastMonthDate.add(Calendar.MONTH, -1);
                lastTimeFrameTimestamp = lastMonthDate.getTimeInMillis();
                break;
        }
    }

    public void setSummaryHighlightedProperty(SummaryHighlightedProperty summaryHighlightedProperty) {
        this.summaryHighlightedProperty = summaryHighlightedProperty;
    }

    private void processSummaries(List<WorkoutSummary> summariesOrderedByTimeDesc) {
        Timber.d("WorkoutSummariesListAdapter.processSummaries(%d)",
            summariesOrderedByTimeDesc.size());
        double absoluteMaxProgressValue = Long.MIN_VALUE;

        // Reset current summaries
        List<WorkoutSummariesContainer> tmpWorkoutSummariesByYear = new ArrayList<>();
        int previousSummaryYear = 0;
        WorkoutSummariesContainer currentSummariesContainer = null;
        for (WorkoutSummary summary : summariesOrderedByTimeDesc) {
            // Store the maximum progress value
            switch (summaryHighlightedProperty) {
                case TOTALS:
                case TIME:
                    if (summary.getTotalTimeInSeconds() > absoluteMaxProgressValue) {
                        absoluteMaxProgressValue = summary.getTotalTimeInSeconds();
                    }
                    break;
                case DISTANCE:
                    if (summary.getTotalDistanceInMeters() > absoluteMaxProgressValue) {
                        absoluteMaxProgressValue = summary.getTotalDistanceInMeters();
                    }
                    break;
                case WORKOUTS:
                    if (summary.getTotalWorkouts() > absoluteMaxProgressValue) {
                        absoluteMaxProgressValue = summary.getTotalWorkouts();
                    }
                    break;
                case ENERGY:
                    if (summary.getTotalEnergy() > absoluteMaxProgressValue) {
                        absoluteMaxProgressValue = summary.getTotalEnergy();
                    }
                    break;
                case AVG_HR:
                    if (summary.getAverageHeartRate() > absoluteMaxProgressValue) {
                        absoluteMaxProgressValue = summary.getAverageHeartRate();
                    }
                    break;
                case AVG_SPEED:
                    if (summary.getAverageSpeed() > absoluteMaxProgressValue) {
                        absoluteMaxProgressValue = summary.getAverageSpeed();
                    }
                    break;
                case AVG_PACE:
                    double pace = measurementUnit.toPaceUnit(summary.getAverageSpeed());
                    if (pace > absoluteMaxProgressValue) {
                        absoluteMaxProgressValue = pace;
                    }
                    break;
                case ASCENT:
                    if (summary.getTotalAscent() > absoluteMaxProgressValue) {
                        absoluteMaxProgressValue = summary.getTotalAscent();
                    }
                    break;
                default:
                    throw new IllegalArgumentException(
                        "Unknown highlighted property for summaries: "
                            + summaryHighlightedProperty);
            }

            int summaryYear = summary.getStartYear();
            if (summaryYear != previousSummaryYear) {
                currentSummariesContainer = new WorkoutSummariesContainer(summaryYear);
                tmpWorkoutSummariesByYear.add(currentSummariesContainer);
                previousSummaryYear = summaryYear;
            }
            currentSummariesContainer.addWorkoutSummary(summary);
        }
        this.absoluteMaxProgressValue = absoluteMaxProgressValue;
        this.workoutSummariesByYear = tmpWorkoutSummariesByYear;
        Timber.d("WorkoutSummariesListAdapter.processSummaries workoutSummariesByYear size %d",
            workoutSummariesByYear.size());
        notifyDataSetChanged();
    }

    private void prepareRowView(ViewItemHolder viewItemHolder, WorkoutSummary workoutSummary,
        boolean isLastChild, int childPosition) {
        viewItemHolder.timeFrame.setText(createSummaryTimeFrame(workoutSummary));

        String totalValueText;
        String totalValueLabel = "";
        double progressBarPercentage;
        switch (summaryHighlightedProperty) {
            case TOTALS:
            case TIME:
                double totalTimeInSeconds = workoutSummary.getTotalTimeInSeconds();
                totalValueText = infoModelFormatter
                    .formatValue(SummaryItem.DURATION, totalTimeInSeconds)
                    .getValue();
                progressBarPercentage = absoluteMaxProgressValue != 0.0 ? (totalTimeInSeconds
                    / absoluteMaxProgressValue) * 100.0 : 0.0;
                break;
            case DISTANCE:
                double totalDistanceInMeters = workoutSummary.getTotalDistanceInMeters();
                WorkoutValue totalDistance = infoModelFormatter
                    .formatValue(SummaryItem.DISTANCE, totalDistanceInMeters);
                totalValueText = totalDistance.getValue();
                totalValueLabel = totalDistance.getUnitLabel(infoModelFormatter.getContext());
                progressBarPercentage = absoluteMaxProgressValue != 0.0 ? (totalDistanceInMeters
                    / absoluteMaxProgressValue) * 100.0 : 0.0;
                break;
            case WORKOUTS:
                int totalWorkouts = workoutSummary.getTotalWorkouts();
                totalValueText = String.valueOf(totalWorkouts);
                totalValueLabel = resources.getString(R.string.workouts);
                progressBarPercentage =
                    absoluteMaxProgressValue != 0.0 ? (totalWorkouts / absoluteMaxProgressValue)
                        * 100.0 : 0.0;
                break;
            case ENERGY:
                int totalEnergy = (int) workoutSummary.getTotalEnergy();
                totalValueText = String.valueOf(totalEnergy);
                totalValueLabel = resources.getString(com.stt.android.core.R.string.kcal);
                progressBarPercentage =
                    absoluteMaxProgressValue != 0.0 ? (totalEnergy / absoluteMaxProgressValue)
                        * 100.0 : 0.0;
                break;
            case AVG_HR:
                double totalAvgHr = workoutSummary.getAverageHeartRate();
                totalValueText = String.valueOf((int) totalAvgHr);
                totalValueLabel = resources.getString(com.stt.android.core.R.string.bpm);
                progressBarPercentage =
                    absoluteMaxProgressValue != 0.0 ? (totalAvgHr / absoluteMaxProgressValue)
                        * 100.0 : 0.0;
                break;
            case AVG_SPEED:
                double totalAvgSpeed = workoutSummary.getAverageSpeed();
                WorkoutValue avgSpeed = infoModelFormatter.formatValue(SummaryItem.AVGSPEED, totalAvgSpeed);
                totalValueText = avgSpeed.getValue();
                totalValueLabel = avgSpeed.getUnitLabel(infoModelFormatter.getContext());
                progressBarPercentage =
                    absoluteMaxProgressValue != 0.0 ? (totalAvgSpeed / absoluteMaxProgressValue)
                        * 100.0 : 0.0;
                break;
            case AVG_PACE:
                WorkoutValue avgPace = infoModelFormatter
                    .formatValue(SummaryItem.AVGPACE, workoutSummary.getAverageSpeed());
                totalValueText = avgPace.getValue();
                totalValueLabel = avgPace.getUnitLabel(infoModelFormatter.getContext());
                progressBarPercentage = absoluteMaxProgressValue != 0.0
                    ? (measurementUnit.toPaceUnit(workoutSummary.getAverageSpeed()) / absoluteMaxProgressValue) * 100.0
                    : 0.0;
                break;
            case ASCENT:
                double totalAscent = workoutSummary.getTotalAscent();
                WorkoutValue ascent = infoModelFormatter.formatValue(SummaryItem.ASCENTALTITUDE, totalAscent);
                totalValueText = ascent.getValue();
                totalValueLabel = ascent.getUnitLabel(infoModelFormatter.getContext());
                progressBarPercentage =
                    absoluteMaxProgressValue != 0.0 ? (totalAscent / absoluteMaxProgressValue)
                        * 100.0 : 0.0;
                break;
            default:
                throw new IllegalArgumentException(
                    "Unknown highlighted property for summaries: " + summaryHighlightedProperty);
        }

        viewItemHolder.totalValue.setText(totalValueText);
        viewItemHolder.totalLabel.setText(totalValueLabel);
        int progressBarColor = resources.getColor(R.color.summaryAccentColor);
        if (progressBarPercentage >= 100) {
            progressBarColor = resources.getColor(R.color.text_progressbar_full);
        }
        viewItemHolder.progressBar.setProgressTintList(ColorStateList.valueOf(progressBarColor));
        viewItemHolder.progressBar.setProgress((int) progressBarPercentage);
        SortedSet<WorkoutSummary.ActivityTyeWithFrequency> sortedActivityTypes =
            workoutSummary.getActivityTypesSorted();
        Iterator<WorkoutSummary.ActivityTyeWithFrequency> activityTypeIterator =
            sortedActivityTypes.iterator();
        for (ImageView activityTypeIcon : viewItemHolder.activityTypeIcons) {
            if (activityTypeIterator.hasNext()) {
                activityTypeIcon.setImageResource(
                    activityTypeIterator.next().getActivityType().getIconId());
                // We need to show the icon because we reuse views
                activityTypeIcon.setVisibility(View.VISIBLE);
            } else {
                // We need to hide the icon because we reuse views
                activityTypeIcon.setVisibility(View.INVISIBLE);
            }
        }

        int extraActivityTypesCount =
            sortedActivityTypes.size() - viewItemHolder.activityTypeIcons.length;
        if (extraActivityTypesCount > 0) {
            viewItemHolder.extraActivityTypes.setText("+" + extraActivityTypesCount);
            viewItemHolder.extraActivityTypes.setVisibility(View.VISIBLE);
        } else {
            viewItemHolder.extraActivityTypes.setVisibility(View.INVISIBLE);
        }

        int totalWorkouts = workoutSummary.getTotalWorkouts();
        viewItemHolder.totalWorkouts.setText(
            resources.getQuantityString(R.plurals.workouts_plural, totalWorkouts, totalWorkouts));

        if (summaryHighlightedProperty.isCompactMode()) {
            viewItemHolder.summaryContainer.setVisibility(View.INVISIBLE);
        } else {
            viewItemHolder.summaryContainer.setVisibility(View.VISIBLE);
            WorkoutValue totalDistance = infoModelFormatter
                .formatValue(SummaryItem.DISTANCE, workoutSummary.getTotalDistanceInMeters());
            viewItemHolder.summaryDistance.setText(
                totalDistance.getValue() + " " + totalDistance.getUnitLabel(infoModelFormatter.getContext()));

            viewItemHolder.summaryEnergy.setText((int) workoutSummary.getTotalEnergy() + kCal);

            String bpmUnit = " " + resources.getString(com.stt.android.core.R.string.bpm);
            String bpm = workoutSummary.getAverageHeartRate() > 0 ? Integer.toString(
                (int) workoutSummary.getAverageHeartRate()) + bpmUnit
                : "-" + bpmUnit;
            viewItemHolder.summaryBpm.setText(bpm);
        }

        if (isLastChild) {
            viewItemHolder.bottomDivider.setVisibility(View.INVISIBLE);
        } else {
            viewItemHolder.bottomDivider.setVisibility(View.VISIBLE);
        }

        if (childPosition == 0) {
            viewItemHolder.gradientBackground.setVisibility(View.VISIBLE);
        } else {
            viewItemHolder.gradientBackground.setVisibility(View.GONE);
        }
    }

    private String createSummaryTimeFrame(WorkoutSummary workoutSummary) {
        String timeFrame;
        switch (summaryTimeFrameUnit) {
            case WEEKLY:
            default:
                if (todayTimestamp >= workoutSummary.getStartTimestamp()
                    && todayTimestamp < workoutSummary.getEndTimestamp()) {
                    timeFrame = resources.getString(R.string.this_week);
                } else if (lastTimeFrameTimestamp >= workoutSummary.getStartTimestamp()
                    && lastTimeFrameTimestamp < workoutSummary.getEndTimestamp()) {
                    timeFrame = resources.getString(R.string.last_week);
                } else {
                    LocalDate date = TimeUtils.epochToLocalZonedDateTime(workoutSummary.getStartTimestamp()).toLocalDate();
                    timeFrame = TimeUtils.dateWithoutYearFormatter(Locale.getDefault()).format(date);
                }
                break;
            case MONTHLY:
                if (todayTimestamp >= workoutSummary.getStartTimestamp()
                    && todayTimestamp < workoutSummary.getEndTimestamp()) {
                    timeFrame = resources.getString(R.string.this_month);
                } else if (lastTimeFrameTimestamp >= workoutSummary.getStartTimestamp()
                    && lastTimeFrameTimestamp < workoutSummary.getEndTimestamp()) {
                    timeFrame = resources.getString(R.string.last_month);
                } else {
                    timeFrame = months[workoutSummary.getStartMonth()];
                }
                break;
        }
        return timeFrame;
    }

    @Override
    public int getGroupCount() {
        return workoutSummariesByYear.size();
    }

    @Override
    public int getChildrenCount(int groupPosition) {
        return workoutSummariesByYear.get(groupPosition).summaries.size();
    }

    @Override
    public WorkoutSummariesContainer getGroup(int groupPosition) {
        return workoutSummariesByYear.get(groupPosition);
    }

    @Override
    public WorkoutSummary getChild(int groupPosition, int childPosition) {
        return workoutSummariesByYear.get(groupPosition).summaries.get(childPosition);
    }

    @Override
    public long getGroupId(int groupPosition) {
        return groupPosition;
    }

    @Override
    public long getChildId(int groupPosition, int childPosition) {
        return childPosition;
    }

    @Override
    public boolean hasStableIds() {
        return true;
    }

    /**
     * Always return false so the header in the list view where this adapter is used can be
     * displayed.
     *
     * @return always false
     */
    @Override
    public boolean isEmpty() {
        return false;
    }

    @Override
    public View getGroupView(int groupPosition, boolean isExpanded, View convertView,
        ViewGroup parent) {
        ConstraintLayout groupView =
            (ConstraintLayout) (convertView == null ? inflater.inflate(
                R.layout.expandable_summary_list_group_view,
                parent, false) : convertView);

        WorkoutSummariesContainer summariesContainer = getGroup(groupPosition);
        String headerTotal;
        switch (summaryHighlightedProperty) {
            case TOTALS:
            case TIME:
            case WORKOUTS:
                headerTotal = infoModelFormatter
                    .formatValue(SummaryItem.DURATION, summariesContainer.totalTimeInSeconds)
                    .getValue();
                break;
            case DISTANCE:
                WorkoutValue totalDistance = infoModelFormatter
                    .formatValue(SummaryItem.DISTANCE, summariesContainer.totalDistanceInMeters);
                headerTotal = totalDistance.getValue()
                    + " "
                    + totalDistance.getUnitLabel(infoModelFormatter.getContext());
                break;
            case ENERGY:
                headerTotal =
                    (int) summariesContainer.totalEnergy + " " + resources.getString(com.stt.android.core.R.string.kcal);
                break;
            case AVG_HR:
                headerTotal = (int) summariesContainer.getAverageHr() + " " + resources.getString(
                    com.stt.android.core.R.string.bpm);
                break;
            case AVG_SPEED:
                WorkoutValue avgSpeed = infoModelFormatter
                    .formatValue(SummaryItem.AVGSPEED, summariesContainer.getAverageSpeed());
                headerTotal = avgSpeed.getValue()
                    + " "
                    + avgSpeed.getUnitLabel(infoModelFormatter.getContext());
                break;
            case AVG_PACE:
                WorkoutValue avgPace = infoModelFormatter
                    .formatValue(SummaryItem.AVGPACE, summariesContainer.getAverageSpeed());
                headerTotal = avgPace.getValue() + " " + avgPace.getUnitLabel(infoModelFormatter.getContext());
                break;
            case ASCENT:
                WorkoutValue ascent = infoModelFormatter
                    .formatValue(SummaryItem.ASCENTALTITUDE, summariesContainer.totalAscent);
                headerTotal = ascent.getValue() + " " + ascent.getUnitLabel(infoModelFormatter.getContext());;
                break;
            default:
                throw new IllegalArgumentException(
                    "Unknown highlighted property for summaries: " + summaryHighlightedProperty);
        }
        TextView year = groupView.findViewById(R.id.date);
        TextView totalValue = groupView.findViewById(R.id.totalValue);
        TextView activitiesSum = groupView.findViewById(R.id.activitiesSum);
        ImageView openIndicator = groupView.findViewById(R.id.openIndicator);
        View bottomDivider = groupView.findViewById(R.id.bottomDivider);
        year.setText(String.valueOf(summariesContainer.year));
        totalValue.setText(headerTotal);
        activitiesSum.setText(resources.getQuantityString(R.plurals.workouts_plural,
            summariesContainer.totalAmountOfWorkouts,
            summariesContainer.totalAmountOfWorkouts));

        float scaleY = 1f;
        float elevation = 0f;
        int bottomDividerVisibility = View.GONE;

        if (isExpanded) {
            scaleY = -1f;
            bottomDividerVisibility = View.VISIBLE;
        } else {
            if (groupPosition == getGroupCount() - 1) {
                elevation = resources.getDimension(R.dimen.size_spacing_xxsmall);
            }
        }

        openIndicator.setScaleY(scaleY);
        groupView.setElevation(elevation);
        bottomDivider.setVisibility(bottomDividerVisibility);

        return groupView;
    }

    @Override
    public View getChildView(int groupPosition, int childPosition, boolean isLastChild,
        View convertView, ViewGroup parent) {
        View view;
        if (convertView == null) {
            view = inflater.inflate(R.layout.workout_summary_list_item, parent, false);

            // Use ViewHolder pattern to improve scrolling:
            // http://developer.android.com/training/improving-layouts/smooth-scrolling
            // .html#ViewHolder
            TextView timeFrame = view.findViewById(R.id.workoutSummaryTimeFrame);
            ProgressBar progressBar = view.findViewById(R.id.summaryProgress);
            TextView totalValue = view.findViewById(R.id.summaryTotalValue);
            TextView totalValueLabel = view.findViewById(R.id.summaryTotalLabel);
            ImageView[] activityTypeIcons = new ImageView[activityTypeIconIds.length];
            for (int i = 0; i < activityTypeIconIds.length; i++) {
                int activityTypeIconId = activityTypeIconIds[i];
                activityTypeIcons[i] = view.findViewById(activityTypeIconId);
            }
            TextView extraActivityTypes = view.findViewById(R.id.extraActivityTypes);
            TextView summaryDistance = view.findViewById(R.id.workoutSummaryDistance);
            TextView summaryEnergy = view.findViewById(R.id.workoutSummaryEnergy);
            TextView summaryBpm = view.findViewById(R.id.workoutSummaryBpm);
            TextView totalWorkouts = view.findViewById(R.id.totalWorkoutsValue);
            View summaryContainer = view.findViewById(R.id.workoutItemSummaryContainer);
            View bottomDivider = view.findViewById(R.id.bottomDivider);
            View gradientBackground = view.findViewById(R.id.gradientBackground);
            ViewItemHolder holder =
                new ViewItemHolder(timeFrame, progressBar, totalValue, totalValueLabel,
                    activityTypeIcons, extraActivityTypes, summaryDistance, summaryEnergy,
                    summaryBpm, totalWorkouts, summaryContainer, bottomDivider, gradientBackground);
            view.setTag(holder);
        } else {
            view = convertView;
        }

        prepareRowView((ViewItemHolder) view.getTag(), getChild(groupPosition, childPosition),
            isLastChild, childPosition);

        return view;
    }

    @Override
    public boolean isChildSelectable(int groupPosition, int childPosition) {
        return true;
    }

    @Override
    protected void publishResults(List<WorkoutSummary> filteredObjects) {
        processSummaries(filteredObjects);
    }

    private static class ViewItemHolder {
        private final TextView timeFrame;
        private final ProgressBar progressBar;
        private final TextView totalValue;
        private final TextView totalLabel;
        private final ImageView[] activityTypeIcons;
        private final TextView extraActivityTypes;
        private final TextView summaryDistance;
        private final TextView summaryEnergy;
        private final TextView summaryBpm;
        private final TextView totalWorkouts;
        private final View summaryContainer;
        private final View bottomDivider;
        private final View gradientBackground;

        public ViewItemHolder(TextView timeFrame, ProgressBar progressBar, TextView totalValue,
            TextView totalLabel, ImageView[] activityTypeIcons, TextView extraActivityTypes,
            TextView summaryDistance, TextView summaryEnergy, TextView summaryBpm,
            TextView totalWorkouts, View summaryContainer, View bottomDivider,
            View gradientBackground) {
            this.timeFrame = timeFrame;
            this.progressBar = progressBar;
            this.totalValue = totalValue;
            this.totalLabel = totalLabel;
            this.activityTypeIcons = activityTypeIcons;
            this.extraActivityTypes = extraActivityTypes;
            this.summaryDistance = summaryDistance;
            this.summaryEnergy = summaryEnergy;
            this.summaryBpm = summaryBpm;
            this.totalWorkouts = totalWorkouts;
            this.summaryContainer = summaryContainer;
            this.bottomDivider = bottomDivider;
            this.gradientBackground = gradientBackground;
        }
    }

    /**
     * Convenient class to store workouts grouped by year
     */
    private static class WorkoutSummariesContainer {
        private final List<WorkoutSummary> summaries;
        private final int year;
        /**
         * Total amount of seconds in this year
         */
        private double totalTimeInSeconds;
        private double totalDistanceInMeters;
        private int totalAmountOfWorkouts;
        private double totalEnergy;
        private double averageHrByTime;
        private double totalHrTimeInSeconds;
        private double totalAverageSpeedTimeInSeconds;
        private double averageSpeedByTime;
        private double totalAscent;

        private WorkoutSummariesContainer(int year) {
            this.summaries = new ArrayList<>();
            this.year = year;
        }

        public void addWorkoutSummary(WorkoutSummary summary) {
            summaries.add(summary);
            totalTimeInSeconds += summary.getTotalTimeInSeconds();
            totalDistanceInMeters += summary.getTotalDistanceInMeters();
            totalAmountOfWorkouts += summary.getTotalWorkouts();
            totalEnergy += summary.getTotalEnergy();
            totalAscent += summary.getTotalAscent();


            totalHrTimeInSeconds += summary.getTotalHrTimeInSeconds();
            averageHrByTime += summary.getAverageHeartRate() * summary.getTotalHrTimeInSeconds();

            totalAverageSpeedTimeInSeconds += summary.getTotalAvgSpeedTimeInSeconds();
            averageSpeedByTime +=
                summary.getAverageSpeed() * summary.getTotalAvgSpeedTimeInSeconds();
        }

        public double getAverageHr() {
            return totalHrTimeInSeconds != 0.0 ? averageHrByTime / totalHrTimeInSeconds : 0.0;
        }

        public double getAverageSpeed() {
            return totalAverageSpeedTimeInSeconds != 0.0 ? averageSpeedByTime
                / totalAverageSpeedTimeInSeconds : 0.0;
        }
    }
}
