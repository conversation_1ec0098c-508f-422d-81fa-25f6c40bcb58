package com.stt.android.ui.fragments.workout;

import android.content.Context;
import android.content.res.Resources;
import android.os.Bundle;
import static android.text.format.DateUtils.DAY_IN_MILLIS;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewpager.widget.ViewPager;
import com.amersports.formatter.unit.jscience.JScienceUnitConverter;
import com.stt.android.R;
import com.stt.android.databinding.RecentWorkoutSummaryFragmentBinding;
import com.stt.android.domain.user.workout.RecentWorkoutSummary;
import com.stt.android.domain.workouts.WorkoutHeader;
import com.stt.android.mapping.InfoModelFormatter;
import com.stt.android.tasks.RecentWorkoutSummaryLoader;
import com.stt.android.ui.adapters.RecentWorkoutSummaryPagerAdapter;
import com.stt.android.ui.utils.PagerBulletStripUtility;
import com.stt.android.ui.utils.TextFormatter;
import com.stt.android.utils.CalendarProvider;
import com.stt.android.utils.DateUtils;
import com.stt.android.utils.STTConstants;
import dagger.hilt.android.AndroidEntryPoint;
import javax.inject.Inject;

@AndroidEntryPoint
public class RecentWorkoutSummaryFragment extends BaseWorkoutHeaderFragment
    implements ViewPager.OnPageChangeListener, RecentWorkoutSummaryLoader.Listener {
    public static final String FRAGMENT_TAG =
        "com.stt.android.ui.fragments.workout.RecentWorkoutSummaryFragment.FRAGMENT_TAG";

    public static RecentWorkoutSummaryFragment newInstance(WorkoutHeader workoutHeader) {
        RecentWorkoutSummaryFragment fragment = new RecentWorkoutSummaryFragment();
        Bundle args = new Bundle();
        args.putParcelable(STTConstants.ExtraKeys.WORKOUT_HEADER, workoutHeader);
        fragment.setArguments(args);
        return fragment;
    }

    @Inject
    protected CalendarProvider calendarProvider;

    @Inject
    InfoModelFormatter infoModelFormatter;

    @Inject
    JScienceUnitConverter unitConverter;

    private RecentWorkoutSummaryFragmentBinding binding;
    private RecentWorkoutSummaryPagerAdapter recentWorkoutSummaryPagerAdapter;
    private int lastKnownPage;
    private ImageView[] bullets;

    private RecentWorkoutSummary recentWorkoutSummary;

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container,
        Bundle savedInstanceState) {
        binding = RecentWorkoutSummaryFragmentBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        binding = null;
    }

    @Override
    public void onActivityCreated(@Nullable Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);

        binding.summaryViewPager.addOnPageChangeListener(this);

        loadRecentWorkoutSummary(getWorkoutHeader());
    }

    private void loadRecentWorkoutSummary(WorkoutHeader workoutHeader) {
        if (binding != null) {
            binding.recentWorkoutSummaryActivityIcon.setImageResource(
                workoutHeader.getActivityType().getIconId());
        }
        new RecentWorkoutSummaryLoader(getActivity(), this, workoutHeader, 30).start();
    }

    private void populateUi() {
        Context context = getActivity();
        if (recentWorkoutSummary != null && context != null && binding != null) {
            binding.recentWorkoutSummaryView.setRecentWorkoutSummary(recentWorkoutSummary, infoModelFormatter.getUnit());

            recentWorkoutSummaryPagerAdapter = new RecentWorkoutSummaryPagerAdapter(context,
                recentWorkoutSummary,
                getWorkoutHeader(),
                infoModelFormatter,
                unitConverter,
                null);
            binding.summaryViewPager.setAdapter(recentWorkoutSummaryPagerAdapter);
            binding.bulletStrip.removeAllViews();
            bullets = PagerBulletStripUtility.updateBulletStrip(
                recentWorkoutSummaryPagerAdapter.getCount(), binding.bulletStrip, binding.summaryViewPager);
            lastKnownPage = -1;

            binding.summaryViewPager.setCurrentItem(0);
            onPageSelected(0);

            // as per design, if the period ends today, we show the start time as e.g. "30 days ago"
            // otherwise we show it as e.g. "1 April 2015"

            Resources resources = context.getResources();
            long now = System.currentTimeMillis();
            long startTime = recentWorkoutSummary.summary.startTime;
            long endTime = recentWorkoutSummary.summary.endTime;
            if (DateUtils.isSameDay(endTime, now, calendarProvider)) {
                int dayGap = (int) ((now - startTime) / DAY_IN_MILLIS);
                binding.startDate.setText(resources.getQuantityString(R.plurals.days_ago, dayGap, dayGap));
            } else {
                binding.startDate.setText(TextFormatter.formatRelativeDateSpan(resources, startTime));
            }
            binding.endDate.setText(TextFormatter.formatRelativeDateSpan(resources, endTime));

            binding.title.setText(resources.getString(R.string.days_summary,
                (endTime - startTime) / DateUtils.DAY_IN_MILLIS));
        }
    }

    @Override
    protected void onWorkoutHeaderUpdated(WorkoutHeader updateWorkoutHeader) {
        WorkoutHeader current = getWorkoutHeader();
        if (!current.getActivityType().equals(updateWorkoutHeader.getActivityType())
            || current.getStartTime() != updateWorkoutHeader.getStartTime()) {
            // we don't want to re-fresh the UI if relevant info is not changed
            loadRecentWorkoutSummary(updateWorkoutHeader);
        }
    }

    @Override
    public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
        // do nothing
    }

    @Override
    public void onPageSelected(int position) {
        binding.dataType.setText(recentWorkoutSummaryPagerAdapter.getPageTitle(position));

        if (bullets != null) {
            bullets[position].setImageLevel(1);
            if (lastKnownPage >= 0) {
                bullets[lastKnownPage].setImageLevel(0);
            }
            lastKnownPage = position;
        }
    }

    @Override
    public void onPageScrollStateChanged(int state) {
        // do nothing
    }

    @Override
    public void onRecentWorkoutSummaryLoaded(@Nullable RecentWorkoutSummary recentWorkoutSummary) {
        if (!isAdded()) {
            return;
        }

        this.recentWorkoutSummary = recentWorkoutSummary;
        populateUi();
    }
}
