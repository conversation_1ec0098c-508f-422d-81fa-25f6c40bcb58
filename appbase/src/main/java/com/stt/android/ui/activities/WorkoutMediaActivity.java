package com.stt.android.ui.activities;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.view.ContextThemeWrapper;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewTreeObserver;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.core.util.Pair;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.viewpager.widget.ViewPager;
import com.google.android.material.snackbar.Snackbar;
import com.stt.android.R;
import com.stt.android.analytics.AmplitudeAnalyticsTracker;
import com.stt.android.analytics.AnalyticsEvent;
import com.stt.android.controllers.CurrentUserController;
import com.stt.android.controllers.PicturesController;
import com.stt.android.controllers.VideoModel;
import com.stt.android.databinding.WorkoutImagesActivityBinding;
import com.stt.android.di.navigation.WorkoutDetailsRewriteNavigator;
import com.stt.android.domain.user.ImageInformation;
import com.stt.android.domain.user.VideoInformation;
import com.stt.android.domain.workouts.WorkoutHeader;
import com.stt.android.multimedia.MultimediaPagerAdapter;
import com.stt.android.multimedia.sportie.SportieHelper;
import com.stt.android.remote.UserAgent;
import com.stt.android.tasks.DeleteWorkoutImageTask;
import com.stt.android.tasks.DeleteWorkoutVideoTask;
import com.stt.android.ui.utils.AnimationHelper;
import com.stt.android.ui.utils.DepthPageTransformer;
import com.stt.android.ui.utils.DialogHelper;
import com.stt.android.utils.STTConstants;
import dagger.hilt.android.AndroidEntryPoint;
import java.util.ArrayList;
import java.util.List;
import javax.inject.Inject;
import pub.devrel.easypermissions.EasyPermissions;
import rx.Observable;
import rx.Single;
import rx.Subscriber;
import rx.Subscription;
import rx.android.schedulers.AndroidSchedulers;
import rx.schedulers.Schedulers;
import timber.log.Timber;

@AndroidEntryPoint
public class WorkoutMediaActivity extends AppCompatActivity
    implements DeleteWorkoutImageTask.Listener,
    MultimediaPagerAdapter.Listener, View.OnClickListener, Toolbar.OnMenuItemClickListener,
    EasyPermissions.PermissionCallbacks, View.OnSystemUiVisibilityChangeListener,
    DeleteWorkoutVideoTask.Listener {
    private static final String KEY_IMAGES = "com.stt.android.KEY_IMAGES";
    private static final String KEY_VIDEOS = "com.stt.android.KEY_VIDEOS";
    private static final String KEY_SELECTED_POSITION = "com.stt.android.KEY_SELECTED_POSITION";

    private enum MediaContentType {
        IMAGE,
        VIDEO,
        UNKNOWN
    }

    /**
     * If workout header is provided, it assumes that all the images and videos are from the same
     * given workout.
     */
    public static Intent newStartIntent(Context context, @Nullable WorkoutHeader workoutHeader,
        @NonNull List<VideoInformation> videos, @NonNull List<ImageInformation> images,
        int selectedPosition) {
        return new Intent(context, WorkoutMediaActivity.class).putExtra(
            STTConstants.ExtraKeys.WORKOUT_HEADER, workoutHeader)
            .putParcelableArrayListExtra(KEY_VIDEOS, new ArrayList<>(videos))
            .putParcelableArrayListExtra(KEY_IMAGES, new ArrayList<>(images))
            .putExtra(KEY_SELECTED_POSITION, selectedPosition);
    }

    @Inject
    @UserAgent
    String userAgent;

    @Inject
    CurrentUserController currentUserController;

    @Inject
    LocalBroadcastManager localBroadcastManager;

    @Inject
    PicturesController picturesController;

    @Inject
    SportieHelper sportieHelper;

    @Inject
    VideoModel videoModel;

    @Inject
    WorkoutDetailsRewriteNavigator rewriteNavigator;

    @Inject
    AmplitudeAnalyticsTracker amplitudeAnalyticsTracker;

    private WorkoutImagesActivityBinding binding;

    private final StringBuilder stringBuilder = new StringBuilder();

    /**
     * Registered when workoutHeader variable has no value. Keeps the images updated if there are
     * changes to any workout.
     */
    private final BroadcastReceiver workoutUpdatedReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            WorkoutHeader workoutHeader =
                intent.getParcelableExtra(STTConstants.ExtraKeys.WORKOUT_HEADER);
            loadImages(workoutHeader.getUsername());
        }
    };

    /**
     * Registered when workoutHeader has a value. Keeps media updated for that specific workout.
     */
    private final BroadcastReceiver workoutHeaderMonitor = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (isFinishing() || workoutHeader == null) {
                return;
            }

            String action = intent.getAction();
            int workoutId;
            if (STTConstants.BroadcastActions.WORKOUT_UPDATED.equals(action)) {
                workoutId = intent.getIntExtra(STTConstants.ExtraKeys.WORKOUT_ID, 0);
            } else if (STTConstants.BroadcastActions.WORKOUT_SYNCED.equals(action)) {
                workoutId = intent.getIntExtra(STTConstants.ExtraKeys.WORKOUT_OLD_ID, 0);
            } else {
                return;
            }
            if (workoutHeader.getId() == workoutId) {
                workoutHeader = intent.getParcelableExtra(STTConstants.ExtraKeys.WORKOUT_HEADER);
                loadMedia();
            }
        }
    };

    @Nullable
    WorkoutHeader workoutHeader;

    private MultimediaPagerAdapter adapter;
    int selectedPosition;

    private final Handler handler = new Handler(Looper.getMainLooper());
    private Runnable runnable;
    private boolean isShowingActionBar;
    float toolbarOriginalY;

    private Subscription loadMediaSubscription;
    private Subscription loadImagesSubscription;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = WorkoutImagesActivityBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        List<ImageInformation> images;
        List<VideoInformation> videos;
        if (savedInstanceState == null) {
            Intent intent = getIntent();
            workoutHeader = intent.getParcelableExtra(STTConstants.ExtraKeys.WORKOUT_HEADER);
            selectedPosition = intent.getIntExtra(KEY_SELECTED_POSITION, 0);
            images = intent.getParcelableArrayListExtra(KEY_IMAGES);
            videos = intent.getParcelableArrayListExtra(KEY_VIDEOS);
        } else {
            workoutHeader = savedInstanceState.getParcelable(STTConstants.ExtraKeys.WORKOUT_HEADER);
            selectedPosition = savedInstanceState.getInt(KEY_SELECTED_POSITION, 0);
            images = savedInstanceState.getParcelableArrayList(KEY_IMAGES);
            videos = savedInstanceState.getParcelableArrayList(KEY_VIDEOS);
        }

        if (workoutHeader != null) {
            binding.openWorkoutBt.setVisibility(View.GONE);

            IntentFilter intentFilter =
                new IntentFilter(STTConstants.BroadcastActions.WORKOUT_UPDATED);
            intentFilter.addAction(STTConstants.BroadcastActions.WORKOUT_SYNCED);
            localBroadcastManager.registerReceiver(workoutHeaderMonitor, intentFilter);

            if (currentUserController.getUsername().equals(workoutHeader.getUsername())) {
                // own workout
                binding.toolbar.inflateMenu(R.menu.workout_images_activity);
                binding.toolbar.setOnMenuItemClickListener(this);
            }
        } else {
            binding.openWorkoutBt.setOnClickListener(this);
            IntentFilter intentFilter =
                new IntentFilter(STTConstants.BroadcastActions.WORKOUT_UPDATED);
            localBroadcastManager.registerReceiver(workoutUpdatedReceiver, intentFilter);
        }

        showMedia(videos, images);

        getWindow()
                .getDecorView()
                .setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                        | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                        | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN);
        getWindow().getDecorView().setOnSystemUiVisibilityChangeListener(this);

        setTitle(null);
        binding.toolbar.setNavigationIcon(com.stt.android.compose.ui.R.drawable.ic_action_back);
        binding.toolbar.setNavigationOnClickListener(this);
        binding.toolbar.getViewTreeObserver()
            .addOnPreDrawListener(new ViewTreeObserver.OnPreDrawListener() {
                @Override
                public boolean onPreDraw() {
                    binding.toolbar.getViewTreeObserver().removeOnPreDrawListener(this);
                    if (binding.toolbar.getY() > 0) {
                        toolbarOriginalY = binding.toolbar.getY();
                    }
                    return true;
                }
            });
    }

    private void loadImages(String userName) {
        unsubscribeLoadImagesSubscription();

        loadImagesSubscription = Single
            .fromCallable(() -> picturesController.findByUserName(userName, 100))
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe(new Subscriber<>() {
                @Override
                public void onCompleted() {
                    // do nothing
                }

                @Override
                public void onError(Throwable e) {
                    Timber.w(e, "Unable to load images in WorkoutMediaActivity.");
                }

                @Override
                public void onNext(List<ImageInformation> images) {
                    if (!images.isEmpty()) {
                        showMedia(new ArrayList<>(), images);
                    } else {
                        showNoMedia();
                    }
                }
            });
    }

    void loadMedia() {
        if (workoutHeader == null) {
            return;
        }
        unsubscribeLoadMediaSubscription();
        loadMediaSubscription =
            Observable.zip(loadVideos(workoutHeader),
                picturesController.loadImages(workoutHeader)
                    .subscribeOn(Schedulers.io()), Pair::new)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Subscriber<>() {
                    @Override
                    public void onCompleted() {
                        // do nothing
                    }

                    @Override
                    public void onError(Throwable e) {
                        Timber.e(e, "Failed to load media");
                        showNoMedia();
                    }

                    @Override
                    public void onNext(Pair<List<VideoInformation>, List<ImageInformation>> pair) {
                        List<VideoInformation> videos = pair.first;
                        List<ImageInformation> images = pair.second;
                        if ((videos == null || videos.isEmpty()) &&
                            (images == null || images.isEmpty())) {
                            showNoMedia();
                        } else {
                            showMedia(videos, images);
                        }
                    }
                });
    }

    @NonNull
    public Observable<List<VideoInformation>> loadVideos(@NonNull final WorkoutHeader workoutHeader) {
        return Observable.fromCallable(() -> videoModel.findByWorkoutId(workoutHeader.getId()))
            .subscribeOn(Schedulers.io());
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions,
        @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);

        // Forward results to EasyPermissions
        EasyPermissions.onRequestPermissionsResult(requestCode, permissions, grantResults, this);
    }

    void showNoMedia() {
        Context themeContext = new ContextThemeWrapper(WorkoutMediaActivity.this, R.style.WhiteTheme);
        DialogHelper.showDialog(themeContext, R.string.no_workout_image,
            (dialog, which) -> finish());
    }

    void showMedia(List<VideoInformation> videos, List<ImageInformation> images) {
        adapter = new MultimediaPagerAdapter(this, this, videos, images, userAgent);
        binding.pager.setAdapter(adapter);
        binding.pager.setCurrentItem(selectedPosition);
        binding.pager.setOffscreenPageLimit(2);
        binding.pager.setPageTransformer(true, new DepthPageTransformer());

        binding.pager.addOnPageChangeListener(new ViewPager.SimpleOnPageChangeListener() {
            @Override
            public void onPageSelected(int position) {
                selectedPosition = position;
                updateUi();
            }
        });
        updateUi();

        isShowingActionBar = true;
        handler.removeCallbacks(runnable);
        runnable = this::onMediaClicked;
        handler.postDelayed(runnable, 2000L);
    }

    private void unsubscribeLoadMediaSubscription() {
        if (loadMediaSubscription != null) {
            loadMediaSubscription.unsubscribe();
            loadMediaSubscription = null;
        }
    }

    private void unsubscribeLoadImagesSubscription() {
        if (loadImagesSubscription != null) {
            loadImagesSubscription.unsubscribe();
            loadImagesSubscription = null;
        }
    }

    private void setUiEnabled(boolean enabled) {
        binding.toolbar.setEnabled(enabled);
        binding.pager.setEnabled(enabled);
    }

    @Override
    protected void onSaveInstanceState(@NonNull Bundle outState) {
        super.onSaveInstanceState(outState);
        outState.putParcelable(STTConstants.ExtraKeys.WORKOUT_HEADER, workoutHeader);
        outState.putInt(KEY_SELECTED_POSITION, selectedPosition);
        outState.putParcelableArrayList(KEY_IMAGES, new ArrayList<>(adapter.getImages()));
        outState.putParcelableArrayList(KEY_VIDEOS, new ArrayList<>(adapter.getVideos()));
    }

    @Override
    protected void onStop() {
        adapter.onStop();
        super.onStop();
    }

    @Override
    protected void onDestroy() {
        adapter.onDestroy();
        localBroadcastManager.unregisterReceiver(workoutHeaderMonitor);
        localBroadcastManager.unregisterReceiver(workoutUpdatedReceiver);
        unsubscribeLoadMediaSubscription();
        unsubscribeLoadImagesSubscription();
        super.onDestroy();
    }

    void updateUi() {
        adapter.setCurrentPosition(selectedPosition);

        binding.openWorkoutBt.setVisibility(shouldShowOpenButton() ? View.VISIBLE : View.GONE);

        synchronized (stringBuilder) {
            stringBuilder.setLength(0);
            stringBuilder.append(selectedPosition + 1).append('/').append(adapter.getCount());
            binding.toolbar.setTitle(stringBuilder.toString());
        }
    }

    private boolean shouldShowOpenButton() {
        // We can not open the workout if there's no workout key (i.e. user's own unsynced
        // workout), so let's just hide the "open workout" button in this case.
        return workoutHeader == null && !TextUtils.isEmpty(getWorkoutKeyForCurrentPosition()) &&
            isShowingActionBar;
    }

    @Override
    public void onWorkoutImageDeleted(boolean successful) {
        AnimationHelper.fadeOut(binding.loadingSpinner);
        setUiEnabled(true);

        if (successful && selectedPosition > 0) {
            --selectedPosition;
        }

        Snackbar.make(binding.container,
            successful ? R.string.workout_image_deleted : R.string.workout_image_delete_failed,
            successful ? Snackbar.LENGTH_SHORT : Snackbar.LENGTH_LONG).show();
    }

    @Override
    public void onMediaClicked() {
        if (isShowingActionBar) {
            hideSystemNavigation();
            adapter.hideControls();
        } else {
            showSystemNavigation();
            adapter.showControls();
        }
    }

    @Override
    public void onZoom() {
        hideSystemNavigation();
    }

    @Override
    public void sendAnalyticsEvent(@AnalyticsEvent.EventName String event, String propertyName, String propertyValue) {
        amplitudeAnalyticsTracker.trackEvent(event, propertyName, propertyValue);
    }

    /**
     * This method also triggers {@link this#hideInternalMenuComponents()}
     * via {@link this#onSystemUiVisibilityChange(int)} listener which is registered when
     * activity is created
     */
    private void hideSystemNavigation() {
        getWindow().getDecorView()
            .setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                | View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                | View.SYSTEM_UI_FLAG_FULLSCREEN
                | View.SYSTEM_UI_FLAG_IMMERSIVE);
    }

    private void hideInternalMenuComponents() {
        AnimationHelper.move(binding.toolbar, 0.0F, 0.0F, binding.toolbar.getY(),
            -binding.toolbar.getHeight() - binding.toolbar.getY());
        if (workoutHeader == null) {
            AnimationHelper.fadeOut(binding.openWorkoutBt);
        }
    }

    /**
     * This method also triggers {@link this#showInternalMenuComponents()}
     * via {@link this#onSystemUiVisibilityChange(int)} listener which is registered when
     * activity is created
     */
    private void showSystemNavigation() {
        getWindow().getDecorView()
            .setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN);
    }

    private void showInternalMenuComponents() {
        AnimationHelper.move(binding.toolbar, 0.0F, 0.0F, binding.toolbar.getY(), toolbarOriginalY);
        if (shouldShowOpenButton()) {
            AnimationHelper.fadeIn(binding.openWorkoutBt);
        }
    }

    @Override
    public void onSystemUiVisibilityChange(int visibility) {
        if ((visibility & (View.SYSTEM_UI_FLAG_FULLSCREEN | View.SYSTEM_UI_FLAG_HIDE_NAVIGATION))
            == 0) {
            isShowingActionBar = true;
            showInternalMenuComponents();
        } else {
            isShowingActionBar = false;
            hideInternalMenuComponents();
        }
    }

    @Override
    public void onClick(View v) {
        if (v == binding.openWorkoutBt) {
            String workoutKey = getWorkoutKeyForCurrentPosition();
            String username = getUsernameForCurrentPosition();
            if (TextUtils.isEmpty(workoutKey) || TextUtils.isEmpty(username)) {
                // It turns out that openWorkoutBt is disabled if there's a workout header so we
                // can only open a workout if there's a key
                throw new IllegalStateException("Missing workout key");
            } else {
                rewriteNavigator.navigate(
                    this,
                    username,
                    null,
                    workoutKey,
                    null,
                    false,
                    false,
                    false,
                    false
                );
            }
        } else {
            finish();
        }
    }

    @Nullable
    private String getWorkoutKeyForCurrentPosition() {
        ImageInformation image = adapter.getImageInformation(selectedPosition);
        if (image != null) {
            return image.getWorkoutKey();
        } else {
            VideoInformation video = adapter.getVideoInformation(selectedPosition);
            if (video != null) {
                return video.getWorkoutKey();
            }
        }
        return null;
    }

    @Nullable
    private String getUsernameForCurrentPosition() {
        ImageInformation image = adapter.getImageInformation(selectedPosition);
        if (image != null) {
            return image.getUsername();
        } else {
            VideoInformation video = adapter.getVideoInformation(selectedPosition);
            if (video != null) {
                return video.getUsername();
            }
        }
        return null;
    }

    @Override
    public boolean onMenuItemClick(MenuItem item) {
        final int id = item.getItemId();
        if (id == R.id.discard) {
            boolean isVideo = getCurrentMediaContentType() == MediaContentType.VIDEO;
            Context themeContext = new ContextThemeWrapper(this, R.style.WhiteTheme);
            DialogHelper.showDialog(themeContext, R.string.delete,
                isVideo ? R.string.delete_workout_video_confirmation
                    : R.string.delete_workout_image_confirmation,
                (dialog, which) -> deleteCurrentMedia(), null);
            return true;
        } else {
            return false;
        }
    }

    private MediaContentType getCurrentMediaContentType(){
        ImageInformation image = adapter.getImageInformation(selectedPosition);
        if (image != null) {
            return MediaContentType.IMAGE;
        } else {
            VideoInformation video = adapter.getVideoInformation(selectedPosition);
            if (video != null) {
                return MediaContentType.VIDEO;
            }
        }
        return MediaContentType.UNKNOWN;
    }

    void deleteCurrentMedia() {
        switch (getCurrentMediaContentType()) {
            case IMAGE:
                deleteImage(adapter.getImageInformation(selectedPosition));
                break;
            case VIDEO:
                deleteVideo(adapter.getVideoInformation(selectedPosition));
                break;
            default:
                break;
        }
    }

    private void deleteImage(ImageInformation image) {
        AnimationHelper.fadeIn(binding.loadingSpinner);
        setUiEnabled(false);

        new DeleteWorkoutImageTask(this, workoutHeader, image).start();
    }

    private void deleteVideo(VideoInformation video) {
        AnimationHelper.fadeIn(binding.loadingSpinner);
        setUiEnabled(false);

        new DeleteWorkoutVideoTask(this, workoutHeader, video).start();
    }

    @Override
    public void onPermissionsGranted(int requestCode, @NonNull List<String> perms) {
    }

    @Override
    public void onPermissionsDenied(int requestCode, @NonNull List<String> perms) {
    }

    @Override
    public void onWorkoutVideoDeleted(boolean successful) {
        AnimationHelper.fadeOut(binding.loadingSpinner);
        setUiEnabled(true);

        if (successful && selectedPosition > 0) {
            --selectedPosition;
        }

        Snackbar.make(binding.container,
            successful ? R.string.workout_image_deleted : R.string.workout_image_delete_failed,
            successful ? Snackbar.LENGTH_SHORT : Snackbar.LENGTH_LONG).show();
    }
}
