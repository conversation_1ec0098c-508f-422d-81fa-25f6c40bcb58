package com.stt.android.ui.components

import android.annotation.SuppressLint
import android.content.Context
import android.content.SharedPreferences
import android.graphics.Color
import android.graphics.Rect
import android.os.Bundle
import android.view.LayoutInflater
import android.view.ViewTreeObserver.OnGlobalLayoutListener
import android.widget.FrameLayout
import androidx.annotation.ColorRes
import androidx.core.content.ContextCompat
import androidx.core.content.edit
import androidx.core.graphics.ColorUtils
import androidx.preference.PreferenceManager
import com.google.android.gms.maps.model.LatLng
import com.google.android.gms.maps.model.LatLngBounds
import com.stt.android.R
import com.stt.android.colorfultrack.WorkoutColorfulTrackMapData
import com.stt.android.colorfultrack.WorkoutGeoPointsWithColor
import com.stt.android.databinding.ViewWorkoutMapviewBinding
import com.stt.android.di.FeatureTogglePreferences
import com.stt.android.domain.sml.TraverseEvent
import com.stt.android.maps.MapType
import com.stt.android.maps.MapTypeHelper
import com.stt.android.maps.OnMapReadyCallback
import com.stt.android.maps.SuuntoBitmapDescriptorFactory
import com.stt.android.maps.SuuntoCameraOptions
import com.stt.android.maps.SuuntoCameraUpdate
import com.stt.android.maps.SuuntoMap
import com.stt.android.maps.SuuntoMapOptions
import com.stt.android.maps.SuuntoMapView
import com.stt.android.maps.SuuntoMarker
import com.stt.android.maps.SuuntoMarkerOptions
import com.stt.android.maps.SuuntoPolyline
import com.stt.android.maps.isAvalancheMap
import com.stt.android.maps.newCameraPosition
import com.stt.android.ui.map.MapHelper
import com.stt.android.ui.map.MapHelper.moveCameraToBoundLatLngs
import com.stt.android.ui.map.MapHelper.moveCameraToLatLng
import com.stt.android.ui.map.MapHelper.updateMapType
import com.stt.android.ui.map.RouteMarkerHelper.drawDashLine
import com.stt.android.ui.map.RouteMarkerHelper.drawEndPoint
import com.stt.android.ui.map.RouteMarkerHelper.drawEvent
import com.stt.android.ui.map.RouteMarkerHelper.drawRoute
import com.stt.android.ui.map.RouteMarkerHelper.drawRouteWithBorder
import com.stt.android.ui.map.RouteMarkerHelper.drawRouteWithColor
import com.stt.android.ui.map.RouteMarkerHelper.drawStartPoint
import com.stt.android.ui.map.SuuntoScaleBarDefaultOptionsFactory
import com.stt.android.utils.LogUtils.debugLog
import com.stt.android.utils.LogUtils.getBundleSize
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber
import javax.inject.Inject

/**
 * Users of this class must forward all the life cycle methods from the Activity or Fragment
 * containing this view to the corresponding ones in this class. In particular, you must forward on
 * the following methods:
 *
 *  * onCreate(Bundle)
 *  * onStart()
 *  * onResume()
 *  * onPause()
 *  * onStop()
 *  * onDestroy()
 *  * onSaveInstanceState()
 *  * onLowMemory()
 *
 */
@SuppressLint("ViewConstructor") // Not used from XML
@AndroidEntryPoint
open class WorkoutMapView(
    context: Context,
    mapOptions: SuuntoMapOptions?,
    topMapPadding: Int?
) : FrameLayout(context), SuuntoMap.OnMapClickListener {

    protected val binding: ViewWorkoutMapviewBinding
    protected val mapView: SuuntoMapView

    /**
     * You must check if it's null and if it's then you must use
     * [SuuntoMapView.getMapAsync]. See [executeCallback] as example
     */
    protected var map: SuuntoMap? = null

    private val mapPadding = Rect(0, 0, 0, 0)
    private val scaleBarTopPadding: Int
        get() = if (mapType.isAvalancheMap) {
            resources.getDimensionPixelOffset(R.dimen.map_compass_padding_top) +
                resources.getDimensionPixelOffset(R.dimen.size_spacing_medium)
        } else {
            resources.getDimensionPixelOffset(R.dimen.map_scale_bar_margin_top)
        }
    private val compassTopPadding: Int
        get() = resources.getDimensionPixelOffset(R.dimen.size_spacing_medium)
    private var mapType: MapType = MapTypeHelper.DEFAULT_MAP_TYPE

    private var onClickListener: OnClickListener? = null

    private var cameraStored = false
    private var currentWorkoutDrawingOptions: WorkoutDrawingOptions? = null
    private var currentActiveRoutePolylines = mutableListOf<SuuntoPolyline>()
    private var currentDashlines = mutableListOf<SuuntoPolyline>()
    private var currentInactiveMultisportPartPolylines = mutableListOf<SuuntoPolyline>()
    private var startPointMarker: SuuntoMarker? = null
    private var endPointMarker: SuuntoMarker? = null
    private var currentActivityTypeChangeMarkers = mutableListOf<SuuntoMarker>()
    private var currentTraverseEventMarkers = mutableListOf<SuuntoMarker>()

    private var onScaleListener: SuuntoMap.OnScaleListener? = null

    private val colorfulSuuntoPolyLines = mutableListOf<SuuntoPolyline>()

    // selected lap line for colorful track
    private val selectedColorfulTrackPolyline = mutableListOf<SuuntoPolyline>()
    private val unSelectedLapLineAlphaValue = 0x60
    private var currentColorfulTrackCameraBounds: LatLngBounds? = null

    private var lastCameraUpdate: SuuntoCameraUpdate? = null

    @Inject
    @FeatureTogglePreferences
    lateinit var featureTogglePreferences: SharedPreferences

    init {
        @Suppress("LeakingThis")
        binding = ViewWorkoutMapviewBinding.inflate(LayoutInflater.from(context), this)
        val defaultMapOptions = SuuntoMapOptions().apply {
            uiAttribution = resources.getBoolean(R.bool.maps_logo_enabled)
            uiLogo = resources.getBoolean(R.bool.maps_logo_enabled)
            uiCompass = true
            uiMapToolbar = false
            showMyLocationMarker = false
        }
        val mergedMapOptions = mapOptions?.merge(defaultMapOptions) ?: defaultMapOptions
        mapView = SuuntoMapView(context, mergedMapOptions)
        @Suppress("LeakingThis")
        addView(mapView, 0)
        mapView.getMapAsync { map: SuuntoMap ->
            // Disable all gestures here instead of layout XML. If disabled in XML, Mapbox won't
            // allow enabling the gestures later.
            map.getUiSettings().setAllGesturesEnabled(false)
            map.addOnMapClickListener(this@WorkoutMapView)
            <EMAIL> = map
        }
        if (topMapPadding != null) {
            setTopMapPadding(topMapPadding)
        }
    }

    fun showScaleBar(optionsFactory: SuuntoScaleBarDefaultOptionsFactory) {
        mapView.getMapAsync { map: SuuntoMap ->
            onScaleListener?.let { map.removeOnScaleListener(it) }
            val listener = object : SuuntoMap.OnScaleListener {
                override fun onScaleBegin() {
                    map.showScaleBar(
                        optionsFactory.createWithTopMarginRes(
                            scaleBarTopPadding.toFloat(),
                            R.dimen.map_scale_bar_margin_left
                        )
                    )
                }

                override fun onScaleEnd() {
                    map.removeScaleBar()
                }
            }
            onScaleListener = listener
            map.addOnScaleListener(listener)
        }
    }

    private fun hideScaleBar() {
        map?.run {
            removeScaleBar()
            onScaleListener?.let { removeOnScaleListener(it) }
            onScaleListener = null
        }
    }

    fun changeMapType(mapType: MapType) {
        this.mapType = mapType
        updateMapType()
    }

    private fun updateMapType() {
        executeCallback {
            updateMapType(it, mapType, binding.credit)
            mapView.post {
                updateMapPadding()
                mapView.requestLayout()
            }
        }
    }

    val cameraCenter: LatLng? get() = map?.getCameraPosition()?.target

    val mapsProviderName: String
        get() = mapView.getProviderName()

    fun showColorfulTrackWorkout(
        workoutColorfulTrackMapData: WorkoutColorfulTrackMapData,
    ) {
        executeCallback { map ->
            if (cameraStored) {
                cameraStored = false
                getStoredCameraPosition(context).let {
                    map.moveCamera(it)
                    lastCameraUpdate = it
                }
            } else if (workoutColorfulTrackMapData.bounds != currentColorfulTrackCameraBounds) {
                // when bounds changed, move camera
                currentColorfulTrackCameraBounds = workoutColorfulTrackMapData.bounds
                lastCameraUpdate = moveCameraToBoundLatLngs(
                    resources,
                    map,
                    workoutColorfulTrackMapData.bounds,
                    false
                )
            }
            // when selected certain lap, draw lap track and make other track translucent
            if (workoutColorfulTrackMapData.updateLapTrack) {
                // after clear selected lap, recover colorful tracks (set transparency to 1)
                if (workoutColorfulTrackMapData.lapTracks.isEmpty()) {
                    recoverColorfulTracks()
                } else {
                    makeDrawnColorfulTrackTranslucent()
                }
            } else {
                drawStartAndEndMarkers(
                    map,
                    workoutColorfulTrackMapData.startPoint,
                    workoutColorfulTrackMapData.endPoint
                )
                // hide other route line
                hideNormalLine()
                // remove colorful line
                clearColorfulLine()
                // remove translucent line
                clearSelectLapLines()
                val colorfulTrackTranslucent = workoutColorfulTrackMapData.lapTracks.isNotEmpty()
                clearDashedLines()
                workoutColorfulTrackMapData.activityRoutesWithColor.forEach { workoutGeoPointsWithColor ->
                    val color = getColorInt(workoutGeoPointsWithColor.color)
                    colorfulSuuntoPolyLines.add(
                        drawRouteWithBorder(
                            context = context,
                            map = map,
                            latLngs = workoutGeoPointsWithColor.points.map { it.latLng },
                            color = if (colorfulTrackTranslucent) {
                                ColorUtils.setAlphaComponent(color, unSelectedLapLineAlphaValue)
                            } else {
                                color
                            },
                            width = R.dimen.route_map_thick_stroke_width,
                            zIndex = 0f,
                        )
                    )
                }
                workoutColorfulTrackMapData.nonActivityRoutesWithColor.forEach { workoutGeoPointsWithColor ->
                    val color = getColorInt(workoutGeoPointsWithColor.color)
                    currentDashlines.add(
                        drawDashLine(
                            context = context,
                            color = color,
                            map = map,
                            latLngs = workoutGeoPointsWithColor.points.map { it.latLng },
                            width = R.dimen.route_map_thick_stroke_width,
                        )
                    )
                }
            }
            drawSelectedLapForColorfulTrack(workoutColorfulTrackMapData.lapTracks)
        }
    }

    /**
     * draw highlight colorful track
     */
    private fun drawSelectedLapForColorfulTrack(lines: List<WorkoutGeoPointsWithColor>) {
        executeCallback { map ->
            lines.forEachIndexed { index, line ->
                selectedColorfulTrackPolyline.elementAtOrNull(index)?.let { polyline ->
                    polyline.setPoints(line.points.map { it.latLng })
                    if (!polyline.isVisible()) {
                        polyline.setVisible(true)
                    }
                    if (polyline.getColor() != getColorInt(line.color)) {
                        polyline.setColor(getColorInt(line.color))
                    }
                } ?: run {
                    selectedColorfulTrackPolyline.add(
                        drawRouteWithBorder(
                            context = context,
                            map = map,
                            latLngs = line.points.map { it.latLng },
                            color = getColorInt(line.color),
                            width = R.dimen.route_map_thick_stroke_width,
                            zIndex = 1.0f,
                        )
                    )
                }
            }
            if (lines.size < selectedColorfulTrackPolyline.size) {
                selectedColorfulTrackPolyline.subList(
                    lines.size,
                    selectedColorfulTrackPolyline.size
                )
                    .forEach {
                        if (it.isVisible()) {
                            it.setVisible(false)
                        }
                    }
            }
        }
    }

    private fun makeDrawnColorfulTrackTranslucent() {
        // make all colorful track line become translucent
        colorfulSuuntoPolyLines.forEach {
            val translucentColor =
                ColorUtils.setAlphaComponent(it.getColor(), unSelectedLapLineAlphaValue)
            if (translucentColor != it.getColor()) {
                it.setColor(translucentColor)
            }
        }
    }

    private fun recoverColorfulTracks() {
        colorfulSuuntoPolyLines.forEach {
            val originalColor = ColorUtils.setAlphaComponent(it.getColor(), 0xFF)
            it.setColor(originalColor)
        }
    }

    private fun clearSelectLapLines() {
        selectedColorfulTrackPolyline.forEach {
            it.remove()
        }
        selectedColorfulTrackPolyline.clear()
    }

    private fun hideNormalLine() {
        currentActiveRoutePolylines.forEach {
            it.setVisible(false)
        }
        currentInactiveMultisportPartPolylines.forEach {
            it.setVisible(false)
        }
    }

    private fun clearColorfulLine() {
        if (colorfulSuuntoPolyLines.isNotEmpty()) {
            colorfulSuuntoPolyLines.forEach {
                it.remove()
            }
            colorfulSuuntoPolyLines.clear()
        }
    }

    private fun clearDashedLines() {
        currentDashlines.forEach { it.remove() }
        currentDashlines.clear()
    }

    private fun getColorInt(@ColorRes colorId: Int): Int {
        var colorInt = Color.RED
        try {
            colorInt = context.getColor(colorId)
        } catch (e: Exception) {
            Timber.w(e, "color not found from resource")
        }
        return colorInt
    }

    fun showWorkout(
        activityRoutes: List<List<LatLng>>,
        nonActivityRoutes: List<List<LatLng>>,
        points: List<LatLng>,
        bounds: LatLngBounds?,
        animate: Boolean,
        isDiving: Boolean = false,
    ) {
        require(points.isNotEmpty()) { "We need at least one point to draw a workout" }

        drawWorkoutWithOptions(
            BasicWorkoutDrawingOptions(
                activityRoutes = activityRoutes,
                nonActivityRoutes = nonActivityRoutes,
                highlightedLapRoutePoints = points,
                nonHighlightedRoutePoints = emptyList(),
                startPoint = points.first(),
                endPoint = points.last(),
                bounds = bounds,
                animate = animate,
                disableZoomToBounds = points.size == 1 || isDiving
            )
        )
    }

    fun showWorkout(
        activityRoutes: List<List<LatLng>>,
        nonActivityRoutes: List<List<LatLng>>,
        highlightedLapPoints: List<LatLng>,
        nonHighlightedPoints: List<List<LatLng>>,
        startPoint: LatLng,
        endPoint: LatLng,
        bounds: LatLngBounds?,
        animate: Boolean,
        disableZoomToBounds: Boolean,
    ) {
        require(highlightedLapPoints.isNotEmpty() || nonHighlightedPoints.isNotEmpty()) {
            "We need at least one point to draw a workout"
        }

        drawWorkoutWithOptions(
            BasicWorkoutDrawingOptions(
                activityRoutes = activityRoutes,
                nonActivityRoutes = nonActivityRoutes,
                highlightedLapRoutePoints = highlightedLapPoints,
                nonHighlightedRoutePoints = nonHighlightedPoints,
                startPoint = startPoint,
                endPoint = endPoint,
                bounds = bounds,
                animate = animate,
                disableZoomToBounds = disableZoomToBounds
            )
        )
    }

    fun showMultisportWorkout(
        activityRoutes: List<List<LatLng>>,
        nonActivityRoutes: List<List<LatLng>>,
        points: List<LatLng>,
        bounds: LatLngBounds?,
        animate: Boolean,
        inactiveMultisportPartRoutes: List<List<LatLng>>,
        activityTypeChangeLocations: List<LatLng>,
    ) {
        require(points.isNotEmpty()) { "We need at least one point to draw a workout" }

        drawWorkoutWithOptions(
            MultisportWorkoutDrawingOptions(
                activityRoutes = activityRoutes,
                nonActivityRoutes = nonActivityRoutes,
                highlightedLapRoutePoints = points,
                nonHighlightedRoutePoints = emptyList(),
                startPoint = points.first(),
                endPoint = points.last(),
                bounds = bounds,
                animate = animate,
                inactiveMultisportPartRoutes = inactiveMultisportPartRoutes,
                activityTypeChangeLocations = activityTypeChangeLocations,
                disableZoomToBounds = points.size == 1,
            )
        )
    }

    fun showMultisportWorkout(
        activityRoutes: List<List<LatLng>>,
        nonActivityRoutes: List<List<LatLng>>,
        highlightedLapPoints: List<LatLng>,
        nonHighlightedPoints: List<List<LatLng>>,
        startPoint: LatLng,
        endPoint: LatLng,
        bounds: LatLngBounds?,
        animate: Boolean,
        inactiveMultisportPartRoutes: List<List<LatLng>>,
        activityTypeChangeLocations: List<LatLng>,
        disableZoomToBounds: Boolean,
    ) {
        require(highlightedLapPoints.isNotEmpty() || nonHighlightedPoints.isNotEmpty()) {
            "We need at least one point to draw a workout"
        }

        drawWorkoutWithOptions(
            MultisportWorkoutDrawingOptions(
                activityRoutes = activityRoutes,
                nonActivityRoutes = nonActivityRoutes,
                highlightedLapRoutePoints = highlightedLapPoints,
                nonHighlightedRoutePoints = nonHighlightedPoints,
                startPoint = startPoint,
                endPoint = endPoint,
                bounds = bounds,
                animate = animate,
                inactiveMultisportPartRoutes = inactiveMultisportPartRoutes,
                activityTypeChangeLocations = activityTypeChangeLocations,
                disableZoomToBounds = disableZoomToBounds,
            )
        )
    }

    fun showHuntingOrFishingWorkout(
        activityRoutes: List<List<LatLng>>,
        nonActivityRoutes: List<List<LatLng>>,
        points: List<LatLng>,
        bounds: LatLngBounds?,
        animate: Boolean,
        traverseEvents: List<TraverseEvent>,
    ) {
        require(points.isNotEmpty()) { "We need at least one point to draw a workout" }

        drawWorkoutWithOptions(
            HuntingOrFishingWorkoutDrawingOptions(
                activityRoutes = activityRoutes,
                nonActivityRoutes = nonActivityRoutes,
                highlightedLapRoutePoints = points,
                nonHighlightedRoutePoints = emptyList(),
                startPoint = points.first(),
                endPoint = points.last(),
                bounds = bounds,
                animate = animate,
                traverseEvents = traverseEvents,
                disableZoomToBounds = points.size == 1,
            )
        )
    }

    fun showHuntingOrFishingWorkout(
        activityRoutes: List<List<LatLng>>,
        nonActivityRoutes: List<List<LatLng>>,
        highlightedLapPoints: List<LatLng>,
        nonHighlightedPoints: List<List<LatLng>>,
        startPoint: LatLng,
        endPoint: LatLng,
        bounds: LatLngBounds?,
        animate: Boolean,
        traverseEvents: List<TraverseEvent>,
    ) {
        require(highlightedLapPoints.isNotEmpty() || nonHighlightedPoints.isNotEmpty()) {
            "We need at least one point to draw a workout"
        }

        drawWorkoutWithOptions(
            HuntingOrFishingWorkoutDrawingOptions(
                activityRoutes = activityRoutes,
                nonActivityRoutes = nonActivityRoutes,
                highlightedLapRoutePoints = highlightedLapPoints,
                nonHighlightedRoutePoints = nonHighlightedPoints,
                startPoint = startPoint,
                endPoint = endPoint,
                bounds = bounds,
                animate = animate,
                traverseEvents = traverseEvents,
                disableZoomToBounds = false,
            )
        )
    }

    /**
     * If `runsOrLifts` contains less than 2 LatLngs the behaviour is unexpected so it's
     * recommended that in those cases you use [showWorkout]
     */
    fun showSkiWorkout(
        activityRoutes: List<List<LatLng>>,
        nonActivityRoutes: List<List<LatLng>>,
        bounds: LatLngBounds?,
        animate: Boolean,
        runsOrLifts: List<List<LatLng>>,
    ) {
        require(runsOrLifts.isNotEmpty()) { "We need at least one run or lift to draw a workout" }

        val lifts = mutableListOf<List<LatLng>>()
        val runs = mutableListOf<List<LatLng>>()
        runsOrLifts.forEachIndexed { index, runOrLift ->
            if (index % 2 == 0) {
                lifts.add(runOrLift)
            } else {
                runs.add(runOrLift)
            }
        }

        drawWorkoutWithOptions(
            SkiWorkoutDrawingOptions(
                activityRoutes = activityRoutes,
                nonActivityRoutes = nonActivityRoutes,
                startPoint = runsOrLifts.first().first(),
                endPoint = runsOrLifts.last().last(),
                bounds = bounds,
                animate = animate,
                highlightedRuns = runs.toList(),
                nonHighlightedRuns = emptyList(),
                highlightedLifts = lifts.toList(),
                nonHighlightedLifts = emptyList(),
                disableZoomToBounds = false
            )
        )
    }

    fun showSkiWorkout(
        activityRoutes: List<List<LatLng>>,
        nonActivityRoutes: List<List<LatLng>>,
        startPoint: LatLng,
        endPoint: LatLng,
        bounds: LatLngBounds?,
        animate: Boolean,
        highlightedRuns: List<List<LatLng>>,
        nonHighlightedRuns: List<List<LatLng>>,
        highlightedLifts: List<List<LatLng>>,
        nonHighlightedLifts: List<List<LatLng>>,
        disableZoomToBounds: Boolean,
    ) {
        if (
            highlightedRuns.none() &&
                highlightedLifts.none() &&
                nonHighlightedRuns.none() &&
                nonHighlightedLifts.none()
        ) {
            Timber.i("showSkiWorkout: We need at least one run or lift to draw a workout")
        }
        drawWorkoutWithOptions(
            SkiWorkoutDrawingOptions(
                activityRoutes = activityRoutes,
                nonActivityRoutes = nonActivityRoutes,
                startPoint = startPoint,
                endPoint = endPoint,
                bounds = bounds,
                animate = animate,
                highlightedRuns = highlightedRuns,
                nonHighlightedRuns = nonHighlightedRuns,
                highlightedLifts = highlightedLifts,
                nonHighlightedLifts = nonHighlightedLifts,
                disableZoomToBounds = disableZoomToBounds,
            )
        )
    }

    fun onCreate(bundle: Bundle?) {
        var mapViewBundle: Bundle? = null
        if (bundle != null) {
            mapViewBundle = bundle.getBundle(MAPVIEW_BUNDLE_KEY + id)
            mapType = MapTypeHelper.getOrDefault(
                mapTypeName = bundle.getString(WORKOUT_MAP_TYPE_KEY + id).orEmpty(),
                defaultValue = mapType,
            )
            updateMapType()
        }
        mapView.onCreate(mapViewBundle)
        redrawCurrentWorkout()
    }

    fun onResume() {
        mapView.onResume()
    }

    fun onPause() {
        mapView.onPause()
    }

    fun onStart() {
        mapView.onStart()
    }

    fun onStop() {
        mapView.onStop()
        hideScaleBar()
    }

    fun onDestroy() {
        mapView.onDestroy()
    }

    fun onLowMemory() {
        mapView.onLowMemory()
    }

    fun onSaveInstanceState(outState: Bundle) {
        val id = id
        if (id == NO_ID) {
            return
        }
        debugLog(
            { "WorkoutMapView: saving bundle, BUNDLE_SIZE: %s" }
        ) { getBundleSize(outState) }

        outState.putString(WORKOUT_MAP_TYPE_KEY + id, mapType.name)

        // *** IMPORTANT ***
        // MapView requires that the Bundle you pass contain _ONLY_ MapView SDK
        // objects or sub-Bundles.
        var mapViewBundle = outState.getBundle(MAPVIEW_BUNDLE_KEY + id)
        if (mapViewBundle == null) {
            mapViewBundle = Bundle()
            outState.putBundle(MAPVIEW_BUNDLE_KEY + id, mapViewBundle)
        }
        mapView.onSaveInstanceState(mapViewBundle)

        debugLog(
            { "WorkoutMapView: mapView saved, BUNDLE_SIZE: %s" }
        ) { getBundleSize(outState) }
    }

    fun setAllGesturesEnabled(enabled: Boolean) {
        executeCallback {
            it.getUiSettings().setAllGesturesEnabled(enabled)
        }
    }

    open fun setBottomMapPadding(bottomPadding: Int) {
        mapPadding.bottom = bottomPadding
        updateMapPadding()
    }

    fun setTopMapPadding(topPadding: Int) {
        mapPadding.top = topPadding
        updateMapPadding()
    }

    private fun updateMapPadding() {
        executeCallback {
            MapHelper.updateMapPaddingWithDefaults(
                resources = resources,
                map = it,
                paddingTop = mapPadding.top,
                paddingBottom = mapPadding.bottom,
                compassTopMargin = compassTopPadding,
                mapboxLogoLeftMargin = resources.getDimensionPixelSize(R.dimen.size_spacing_xsmall),
                mapCreditTextView = binding.credit,
                addCreditHeightToBottomPadding = true
            )
        }
    }

    private fun redrawCurrentWorkout() {
        currentWorkoutDrawingOptions?.let {
            currentWorkoutDrawingOptions = null
            drawWorkoutWithOptions(it)
        }
    }

    fun getMapAsync(callback: OnMapReadyCallback) {
        mapView.getMapAsync(callback)
    }

    private fun executeCallback(mapReadyCallback: OnMapReadyCallback) {
        map?.let {
            mapReadyCallback.onMapReady(it)
        } ?: run {
            mapView.getMapAsync(mapReadyCallback)
        }
    }

    override fun setOnClickListener(onClickListener: OnClickListener?) {
        this.onClickListener = onClickListener
    }

    override fun onMapClick(latLng: LatLng, placeName: String?) {
        onClickListener?.onClick(this)
    }

    fun storeCamera() {
        val cameraPosition = map?.getCameraPosition() ?: return
        PreferenceManager.getDefaultSharedPreferences(context).edit {
            putString(LATITUDE_KEY, cameraPosition.target.latitude.toString())
            putString(LONGITUDE_KEY, cameraPosition.target.longitude.toString())
            putFloat(ZOOM_KEY, cameraPosition.zoom)
            putFloat(BEARING_KEY, cameraPosition.bearing)
            putFloat(TILT_KEY, cameraPosition.tilt)

            cameraStored = true
        }
    }

    private fun drawStartAndEndMarkers(
        map: SuuntoMap,
        startPoint: LatLng?,
        endPoint: LatLng?,
    ) {
        startPoint?.let {
            startPointMarker?.setPosition(startPoint)
                ?: run {
                    startPointMarker = drawStartPoint(
                        context,
                        map,
                        startPoint,
                        isSinglePointRoute = startPoint == endPoint,
                        isWorkout = true,
                    )
                }
        }

        if (startPoint != endPoint) {
            endPoint?.let {
                endPointMarker?.let {
                    it.setPosition(endPoint)
                    it.setVisible(true)
                } ?: run {
                    endPointMarker = drawEndPoint(
                        context,
                        map,
                        endPoint,
                        isWorkout = true,
                    )
                }
            }
        } else {
            endPointMarker?.setVisible(false)
        }
    }

    private fun drawWorkoutWithOptions(options: WorkoutDrawingOptions) {
        fun doDrawWorkout() = executeCallback { map ->
            clearColorfulLine()
            clearSelectLapLines()
            val previousOptions = currentWorkoutDrawingOptions
            currentWorkoutDrawingOptions = options

            if (previousOptions == null || previousOptions.bounds != options.bounds) {
                when {
                    options.disableZoomToBounds -> {
                        // The workout has a manually added location, let's move to latLng and not zoom into bounds
                        lastCameraUpdate = moveCameraToLatLng(map, options.startPoint, options.animate)
                    }

                    cameraStored -> {
                        cameraStored = false
                        getStoredCameraPosition(context).let {
                            map.moveCamera(it)
                            lastCameraUpdate = it
                        }
                    }

                    options.bounds != null -> {
                        lastCameraUpdate = moveCameraToBoundLatLngs(resources, map, options.bounds, options.animate)
                    }

                    options.highlightedLapRoutePoints.size <= 1 -> {
                        lastCameraUpdate = moveCameraToLatLng(
                            map,
                            options.startPoint,
                            options.animate
                        )
                    }

                    else -> {
                        Timber.w("WorkoutDrawingOptions has null bounds for workout with route, can't update camera")
                    }
                }
            }

            // If there's big changes that could involve changing the drawing type or
            // marker anchors, redo everything. Otherwise we reuse markers and polylines
            // already on the map to reduce flickering. In practice this check should never be true,
            // as the map instance is used to show only one workout.
            if (previousOptions != null && previousOptions::class != options::class ||
                previousOptions?.startPoint != options.startPoint ||
                previousOptions.endPoint != options.endPoint
            ) {
                currentActiveRoutePolylines.forEach { it.remove() }
                currentActiveRoutePolylines.clear()

                clearDashedLines()

                currentActivityTypeChangeMarkers.forEach { it.remove() }
                currentActivityTypeChangeMarkers.clear()

                currentInactiveMultisportPartPolylines.forEach { it.remove() }
                currentInactiveMultisportPartPolylines.clear()

                currentTraverseEventMarkers.forEach { it.remove() }
                currentTraverseEventMarkers.clear()

                startPointMarker?.remove()
                startPointMarker = null

                endPointMarker?.remove()
                endPointMarker = null
            }

            fun updateOrAddRoutes(
                routes: List<List<LatLng>>,
                polylines: MutableList<SuuntoPolyline>,
                createPolyline: (List<LatLng>) -> SuuntoPolyline,
                updatePolyline: (SuuntoPolyline) -> Unit = {}
            ) {
                routes.forEachIndexed { index, points ->
                    polylines.elementAtOrNull(index)?.let {
                        it.setPoints(points)
                        it.setVisible(true)
                        updatePolyline(it)
                    } ?: run {
                        polylines.add(createPolyline(points))
                    }
                }

                if (routes.size < polylines.size) {
                    polylines.subList(routes.size + 1, polylines.size).forEach {
                        it.setVisible(false)
                    }
                }
            }

            fun <T> updateOrAddMarkers(
                markerData: List<T>,
                mapMarkers: MutableList<SuuntoMarker>,
                createMarker: (T) -> SuuntoMarker?,
                updateMarker: (T, SuuntoMarker) -> Unit
            ) {
                markerData.forEachIndexed { index, data ->
                    mapMarkers.elementAtOrNull(index)?.let {
                        updateMarker(data, it)
                        it.setVisible(true)
                    } ?: run {
                        createMarker(data)?.let { mapMarkers.add(it) }
                    }
                }

                if (markerData.size < mapMarkers.size) {
                    mapMarkers.subList(markerData.size + 1, mapMarkers.size).forEach {
                        it.setVisible(false)
                    }
                }
            }

            fun drawRouteAndCachePolyline(index: Int, latLngs: List<LatLng>) {
                currentActiveRoutePolylines.elementAtOrNull(index)?.let { polyline ->
                    polyline.setPoints(latLngs)
                    polyline.setVisible(true)
                    polyline.setColor(ContextCompat.getColor(context, R.color.map_route))
                } ?: run {
                    currentActiveRoutePolylines.add(drawRoute(
                        context = context,
                        map = map,
                        latLngs = latLngs,
                        isWorkout = true,
                        width = R.dimen.route_map_stroke_width,
                    ))
                }
            }

            fun drawDashline(dashLineRoutePoints: List<List<LatLng>>) {
                dashLineRoutePoints.forEach {
                    Timber.d("WorkoutMapView drawBasicRoute dashLineRoutePoints:${it.joinToString { point -> "${point.latitude},${point.longitude}" }}")
                    currentDashlines.add(
                        drawDashLine(
                            context = context,
                            color = ContextCompat.getColor(context, R.color.map_route),
                            map = map,
                            latLngs = it
                        )
                    )
                }
            }

            fun drawBasicRoute(
                activityRoutes: List<List<LatLng>>,
                nonActivityRoutes: List<List<LatLng>>,
                highlightedLapPoints: List<LatLng>,
                nonHighlightedPoints: List<List<LatLng>>,
            ) {
                if (nonActivityRoutes.isNotEmpty()) {
                    Timber.d("WorkoutMapView drawBasicRoute: ready to draw dash line")
                    clearDashedLines()
                }

                val nonHighlightedRouteColor =
                    ContextCompat.getColor(context, R.color.map_route_semitransparent)

                when {
                    nonHighlightedPoints.isNotEmpty() && nonActivityRoutes.isNotEmpty() -> {
                        drawDashline(nonActivityRoutes)
                        val filterHighlightedLapPoints = MapHelper.filterLapPointsNotInDashLines(
                            highlightedLapPoints,
                            nonActivityRoutes
                        )
                        filterHighlightedLapPoints.forEachIndexed { index, latLngs ->
                            drawRouteAndCachePolyline(index, latLngs)
                        }
                        val filterNonHighlightedPoints = MapHelper.filterNonHighlightedPointsNotInDashLines(
                            nonHighlightedPoints,
                            nonActivityRoutes
                        )
                        updateOrAddRoutes(
                            routes = filterNonHighlightedPoints,
                            polylines = currentActiveRoutePolylines.subList(
                                filterHighlightedLapPoints.size,
                                currentActiveRoutePolylines.size
                            ),
                            createPolyline = {
                                drawRouteWithColor(
                                    context,
                                    map,
                                    it,
                                    nonHighlightedRouteColor
                                )
                            },
                            updatePolyline = {
                                it.setColor(nonHighlightedRouteColor)
                            },
                        )
                    }

                    nonActivityRoutes.isNotEmpty() -> { // draw all of the record tracks with dashline
                        drawDashline(nonActivityRoutes)
                        activityRoutes.forEachIndexed { index, latLngs ->
                            drawRouteAndCachePolyline(index, latLngs)
                        }
                        // remove the rest of the polylines
                        updateOrAddRoutes(
                            routes = emptyList(),
                            polylines = currentActiveRoutePolylines.subList(
                                activityRoutes.size,
                                currentActiveRoutePolylines.size,
                            ),
                            createPolyline = {
                                throw IllegalStateException("Should not create any polylines here")
                            }
                        )
                    }

                    else -> {
                        drawRouteAndCachePolyline(0, highlightedLapPoints)

                        updateOrAddRoutes(
                            routes = nonHighlightedPoints,
                            polylines = currentActiveRoutePolylines.subList(1, currentActiveRoutePolylines.size),
                            createPolyline = {
                                drawRouteWithColor(
                                    context,
                                    map,
                                    it,
                                    nonHighlightedRouteColor
                                )
                            },
                            updatePolyline = {
                                it.setColor(nonHighlightedRouteColor)
                            },
                        )
                    }
                }
            }

            when (options) {
                is BasicWorkoutDrawingOptions -> {
                    drawStartAndEndMarkers(map, options.startPoint, options.endPoint)

                    drawBasicRoute(
                        activityRoutes = options.activityRoutes,
                        nonActivityRoutes = options.nonActivityRoutes,
                        highlightedLapPoints = options.highlightedLapRoutePoints,
                        nonHighlightedPoints = options.nonHighlightedRoutePoints,
                    )
                }

                is MultisportWorkoutDrawingOptions -> {
                    updateOrAddRoutes(
                        routes = options.inactiveMultisportPartRoutes,
                        polylines = currentInactiveMultisportPartPolylines,
                        createPolyline = {
                            drawRouteWithColor(
                                context,
                                map,
                                it,
                                ContextCompat.getColor(context, R.color.dark_gray)
                            )
                        }
                    )

                    drawStartAndEndMarkers(map, options.startPoint, options.endPoint)
                    drawBasicRoute(
                        activityRoutes = options.activityRoutes,
                        nonActivityRoutes = options.nonActivityRoutes,
                        highlightedLapPoints = options.highlightedLapRoutePoints,
                        nonHighlightedPoints = options.nonHighlightedRoutePoints,
                    )

                    updateOrAddMarkers(
                        options.activityTypeChangeLocations,
                        currentActivityTypeChangeMarkers,
                        updateMarker = { point, marker -> marker.setPosition(point) },
                        createMarker = {
                            map.addMarker(
                                SuuntoMarkerOptions()
                                    .icon(
                                        SuuntoBitmapDescriptorFactory(context)
                                            .fromResource(R.drawable.activity_change_marker)
                                    )
                                    .position(it)
                                    .anchor(0.5f, 0.5f)
                            )
                        }
                    )
                }

                is HuntingOrFishingWorkoutDrawingOptions -> {
                    drawStartAndEndMarkers(map, options.startPoint, options.endPoint)
                    drawBasicRoute(
                        activityRoutes = options.activityRoutes,
                        nonActivityRoutes = options.nonActivityRoutes,
                        highlightedLapPoints = options.highlightedLapRoutePoints,
                        nonHighlightedPoints = options.nonHighlightedRoutePoints,
                    )

                    updateOrAddMarkers(
                        markerData = options.traverseEvents.filter { it.location != null },
                        mapMarkers = currentTraverseEventMarkers,
                        updateMarker = { event, marker ->
                            val location = event.location!!
                            val point = LatLng(location.latitude, location.longitude)

                            marker.setPosition(point)
                            marker.setIcon(
                                SuuntoBitmapDescriptorFactory(context)
                                    .fromResource(event.iconRes)
                            )
                        },
                        createMarker = { event ->
                            val location = event.location!!
                            val point = LatLng(location.latitude, location.longitude)
                            drawEvent(context, map, point, event.iconRes)
                        }
                    )
                }

                is SkiWorkoutDrawingOptions -> {
                    drawStartAndEndMarkers(map, options.startPoint, options.endPoint)

                    val highlightedRunColor = ContextCompat.getColor(context, R.color.map_route)
                    val nonHighlightedRunColor =
                        ContextCompat.getColor(context, R.color.map_route_semitransparent)
                    val highlightedLiftColor = ContextCompat.getColor(context, R.color.map_ski_lift)
                    val nonHighlightedLiftColor =
                        ContextCompat.getColor(context, R.color.map_ski_lift_semitransparent)

                    var listStartIndex = 0
                    updateOrAddRoutes(
                        routes = options.highlightedRuns,
                        polylines = currentActiveRoutePolylines.subList(
                            listStartIndex,
                            (listStartIndex + options.highlightedRuns.size)
                                .coerceAtMost(currentActiveRoutePolylines.size)
                        ),
                        createPolyline = {
                            drawRouteWithColor(
                                context,
                                map,
                                it,
                                highlightedRunColor
                            )
                        },
                        updatePolyline = {
                            it.setColor(highlightedRunColor)
                        }
                    )

                    listStartIndex += options.highlightedRuns.size

                    updateOrAddRoutes(
                        routes = options.highlightedLifts,
                        polylines = currentActiveRoutePolylines.subList(
                            listStartIndex,
                            (listStartIndex + options.highlightedLifts.size)
                                .coerceAtMost(currentActiveRoutePolylines.size)
                        ),
                        createPolyline = {
                            drawRouteWithColor(
                                context,
                                map,
                                it,
                                highlightedLiftColor
                            )
                        },
                        updatePolyline = {
                            it.setColor(highlightedLiftColor)
                        }
                    )

                    listStartIndex += options.highlightedLifts.size

                    updateOrAddRoutes(
                        routes = options.nonHighlightedRuns,
                        polylines = currentActiveRoutePolylines.subList(
                            listStartIndex,
                            (listStartIndex + options.nonHighlightedRuns.size)
                                .coerceAtMost(currentActiveRoutePolylines.size)
                        ),
                        createPolyline = {
                            drawRouteWithColor(
                                context,
                                map,
                                it,
                                nonHighlightedRunColor
                            )
                        },
                        updatePolyline = {
                            it.setColor(nonHighlightedRunColor)
                        }
                    )

                    listStartIndex += options.nonHighlightedRuns.size

                    updateOrAddRoutes(
                        routes = options.nonHighlightedLifts,
                        polylines = currentActiveRoutePolylines.subList(
                            listStartIndex,
                            currentActiveRoutePolylines.size
                        ),
                        createPolyline = {
                            drawRouteWithColor(
                                context,
                                map,
                                it,
                                nonHighlightedLiftColor
                            )
                        },
                        updatePolyline = {
                            it.setColor(nonHighlightedLiftColor)
                        }
                    )
                }
            }
        }

        // In some devices (Samsung S3) this crashes if there's no proper height/width
        if (measuredHeight == 0 || measuredWidth == 0) {
            viewTreeObserver.addOnGlobalLayoutListener(
                object : OnGlobalLayoutListener {
                    override fun onGlobalLayout() {
                        val viewTreeObserver = viewTreeObserver
                        if (!viewTreeObserver.isAlive) {
                            return
                        }
                        viewTreeObserver.removeOnGlobalLayoutListener(this)
                        doDrawWorkout()
                    }
                }
            )
        } else {
            doDrawWorkout()
        }
    }

    fun moveCamera(update: SuuntoCameraUpdate) {
        executeCallback { map ->
            lastCameraUpdate = update
            map.moveCamera(update)
        }
    }

    fun resetLastCameraUpdate() {
        lastCameraUpdate?.let { cameraUpdate ->
            executeCallback { map ->
                map.moveCamera(cameraUpdate)
            }
        }
    }

    companion object {
        private const val MAPVIEW_BUNDLE_KEY = "MAPVIEW_BUNDLE_KEY_"
        private const val WORKOUT_MAP_TYPE_KEY = "WORKOUT_MAP_TYPE_"
        const val LATITUDE_KEY = "WorkoutMapViewLatitudeKey"
        const val LONGITUDE_KEY = "WorkoutMapViewLongitudeKey"
        const val BEARING_KEY = "WorkoutMapViewBearingKey"
        const val TILT_KEY = "WorkoutMapViewTiltKey"
        const val ZOOM_KEY = "WorkoutMapViewZoomKey"

        private fun getStoredCameraPosition(context: Context): SuuntoCameraUpdate {
            val preferences = PreferenceManager.getDefaultSharedPreferences(context)
            val latitude = preferences.getString(LATITUDE_KEY, "0.0")
            val longitude = preferences.getString(LONGITUDE_KEY, "0.0")
            val zoom = preferences.getFloat(ZOOM_KEY, 0.0f)
            val bearing = preferences.getFloat(BEARING_KEY, 0.0f)
            val tilt = preferences.getFloat(TILT_KEY, 0.0f)
            val target = LatLng(
                latitude!!.toDouble(),
                longitude!!.toDouble()
            )
            return newCameraPosition(
                SuuntoCameraOptions.Builder()
                    .target(target)
                    .bearing(bearing)
                    .tilt(tilt)
                    .zoom(zoom)
                    .build()
            )
        }
    }
}

sealed class WorkoutDrawingOptions(
    val activityRoutes: List<List<LatLng>>,
    val nonActivityRoutes: List<List<LatLng>>,
    val highlightedLapRoutePoints: List<LatLng>,
    val nonHighlightedRoutePoints: List<List<LatLng>>,
    val startPoint: LatLng,
    val endPoint: LatLng,
    val bounds: LatLngBounds?,
    val animate: Boolean,
    val disableZoomToBounds: Boolean,
)

data class RouteWithDashLinePoints(
    val solidLineRoutePoints: List<List<LatLng>>,
    val dashLineRoutePoints: List<List<LatLng>>,
)

class BasicWorkoutDrawingOptions(
    activityRoutes: List<List<LatLng>>,
    nonActivityRoutes: List<List<LatLng>>,
    highlightedLapRoutePoints: List<LatLng>,
    nonHighlightedRoutePoints: List<List<LatLng>>,
    startPoint: LatLng,
    endPoint: LatLng,
    bounds: LatLngBounds?,
    animate: Boolean,
    disableZoomToBounds: Boolean,
) : WorkoutDrawingOptions(
    activityRoutes = activityRoutes,
    nonActivityRoutes = nonActivityRoutes,
    highlightedLapRoutePoints = highlightedLapRoutePoints,
    nonHighlightedRoutePoints = nonHighlightedRoutePoints,
    startPoint = startPoint,
    endPoint = endPoint,
    bounds = bounds,
    animate = animate,
    disableZoomToBounds = disableZoomToBounds,
)

class MultisportWorkoutDrawingOptions(
    activityRoutes: List<List<LatLng>>,
    nonActivityRoutes: List<List<LatLng>>,
    highlightedLapRoutePoints: List<LatLng>,
    nonHighlightedRoutePoints: List<List<LatLng>>,
    startPoint: LatLng,
    endPoint: LatLng,
    bounds: LatLngBounds?,
    animate: Boolean,
    disableZoomToBounds: Boolean,
    val inactiveMultisportPartRoutes: List<List<LatLng>>,
    val activityTypeChangeLocations: List<LatLng>,
) : WorkoutDrawingOptions(
    activityRoutes = activityRoutes,
    nonActivityRoutes = nonActivityRoutes,
    highlightedLapRoutePoints = highlightedLapRoutePoints,
    nonHighlightedRoutePoints = nonHighlightedRoutePoints,
    startPoint = startPoint,
    endPoint = endPoint,
    bounds = bounds,
    animate = animate,
    disableZoomToBounds = disableZoomToBounds,
)

class HuntingOrFishingWorkoutDrawingOptions(
    activityRoutes: List<List<LatLng>>,
    nonActivityRoutes: List<List<LatLng>>,
    highlightedLapRoutePoints: List<LatLng>,
    nonHighlightedRoutePoints: List<List<LatLng>>,
    startPoint: LatLng,
    endPoint: LatLng,
    bounds: LatLngBounds?,
    animate: Boolean,
    disableZoomToBounds: Boolean,
    val traverseEvents: List<TraverseEvent>,
) : WorkoutDrawingOptions(
    activityRoutes = activityRoutes,
    nonActivityRoutes = nonActivityRoutes,
    highlightedLapRoutePoints = highlightedLapRoutePoints,
    nonHighlightedRoutePoints = nonHighlightedRoutePoints,
    startPoint = startPoint,
    endPoint = endPoint,
    bounds = bounds,
    animate = animate,
    disableZoomToBounds = disableZoomToBounds,
)

class SkiWorkoutDrawingOptions(
    activityRoutes: List<List<LatLng>>,
    nonActivityRoutes: List<List<LatLng>>,
    startPoint: LatLng,
    endPoint: LatLng,
    bounds: LatLngBounds?,
    animate: Boolean,
    disableZoomToBounds: Boolean,
    val highlightedRuns: List<List<LatLng>>,
    val nonHighlightedRuns: List<List<LatLng>>,
    val highlightedLifts: List<List<LatLng>>,
    val nonHighlightedLifts: List<List<LatLng>>,
) : WorkoutDrawingOptions(
    activityRoutes = activityRoutes,
    nonActivityRoutes = nonActivityRoutes,
    highlightedLapRoutePoints = emptyList(),
    nonHighlightedRoutePoints = emptyList(),
    startPoint = startPoint,
    endPoint = endPoint,
    bounds = bounds,
    animate = animate,
    disableZoomToBounds = disableZoomToBounds,
)
