package com.stt.android.ui.activities.map;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.content.Context;
import android.content.Intent;
import android.content.res.Resources;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.view.View;
import android.view.ViewPropertyAnimator;
import android.view.ViewTreeObserver;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.lifecycle.LifecycleOwnerKt;
import com.github.mikephil.charting.data.Entry;
import com.google.android.gms.maps.model.LatLng;
import com.stt.android.R;
import com.stt.android.controllers.CurrentUserController;
import com.stt.android.domain.Point;
import com.stt.android.domain.user.ActivityTypeHelper;
import com.stt.android.domain.workout.SpeedPaceState;
import com.stt.android.domain.workout.WorkoutData;
import com.stt.android.domain.workout.WorkoutDataExtensionsKt;
import com.stt.android.domain.workout.WorkoutGeoPoint;
import com.stt.android.domain.workout.WorkoutHrEvent;
import com.stt.android.domain.workouts.WorkoutHeader;
import com.stt.android.extensions.ActivityTypeExtensionsKt;
import com.stt.android.infomodel.SummaryItem;
import com.stt.android.mapping.InfoModelFormatter;
import com.stt.android.maps.MarkerZPriority;
import com.stt.android.maps.SuuntoBitmapDescriptorFactory;
import com.stt.android.maps.SuuntoCameraUpdateFactory;
import com.stt.android.maps.SuuntoMap;
import com.stt.android.maps.SuuntoMarker;
import com.stt.android.maps.SuuntoMarkerOptions;
import com.stt.android.maps.extensions.SuuntoMapExtensionsKt;
import com.stt.android.ui.components.WorkoutSnapshotView;
import com.stt.android.ui.components.charts.WorkoutDataChart;
import com.stt.android.ui.controllers.WorkoutDataLoaderController;
import static com.stt.android.ui.fragments.workout.analysis.WorkoutAnalysisHelper.getEntryForXIndexExact;
import com.stt.android.ui.map.MapHelper;
import com.stt.android.ui.map.RouteMarkerHelper;
import com.stt.android.ui.map.mapoptions.MapOption;
import com.stt.android.ui.map.selection.MapSelectionDialogFragment;
import com.stt.android.ui.utils.AnimationHelper;
import com.stt.android.ui.utils.ViewHelper;
import com.stt.android.utils.STTConstants;
import com.stt.android.workouts.details.values.WorkoutValue;
import dagger.hilt.android.AndroidEntryPoint;
import java.util.List;
import javax.inject.Inject;
import timber.log.Timber;

@AndroidEntryPoint
public class StaticWorkoutMapActivity extends MapActivity
    implements WorkoutDataChart.Listener, View.OnClickListener {
    private static final String EXTRA_USE_USER_SPEED_PACE_STATE = "com.stt.android.ui.activities.map.EXTRA_USE_USER_SPEED_PACE_STATE";
    private static final String EXTRA_BUY_PREMIUM_POPUP_SHOWN_ANALYTICS_SOURCE = "com.stt.android.ui.activities.map.EXTRA_BUY_PREMIUM_POPUP_SHOWN_ANALYTICS_SOURCE";

    public static Intent newStartIntent(
        Context context,
        WorkoutHeader workoutHeader,
        @Nullable WorkoutHeader targetWorkout,
        boolean useUserSpeedPaceState,
        String buyPremiumPopupShownAnalyticsSource
    ) {
        // NOTE: the polyline is removed here as a quick fix so that it won't crash with long
        // workout, causing TransactionTooLargeException on Nougat
        Intent startIntent = new Intent(context, StaticWorkoutMapActivity.class).putExtra(
            STTConstants.ExtraKeys.WORKOUT_HEADER,
            workoutHeader.toBuilder().polyline(null).build());
        if (targetWorkout != null) {
            startIntent.putExtra(STTConstants.ExtraKeys.GHOST_TARGET_WORKOUT_HEADER,
                targetWorkout.toBuilder().polyline(null).build());
        }

        startIntent
            .putExtra(EXTRA_USE_USER_SPEED_PACE_STATE, useUserSpeedPaceState)
            .putExtra(EXTRA_BUY_PREMIUM_POPUP_SHOWN_ANALYTICS_SOURCE, buyPremiumPopupShownAnalyticsSource);

        return startIntent;
    }

    private final WorkoutDataLoaderController.Listener targetWorkoutLoaderListener =
        new WorkoutDataLoaderController.Listener() {
            @Override
            public void onWorkoutDataLoaded(int workoutId, WorkoutData workoutData) {
                onTargetWorkoutLoaded(workoutData);
            }

            @Override
            public void onWorkoutDataLoadFailed(int workoutId) {
                // do nothing
            }
        };

    private final WorkoutDataLoaderController.Listener currentWorkoutLoaderListener =
        new WorkoutDataLoaderController.Listener() {
            @Override
            public void onWorkoutDataLoaded(int workoutId, WorkoutData workoutData) {
                StaticWorkoutMapActivity.this.currentWorkoutData = workoutData;
                onCurrentWorkoutLoaded();
            }

            @Override
            public void onWorkoutDataLoadFailed(int workoutId) {
                disableMap();
            }
        };

    FrameLayout rootView;
    WorkoutSnapshotView workoutSnapshotView;
    TextView noWorkoutData;
    ProgressBar loadingSpinner;
    LinearLayout chartViewContainer;
    ImageView chartViewController;
    TextView currentDuration;
    TextView currentDistance;
    TextView currentDistanceUnit;
    LinearLayout currentSpeedPaceContainer;
    TextView currentSpeedPace;
    TextView currentSpeedPaceUnit;
    LinearLayout currentAltitudeContainer;
    TextView currentAltitude;
    TextView currentAltitudeUnit;
    LinearLayout currentHeartRateContainer;
    TextView currentHeartRate;
    TextView currentHeartRateUnit;
    WorkoutDataChart workoutDataChart;

    @Inject
    WorkoutDataLoaderController workoutDataLoaderController;

    @Inject
    CurrentUserController currentUserController;

    @Inject
    InfoModelFormatter infoModelFormatter;

    private final Handler handler = new Handler(Looper.getMainLooper());

    private boolean moveCameraToBoundingBox = true;

    private WorkoutHeader currentWorkoutHeader;
    WorkoutData currentWorkoutData;
    private boolean isChartShown = true;
    private SuuntoMarker marker;
    private SuuntoBitmapDescriptorFactory bitmapDescriptorFactory;
    private boolean hasAltitudeData;
    private boolean hasSpeedData;
    private boolean hasHrData;
    private boolean cameraCenteredToRoute;

    private boolean hasLoggedChartInteraction = false;
    private boolean useUserSpeedPaceState = false;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        useUserSpeedPaceState = getIntent().getBooleanExtra(EXTRA_USE_USER_SPEED_PACE_STATE, false);

        rootView = findViewById(R.id.rootView);
        workoutSnapshotView = findViewById(R.id.workoutSnapshotView);
        noWorkoutData = findViewById(R.id.noWorkoutData);
        loadingSpinner = findViewById(R.id.loadingSpinner);
        chartViewContainer = findViewById(R.id.chartViewContainer);
        chartViewController = findViewById(R.id.chartViewController);
        currentDuration = findViewById(R.id.currentDuration);
        currentDistance = findViewById(R.id.currentDistance);
        currentDistanceUnit = findViewById(R.id.currentDistanceUnit);
        currentSpeedPaceContainer = findViewById(R.id.currentSpeedPaceContainer);
        currentSpeedPace = findViewById(R.id.currentSpeedPace);
        currentSpeedPaceUnit = findViewById(R.id.currentSpeedPaceUnit);
        currentAltitudeContainer = findViewById(R.id.currentAltitudeContainer);
        currentAltitude = findViewById(R.id.currentAltitude);
        currentAltitudeUnit = findViewById(R.id.currentAltitudeUnit);
        currentHeartRateContainer = findViewById(R.id.currentHeartRateContainer);
        currentHeartRate = findViewById(R.id.currentHeartRate);
        currentHeartRateUnit = findViewById(R.id.currentHeartRateUnit);
        workoutDataChart = findViewById(R.id.workoutDataChartView);

        workoutSnapshotView.setShowActivityType(true);
        workoutSnapshotView.setShowLabels(true);
        workoutSnapshotView.setUseUserSpeedPaceState(useUserSpeedPaceState);

        loadingSpinner.setVisibility(View.VISIBLE);
        chartViewController.setOnClickListener(this);
        currentSpeedPaceContainer.setOnClickListener(this);
        currentAltitudeContainer.setOnClickListener(this);
        currentHeartRateContainer.setOnClickListener(this);

        if (savedInstanceState != null) {
            // We don't want to move the camera to the entire route if we're just being re-created
            moveCameraToBoundingBox = false;
        }

        bitmapDescriptorFactory = new SuuntoBitmapDescriptorFactory(this);

        locationBt.setOnClickListener(v -> locationBtClicked());
    }

    private void locationBtClicked() {
        SuuntoMap map = getMap();
        if (map == null) {
            return;
        }

        List<WorkoutGeoPoint> routePoints = currentWorkoutData.getRoutePoints();
        try {
            MapHelper.moveCameraToBoundGeoPoints(getResources(), map,
                routePoints);
            setLocationButtonResource(true);
        } catch (IllegalStateException e) {
            Timber.w(e, "Failed to move camera to bound geo points");
        }
    }

    private void setLocationButtonResource(Boolean centered) {
        if (centered) {
            cameraCenteredToRoute = true;
            locationBt.setImageResource(R.drawable.ic_location_arrow_fill);
        } else {
            cameraCenteredToRoute = false;
            locationBt.setImageResource(R.drawable.ic_location_arrow);
        }
    }

    private void loadWorkouts() {
        Intent intent = getIntent();
        currentWorkoutHeader = intent.getParcelableExtra(STTConstants.ExtraKeys.WORKOUT_HEADER);
        workoutSnapshotView.setWorkoutHeader(currentWorkoutHeader);
        workoutDataLoaderController.loadWorkout(currentWorkoutHeader, currentWorkoutLoaderListener);

        WorkoutHeader targetWorkout =
            intent.getParcelableExtra(STTConstants.ExtraKeys.GHOST_TARGET_WORKOUT_HEADER);
        if (targetWorkout != null) {
            workoutDataLoaderController.loadWorkout(targetWorkout, targetWorkoutLoaderListener);
        }
    }

    @Override
    protected void onPause() {
        workoutDataLoaderController.removeListener(currentWorkoutLoaderListener);
        workoutDataLoaderController.removeListener(targetWorkoutLoaderListener);
        super.onPause();
    }

    @Override
    protected int getActivityLayout() {
        return R.layout.static_workout_map_activity;
    }

    @Override
    protected void setUpMap(@NonNull SuuntoMap map) {
        map.getUiSettings().setZoomControlsEnabled(false);

        SuuntoMapExtensionsKt.addScopedOnCameraMoveListener(
            map,
            LifecycleOwnerKt.getLifecycleScope(this),
            100,
            () -> {
                if (cameraCenteredToRoute) {
                    setLocationButtonResource(false);
                }
            });
    }

    @Override
    protected boolean useSkiMapStyle() {
        WorkoutHeader workoutHeader = getIntent().getParcelableExtra(STTConstants.ExtraKeys.WORKOUT_HEADER);
        if (workoutHeader == null) {
            return false;
        }
        return ActivityTypeExtensionsKt.getUseSkiMapStyle(workoutHeader.getActivityType());
    }

    @Override
    protected void openMapSelection() {
        SuuntoMap map = getMap();
        LatLng mapCenter = null;

        if (map != null && map.getCameraPosition() != null) {
            mapCenter = map.getCameraPosition().getTarget();
        }

        MapSelectionDialogFragment.newInstance(
            getMapsProviderName(),
            false, // showHeatmaps
            false, // showRoadSurface
            false, // showMyTracks
            false, // showMyPOIsGroup
            mapCenter, // mapCenter
            "", // analyticsSource
            MapOption.MAP_STYLE,
            false
        )
            .show(getSupportFragmentManager(), MapSelectionDialogFragment.FRAGMENT_TAG);
    }

    void disableMap() {
        loadingSpinner.setVisibility(View.GONE);
        noWorkoutData.setVisibility(View.VISIBLE);
    }

    void onCurrentWorkoutLoaded() {
        Point startPosition = currentWorkoutHeader.getStartPosition();
        if (startPosition != null
            && startPosition.getLatitude() != 0.0
            && startPosition.getLongitude() != 0.0) {
            loadingSpinner.setVisibility(View.GONE);
            Context context = this;
            getWindow().getDecorView()
                .getViewTreeObserver()
                .addOnPreDrawListener(new ViewTreeObserver.OnPreDrawListener() {
                    @Override
                    public boolean onPreDraw() {
                        getWindow().getDecorView()
                            .getViewTreeObserver()
                            .removeOnPreDrawListener(this);

                        if (currentWorkoutData.getRoutePoints() != null &&
                            !currentWorkoutData.getRoutePoints().isEmpty()) {

                            // only show chart if there's no target workout
                            if (getIntent().hasExtra(
                                STTConstants.ExtraKeys.GHOST_TARGET_WORKOUT_HEADER)) {
                                chartViewContainer.setVisibility(View.GONE);
                            } else {
                                prepareChart();
                            }

                            drawCurrentWorkoutRoute();
                        } else {
                            showWorkoutStartLocation(startPosition, context);
                        }
                        return true;
                    }
                });
        } else {
            disableMap();
        }
    }

    private void showWorkoutStartLocation(Point startPosition, Context context) {
        LatLng latLng = new LatLng(startPosition.getLatitude(), startPosition.getLongitude());
        SuuntoMap map = getMap();

        if (map != null) {
            MapHelper.moveCameraToLatLng(map, latLng, true);
            map.addMarker(
                new SuuntoMarkerOptions()
                    .anchor(0.5F, 0.5F)
                    .icon(
                        new SuuntoBitmapDescriptorFactory(context)
                            .fromResource(R.drawable.map_pin)
                    )
                    .position(latLng)
                    .zPriority(MarkerZPriority.START_POINT)
            );
        }
    }

    void drawCurrentWorkoutRoute() {
        // We need the size (in pixels) of the map and the summary container in order
        // to move the camera to the right place
        SuuntoMap map = getMap();
        if (map == null) {
            return;
        }

        addPadding(getSnapshotViewHeight(), 0);

        List<WorkoutGeoPoint> routePoints = currentWorkoutData.getRoutePoints();
        if (currentWorkoutHeader.getActivityType().isSlopeSki() && !getIntent().hasExtra(
            STTConstants.ExtraKeys.GHOST_TARGET_WORKOUT_HEADER)) {
            RouteMarkerHelper.drawSkiRouteWithStartPoint(this, map, routePoints);
        } else {
            List<LatLng> latLngs = MapHelper.geoPointsToLatLngs(routePoints, 0, routePoints.size());
            RouteMarkerHelper.drawRouteWithStartPoint(this, map, latLngs, true);
        }
        if (moveCameraToBoundingBox) {
            try {
                MapHelper.moveCameraToBoundGeoPoints(getResources(), map, routePoints);
                setLocationButtonResource(true);
            } catch (IllegalStateException e) {
                // for whatever reason, we still suffer a lot of 0-size map view
                // e.g. https://crashlytics.com/sporst-tracker/android/apps/com.stt
                // .android/issues/551995525141dcfd8f4a38ab
                Timber.e(e, "Failed to move camera to bound geo points");
            }
        }
    }

    void prepareChart() {
        chartViewContainer.setVisibility(View.VISIBLE);
        workoutDataChart.setListener(this);
        workoutDataChart.setHardwareAccelerationEnabled(true);

        workoutDataChart.setWorkout(userSettingsController.getSettings().getMeasurementUnit(),
            currentWorkoutHeader, currentWorkoutData, true,
            (altitudeLineDataSet, speedPaceLineDataSet, heartRateLineDataSet) -> {
                hasAltitudeData = !altitudeLineDataSet.getValues().isEmpty();
                hasSpeedData = !speedPaceLineDataSet.getValues().isEmpty();
                hasHrData = !heartRateLineDataSet.getValues().isEmpty();

                List<WorkoutGeoPoint> routePoints = currentWorkoutData.getRoutePoints();
                if (!routePoints.isEmpty()) {
                    Entry altitudeEntry = getEntryForXIndexExact(altitudeLineDataSet, 0);
                    Entry speedPaceEntry = getEntryForXIndexExact(speedPaceLineDataSet, 0);
                    Entry heartRateEntry = getEntryForXIndexExact(heartRateLineDataSet, 0);
                    updateChartLabels(routePoints.get(0), altitudeEntry, speedPaceEntry, heartRateEntry);
                }
            });

        addPadding( 0,chartViewContainer.getHeight() - chartViewController.getHeight());

        // delays the animation so that the user could notice it
        handler.postDelayed(() -> showSnapshot(false), 1000L);
    }

    private void showSnapshot(boolean show) {
        Resources resources = getResources();
        int snapshotViewLeftMargin = resources.getDimensionPixelSize(R.dimen.size_spacing_xsmall);
        int snapshotViewTopMargin = resources.getDimensionPixelSize(R.dimen.size_spacing_small);
        int mapButtonSpacing = resources.getDimensionPixelSize(R.dimen.size_spacing_medium);
        int buttonsLeftMargin = mapButtonSpacing;
        int locationBtHeight = locationBt.getHeight();
        int locationBtOffset = mapButtonSpacing;
        int avalancheLegendMargin = buttonsLeftMargin + locationBt.getWidth() + mapButtonSpacing;
        int avalancheLegendOffset = mapButtonSpacing;
        int mapOptionsBtHeight = mapOptionsBt.getHeight();
        int mapOptionsBtOffset = locationBtOffset + locationBtHeight + mapButtonSpacing;
        int toolTipViewOffset = mapOptionsBtOffset + mapOptionsBtHeight;
        int snapshotViewOffset = getSnapshotViewHeight();

        final Runnable endAction;
        if (toolTipView != null) {
            AnimationHelper.move(
                toolTipView,
                buttonsLeftMargin,
                buttonsLeftMargin,
                show ? toolTipViewOffset : toolTipViewOffset + snapshotViewOffset,
                show ? toolTipViewOffset + snapshotViewOffset : toolTipViewOffset);
            endAction = null;
        } else if (!show) {
            endAction = () -> {
                showMapToolTipIfNeeded();
                if (toolTipView != null) {
                    toolTipView.setTranslationY(-snapshotViewOffset);
                }
            };
        } else {
            endAction = null;
        }

        AnimationHelper.move(
            workoutSnapshotView,
            snapshotViewLeftMargin,
            snapshotViewLeftMargin,
            show ? -snapshotViewOffset : snapshotViewTopMargin,
            show ? snapshotViewTopMargin : -snapshotViewOffset);

        AnimationHelper.move(
            locationBt,
            buttonsLeftMargin,
            buttonsLeftMargin,
            show ? locationBtOffset : locationBtOffset + snapshotViewOffset,
            show ? locationBtOffset + snapshotViewOffset : locationBtOffset);

        AnimationHelper.move(
            avalancheLegend,
            avalancheLegendMargin,
            avalancheLegendMargin,
            show ? avalancheLegendOffset : avalancheLegendOffset + snapshotViewOffset,
            show ? avalancheLegendOffset + snapshotViewOffset : avalancheLegendOffset);

        final ViewPropertyAnimator vpa =
            AnimationHelper.move(
                mapOptionsBt,
                buttonsLeftMargin,
                buttonsLeftMargin,
                show ? mapOptionsBtOffset : mapOptionsBtOffset + snapshotViewOffset,
                show ? mapOptionsBtOffset + snapshotViewOffset : mapOptionsBtOffset);

        if (endAction != null) {
            vpa.setListener(new AnimatorListenerAdapter() {
                @Override
                public void onAnimationEnd(Animator animation) {
                    endAction.run();
                    vpa.setListener(null);
                }
            });
        }

        addPadding(show ? snapshotViewOffset : -snapshotViewOffset,0);
    }

    void onTargetWorkoutLoaded(WorkoutData workoutData) {
        if (workoutData.getRoutePoints() == null || workoutData.getRoutePoints().isEmpty()) {
            return;
        }

        SuuntoMap map = getMap();
        if (map != null) {
            RouteMarkerHelper.drawTargetRouteWithStartPoint(this, map,
                workoutData.getRoutePoints());
        }
    }

    @Override
    public void onWorkoutGeoPointSelected(
        WorkoutGeoPoint workoutGeoPoint,
        @Nullable Entry altitudeEntry,
        @Nullable Entry speedPaceEntry,
        @Nullable Entry heartRateEntry
    ) {
        SuuntoMap map = getMap();
        if (map == null) {
            return;
        }
        LatLng position = workoutGeoPoint.getLatLng();

        if (!hasLoggedChartInteraction) {
            hasLoggedChartInteraction = true;
        }

        map.moveCamera(SuuntoCameraUpdateFactory.newLatLng(position));

        if (marker == null) {
            marker = map.addMarker(new SuuntoMarkerOptions()
                .anchor(0.5F, 0.5F)
                .icon(bitmapDescriptorFactory.forCurrentLocationDot())
                .position(position)
                .zPriority(MarkerZPriority.SELECTED_GEOPOINT)
            );
        } else {
            marker.setPosition(position);
        }

        updateChartLabels(workoutGeoPoint, altitudeEntry, speedPaceEntry, heartRateEntry);
    }

    private void updateChartLabels(
        WorkoutGeoPoint workoutGeoPoint,
        @Nullable Entry altitudeEntry,
        @Nullable Entry speedPaceEntry,
        @Nullable Entry heartRateEntry
    ) {
        currentDuration.setText(
            infoModelFormatter.formatValue(SummaryItem.DURATION, workoutGeoPoint.getMillisecondsInWorkout() / 1000).getValue());

        WorkoutValue distance = infoModelFormatter
            .formatValue(SummaryItem.DISTANCE, workoutGeoPoint.getTotalDistance());
        currentDistance.setText(distance.getValue());
        currentDistanceUnit.setText(
            distance.getUnit() != null ? distance.getUnit() : infoModelFormatter.getUnit().getDistanceUnit());

        if (altitudeEntry != null) {
            WorkoutValue altitude = infoModelFormatter.formatValue(SummaryItem.HIGHALTITUDE, workoutGeoPoint.getAltitude());
            currentAltitude.setText(altitude.getValue());
            currentAltitudeUnit.setText(
                altitude.getUnit() != null ? altitude.getUnit() : infoModelFormatter.getUnit().getAltitudeUnit());
            currentAltitudeContainer.setVisibility(View.VISIBLE);
        } else if (hasAltitudeData) {
            currentAltitude.setText("—");
            currentAltitudeContainer.setVisibility(View.VISIBLE);
        } else {
            currentAltitudeContainer.setVisibility(View.GONE);
        }

        SpeedPaceState speedPaceState =
            ActivityTypeHelper.getSpeedPaceState(useUserSpeedPaceState, this, currentWorkoutHeader.getActivityType());

        if (speedPaceEntry != null) {
            currentSpeedPaceContainer.setVisibility(View.VISIBLE);
            if (speedPaceState == SpeedPaceState.SPEED) {
                WorkoutValue speed = infoModelFormatter
                    .formatValue(SummaryItem.AVGSPEED, workoutGeoPoint.getSpeedMetersPerSecond());
                currentSpeedPace.setText(speed.getValue());
                currentSpeedPaceUnit.setText(
                    speed.getUnit() != null ? speed.getUnit() : infoModelFormatter.getUnit().getSpeedUnit());
            } else {
                WorkoutValue pace = infoModelFormatter
                    .formatValue(SummaryItem.AVGPACE, workoutGeoPoint.getSpeedMetersPerSecond());
                currentSpeedPace.setText(pace.getValue());
                currentSpeedPaceUnit.setText(
                    pace.getUnit() != null ? pace.getUnit() : infoModelFormatter.getUnit().getPaceUnit());
            }
        } else if (hasSpeedData) {
            currentSpeedPace.setText("—");
            currentSpeedPaceContainer.setVisibility(View.VISIBLE);
        } else {
            currentSpeedPaceContainer.setVisibility(View.GONE);
        }

        int heartRate = -1;
        List<WorkoutHrEvent> heartRateEvents = WorkoutDataExtensionsKt.getHeartRateEventsWithoutPauses(currentWorkoutData);
        int hrEventsCount = heartRateEvents != null ? heartRateEvents.size() : 0;
        if (hrEventsCount > 0) {
            long timestamp = workoutGeoPoint.getTimestamp();
            for (int i = 0; i < hrEventsCount; ++i) {
                WorkoutHrEvent workoutHrEvent = heartRateEvents.get(i);
                if (workoutHrEvent.getTimestamp() >= timestamp) {
                    heartRate = workoutHrEvent.getHeartRate();
                    break;
                }
            }
        }
        if (heartRateEntry != null && heartRate > 0) {
            currentHeartRate.setText(Integer.toString(heartRate));
            ViewHelper.setVisibility(currentHeartRateContainer, View.VISIBLE);
        } else if (hasHrData) {
            currentHeartRate.setText("—");
            currentHeartRateContainer.setVisibility(View.VISIBLE);
        } else {
            ViewHelper.setVisibility(currentHeartRateContainer, View.GONE);
        }
    }

    @Override
    public void onClick(View v) {
        if (v == chartViewController) {
            if (isChartShown) {
                hideChart();
                showSnapshot(true);
            } else {
                showChart();
                showSnapshot(false);
            }
            isChartShown = !isChartShown;
        } else if (v == currentSpeedPaceContainer) {
            boolean shouldShowSpeedPace = !workoutDataChart.isSpeedPaceShown();
            workoutDataChart.setSpeedPaceShown(shouldShowSpeedPace);

            int textColor = ContextCompat.getColor(this,
                shouldShowSpeedPace ? R.color.graphlib_speed : R.color.almost_white);
            currentSpeedPace.setTextColor(textColor);
            currentSpeedPaceUnit.setTextColor(textColor);
        } else if (v == currentAltitudeContainer) {
            boolean shouldShowAltitude = !workoutDataChart.isAltitudeShown();
            workoutDataChart.setAltitudeShown(shouldShowAltitude);

            int textColor = ContextCompat.getColor(this,
                shouldShowAltitude ? R.color.graphlib_altitude : R.color.almost_white);
            currentAltitude.setTextColor(textColor);
            currentAltitudeUnit.setTextColor(textColor);
        } else if (v == currentHeartRateContainer) {
            boolean shouldShowHeartRate = !workoutDataChart.isHeartRateShown();
            workoutDataChart.setHeartRateShown(shouldShowHeartRate);

            int textColor = ContextCompat.getColor(this,
                shouldShowHeartRate ? R.color.graphlib_hr : R.color.almost_white);
            currentHeartRate.setTextColor(textColor);
            currentHeartRateUnit.setTextColor(textColor);
        }
    }

    private void hideChart() {
        int rootViewHeight = rootView.getHeight();
        int chartViewControllerHeight = chartViewController.getHeight();
        int chartViewContainerHeight = chartViewContainer.getHeight();
        AnimationHelper.move(chartViewContainer, 0, 0, rootViewHeight - chartViewContainerHeight,
            rootViewHeight - chartViewControllerHeight);
        AnimationHelper.scale(chartViewController, 1, 1, 1, -1);

        addPadding(0, chartViewControllerHeight - chartViewContainerHeight);
    }

    private void showChart() {
        int rootViewHeight = rootView.getHeight();
        int chartViewControllerHeight = chartViewController.getHeight();
        int chartViewContainerHeight = chartViewContainer.getHeight();
        AnimationHelper.move(chartViewContainer, 0, 0, rootViewHeight - chartViewControllerHeight,
            rootViewHeight - chartViewContainerHeight);
        AnimationHelper.scale(chartViewController, 1, 1, -1, 1);

        addPadding(0, chartViewContainerHeight - chartViewControllerHeight);
    }

    @Override
    public void onMapReady(SuuntoMap map) {
        super.onMapReady(map);

        loadWorkouts();
    }

    @NonNull
    @Override
    protected String getBuyPremiumPopupShownAnalyticsSource() {
        String unknown = "UnknownStaticWorkoutMapActivity";
        Bundle extras = getIntent().getExtras();
        if (extras == null) {
            return unknown;
        }

        return extras.getString(EXTRA_BUY_PREMIUM_POPUP_SHOWN_ANALYTICS_SOURCE, unknown);
    }
}
