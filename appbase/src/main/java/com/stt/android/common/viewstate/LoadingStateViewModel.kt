package com.stt.android.common.viewstate

import androidx.annotation.MainThread
import androidx.lifecycle.LiveData
import androidx.lifecycle.map
import com.stt.android.common.coroutines.CoroutinesDispatcherProvider
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.common.ui.ErrorEvent
import com.stt.android.common.ui.RxViewModel
import com.stt.android.ui.utils.OnActiveListenableMutableLiveData
import com.stt.android.ui.utils.OnActiveListener
import io.reactivex.Scheduler

/**
 * A base view model based on [RxViewModel] that encapsulates view data loading
 * and dispatches [ViewState] events to the UI.
 *
 * This class works together with classes that extend either [ViewStateFragment] or [ViewStateListFragment].
 * 1. Call [notifyLoading] with an optional initial data before you start loading the data.
 * 2. Once loading is complete, call [notifyDataLoaded] and pass in the data or null.
 * 3. In case of an error, call [notifyError] and pass in the [Throwable] and optional data. The error will be
 *   picked up by [ViewStateFragment] and would be displayed in a SnackBar with an possible action button
 *   depending on whether the error is recoverable or not. If the error is recoverable, [retryLoading]
 *   will be called to indicate that you should retry loading.
 *
 * It is up to you to decide when you want to start loading the data. A common use case would be in the init block.
 * Alternatively, you can expose a method that you would call from the fragment to start loading.
 *
 * @param T The data type that will be wrapped inside [ViewState]
 * @param ioThread [Scheduler] to be used for IO operations
 * @param mainThread [Scheduler] to be used for UI operations
 * @param coroutinesDispatchers [CoroutinesDispatchers] dispatchers to be used with coroutines
 *
 * @see [ViewState]
 * @see [ViewStateFragment]
 * @see [ViewStateListFragment]
 */
abstract class LoadingStateViewModel<T>(
    ioThread: Scheduler,
    mainThread: Scheduler,
    coroutinesDispatchers: CoroutinesDispatchers = CoroutinesDispatcherProvider()
) : RxViewModel(ioThread, mainThread, coroutinesDispatchers) {

    val viewState: LiveData<ViewState<T?>>
        get() = _viewState
    private val _viewState = OnActiveListenableMutableLiveData<ViewState<T?>>()

    init {
        _viewState.setOnActiveListener(object : OnActiveListener {
            override fun onActive() {
                onViewStateActive()
            }

            override fun onInactive() {
                onViewStateInactive()
            }
        })
    }

    override fun onCleared() {
        super.onCleared()
        _viewState.setOnActiveListener(null)
    }

    /**
     * Notify the UI that the data is currently loading. Emits a [ViewState.Loading<T?>] via [viewState].
     * You can optionally pass initial or previously loaded data to [data] in cases where loading more is needed.
     *
     * @param data instance of [T] or null
     */
    @Suppress("MemberVisibilityCanBePrivate")
    protected fun notifyLoading(data: T? = null) {
        _viewState.postValue(ViewState.Loading(data))
    }

    /**
     * Notify the UI that the data is currently loading. Emits a [ViewState.Loading<T?>] via [viewState].
     * You can optionally pass initial or previously loaded data to [data] in cases where loading more is needed.
     *
     * Use from UI thread only.
     *
     * @param data instance of [T] or null
     */
    @Suppress("MemberVisibilityCanBePrivate")
    protected fun notifyLoadingImmediately(data: T? = null) {
        _viewState.value = ViewState.Loading(data)
    }

    /**
     * Notify the UI that the data has been loaded. Emits a [ViewState.Loaded<T?>] via [viewState].
     *
     * @param data instance of [T] or null
     */
    @Suppress("MemberVisibilityCanBePrivate")
    protected fun notifyDataLoaded(data: T? = null) {
        _viewState.postValue(ViewState.Loaded(data))
    }

    /**
     * Notify the UI that the data has been loaded. Emits a [ViewState.Loaded<T?>] via [viewState].
     * Use from UI thread only.
     *
     * @param data instance of [T] or null
     */
    @MainThread
    @Suppress("MemberVisibilityCanBePrivate")
    protected fun notifyDataLoadedImmediately(data: T? = null) {
        _viewState.value = ViewState.Loaded(data)
    }

    /**
     * Notify the UI that an error has occurred. Emits a [ViewState.Error<T?>] via [viewState].
     * You can optionally pass initial or previously loaded data to [data].
     *
     * @param throwable the exception that occurred
     * @param data instance of [T] or null
     */
    @Suppress("MemberVisibilityCanBePrivate")
    protected fun notifyError(throwable: Throwable, data: T? = null) {
        _viewState.postValue(ViewState.Error(ErrorEvent.get(throwable::class), data))
    }

    /**
     * Notify the UI that an error has occurred. Emits a [ViewState.Error<T?>] via [viewState].
     * You can optionally pass initial or previously loaded data to [data].
     * Use from UI thread only.
     *
     * @param errorEvent the error event that occurred
     * @param data instance of [T] or null
     */
    @Suppress("MemberVisibilityCanBePrivate")
    protected fun notifyErrorImmediately(errorEvent: ErrorEvent, data: T? = null) {
        _viewState.value = ViewState.Error(errorEvent, data)
    }

    /**
     * Called when recovering from an error and the view model should retry to load
     */
    abstract fun retryLoading()

    /**
     * Called when viewState's observer count becomes non-zero, can be used to
     * start jobs that should be active only when viewState is being observed
     */
    protected open fun onViewStateActive() {}

    /**
     * Called when viewState's observer count becomes zero, can be used to
     * cancel jobs started in [onViewStateActive]
     */
    protected open fun onViewStateInactive() {}

    val viewStateData: LiveData<T?>
        get() = viewState.map { it.data }
}
