package com.stt.android.social.badges.badgesDetail

import android.annotation.SuppressLint
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.systemBars
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.VerticalDivider
import androidx.compose.runtime.Composable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.zIndex
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.stt.android.compose.theme.nearBlack
import com.stt.android.compose.theme.spacing
import coil3.compose.AsyncImage
import com.stt.android.R
import com.stt.android.compose.component.SuuntoCard
import com.stt.android.compose.component.SuuntoTopBar
import com.stt.android.compose.modifiers.narrowContentWithBgColors
import com.stt.android.compose.theme.dividerColor
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.material3.body
import com.stt.android.compose.theme.material3.bodyLargeBold
import com.stt.android.compose.theme.material3.bodyXLargeBold
import com.stt.android.data.badges.BadgeStatus
import com.stt.android.data.badges.ExploreMore
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.toImmutableList
import java.text.DateFormat
import java.util.Date

private val expandedHeaderWithoutStatusBarHeight = 246.dp
private val collapsedHeaderWithoutStatusBarHeight = 52.dp
private val maxBadgesSize = 140.dp
private val minBadgesSize = 56.dp

@Composable
internal fun BadgeDetailScreen(
    viewData: BadgesDetailViewData.Loaded,
    onEvent: (BadgeDetailViewEvent) -> Unit,
    modifier: Modifier = Modifier,
) {
    val lazyListState = rememberLazyListState()
    Box(
        modifier = modifier
            .fillMaxSize()
            .narrowContentWithBgColors(
                backgroundColor = MaterialTheme.colorScheme.surface,
                outerBackgroundColor = MaterialTheme.colorScheme.background
            ),
    ) {
        BadgesHeader(
            viewData = viewData,
            scrollState = lazyListState,
            onEvent = onEvent,
            modifier = Modifier,
        ) { paddingValue ->
            LazyColumn(
                state = lazyListState,
                modifier = Modifier,
                contentPadding = paddingValue,
            ) {
                item {
                    Spacer(modifier = Modifier.height(MaterialTheme.spacing.smaller))
                    Text(
                        text = viewData.badgesDetail.badgeName ?: "badges name null",
                        style = MaterialTheme.typography.headlineSmall,
                        color = MaterialTheme.colorScheme.nearBlack,
                        modifier = Modifier
                            .padding(MaterialTheme.spacing.medium)
                    )
                    Spacer(modifier = Modifier.height(MaterialTheme.spacing.smaller))
                }
                item {
                    Spacer(modifier = Modifier.height(MaterialTheme.spacing.smaller))
                }
                when (viewData.badgesDetail.badgeStatus) {
                    BadgeStatus.ACQUIRED -> {
                        item {
                            BadgeRankingSummary(viewData)
                            Spacer(modifier = Modifier.height(MaterialTheme.spacing.smaller))
                        }
                        item {
                            BadgeTimeRangeSummary(viewData)
                            Spacer(modifier = Modifier.height(MaterialTheme.spacing.smaller))
                        }
                        item {
                            BadgeAcquisitionTimeSummary(viewData)
                            Spacer(modifier = Modifier.height(MaterialTheme.spacing.smaller))
                        }
                        item {
                            BadgesAchievementList(viewData)
                            Spacer(modifier = Modifier.height(MaterialTheme.spacing.smaller))
                        }
                    }

                    BadgeStatus.IN_PROGRESS -> {
                        item {
                            BadgeTimeRangeSummary(viewData)
                            Spacer(modifier = Modifier.height(MaterialTheme.spacing.smaller))
                        }
                    }

                    BadgeStatus.NOT_STARTED -> {
                        item {
                            Spacer(modifier = Modifier.height(MaterialTheme.spacing.smaller))
                        }
                    }

                    null -> {
                        item {
                            Text("something wrong with BadgeStatus ")
                            Spacer(modifier = Modifier.height(MaterialTheme.spacing.smaller))
                        }
                    }
                }
                item {
                    BadgesDetailIntroduction(viewData)
                    Spacer(modifier = Modifier.height(MaterialTheme.spacing.smaller))
                    ExploreMoreItems(viewData, onEvent = onEvent)
                }
            }
        }
    }
}

@SuppressLint("UnusedBoxWithConstraintsScope")
@Composable
private fun BadgesHeader(
    viewData: BadgesDetailViewData.Loaded,
    scrollState: LazyListState,
    onEvent: (BadgeDetailViewEvent) -> Unit,
    modifier: Modifier = Modifier,
    content: @Composable (PaddingValues) -> Unit,
) {
    val backgroundImage: String? = viewData.badgesDetail.badgeBackgroundImageUrl
    val badgeImage: String? = viewData.badgesDetail.acquiredBadgeIconUrl
    val badgeImageNot: String? = viewData.badgesDetail.badgeIconUrl
    val badgesId = if (viewData.badgesDetail.badgeStatus == BadgeStatus.ACQUIRED) {
        badgeImage
    } else {
        badgeImageNot
    }
    val density = LocalDensity.current
    val statusBarHeight = WindowInsets.systemBars.getTop(density)
    val expandedHeight =
        with(density) { (statusBarHeight + expandedHeaderWithoutStatusBarHeight.toPx()).toDp() }
    val collapsedHeight =
        with(density) { (statusBarHeight + expandedHeaderWithoutStatusBarHeight.toPx()).toDp() }
    val scrollProgress by remember {
        derivedStateOf {
            val offset = scrollState.firstVisibleItemScrollOffset.toFloat()
            val scrolledItems = scrollState.firstVisibleItemIndex

            when {
                scrolledItems > 0 -> 1f
                else -> {
                    val maxScroll = with(density) { expandedHeaderWithoutStatusBarHeight.toPx() }
                    (offset / maxScroll).coerceIn(0f, 1f)
                }
            }
        }
    }
    val maxBadgesOffsetY = expandedHeight - maxBadgesSize / 2
    val minBadgesOffsetY = collapsedHeight - minBadgesSize - MaterialTheme.spacing.small
    val avatarOffsetY = lerp(
        maxBadgesOffsetY,
        minBadgesOffsetY,
        scrollProgress
    )
    val badgesSize = lerp(maxBadgesSize, minBadgesSize, scrollProgress)
    val imageAlpha = 1f - scrollProgress
    val gradientAlpha = 1f - scrollProgress
    val backgroundColor = MaterialTheme.colorScheme.surface.copy(alpha = scrollProgress)
    BoxWithConstraints(
        modifier = modifier.fillMaxWidth()
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(expandedHeight)
                .background(backgroundColor)
        ) {
            BackgroundImage(
                coverPhotoResId = backgroundImage,
                alpha = imageAlpha,
                modifier = Modifier.fillMaxSize()
            )
            TopGradient(
                statusBarHeight = statusBarHeight,
                alpha = gradientAlpha
            )
        }
        content(
            PaddingValues(top = expandedHeight + maxBadgesSize / 2 - MaterialTheme.spacing.medium)
        )

        BadgesDetailTopBar(
            onEvent = onEvent
        )

        FloatingBadges(
            badgesId = badgesId,
            size = badgesSize,
            offsetY = avatarOffsetY,
        )
    }
}

private fun lerp(start: Dp, end: Dp, fraction: Float): Dp {
    return start + ((end - start) * fraction)
}

@Composable
private fun TopGradient(
    statusBarHeight: Int,
    alpha: Float,
    modifier: Modifier = Modifier,
) {
    val density = LocalDensity.current
    val gradientHeight =
        with(density) { statusBarHeight.toDp() + collapsedHeaderWithoutStatusBarHeight }

    Box(
        modifier = modifier
            .fillMaxWidth()
            .height(gradientHeight)
            .graphicsLayer {
                this.alpha = alpha
            }
            .background(
                brush = Brush.verticalGradient(
                    colors = listOf(
                        MaterialTheme.colorScheme.nearBlack.copy(alpha = 0.45f),
                        Color.Transparent,
                    )
                )
            )
    )
}

@Composable
private fun BackgroundImage(
    coverPhotoResId: String?,
    alpha: Float,
    modifier: Modifier = Modifier,
) {
    AsyncImage(
        model = coverPhotoResId,
        contentDescription = "background_Image",
        modifier = modifier
            .fillMaxSize()
            .graphicsLayer { this.alpha = alpha },
        contentScale = ContentScale.Crop
    )
}

@Composable
private fun FloatingBadges(
    badgesId: String?,
    size: Dp,
    offsetY: Dp,
    modifier: Modifier = Modifier,
) {
    Box(
        modifier = modifier
            .fillMaxWidth()
            .zIndex(10f)
    ) {
        AsyncImage(
            model = badgesId,
            contentDescription = "badge_Image",
            modifier = Modifier
                .size(size)
                .align(Alignment.TopCenter)
                .offset(y = offsetY),
            contentScale = ContentScale.Fit
        )
    }
}

@Composable
private fun BadgesDetailTopBar(
    onEvent: (BadgeDetailViewEvent) -> Unit
) {
    SuuntoTopBar(
        title = "",
        onNavigationClick = { onEvent(BadgeDetailViewEvent.Close) },
    )
}

@Composable
fun BadgeProgressRow(loaded: BadgesDetailViewData.Loaded, modifier: Modifier = Modifier) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = modifier.padding(MaterialTheme.spacing.medium)
    ) {
        Icon(
            painter = painterResource(id = R.drawable.summary_icon),
            contentDescription = null,
            modifier = Modifier.size(20.dp)
        )
        val progressList = loaded.conditionData.map { item ->
            "${item.current} of ${item.target}"
        }
        val progressText = progressList.joinToString(", ")
        Text(progressText, color = MaterialTheme.colorScheme.nearBlack)
    }
}

@Composable
fun BadgeRankingSummary(userBadge: BadgesDetailViewData.Loaded, modifier: Modifier = Modifier) {
    val ranking = userBadge.badgesDetail.acquisitionRanking
    if (ranking != null && ranking > 0) {
        val text = stringResource(R.string.badges_earned_ranking, ranking.toString())
        Row(
            modifier = modifier,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                painter = painterResource(id = R.drawable.ranking_summary),
                contentDescription = null,
                modifier = Modifier.size(MaterialTheme.iconSizes.small)
            )
            Spacer(modifier = Modifier.width(MaterialTheme.spacing.small))
            Text(
                text, style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.nearBlack
            )
        }
    }
}

@Composable
fun BadgeTimeRangeSummary(viewData: BadgesDetailViewData.Loaded, modifier: Modifier = Modifier) {
    val userBadge = viewData.badgesDetail
    val start = userBadge.startTime
    val end = userBadge.endTime
    val configuration = LocalConfiguration.current
    val locale = configuration.locales[0]
    if (start != null && end != null) {
        val startDate = Date(start)
        val endDate = Date(end)
        val dateFormat = DateFormat.getDateInstance(DateFormat.MEDIUM, locale)
        val text = "${dateFormat.format(startDate)} - ${dateFormat.format(endDate)}"
        Row(
            modifier = modifier,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                painter = painterResource(id = R.drawable.time_range_summary),
                contentDescription = null,
                modifier = Modifier.size(MaterialTheme.iconSizes.small)
            )
            Spacer(modifier = Modifier.width(MaterialTheme.spacing.small))
            Text(
                text,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.nearBlack
            )
        }
    }
}

@Composable
fun BadgeAcquisitionTimeSummary(
    viewData: BadgesDetailViewData.Loaded,
    modifier: Modifier = Modifier
) {
    val userBadge = viewData.badgesDetail
    val acquisitionTime = userBadge.acquisitionTime
    if (acquisitionTime != null) {
        val date = Date(acquisitionTime)
        val configuration = LocalConfiguration.current
        val locale = configuration.locales[0]
        val dateFormat = DateFormat.getDateInstance(DateFormat.MEDIUM, locale)
        val dateStr = dateFormat.format(date)
        val text = stringResource(R.string.acquisition_time_summary_badges, dateStr)
        Row(
            modifier = modifier,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                painter = painterResource(id = R.drawable.acquisition_time_summary),
                contentDescription = null,
                modifier = Modifier.size(MaterialTheme.iconSizes.small)
            )
            Spacer(modifier = Modifier.width(MaterialTheme.spacing.small))
            Text(
                text,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.nearBlack
            )
        }
    }
}

private const val SPAN_COUNT = 2

@Composable
fun BadgesAchievementList(
    viewData: BadgesDetailViewData.Loaded,
    modifier: Modifier = Modifier,
) {
    if (viewData.badgesAchievementDataList != null) {
        if (viewData.badgesAchievementDataList.isNotEmpty()) {
            BadgesAchievementListCom(viewData.badgesAchievementDataList.toImmutableList(), modifier)
        }
    }
}

@Composable
private fun BadgesAchievementListCom(
    value: ImmutableList<BadgesAchievementData>,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier,
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.medium)
    ) {
        val isOddCount = value.size % 2 != 0

        if (isOddCount) {
            val rowCount = (value.size + 1) / 2

            for (rowIndex in 0 until rowCount) {
                val startIndex = rowIndex * 2
                val itemsInThisRow = minOf(2, value.size - startIndex)

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.medium)
                ) {
                    for (i in 0 until itemsInThisRow) {
                        val item = value[startIndex + i]
                        SuuntoCard(
                            modifier = Modifier.weight(1f)
                        ) {
                            Column(
                                modifier = Modifier.padding(MaterialTheme.spacing.medium),
                            ) {
                                BadgesAchievementItem(item)
                            }
                        }
                    }
                }
            }
        } else {
            val rows = remember(value) {
                value.chunked(SPAN_COUNT)
            }
            rows.forEach { rowItems ->
                SuuntoCard(Modifier.fillMaxWidth()) {
                    Column(
                        modifier = Modifier.padding(MaterialTheme.spacing.medium)
                    ) {
                        BadgesAchievementRow(rowItems)
                    }
                }
            }
        }
    }
}

@Composable
private fun BadgesAchievementRow(
    rowItems: List<BadgesAchievementData>,
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.medium)
    ) {
        rowItems.forEachIndexed { index, item ->
            Row(
                modifier = Modifier.weight(1f),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                BadgesAchievementItem(item)
                if (index < rowItems.size - 1) {
                    VerticalDivider(
                        color = MaterialTheme.colorScheme.dividerColor,
                        modifier = Modifier.height(24.dp)
                    )
                }
            }
        }
    }
}

@Composable
private fun BadgesAchievementItem(
    value: BadgesAchievementData,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier
    ) {
        Text(
            text = value.explanation,
            color = MaterialTheme.colorScheme.onSurface,
            style = MaterialTheme.typography.bodySmall,
        )
        Row(
            modifier = Modifier.padding(top = MaterialTheme.spacing.xsmall),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Text(
                modifier = Modifier.padding(start = MaterialTheme.spacing.xsmall),
                text = value.explanation,
                color = MaterialTheme.colorScheme.onSurface,
                style = MaterialTheme.typography.bodyXLargeBold,
            )
        }
    }
}

@Composable
internal fun BadgesDetailIntroduction(
    viewData: BadgesDetailViewData.Loaded,
    modifier: Modifier = Modifier,
) {
    SuuntoCard(
        modifier = modifier
            .padding(MaterialTheme.spacing.medium),
    ) {
        Column(
            modifier = Modifier
                .padding(MaterialTheme.spacing.medium),
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.Start,
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Text(
                    text = stringResource(id = R.string.description_badges),
                    color = MaterialTheme.colorScheme.nearBlack,
                    style = MaterialTheme.typography.bodyLargeBold,
                )
            }
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = MaterialTheme.spacing.small),
                horizontalArrangement = Arrangement.Start,
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Text(
                    text = viewData.badgesDetail.badgeDesc ?: "badges desc null",
                    color = MaterialTheme.colorScheme.nearBlack,
                    style = MaterialTheme.typography.bodyLarge,
                )
            }
        }
    }
}

@Composable
private fun ExploreMoreItems(
    viewData: BadgesDetailViewData.Loaded,
    onEvent: (BadgeDetailViewEvent) -> Unit,
    modifier: Modifier = Modifier,
) {
    SuuntoCard(
        modifier = modifier
            .padding(MaterialTheme.spacing.medium),
    ) {
        Column(
            modifier = Modifier
                .padding(MaterialTheme.spacing.medium),
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.Start,
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Text(
                    text = stringResource(id = R.string.explore_more_badges),
                    color = MaterialTheme.colorScheme.nearBlack,
                    style = MaterialTheme.typography.bodyLargeBold,
                )
            }
            ExploreMoreContent(viewData, onEvent = onEvent)
        }
    }
}

@Composable
private fun ExploreMoreContent(
    badges: BadgesDetailViewData.Loaded,
    onEvent: (BadgeDetailViewEvent) -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .padding(top = MaterialTheme.spacing.medium),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        badges.exploreMore.forEach { badge ->
            ExploreMoreBadgeItem(
                badge = badge,
                onEvent = onEvent
            )
        }
    }
}

@Composable
private fun ExploreMoreBadgeItem(
    badge: ExploreMore,
    onEvent: (BadgeDetailViewEvent) -> Unit
) {
    Column(
        modifier = Modifier
            .width(80.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        val imageUrl = badge.badgeIconUrl
        Box(
            modifier = Modifier
                .size(80.dp)
                .background(
                    shape = MaterialTheme.shapes.medium,
                    color = MaterialTheme.colorScheme.surface
                )
                .clickable {
                    badge.badgeConfigId?.let {
                        BadgeDetailViewEvent.OnExploreBadgesClick(
                            it
                        )
                    }?.let { onEvent(it) }
                },

            contentAlignment = Alignment.Center
        ) {
            if (!imageUrl.isNullOrBlank()) {
                AsyncImage(
                    model = imageUrl,
                    contentDescription = badge.badgeName,
                    modifier = Modifier.size(80.dp)
                )
            }
        }
        Spacer(modifier = Modifier.height(MaterialTheme.spacing.small))
        Text(
            text = badge.badgeName ?: "",
            style = MaterialTheme.typography.body,
            textAlign = TextAlign.Center,
            color = MaterialTheme.colorScheme.nearBlack,
            modifier = Modifier.fillMaxWidth()
        )
    }
}
