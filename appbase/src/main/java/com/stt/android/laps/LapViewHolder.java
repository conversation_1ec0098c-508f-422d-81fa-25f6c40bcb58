package com.stt.android.laps;

import android.content.Context;
import android.view.View;
import androidx.annotation.ColorInt;
import androidx.core.content.ContextCompat;
import androidx.percentlayout.widget.PercentFrameLayout;
import androidx.recyclerview.widget.RecyclerView;
import com.github.mikephil.charting.data.LineData;
import com.github.mikephil.charting.data.LineDataSet;
import com.stt.android.R;
import com.stt.android.ThemeColors;
import com.stt.android.databinding.LapTableWidgetItemBinding;
import com.stt.android.domain.user.ActivityTypeHelper;
import com.stt.android.domain.user.MeasurementUnit;
import com.stt.android.domain.workout.ActivityType;
import com.stt.android.domain.workout.SpeedPaceState;
import com.stt.android.ski.CompleteSkiRun;
import com.stt.android.ui.utils.TextFormatter;

class LapViewHolder extends RecyclerView.ViewHolder {
    @ColorInt
    private final int defaultColor;
    @ColorInt
    private final int fastestColor;
    @ColorInt
    private final int fastestAltitudeChartColor;

    private final ActivityType activityType;
    private final MeasurementUnit measurementUnit;

    private final PercentFrameLayout itemView;

    private final LapTableWidgetItemBinding binding;

    LapViewHolder(PercentFrameLayout itemView, ActivityType activityType,
        MeasurementUnit measurementUnit) {
        super(itemView);
        binding = LapTableWidgetItemBinding.bind(itemView);
        this.itemView = itemView;

        binding.altitudeChart.setNoDataText("");
        binding.altitudeChart.getDescription().setText("");
        binding.altitudeChart.setEnabled(false);
        binding.altitudeChart.setScaleEnabled(false);
        binding.altitudeChart.setDrawGridBackground(false);
        binding.altitudeChart.setDrawBorders(false);
        binding.altitudeChart.setHighlightPerDragEnabled(false);
        binding.altitudeChart.setHighlightPerTapEnabled(false);
        binding.altitudeChart.getXAxis().setEnabled(false);
        binding.altitudeChart.getAxisLeft().setEnabled(false);
        binding.altitudeChart.getAxisRight().setEnabled(false);

        Context context = itemView.getContext();
        defaultColor = ThemeColors.primaryTextColor(context);
        fastestColor = ThemeColors.resolveColor(context, R.attr.newAccentColor);
        fastestAltitudeChartColor =
            ContextCompat.getColor(context, R.color.graphlib_altitude_fastest);
        this.activityType = activityType;
        this.measurementUnit = measurementUnit;
    }

    void bind(CompleteLap lap, double fastestFullLapSpeed, double fastestLapSpeed,
        boolean singleLap, boolean activityHasHr) {

        SpeedPaceState speedPaceState =
            ActivityTypeHelper.getDefaultSpeedPaceState(activityType);

        // distance
        String lapNumberText = TextFormatter.formatDistanceWithoutZeros(
            speedPaceState == SpeedPaceState.SPEEDKNOTS
                ? measurementUnit.fromMetersToNauticalMile(lap.getWorkoutDistanceOnEnd())
                : measurementUnit.toDistanceUnit(lap.getWorkoutDistanceOnEnd()));

        // avg speed or pace
        String formattedAvgSpeedOrPace;

        switch (speedPaceState) {
            case SPEED: {
                formattedAvgSpeedOrPace =
                    TextFormatter.formatSpeed(measurementUnit.toSpeedUnit(lap.getAverageSpeed()));
                break;
            }
            case SPEEDKNOTS: {
                formattedAvgSpeedOrPace =
                    TextFormatter.formatSpeed(measurementUnit.toKnots(lap.getAverageSpeed()));
                break;
            }
            case PACE:
            default: {
                double pace = measurementUnit.toPaceUnit(lap.getAverageSpeed());
                formattedAvgSpeedOrPace =
                    TextFormatter.formatElapsedTime(Math.round(pace * 60.0), false);
                break;
            }
        }

        double speed = lap.getAverageSpeed();
        binding.altitudeChartContainer.setVisibility(View.GONE);
        //set progress based on fastest lap. Incomplete laps included
        int progress =
            speed >= fastestLapSpeed ? 100 : (int) Math.floor(100 * speed / fastestLapSpeed);
        binding.avgSpeedBar.setProgress(progress);
        binding.avgSpeedBar.setVisibility(View.VISIBLE);

        String lapDurationText = formatLapDuration(lap);
        String lapAscentText =
            TextFormatter.formatAltitude(measurementUnit.toAltitudeUnit(lap.getTotalAscent()));
        String lapDescentText =
            TextFormatter.formatAltitude(measurementUnit.toAltitudeUnit(lap.getTotalDescent()));

        // hr
        String lapAvgHrText = "";
        int hr = lap.getAverageHeartRate();
        if (!activityHasHr) {
            LapsPercentLayoutUtils.hideColumnAndResize(itemView, binding.lapAvgHr);
        } else if (hr > 0) {
            lapAvgHrText = String.valueOf(hr);
        }

        if (activityType.isIndoor()) {
            LapsPercentLayoutUtils.hideColumnAndResize(itemView, binding.lapAscendOrSkiDistance);
            LapsPercentLayoutUtils.hideColumnAndResize(itemView, binding.lapDescent);
        } else {
            // ascend & descend
            binding.lapAscendOrSkiDistance.setText(lapAscentText);
            binding.lapDescent.setText(lapDescentText);
        }

        //only full laps counted here
        boolean isFastestLap = !singleLap && speed > 0.0 && speed == fastestFullLapSpeed;
        updateLapTexts(isFastestLap, lapNumberText, formattedAvgSpeedOrPace, lapDurationText,
            lapAvgHrText, lapAscentText, lapDescentText);
    }

    private static String formatLapDuration(CompleteLap completeLap) {
        long durationInSeconds = Math.round(
            (completeLap.getWorkoutDurationOnEnd() - completeLap.getWorkoutDurationOnStart())
                / 1000.0);
        return TextFormatter.formatElapsedTime(durationInSeconds, durationInSeconds > 3600L);
    }

    void bindSlopeSki(CompleteSkiRun skiRun, int run, double fastestFullLapSpeed,
            double longestLapDistance, double shortestLapDistance, boolean singleLap,
            boolean activityHasHr) {
        // run
        String skiRunText = Integer.toString(run + 1);
        binding.lapDistanceOrSkiRun.setText(skiRunText);

        // avg speed
        double avgLapSpeed = skiRun.getAverageSpeed();
        String formattedAvgSpeed =
            TextFormatter.formatSpeed(measurementUnit.toSpeedUnit(avgLapSpeed));
        binding.avgSpeedBar.setVisibility(View.GONE);

        boolean isFastestLap =
            !singleLap && avgLapSpeed > 0.0 && avgLapSpeed >= fastestFullLapSpeed;
        binding.altitudeChartContainer.setVisibility(View.VISIBLE);
        final LineData altitudeData = skiRun.getAltitudeData(itemView.getContext());
        if (isFastestLap && altitudeData.getDataSets().size() > 0) {
            LineDataSet dataSet = (LineDataSet) altitudeData.getDataSetByIndex(0);
            dataSet.setColor(fastestAltitudeChartColor);
            dataSet.setFillColor(fastestAltitudeChartColor);
        }
        binding.altitudeChart.setData(altitudeData);

        binding.altitudeChart.getLegend().setEnabled(false);
        binding.altitudeChart.setViewPortOffsets(0.0F, 0.0F, 0.0F, 0.0F);

        // scales the chart such that the shortest has a width percentage of 50, and longest 100
        float widthPercent;
        double lapDistance = skiRun.getDistance();
        if (lapDistance >= longestLapDistance) {
            widthPercent = 1.0F;
        } else {
            widthPercent = 0.50F + (float) (0.25 * (lapDistance - shortestLapDistance) / (
                longestLapDistance
                    - shortestLapDistance));
        }
        PercentFrameLayout.LayoutParams layoutParams =
            (PercentFrameLayout.LayoutParams) binding.altitudeChart.getLayoutParams();
        layoutParams.getPercentLayoutInfo().widthPercent = widthPercent;
        binding.altitudeChart.setLayoutParams(layoutParams);

        if (binding.altitudeChart.getWidth() > 0 && binding.altitudeChart.getHeight() > 0) {
            binding.altitudeChart.invalidate();
        } else {
            // otherwise there will be some rendering artifacts
            binding.altitudeChart.animateY(750);
        }
        String lapDescendText =
            TextFormatter.formatAltitude(measurementUnit.toAltitudeUnit(skiRun.getTotalDescent()));
        String lapDurationText = formatLapDuration(skiRun);

        // hr
        String lapAvgHrText = "";
        int hr = skiRun.getAverageHeartRate();
        if (!activityHasHr) {
            LapsPercentLayoutUtils.hideColumnAndResize(itemView, binding.lapAvgHr);
        } else if (hr > 0) {
            lapAvgHrText = String.valueOf(hr);
        }

        updateLapTexts(isFastestLap, skiRunText, formattedAvgSpeed, lapDurationText, lapAvgHrText,
            TextFormatter.formatDistance(measurementUnit.toDistanceUnit(lapDistance)),
            lapDescendText);
    }

    private void updateLapTexts(boolean isFastestLap, String lapNumberOrSkiRunText,
        String formattedAvgSpeed, String lapDurationText, String lapAvgHrText,
        String lapAscentOrDistanceText, String lapDescentText) {
        int achievementResourceId = isFastestLap ? R.drawable.ic_achievement : 0;
        int achievementDrawablePadding = isFastestLap ? binding.avgSpeed.getContext()
            .getResources()
            .getDimensionPixelSize(R.dimen.fastest_lap_icon_padding) : 0;
        int lapColor = isFastestLap ? fastestColor : defaultColor;

        binding.lapDistanceOrSkiRun.setTextColor(lapColor);
        binding.lapDistanceOrSkiRun.setText(lapNumberOrSkiRunText);
        binding.avgSpeed.setText(formattedAvgSpeed);
        binding.avgSpeed.setTextColor(lapColor);
        binding.avgSpeed.setCompoundDrawablePadding(achievementDrawablePadding);
        binding.avgSpeed.setCompoundDrawablesWithIntrinsicBounds(achievementResourceId, 0, 0, 0);
        binding.lapDuration.setTextColor(lapColor);
        binding.lapDuration.setText(lapDurationText);
        binding.lapAvgHr.setTextColor(lapColor);
        binding.lapAvgHr.setText(lapAvgHrText);
        binding.lapAscendOrSkiDistance.setTextColor(lapColor);
        binding.lapAscendOrSkiDistance.setText(lapAscentOrDistanceText);
        binding.lapDescent.setTextColor(lapColor);
        binding.lapDescent.setText(lapDescentText);
    }
}
