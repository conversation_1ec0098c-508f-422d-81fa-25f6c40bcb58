package com.stt.android.utils

import android.annotation.SuppressLint
import android.app.Application
import android.util.Log
import com.stt.android.easterEgg.EasterEggBase.Companion.getSttDumpFolder
import com.stt.android.network.interfaces.ANetworkProvider
import timber.log.Timber
import java.io.File
import java.io.FileOutputStream
import java.io.OutputStreamWriter
import java.util.regex.Pattern
import javax.inject.Singleton

@Singleton
class FileTimberTree
@SuppressLint("LogNotTimber")
constructor(
    application: Application
) : Timber.DebugTree() {

    override fun v(message: String?, vararg args: Any?) {
        super.v(message, *args)
        printToFile(Log.VERBOSE, formatString(message, *args))
    }

    override fun v(t: Throwable?, message: String?, vararg args: Any?) {
        super.v(t, message, *args)
        printToFile(Log.VERBOSE, formatString(message, *args), t)
    }

    override fun d(message: String?, vararg args: Any?) {
        super.d(message, *args)
        printToFile(Log.DEBUG, formatString(message, *args))
    }

    override fun d(t: Throwable?, message: String?, vararg args: Any?) {
        super.d(t, message, *args)
        printToFile(Log.DEBUG, formatString(message, *args), t)
    }

    override fun i(message: String?, vararg args: Any?) {
        super.i(message, *args)
        printToFile(Log.INFO, formatString(message, *args))
    }

    override fun i(t: Throwable?, message: String?, vararg args: Any?) {
        super.i(t, message, *args)
        printToFile(Log.INFO, formatString(message, *args), t)
    }

    override fun w(message: String?, vararg args: Any?) {
        super.w(message, *args)
        printToFile(Log.WARN, formatString(message, *args))
    }

    override fun w(t: Throwable?, message: String?, vararg args: Any?) {
        super.w(t, message, *args)
        printToFile(Log.WARN, formatString(message, *args), t)
    }

    override fun e(message: String?, vararg args: Any?) {
        super.e(message, *args)
        printToFile(Log.ERROR, formatString(message, *args))
    }

    override fun e(t: Throwable?, message: String?, vararg args: Any?) {
        super.e(t, message, *args)
        printToFile(Log.ERROR, formatString(message, *args), t)
    }

    /**
     * The actual file where to write the logs
     */
    private var logFile: File? = null

    private fun printToFile(priority: Int, incomingMsg: String?, t: Throwable?) {
        var msg = incomingMsg
        if (t != null) {
            msg += "\n${Log.getStackTraceString(t)}"
        }
        printToFile(priority, msg)
    }

    private fun printToFile(priority: Int, msg: String?) {
        val tag = createTag()
        kotlin.runCatching {
            OutputStreamWriter(
                FileOutputStream(logFile, true),
                ANetworkProvider.UTF_8_CHARSET
            ).use {
                it.write(System.currentTimeMillis().toString())
                it.write('\t'.code)
                it.write(logLevelToString(priority))
                it.write('\t'.code)
                it.write(tag)
                it.write('\t'.code)
                it.write(msg)
                it.write('\n'.code)
            }
        }.onFailure {
            Timber.w(it, "Error printing to file")
        }
    }

    companion object {
        private const val LOG_FILENAME = "app.log"
        private val TAG = FileTimberTree::class.java.simpleName
        private val ANONYMOUS_CLASS = Pattern.compile("\\$\\d+$")
        private val NEXT_TAG = ThreadLocal<String>()
        private fun createTag(): String {
            var tag = NEXT_TAG.get()
            if (tag != null) {
                NEXT_TAG.remove()
                return tag
            }
            val stackTrace = Throwable().stackTrace
            check(stackTrace.size >= 6) { "Synthetic stacktrace didn't have enough elements: are you using proguard?" }
            tag = stackTrace[5].className
            if (tag != null) {
                val m = ANONYMOUS_CLASS.matcher(tag)
                if (m.find()) {
                    tag = m.replaceAll("")
                }
            }
            return tag?.substring(tag.lastIndexOf('.') + 1) ?: ""
        }

        fun logLevelToString(loglevel: Int): String {
            return when (loglevel) {
                Log.VERBOSE -> "VERBOSE"
                Log.DEBUG -> "DEBUG"
                Log.INFO -> "INFO"
                Log.WARN -> "WARN"
                Log.ERROR -> "ERROR"
                Log.ASSERT -> "ASSERT"
                else -> "UNKNOWN"
            }
        }

        private fun formatString(message: String?, vararg args: Any?): String? {
            // If no varargs are supplied, treat it as a request to log the string without formatting.
            return if (message == null || args.isEmpty()) message else String.format(message, *args)
        }
    }

    init {
        val dst = getSttDumpFolder(application)
        logFile = if (dst == null) {
            Log.w(TAG, "Unable to open '$LOG_FILENAME' file for logging")
            // This might happen if there's no sdcard in the device
            null
        } else {
            File(dst, LOG_FILENAME)
        }
    }
}
