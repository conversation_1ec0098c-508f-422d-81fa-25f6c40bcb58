package com.stt.android.domain.user;

import android.os.Parcel;
import android.os.Parcelable;
import android.provider.BaseColumns;
import androidx.annotation.NonNull;
import androidx.annotation.StringDef;
import android.text.TextUtils;
import com.j256.ormlite.field.DataType;
import com.j256.ormlite.field.DatabaseField;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import timber.log.Timber;

public class ReactionSummary implements Parcelable {
    public static final String TABLE_NAME = "reactionSummary";

    public abstract static class DbFields {
        public static final String ID = BaseColumns._ID;
        public static final String WORKOUT_KEY = "workoutKey";
        public static final String REACTION = "reaction";
        public static final String COUNT = "count";
        public static final String USER_REACTED = "userReacted";
    }

    public static final String REACTION_LIKE = "LIKE";

    @StringDef({ REACTION_LIKE })
    @Retention(RetentionPolicy.SOURCE)
    public @interface ReactionType {
    }

    @DatabaseField(dataType = DataType.LONG, columnName = DbFields.ID, id = true)
    private final long id;

    @NonNull
    @DatabaseField(dataType = DataType.STRING, columnName = DbFields.WORKOUT_KEY)
    private final String workoutKey;

    @ReactionType
    @NonNull
    @DatabaseField(dataType = DataType.STRING, columnName = DbFields.REACTION)
    private final String reaction;

    @DatabaseField(dataType = DataType.INTEGER, columnName = DbFields.COUNT)
    private final int count;

    @DatabaseField(dataType = DataType.BOOLEAN, columnName = DbFields.USER_REACTED)
    private final boolean userReacted;

    /**
     * Only used by OrmLite.
     */
    ReactionSummary() {
        //noinspection ConstantConditions
        this(0L, null, null, 0, false);
    }

    private ReactionSummary(long id, @NonNull String workoutKey, @NonNull String reaction,
        int count, boolean userReacted) {
        this.id = id;
        this.workoutKey = workoutKey;
        this.reaction = reaction;
        this.count = count;
        this.userReacted = userReacted;
    }

    @NonNull
    public String getWorkoutKey() {
        return workoutKey;
    }

    @ReactionType
    @NonNull
    public String getReaction() {
        return reaction;
    }

    public int getCount() {
        return count;
    }

    public boolean isUserReacted() {
        return userReacted;
    }

    @Override
    public int hashCode() {
        int result;
        result = (int) (id ^ (id >>> 32));
        result = 31 * result + workoutKey.hashCode();
        result = 31 * result + reaction.hashCode();
        result = 31 * result + count;
        result = 31 * result + (userReacted ? 1 : 0);
        return result;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        ReactionSummary that = (ReactionSummary) o;
        if (id != that.id) return false;
        if (!workoutKey.equals(that.workoutKey)) return false;
        if (!reaction.equals(that.reaction)) return false;
        if (count != that.count) return false;
        if (userReacted != that.userReacted) return false;

        return true;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel parcel, int i) {
        parcel.writeLong(id);
        parcel.writeString(workoutKey);
        parcel.writeString(reaction);
        parcel.writeInt(count);
        parcel.writeByte((byte) (userReacted ? 1 : 0));
    }

    public static final Creator<ReactionSummary> CREATOR = new Creator<ReactionSummary>() {
        @Override
        public ReactionSummary createFromParcel(Parcel in) {
            long id = in.readLong();
            String workoutKey = in.readString();
            String reaction = in.readString();
            int count = in.readInt();
            boolean userReacted = in.readByte() == 1;
            return new Builder().id(id)
                .workoutKey(workoutKey)
                .reaction(reaction)
                .count(count)
                .userReacted(userReacted)
                .build();
        }

        @Override
        public ReactionSummary[] newArray(int size) {
            return new ReactionSummary[size];
        }
    };

    @NonNull
    public static ReactionSummary remote(@NonNull String workoutKey, @NonNull String reaction,
        int count, boolean userReacted) {
        return new Builder().workoutKey(workoutKey)
            .reaction(reaction)
            .count(count)
            .userReacted(userReacted)
            .build();
    }

    @NonNull
    public static ReactionSummary local(@NonNull String workoutKey, @NonNull String reaction) {
        return new Builder().workoutKey(workoutKey)
            .reaction(reaction)
            .count(0)
            .userReacted(false)
            .build();
    }

    @NonNull
    public Builder toBuilder() {
        return new Builder().id(id)
            .workoutKey(workoutKey)
            .reaction(reaction)
            .count(count)
            .userReacted(userReacted);
    }

    public static class Builder {
        private long id;
        private String workoutKey;
        @ReactionType
        private String reaction;
        private int count;
        private boolean userReacted;

        private Builder() {
        }

        @NonNull
        public Builder id(long id) {
            this.id = id;
            return this;
        }

        @NonNull
        public Builder workoutKey(String workoutKey) {
            this.workoutKey = workoutKey;
            return this;
        }

        @NonNull
        public Builder reaction(String reaction) {
            this.reaction = reaction;
            return this;
        }

        @NonNull
        public Builder count(int count) {
            this.count = count;
            return this;
        }

        @NonNull
        public Builder userReacted(boolean userReacted) {
            this.userReacted = userReacted;
            return this;
        }

        @NonNull
        public ReactionSummary build() {
            if (TextUtils.isEmpty(workoutKey)) {
                throw new IllegalStateException("Missing workout key");
            }
            if (TextUtils.isEmpty(reaction)) {
                throw new IllegalStateException("Missing reaction");
            }
            if (!REACTION_LIKE.equals(reaction)) {
                Timber.w("Unsupported reaction: %s", reaction);
            }

            id = 31 * workoutKey.hashCode() + reaction.hashCode();
            return new ReactionSummary(id, workoutKey, reaction, count, userReacted);
        }
    }
}
