@file:Suppress("DEPRECATION")

package com.stt.android.domain.database

import android.content.SharedPreferences
import android.database.Cursor
import android.database.sqlite.SQLiteDatabase
import com.squareup.moshi.Moshi
import com.squareup.moshi.Types
import com.squareup.moshi.adapter
import com.stt.android.data.source.local.routes.LocalPoint
import com.stt.android.data.source.local.user.UserDao
import com.stt.android.data.source.local.workout.LocalWorkoutHeader
import com.stt.android.data.source.local.workout.WorkoutHeaderDao
import com.stt.android.data.source.local.workout.tss.LocalTSS
import com.stt.android.data.source.local.workouts.KEY_CURRENT_USER_WORKOUTS_LAST_MODIFIED_TIMESTAMP
import com.stt.android.data.workout.WorkoutRepository
import com.stt.android.db.containsTable
import com.stt.android.db.getBlobOrNull
import com.stt.android.db.getDoubleOrNull
import com.stt.android.db.getInt
import com.stt.android.db.getIntOrNull
import com.stt.android.db.getLongOrNull
import com.stt.android.db.getStringOrNull
import com.stt.android.domain.database.MigrationUtil.bytesToPoint
import com.stt.android.domain.user.LegacyWorkoutHeader
import kotlinx.coroutines.Dispatchers.IO
import kotlinx.coroutines.runBlocking
import timber.log.Timber
import androidx.core.content.edit

class DatabaseUpgrade56To57Helper(
    private val db: SQLiteDatabase,
    private val dao: WorkoutHeaderDao,
    private val userDao: UserDao,
    private val sharedPreferences: SharedPreferences,
    moshi: Moshi
) {
    private val tableName = LegacyWorkoutHeader.TABLE_NAME

    private val localTSSAdapter = moshi.adapter<LocalTSS>()
    private val localTSSListAdapter = moshi.adapter<List<LocalTSS>>(
        Types.newParameterizedType(
            List::class.java,
            LocalTSS::class.java
        )
    )

    fun migrateToRoom() {
        if (db.containsTable(tableName)) {
            val currentUsername = runBlocking(IO) {
                userDao.findCurrentUser()?.username
            }
            if (currentUsername == null) {
                Timber.w("Skipping migration of workouts, missing username")
            } else {
                Timber.i("Migrating $tableName from old db to room db for user: $currentUsername")
                // first we migrate up to OWN_WORKOUT_LIMIT of users own workouts
                val ownCount = db.query(
                    tableName,
                    where = "${LegacyWorkoutHeader.DbFields.USERNAME} = '$currentUsername'",
                    orderBy = "${LegacyWorkoutHeader.DbFields.START_TIME} DESC",
                    limit = OWN_WORKOUT_LIMIT
                ).use(::migrateAllWorkoutHeaders)
                // then we migrate the last OTHERS_WORKOUT_LIMIT of followees workouts
                db.query(
                    tableName,
                    where = "${LegacyWorkoutHeader.DbFields.USERNAME} != '$currentUsername'",
                    orderBy = "${LegacyWorkoutHeader.DbFields.START_TIME} DESC",
                    limit = OTHERS_WORKOUT_LIMIT
                ).use(::migrateAllWorkoutHeaders)

                val limitHit = ownCount >= OWN_WORKOUT_LIMIT
                if (limitHit) {
                    Timber.i("Hit limit of $OWN_WORKOUT_LIMIT own workouts in migration")
                    // Clear own workouts fetched timestamp to make sure all workouts are fetched
                    // when syncing, in case some could not be migrated due to hitting the limit.
                    clearOwnWorkoutsFetchedTimestamp()
                }
            }
            Timber.d("Dropping old $tableName table")
            db.execSQL("DROP TABLE IF EXISTS $tableName")
        }
    }

    private fun migrateAllWorkoutHeaders(cursor: Cursor): Int {
        var count = 0
        if (cursor.moveToFirst()) {
            do {
                cursor.migrateWorkoutHeader()
                count++
            } while (cursor.moveToNext())
        }
        return count
    }

    private fun Cursor.migrateWorkoutHeader() {
        val localWorkoutHeader = LocalWorkoutHeader(
            id = getInt(LegacyWorkoutHeader.DbFields.ID),
            key = getStringOrNull(LegacyWorkoutHeader.DbFields.KEY),
            totalDistance = getDoubleOrNull(LegacyWorkoutHeader.DbFields.TOTAL_DISTANCE) ?: 0.0,
            maxSpeed = getDoubleOrNull(LegacyWorkoutHeader.DbFields.MAX_SPEED) ?: 0.0,
            activityId = getIntOrNull(LegacyWorkoutHeader.DbFields.ACTIVITY_ID) ?: 0,
            avgSpeed = getDoubleOrNull(LegacyWorkoutHeader.DbFields.AVERAGE_SPEED) ?: 0.0,
            description = getStringOrNull(LegacyWorkoutHeader.DbFields.DESCRIPTION),
            startPosition = getPointOrNull(LegacyWorkoutHeader.DbFields.START_POSITION),
            stopPosition = getPointOrNull(LegacyWorkoutHeader.DbFields.STOP_POSITION),
            centerPosition = getPointOrNull(LegacyWorkoutHeader.DbFields.CENTER_POSITION),
            startTime = getLongOrNull(LegacyWorkoutHeader.DbFields.START_TIME) ?: 0,
            stopTime = getLongOrNull(LegacyWorkoutHeader.DbFields.STOP_TIME) ?: 0,
            totalTime = getDoubleOrNull(LegacyWorkoutHeader.DbFields.TOTAL_TIME) ?: 0.0,
            energyConsumption = getDoubleOrNull(LegacyWorkoutHeader.DbFields.ENERGY_CONSUMPTION)
                ?: 0.0,
            username = getStringOrNull(LegacyWorkoutHeader.DbFields.USERNAME) ?: run {
                Timber.w("Cannot migrate to room workout with null username, it was prob invalid.")
                return
            },
            heartRateAvg = getDoubleOrNull(LegacyWorkoutHeader.DbFields.HEART_RATE_AVG) ?: 0.0,
            heartRateAvgPercentage = getDoubleOrNull(LegacyWorkoutHeader.DbFields.HEART_RATE_AVG_PERCENTAGE)
                ?: 0.0,
            heartRateMax = getDoubleOrNull(LegacyWorkoutHeader.DbFields.HEART_RATE_MAX) ?: 0.0,
            heartRateMaxPercentage = getDoubleOrNull(LegacyWorkoutHeader.DbFields.HEART_RATE_AVG_MAX_PERCENTAGE)
                ?: 0.0,
            heartRateUserSetMax = getDoubleOrNull(LegacyWorkoutHeader.DbFields.HEART_RATE_USER_SET_MAX)
                ?: 0.0,
            pictureCount = getIntOrNull(LegacyWorkoutHeader.DbFields.PICTURE_COUNT) ?: 0,
            viewCount = getIntOrNull(LegacyWorkoutHeader.DbFields.VIEW_COUNT) ?: 0,
            commentCount = getIntOrNull(LegacyWorkoutHeader.DbFields.COMMENT_COUNT) ?: 0,
            sharingFlags = getIntOrNull(LegacyWorkoutHeader.DbFields.SHARING_FLAGS) ?: 0,
            locallyChanged = getIntOrNull(LegacyWorkoutHeader.DbFields.LOCALLY_CHANGED) == 1,
            deleted = getIntOrNull(LegacyWorkoutHeader.DbFields.DELETED) == 1,
            manuallyCreated = getIntOrNull(LegacyWorkoutHeader.DbFields.MANUALLY_CREATED) == 1,
            averageCadence = getIntOrNull(LegacyWorkoutHeader.DbFields.AVERAGE_CADENCE) ?: 0,
            maxCadence = getIntOrNull(LegacyWorkoutHeader.DbFields.MAX_CADENCE) ?: 0,
            polyline = getStringOrNull(LegacyWorkoutHeader.DbFields.POLYLINE),
            stepCount = getIntOrNull(LegacyWorkoutHeader.DbFields.STEP_COUNT) ?: 0,
            reactionCount = getIntOrNull(LegacyWorkoutHeader.DbFields.REACTION_COUNT) ?: 0,
            totalAscent = getDoubleOrNull(LegacyWorkoutHeader.DbFields.TOTAL_ASCENT) ?: 0.0,
            totalDescent = getDoubleOrNull(LegacyWorkoutHeader.DbFields.TOTAL_DESCENT) ?: 0.0,
            recoveryTime = getLongOrNull(LegacyWorkoutHeader.DbFields.RECOVERY_TIME) ?: 0,
            maxAltitude = getDoubleOrNull(LegacyWorkoutHeader.DbFields.MAX_ALTITUDE) ?: 0.0,
            minAltitude = getDoubleOrNull(LegacyWorkoutHeader.DbFields.MIN_ALTITUDE) ?: 0.0,
            seen = getIntOrNull(LegacyWorkoutHeader.DbFields.SEEN) == 1,
            extensionsFetched = getIntOrNull(LegacyWorkoutHeader.DbFields.EXTENSIONS_FETCHED) == 1,
            tss = getStringOrNull(LegacyWorkoutHeader.DbFields.TSS)?.let {
                localTSSAdapter.fromJson(
                    it
                )
            },
            tssList = getStringOrNull(LegacyWorkoutHeader.DbFields.TSS_LIST)?.let {
                localTSSListAdapter.fromJson(it)
            },
            isCommute = false,
            impactCardio = null,
            impactMuscular = null,
            suuntoTags = null,
            zoneSense = null,
        )
        runBlocking(IO) { dao.insert(localWorkoutHeader) }
    }

    /**
     * Lenient deserialization of points from the WorkoutHeader table due to the custom serialization
     * technique used and the previous migrations.
     */
    private fun Cursor.getPointOrNull(columnName: String): LocalPoint? = try {
        bytesToPoint(getBlobOrNull(columnName))
            ?.let {
                LocalPoint(
                    longitude = it.longitude,
                    latitude = it.latitude,
                    altitude = it.altitude,
                    name = it.name,
                    type = it.type
                )
            }
    } catch (e: Throwable) {
        Timber.w(e, "Error migrating point")
        null
    }

    private fun SQLiteDatabase.query(
        tableName: String,
        where: String?,
        orderBy: String? = null,
        limit: Int? = null
    ) = query(
        tableName,
        null,
        where,
        null,
        null,
        null,
        orderBy,
        limit?.toString()
    )

    private fun clearOwnWorkoutsFetchedTimestamp() =
        sharedPreferences.edit {
            remove(KEY_CURRENT_USER_WORKOUTS_LAST_MODIFIED_TIMESTAMP)
        }

    companion object {
        private const val OWN_WORKOUT_LIMIT = 1000
        private const val OTHERS_WORKOUT_LIMIT = WorkoutRepository.MAX_FOLLOWEES_WORKOUTS
    }
}
