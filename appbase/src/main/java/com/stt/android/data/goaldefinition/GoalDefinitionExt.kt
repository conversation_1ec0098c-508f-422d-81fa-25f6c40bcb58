package com.stt.android.data.goaldefinition

import android.content.Context
import android.content.res.Resources
import android.text.format.DateUtils
import androidx.annotation.WorkerThread
import com.stt.android.R
import com.stt.android.controllers.WorkoutHeaderController
import com.stt.android.data.releasedUTCMilli
import com.stt.android.domain.goaldefinition.GoalDefinition
import com.stt.android.domain.user.Goal
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.domain.workout.ActivityType
import com.stt.android.ui.utils.TextFormatter
import com.stt.android.utils.CalendarProvider
import timber.log.Timber
import java.util.Calendar
import java.util.Locale

private const val WEEKS_TO_CHECK_FOR_DEFAULT_GOALS = 5

// TODO use LocalDateTime instead
fun getStartTimeOfTheDay(startTime: Long, calendarProvider: CalendarProvider): Long {
    val calendar = calendarProvider.getCalendar()
    calendar.timeInMillis = startTime
    calendar.set(Calendar.HOUR_OF_DAY, 0)
    calendar.set(Calendar.MINUTE, 0)
    calendar.set(Calendar.SECOND, 0)
    calendar.set(Calendar.MILLISECOND, 0)
    return calendar.timeInMillis
}

/**
 * Calculates the default start time based on the given period.
 *
 *
 * For weekly / monthly goals, It will be start time of this week / month. For custom goals,
 * it's the current time.
 */
fun calculateDefaultStartTime(period: GoalDefinition.Period, calendarProvider: CalendarProvider): Long {
    return calculateStartTime(period, System.currentTimeMillis(), calendarProvider)
}

/**
 * Calculates the default start time based on the given period.
 *
 *
 * For weekly / monthly goals, It will be start time of the week / month for the timestamp. For
 * custom goals, it's the given timestamp.
 */
fun calculateStartTime(period: GoalDefinition.Period, timestamp: Long, calendarProvider: CalendarProvider): Long {
    val calendar = calendarProvider.getCalendar()
    calendar.timeInMillis = timestamp
    when (period) {
        GoalDefinition.Period.MONTHLY -> {
            calendar.timeInMillis = timestamp
            calendar.set(Calendar.HOUR_OF_DAY, 0)
            calendar.set(Calendar.MINUTE, 0)
            calendar.set(Calendar.SECOND, 0)
            calendar.set(Calendar.MILLISECOND, 0)
            calendar.set(Calendar.DATE, 1)
            return calendar.timeInMillis
        }
        GoalDefinition.Period.WEEKLY -> {
            calendar.timeInMillis = timestamp
            calendar.set(Calendar.HOUR_OF_DAY, 0)
            calendar.set(Calendar.MINUTE, 0)
            calendar.set(Calendar.SECOND, 0)
            calendar.set(Calendar.MILLISECOND, 0)
            calendar.set(Calendar.DAY_OF_WEEK, calendar.firstDayOfWeek)
            return calendar.timeInMillis
        }
        GoalDefinition.Period.CUSTOM -> return calendar.timeInMillis
        else -> throw IllegalArgumentException("Invalid goal period.")
    }
}

/**
 * For CUSTOM goals, it returns the end time set. Otherwise, returns [Long.MAX_VALUE], as there
 * is no expiration date for MONTHLY and WEEKLY goals.
 */
fun GoalDefinition.getEndTimeOrMaxValue(): Long {
    return if (period == GoalDefinition.Period.CUSTOM) endTime else Long.MAX_VALUE
}

fun GoalDefinition.updateEndTime(endTime: Long, calendarProvider: CalendarProvider): GoalDefinition {
    val calendar = calendarProvider.getCalendar()
    calendar.timeInMillis = endTime
    calendar.set(Calendar.HOUR_OF_DAY, 23)
    calendar.set(Calendar.MINUTE, 59)
    calendar.set(Calendar.SECOND, 59)
    calendar.set(Calendar.MILLISECOND, 999)
    return this.copy(endTime = calendar.timeInMillis)
}

fun GoalDefinition.getActivities(): List<ActivityType> = activityIds.map { ActivityType.valueOf(it) }

fun GoalDefinition.updateActivities(activities: List<ActivityType>): GoalDefinition {
    return this.copy(activityIds = activities.map { it.id })
}

fun GoalDefinition.getTitle(resources: Resources): String {
    return resources.getString(
        when (period) {
            GoalDefinition.Period.MONTHLY -> R.string.goal_title_monthly
            GoalDefinition.Period.WEEKLY -> R.string.goal_title_weekly
            GoalDefinition.Period.CUSTOM -> when (type) {
                GoalDefinition.Type.DURATION -> R.string.goal_title_duration
                GoalDefinition.Type.DISTANCE -> R.string.goal_title_distance
                GoalDefinition.Type.WORKOUTS -> R.string.goal_title_numberofworkouts
                GoalDefinition.Type.ENERGY -> R.string.goal_title_energy
            }
        }
    )
}

fun GoalDefinition.getAmountTargetString(context: Context, amount: Int, measurementUnit: MeasurementUnit): String {
    return when (type) {
        GoalDefinition.Type.DURATION -> String.format(
            Locale.US,
            "%d:%02d:%02d / %d:%02d:%02d",
            amount / 3600,
            amount % 3600 / 60,
            amount % 60,
            target / 3600,
            target % 3600 / 60,
            target % 60
        )
        GoalDefinition.Type.DISTANCE -> String.format(
            "%s / %s %s",
            TextFormatter.formatDistance(measurementUnit.toDistanceUnit(amount.toDouble())),
            TextFormatter.formatDistance(measurementUnit.toDistanceUnit(target.toDouble())),
            context.getString(measurementUnit.distanceUnit)
        )
        GoalDefinition.Type.WORKOUTS -> String.format(Locale.US, "%d / %d", amount, target)
        GoalDefinition.Type.ENERGY -> String.format(Locale.US, "%d / %d kcal", amount, target)
    }
}

/**
 * Create a goal that includes the given timestamp.
 *
 *
 * For weekly / monthly goal, the start / end time is adjusted to the beginning / end of the
 * week / month of the given timestamp.
 * For custom goal, the start / ent time is the start / end time of the goal definition.
 */
fun GoalDefinition.createGoal(timestamp: Long, calendarProvider: CalendarProvider): Goal {
    val startTime: Long
    val endTime: Long
    val calendar = calendarProvider.getCalendar()
    when (period) {
        GoalDefinition.Period.MONTHLY -> {
            calendar.timeInMillis = timestamp
            calendar.set(Calendar.HOUR_OF_DAY, 0)
            calendar.set(Calendar.MINUTE, 0)
            calendar.set(Calendar.SECOND, 0)
            calendar.set(Calendar.MILLISECOND, 0)
            calendar.set(Calendar.DATE, 1)
            startTime = Math.max(this.startTime, calendar.timeInMillis)

            calendar.set(Calendar.DATE, calendar.getActualMaximum(Calendar.DATE))
            calendar.set(Calendar.HOUR_OF_DAY, 23)
            calendar.set(Calendar.MINUTE, 59)
            calendar.set(Calendar.SECOND, 59)
            calendar.set(Calendar.MILLISECOND, 999)
            endTime = calendar.timeInMillis
        }
        GoalDefinition.Period.WEEKLY -> {
            calendar.timeInMillis = timestamp
            calendar.set(Calendar.HOUR_OF_DAY, 0)
            calendar.set(Calendar.MINUTE, 0)
            calendar.set(Calendar.SECOND, 0)
            calendar.set(Calendar.MILLISECOND, 0)
            calendar.set(Calendar.DAY_OF_WEEK, calendar.firstDayOfWeek)
            startTime = Math.max(this.startTime, calendar.timeInMillis)

            calendar.add(Calendar.DATE, 7)
            endTime = calendar.timeInMillis - 1
        }
        GoalDefinition.Period.CUSTOM -> {
            startTime = this.startTime
            endTime = this.endTime
        }
        else -> throw IllegalArgumentException("Invalid goal period type.")
    }

    return Goal(this, startTime, endTime)
}

/**
 * Creates a goal before the start time of the goal definition.
 *
 *
 * For weekly / monthly goal, the start / end time is adjusted to the beginning / end of the
 * week / month of the given timestamp.
 * For custom goal, it doesn't make much sense, so the world will be destroyed.
 */
fun GoalDefinition.createGoalBeforeTimeBegins(timestamp: Long, calendarProvider: CalendarProvider): Goal {
    val startTime: Long
    val endTime: Long
    val calendar = calendarProvider.getCalendar()
    when (period) {
        GoalDefinition.Period.MONTHLY -> {
            calendar.timeInMillis = timestamp
            calendar.set(Calendar.HOUR_OF_DAY, 0)
            calendar.set(Calendar.MINUTE, 0)
            calendar.set(Calendar.SECOND, 0)
            calendar.set(Calendar.MILLISECOND, 0)
            calendar.set(Calendar.DATE, 1)
            startTime = calendar.timeInMillis

            calendar.set(Calendar.DATE, calendar.getActualMaximum(Calendar.DATE))
            calendar.set(Calendar.HOUR_OF_DAY, 23)
            calendar.set(Calendar.MINUTE, 59)
            calendar.set(Calendar.SECOND, 59)
            calendar.set(Calendar.MILLISECOND, 999)
            endTime = Math.min(calendar.timeInMillis, this.startTime)
        }
        GoalDefinition.Period.WEEKLY -> {
            calendar.timeInMillis = timestamp
            calendar.set(Calendar.HOUR_OF_DAY, 0)
            calendar.set(Calendar.MINUTE, 0)
            calendar.set(Calendar.SECOND, 0)
            calendar.set(Calendar.MILLISECOND, 0)
            calendar.set(Calendar.DAY_OF_WEEK, calendar.firstDayOfWeek)
            startTime = calendar.timeInMillis

            calendar.add(Calendar.DATE, 7)
            calendar.add(Calendar.MILLISECOND, -1)
            endTime = Math.min(calendar.timeInMillis, this.startTime - 1L)
        }
        GoalDefinition.Period.CUSTOM -> throw IllegalArgumentException("Invalid goal period type.")
        else -> throw IllegalArgumentException("Invalid goal period type.")
    }

    return Goal(this, startTime, endTime)
}

/**
 * Creates a goal to represent the given time period.
 *
 *
 * The actual start time will be adjusted to the beginning of the given day, and the end time
 * will be adjusted to the end of the given day.
 */
fun GoalDefinition.createGoal(startTime: Long, endTime: Long, calendarProvider: CalendarProvider): Goal {
    var startTimeInternal = startTime
    var endTimeInternal = endTime
    val calendar = calendarProvider.getCalendar()
    calendar.timeInMillis = Math.max(startTimeInternal, this.startTime)
    calendar.set(Calendar.HOUR_OF_DAY, 0)
    calendar.set(Calendar.MINUTE, 0)
    calendar.set(Calendar.SECOND, 0)
    calendar.set(Calendar.MILLISECOND, 0)
    startTimeInternal = calendar.timeInMillis

    calendar.timeInMillis = Math.min(endTimeInternal, this.endTime)
    calendar.set(Calendar.HOUR_OF_DAY, 23)
    calendar.set(Calendar.MINUTE, 59)
    calendar.set(Calendar.SECOND, 59)
    calendar.set(Calendar.MILLISECOND, 999)
    endTimeInternal = calendar.timeInMillis

    return Goal(this, startTimeInternal, endTimeInternal)
}

private val TYPE_RESOURCE_MAPPING = mapOf(
    GoalDefinition.Type.DURATION to R.string.duration,
    GoalDefinition.Type.DISTANCE to R.string.distance,
    GoalDefinition.Type.WORKOUTS to R.string.goal_type_amounts,
    GoalDefinition.Type.ENERGY to R.string.energy
)

fun GoalDefinition.Type.toString(resources: Resources): String = resources.getString(TYPE_RESOURCE_MAPPING.getValue(this))

fun GoalDefinition.Type.getAmountString(context: Context, amount: Double, measurementUnit: MeasurementUnit): String {
    return when (this) {
        GoalDefinition.Type.DURATION -> {
            val a = amount.toInt()
            String.format(Locale.US, "%d:%02d:%02d", a / 3600, a % 3600 / 60, a % 60)
        }
        GoalDefinition.Type.DISTANCE -> String.format(
            "%s %s",
            TextFormatter.formatDistance(measurementUnit.toDistanceUnit(amount)),
            context.getString(measurementUnit.distanceUnit)
        )
        GoalDefinition.Type.WORKOUTS -> String.format(Locale.US, "%d", amount.toInt())
        GoalDefinition.Type.ENERGY -> String.format(Locale.US, "%d kcal", amount.toInt())
    }
}

private val PERIOD_RESOURCE_MAPPING = mapOf(
    GoalDefinition.Period.WEEKLY to R.string.weekly,
    GoalDefinition.Period.MONTHLY to R.string.monthly,
    GoalDefinition.Period.CUSTOM to R.string.goal_period_custom
)

fun GoalDefinition.Period.toString(resources: Resources): String = resources.getString(PERIOD_RESOURCE_MAPPING.getValue(this))

@WorkerThread
fun createDefaultGoalDefinition(
    workoutHeaderController: WorkoutHeaderController,
    username: String,
    calendarProvider: CalendarProvider
): GoalDefinition {
    Timber.d("creating default goal definition")
    var goalDefinition = GoalDefinition(
        userName = username,
        name = null,
        type = GoalDefinition.Type.DURATION,
        period = GoalDefinition.Period.WEEKLY,
        startTime = 0L,
        endTime = 0L,
        target = 5400
    )

    val oldestWorkout = workoutHeaderController.findOldestNotDeletedWorkout(username, releasedUTCMilli)
    if (oldestWorkout != null) {
        // the user has some workouts, mark the goal start time as start time of that
        // week
        val oldestWorkoutStartTime = oldestWorkout.startTime
        goalDefinition = goalDefinition.copy(
            startTime = calculateStartTime(goalDefinition.period, oldestWorkoutStartTime, calendarProvider)
        )

        // the goal target is calculated as 75% of the average duration for the
        // previous five weeks, then floored to 15 minutes
        val beginningOfThisWeek = beginningOfWeek(System.currentTimeMillis(), calendarProvider)
        val beginningOfFiveWeeksAgo =
            beginningOfThisWeek - DateUtils.WEEK_IN_MILLIS * WEEKS_TO_CHECK_FOR_DEFAULT_GOALS
        val beginningOfTheWeekForOldestWorkout = beginningOfWeek(oldestWorkoutStartTime, calendarProvider)
        val timeToStartChecking = Math.max(beginningOfFiveWeeksAgo, beginningOfTheWeekForOldestWorkout)
        val workoutHeaders = workoutHeaderController.findNotDeletedWorkoutHeaders(
            username,
            null,
            timeToStartChecking,
            beginningOfThisWeek - 1L
        )
        var totalDuration = 0.0
        val workoutCount = workoutHeaders.size
        for (i in 0 until workoutCount) {
            totalDuration += workoutHeaders[i].totalTime
        }
        var duration =
            (0.75 * totalDuration / ((beginningOfThisWeek - timeToStartChecking) / DateUtils.WEEK_IN_MILLIS)).toInt()
        duration -= duration % 900
        val goalTarget = Math.max(duration, goalDefinition.target)
        goalDefinition = goalDefinition.copy(target = goalTarget)
    } else {
        // the user has no workout, let's start from this week
        goalDefinition = goalDefinition.copy(startTime = calculateDefaultStartTime(goalDefinition.period, calendarProvider))
        // We don't store the goal definition because the user might log in later
        // and we don't want him to have the default but try to figure it out
        // from her workouts
    }
    return goalDefinition
}

private fun beginningOfWeek(timestamp: Long, calendarProvider: CalendarProvider): Long {
    val calendar = calendarProvider.getCalendar()
    calendar.timeInMillis = timestamp
    calendar.set(Calendar.HOUR_OF_DAY, 0)
    calendar.set(Calendar.MINUTE, 0)
    calendar.set(Calendar.SECOND, 0)
    calendar.set(Calendar.MILLISECOND, 0)
    calendar.set(Calendar.DAY_OF_WEEK, calendar.firstDayOfWeek)
    return calendar.timeInMillis
}

fun GoalDefinition.updateId(id: Long): GoalDefinition = this.copy(id = id)
