package com.stt.android.mapping

import android.annotation.SuppressLint
import android.content.Context
import android.content.res.Resources
import android.text.format.DateFormat
import android.text.format.DateUtils
import androidx.annotation.DrawableRes
import androidx.annotation.IntRange
import androidx.annotation.StringRes
import androidx.annotation.VisibleForTesting
import com.amersports.formatter.Failure
import com.amersports.formatter.Formatter
import com.amersports.formatter.FormattingOptions
import com.amersports.formatter.Result
import com.amersports.formatter.SourceUnit
import com.amersports.formatter.Success
import com.amersports.formatter.Unit
import com.amersports.formatter.UnitType
import com.soy.android.infomodel.activitymodes.ValueFormat
import com.soy.android.infomodel.activitymodes.ValueFormatStyle
import com.stt.android.R
import com.stt.android.TestOpen
import com.stt.android.compose.format.ComposeFormatter
import com.stt.android.controllers.UserSettingsController
import com.stt.android.data.TimeUtils
import com.stt.android.domain.advancedlaps.LapsTableRow
import com.stt.android.domain.advancedlaps.LapsTableType
import com.stt.android.domain.advancedlaps.SwimStyle
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.domain.workouts.extensions.AltitudeSetting
import com.stt.android.domain.workouts.extensions.DiveExtension
import com.stt.android.extensions.stringRes
import com.stt.android.infomodel.ItemDescriptionUtils
import com.stt.android.infomodel.SummaryItem
import com.stt.android.ui.utils.TextFormatter
import com.stt.android.utils.STTConstants
import com.stt.android.utils.toDaysPartSafe
import com.stt.android.utils.toHoursPartSafe
import com.stt.android.utils.toMinutesPartSafe
import com.stt.android.workouts.details.values.WorkoutFeeling
import com.stt.android.workouts.details.values.WorkoutValue
import timber.log.Timber
import java.time.Duration
import java.time.format.DateTimeFormatter
import java.time.format.FormatStyle
import java.util.Locale
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.math.roundToInt
import kotlin.math.roundToLong
import com.stt.android.core.R as CR

@Singleton
@TestOpen
class InfoModelFormatter
@Inject constructor(
    private val simFormatter: Formatter,
    val context: Context,
    private val userSettingsController: UserSettingsController
) : ComposeFormatter {

    override val localTimeFormatter: DateTimeFormatter =
        DateTimeFormatter.ofLocalizedTime(FormatStyle.SHORT)

    val unit: MeasurementUnit
        get() = userSettingsController.settings.measurementUnit

    val res: Resources
        get() = context.resources

    // caches to avoid looking for same identifiers more than once
    private val stringResIdCache = mutableMapOf<String, Int>()
    private val drawableResIdCache = mutableMapOf<String, Int>()

    data class WorkoutValueFormatResult(
        val value: String? = null,
        @StringRes val unit: Int? = null,
        @DrawableRes val drawableResId: Int? = null
    )

    private var formattingOptions: FormattingOptions = getFormattingOptions()

    fun refreshFormattingOptions() {
        formattingOptions = getFormattingOptions()
    }

    private fun getFormattingOptions() =
        FormattingOptions(
            unitType = if (unit == MeasurementUnit.IMPERIAL) UnitType.IMPERIAL else UnitType.METRIC,
            use24HClock = DateFormat.is24HourFormat(context)
        )

    override fun format(
        formatName: String,
        value: Number,
        withStyle: Boolean
    ): kotlin.Result<ComposeFormatter.FormatResult> {
        return formatValue(formatName, value, withStyle).toKotlinResultOrError {
            "Failure formatting $value, formatName=$formatName, withStyle=$withStyle"
        }
    }

    /**
     * Input for simFormatter is value in SIM unit (which usually means SI unit).
     * Other cases not handled by simFormatter are handled in [formatValueInternal]
     */
    fun formatValueAsString(summaryItem: SummaryItem, rowValue: Any): String {
        val workoutValue = formatValue(summaryItem, rowValue)
        val value = workoutValue.value.orEmpty()
        val unit = workoutValue.getUnitLabel(context)
        return context.getString(R.string.value_unit, value, unit).trim()
    }

    /**
     * Input for simFormatter is value in SIM unit (which usually means SI unit).
     */
    fun formatValueAsString(
        formatName: String,
        value: Any,
        withStyle: Boolean,
        sourceUnit: SourceUnit = SourceUnit.SI,
        forceRangeUnitOutput: Boolean = false
    ): String {
        if (value !is Number) {
            // SIM formatter cannot format this
            return value.toString()
        }
        return when (
            val result = formatValue(formatName, value, withStyle, sourceUnit, forceRangeUnitOutput)
        ) {
            is Success -> {
                val formattedValue = result.value
                val unit = getUnitResId(result.unit)?.let { context.getString(it) } ?: ""
                context.getString(R.string.value_unit, formattedValue, unit).trim()
            }

            is Failure -> {
                Timber.w("Failure at formatting $formatName with value $value ($result)")
                ""
            }
        }
    }

    fun formatValue(
        formatName: String,
        value: Number,
        withStyle: Boolean,
        sourceUnit: SourceUnit = SourceUnit.SI,
        forceRangeUnitOutput: Boolean = false
    ): Result =
        try {
            // TODO: Remove this if statement once simFormatter can convert joules to kN/m.
            if (formatName == "StiffnessTwodigits") {
                simFormatter.formatWithStyle(
                    formatName,
                    value.toDouble() / 1000,
                    formattingOptions.copy(
                        sourceUnit = sourceUnit,
                        forceRangeUnitOutput = false
                    )
                )
            } else if (withStyle) {
                simFormatter.formatWithStyle(
                    formatName,
                    value.toDouble(),
                    formattingOptions.copy(
                        sourceUnit = sourceUnit,
                        forceRangeUnitOutput = forceRangeUnitOutput
                    )
                )
            } else {
                simFormatter.format(
                    formatName,
                    value.toDouble(),
                    formattingOptions.copy(
                        sourceUnit = sourceUnit,
                        forceRangeUnitOutput = forceRangeUnitOutput
                    )
                )
            }
        } catch (e: Exception) {
            val reason = "Error calling direct formatValue($formatName, $value)"
            Timber.w(e, reason)
            Failure(reason)
        }

    /**
     * Input for simFormatter is value in SIM unit (which usually means SI unit).
     * Other cases not handled by simFormatter are handled in [formatValueInternal]
     */
    @JvmOverloads
    fun formatValue(
        summaryItem: SummaryItem,
        value: Any,
        forceUnitType: UnitType? = null,
        sourceUnit: SourceUnit = SourceUnit.SI,
        forceRangeUnitOutput: Boolean = false
    ): WorkoutValue {
        return if (summaryItem.valueFormat == ValueFormat.NONE || value !is Number) {
            // sim formatter cannot handle formatting of this value
            formatValueInternal(summaryItem, value)
        } else {
            val result: Result = try {
                if (summaryItem.valueFormatStyle == ValueFormatStyle.NONE) {
                    simFormatter.format(
                        summaryItem.valueFormat.formatName,
                        value.toDouble(),
                        formattingOptions.copy(
                            unitType = forceUnitType ?: formattingOptions.unitType,
                            sourceUnit = sourceUnit,
                            forceRangeUnitOutput = forceRangeUnitOutput
                        )
                    )
                } else {
                    val formatNameWithStyle = summaryItem.valueFormat.formatName +
                        summaryItem.valueFormatStyle.formatStyleName
                    simFormatter.formatWithStyle(
                        formatNameWithStyle,
                        value.toDouble(),
                        formattingOptions.copy(
                            unitType = forceUnitType ?: formattingOptions.unitType,
                            sourceUnit = sourceUnit,
                            forceRangeUnitOutput = forceRangeUnitOutput
                        )
                    )
                }
            } catch (e: Exception) {
                // Formatter may throw Exception("Range not identified for ...") for large values
                Failure("Formatting failed with $e")
            }

            if (result is Success) {
                WorkoutValueFormatResult(
                    value = result.value,
                    unit = getUnitResId(result.unit)
                )
            } else {
                Timber.w("Failure at formatting $summaryItem with value $value ($result)")
                formatValueInternal(summaryItem, value)
            }
        }.let { formatResult ->
            val descriptions = ItemDescriptionUtils.getDescriptionForItem(summaryItem)
            getStaticWorkoutValueInfo(res, summaryItem).copy(
                value = formatResult.value,
                unit = formatResult.unit,
                drawableResId = formatResult.drawableResId,
                descriptionTitleResId = getStringResId(descriptions?.titleResId),
                descriptionSubtitleResId = getStringResId(descriptions?.subtitleResId),
                descriptionTextResId = getStringResId(descriptions?.textResId),
                descriptionImageResId = getDrawableResId(descriptions?.imageResId),
                descriptionUrl = descriptions?.url,
                urlText = getStringResId(descriptions?.urlText),
                extraInfoTextResId = getStringResId(descriptions?.secondaryTextResId)
            )
        }
    }

    fun formatEstimatedRouteDuration(seconds: Long): String {
        var res = simFormatter.durationFixedNoLeadingZero(seconds.toDouble(), formattingOptions)
        return if (res is Success) {
            res.value
        } else {
            // DurationFixedNoLeadingZero fails for durations longer than 100 hours.
            // Try with leading zero.
            res = simFormatter.durationFixed(seconds.toDouble(), formattingOptions)
            if (res is Success) {
                res.value
            } else {
                // Fall back to fail-safe formatting if SIM formatter fails
                TimeUtils.getDurationStringAsHmm(seconds)
            }
        }
    }

    fun formatLapTitleAbbr(
        lapsTableType: LapsTableType?,
        lap: LapsTableRow?,
        autoLapLength: Float?
    ): String? {
        return when (lapsTableType) {
            LapsTableType.ONE_KM_AUTO_LAP,
            LapsTableType.FIVE_KM_AUTO_LAP,
            LapsTableType.TEN_KM_AUTO_LAP,
            LapsTableType.ONE_MILE_AUTO_LAP,
            LapsTableType.FIVE_MILE_AUTO_LAP,
            LapsTableType.TEN_MILE_AUTO_LAP,
            LapsTableType.DISTANCE_AUTO_LAP ->
                formatDistanceAutoLapTitle(autoLapLength?.toDouble(), lapsTableType)

            LapsTableType.DURATION_AUTO_LAP ->
                formatDurationAutoLapTitle(autoLapLength?.toDouble(), lapsTableType)

            LapsTableType.MANUAL -> context.getString(CR.string.laps_table_type_manual_abbr)

            LapsTableType.INTERVAL,
            LapsTableType.DOWNHILL,
            LapsTableType.DIVE -> context.getString(lapsTableType.stringRes())

            else -> null
        }
    }

    fun formatDistanceAutoLapTitle(distance: Double?, type: LapsTableType): String {
        val res = distance?.let { simFormatter.distanceFourdigits(distance, formattingOptions) }
        return if (res is Success) {
            context.getString(
                type.stringRes(),
                res.valueTrimTrailingZeros,
                getUnitResId(res.unit)?.let { context.getString(it) } ?: ""
            )
        } else {
            context.getString(CR.string.laps_table_type_autolap_fallback)
        }
    }

    fun formatDurationAutoLapTitle(duration: Double?, type: LapsTableType): String {
        val result = duration?.let { formatDurationNoDecimals(it) }
        return if (result is Success) {
            context.getString(
                type.stringRes(),
                result.value
            )
        } else {
            context.getString(CR.string.laps_table_type_autolap_fallback)
        }
    }

    override fun formatDistance(distance: Double): kotlin.Result<ComposeFormatter.FormatResult> {
        return simFormatter.distanceAccurate(distance, formattingOptions).toKotlinResultOrError {
            "Error formatting distance: $distance"
        }
    }

    override fun formatDuration(duration: Double): kotlin.Result<ComposeFormatter.FormatResult> {
        return formatDurationNoDecimals(duration).toKotlinResultOrError {
            "Error formatting duration: $duration"
        }
    }

    fun formatDurationNoDecimals(duration: Double): Result =
        simFormatter.durationNodecimal(duration, formattingOptions)

    fun durationFourdigitsFixed(duration: Double): Result =
        simFormatter.durationFourdigitsFixed(duration, formattingOptions)

    fun formatDurationHumane(duration: Double): Result =
        simFormatter.durationHumane(duration, formattingOptions)

    fun formatAccumulatedTotalDistance(
        distance: Double,
        usesNauticalUnits: Boolean,
        isSwimming: Boolean = false,
        fourDigitsRegularDistance: Boolean = false
    ): String {
        val result = when {
            usesNauticalUnits -> simFormatter.nauticalDistanceFourdigits(
                distance,
                formattingOptions
            )

            isSwimming -> simFormatter.swimDistanceFourdigits(distance, formattingOptions)
            fourDigitsRegularDistance -> simFormatter.distanceFourdigits(
                distance,
                formattingOptions
            )

            else -> simFormatter.distanceAccumulated(distance, formattingOptions)
        }
        return if (result is Success) {
            result.value
        } else {
            // Fall back to legacy formatting if SIM formatter fails
            when {
                usesNauticalUnits -> TextFormatter.formatDistance(
                    unit.fromMetersToNauticalMile(
                        distance
                    )
                )

                isSwimming -> TextFormatter.formatShortDistance(
                    unit.toShortDistanceUnit(distance),
                    unit
                )

                else -> TextFormatter.formatDistance(unit.toDistanceUnit(distance))
            }
        }
    }

    fun formatAccumulatedTotalDuration(durationSeconds: Double): String {
        // Formatting drops seconds, so to get rounding up to nearest minute work
        // do the rounding before formatting
        val secondsInCurrentMinute = durationSeconds % 60
        val roundedDurationSeconds = if (secondsInCurrentMinute >= 30.0) {
            durationSeconds + 60 - secondsInCurrentMinute
        } else {
            durationSeconds
        }

        val result = simFormatter.durationAccumulated(roundedDurationSeconds, formattingOptions)
        return if (result is Success) {
            result.value
        } else {
            // Fall back to showing hours only if SIM formatter fails
            Duration.ofSeconds(roundedDurationSeconds.roundToLong()).toHours().toString()
        }
    }

    fun formatDurationAsPace(durationSeconds: Double): String =
        (simFormatter.durationFourdigitsFixed(durationSeconds, formattingOptions) as? Success)
            ?.value
            ?: DateUtils.formatElapsedTime(durationSeconds.roundToLong())

    // We can't use SummaryItem.TRAININGSTRESSSCORE as it uses CountFivedigits which has max value of 99999.5
    // CountSixdigits max is 999999.5
    fun formatTss(tss: Float): String? =
        (formatValue("CountSixdigits", tss, true) as? Success)?.value

    fun formatDuration(@IntRange(from = 0) seconds: Long): String {
        val duration = Duration.ofSeconds(seconds)
        val hours = (duration.toDaysPartSafe() * 24) + duration.toHoursPartSafe()
        val minutes = duration.toMinutesPartSafe()
        val hourUnit = getString(CR.string.hour)
        val minuteUnit = getString(CR.string.minute)
        return when {
            hours == 0L && minutes == 0 -> "0$hourUnit"
            hours == 0L -> "$minutes $minuteUnit"
            minutes == 0 -> "$hours $hourUnit"
            else -> "$hours $hourUnit $minutes $minuteUnit"
        }
    }

    fun getString(@StringRes resId: Int): String {
        return context.getString(resId)
    }

    fun convertFromSItoRangeUnit(formatStyle: String, value: Number): Double? {
        val result = formatValue(formatStyle, value, true)
        return if (result is Success) {
            result.valueInRangeUnit
        } else {
            Timber.i("Failure converting value: $value in style: $formatStyle for reason: ${(result as? Failure)?.reason}")
            null
        }
    }

    private val Success.valueTrimTrailingZeros
        get() = value.trimEnd { it == '0' }.trimEnd { it == '.' }

    @VisibleForTesting
    internal fun formatValueInternal(
        summaryItem: SummaryItem,
        value: Any
    ): WorkoutValueFormatResult {
        return when (summaryItem) {
            SummaryItem.STEPS,
            SummaryItem.ZONESENSEBASELINE,
            SummaryItem.ZONESENSECUMULATIVEBASELINE -> toStringFormat(value)
            SummaryItem.FEELING -> {
                if (value is Number && value.toInt() in 0..5) {
                    val feeling = WorkoutFeeling.getFeelingByValue(value.toInt())
                    WorkoutValueFormatResult(drawableResId = feeling.resId)
                } else {
                    Timber.w("Cannot format feeling value: $value")
                    toStringFormat(value)
                }
            }
            // todo not supported yet
            SummaryItem.MOVETYPE -> toStringFormat(value)
            SummaryItem.DIVEMODE -> toStringFormat(value)
            // todo not supported yet
            SummaryItem.ALGORITHMLOCK -> {
                if (value == true) {
                    WorkoutValueFormatResult(drawableResId = R.drawable.icon_lock_fill)
                } else {
                    Timber.w("Cannot format algorithmLock value: $value")
                    toStringFormat(value)
                }
            }

            SummaryItem.DIVECNS -> {
                if (value is Number) {
                    val perc = TextFormatter.formatPercentage(value.toDouble() * 100)
                    WorkoutValueFormatResult(
                        value = perc,
                        unit = getUnitResId(Unit.PERCENT)
                    )
                } else {
                    Timber.w("Cannot format diveCNS value: $value")
                    toStringFormat(value)
                }
            }

            SummaryItem.DIVEOTU -> {
                if (value is Number) {
                    val otu = value.toFloat().roundToInt().toString()
                    WorkoutValueFormatResult(value = otu)
                } else {
                    Timber.w("Cannot format diveOTU value: $value")
                    toStringFormat(value)
                }
            }

            SummaryItem.DIVEGASES -> {
                toStringFormat(value)
            }

            SummaryItem.PERSONAL -> {
                if (value is Number) {
                    val personal = TextFormatter.formatPersonalSetting(value.toInt())
                    WorkoutValueFormatResult(value = personal)
                } else {
                    Timber.w("Cannot format personal value: $value")
                    toStringFormat(value)
                }
            }

            SummaryItem.GRADIENTFACTORS -> {
                if (value is Pair<*, *> &&
                    value.first is Number &&
                    value.second is Number
                ) {
                    val minGF = (value.first as Number).toFloat()
                    val maxGF = (value.second as Number).toFloat()
                    val minGradient = (minGF * 100).roundToInt()
                    val maxGradient = (maxGF * 100).roundToInt()
                    val gradientFactors = res.getString(
                        R.string.workout_values_value_dive_gradient_factors,
                        minGradient,
                        maxGradient
                    )
                    WorkoutValueFormatResult(value = gradientFactors)
                } else {
                    Timber.w("Cannot format gradientFactors value: $value")
                    toStringFormat(value)
                }
            }

            SummaryItem.ALTITUDESETTING -> {
                if (value is AltitudeSetting) {
                    val altitudeValue = TextFormatter.formatAltitudeSetting(value, unit, res)
                    WorkoutValueFormatResult(
                        value = altitudeValue,
                        unit = unit.getAltitudeSettingUnit(value)
                    )
                } else {
                    Timber.w("Cannot format altitudeSetting value: $value")
                    toStringFormat(value)
                }
            }

            SummaryItem.ALGORITHM -> {
                val algorithm = when (value.toString()) {
                    DiveExtension.ALGORITHM_BUHLMANN ->
                        res.getString(R.string.algorithm_buhlmann)

                    DiveExtension.ALGORITHM_SUUNTO_FUSED_RGBM ->
                        res.getString(R.string.algorithm_suunto_fused_rgbm)

                    DiveExtension.ALGORITHM_SUUNTO_FUSED_RGBM_2 ->
                        res.getString(R.string.algorithm_suunto_fused_rgbm_2)

                    else -> value.toString()
                }
                WorkoutValueFormatResult(value = algorithm)
            }

            SummaryItem.SWIMSTYLE -> {
                val localized = try {
                    when (SwimStyle.fromValue(value.toString())) {
                        SwimStyle.OTHER -> res.getString(CR.string.swimstyle_other)
                        SwimStyle.FREE,
                        SwimStyle.FREE_LEGACY -> res.getString(CR.string.swimstyle_freestyle)

                        SwimStyle.BUTTERFLY -> res.getString(CR.string.swimstyle_butterfly)
                        SwimStyle.BREAST,
                        SwimStyle.BREAST_LEGACY -> res.getString(CR.string.swimstyle_breaststroke)

                        SwimStyle.BACK,
                        SwimStyle.BACK_LEGACY -> res.getString(CR.string.swimstyle_backstroke)

                        SwimStyle.DRILL -> res.getString(CR.string.swimstyle_drill)
                    }
                } catch (e: IllegalArgumentException) {
                    value.toString()
                }
                WorkoutValueFormatResult(value = localized)
            }

            SummaryItem.BREATHINGRATE -> WorkoutValueFormatResult(
                value = value.toString(),
                unit = getUnitResId(Unit.SPM)
            )

            SummaryItem.FREESTYLEPERCENT,
            SummaryItem.BREASTSTROKEPERCENT -> WorkoutValueFormatResult(
                value = value.toString(),
                unit = getUnitResId(Unit.PERCENT)
            )

            SummaryItem.FREESTYLEPITCHANGLE,
            SummaryItem.MAXFREESTYLEBREATHANGLE,
            SummaryItem.AVGFREESTYLEBREATHANGLE,
            SummaryItem.AVGBREASTSTROKEBREATHANGLE,
            SummaryItem.MAXBREASTSTROKEBREATHANGLE,
            SummaryItem.BREASTSTROKEHEADANGLE -> WorkoutValueFormatResult(
                value = value.toString(),
                unit = CR.string.degree
            )
            SummaryItem.AVGGROUNDCONTACTBALANCE -> {
                if (value is Pair<*, *> &&
                    value.first is Number &&
                    value.second is Number
                ) {
                    fun Float.format(): String = "%.1f".format(this)
                    val leftGroundContactBalance = (value.first as Number).toFloat()
                    val rightGroundContactBalance = (value.second as Number).toFloat()
                    WorkoutValueFormatResult(value = "${leftGroundContactBalance.format()}% - ${rightGroundContactBalance.format()}%")
                } else {
                    Timber.w("Cannot format gradientFactors value: $value")
                    toStringFormat(value)
                }
            }
            // todo not supported yet
            SummaryItem.TYPE -> toStringFormat(value)
            SummaryItem.AVGSKIPSRATE,
            SummaryItem.MAXAVGSKIPSRATE -> WorkoutValueFormatResult(
                value = value.toString(),
                unit = getUnitResId(Unit.SPM)
            )
            SummaryItem.AVGSKIPSPERROUND -> WorkoutValueFormatResult(
                value = if (value is Number) value.toInt().toString() else value.toString(),
                unit = CR.string.round
            )
            SummaryItem.ROUNDS,
            SummaryItem.ESTIMATEDFLOORSCLIMBED,
            SummaryItem.MAXCONSECUTIVESKIPS -> toStringFormat(value)
            SummaryItem.NONE -> toStringFormat("")
            else -> {
                if (STTConstants.DEBUG) {
                    Timber.w("Internal formatter failed to format $summaryItem, value=$value")
                    // Instead of crashing, show warning symbol in debug builds to highlight
                    // formatting issues
                    toStringFormat("\u26a0\ufe0f")
                } else {
                    toStringFormat("")
                }
            }
        }
    }

    fun formatIntensityTarget(intensityTargetType: IntensityZoneType, value: Int): String {
        return when (intensityTargetType) {
            IntensityZoneType.SPEED -> String.format(Locale.US, "%.1f", value / 10f)
            IntensityZoneType.PACE -> formatDurationAsPace((value / 1000.0).roundToInt().toDouble()) // value's unit is millis
            else -> value.toString()
        }
    }

    fun formatDistanceFourdigits(meters: Double): kotlin.Result<ComposeFormatter.FormatResult> {
        return simFormatter.distanceFourdigits(meters, formattingOptions).toKotlinResultOrError {
            "Error formatting distance: $meters meters"
        }
    }

    enum class IntensityZoneType {
        HEART_RATE,
        PACE,
        SPEED,
        CADENCE,
        POWER
    }

    private fun toStringFormat(value: Any) = WorkoutValueFormatResult(value = value.toString())

    @SuppressLint("DiscouragedApi")
    @StringRes
    private fun getStringResId(resourceName: String?): Int? {
        if (resourceName == null) {
            return null
        }
        return stringResIdCache.getOrPut(resourceName) {
            res.getIdentifier(resourceName, "string", context.packageName)
        }.let { if (it == 0) null else it }
    }

    @SuppressLint("DiscouragedApi")
    @DrawableRes
    private fun getDrawableResId(resourceName: String?): Int? {
        if (resourceName == null) {
            return null
        }
        return drawableResIdCache.getOrPut(resourceName) {
            res.getIdentifier(resourceName, "drawable", context.packageName)
        }.let { if (it == 0) null else it }
    }

    companion object {

        @StringRes
        fun getUnitResId(unit: Unit): Int? {
            return when (unit) {
                Unit.M_PER_S -> CR.string.TXT_M_SEC
                Unit.S_PER_100M -> CR.string.TXT_PER_100M
                Unit.S_PER_100YD -> CR.string.TXT_PER_100YD
                Unit.KM_PER_H -> CR.string.TXT_KMH
                Unit.MI_PER_H -> CR.string.TXT_MPH
                Unit.HZ -> CR.string.TXT_HZ
                Unit.RPM -> CR.string.TXT_RPM
                Unit.SCALAR -> null
                Unit.M -> CR.string.TXT_M
                Unit.KM -> CR.string.TXT_KM
                Unit.YD -> CR.string.TXT_YD
                Unit.MI -> CR.string.TXT_MI
                // fixme A bug in SIM lib returning this unit for duration. Return R.string.seconds when fixed.
                Unit.S -> null // CR.string.TXT_S_SECONDS
                Unit.MS -> CR.string.TXT_MS
                Unit.H -> CR.string.TXT_H
                Unit.FT -> CR.string.TXT_FEET
                Unit.KFT -> CR.string.TXT_KFT_POSTFIX
                Unit.S_PER_KM -> CR.string.TXT_PER_KM
                Unit.S_PER_MI -> CR.string.TXT_PER_MILE
                Unit.J -> CR.string.TXT_J_JOULE
                Unit.KNM -> CR.string.TXT_KNM
                Unit.RAD -> CR.string.TXT_RAD
                Unit.DEG -> CR.string.TXT_DEGREE_SYMBOL
                Unit.CM -> CR.string.TXT_CM
                Unit.IN -> CR.string.TXT_IN
                Unit.MIL -> CR.string.TXT_MILS
                Unit.KG -> CR.string.TXT_KG_POSTFIX
                Unit.LB -> CR.string.TXT_LB_POSTFIX
                Unit.SPM -> CR.string.TXT_PER_MIN
                Unit.BPM -> CR.string.TXT_BPM
                Unit.K -> CR.string.TXT_K
                Unit.C -> CR.string.TXT_CELSIUS
                Unit.F -> CR.string.TXT_FAHRENHEIT
                Unit.S_PER_500M -> CR.string.TXT_PER_500M
                Unit.NMI -> CR.string.TXT_NMI
                Unit.KCAL -> CR.string.TXT_KCAL
                Unit.PERCENT -> CR.string.TXT_PERCENT
                Unit.M_PER_H -> CR.string.TXT_M_HOUR
                Unit.FT_PER_HOUR -> CR.string.TXT_FT_HOUR
                Unit.KN -> CR.string.TXT_KN
                Unit.M_PER_MIN -> CR.string.TXT_M_MIN
                Unit.FT_PER_MIN -> CR.string.TXT_FT_MIN
                Unit.PA -> CR.string.TXT_PA_POSTFIX
                Unit.HPA -> CR.string.TXT_HPA_POSTFIX
                Unit.INHG -> CR.string.TXT_INHG_POSTFIX
                Unit.W -> CR.string.TXT_W
                Unit.KPA -> CR.string.TXT_KPA_POSTFIX
                Unit.BAR -> CR.string.bar
                Unit.PSI -> CR.string.psi
                Unit.HOUR -> CR.string.hour
                Unit.MIN -> CR.string.minute
                Unit.M3_PER_S -> CR.string.cubic_meter_per_second
                Unit.L_PER_MIN -> CR.string.liters_per_minute
                Unit.FT3_PER_MIN -> CR.string.cubic_feet_per_minute
                Unit.DAY -> CR.string.days
                Unit.AM -> null
                Unit.PM -> null
                Unit.M3 -> CR.string.cubic_meter
                Unit.L -> CR.string.liters
                Unit.G -> CR.string.TXT_G_POSTFIX
                Unit.OZ -> CR.string.TXT_OZ_POSTFIX
                Unit.NONE -> null
            }
        }

        fun getStaticWorkoutValueInfo(
            res: Resources,
            summaryItem: SummaryItem
        ): WorkoutValue {
            return when (summaryItem) {
                SummaryItem.DURATION -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_duration)
                )

                SummaryItem.TOTALTIME -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_total_time)
                )

                SummaryItem.PAUSETIME -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_pause_time)
                )

                SummaryItem.MOVINGTIME -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_moving_time)
                )

                SummaryItem.RESTTIME -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_rest_time)
                )

                SummaryItem.DISTANCE,
                SummaryItem.DIVEDISTANCE,
                SummaryItem.SWIMDISTANCE -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_distance)
                )

                SummaryItem.AVGPACE -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(R.string.workout_values_headline_avg_pace)
                )

                SummaryItem.MOVINGPACE -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(R.string.workout_values_headline_moving_pace)
                )

                SummaryItem.AVGHEARTRATE -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(R.string.workout_values_headline_avg_heart_rate)
                )

                SummaryItem.MINHEARTRATE -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(R.string.workout_values_headline_min_heart_rate)
                )

                SummaryItem.MAXHEARTRATE -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(R.string.workout_values_headline_max_heart_rate)
                )

                SummaryItem.ENERGY -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_energy)
                )

                SummaryItem.RECOVERYTIME -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_recoveryTime)
                )

                SummaryItem.PTE -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_pte)
                )

                SummaryItem.PERFORMANCELEVEL -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_performance)
                )

                SummaryItem.AVGSPEED -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(R.string.workout_values_headline_avg_speed)
                )

                SummaryItem.MOVINGSPEED -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(R.string.workout_values_headline_moving_speed)
                )

                SummaryItem.AVGVERTICALSPEED -> WorkoutValue(
                    item = summaryItem,
                    label = "" // todo not supported yet
                )
                // AVGCADENCE in SportsTracker is cadence from stepcount
                // fixme should we define a different summaryItem for this? then it would need to be filtered out by product
                SummaryItem.AVGCADENCE -> WorkoutValue(
                    item = summaryItem,
                    label = if (res.getBoolean(R.bool.sportsTrackerFlavorSpecific)) {
                        res.getString(R.string.workout_values_headline_step_rate)
                    } else {
                        res.getString(R.string.workout_values_headline_avg_cadence)
                    }
                )

                SummaryItem.STEPS -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(R.string.workout_values_headline_step_count)
                )

                SummaryItem.AVGSTEPCADENCE -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(R.string.workout_values_headline_step_cadence)
                )

                SummaryItem.MAXSTEPCADENCE -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(R.string.workout_values_headline_max_step_cadence)
                )

                SummaryItem.ASCENTALTITUDE -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.all_ascent)
                )
                // fixme we are not supporting skidescent here yet, figure out if we need to
                SummaryItem.DESCENTALTITUDE -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.all_descent)
                )

                SummaryItem.HIGHALTITUDE -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_high_altitude)
                )

                SummaryItem.LOWALTITUDE -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_low_altitude)
                )

                SummaryItem.PEAKVERTICALSPEED30S -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_peak_vertical_speed_30s)
                )

                SummaryItem.PEAKVERTICALSPEED1M -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_peak_vertical_speed_1m)
                )

                SummaryItem.PEAKVERTICALSPEED3M -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_peak_vertical_speed_3m)
                )

                SummaryItem.PEAKVERTICALSPEED5M -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_peak_vertical_speed_5m)
                )

                SummaryItem.AVGTEMPERATURE -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_avg_temperature)
                )

                SummaryItem.MAXTEMPERATURE -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_max_temperature)
                )

                SummaryItem.PEAKEPOC -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_peak_epoc)
                )

                SummaryItem.FEELING -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_feeling)
                )

                SummaryItem.MOVETYPE -> WorkoutValue(
                    item = summaryItem,
                    label = "[MoveType]" // todo not supported yet
                )

                SummaryItem.CATCHFISH -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_catch_count)
                )

                SummaryItem.CATCHBIGGAME -> WorkoutValue(
                    item = summaryItem,
                    label = "[CatchBigGame]" // todo not supported yet
                )

                SummaryItem.CATCHSMALLGAME -> WorkoutValue(
                    item = summaryItem,
                    label = "[CatchSmallGame]" // todo not supported yet
                )

                SummaryItem.CATCHBIRD -> WorkoutValue(
                    item = summaryItem,
                    label = "[CatchBird]" // todo not supported yet
                )

                SummaryItem.CATCHSHOTCOUNT -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_shot_count)
                )

                SummaryItem.AVGPOWER -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_avg_power)
                )

                SummaryItem.AVGPOWERWITHZERO -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_avg_power_with_zero)
                )

                SummaryItem.MAXPOWER -> WorkoutValue(
                    item = summaryItem,
                    label = "[MaxPower]" // todo not supported yet
                )

                SummaryItem.PEAKPOWER30S -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_peak_power_30s)
                )

                SummaryItem.PEAKPOWER1M -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_peak_power_1m)
                )

                SummaryItem.PEAKPOWER3M -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_peak_power_3m)
                )

                SummaryItem.PEAKPOWER5M -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_peak_power_5m)
                )

                SummaryItem.AVGSWOLF -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_swolf)
                )

                SummaryItem.AVGSWIMSTROKERATE -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_swim_stroke_rate)
                )

                SummaryItem.AVGNAUTICALSPEED -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(R.string.workout_values_headline_avg_speed)
                )

                SummaryItem.MAXNAUTICALSPEED -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(R.string.workout_values_headline_max_speed)
                )

                SummaryItem.NAUTICALDISTANCE -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_nautical_distance)
                )

                SummaryItem.AVGSEALEVELPRESSURE -> WorkoutValue(
                    item = summaryItem,
                    label = "[AvgSeaLevelPressure]" // todo not supported yet
                )

                SummaryItem.MAXPACE -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(R.string.workout_values_headline_max_pace)
                )

                SummaryItem.PEAKPACE30S -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(R.string.workout_values_headline_peak_pace_30s)
                )

                SummaryItem.PEAKPACE1M -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(R.string.workout_values_headline_peak_pace_1m)
                )

                SummaryItem.PEAKPACE3M -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(R.string.workout_values_headline_peak_pace_3m)
                )

                SummaryItem.PEAKPACE5M -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(R.string.workout_values_headline_peak_pace_5m)
                )

                SummaryItem.MAXSPEED -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(R.string.workout_values_headline_max_speed)
                )

                SummaryItem.PEAKSPEED30S -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(R.string.workout_values_headline_peak_speed_30s)
                )

                SummaryItem.PEAKSPEED1M -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(R.string.workout_values_headline_peak_speed_1m)
                )

                SummaryItem.PEAKSPEED3M -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(R.string.workout_values_headline_peak_speed_3m)
                )

                SummaryItem.PEAKSPEED5M -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(R.string.workout_values_headline_peak_speed_5m)
                )

                SummaryItem.MAXDEPTH -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(R.string.workout_values_headline_max_depth)
                )

                SummaryItem.DIVETIME -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(R.string.workout_values_headline_dive_time)
                )

                SummaryItem.DIVETIMEMAX -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(R.string.workout_values_headline_dive_time_max)
                )

                SummaryItem.DIVEMODE -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(R.string.workout_values_headline_dive_mode)
                )

                SummaryItem.DIVENUMBERINSERIES -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(R.string.workout_values_headline_dive_num_in_series)
                )

                SummaryItem.DIVESURFACETIME -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(R.string.workout_values_headline_dive_surface_time)
                )

                SummaryItem.DIVERECOVERYTIME -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(R.string.workout_values_headline_dive_recovery_time)
                )

                SummaryItem.DIVEVISIBILITY -> WorkoutValue(
                    item = summaryItem,
                    label = "[DiveVisibility]" // todo not supported yet
                )

                SummaryItem.DIVEMAXDEPTHTEMPERATURE -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(R.string.workout_values_headline_dive_max_depth_temperature)
                )

                SummaryItem.SKIRUNCOUNT -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(R.string.workout_values_headline_ski_runs)
                )

                SummaryItem.SKIDISTANCE -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(R.string.workout_values_headline_ski_distance)
                )

                SummaryItem.SKITIME -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(R.string.workout_values_headline_ski_time)
                )

                SummaryItem.AVGSKISPEED -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(R.string.workout_values_headline_avg_ski_speed)
                )

                SummaryItem.MAXSKISPEED -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(R.string.workout_values_headline_max_ski_speed)
                )

                SummaryItem.ASCENTTIME -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_ascent_duration)
                )

                SummaryItem.DESCENTTIME -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_descent_duration)
                )

                SummaryItem.ESTVO2PEAK -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_vo2_max)
                )

                SummaryItem.ALGORITHMLOCK -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(R.string.workout_values_headline_dive_algorithm_lock)
                )

                SummaryItem.DIVECNS -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(R.string.workout_values_headline_dive_cns)
                )

                SummaryItem.DIVEOTU -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(R.string.workout_values_headline_dive_otu)
                )

                SummaryItem.AVGDEPTH -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(R.string.workout_values_headline_avg_depth)
                )

                SummaryItem.MINDEPTH -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(R.string.workout_values_headline_min_depth)
                )

                SummaryItem.DIVEGASES -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(R.string.workout_values_headline_dive_gas)
                )

                SummaryItem.PERSONAL -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(R.string.workout_values_headline_dive_personal_setting)
                )

                SummaryItem.GRADIENTFACTORS -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(R.string.workout_values_headline_dive_gradient_factors)
                )

                SummaryItem.ALTITUDESETTING -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(R.string.workout_values_headline_dive_altitude_setting)
                )

                SummaryItem.GASCONSUMPTION -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(R.string.workout_values_headline_dive_gas_consumption)
                )

                SummaryItem.ALGORITHM -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(R.string.workout_values_headline_dive_algorithm)
                )

                SummaryItem.AVGSWIMPACE -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(R.string.workout_values_headline_swimming_avg_pace)
                )

                SummaryItem.SWIMSTROKECOUNT -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(R.string.workout_values_headline_swim_stroke_count)
                )

                SummaryItem.SWIMSTROKEDISTANCE -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(R.string.workout_values_headline_swim_stroke_distance)
                )

                SummaryItem.CUMULATEDDISTANCE -> WorkoutValue(
                    item = summaryItem,
                    label = "[CumulatedDistance]" // todo not supported yet
                )

                SummaryItem.CUMULATEDDURATION -> WorkoutValue(
                    item = summaryItem,
                    label = "[CumulatedDuration]" // todo not supported yet
                )

                SummaryItem.CUMULATEDSWIMDISTANCE -> WorkoutValue(
                    item = summaryItem,
                    label = "[CumulatedSwimDistance]" // todo not supported yet
                )

                SummaryItem.SWIMSTYLE -> WorkoutValue(
                    item = summaryItem,
                    label = "[SwimStyle]" // todo not supported yet
                )

                SummaryItem.TYPE -> WorkoutValue(
                    item = summaryItem,
                    label = "[Type]" // todo not supported yet
                )

                SummaryItem.DIVEGASPRESSURE -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(R.string.workout_values_headline_dive_start_pressure)
                )

                SummaryItem.DIVEGASENDPRESSURE -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(R.string.workout_values_headline_dive_end_pressure)
                )

                SummaryItem.DIVEGASUSEDPRESSURE -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(R.string.workout_values_headline_dive_used_pressure)
                )

                SummaryItem.NONE -> WorkoutValue(
                    item = summaryItem,
                    label = ""
                )

                SummaryItem.TRAININGSTRESSSCORE -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(R.string.workout_values_headline_tss)
                )

                SummaryItem.NORMALIZEDPOWER -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(R.string.workout_values_headline_np)
                )

                SummaryItem.NORMALIZEDGRADEDPACE -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(R.string.workout_values_headline_ngp)
                )

                SummaryItem.CO2EMISSIONSREDUCED -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(R.string.workout_values_co2_saved)
                )

                SummaryItem.DIVEINWORKOUT -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(R.string.workout_values_headline_dive_in_workout)
                )

                SummaryItem.DOWNHILLDESCENT -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(R.string.workout_values_headline_downhill_descent)
                )

                SummaryItem.DOWNHILLDURATION -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(R.string.workout_values_headline_downhill_duration)
                )

                SummaryItem.DOWNHILLDISTANCE -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(R.string.workout_values_headline_downhill_distance)
                )

                SummaryItem.DOWNHILLCOUNT -> WorkoutValue(
                    item = summaryItem,
                    label = "[DownhillCount]"
                )

                SummaryItem.AVGDOWNHILLSPEED -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(R.string.workout_values_headline_downhill_avg_speed)
                )

                SummaryItem.MAXDOWNHILLSPEED -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(R.string.workout_values_headline_downhill_max_speed)
                )

                SummaryItem.AVGDOWNHILLGRADE -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(R.string.workout_values_downhill_grade_avg)
                )

                SummaryItem.MAXDOWNHILLGRADE -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(R.string.workout_values_downhill_grade_max)
                )

                SummaryItem.DOWNHILLMAXDESCENT -> WorkoutValue(
                    item = summaryItem,
                    label = "[DownhillMaxDescent]"
                )

                SummaryItem.DOWNHILLMAXLENGTH -> WorkoutValue(
                    item = summaryItem,
                    label = "[DownhillMaxLength]"
                )

                SummaryItem.REVOLUTIONCOUNT -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(R.string.workout_values_headline_revolution_count)
                )

                SummaryItem.ROWINGSTROKECOUNT -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.summary_item_title_rowing_stroke_count)
                )

                SummaryItem.SKIPCOUNT -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.summary_item_title_skip_count)
                )

                SummaryItem.MAXCADENCE -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(R.string.workout_values_headline_max_cadence)
                )

                SummaryItem.HRAEROBICTHRESHOLD -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_aerobic_hr_threshold)
                )

                SummaryItem.HRANAEROBICTHRESHOLD -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_anaerobic_hr_threshold)
                )

                SummaryItem.ZONESENSEBASELINE -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_zone_sense_baseline)
                )

                SummaryItem.ZONESENSECUMULATIVEBASELINE -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_zone_sense_cumulative_baseline)
                )

                SummaryItem.AEROBICPACETHRESHOLD -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_aerobic_pace_threshold)
                )

                SummaryItem.ANAEROBICPACETHRESHOLD -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_anaerobic_pace_threshold)
                )

                SummaryItem.AEROBICPOWERTHRESHOLD -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_aerobic_power_threshold)
                )

                SummaryItem.ANAEROBICPOWERTHRESHOLD -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_anaerobic_power_threshold)
                )

                SummaryItem.AEROBICDURATION -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_aerobic_duration)
                )

                SummaryItem.ANAEROBICDURATION -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_anaerobic_duration)
                )

                SummaryItem.VO2MAXDURATION -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_vo2_max_duration)
                )

                SummaryItem.BREATHINGRATE -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_breathing_rate)
                )

                SummaryItem.BREASTSTROKEDURATION -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_breaststroke_duration)
                )

                SummaryItem.BREASTSTROKEPERCENT -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_breaststroke_percent)
                )

                SummaryItem.BREASTSTROKEGLIDETIME -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_breaststroke_glide_time)
                )

                SummaryItem.MAXBREASTSTROKEBREATHANGLE -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_breaststroke_max_breath_angle)
                )

                SummaryItem.AVGBREASTSTROKEBREATHANGLE -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_breaststroke_avg_breath_angle)
                )

                SummaryItem.FREESTYLEDURATION -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_freestyle_duration)
                )

                SummaryItem.FREESTYLEPERCENT -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_freestyle_percent)
                )

                SummaryItem.AVGFREESTYLEBREATHANGLE -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_freestyle_avg_breath_angle)
                )

                SummaryItem.MAXFREESTYLEBREATHANGLE -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_freestyle_max_breath_angle)
                )

                SummaryItem.FREESTYLEPITCHANGLE -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_freestyle_head_angel)
                )

                SummaryItem.BREASTSTROKEHEADANGLE -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_breaststroke_head_angel)
                )

                SummaryItem.AVGSTRIDELENGTH -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_stride_length)
                )

                SummaryItem.AVGSTEPLENGTH -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_step_length)
                )

                SummaryItem.FATCONSUMPTION -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_fatConsumption)
                )
                SummaryItem.CARBOHYDRATECONSUMPTION -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_carbohydrateConsumption)
                )
                SummaryItem.AVGGROUNDCONTACTTIME -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_avgGroundContactTime)
                )
                SummaryItem.AVGVERTICALOSCILLATION -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_avgVerticalOscillation)
                )
                SummaryItem.AVGGROUNDCONTACTBALANCE -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_avgGroundContactBalance)
                )
                SummaryItem.CLIMBS -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_climbs)
                )
                SummaryItem.CLIMBSCATEGORY1 -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_climbs_category_1)
                )
                SummaryItem.CLIMBSCATEGORY2 -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_climbs_category_2)
                )
                SummaryItem.CLIMBSCATEGORY3 -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_climbs_category_3)
                )
                SummaryItem.CLIMBSCATEGORY4 -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_climbs_category_4)
                )
                SummaryItem.CLIMBSCATEGORYHC -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_climbs_category_hc)
                )
                SummaryItem.CLIMBASCENTCATEGORY1 -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_climb_ascent_category_1)
                )
                SummaryItem.CLIMBASCENTCATEGORY2 -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_climb_ascent_category_2)
                )
                SummaryItem.CLIMBASCENTCATEGORY3 -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_climb_ascent_category_3)
                )
                SummaryItem.CLIMBASCENTCATEGORY4 -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_climb_ascent_category_4)
                )
                SummaryItem.CLIMBASCENTCATEGORYHC -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_climb_ascent_category_hc)
                )
                SummaryItem.CLIMBDISTANCECATEGORY1 -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_climb_distance_category_1)
                )
                SummaryItem.CLIMBDISTANCECATEGORY2 -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_climb_distance_category_2)
                )
                SummaryItem.CLIMBDISTANCECATEGORY3 -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_climb_distance_category_3)
                )
                SummaryItem.CLIMBDISTANCECATEGORY4 -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_climb_distance_category_4)
                )
                SummaryItem.CLIMBDISTANCECATEGORYHC -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_climb_distance_category_hc)
                )
                SummaryItem.CLIMBDURATIONCATEGORY1 -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_climb_duration_category_1)
                )
                SummaryItem.CLIMBDURATIONCATEGORY2 -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_climb_duration_category_2)
                )
                SummaryItem.CLIMBDURATIONCATEGORY3 -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_climb_duration_category_3)
                )
                SummaryItem.CLIMBDURATIONCATEGORY4 -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_climb_duration_category_4)
                )
                SummaryItem.CLIMBDURATIONCATEGORYHC -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_climb_duration_category_hc)
                )
                SummaryItem.AVGASCENTSPEED -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_ascent_speed)
                )
                SummaryItem.AVGDESCENTSPEED -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_descent_speed)
                )
                SummaryItem.MAXASCENTSPEED -> WorkoutValue(
                    item = summaryItem,
                    label =res.getString(CR.string.workout_values_headline_max_ascent_speed)
                )
                SummaryItem.MAXDESCENTSPEED -> WorkoutValue(
                    item = summaryItem,
                    label =res.getString(CR.string.workout_values_headline_max_descent_speed)
                )
                SummaryItem.AVGDISTANCEPERSTROKE -> WorkoutValue(
                    item = summaryItem,
                    label =res.getString(CR.string.workout_values_headline_avg_distance_per_stroke)
                )
                SummaryItem.AVGSKIPSRATE -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_skips_per_min)
                )
                SummaryItem.MAXAVGSKIPSRATE -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_max_skips_per_min)
                )
                SummaryItem.ROUNDS -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_rounds)
                )
                SummaryItem.AVGSKIPSPERROUND -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_avg_skip_per_round)
                )
                SummaryItem.MAXCONSECUTIVESKIPS -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_max_consecutive_skips)
                )
                SummaryItem.AVGROWINGPACE -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_avg_rowing_pace),
                )
                SummaryItem.ESTIMATEDFLOORSCLIMBED -> WorkoutValue(
                    item = summaryItem,
                    label = res.getString(CR.string.workout_values_headline_estimated_floors_climbed),
                )
            }
        }
    }
}

private inline fun Result.toKotlinResultOrError(
    errorMessage: () -> String
): kotlin.Result<ComposeFormatter.FormatResult> {
    return if (this is Success) {
        kotlin.Result.success(
            ComposeFormatter.FormatResult(
                value = value,
                unitResId = InfoModelFormatter.getUnitResId(unit),
                valueInRangeUnit = valueInRangeUnit,
                rangeUnitResId = InfoModelFormatter.getUnitResId(rangeUnit)
            )
        )
    } else {
        kotlin.Result.failure(InfoModelFormatException(errorMessage()))
    }
}

class InfoModelFormatException(message: String) : RuntimeException(message)
