package com.stt.android.appupdates

import android.annotation.SuppressLint
import android.content.ActivityNotFoundException
import android.content.Context
import android.content.DialogInterface
import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import androidx.appcompat.app.AppCompatActivity
import androidx.core.net.toUri
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.rememberNavController
import com.stt.android.R
import com.stt.android.STTApplication
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.AnalyticsEventProperty
import com.stt.android.appupdates.ui.navigation.AppUpdatesRoute
import com.stt.android.appupdates.ui.navigation.appUpdatesDestination
import com.stt.android.appupdates.ui.navigation.navigateToOldVersions
import com.stt.android.appupdates.ui.navigation.navigateToVersionNote
import com.stt.android.appupdates.ui.navigation.oldVersionsDestination
import com.stt.android.appupdates.ui.navigation.popUpToAppUpdates
import com.stt.android.appupdates.ui.navigation.popUpToOldVersions
import com.stt.android.appupdates.ui.navigation.versionNoteDestination
import com.stt.android.common.ui.SimpleDialogFragment
import com.stt.android.common.ui.SimpleDialogFragment.Companion.newInstance
import com.stt.android.compose.util.setContentWithM3Theme
import com.stt.android.eventtracking.EventTracker
import com.stt.android.ui.extensions.shortToast
import com.stt.android.utils.FlavorUtils
import dagger.hilt.android.AndroidEntryPoint
import java.util.concurrent.atomic.AtomicInteger
import javax.inject.Inject

/**
 * It has an "easter egg" that can be
 * activated by the user by clicking {@link #CLICKS_TO_ACTIVATE_EASTER_EGG}
 * times the preference. The easter eggs performs the following actions:
 * <p/>
 * <pre>
 * - Copy the main app database to the sdcard
 * - Copy the shared preferences to the sdcard
 * - Copy the not-sync workouts to the sdcard
 * - Trigger an error report silently with a custom exception
 * - Activate writing logs to the sdcard
 * </pre>
 * <p/>
 * Writing logs to the sdcard can slow down the app so the user can disable it
 * by clicking again {@link #CLICKS_TO_ACTIVATE_EASTER_EGG} times the
 * preference.
 */
@AndroidEntryPoint
class AppUpdatesActivity : AppCompatActivity(), SimpleDialogFragment.Callback {

    private val mainHandler = Handler(Looper.getMainLooper())
    private val resetCounter = Runnable { clickCounter.set(0) }
    private val clickCounter = AtomicInteger(0)

    @Inject
    lateinit var eventTracker: EventTracker
    private var url: String? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        url = savedInstanceState?.getString(UPDATE_URL) ?: url
        setContentWithM3Theme {
            val navController = rememberNavController()
            NavHost(
                navController = navController,
                startDestination = AppUpdatesRoute.APP_UPDATES,
            ) {
                appUpdatesDestination(
                    onNavigateUp = { onBackPressedDispatcher.onBackPressed() },
                    onAppVersionClicked = ::onAppVersionClicked,
                    onUpdateClicked = ::showAppUpdateConfirmDialog,
                    onOldVersionsClicked = { navController.navigateToOldVersions() },
                )
                oldVersionsDestination(
                    onNavigateUp = { navController.popUpToAppUpdates() },
                    onVersionSelected = { versionName, versionCode ->
                        navController.navigateToVersionNote(versionName, versionCode)
                    },
                )
                versionNoteDestination(
                    onNavigateUp = { navController.popUpToOldVersions() },
                )
            }
        }
    }

    private fun onAppVersionClicked() {
        if (clickCounter.incrementAndGet() >= CLICKS_TO_ACTIVATE_EASTER_EGG) {
            mainHandler.removeCallbacks(resetCounter)
            clickCounter.set(0)

            // EasterEgg activated.
            val application = applicationContext as STTApplication
            application.toggleEasterEgg(this)
        } else {
            mainHandler.removeCallbacks(resetCounter)
            mainHandler.postDelayed(resetCounter, RESET_COUNTER_DELAY)
        }
    }

    @SuppressLint("UnsafeImplicitIntentLaunch")
    private fun showAppUpdateConfirmDialog(url: String) {
        this.url = url
        val storeName =
            getString(if (FlavorUtils.isSuuntoAppChina) R.string.app_updates_app_store else R.string.app_updates_google_play_store)

        val existingDialog = supportFragmentManager.findFragmentByTag(APP_UPDATE_DIALOG_TAG)
        if (existingDialog == null) {
            val dialog = newInstance(
                "",
                getString(R.string.app_updates_confirm_dialog_title, storeName),
                getString(R.string.app_updates_open),
                getString(R.string.cancel),
                false
            ).apply {
                setCancelable(false)
            }

            dialog.show(supportFragmentManager, APP_UPDATE_DIALOG_TAG)
        }
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        outState.putString(UPDATE_URL, url)
    }
    
    private fun trackAppUpdateEvent() {
        intent.getStringExtra(EXTRA_SOURCE)?.let { source ->
            eventTracker.trackEvent(
                AnalyticsEvent.APP_UPDATE_NOW_BUTTON_CLICK,
                mapOf(AnalyticsEventProperty.APP_UPDATE_NOW_SOURCE to source),
            )
        }
    }

    override fun onDialogButtonPressed(tag: String?, which: Int) {
        if (APP_UPDATE_DIALOG_TAG == tag && which == DialogInterface.BUTTON_POSITIVE) {
            url?.let {
                try {
                    val intent = Intent(Intent.ACTION_VIEW, it.toUri()).apply {
                        addCategory(Intent.CATEGORY_BROWSABLE)
                    }

                    val resolvedActivity = intent.resolveActivity(packageManager)
                    if (resolvedActivity != null) {
                        startActivity(Intent.createChooser(intent, ""))
                        trackAppUpdateEvent()
                    } else {
                        shortToast(R.string.app_updates_store_not_found)
                    }
                } catch (e: Exception) {
                    shortToast(R.string.app_updates_store_not_found)
                }
            }
        }
    }

    override fun onDialogDismissed(tag: String?) {
    }

    companion object {
        private const val RESET_COUNTER_DELAY = 5000L
        private const val CLICKS_TO_ACTIVATE_EASTER_EGG = 7

        private const val EXTRA_SOURCE = "com.stt.android.appupdates.EXTRA_SOURCE"

        const val SOURCE_MANDATORY_UPDATE_POPUP = "MandatoryUpdatePopup"
        const val SOURCE_RECOMMENDED_UPDATE_POPUP = "RecommendedUpdatePopup"
        const val SOURCE_SETTINGS = "Settings"
        private const val APP_UPDATE_DIALOG_TAG = "showAppUpdateConfirmDialog"

        private const val UPDATE_URL = "updateUrl"
        @JvmStatic
        fun newStartIntent(context: Context, source: String): Intent {
            return Intent(context, AppUpdatesActivity::class.java).putExtra(EXTRA_SOURCE, source)
        }
    }
}
