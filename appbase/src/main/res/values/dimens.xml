<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">


    <!-- Dimensions for generic spacings.
        These can be used for paddings and margins, but also for view heights and widths. -->

    <dimen name="size_spacing_xxxxlarge">64dp</dimen>
    <dimen name="size_spacing_xxxlarge">56dp</dimen>
    <dimen name="size_spacing_xxlarge">48dp</dimen>
    <dimen name="size_spacing_xlarge">32dp</dimen>
    <dimen name="size_spacing_large">24dp</dimen>
    <dimen name="size_spacing_medium">16dp</dimen>
    <dimen name="size_spacing_smaller">12dp</dimen>
    <dimen name="size_spacing_xsmaller">10dp</dimen>
    <dimen name="size_spacing_small">8dp</dimen>
    <dimen name="size_spacing_xsmall">4dp</dimen>
    <dimen name="size_spacing_xxsmall">2dp</dimen>
    <dimen name="size_spacing_xxxsmall">1dp</dimen>
    <dimen name="size_spacing_zero">0dp</dimen>

    <dimen name="negative_size_spacing_xxxxlarge">-64dp</dimen>
    <dimen name="negative_size_spacing_medium">-16dp</dimen>
    <dimen name="negative_size_spacing_small">-8dp</dimen>
    <dimen name="negative_size_spacing_xsmall">-4dp</dimen>
    <dimen name="negative_size_spacing_xxsmall">-2dp</dimen>

    <!-- Icons -->
    <!-- Sizes for icons and images. -->

    <dimen name="size_icon_xxxlarge">96dp</dimen>
    <dimen name="size_icon_xxlarge">64dp</dimen>
    <dimen name="size_icon_xlarge">48dp</dimen>
    <dimen name="size_icon_large">40dp</dimen>
    <dimen name="size_icon_medium">32dp</dimen>
    <dimen name="size_icon_xmedium">28dp</dimen>
    <dimen name="size_icon_small">24dp</dimen>
    <dimen name="size_icon_mini">16dp</dimen>
    <dimen name="size_icon_tiny">12dp</dimen>

    <!-- Navigation bars and toolbar specific-->

    <dimen name="elevation_navbar">8dp</dimen>
    <dimen name="elevation_toolbar">4dp</dimen>
    <dimen name="height_toolbar">56dp</dimen>
    <dimen name="height_searchfield">48dp</dimen>
    <dimen name="bottom_sheet_elevation">24dp</dimen>
    <dimen name="bottom_sheet_floating_layer_elevation">38dp</dimen>
    <dimen name="bottom_sheet_corner_radius">@dimen/size_spacing_smaller</dimen>
    <dimen name="bottom_sheet_bottom_padding">@dimen/size_spacing_xlarge</dimen>
    <dimen name="dashboard_toolbar_button_size">48dp</dimen>
    <dimen name="dashboard_toolbar_icon_size">24dp</dimen>
    <dimen name="dashboard_map_width_height">166dp</dimen>
    <dimen name="dashboard_map_corner_radius">83dp</dimen>
    <dimen name="dashboard_grid_item_width">164dp</dimen>
    <dimen name="dashboard_grid_item_height">190dp</dimen>
    <dimen name="dashboard_grid_widget_barchart_height">62dp</dimen>
    <dimen name="dashboard_grid_height">360dp</dimen>
    <dimen name="dashboard_grid_padding">@dimen/size_spacing_small</dimen>
    <dimen name="dashboard_chart_item_height_with_header">33dp</dimen>
    <dimen name="dashboard_chart_item_height_no_header">42dp</dimen>
    <dimen name="dashboard_duration_max_text_size" tools:ignore="SpUsage">80dp</dimen>
    <dimen name="dashboard_goal_wheel_stroke_size">7dp</dimen>
    <dimen name="dashboard_grid_widget_linechart_dot_radius">3dp</dimen>
    <dimen name="dashboard_grid_widget_linechart_line_width">2dp</dimen>

    <!-- Button specific -->

    <dimen name="height_expandable_list_header">56dp</dimen>
    <dimen name="height_button">40dp</dimen>
    <dimen name="wide_button_corner_radius">20dp</dimen>


    <!-- Profile specific -->

    <dimen name="size_profile_image_card">32dp</dimen>
    <dimen name="size_profile_image_large">80dp</dimen>
    <dimen name="height_profile_background_image">128dp</dimen>


    <!-- Progress bars and spinners -->

    <dimen name="size_progress_bar_circular_small">32dp</dimen>
    <dimen name="size_progress_bar_circular_xxsmall">16dp</dimen>

    <!-- Miscellaneous -->

    <dimen name="summary_map_height">196dp</dimen>
    <dimen name="explore_card_map_width">312dp</dimen>
    <dimen name="explore_card_map_height">234dp</dimen>
    <dimen name="size_intro_screen_logo">128dp</dimen>
    <dimen name="size_login_done_circle">160dp</dimen>
    <dimen name="radius_corners_generic">2dp</dimen>
    <dimen name="size_divider">1dp</dimen>
    <dimen name="size_divider_thin">1px</dimen>
    <dimen name="reactions_height">56dp</dimen>
    <dimen name="search_screen_top_offset">64dp</dimen>
    <dimen name="explore_card_height">408dp</dimen>
    <dimen name="explore_card_top_margin">24dp</dimen>
    <dimen name="explore_card_details_button_height">48dp</dimen>
    <dimen name="size_photo_thumbnail">80dp</dimen>
    <dimen name="height_imagepicker">160dp</dimen>
    <dimen name="height_workout_edit_imagepicker">@dimen/size_photo_thumbnail</dimen>
    <dimen name="height_activity_save_imagepicker">@dimen/size_photo_thumbnail</dimen>
    <dimen name="height_small_map">128dp</dimen>
    <dimen name="summary_ab_graph_height">565dp</dimen>
    <dimen name="sportie_card_height">380dp</dimen>
    <dimen name="sportie_card_top_margin">24dp</dimen>
    <dimen name="feed_card_elevation">2dp</dimen>
    <dimen name="feed_card_divider">@dimen/size_spacing_small</dimen>
    <dimen name="feed_card_gradient_height">26dp</dimen>
    <dimen name="laps_type_selector_elevation">2dp</dimen>
    <dimen name="pager_bullet_size">14dp</dimen>
    <dimen name="pager_bullet_padding">4dp</dimen>
    <dimen name="image_picker_selected_badge_size">42dp</dimen>
    <dimen name="dialog_padding">20dp</dimen>
    <dimen name="elevation_separator">1dp</dimen>
    <dimen name="weather_conditions_min_height">22dp</dimen>
    <dimen name="route_view_fab_size">56dp</dimen>
    <dimen name="route_view_fab_top_margin">72dp</dimen>
    <dimen name="tracking_map_compass_padding_top">128dp</dimen>
    <dimen name="dashboard_widget_header_image_padding">6dp</dimen>
    <dimen name="dashboard_widget_header_image_background_size">40dp</dimen>
    <dimen name="dashboard_widget_granularity_icon_size">22dp</dimen>
    <dimen name="dashboard_widget_granularity_icon_margin">6dp</dimen>
    <dimen name="dashboard_widget_buy_premium_map_icon_margin">2dp</dimen>
    <dimen name="dashboard_widget_buy_premium_map_icon_padding">4dp</dimen>
    <dimen name="dashboard_widget_buy_premium_map_placeholder_image_padding">5dp</dimen>
    <dimen name="top_bottom_divider">0dp</dimen>
    <dimen name="height_divemode_customization_alarm_description">75dp</dimen>

    <!-- summary in save tracking view  -->
    <dimen name="activity_summary_value_left_padding">@dimen/size_spacing_small</dimen>
    <dimen name="activity_summary_unit_padding">@dimen/size_spacing_xsmall</dimen>
    <dimen name="activity_summary_row_height">56dp</dimen>

    <!-- comparisons with target  -->
    <dimen name="ghost_target_graph_divider_height">21dp</dimen>
    <dimen name="ghost_target_graph_height">110dp</dimen>
    <dimen name="ghost_target_graph_sweep_view_height">120dp</dimen>

    <dimen name="activity_horizontal_margin">16dp</dimen>

    <!-- Diary specific-->
    <dimen name="diary_image_container_height">120dp</dimen>
    <dimen name="diary_item_height">72dp</dimen>
    <dimen name="workout_summary_timestamp_width">72dp</dimen>
    <dimen name="workout_summary_divider_margin">64dp</dimen>
    <dimen name="diary_calendar_rest_day_radius">4dp</dimen>
    <dimen name="diary_calendar_today_day_radius">4dp</dimen>
    <dimen name="diary_calendar_future_day_radius">2dp</dimen>
    <dimen name="diary_calendar_activity_progress_bar_height">6dp</dimen>
    <dimen name="diary_sleep_chip_max_width">90dp</dimen>
    <dimen name="diary_highlight_corner_radius">6dp</dimen>
    <dimen name="diary_chart_animation_translation_x">70dp</dimen>

    <!-- Route specific-->
    <dimen name="route_empty_image_top_margin">96dp</dimen>
    <dimen name="route_detail_bottom_sheet_peek_height">152dp</dimen>
    <dimen name="route_planner_bottom_sheet_peek_height">28dp</dimen>
    <dimen name="route_planner_bottom_sheet_half_expanded_height">164dp</dimen>
    <dimen name="route_planner_bottom_sheet_half_expanded_height_with_graph">236dp</dimen>
    <dimen name="route_planner_bottom_sheet_half_expanded_height_with_graph_and_route_sections">280dp</dimen>


    <dimen name="route_planner_bottom_sheet_half_expanded_height_old_version">94dp</dimen>
    <dimen name="route_planner_bottom_sheet_half_expanded_height_with_graph_old_version">166dp</dimen>
    <dimen name="route_planner_bottom_sheet_half_expanded_height_with_graph_and_route_sections_old_version">210dp</dimen>

    <dimen name="route_details_bottom_sheet_half_expanded_height">144dp</dimen>
    <dimen name="route_details_bottom_sheet_half_expanded_height_with_graph">216dp</dimen>
    <dimen name="route_details_bottom_sheet_half_expanded_height_with_graph_and_route_sections">260dp</dimen>

    <dimen name="route_planner_floating_action_button_size">48dp</dimen>
    <dimen name="explore_route_card_map_height">@dimen/summary_map_height</dimen>
    <dimen name="route_library_filter_height">48dp</dimen>

    <!-- Activity Icons-->
    <dimen name="activity_type_icon_background_size">40dp</dimen>

    <!-- User profile -->
    <dimen name="profile_data_gone_top_margin">35dp</dimen>
    <dimen name="profile_spacer_gone_top_margin">27dp</dimen>

    <!-- New item count badge -->
    <dimen name="notification_counter_background_size">20dp</dimen>
    <dimen name="notification_counter_text_size">10dp</dimen><!-- intentionally use dp unit for text size -->

    <dimen name="start_workout_activity_icon_size">32dp</dimen>
    <dimen name="start_workout_setting_row_height">56dp</dimen>
    <dimen name="start_workout_setting_with_subtitle_row_height">72dp</dimen>

    <!-- Advanced laps -->
    <dimen name="advanced_laps_header_row_height">82dp</dimen>
    <dimen name="advanced_laps_number_width">22dp</dimen>

    <!-- Feed braze card -->
    <dimen name="feed_braze_card_vertical_text_margin">20dp</dimen>
    <dimen name="feed_braze_card_button_height">32dp</dimen>
    <dimen name="feed_braze_card_button_corner_radius">16dp</dimen>
    <dimen name="feed_braze_card_close_button_touch_area">56dp</dimen>
    <dimen name="feed_braze_card_close_button_background_size">24dp</dimen>
    <dimen name="feed_braze_card_close_button_size">20dp</dimen>

    <!-- NOTE! From all here down is legacy. -->

    <dimen name="activity_vertical_margin">16dp</dimen>
    <dimen name="d20">20.00dp</dimen>

    <dimen name="notification_counter_size">18dp</dimen>
    <dimen name="activity_data_goal_wheel_stroke_size">4dp</dimen>
    <dimen name="goal_wheel_txt_spacing">6dp</dimen>
    <dimen name="goal_achieved_txt_max_width">250dp</dimen>

    <dimen name="barchart_margin">30dp</dimen>

    <dimen name="dashboard_profile_image_width">32dp</dimen>
    <dimen name="dashboard_profile_image_height">32dp</dimen>
    <dimen name="dashboard_secondary_height">60dp</dimen>
    <dimen name="dashboard_grid_spacing">@dimen/smaller_padding</dimen>
    <dimen name="dashboard_diary_calendar_rest_day_radius">2dp</dimen>
    <dimen name="dashboard_diary_calendar_future_day_radius">2dp</dimen>

    <dimen name="start_workout_button_width">176dp</dimen>
    <dimen name="start_workout_button_height">48dp</dimen>
    <dimen name="start_workout_button_bottom_margin">16dp</dimen>
    <dimen name="start_workout_button_top_margin">4dp</dimen>

    <dimen name="button_corner_radius">10dp</dimen>

    <dimen name="ad_padding">16dp</dimen>

    <dimen name="whats_new_text_size">14dp</dimen>
    <dimen name="whats_new_title_text_size">20dp</dimen>
    <dimen name="whats_new_line_spacing_extra">3dp</dimen>

    <dimen name="like_profile_images_spacing">4dp</dimen>

    <dimen name="fastest_lap_icon_padding">4dp</dimen>

    <dimen name="fab_bottom_padding_with_nav">56dp</dimen>
    <dimen name="fab_bottom_internal_padding_with_search">102dp</dimen>

    <dimen name="workout_value_item_margin">.5dp</dimen>
    <dimen name="workout_details_initial_peek">260dp</dimen>

    <dimen name="login_sign_up_btn_height_intro_48dp">48dp</dimen>
    <dimen name="analysis_chart_height_workout_details">144dp</dimen>
    <dimen name="bottommost_item_margin">24dp</dimen>

    <dimen name="zoned_chart_bar_width">4dp</dimen>
    <dimen name="dot_indicator_size">6dp</dimen>
    <dimen name="dot_indicator_margin">3dp</dimen>
    <dimen name="size_chart_height">160dp</dimen>

    <dimen name="share_preview_image_height">260dp</dimen>

    <!-- Map selection -->
    <dimen name="map_selection_item_width">80dp</dimen>
    <dimen name="map_selection_image_size">52dp</dimen>
    <dimen name="heatmap_selection_image_size">44dp</dimen>
    <dimen name="road_surface_selection_image_size">52dp</dimen>
    <dimen name="my_tracks_selection_image_size">32dp</dimen>
    <dimen name="map_selection_image_size_none">32dp</dimen>
    <dimen name="map_selection_item_title_max_text_size">12dp</dimen>
    <dimen name="map_selection_item_min_text_size">6dp</dimen>
    <dimen name="map_selection_item_text_size_granularity">2dp</dimen>
    <dimen name="map_type_selection_item_height">110dp</dimen>
    <dimen name="map_option_selection_indicator_size">60dp</dimen>

    <!-- Maps -->
    <dimen name="map_snapshot_logo_margin">16dp</dimen>
    <dimen name="map_route_padding">26dp</dimen>
    <dimen name="map_popular_route_padding">8dp</dimen>
    <dimen name="map_compass_padding_top_with_map_info">116dp</dimen>
    <dimen name="map_padding_horizontal_googlemap">6dp</dimen>
    <dimen name="map_padding_horizontal_mapbox">8dp</dimen>
    <dimen name="map_compass_padding_horizontal_mapbox">13dp</dimen>
    <dimen name="map_explore_extra_top_padding">72dp</dimen>
    <dimen name="map_top_routes_scale_bar_top_margin">118dp</dimen>
    <dimen name="map_scale_bar_margin_left">72dp</dimen>
    <dimen name="map_scale_bar_margin_top">20dp</dimen>
    <dimen name="map_compass_padding_top">60dp</dimen>
    <dimen name="map_scale_bar_height">4dp</dimen>
    <item name="map_scale_bar_max_width_ratio" type="dimen" format="float">0.6</item>
    <dimen name="map_scale_bar_text_size">10dp</dimen>
    <dimen name="map_scale_bar_text_margin">4dp</dimen>
    <dimen name="map_scale_bar_border_width">0.5dp</dimen>
    <dimen name="map_option_item_elevation">6dp</dimen>

    <!-- Road surface info -->
    <dimen name="road_surface_icon_elevation">8dp</dimen>
    <dimen name="road_surface_icon_border">3dp</dimen>
    <dimen name="road_surface_icon_x_offset">-17dp</dimen>
    <dimen name="road_surface_icon_y_offset">-9dp</dimen>

    <!-- Full screen chart -->
    <dimen name="chart_highlight_corner_radius">8dp</dimen>


    <!-- Dimensions for outdoor dashboard duration widget -->
    <dimen name="size_translationY_durationtxt">@dimen/size_spacing_small</dimen>
    <dimen name="size_margin_descriptiontxt">@dimen/size_spacing_xsmall</dimen>
    <dimen name="size_margin_unittxt">6dp</dimen>

    <dimen name="resolver_icon_size">44dp</dimen>
    <dimen name="barchart_bar_corner_radius">2dp</dimen>

    <!-- BrazeContentCardsFragment recyclerview -->
    <dimen name="com_braze_content_cards_divider_height">@dimen/size_spacing_medium</dimen>

    <!-- System widgets -->
    <dimen name="systemwidget_default_min_size">110dp</dimen>
    <dimen name="systemwidget_resize_min_height">40dp</dimen>
    <dimen name="systemwidget_minimal_height_threshold">90dp</dimen>
    <dimen name="systemwidget_limited_height_threshold">140dp</dimen>
    <dimen name="systemwidget_background_corner_radius">8dp</dimen>
    <dimen name="systemwidget_min_size">110dp</dimen>
    <dimen name="progresswidget_minimal_height_threshold">88dp</dimen>
    <dimen name="progresswidget_very_limited_height_threshold">98dp</dimen>
    <dimen name="commutewidget_minimal_height_threshold">96dp</dimen>

    <!-- TSS calculation method selector -->
    <dimen name="tss_value_editor_item_height">52dp</dimen>

    <!-- Onboarding page -->
    <dimen name="onboarding_page_photo_guideline_percent">0.5</dimen>

    <!-- Avalanche map legend -->
    <dimen name="avalanche_info_max_width">300dp</dimen>

    <!-- Tags -->
    <dimen name="tag_default_chip_min_height">24dp</dimen>
    <dimen name="tag_editable_chip_min_height">32dp</dimen>
    <dimen name="tag_editable_chip_min_width">56dp</dimen>
    <dimen name="tag_editable_close_icon_size">24dp</dimen>
    
    <!-- POI -->
    <dimen name="poi_icon_size">32dp</dimen>
    <dimen name="hr_zone_box_height">62dp</dimen>

    <dimen name="bottom_picker_height">216dp</dimen>

    <!-- Popular routes -->
    <dimen name="popular_route_filter_map_width">70dp</dimen>
    <dimen name="popular_route_filter_map_height">70dp</dimen>
    <dimen name="popular_route_detail_bottom_sheet_peek_height">142dp</dimen>

</resources>
