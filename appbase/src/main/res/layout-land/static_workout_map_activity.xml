<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/rootView"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <include
        layout="@layout/map_activity"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <LinearLayout
        android:id="@+id/chartViewContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:orientation="vertical"
        android:visibility="invisible"
        tools:visibility="visible">

        <FrameLayout
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_gravity="end"
            android:background="@color/barely_transparent">

            <ImageView
                android:id="@+id/chartViewController"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:contentDescription="@null"
                android:padding="8dp"
                app:srcCompat="@drawable/ic_chevron" />
        </FrameLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/barely_transparent"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/currentDuration"
                style="@style/s21"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:freezesText="true"
                android:gravity="center"
                android:paddingTop="@dimen/smaller_padding"
                android:textColor="@color/almost_white"
                tools:text="11:22:33" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center_horizontal"
                android:orientation="horizontal"
                android:paddingTop="@dimen/smaller_padding">

                <TextView
                    android:id="@+id/currentDistance"
                    style="@style/s21"
                    android:freezesText="true"
                    android:textColor="@color/almost_white"
                    tools:text="4.56" />

                <TextView
                    android:id="@+id/currentDistanceUnit"
                    style="@style/s15"
                    android:layout_marginStart="@dimen/d2h"
                    android:freezesText="true"
                    android:textColor="@color/almost_white"
                    tools:text="km" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/currentSpeedPaceContainer"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center_horizontal"
                android:orientation="horizontal"
                android:paddingTop="@dimen/smaller_padding"
                android:paddingBottom="@dimen/smaller_padding">

                <TextView
                    android:id="@+id/currentSpeedPace"
                    style="@style/s21"
                    android:freezesText="true"
                    android:textColor="@color/graphlib_speed"
                    tools:text="7.8" />

                <TextView
                    android:id="@+id/currentSpeedPaceUnit"
                    style="@style/s15"
                    android:layout_marginStart="@dimen/d2h"
                    android:freezesText="true"
                    android:textColor="@color/graphlib_speed"
                    tools:text="km/h" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/currentAltitudeContainer"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center_horizontal"
                android:orientation="horizontal"
                android:paddingTop="@dimen/smaller_padding"
                android:paddingBottom="@dimen/smaller_padding">

                <TextView
                    android:id="@+id/currentAltitude"
                    style="@style/s21"
                    android:freezesText="true"
                    android:textColor="@color/graphlib_altitude"
                    tools:text="90" />

                <TextView
                    android:id="@+id/currentAltitudeUnit"
                    style="@style/s15"
                    android:layout_marginStart="@dimen/d2h"
                    android:freezesText="true"
                    android:textColor="@color/graphlib_altitude"
                    tools:text="m" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/currentHeartRateContainer"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center_horizontal"
                android:orientation="horizontal"
                android:paddingTop="@dimen/smaller_padding"
                android:paddingBottom="@dimen/smaller_padding">

                <TextView
                    android:id="@+id/currentHeartRate"
                    style="@style/s21"
                    android:freezesText="true"
                    android:textColor="@color/graphlib_hr"
                    tools:text="123" />

                <TextView
                    android:id="@+id/currentHeartRateUnit"
                    style="@style/s15"
                    android:layout_marginStart="@dimen/d2h"
                    android:freezesText="true"
                    android:text="@string/bpm"
                    android:textColor="@color/graphlib_hr" />
            </LinearLayout>
        </LinearLayout>

        <com.stt.android.ui.components.charts.WorkoutDataChart
            android:id="@+id/workoutDataChartView"
            android:layout_width="match_parent"
            android:layout_height="90dp"
            android:background="@color/barely_transparent" />
    </LinearLayout>
</FrameLayout>
