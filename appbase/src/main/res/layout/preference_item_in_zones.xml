<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <variable
            name="titletext"
            type="String" />

        <variable
            name="summarytext"
            type="String" />

        <variable
            name="operationText"
            type="String" />

        <variable
            name="summaryGone"
            type="java.lang.Boolean" />

        <variable
            name="separatorVisible"
            type="java.lang.Boolean" />

        <variable
            name="enableTextSelection"
            type="boolean" />
    </data>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/body"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:background="@drawable/selectable_card_background"
            android:orientation="vertical"
            android:paddingTop="@dimen/settings_padding_top"
            android:paddingBottom="@dimen/settings_padding_bottom">

            <TextView
                android:id="@android:id/title"
                style="@style/Body.Larger"
                android:duplicateParentState="true"
                android:paddingStart="@dimen/padding"
                android:paddingTop="@dimen/settings_text_padding_top"
                android:paddingEnd="@dimen/padding"
                android:text="@{titletext}"
                android:textColor="@color/settings_item_color"
                tools:text="@string/settings_service_sign_out" />

            <TextView
                android:id="@android:id/summary"
                style="@style/Body.Small"
                android:duplicateParentState="true"
                android:lineSpacingMultiplier="1.3"
                android:paddingStart="@dimen/padding"
                android:paddingTop="@dimen/settings_text_padding_top"
                android:paddingEnd="@dimen/padding"
                android:text="@{summarytext}"
                android:textColor="@color/suunto_dark_gray"
                android:textIsSelectable="@{enableTextSelection}"
                android:visibility="@{summaryGone ? View.GONE : View.VISIBLE}"
                tools:text="@string/settings_service_sign_out_summary" />
        </LinearLayout>


        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/very_light_gray"
            android:layout_alignBottom="@id/body"
            android:visibility="@{separatorVisible ? View.VISIBLE : View.GONE}" />
    </RelativeLayout>
</layout>
