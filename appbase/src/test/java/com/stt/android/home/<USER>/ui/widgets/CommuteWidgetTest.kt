package com.stt.android.home.dashboardv2.ui.widgets

import androidx.compose.ui.test.junit4.createComposeRule
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.domain.workout.ActivityType
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith

@RunWith(AndroidJUnit4::class)
class CommuteWidgetTest {

    @get:Rule
    val composeTestRule = createComposeRule()

    @Test
    fun commuteActivityList_withMultipleActivities_rendersCorrectly() {
        // Arrange
        val activityCommuteList = listOf(
            Pair(ActivityType.CYCLING, 0.4f),
            Pair(ActivityType.RUNNING, 0.3f),
            Pair(ActivityType.WALKING, 0.3f)
        )

        // Act
        composeTestRule.setContent {
            M3AppTheme {
                CommuteActivityList(
                    activityCommuteList = activityCommuteList
                )
            }
        }

        // Assert - The composable should render without crashing
        // This test verifies that:
        // 1. Each segment is drawn proportionally
        // 2. Every segment end has rounded corners
        // 3. From the second segment onwards, the start overlaps with previous segment's rounded end
        composeTestRule.waitForIdle()
    }

    @Test
    fun commuteActivityList_withSingleActivity_rendersCorrectly() {
        // Arrange
        val activityCommuteList = listOf(
            Pair(ActivityType.CYCLING, 1.0f)
        )

        // Act
        composeTestRule.setContent {
            M3AppTheme {
                CommuteActivityList(
                    activityCommuteList = activityCommuteList
                )
            }
        }

        // Assert - The composable should render without crashing
        composeTestRule.waitForIdle()
    }

    @Test
    fun commuteActivityList_withZeroPercentActivities_filtersCorrectly() {
        // Arrange
        val activityCommuteList = listOf(
            Pair(ActivityType.CYCLING, 0.5f),
            Pair(ActivityType.RUNNING, 0.0f), // This should be filtered out
            Pair(ActivityType.WALKING, 0.5f)
        )

        // Act
        composeTestRule.setContent {
            M3AppTheme {
                CommuteActivityList(
                    activityCommuteList = activityCommuteList
                )
            }
        }

        // Assert - The composable should render without crashing
        composeTestRule.waitForIdle()
    }

    @Test
    fun commuteActivityList_withEmptyList_rendersCorrectly() {
        // Arrange
        val activityCommuteList = emptyList<Pair<ActivityType?, Float>>()

        // Act
        composeTestRule.setContent {
            M3AppTheme {
                CommuteActivityList(
                    activityCommuteList = activityCommuteList
                )
            }
        }

        // Assert - The composable should render without crashing
        composeTestRule.waitForIdle()
    }
}
