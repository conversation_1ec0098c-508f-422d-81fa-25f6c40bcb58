package com.stt.android.utils

import org.assertj.core.api.Assertions
import org.assertj.core.api.Assertions.assertThat
import org.junit.Rule
import org.junit.Test
import org.junit.rules.TemporaryFolder
import java.io.File
import java.nio.file.Paths

class FileExtensionsKtTest {
    @get:Rule
    var temporaryFolder = TemporaryFolder()

    @Test
    fun `test unzipping works correctly when no files present`() {
        val filesDir: File = temporaryFolder.newFolder("sportmodecomponent")
        javaClass.classLoader?.getResourceAsStream("testarchive.zip")
            ?.unzipFromAssets(filesDir.absolutePath)
        Assertions.assertThat(filesDir.listFiles().size).isEqualTo(3)
    }

    @Test
    fun `test unzipping works correctly when files already exist`() {
        val filesDir: File = temporaryFolder.newFolder("sportmodecomponent")
        Paths.get(filesDir.absolutePath, "test1.txt").toFile().createNewFile()
        Paths.get(filesDir.absolutePath, "test2.txt").toFile().createNewFile()
        javaClass.classLoader?.getResourceAsStream("testarchive.zip")
            ?.unzipFromAssets(filesDir.absolutePath)
        assertThat(filesDir.listFiles().size).isEqualTo(3)
    }
}
