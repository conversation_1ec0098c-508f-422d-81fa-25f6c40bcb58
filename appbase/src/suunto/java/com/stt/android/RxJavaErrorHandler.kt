package com.stt.android

import com.stt.android.exceptions.BackendException
import com.stt.android.utils.STTConstants
import com.stt.android.watch.MissingCurrentWatchException
import io.reactivex.exceptions.UndeliverableException
import timber.log.Timber
import java.io.IOException

fun handleRxJava2Error(t: Throwable) {
    // See <https://github.com/ReactiveX/RxJava/wiki/What%27s-different-in-2.0#error-handling>
    Timber.w(t, "Uncaught rxjava2 error")

    // Log and ignore all undeliverable exceptions in release builds. Conditionally crash the app
    // in debug builds.
    if (STTConstants.DEBUG) {
        // Ignore exceptions probably caused by interruptions and not programming errors
        if (t.shouldIgnore()) {
            return
        }

        if (t is UndeliverableException && t.cause?.shouldIgnore() == true) {
            return
        }

        // Let other undeliverable exceptions crash the app
        val thread = Thread.currentThread()
        thread.uncaughtExceptionHandler!!.uncaughtException(thread, t)
    }
}

fun handleRxJavaError(t: Throwable) {
    // let's not crash the app cause rx1 throws at random and there is no wrapper
    Timber.w(t, "Uncaught rxjava error")
}

// Ignore these exceptions as they may be the result of a normal operation throwing
// after being cancelled.
private fun Throwable.shouldIgnore() =
    this is IOException || this is InterruptedException || this is BackendException || this is MissingCurrentWatchException
