package com.stt.android.watch.offlinemaps.datasource

import com.stt.android.watch.SuuntoWatchModel
import javax.inject.Inject

class WatchMapDownloadDataSource @Inject constructor(
    private val watchModel: SuuntoWatchModel,
) : MapDownloadDataSource {

    override suspend fun notifyAreaSelectionChanged() {
        watchModel.notifyAreaSelectionChanged()
    }

    override suspend fun notifyAreaUnderDownloadDeleted(areaId: String) {
        watchModel.notifyAreaUnderDownloadDeleted(areaId)
    }

    override suspend fun numberOfAreas(): Int {
        return watchModel.numberOfAreas()
    }
}
