package com.stt.android.watch.sportmodes.fte

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.SavedStateHandle
import com.stt.android.common.ui.LoadingViewModel
import com.stt.android.data.device.DeviceInfoApi
import com.stt.android.domain.di.IoThread
import com.stt.android.domain.di.MainThread
import com.stt.android.domain.sportmodes.ChangeSportModesUseCase
import com.stt.android.ui.utils.SingleLiveEvent
import com.stt.android.utils.getEnumExtra
import com.stt.android.watch.WatchHelper
import com.stt.android.watch.sportmodes.SportModeActivity
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoDeviceType
import dagger.hilt.android.lifecycle.HiltViewModel
import io.reactivex.Scheduler
import io.reactivex.rxkotlin.plusAssign
import io.reactivex.rxkotlin.subscribeBy
import timber.log.Timber
import javax.inject.Inject

@HiltViewModel
class SportModeFteViewModel
@Inject constructor(
    savedStateHandle: SavedStateHandle,
    @IoThread ioThread: Scheduler,
    @MainThread mainThread: Scheduler,
    private val changeSportModesUseCase: ChangeSportModesUseCase,
    deviceInfoApi: DeviceInfoApi
) : LoadingViewModel(ioThread, mainThread) {

    var suuntoDeviceType: SuuntoDeviceType = savedStateHandle.getEnumExtra(
        SportModeActivity.SUUNTO_DEVICE_TYPE,
        SuuntoDeviceType::class.java,
        SuuntoDeviceType.Unrecognized
    )

    val continueToSportModesEvent = SingleLiveEvent<Any>()

    val deviceDrawableResId: MutableLiveData<Int> = MutableLiveData<Int>().apply {
        value = WatchHelper.getDrawableResIdForSuuntoDeviceType(suuntoDeviceType, "")
    }

    init {
        disposables += deviceInfoApi.sku()
            .subscribeOn(ioThread)
            .observeOn(mainThread)
            .subscribeBy(
                onSuccess = { sku ->
                    deviceDrawableResId.value = WatchHelper.getDrawableResIdForSuuntoDeviceType(suuntoDeviceType, sku)
                },
                onError = { Timber.w(it, "Error fetching sku information") }
            )
    }

    override fun loadData() {
    }

    private fun continueToSportModes() {
        continueToSportModesEvent.call()
    }

    fun onContinueButtonClicked() {
        disposables.add(
            changeSportModesUseCase.setSportModeFteCompleted(true)
                .subscribeOn(ioThread)
                .observeOn(mainThread)
                .retryWhen { error -> handleError(error) }
                .subscribe({
                    continueToSportModes()
                }, {
                    Timber.w(it, "Error in setSportModeFteCompleted")
                })
        )
    }
}
