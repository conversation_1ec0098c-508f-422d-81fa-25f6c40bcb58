package com.stt.android.watch

import android.content.Context
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.data.device.DeviceLogApi
import com.stt.android.logs.LogUploader
import com.stt.android.remote.device.DeviceLogRemoteApi
import com.stt.android.utils.FileUtils.getCacheDirectory
import com.stt.android.utils.toV2
import com.suunto.connectivity.ScLib
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.toList
import kotlinx.coroutines.rx2.asFlow
import kotlinx.coroutines.withContext
import java.io.BufferedOutputStream
import java.io.File
import java.io.FileInputStream
import java.io.FileNotFoundException
import java.io.FileOutputStream
import java.util.zip.ZipEntry
import java.util.zip.ZipOutputStream
import javax.inject.Inject

@Module
@InstallIn(SingletonComponent::class)
abstract class DeviceLogHandlerModule {
    @Binds
    abstract fun bindDeviceLogApi(deviceLogHandler: DeviceLogHandler): DeviceLogApi

    @Binds
    abstract fun bindDeviceLogRemoteApi(deviceLogHandler: DeviceLogHandler): DeviceLogRemoteApi
}

/**
 * DeviceLogHandler is an injected helper class that implements [DeviceLogApi]
 * and [DeviceLogRemoteApi] for sending device logs to backend
 */
class DeviceLogHandler @Inject constructor(
    private val scLib: ScLib,
    private val logUploader: LogUploader,
    private val context: Context,
    private val coroutinesDispatchers: CoroutinesDispatchers,
) : DeviceLogApi, DeviceLogRemoteApi {

    override suspend fun getLogs(logType: Int): List<File> = withContext(coroutinesDispatchers.io) {
        // TODO Convert scLib.fetchLogs to Coroutines
        scLib.getLogs(logType)
            .filter { it != null } // RxJava V1 could emit null but V2 hates it.
            .toV2()
            .asFlow()
            .toList()
    }

    override suspend fun sendLogs(logFiles: List<File>, toFile: Boolean, remoteLogId: String) {
        // TODO Move LogUploader implementation to remote module
        logUploader.sendLogs(logFiles, toFile, remoteLogId)
    }

    override suspend fun getHeadsetCacheLogs(): List<File> = withContext(coroutinesDispatchers.io) {
        val subDirs = listOf(SUB_DIR_NECK, SUB_DIR_JUMP, SUB_DIR_SWIM)
        val zipFiles = mutableListOf<File>()

        subDirs.forEach { subDir ->
            val dir = getCacheDirectory(context, "$DIRECTORY_HEADSET$subDir")
            if (dir.exists() && dir.isDirectory) {
                val files = dir.listFiles()?.filter { it.isFile }.orEmpty()
                if (files.any()) {
                    val zipFile = File(dir.parent, "$subDir.zip")
                    ZipOutputStream(BufferedOutputStream(FileOutputStream(zipFile))).use { zipOut ->
                        files.forEach { file ->
                            FileInputStream(file).use { input ->
                                val entry = ZipEntry(file.name)
                                zipOut.putNextEntry(entry)
                                input.copyTo(zipOut)
                                zipOut.closeEntry()
                            }
                        }
                    }
                    zipFiles.add(zipFile)
                }
            }
        }

        if (zipFiles.none()) {
            throw FileNotFoundException("No files found in Headset_cache")
        }

        zipFiles
    }

    companion object {
        private const val DIRECTORY_HEADSET = "Headset_cache/"
        private const val SUB_DIR_NECK = "neck"
        private const val SUB_DIR_JUMP = "jump"
        private const val SUB_DIR_SWIM = "swim"
    }
}
