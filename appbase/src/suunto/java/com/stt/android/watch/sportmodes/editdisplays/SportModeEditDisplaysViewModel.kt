package com.stt.android.watch.sportmodes.editdisplays

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.SavedStateHandle
import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.AnalyticsEventProperty
import com.stt.android.analytics.AnalyticsProperties
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.analytics.EmarsysAnalytics
import com.stt.android.data.device.DeviceInfoApi
import com.stt.android.domain.di.IoThread
import com.stt.android.domain.di.MainThread
import com.stt.android.domain.sportmodes.ChangeSportModesUseCase
import com.stt.android.domain.sportmodes.Display
import com.stt.android.domain.sportmodes.FetchSportModesUseCase
import com.stt.android.domain.sportmodes.Field
import com.stt.android.domain.sportmodes.Group
import com.stt.android.domain.workout.ActivityType
import com.stt.android.infomodel.getStIdForMcId
import com.stt.android.ui.utils.SingleLiveEvent
import com.stt.android.watch.sportmodes.SportModeViewModel
import com.stt.android.watch.sportmodes.errors.SaveFailedException
import com.xwray.groupie.Section
import dagger.hilt.android.lifecycle.HiltViewModel
import io.reactivex.BackpressureStrategy
import io.reactivex.Observable
import io.reactivex.Scheduler
import io.reactivex.functions.BiFunction
import io.reactivex.functions.Function3
import io.reactivex.rxkotlin.Singles
import timber.log.Timber
import java.util.Locale
import java.util.concurrent.TimeUnit
import java.util.concurrent.TimeoutException
import javax.inject.Inject

@HiltViewModel
class SportModeEditDisplaysViewModel
@Inject constructor(
    savedStateHandle: SavedStateHandle,
    @IoThread ioThread: Scheduler,
    @MainThread mainThread: Scheduler,
    private val fetchSportModesUseCase: FetchSportModesUseCase,
    private val changeSportModesUseCase: ChangeSportModesUseCase,
    private val emarsysAnalytics: EmarsysAnalytics,
    private val amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker,
    private val deviceInfoApi: DeviceInfoApi
) : SportModeViewModel(ioThread, mainThread),
    SportModeDisplayChangeListener,
    SportModeNameChangeListener,
    SaveChangesCallback {

    private var sportModeId: Int = SportModeEditDisplaysFragmentArgs.fromSavedStateHandle(savedStateHandle).sportModeId
    var groupId: Int = SportModeEditDisplaysFragmentArgs.fromSavedStateHandle(savedStateHandle).groupId

    companion object {
        const val SPORT_MODE_ID = "sportModeId"
        const val GROUP_ID = "groupId"
        const val SPORT_MODE_INVALID_ID = -1
    }

    val showDeleteDisplayAlertEvent = SingleLiveEvent<Any>()
    val showNonSavedChangesAlertEvent = SingleLiveEvent<Any>()
    val showSaveCompleteEvent = SingleLiveEvent<Any>()
    val saveEnabled = MutableLiveData<Boolean>()
    private var isInputValid = true
    private var isWatchAvailable = true
    private var displaysHolder = mutableListOf<Display>()
    private var fieldsHolder = mutableListOf<List<Field>>()
    private var displayIndex = 0
    private var saveDelegate: SaveDelegate? = null
    private var headerItem: SportModeEditDisplaysHeaderItem? = null
    private var updatingDisplay = false

    override fun onCleared() {
        super.onCleared()
        sharedViewModel.saveChangesCallback = null
        sharedViewModel.displaysRelay.accept("")
        sharedViewModel.settingsRelay.accept("")
        sharedViewModel.groupRelay.accept("")
    }

    /**
     * Callback of name validator for the header edit text. It updates the value in the group json that is stored
     * on the shared view model and updates the save button availability
     */
    override fun onNameChanged(name: String, isValid: Boolean) {
        sharedViewModel.updateName(name)
        isInputValid = isValid
        updateSaveAvailability()
    }

    /**
     * Callback of horizontal recycler view that is called whenever user changes currently selected display
     * We repopulate the fields corresponding to the currently selected display here
     */
    override fun onDisplayChanged(displayIndex: Int, displayId: String) {
        this.displayIndex = displayIndex
        val dataSection: Section
        if (displayIndex < sharedViewModel.currentDisplayCount) {
            val display = displaysHolder[displayIndex]
            dataSection = Section(
                headerItem,
                (
                    listOf(
                        // children for groupie are passed here, but since we are moving more to Compose
                        // then we use one compose view here that wraps all the children
                        SportModeEditFields(
                            displayId = displayId,
                            displayIndex = displayIndex,
                            isReadOnly = display.isReadonly,
                            sportModeDisplayChangeDelegate = this,
                            fields = fieldsHolder[displayIndex]
                        )
                    )
                    )
            )
            if (sharedViewModel.currentDisplayCount > sharedViewModel.minDisplayCount && display.deletable) {
                dataSection.setFooter(SportModeEditDisplaysDeleteItem(this))
            } else {
                dataSection.removeFooter()
            }
        } else {
            dataSection = Section(headerItem)
        }
        notifyDataLoaded(listOf(dataSection))

        // We want to send analytics only when user navigates between screens by swiping
        if (!updatingDisplay && sharedViewModel.lastSelectedDisplayIndex != displayIndex) {
            trackSportModeDisplayNavigationEvent()
        }
    }

    /**
     * Saves currently selected index and updates the group value in relay.
     * This way we after user returns to the screen he will have the proper name value
     * and previously selected display index. Storing display index only is not enough as it is changed
     * by the horizontal scroll view with displays when adapter gets repopulated
     */
    override fun onChangeDisplayLayoutOrFieldClicked() {
        sharedViewModel.lastSelectedDisplayIndex = displayIndex
        sharedViewModel.updateGroupRelay()
        updatingDisplay = true
    }

    /**
     * Callback called when we click on delete display
     */
    override fun onDeleteDisplayRequested() {
        showDeleteDisplayAlertEvent.call()
    }

    /**
     * Callback called when the user confirms deletion of a display.
     * Decreases the index of currently selected display and updates the mode stored on the shared view model
     */
    override fun onDisplayDeleted() {
        val displayId = displaysHolder[displayIndex].id
        disposables.add(
            changeSportModesUseCase.deleteDisplay(
                sharedViewModel.getCurrentDisplays(),
                sharedViewModel.getCurrentSettings(),
                displayIndex
            )
                .retryWhen { errors ->
                    handleError(errors)
                }
                .subscribeOn(ioThread)
                .observeOn(mainThread)
                .doOnSuccess {
                    trackSportModeDeleteDisplayEvent(displayId)
                    if (displayIndex > 0) {
                        displayIndex--
                    }
                    sharedViewModel.lastSelectedDisplayIndex = displayIndex
                    sharedViewModel.setCurrentMode(it)
                }
                .ignoreElement()
                .subscribe()
        )
    }

    /**
     * Loading the data for edit displays screen. Clears values out of relays (unless we have created new sport mode
     * template), resets the last selected display index. Then it uses the three relays (displays, settings, groups)
     * to populate the screen.
     */
    override fun loadData() {
        sharedViewModel.saveChangesCallback = this
        sharedViewModel.lastSelectedDisplayIndex = 0
        notifyDataLoading()
        disposables.add(
            Observable.combineLatest(
                // check the displays json, if it is empty, fetch from the watch
                sharedViewModel.displaysRelay
                    .flatMap {
                        if (it.isEmpty()) {
                            fetchSportModesUseCase.fetchSportModeDisplaysJson(sportModeId)
                                .flatMap {
                                    // The list of displays that comes from the watch is serialized, we need to deserialize it first before using it
                                    fetchSportModesUseCase.setDisplayValuesAsStrings(it)
                                }
                                .doOnSuccess {
                                    sharedViewModel.setCurrentDisplays(it, true)
                                }
                                .toObservable()
                        } else {
                            Observable.just(it)
                        }
                    },
                // check the settings json, if it is empty, fetch from the watch
                sharedViewModel.settingsRelay
                    .flatMap {
                        if (it.trim().isEmpty()) {
                            fetchSportModesUseCase.fetchSportModeSettingsJson(sportModeId)
                                .doOnSuccess {
                                    sharedViewModel.setCurrentSettings(it, true)
                                }
                                .toObservable()
                        } else {
                            Observable.just(it)
                        }
                    }
                    // using settings, ask for min and max amount of displays and then emit settings json down the chain
                    .flatMap {
                        fetchSportModesUseCase.fetchMinNumberOfDisplays(it)
                            .zipWith(
                                fetchSportModesUseCase.fetchMaxNumberOfDisplays(it),
                                BiFunction<Int, Int, Pair<Int, Int>> { min, max -> min to max }
                            )
                            .doOnSuccess {
                                sharedViewModel.minDisplayCount = it.first
                                sharedViewModel.maxDisplayCount = it.second
                            }
                            .ignoreElement()
                            .toSingleDefault(it)
                            .toObservable()
                    },
                // check the group json, if it is empty, fetch from the wath
                sharedViewModel.groupRelay
                    .flatMap {
                        if (it.isEmpty()) {
                            fetchSportModesUseCase.fetchGroup(groupId)
                                .doOnSuccess { groupJson ->
                                    // please note we do not update our relay value here,
                                    // otherwise every char change on the name will trigger the group relay and
                                    // it will cause unnecessary updates to the UI
                                    sharedViewModel.setGroup(Group(groupId, groupJson), true)
                                }
                                .toObservable()
                        } else {
                            Observable.just(it)
                        }
                    },
                Function3<String, String, String, Triple<String, String, String>> { displays, settings, group ->
                    Triple(displays, settings, group)
                }
            )
                // small debounce to prevent extra calls on populating ui in case where we update multiple mode jsons
                // like displays and settings or group at the same time
                .debounce(100, TimeUnit.MILLISECONDS)
                .distinctUntilChanged()
                // using displays and settings jsons ask for list of displays and corresponding fields
                .flatMap {
                    fetchSportModesUseCase.fetchCurrentDisplayList(it.first, it.second)
                        .toObservable()
                }
                .flatMapSingle { displayAndFieldsList ->
                    // using the group and settings we fetch name length limits
                    // Also fetch sku info needed to create SportModesEditDisplaysHeaderItem
                    Singles.zip(
                        deviceInfoApi.sku(),
                        fetchSportModesUseCase.fetchNameSetting(
                            sharedViewModel.getCurrentGroup(),
                            sharedViewModel.getCurrentSettings()
                        ).doOnSuccess { setting ->
                            sharedViewModel.setNameSetting(setting)
                        }
                            .ignoreElement()
                            .toSingleDefault(displayAndFieldsList)
                    ) { sku: String, nameSettingsResult: List<Pair<Display, List<Field>>> ->
                        Pair(sku, nameSettingsResult)
                    }
                }
                .map { (sku, displayAndFieldsList) ->
                    sharedViewModel.currentDisplayCount = displayAndFieldsList.size
                    displaysHolder.clear()
                    fieldsHolder.clear()
                    SportModeEditDisplaysHeaderItem(
                        sharedViewModel.suuntoDeviceType,
                        sharedViewModel.isNameChangeSupported(),
                        sharedViewModel.getCurrentName(),
                        sharedViewModel.minNameLength,
                        sharedViewModel.maxNameLength,
                        displayAndFieldsList.map { (display, fields) ->
                            displaysHolder.add(display)
                            fieldsHolder.add(fields)
                            display
                        },
                        sharedViewModel.maxDisplayCount,
                        sharedViewModel.lastSelectedDisplayIndex,
                        sportModeDisplayChangeListener = this,
                        sportModeNameChangeListener = this,
                        sku = sku
                    )
                }
                .toFlowable(BackpressureStrategy.LATEST)
                .retryWhen { errors ->
                    handleError(errors)
                }
                .observeOn(mainThread)
                .subscribe({ header ->
                    val display = displaysHolder.first()
                    saveDelegate = header
                    headerItem = header
                    onDisplayChanged(sharedViewModel.lastSelectedDisplayIndex, display.id)
                    notifyDataLoaded(listOf(Section(header)))
                    updatingDisplay = false
                }, { throwable ->
                    Timber.w(throwable, "Failed to fetch displays and fields from the component")
                })
        )
    }

    /**
     * Saves the customized sport mode to the watch
     */
    fun saveSportMode() {
        notifyChangesInProgress()
        trackSportModeSyncModeEvent()
        saveDelegate?.disableInput()
        val startTime = System.currentTimeMillis()
        disposables.add(
            sharedViewModel.saveSportMode()
                .retryWhen { errors ->
                    handleError(
                        errors.map { error ->
                            if (error is TimeoutException) {
                                return@map SaveFailedException(error)
                            }
                            return@map error
                        }
                    )
                }
                .subscribeOn(ioThread)
                .observeOn(mainThread)
                .doOnTerminate {
                    saveDelegate?.enableInput()
                    notifyChangesDone()
                }
                .subscribe({
                    trackSportModeSyncSuccessfulEvent(startTime)
                    showSaveCompleteEvent.call()
                }, {
                    trackSportModeSyncErrorEvent(startTime, it)
                    Timber.w(it)
                })
        )
    }

    /**
     * Showing alert
     */
    override fun askToSaveChanges() {
        showNonSavedChangesAlertEvent.call()
    }

    /**
     * Updates watch availability state (aka whether watch is not busy or syncing or disconnected).
     * After calls update on save button availability
     */
    override fun setWatchAvailableState(available: Boolean) {
        isWatchAvailable = available
        updateSaveAvailability()
    }

    /**
     * Method that updates save button availability on edit displays fragment based on watch availability and validation
     * of sport mode name input
     */
    private fun updateSaveAvailability() {
        saveEnabled.postValue(isWatchAvailable && isInputValid)
    }

    private fun activityName() = ActivityType.valueOf(getStIdForMcId(sharedViewModel.activityId)).simpleName

    //region Analytics
    fun trackCustomizationScreenEvent() {
        amplitudeAnalyticsTracker.trackEvent(AnalyticsEvent.SPORT_MODE_CUSTOMIZATION_SCREEN, defaultWatchProperties())
        emarsysAnalytics.trackEventWithProperties(
            AnalyticsEvent.SPORT_MODE_CUSTOMIZATION_SCREEN,
            defaultWatchProperties().map
        )
    }

    fun trackEditCancelEvent() {
        val properties = defaultWatchProperties()
            .put(AnalyticsEventProperty.ACTIVITY_TYPE, activityName())
        amplitudeAnalyticsTracker.trackEvent(AnalyticsEvent.SPORT_MODE_EDIT_CANCELLED, properties)
    }

    private fun trackSportModeDisplayNavigationEvent() {
        val properties = AnalyticsProperties()
            .put(
                AnalyticsEventProperty.SPORT_MODE_DISPLAY_NAVIGATION_TYPE,
                AnalyticsPropertyValue.SportModeMethod.SWIPE
            )
            .put(AnalyticsEventProperty.SPORT_MODE_TOTAL_DISPLAYS, sharedViewModel.currentDisplayCount)
        amplitudeAnalyticsTracker.trackEvent(AnalyticsEvent.SPORT_MODE_DISPLAY_NAVIGATION, properties)
    }

    private fun trackSportModeSyncModeEvent() {
        val properties = defaultWatchPropertiesWithSerial()
            .put(AnalyticsEventProperty.SPORT_MODE_SYNC_TYPE, syncType())
            .put(AnalyticsEventProperty.ACTIVITY_TYPE, activityName())
        for (displayIndex in 0 until displaysHolder.size) {
            val fieldNameList = fieldsHolder[displayIndex].map { it.id }
            val displayTypeName = String.format(Locale.US, AnalyticsEventProperty.SPORT_MODE_DISPLAY_TYPE, displayIndex + 1)
            val displayFieldsName = String.format(Locale.US, AnalyticsEventProperty.SPORT_MODE_DISPLAY_FIELDS, displayIndex + 1)
            properties.put(displayTypeName, displaysHolder[displayIndex].id)
                .put(displayFieldsName, fieldNameList.toString())
        }
        amplitudeAnalyticsTracker.trackEvent(AnalyticsEvent.SPORT_MODE_SYNC_MODE, properties)
        emarsysAnalytics.trackEventWithProperties(AnalyticsEvent.SPORT_MODE_SYNC_MODE, properties.map)
    }

    private fun trackSportModeDeleteDisplayEvent(displayId: String) {
        val properties = AnalyticsProperties()
            .put(AnalyticsEventProperty.ACTIVITY_TYPE, activityName())
            .put(AnalyticsEventProperty.SPORT_MODE_DISPLAY_TYPE_WITHOUT_INDEX, displayId)
            .put(AnalyticsEventProperty.SPORT_MODE_TOTAL_DISPLAYS, sharedViewModel.currentDisplayCount - 1)
        amplitudeAnalyticsTracker.trackEvent(AnalyticsEvent.SPORT_MODE_DELETE_DISPLAY, properties)
    }

    private fun syncType() = if (sharedViewModel.isNewMode) {
        AnalyticsPropertyValue.SportModeSyncType.NEW_SPORT_MODE
    } else {
        AnalyticsPropertyValue.SportModeSyncType.CUSTOM_MODE_EDIT
    }

    private fun trackSportModeSyncSuccessfulEvent(startTime: Long) {
        val endTime = System.currentTimeMillis()
        // Format duration in seconds using two decimals
        val duration = String.format(Locale.US, "%.2f", (endTime - startTime) / 1000.0f)
        val successEventProperties = defaultWatchPropertiesWithSerial()
            .put(AnalyticsEventProperty.SPORT_MODE_SYNC_TYPE, syncType())
            .put(AnalyticsEventProperty.SYNC_DURATION, duration)
        amplitudeAnalyticsTracker.trackEvent(AnalyticsEvent.SPORT_MODE_SYNC_SUCCESSFUL, successEventProperties)
        emarsysAnalytics.trackEventWithProperties(
            AnalyticsEvent.SPORT_MODE_SYNC_SUCCESSFUL,
            successEventProperties.map
        )
    }

    private fun trackSportModeSyncErrorEvent(startTime: Long, throwable: Throwable) {
        val endTime = System.currentTimeMillis()
        // Format duration in seconds using two decimals
        val duration = String.format(Locale.US, "%.2f", (endTime - startTime) / 1000.0f)
        val errorEventProperties = defaultWatchPropertiesWithSerial()
            .put(AnalyticsEventProperty.SPORT_MODE_SYNC_TYPE, syncType())
            .put(AnalyticsEventProperty.SYNC_DURATION, duration)
            .put(AnalyticsEventProperty.ERROR_TYPE, throwable.message)
        amplitudeAnalyticsTracker.trackEvent(AnalyticsEvent.SPORT_MODE_SYNC_ERROR, errorEventProperties)
        emarsysAnalytics.trackEventWithProperties(
            AnalyticsEvent.SPORT_MODE_SYNC_ERROR,
            errorEventProperties.map
        )
    }
    //endregion
}
