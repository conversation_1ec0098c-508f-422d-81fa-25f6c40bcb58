package com.stt.android.watch.preference.direction

import com.stt.android.watch.preference.PreferenceDirection
import com.stt.android.watch.preference.SetupPreference
import com.stt.android.watch.preference.SetupPreferenceReducer

class CheckDirectionReducer(private val isLeft: Boolean) : SetupPreferenceReducer {
    override val reduce: suspend (SetupPreference) -> SetupPreference = {
        it.copy(
            direction = if (isLeft) {
                PreferenceDirection.LEFT
            } else {
                PreferenceDirection.RIGHT
            }
        )
    }
}
