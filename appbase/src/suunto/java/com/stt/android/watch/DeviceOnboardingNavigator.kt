package com.stt.android.watch

import android.content.Context
import android.content.Intent
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoDeviceType

interface DeviceOnboardingNavigator {
    fun newOnboardingActivityIntent(
        context: Context,
        deviceType: SuuntoDeviceType
    ): Intent?

    fun newOnboardingIntroIntent(
        context: Context,
        deviceType: SuuntoDeviceType
    ): Intent
}
