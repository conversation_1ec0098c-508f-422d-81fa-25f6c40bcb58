package com.stt.android.watch.sportmodes.editdisplays

import android.content.res.Resources
import android.net.Uri
import coil3.load
import com.stt.android.R
import com.stt.android.common.ui.BaseBindableItem
import com.stt.android.data.source.local.sportmodes.SportModesFileStorage.Companion.SPORT_MODE_PATH
import com.stt.android.databinding.ItemEditDisplaysDisplayBinding
import com.stt.android.domain.sportmodes.Display
import java.io.File

data class SportModeEditDisplaysDisplayItem(
    val display: Display,
    private val clickHandler: SportModeEditDisplaysClickHandler
) : BaseBindableItem<ItemEditDisplaysDisplayBinding>() {

    override fun getLayout() = R.layout.item_edit_displays_display

    override fun bind(viewBinding: ItemEditDisplaysDisplayBinding, position: Int) {
        super.bind(viewBinding, position)
        val context = viewBinding.displayImage.context
        val displayWidth = context.resources.getDimensionPixelSize(R.dimen.watch_display_size)
        val padding = (Resources.getSystem().displayMetrics.widthPixels - displayWidth) / 8
        if (display.icon.placeholder.isNullOrEmpty() && display.icon.values.isEmpty()) {
            viewBinding.displayImage.setImageResource(R.drawable.add_display)
        } else {
            val path: String = if (display.icon.placeholder.isNullOrEmpty()) {
                context.filesDir.absolutePath + SPORT_MODE_PATH + display.icon.values
            } else {
                context.filesDir.absolutePath + SPORT_MODE_PATH + display.icon.placeholder
            }
            viewBinding.displayImage.load(Uri.fromFile(File(path)))
        }
        viewBinding.displayImage.apply {
            layoutParams.width = layoutParams.width + padding * 2
            setPadding(padding, 0, padding, 0)
            setOnClickListener { displayImage -> clickHandler.onDisplayClicked(displayImage, position) }
        }
    }
}
