package com.stt.android.watch

import android.content.SharedPreferences
import com.stt.android.di.SuuntoSharedPrefs
import com.stt.android.utils.STTConstants
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoDeviceType
import javax.inject.Inject

class IsRunDeviceUseCase @Inject constructor(
    @SuuntoSharedPrefs private val suuntoSharedPrefs: SharedPreferences
) {

    operator fun invoke() = getDeviceType(suuntoSharedPrefs).isRunDevice

    private fun getVariantName(sharedPreferences: SharedPreferences): String =
        sharedPreferences.getString(STTConstants.SuuntoPreferences.KEY_SUUNTO_PAIRED_WATCH_MODEL, "") ?: ""


    private fun getDeviceType(sharedPreferences: SharedPreferences): SuuntoDeviceType {
        return SuuntoDeviceType.fromVariantName(getVariantName(sharedPreferences))
    }

}
