package com.stt.android.watch.forcedupdate

import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.firstpairing.FirstPairingInfoUseCase
import com.stt.android.utils.toV2
import com.stt.android.watch.MissingCurrentWatchException
import com.stt.android.watch.SuuntoWatchModel
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoDeviceType
import kotlinx.coroutines.rx2.await
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject

class ShouldShowOnboardingUseCase @Inject constructor(
    private val suuntoWatchModel: SuuntoWatchModel,
    private val firstPairingInfoUseCase: FirstPairingInfoUseCase,
    private val dispatchers: CoroutinesDispatchers
) {

    suspend fun checkNotOnboardedDevice(): SuuntoDeviceType? = withContext(dispatchers.io) {
        runSuspendCatching {
            val currentDeviceType = suuntoWatchModel.currentWatch.toV2().await().suuntoBtDevice.deviceType

            val isOnboardingShown =
                firstPairingInfoUseCase.isOnboardingEverShown(currentDeviceType).await()
            if (isOnboardingShown) return@runSuspendCatching null

            val hasForcedUpdateCapability =
                suuntoWatchModel.requiresForcedUpdate(waitForConnect = true).await()

            currentDeviceType.takeIf { !hasForcedUpdateCapability }
        }.onFailure {
            if (it is MissingCurrentWatchException) {
                Timber.i("Missing current watch")
            } else {
                Timber.w(it, "Error checking not onboarded device")
            }
        }.getOrNull()
    }
}
