package com.stt.android.watch.sportmodes.create.multisport

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.OnBackPressedCallback
import androidx.activity.compose.LocalOnBackPressedDispatcherOwner
import androidx.activity.result.contract.ActivityResultContract
import androidx.appcompat.app.AppCompatActivity
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarDuration
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberUpdatedState
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.hilt.navigation.compose.hiltViewModel
import com.squareup.moshi.Moshi
import com.stt.android.R
import com.stt.android.compose.modifiers.narrowContentWithBgColors
import com.stt.android.compose.util.setContentWithM3Theme
import com.stt.android.domain.sportmodes.SportModeHeader
import com.stt.android.watch.sportmodes.create.multisport.composable.MultisportModeEditContent
import com.stt.android.watch.sportmodes.create.multisport.composable.MultisportModeEditTopBar
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class MultisportModeEditActivity : AppCompatActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentWithM3Theme {
            Screen(
                onSaved = {
                    setResult(RESULT_OK)
                    finish()
                },
            )
        }
    }

    @OptIn(ExperimentalMaterial3Api::class)
    @Composable
    private fun Screen(
        onSaved: () -> Unit,
        modifier: Modifier = Modifier,
        viewModel: MultisportModeEditViewModel = hiltViewModel(),
    ) {
        val currentState by viewModel.state.collectAsState()
        val state = currentState
        val unableToEditReason by viewModel.unableToEditReasonFlow.collectAsState(null)

        val backCallback = rememberUpdatedState(newValue = {
            when (state) {
                is MultisportModeEditViewState.SelectParent -> finish()
                is MultisportModeEditViewState.SelectChildren ->
                    if (state.parentSportMode != null) {
                        viewModel.handleIntent(MultisportModeEditIntent.ConfirmSelectedChildren)
                    } else {
                        viewModel.handleIntent(MultisportModeEditIntent.BackToParentSelection)
                    }

                is MultisportModeEditViewState.Confirm ->
                    when {
                        state.loading -> { /* Do nothing */ }
                        state.parentSportMode != null -> finish()
                        else -> viewModel.handleIntent(MultisportModeEditIntent.BackToChildSelection)
                    }
            }
        })

        val dispatcher = LocalOnBackPressedDispatcherOwner.current?.onBackPressedDispatcher
        DisposableEffect(Unit) {
            val callback = object : OnBackPressedCallback(true) {
                override fun handleOnBackPressed() {
                    backCallback.value()
                }
            }
            dispatcher?.addCallback(callback)
            onDispose {
                callback.remove()
            }
        }

        val context = LocalContext.current
        val snackbarHostState = remember { SnackbarHostState() }
        var scrollToChild by remember { mutableStateOf<MultisportChild?>(null) }

        LaunchedEffect(Unit) {
            viewModel.events.collect { event ->
                when (event) {
                    is MultisportModeEditEvent.Saved -> {
                        onSaved()
                    }

                    is MultisportModeEditEvent.Error -> {
                        snackbarHostState.showSnackbar(
                            context.getString(R.string.error_generic),
                            duration = SnackbarDuration.Short,
                        )
                    }

                    is MultisportModeEditEvent.ScrollToChild -> {
                        scrollToChild = event.multisportChild
                    }
                }
            }
        }

        Scaffold(
            topBar = {
                MultisportModeEditTopBar(
                    state = state,
                    onBackClick = backCallback.value,
                    onSaveClick = {
                        if (state is MultisportModeEditViewState.SelectChildren) {
                            viewModel.handleIntent(MultisportModeEditIntent.ConfirmSelectedChildren)
                        } else {
                            viewModel.handleIntent(MultisportModeEditIntent.Confirm)
                        }
                    },
                    unableToEditReason = unableToEditReason,
                )
            },
            snackbarHost = {
                SnackbarHost(hostState = snackbarHostState)
            },
            modifier = modifier,
        ) { internalPadding ->
            MultisportModeEditContent(
                state = state,
                onIntent = viewModel::handleIntent,
                scrollToChild = scrollToChild,
                onScrollToChildConsumed = { scrollToChild = null },
                modifier = Modifier
                    .padding(internalPadding)
                    .narrowContentWithBgColors(
                        backgroundColor = MaterialTheme.colorScheme.surface,
                        outerBackgroundColor = MaterialTheme.colorScheme.background,
                    ),
            )
        }

        unableToEditReason?.let {
            val message = stringResource(id = it)
            LaunchedEffect(snackbarHostState) {
                snackbarHostState.showSnackbar(
                    message = message,
                    duration = SnackbarDuration.Indefinite,
                )
            }
        }
    }

    class ResultContract : ActivityResultContract<MultisportModeEditInput, Boolean>() {
        override fun createIntent(context: Context, input: MultisportModeEditInput): Intent =
            newStartIntent(context, input.currentSportModesJson, input.sportModeHeader)

        override fun parseResult(resultCode: Int, intent: Intent?): Boolean {
            return resultCode == RESULT_OK
        }
    }

    data class MultisportModeEditInput(
        val currentSportModesJson: String,
        val sportModeHeader: SportModeHeader? = null
    )

    companion object {
        internal const val EXTRA_CURRENT_SPORT_MODES_JSON = "currentSportModesJson"
        internal const val EXTRA_SPORT_MODE = "sportMode"

        private val moshi = Moshi.Builder().build()

        private fun newStartIntent(
            context: Context,
            currentSportModesJson: String,
            sportModeHeader: SportModeHeader? = null
        ): Intent {
            return Intent(context, MultisportModeEditActivity::class.java).apply {
                putExtra(EXTRA_CURRENT_SPORT_MODES_JSON, currentSportModesJson)
                sportModeHeader?.let {
                    putExtra(
                        EXTRA_SPORT_MODE,
                        moshi.adapter(SportModeHeader::class.java).toJson(sportModeHeader)
                    )
                }
            }
        }
    }
}

