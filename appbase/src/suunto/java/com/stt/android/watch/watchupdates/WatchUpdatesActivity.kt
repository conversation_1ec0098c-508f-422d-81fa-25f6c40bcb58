package com.stt.android.watch.watchupdates

import android.app.Activity
import android.content.ActivityNotFoundException
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.MenuItem
import androidx.appcompat.app.AppCompatActivity
import androidx.core.net.toUri
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import com.stt.android.R
import com.stt.android.common.ui.SimpleDialogFragment
import com.stt.android.databinding.ActivityWatchUpdatesBinding
import com.stt.android.utils.getEnumExtra
import com.stt.android.utils.putEnumExtra
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoDeviceType
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber

@AndroidEntryPoint
class WatchUpdatesActivity : AppCompatActivity(), WatchUpdatesFragment.Listener {
    lateinit var binding: ActivityWatchUpdatesBinding
    lateinit var suuntoDeviceType: SuuntoDeviceType

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding =
            DataBindingUtil.setContentView(this, R.layout.activity_watch_updates)
        suuntoDeviceType = intent.getEnumExtra(
            KEY_EXTRA_SUUNTO_DEVICE_TYPE,
            SuuntoDeviceType::class.java,
            SuuntoDeviceType.Unrecognized
        )
        setSupportActionBar(binding.toolbar)
        supportActionBar?.let {
            it.setDisplayShowHomeEnabled(false)
            it.setDisplayHomeAsUpEnabled(true)
        }
        // By default result is OK unless DataLoadFailed() is called.
        setResult(Activity.RESULT_OK)
    }

    @Deprecated("Deprecated in Java")
    override fun onAttachFragment(fragment: Fragment) {
        if (fragment is WatchUpdatesFragment) {
            Timber.d("WatchUpdatesFragment attached")

            fragment.listener = this
        }
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        when (item.itemId) {
            android.R.id.home -> {
                onBackPressed()
                return true
            }
        }
        return super.onOptionsItemSelected(item)
    }

    override fun onDataLoadFailed() {
        Timber.d("Failed to load watch updates data.")
        setResult(Activity.RESULT_CANCELED)
        finish()
    }

    override fun onLearnMoreAboutUpdateClicked() {
        Timber.d("onLearnMoreAboutUpdateClicked")
        openUrl(getSoftwareUpdateInfoUrl())
    }

    override fun onNeedHelpClicked() {
        Timber.d("onNeedHelpClicked")
        openUrl(getNeedHelpUrl())
    }

    override fun downgradeDenied() {
        Timber.d("User denied downgrade and no update available")
        setResult(Activity.RESULT_OK)
        finish()
    }

    private fun getSoftwareUpdateInfoUrl(): String {
        return when (suuntoDeviceType) {
            SuuntoDeviceType.Suunto9,
            SuuntoDeviceType.Suunto9Lima ->
                "https://www.suunto.com/Support/Software-updates/Release-notes/suunto-9/"

            SuuntoDeviceType.Suunto9Peak ->
                "https://www.suunto.com/suunto9peak/userguide/softwareupdates"

            SuuntoDeviceType.Suunto9PeakPro ->
                "https://www.suunto.com/suunto9peakpro/userguide/softwareupdates/"

            SuuntoDeviceType.SuuntoVertical ->
                "https://www.suunto.com/suuntovertical/userguide/softwareupdates/"

            SuuntoDeviceType.Suunto5Peak ->
                "https://www.suunto.com/suunto5peak/userguide/softwareupdates"

            SuuntoDeviceType.SuuntoOcean ->
                "https://www.suunto.com/suuntoocean/userguide/softwareupdates"

            SuuntoDeviceType.SuuntoRaceS ->
                "https://www.suunto.com/suuntoraces/userguide/softwareupdates"

            SuuntoDeviceType.SuuntoRace ->
                "https://www.suunto.com/suuntorace/userguide/softwareupdates"

            SuuntoDeviceType.SuuntoRun ->
                "https://www.suunto.com/suuntorun/userguide/softwareupdates"

            SuuntoDeviceType.SuuntoGT ->
                // todo use suunto GT web_url
                ""

            SuuntoDeviceType.SuuntoRace2 ->
                // todo use suunto race 2 web_url
                ""

            SuuntoDeviceType.SuuntoVertical2 ->
                // todo use suunto vertical 2 web_url
                ""

            else -> "https://www.suunto.com/software-updates/"
        }
    }

    private fun getNeedHelpUrl(): String {
        return when (suuntoDeviceType) {
            SuuntoDeviceType.Suunto9Peak ->
                "https://www.suunto.com/Support/Product-support/suunto_9_peak/suunto_9_peak/getting-started/software-updates/"

            SuuntoDeviceType.SuuntoOcean ->
                "https://www.suunto.com/suuntoocean/userguide/softwareupdates"

            SuuntoDeviceType.SuuntoRaceS ->
                "https://www.suunto.com/suuntoraces/userguide/softwareupdates"

            SuuntoDeviceType.SuuntoRace ->
                "https://www.suunto.com/suuntorace/userguide/softwareupdates/"

            SuuntoDeviceType.SuuntoVertical ->
                "https://www.suunto.com/suuntovertical/userguide/softwareupdates/"

//            SuuntoDeviceType.SuuntoRun ->
//                // todo use suunto_run web_url
//                ""

            SuuntoDeviceType.SuuntoGT ->
                // todo use suunto GT web_url
                ""
//                ""

            SuuntoDeviceType.SuuntoRace2 ->
                // todo use suunto race 2 web_url
                ""

            SuuntoDeviceType.SuuntoVertical2 ->
                // todo use suunto vertical 2 web_url
                ""

            else ->
                // Other device types don't have a dedicated page for software updates in the
                // user guide for now. Use the same link as for "Learn more".
                getSoftwareUpdateInfoUrl()
        }
    }

    private fun openUrl(url: String) {
        try {
            val uri = url.toUri()
            @Suppress("UnsafeImplicitIntentLaunch")
            startActivity(Intent(Intent.ACTION_VIEW).setData(uri))
        } catch (e: ActivityNotFoundException) {
            val dialog = SimpleDialogFragment.newInstance(message = getString(R.string.error_0))
            dialog.show(supportFragmentManager, "GenericErrorDialog")
        }
    }

    companion object {
        private const val KEY_EXTRA_SUUNTO_DEVICE_TYPE =
            "com.stt.android.watch.watchupdates.SUUNTO_DEVICE_TYPE"

        const val KEY_EXTRA_DEEP_LINK_URL =
            "com.stt.android.watch.watchupdates.DEEP_LINK_URL"

        @JvmStatic
        fun newStartIntent(context: Context, suuntoDeviceType: SuuntoDeviceType): Intent {
            return Intent(context, WatchUpdatesActivity::class.java)
                .putEnumExtra(KEY_EXTRA_SUUNTO_DEVICE_TYPE, suuntoDeviceType)
        }

        @JvmStatic
        fun newOtaUpdateDeepLinkIntent(context: Context, deeplinkUrl: String): Intent {
            return Intent(context, WatchUpdatesActivity::class.java)
                .putExtra(KEY_EXTRA_DEEP_LINK_URL, deeplinkUrl)
                .putEnumExtra(KEY_EXTRA_SUUNTO_DEVICE_TYPE, SuuntoDeviceType.Unrecognized)
        }
    }
}
