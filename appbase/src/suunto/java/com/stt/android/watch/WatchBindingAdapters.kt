package com.stt.android.watch

import android.widget.TextView
import androidx.core.view.isVisible
import androidx.databinding.BindingAdapter
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.findViewTreeLifecycleOwner
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.stt.android.R
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch

@BindingAdapter("deviceSwitchHeader")
fun bindDeviceSwitchHeaderName(textView: TextView, state: DeviceStateEvent?) {
    // null deviceType results in device name being shown as "PAIRING". Prefer keeping the old
    // name if there's some text already in the textview to prevent flashing the PAIRING 'name'
    // in situations where the view is still briefly visible while current device disconnects
    if (state is DeviceStateUpdate && (state.deviceType != null || textView.text.isEmpty())) {
        val name = textView.context.getString(
            WatchHelper.getStringResIdForSuuntoDeviceType(state.deviceType)
        )
        textView.text = textView.context.getString(R.string.device_type_select_watch_or_dive_computer_tips, name)
    }
}

@BindingAdapter("textFlow")
fun TextView.bindText(flow: StateFlow<CharSequence>?) {
    flow?.let { stateFlow ->
        val lifecycleOwner = findViewTreeLifecycleOwner()
        lifecycleOwner?.lifecycleScope?.launch {
            lifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
                stateFlow.collect { value ->
                    text = value
                    isVisible = value.isNotEmpty()
                }
            }
        }
    }
}

