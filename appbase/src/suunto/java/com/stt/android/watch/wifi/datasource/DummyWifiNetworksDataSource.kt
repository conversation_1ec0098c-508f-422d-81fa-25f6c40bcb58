package com.stt.android.watch.wifi.datasource

import com.stt.android.watch.wifi.entity.WifiGeneralSettings
import com.stt.android.watch.wifi.entity.WifiNetworkInfo
import com.stt.android.watch.wifi.entity.WifiSecurity
import com.suunto.connectivity.repository.commands.SaveWifiNetworkResponse
import kotlinx.collections.immutable.persistentListOf
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flowOf
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class DummyWifiNetworksDataSource @Inject constructor() : WifiNetworksDataSource {

    private var savedNetworks: List<WifiNetworkInfo> = emptyList()

    override suspend fun saveWifiNetwork(
        networkInfo: WifiNetworkInfo,
        password: String
    ): SaveWifiNetworkResponse {
        delay(1000)
        return synchronized(this) {
            savedNetworks = savedNetworks.plus(networkInfo)
            SaveWifiNetworkResponse(isSuccess = true)
        }
    }

    override suspend fun forgetWifiNetwork(index: Int) {
        delay(1000)
        return synchronized(this) {
            savedNetworks = savedNetworks.drop(1)
        }
    }

    override fun getSavedWifiNetworksCount(): Flow<Int> = synchronized(this) {
        flowOf(savedNetworks.size)
    }

    override suspend fun getSavedWifiNetworks(): List<WifiNetworkInfo> {
        delay(1000)
        return synchronized(this) {
            savedNetworks
        }
    }

    override suspend fun scanAvailableWifiNetworks(): List<WifiNetworkInfo> = DUMMY_NETWORKS

    override suspend fun setWifiGeneralSettings(settings: WifiGeneralSettings) {
        // do nothing
    }

    override suspend fun setOfflineMapsUrl(url: String) {
        // do nothing
    }

    override suspend fun setAuthToken(token: String) {
        // do nothing
    }

    override fun observeWifiEnabled(): Flow<Boolean> = flowOf(true)

    override suspend fun setWifiEnabled(enabled: Boolean) {
        // do nothing
    }

    override suspend fun enableInboxWifi() {
        // do nothing
    }

    companion object {
        val DUMMY_NETWORKS = persistentListOf(
            WifiNetworkInfo("HOME", WifiSecurity.SECURE),
            WifiNetworkInfo("GUEST", WifiSecurity.OPEN),
        )
    }
}
