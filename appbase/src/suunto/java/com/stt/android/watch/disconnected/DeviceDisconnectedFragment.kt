package com.stt.android.watch.disconnected

import androidx.core.view.doOnNextLayout
import androidx.fragment.app.viewModels
import com.stt.android.R
import com.stt.android.databinding.FragmentDeviceDisconnectedBinding
import com.stt.android.watch.DeviceFragment
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class DeviceDisconnectedFragment : DeviceFragment() {

    override fun getLayoutResId() = R.layout.fragment_device_disconnected
    override val viewModel: DeviceDisconnectedViewModel by viewModels()
    private val viewDataBinding: FragmentDeviceDisconnectedBinding get() = requireBinding()

    override fun getNavId() = R.id.deviceDisconnectedFragment

    override fun getScrollFamily() = ScrollFamily.DEVICE_PAIRED
    override fun getScrollParams(): ScrollParams = ScrollParams.DevicePaired(
        viewDataBinding.deviceDisconnectedScrollview.scrollY,
        viewDataBinding.deviceActionList.root.top
    )
    override fun applyInitialScroll(scrollParams: ScrollParams) {
        viewDataBinding.deviceDisconnectedScrollview.doOnNextLayout { v ->
            val scrollToPosition = if (scrollParams is ScrollParams.DevicePaired) {
                scrollParams.getAdjustedScrollY(viewDataBinding.deviceActionList.root.top)
            } else {
                scrollParams.scrollY
            }

            v.scrollTo(0, scrollToPosition)
        }
    }

    override fun onResume() {
        super.onResume()
        sharedViewModel.setNeedNotificationAccessAlert(false)
    }
}
