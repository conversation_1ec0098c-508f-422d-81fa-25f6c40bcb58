package com.stt.android.watch.sportmodes

import android.app.Application
import androidx.annotation.WorkerThread
import com.squareup.duktape.Duktape
import com.squareup.duktape.DuktapeException
import com.stt.android.data.source.local.sportmodes.SportModesFileStorage.Companion.BUNDLE_PATH
import com.stt.android.data.source.local.sportmodes.SportModesFileStorage.Companion.SPORT_MODE_PATH
import com.stt.android.data.sportmodes.SportModeComponent
import com.stt.android.data.sportmodes.SportModeComponentManager
import com.stt.android.data.sportmodes.SportModeComponentWrapperFactory
import timber.log.Timber
import java.io.File
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.text.Charsets.UTF_8

@Singleton
class SportModeComponentManagerImpl @Inject constructor(
    private val app: Application
) : SportModeComponentManager {
    private val duktapeLogger = DuktapeLogger()
    private val clients = mutableSetOf<String>()
    private var duktape: Duktape? = null
    private var _sportModeComponent: SportModeComponent? = null

    override val sportModeComponent: SportModeComponent
        @Synchronized get() = _sportModeComponent
            ?: throw IllegalStateException("Sport mode component not initialized")

    @Synchronized
    @WorkerThread
    override fun initialize(client: String, variant: String, version: String, locale: String) {
        clients.add(client)
        if (duktape != null) {
            Timber.i("Duktape already initialized")
            return
        }
        Timber.i("Creating Duktape context")
        val newDuktape = Duktape.create().apply {
            set("console", DuktapeLoggerInterface::class.java, duktapeLogger)
            duktape = this
        }
        val bundleFile = File(app.filesDir.absolutePath + SPORT_MODE_BUNDLE_PATH)
        val compBundle = bundleFile.readText(UTF_8)
        try {
            newDuktape.evaluate(compBundle)
            _sportModeComponent =
                SportModeComponentWrapperFactory.create(newDuktape, variant, version, locale)
        } catch (error: NoSuchMethodError) {
            throw DuktapeException(
                "No such method exception inside of the duktape sport mode environment"
            )
        }
    }

    @Synchronized
    override fun close(client: String) {
        if (duktape == null) {
            Timber.i("Duktape already closed")
            return
        }
        clients.remove(client)
        if (clients.isNotEmpty()) {
            Timber.i("Duktape not closed, still used by: $clients")
            return
        }
        Timber.i("Closing Duktape")
        duktape?.close()
        duktape = null
        _sportModeComponent = null
    }

    companion object {
        private const val SPORT_MODE_BUNDLE_PATH = SPORT_MODE_PATH + BUNDLE_PATH
    }
}
