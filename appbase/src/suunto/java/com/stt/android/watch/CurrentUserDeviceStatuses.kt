package com.stt.android.watch

import android.content.SharedPreferences
import androidx.core.content.edit
import com.stt.android.controllers.CurrentUserController
import com.stt.android.utils.STTConstants
import com.suunto.connectivity.Spartan
import javax.inject.Inject

/**
 * Helper classes used by SuuntoWatchModel for storing device and user specific
 * statuses to preferences.
 */
abstract class CurrentUserAndDeviceStatus(
    val sharedPreferences: SharedPreferences,
    val currentUserController: CurrentUserController,
    val preferenceKey: String
) {
    fun clear() {
        sharedPreferences.edit {
            putString(preferenceKey, "")
        }
    }

    protected fun set(value: String) {
        sharedPreferences.edit {
            putString(preferenceKey, value)
        }
    }

    protected fun get(value: String): Boolean {
        val storedValue =
            sharedPreferences.getString(preferenceKey, "")
        return value == storedValue
    }
}

class BackendSyncedItemsSentToConnectivityStatus @Inject constructor(
    sharedPreferences: SharedPreferences,
    currentUserController: CurrentUserController
) : CurrentUserAndDeviceStatus(
    sharedPreferences,
    currentUserController,
    STTConstants.SuuntoPreferences.KEY_SUUNTO_BACKEND_SYNCED_ITEMS_SENT_TO_CONNECTIVITY_STATUS
) {
    fun set(device: Spartan) {
        val userAndDeviceIdentification =
            device.suuntoBtDevice.serial + currentUserController.currentUser.username
        set(userAndDeviceIdentification)
    }

    fun get(device: Spartan): Boolean {
        val userAndDeviceIdentification =
            device.suuntoBtDevice.serial + currentUserController.currentUser.username
        return get(userAndDeviceIdentification)
    }
}

class BackendSyncedAtLeastOnceStatus @Inject constructor(
    sharedPreferences: SharedPreferences,
    currentUserController: CurrentUserController
) : CurrentUserAndDeviceStatus(
    sharedPreferences,
    currentUserController,
    STTConstants.SuuntoPreferences.KEY_SUUNTO_BACKEND_SYNCED_AT_LEAST_ONCE_STATUS
) {
    fun set() {
        set(currentUserController.currentUser.username)
    }

    fun get(): Boolean {
        return get(currentUserController.currentUser.username)
    }
}
