package com.stt.android.watch.background

import com.google.android.gms.maps.model.LatLng
import com.google.maps.android.PolyUtil
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.Point
import com.stt.android.domain.workout.Workout
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.domain.workouts.attributes.AddWorkoutAttributesUpdateUseCase
import com.stt.android.domain.workouts.autolocation.IsAutoLocationEnabledUseCase
import com.stt.android.location.LastLocationRequest
import com.stt.android.location.LocationModel
import timber.log.Timber
import javax.inject.Inject
import kotlin.time.Duration.Companion.minutes

class SetDiveAutoLocationUseCase @Inject constructor(
    private val locationModel: LocationModel,
    private val isAutoLocationEnabledUseCase: IsAutoLocationEnabledUseCase,
    private val addWorkoutAttributesUpdateUseCase: AddWorkoutAttributesUpdateUseCase,
) {
    /**
     * Returned boolean is true if automatic location is added.
     */
    suspend operator fun invoke(workout: Workout): Pair<Workout, Boolean> = runSuspendCatching {
        val workoutHeader = workout.header
        // If not a dive or dive already has location (dive from Seal), do nothing.
        if (!workoutHeader.activityType.isDiving ||
            (workoutHeader.startPosition != null && workoutHeader.stopPosition != null)) {
            return workout to false
        }

        // Auto location not enabled, do nothing.
        if (!isAutoLocationEnabledUseCase()) {
            return workout to false
        }

        val lastKnownLocation = locationModel.getLastLocation(
            LastLocationRequest.builder()
                .skipPassiveProvider(false)
                .timeInMilliSecondsSinceEpoch(workoutHeader.startTime)
                .build()
        ) ?: return workout to false // No location, do nothing.

        // We cannot add location to dives that have ended over 30 minutes ago without user consent.
        val isWorkoutTooOld = System.currentTimeMillis() - workoutHeader.stopTime >= 30.minutes.inWholeMilliseconds

        // Adds a workout attribute update to be sent to the backend later
        addWorkoutAttributesUpdateUseCase(
            params = addWorkoutAttributesUpdateUseCase.getLocationUpdateParams(
                workoutId = workoutHeader.id,
                username = workoutHeader.username,
                latitude = lastKnownLocation.latitude,
                longitude = lastKnownLocation.longitude,
                requiresUserConfirmation = isWorkoutTooOld,
            )
        )

        // If workout is too old, we only store a pending update that requires confirmation from the user.
        if (isWorkoutTooOld) {
            return workout to false
        }

        val updatedWorkoutHeader = workoutHeader.update(
            latitude = lastKnownLocation.latitude,
            longitude = lastKnownLocation.longitude,
        )
        val updatedWorkout = Workout(
            updatedWorkoutHeader,
            workout.data,
            workout.pictures,
            workout.extensions
        )
        updatedWorkout to true
    }.getOrElse { e ->
        Timber.w(e, "Failed to set auto location for dive")
        workout to false
    }

    private companion object {
        fun WorkoutHeader.update(
            latitude: Double,
            longitude: Double,
        ): WorkoutHeader {
            val polyline = PolyUtil.encode(mutableListOf(LatLng(latitude, longitude)))
            val position = Point(longitude, latitude, null, 0.0)
            // Below are the fields the backend will update when the workout location changes.
            // Let's do the same updates to local header.
            return toBuilder()
                .startPosition(position)
                .centerPosition(position)
                .stopPosition(position)
                .polyline(polyline)
                .build()
        }
    }
}
