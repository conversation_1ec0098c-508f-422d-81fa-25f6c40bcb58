package com.stt.android.watch.sportmodes

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.viewModelScope
import com.jakewharton.rxrelay2.BehaviorRelay
import com.stt.android.R
import com.stt.android.common.ui.RxViewModel
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.device.ConnectedWatchState
import com.stt.android.domain.device.DeviceConnectionStateUseCase
import com.stt.android.domain.di.IoThread
import com.stt.android.domain.di.MainThread
import com.stt.android.domain.sportmodes.Group
import com.stt.android.domain.sportmodes.InitSportModeComponentUseCase
import com.stt.android.domain.sportmodes.Mode
import com.stt.android.domain.sportmodes.SaveSportModesUseCase
import com.stt.android.domain.sportmodes.Setting
import com.stt.android.domain.sportmodes.SupportMode
import com.stt.android.domain.sportmodes.WatchSportMode
import com.stt.android.utils.getBooleanExtra
import com.stt.android.utils.getEnumExtra
import com.stt.android.watch.sportmodes.editdisplays.SaveChangesCallback
import com.stt.android.watch.sportmodes.list.ToolbarDelegate
import com.stt.android.watch.sportmodes.mappers.SportModeJsonEditor
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoDeviceType
import dagger.hilt.android.lifecycle.HiltViewModel
import io.reactivex.Completable
import io.reactivex.Scheduler
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

/**
 * Class used to keep the state of sport mode being customized in the flow
 */
@HiltViewModel
class SportModeHolderViewModel
@Inject constructor(
    savedStateHandle: SavedStateHandle,
    private val sportModeJsonEditor: SportModeJsonEditor,
    private val saveSportModesUseCase: SaveSportModesUseCase,
    private val deviceConnectionStateUseCase: DeviceConnectionStateUseCase,
    private val initSportModeComponentUseCase: InitSportModeComponentUseCase,
    private val toolbarDelegate: ToolbarDelegate,
    @IoThread ioThread: Scheduler,
    @MainThread mainThread: Scheduler
) : RxViewModel(ioThread, mainThread) {
    var supportMode: SupportMode = savedStateHandle.getEnumExtra(
        SportModeActivity.SUPPORT_MODE,
        SupportMode::class.java,
        SupportMode.NOT_SUPPORTED
    )

    var fteCompleted: Boolean =
        savedStateHandle.getBooleanExtra(SportModeActivity.FTE_COMPLETED, false)

    var suuntoDeviceType: SuuntoDeviceType = savedStateHandle.getEnumExtra(
        SportModeActivity.SUUNTO_DEVICE_TYPE,
        SuuntoDeviceType::class.java,
        SuuntoDeviceType.Unrecognized
    )
    var watchFirmware: String = savedStateHandle.get(SportModeActivity.WATCH_FIRMWARE) ?: ""

    var watchSerialNumber: String =
        savedStateHandle.get(SportModeActivity.WATCH_SERIAL_NUMBER) ?: ""

    val actionModeEvent = toolbarDelegate.actionModeEvent
    var activityId: Int = 0
    private var selectedModeId: Int = 0
    var saveChangesCallback: SaveChangesCallback? = null
    var isNewMode = false

    private var group: Group = Group()

    private var modesMap: MutableMap<Int, Mode?> = emptyMap<Int, Mode?>().toMutableMap()

    // Relays for display/settings/group jsons for keeping the UI up to date
    val displaysRelay: BehaviorRelay<String> = BehaviorRelay.createDefault("")
    val settingsRelay: BehaviorRelay<String> = BehaviorRelay.createDefault("")
    val groupRelay: BehaviorRelay<String> = BehaviorRelay.createDefault("")

    var minDisplayCount = 0
    var currentDisplayCount = 0
    var maxDisplayCount = 0
    var minNameLength = 0
    var maxNameLength = 0

    // holds the last selected display index
    var lastSelectedDisplayIndex: Int = 0

    private var initialDisplays: String = ""
    private var initialSettings: String = ""
    private var initialGroup: String = ""
    private var refreshModesList: Boolean = true

    // Flag that decides whether we need to clear our relays when we proceed to edit displays screen
    private var clearModel = true

    // Live data to present snackbar regarding tricky watch states
    val connectedWatchState = MutableLiveData<ConnectedWatchState>()

    private val _title = MutableLiveData<String>()
    val title: LiveData<String> = _title

    fun setTitle(title: String) {
        _title.value = title
    }

    init {
        disposables.add(
            deviceConnectionStateUseCase.connectedWatchState()
                .subscribe({ watchState: ConnectedWatchState ->
                    saveChangesCallback?.setWatchAvailableState(watchState.isNormalState())
                    connectedWatchState.postValue(watchState)
                }, {
                    Timber.w(it, "Cannot fetch watch state")
                })
        )
        viewModelScope.launch {
            runSuspendCatching {
                initSportModeComponentUseCase.init(TAG)
            }.onFailure { e ->
                Timber.w(e, "Error initialising sport mode component")
            }
        }
    }

    override fun onCleared() {
        super.onCleared()
        initSportModeComponentUseCase.close(TAG)
    }

    /**
     * This function is for the case where we pick custom sport mode from the list of existing ones,
     * since SportModeHeader object contains only array of mode ids.
     * Most of the time we will have only a single mode id.
     */
    fun setModeIds(modeIds: List<Int>) {
        modesMap.clear()
        for (id in modeIds) {
            modesMap[id] = null
        }
        selectedModeId = modeIds.first()
    }

    /**
     * This function is for the case where we create a new custom sport mode,
     * we put modes from freshly created template to map
     * Most of the time we will have only a single mode.
     */
    fun setModes(modes: List<Mode>, initialSetup: Boolean = false) {
        isNewMode = true
        modesMap.clear()
        for (mode in modes) {
            modesMap[mode.id] = mode
        }
        selectedModeId = modes.first().id
        setCurrentDisplays(modes.first().displays)
        setCurrentSettings(modes.first().settings)
        if (initialSetup) {
            initialDisplays = modes.first().displays
            initialSettings = modes.first().settings
        }
        setClearModel(initialSetup)
    }

    fun setClearModel(needToClearModel: Boolean) {
        clearModel = needToClearModel
    }

    /**
     * Returns a string that contains json representing current mode's displays
     */
    fun getCurrentDisplays(): String {
        return displaysRelay.value ?: ""
    }

    /**
     * Updates json string with current displays
     */
    fun setCurrentDisplays(watchSportModeDisplays: String, initialSetup: Boolean = false) {
        val mode = modesMap[selectedModeId]
        mode?.let {
            modesMap.put(selectedModeId, it.copy(displays = watchSportModeDisplays))
        } ?: modesMap.put(selectedModeId, Mode(selectedModeId, "", watchSportModeDisplays))
        if (displaysRelay.value != watchSportModeDisplays) {
            displaysRelay.accept(watchSportModeDisplays)
        }
        if (initialSetup) {
            initialDisplays = watchSportModeDisplays
        }
    }

    /**
     * Updates json string with currently represented group (mainly for storing the name)
     */
    fun setGroup(group: Group, initialSetup: Boolean = false) {
        this.group = group
        if (initialSetup) {
            initialGroup = group.value ?: ""
        }
    }

    /**
     * Returns a string representing sportmodes group json
     */
    fun getCurrentGroup(): String {
        return group.value ?: ""
    }

    /**
     * Updates display and settings with fresh jsons
     * This method is used after we delete/add/change display layout
     */
    fun setCurrentMode(watchSportMode: WatchSportMode) {
        modesMap.put(selectedModeId, watchSportMode.modes.first().copy(id = selectedModeId))
        val mode = modesMap[selectedModeId]
        val group = group.value

        if (mode != null && group != null) {
            displaysRelay.accept(mode.displays)
            settingsRelay.accept(mode.settings)
            groupRelay.accept(group)
        }
    }

    /**
     * Returns a string representing sportmodes settings json
     * We currently do not change or edit settings, so it is only used for proper fetching displays
     * with navigation screen
     */
    fun getCurrentSettings(): String {
        return settingsRelay.value ?: ""
    }

    /**
     * Updates settings values for selected sport mode. Right now only used on initial load of sport mode's displays
     */
    fun setCurrentSettings(watchSportModeSettings: String, initialSetup: Boolean = false) {
        modesMap[selectedModeId] = modesMap[selectedModeId]?.copy(settings = watchSportModeSettings)
            ?: modesMap.put(selectedModeId, Mode(selectedModeId, watchSportModeSettings, ""))
        if (settingsRelay.value != watchSportModeSettings) {
            settingsRelay.accept(watchSportModeSettings)
        }
        if (initialSetup) {
            initialSettings = watchSportModeSettings
        }
    }

    /**
     * Clearing the state of SportModeHolder in case if it is not a new Sport Mode
     */
    fun clear() {
        isNewMode = false
        if (clearModel) {
            modesMap.clear()
            displaysRelay.accept("")
            settingsRelay.accept("")
            groupRelay.accept("")
            setGroup(Group())
            initialSettings = ""
            initialDisplays = ""
            initialGroup = ""
        }
    }

    /**
     * Returns sport mode's name
     */
    fun getCurrentName(): String {
        return sportModeJsonEditor.getGroupNameAsString(group)
    }

    /**
     * Updates the name of sport mode object
     */
    fun updateName(name: String) {
        group = sportModeJsonEditor.getGroupWithUpdatedName(name, group)
    }

    /**
     * Updates the group relay with current value to trigger the update on UI. This is actually used only in cases
     * where user goes to switch display or field
     */
    fun updateGroupRelay() {
        val value = group.value
        if (value != null) {
            groupRelay.accept(value)
        }
    }

    /**
     * Check which parts are changed and proceeds to save
     * If changes are present we are setting that we need to refresh sport modes list afterwards
     */
    fun saveSportMode(): Completable {
        val displayChanged = isNewMode || initialDisplays.isEmpty() || sportModeJsonEditor.hasChanged(initialDisplays, getCurrentDisplays())
        val settingsChanged = isNewMode || initialSettings.isEmpty() || sportModeJsonEditor.hasChanged(initialSettings, getCurrentSettings())
        val groupChanged = isNewMode || initialGroup.isEmpty() || sportModeJsonEditor.hasChanged(initialGroup, getCurrentGroup())
        refreshModesList = refreshModesList || displayChanged || settingsChanged || groupChanged

        Timber.d("saveSportMode displayChanged=$displayChanged settingsChanged=$settingsChanged groupChanged=$groupChanged refreshModesList=$refreshModesList")
        return saveSportModesUseCase.saveSportMode(
            group,
            modesMap.values.filterNotNull().toTypedArray(),
            displayChanged,
            settingsChanged,
            groupChanged
        )
            .doOnComplete {
                initialDisplays = getCurrentDisplays()
                initialSettings = getCurrentSettings()
                initialGroup = getCurrentGroup()
            }
    }

    /**
     * Returns whether we need to refresh sport modes list
     */
    fun needsRefresh(): Boolean {
        return refreshModesList
    }

    /**
     * Notifies that we have successfully loaded sport modes list and we don't need to update it if no changes
     * have been done to the sport modes
     */
    fun setModesLoaded() {
        refreshModesList = false
    }

    fun setNameSetting(setting: Setting) {
        sportModeJsonEditor.getNameLengthLimits(setting).apply {
            minNameLength = first
            maxNameLength = second
        }
    }

    fun isNameChangeSupported(): Boolean {
        return supportMode == SupportMode.SUPPORTED
    }

    private fun hasChanged(): Boolean {
        val displayChanged =
            isNewMode && initialDisplays.isNotEmpty() && sportModeJsonEditor.hasChanged(
                initialDisplays,
                getCurrentDisplays()
            )
        val settingsChanged =
            isNewMode && initialSettings.isNotEmpty() && sportModeJsonEditor.hasChanged(
                initialSettings,
                getCurrentSettings()
            )
        val groupChanged = isNewMode && initialGroup.isNotEmpty() && sportModeJsonEditor.hasChanged(
            initialGroup,
            getCurrentGroup()
        )
        return displayChanged || settingsChanged || groupChanged
    }

    fun shouldShowNonSavedChangesAlert(currentDestinationId: Int?): Boolean {
        return if (currentDestinationId == R.id.sportModeEditDisplaysFragment && hasChanged()) {
            saveChangesCallback?.askToSaveChanges()
            true
        } else {
            false
        }
    }

    companion object {
        private const val TAG = "SportModeHolderViewModel"
    }
}
