package com.stt.android.watch

import android.content.SharedPreferences
import android.os.Bundle
import android.view.Menu
import android.view.MenuInflater
import android.view.View
import androidx.annotation.IdRes
import androidx.annotation.StringRes
import androidx.fragment.app.activityViewModels
import androidx.navigation.NavController
import androidx.navigation.NavOptions
import androidx.navigation.fragment.findNavController
import com.airbnb.lottie.LottieComposition
import com.airbnb.lottie.LottieCompositionFactory
import com.airbnb.lottie.LottieListener
import com.stt.android.BuildConfig
import com.stt.android.R
import com.stt.android.common.ui.ViewModelFragment2
import com.stt.android.common.ui.observeNotNull
import com.stt.android.di.FeatureTogglePreferences
import com.stt.android.domain.device.ConnectedWatchConnectionState
import com.stt.android.utils.STTConstants
import com.suunto.connectivity.deviceid.SuuntoDeviceCapabilityInfoProvider
import com.suunto.connectivity.util.NotificationSettingsHelper
import timber.log.Timber
import java.io.Serializable
import javax.inject.Inject

/**
 * DeviceFragment is an abstract base fragment class for device related fragments.
 * This provides common methods and variables for device views.
 *
 * In the basic case, DeviceFragment listens to the device connection and state
 * updates and handles navigation between different UI states automatically.
 *
 * For some special cases it is required to handle the UI navigation manually
 * and that can be achieved by overriding the [navigateAutomatically] and
 * returning false from it.
 *
 * DeviceFragment also provides [onStateUpdate] which is called every time the
 * device state is changed. Extending fragments can override this method and
 * update the UI based on this.
 */
// Note: derived classes should use @AndroidEntryPoint annotation
abstract class DeviceFragment : ViewModelFragment2() {

    @Inject
    lateinit var notificationSettingsHelper: NotificationSettingsHelper

    @Inject
    @FeatureTogglePreferences
    lateinit var featureTogglePreferences: SharedPreferences

    protected lateinit var navController: NavController

    protected val sharedViewModel by activityViewModels<DeviceHolderViewModel>()

    /**
     * Navigation id resource of the previous fragment. Defaults to 0 when there's no previous fragment.
     */
    protected var previousFragmentId: Int = 0
        private set

    /**
     * Options menu to show
     */
    private var menuId = R.menu.device_unregistered

    private var supportsNotifications = false
    private var supportsWifiSetup = false

    open val canScanQrcode: Boolean = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Set shared view model to view models that support it
        (viewModel as? DeviceViewModel)?.sharedViewModel = sharedViewModel

        arguments?.let {
            previousFragmentId = it.getInt(PREVIOUS_FRAGMENT_ID)
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        navController = findNavController()

        // Enable options menu for device fragments
        setHasOptionsMenu(true)

        // Handle state updates
        sharedViewModel.deviceStateEvent.observeNotNull(viewLifecycleOwner) { event ->
            // Handle events only for current fragment
            if (navController.currentDestination?.id == getNavId()) {
                Timber.d("State event: $event")

                // When device state event happens, check that the event is an
                // update. Loading events are not propagated to fragments, since
                // not all information is yet available
                when (event) {
                    is DeviceStateUpdate -> {
                        updateTitle(event)
                        handleStateUpdate(event)
                        updateMenu(event)
                    }
                    else -> {
                        // Update, do nothing
                    }
                }
            }
        }

        if (savedInstanceState == null) {
            val previousScrollFamily = arguments?.getSerializable(PREVIOUS_FRAGMENT_SCROLL_FAMILY)
            val previousScrollParams =
                arguments?.getSerializable(PREVIOUS_FRAGMENT_SCROLL_PARAMS) as? ScrollParams
            if (previousScrollParams != null &&
                previousScrollFamily != null &&
                getScrollFamily() == previousScrollFamily
            ) {
                applyInitialScroll(previousScrollParams)
            }
        }
    }

    override fun onStart() {
        super.onStart()
        // Check device availability when starting the fragment
        sharedViewModel.checkDeviceAvailability()
    }

    /**
     * Navigate to the given fragment or action id.
     *
     * This method handles changes between fragments and makes sure that
     * there's only one fragment running at anytime and that the transitions
     * use the default animations.
     *
     * @param navActionId Fragment or action id in the navigation graph
     * @param bundle Optional Bundle which includes parameters to the fragment
     */
    protected fun navigateTo(@IdRes navActionId: Int, bundle: Bundle = Bundle.EMPTY) {
        val builder = NavOptions.Builder()
        builder.apply {
            // Clear the stack since we're only in one state at a time
            setPopUpTo(navActionId, false)

            // Set enter and exit animations
            setEnterAnim(androidx.navigation.ui.R.anim.nav_default_enter_anim)
            setExitAnim(androidx.navigation.ui.R.anim.nav_default_exit_anim)
            setPopEnterAnim(androidx.navigation.ui.R.anim.nav_default_pop_enter_anim)
            setPopExitAnim(androidx.navigation.ui.R.anim.nav_default_pop_exit_anim)
        }

        navController.navigate(navActionId, bundle, builder.build())
    }

    /**
     * Create LottieComposition from the given resource string.
     * @param stringResId Animation resource to load
     * @param success Listener which is called if the loading succeeds
     * @param failure Listener which is called if the loading failed
     */
    protected fun startNewComposition(
        @StringRes stringResId: Int,
        success: LottieListener<LottieComposition>,
        failure: LottieListener<Throwable>
    ) {
        LottieCompositionFactory.fromAsset(requireContext(), getString(stringResId))
            .addListener(success)
            .addFailureListener(failure)
    }

    /**
     * Update activity title based on the current state
     */
    private fun updateTitle(state: DeviceStateUpdate) {
        // Get title
        val title = if (state.registered || state.pairing) {
            WatchHelper.getStringResIdForSuuntoDeviceType(state.deviceType)
        } else {
            R.string.watch_ui_status_pairing
        }

        activity?.setTitle(title)
    }

    /**
     * Handle state update. This method finds out from the [DeviceStateUpdate]
     * the current state the device connection is in.
     *
     * This method also initiates navigation to the right UI state if current
     * one differs from the resolved one and the [navigateAutomatically] method returns true.
     */
    private fun handleStateUpdate(state: DeviceStateUpdate) {
        val uiState = if (state.permissionsRequired) {
            R.id.devicePermissionFragment
        } else if (state.registered) {
            // When device is registered, the UI state depends on the connection state.
            when (state.watchState?.connectedWatchConnectionState) {
                ConnectedWatchConnectionState.CONNECTING,
                ConnectedWatchConnectionState.RECONNECTING -> {
                    R.id.deviceConnectingFragment
                }
                ConnectedWatchConnectionState.CONNECTED -> {
                    when {
                        state.busy -> R.id.deviceBusyFragment
                        state.syncing -> R.id.deviceSyncFragment
                        state.syncSuccess -> R.id.deviceConnectedFragment
                        else -> R.id.deviceSyncFailFragment
                    }
                }
                ConnectedWatchConnectionState.DISCONNECTED -> {
                    R.id.deviceDisconnectedFragment
                }
                else -> {
                    R.id.deviceConnectingFragment
                }
            }
        } else if (state.pairing || state.switching) {
            R.id.devicePairFragment
        } else if (getNavId() == R.id.scanFailedFragment) { // When the scan fails, you can only switch status by clicking "Try again"
            R.id.scanFailedFragment
        } else {
            Timber.d("Activating device scan fragment: $state")
            R.id.deviceScanFragment
        }

        // Determine if Mobile notifications menu item should be shown
        val fwVersion = state.watchState?.deviceInfo?.fwVersion
        val capabilities = SuuntoDeviceCapabilityInfoProvider.get(state.deviceType)
        supportsNotifications = capabilities.supportsNotifications(fwVersion)
        supportsWifiSetup = capabilities.supportsWifiSetup(state.watchState?.deviceInfo?.capabilities)

        // Check if navigation should be done automatically and navigate
        // only to a fragment that differs from the current one
        if (navigateAutomatically(uiState) && navController.currentDestination?.id != uiState) {
            // Include previous fragment id in the bundle
            val bundle = Bundle()
            bundle.putInt(PREVIOUS_FRAGMENT_ID, navController.currentDestination?.id ?: 0)
            bundle.putSerializable(PREVIOUS_FRAGMENT_SCROLL_FAMILY, getScrollFamily())
            bundle.putSerializable(PREVIOUS_FRAGMENT_SCROLL_PARAMS, getScrollParams())

            navigateTo(uiState, bundle)
        }

        onStateUpdate(state)
    }

    /**
     * Handle fragment navigation automatically to correct state fragment.
     *
     * @param fragmentNavId Fragment id where we're navigating to
     * @return By default returns true which allows navigation to the fragment
     * with the given id automatically. Returning false means the navigation is
     * handled manually in the fragment.
     */
    protected open fun navigateAutomatically(@IdRes fragmentNavId: Int): Boolean {
        return true
    }

    /**
     * Return id set for this fragment in the navigation graph
     */
    @IdRes
    protected abstract fun getNavId(): Int

    /**
     * Handle state change event in the fragment. This method is called every
     * time the device state changes. Overriding this in the extending
     * fragments allows the fragment to either update the UI or navigate to
     * other view based on the update.
     *
     * @param state Update state instance
     */
    protected open fun onStateUpdate(state: DeviceStateUpdate) {
    }

    //region Menu

    /**
     * Update menu resource based on the given device state
     */
    private fun updateMenu(event: DeviceStateUpdate) {
        // Check menu id
        val newMenuId = if (event.registered) R.menu.device_registered else R.menu.device_unregistered
        // Invalidate menu if the menuId is different
        if (newMenuId != menuId) {
            menuId = newMenuId
            activity?.invalidateOptionsMenu()
        }
    }

    @Deprecated("Deprecated in Java")
    override fun onCreateOptionsMenu(menu: Menu, inflater: MenuInflater) {
        // Initially inflate the unpaired menu
        inflater.inflate(menuId, menu)
    }

    @Deprecated("Deprecated in Java")
    override fun onPrepareOptionsMenu(menu: Menu) {
        // Should show transfer firmware and watch updates menu item.
        val transferFirmwareItem = menu.findItem(R.id.device_menu_transfer_firmware)
        val importWorkout = menu.findItem(R.id.device_menu_import_workout)
        val debugLocation = menu.findItem(R.id.device_menu_debug_location)
        val getEonSettings = menu.findItem(R.id.device_menu_get_eon_settings)
        val setEonSettings = menu.findItem(R.id.device_menu_set_eon_settings)
        val wifiNetworks = menu.findItem(R.id.device_menu_wifi_networks)
        val scanQrcode = menu.findItem(R.id.device_menu_scan_qrcode)

        transferFirmwareItem?.isVisible = BuildConfig.DEBUG
        val isEon = sharedViewModel.suuntoDeviceType?.isEon
        (BuildConfig.DEBUG || sharedViewModel.isCurrentUserFieldTester()).let { show ->
            importWorkout.isVisible = show
            getEonSettings?.let { it.isVisible = BuildConfig.DEBUG && isEon == true }
            setEonSettings?.let { it.isVisible = BuildConfig.DEBUG && isEon == true }
        }

        debugLocation?.isVisible = featureTogglePreferences.getBoolean(
            STTConstants.FeatureTogglePreferences.KEY_SHOW_WATCH_DEBUG_LOCATION_OPTION,
            STTConstants.FeatureTogglePreferences.KEY_SHOW_WATCH_DEBUG_LOCATION_OPTION_DEFAULT
        )

        wifiNetworks?.isVisible = supportsWifiSetup

        scanQrcode?.isVisible = canScanQrcode
        scanQrcode?.setOnMenuItemClickListener {
            onScanQrcodeClick()
            true
        }
    }
    //endregion

    //region keep scroll between fragments
    protected open fun getScrollFamily(): ScrollFamily? = null

    protected open fun getScrollParams(): ScrollParams = ScrollParams(0)

    protected open fun applyInitialScroll(scrollParams: ScrollParams) {
        // no-op
    }

    protected open fun onScanQrcodeClick() {

    }

    protected enum class ScrollFamily {
        DEVICE_PAIRED
    }

    protected open class ScrollParams(val scrollY: Int) : Serializable {
        class DevicePaired(scrollY: Int, private val actionListTopY: Int) : ScrollParams(scrollY) {
            fun getAdjustedScrollY(newActionListTopY: Int): Int {
                return if (scrollY <= actionListTopY) {
                    scrollY
                } else {
                    scrollY + (newActionListTopY - actionListTopY)
                }
            }
        }
    }

    //endregion

    companion object {
        private const val PREVIOUS_FRAGMENT_ID = "previous_fragment_id"
        private const val PREVIOUS_FRAGMENT_SCROLL_FAMILY = "previous_fragment_scroll_family"
        private const val PREVIOUS_FRAGMENT_SCROLL_PARAMS = "previous_fragment_scroll_position"
    }
}
