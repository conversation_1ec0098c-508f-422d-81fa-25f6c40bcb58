package com.stt.android.watch.sportmodes

import android.os.Bundle
import androidx.lifecycle.ViewModelProvider
import com.stt.android.common.ui.ListFragment2

abstract class SportModeFragment<T : SportModeViewModel> : ListFragment2<T>() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        val sharedViewModel = ViewModelProvider(requireActivity())
            .get(SportModeHolderViewModel::class.java)

        viewModel.sharedViewModel = sharedViewModel
    }
}
