package com.stt.android.watch.companionAssociation

import android.annotation.SuppressLint
import android.app.NotificationManager
import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothDevice
import android.companion.AssociationRequest
import android.companion.AssociationRequest.DEVICE_PROFILE_WATCH
import android.companion.BluetoothDeviceFilter
import android.companion.CompanionDeviceManager
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.IntentSender
import android.os.Build
import androidx.core.os.BuildCompat
import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.AnalyticsEventProperty
import com.stt.android.analytics.AnalyticsProperties
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.analytics.AnalyticsUserProperty
import com.stt.android.analytics.toYesNo
import com.stt.android.coroutines.await
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.watch.ConnectPhase
import com.stt.android.watch.MissingCurrentWatchException
import com.stt.android.watch.SuuntoWatchModel
import com.suunto.connectivity.notifications.AncsService
import com.suunto.connectivity.notifications.AppCompanionDeviceService
import com.suunto.connectivity.suuntoconnectivity.device.AnalyticsDevicePropertyHelper
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoBtDevice
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoDeviceType
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers.IO
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.buffer
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import kotlinx.coroutines.rx2.asFlow
import timber.log.Timber

/**
 * Inject this class in fragment or activity directly, not in ViewModel as it will stop working
 * when the Activity is destroyed.
 *
 * To use this class:
 * 0. collect changes to companionState to see if companion is enabled
 * 1. collect changes to pairingRequestState and send an intent when requested
 *           activity.startIntentSenderForResult(chooserLauncher,123546,null,0,0,0,null)
 * 2. call startCompanionAssociationRequest() and react to the pairing request state change
 * 3. forward the onActivityResult to handleOnActivityResult()
 * 4. call destroy() when activity is destroyed
 */
class CompanionAssociationHelper(
    private val suuntoWatchModel: SuuntoWatchModel,
    private var companionDeviceManager: CompanionDeviceManager?,
    private val amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker,
) {

    sealed class CompanionState {
        object Unknown : CompanionState()
        class Connecting(val deviceType: SuuntoDeviceType) : CompanionState()
        class Associated(val device: SuuntoBtDevice) : CompanionState()
        class NotAssociated(val device: SuuntoBtDevice) : CompanionState()
        class Error(val throwable: Throwable) : CompanionState()
    }

    sealed class PairingRequestState {
        class Pending(
            val macAddress: String,
            val intentSender: IntentSender?,
            var consumed: Boolean = false
        ) : PairingRequestState()

        class Error(val throwable: Throwable) : PairingRequestState()
    }

    class CompanionDeviceManagerNotAvailableException : RuntimeException()
    class CompanionAssociationException(message: CharSequence?) :
        RuntimeException(message?.toString())
    class CompanionDeviceNotFoundFromPairedDevices :
        RuntimeException("Device not found from paired devices")

    private val scope = CoroutineScope(SupervisorJob() + IO)

    private val _companionState = MutableStateFlow<CompanionState>(CompanionState.Unknown)
    val companionState: StateFlow<CompanionState> = _companionState

    private val _pairingRequestState = MutableStateFlow<PairingRequestState?>(null)
    val pairingRequestState: StateFlow<PairingRequestState?> = _pairingRequestState

    private var uiContext: CompanionAssociationUIContext = CompanionAssociationUIContext.UNKNOWN

    @Volatile
    private var suuntoBtDevice: SuuntoBtDevice? = null

    val isAssociated: Boolean
        get() = companionState.value is CompanionState.Associated

    init {
        refreshCompanionState()
    }

    fun startCompanionAssociationRequest(uiContext: CompanionAssociationUIContext) {
        if (companionState.value is CompanionState.Associated) {
            Timber.d("Companion already associated, returning")
            return
        }
        this.uiContext = uiContext
        scope.launch {
            runSuspendCatching {
                companionDeviceManager ?: throw CompanionDeviceManagerNotAvailableException()
                val device = suuntoWatchModel.currentWatch.await().suuntoBtDevice
                suuntoBtDevice = device
                val macAddress = getMacAddress(device)

                // Define the device filter to find exact mac address.
                val deviceFilter: BluetoothDeviceFilter =
                    BluetoothDeviceFilter.Builder()
                        .setAddress(macAddress)
                        .build()
                // Request to find exactly one device matching the filter.
                val pairingRequest: AssociationRequest = AssociationRequest.Builder()
                    .addDeviceFilter(deviceFilter)
                    .setSingleDevice(true)
                    .apply {
                        if (BuildCompat.isAtLeastS()) {
                            setDeviceProfile(DEVICE_PROFILE_WATCH)
                        }
                    }
                    .build()

                companionDeviceManager?.associateFlow(macAddress, pairingRequest)
                    ?.collect {
                        // if it's success, the analytics are sent when intent is returned
                        if (it is PairingRequestState.Error) {
                            sendAnalytics(it.throwable)
                        }
                        _pairingRequestState.value = it
                    }
            }.onFailure { e ->
                if (e is MissingCurrentWatchException) {
                    Timber.d("Cannot startCompanionAssociationRequest, missing current watch")
                } else {
                    Timber.w(e, "Error in startCompanionAssociationRequest")
                }
                _pairingRequestState.value = PairingRequestState.Error(e)
            }
        }
    }

    fun handleOnActivityResult(context: Context, data: Intent?): Boolean {
        val deviceToPair: BluetoothDevice? =
            data?.getParcelableExtra(CompanionDeviceManager.EXTRA_DEVICE)
        val macAddress = deviceToPair?.address
        val pairingRequestState = _pairingRequestState.value
        return if (pairingRequestState is PairingRequestState.Pending &&
            pairingRequestState.macAddress == macAddress
        ) {
            _pairingRequestState.value = null
            refreshCompanionState()
            sendAnalytics(null)
            // also we can request notification access now
            requestNotificationAccess(context)
            true
        } else {
            false
        }
    }

    fun requestNotificationAccess(context: Context) {
        try {
            val component = ComponentName(context, AncsService::class.java)
            val notificationManager = context.getSystemService(NotificationManager::class.java)
            if (
                Build.VERSION.SDK_INT >= Build.VERSION_CODES.O_MR1 &&
                notificationManager.isNotificationListenerAccessGranted(component)
            ) {
                // we already have notification access
                return
            }
            @Suppress("DEPRECATION")
            if (companionDeviceManager?.hasNotificationAccess(component) == true) {
                // we already have notification access
                return
            }
            companionDeviceManager?.requestNotificationAccess(component)
        } catch (e: Throwable) {
            Timber.w(e, "Error requesting notification access after companion association is granted")
        }
    }

    fun removeCompanionAssociation() {
        scope.launch {
            runSuspendCatching {
                companionDeviceManager ?: throw CompanionDeviceManagerNotAvailableException()
                // using locally stored suuntoBtDevice cause the watch might be unpaired already
                val device = suuntoBtDevice ?: throw MissingCurrentWatchException()
                val macAddress = getMacAddress(device)
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                    AppCompanionDeviceService.stopObservingDevicePresence(
                        companionDeviceManager,
                        macAddress
                    )
                }

                if (companionDeviceManager?.associations?.contains(macAddress) != true) {
                    // nothing to remove
                    return@launch
                }
                // from Android 12, if we disassociate the last companion device while using InCallService, the system restarts our processes
                // which shows to the user as a crash and can create UI issues.
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S &&
                    companionDeviceManager?.associations?.size == 1
                ) {
                    Timber.w("Cannot remove last companion association from Android 12, to prevent crash")
                    return@launch
                }
                companionDeviceManager?.disassociate(macAddress)
                trackCompanionAssociated(false)
                _companionState.value = CompanionState.NotAssociated(device)
            }.onFailure { e ->
                if (e is MissingCurrentWatchException) {
                    Timber.i("Missing current watch")
                } else {
                    Timber.w(e, "Error in removeCompanionAssociationFromCurrentWatch")
                }
                _companionState.value = CompanionState.Error(e)
            }
        }
    }

    fun destroy() {
        scope.cancel()
        companionDeviceManager = null // holds reference to the activity, terrible API
    }

    fun refreshCompanionState() {
        scope.launch {
            runSuspendCatching {
                companionDeviceManager ?: throw CompanionDeviceManagerNotAvailableException()
                val connectAttemptState = suuntoWatchModel.connectAttemptState
                    .asFlow()
                    .first()
                if (connectAttemptState.connectPhase == ConnectPhase.Connecting) {
                    _companionState.value =
                        CompanionState.Connecting(connectAttemptState.suuntoDeviceType)
                } else {
                    val device = suuntoWatchModel.currentWatch.await().suuntoBtDevice
                    suuntoBtDevice = device
                    val macAddress = getMacAddress(device)
                    if (companionDeviceManager?.associations?.contains(macAddress) == true) {
                        trackCompanionAssociated(true)
                        _companionState.value = CompanionState.Associated(device)
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                            AppCompanionDeviceService.startObservingDevicePresence(companionDeviceManager,macAddress)
                        }
                    } else {
                        trackCompanionAssociated(false)
                        _companionState.value = CompanionState.NotAssociated(device)
                    }
                }
            }.onFailure { e ->
                if (e is MissingCurrentWatchException) {
                    Timber.d("Cannot initialise CompanionAssociationHelper, missing current watch")
                } else {
                    Timber.w(e, "Error while initialising CompanionAssociationHelper")
                }
                _companionState.value = CompanionState.Error(e)
            }
        }
    }

    private fun trackCompanionAssociated(isAssociated: Boolean) {
        amplitudeAnalyticsTracker.trackUserProperty(
            AnalyticsUserProperty.WATCH_LINKED_AS_ANDROID_COMPANION_DEVICE,
            isAssociated.toYesNo()
        )
    }

    /**
     * Some special handling for S7 as we need to get the real mac address from the
     * bluetooth adapter.
     * @throws DeviceNotFoundFromPairedDevices
     */
    @SuppressLint("MissingPermission")
    private fun getMacAddress(device: SuuntoBtDevice): String =
        if (device.deviceType.isDataLayerDevice) {
            BluetoothAdapter.getDefaultAdapter()?.bondedDevices?.firstOrNull {
                it.name == device.name
            }?.address ?: throw CompanionDeviceNotFoundFromPairedDevices()
        } else {
            device.macAddress
        }

    private fun sendAnalytics(throwable: Throwable?) {
        val properties = AnalyticsProperties()
            .put(
                AnalyticsEventProperty.SUUNTO_COMPANION_LINKING_CONTEXT,
                uiContext.analyticsDescription
            )
        suuntoBtDevice?.let {
            properties.put(
                AnalyticsEventProperty.SUUNTO_WATCH_MODEL,
                AnalyticsDevicePropertyHelper.getWatchModelNameForSuuntoDeviceType(it.deviceType)
            )
        }
        when (throwable) {
            null -> {
                properties.put(
                    AnalyticsEventProperty.SUUNTO_COMPANION_LINKING_RESPONSE,
                    AnalyticsPropertyValue.CompanionAssociationLinkingProperty.OK
                )
            }
            is CompanionAssociationException -> {
                properties.put(
                    AnalyticsEventProperty.SUUNTO_COMPANION_LINKING_RESPONSE,
                    AnalyticsPropertyValue.CompanionAssociationLinkingProperty.CANCEL
                )
            }
            else -> {
                properties.put(
                    AnalyticsEventProperty.SUUNTO_COMPANION_LINKING_RESPONSE,
                    AnalyticsPropertyValue.CompanionAssociationLinkingProperty.ERROR
                )
                properties.put(AnalyticsEventProperty.ERROR_TYPE, throwable.message)
            }
        }
        amplitudeAnalyticsTracker.trackEvent(
            AnalyticsEvent.LINK_ANDROID_COMPANION_DEVICE_RESPONSE,
            properties
        )
    }
}

/**
 * The UI Context, from where association is started.
 */
enum class CompanionAssociationUIContext(val analyticsDescription: String) {
    UNKNOWN("Unknown"),
    WATCH_SCREEN_POPUP("WatchScreenPopUp"),
    WATCH_MANAGE_CONNECTION_SCREEN("WatchManageConnectionScreen"),
    INITIATED_FROM_SERVICE_NOTIFICATION("InitiatedFromServiceNotification"),
    SETTINGS_POWER_MANAGEMENT("SettingsPowerManagement"),
    POWER_MANAGEMENT_SETTINGS_DEEPLINK("PowerManagementSettingsDeepLink"),
    GPS_TRACKING_INTERRUPTED_POPUP("InitiatedFromGpsTrackingInterruptedPopUp"),
    WATCH_NOTIFICATION_PERMISSIONS_SCREEN("WatchNotificationPermissionsScreen")
}

/**
 * The reason for the extension function is that the CompanionDeviceManager retains the callback in memory
 * after [CompanionDeviceManager.Callback.onDeviceFound] is called, so we should not pass the
 * CompanionAssociationHelper instance to it.
 * There is no way to unregister the callback.
 */
private fun CompanionDeviceManager.associateFlow(
    macAddress: String,
    pairingRequest: AssociationRequest
): Flow<CompanionAssociationHelper.PairingRequestState> = callbackFlow {
    associate(
        pairingRequest,
        object : CompanionDeviceManager.Callback() {
            @Deprecated("Deprecated in Java")
            override fun onDeviceFound(chooserLauncher: IntentSender) {
                trySend(
                    CompanionAssociationHelper.PairingRequestState.Pending(
                        macAddress,
                        chooserLauncher
                    )
                )
            }

            override fun onFailure(error: CharSequence?) {
                trySend(
                    CompanionAssociationHelper.PairingRequestState.Error(
                        CompanionAssociationHelper.CompanionAssociationException("User rejected association")
                    )
                )
            }
        },
        null
    )
    awaitClose {
        // no way to unregister callback :(
    }
}.buffer(Channel.CONFLATED)
