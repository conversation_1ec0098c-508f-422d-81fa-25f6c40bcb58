package com.stt.android.watch.sportmodes.editdisplays

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.InputMethodManager
import androidx.databinding.ViewDataBinding
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import com.google.android.material.snackbar.Snackbar
import com.stt.android.R
import com.stt.android.common.ui.observeK
import com.stt.android.common.ui.observeNotNull
import com.stt.android.watch.sportmodes.SportModeFragment
import com.stt.android.watch.sportmodes.dialogs.SportModeAlertDialog
import com.stt.android.watch.sportmodes.dialogs.SportModeDialogCallback
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class SportModeEditDisplaysFragment : SportModeFragment<SportModeEditDisplaysViewModel>(), SportModeDialogCallback {

    private val viewDataBinding: ViewDataBinding get() = requireBinding()

    private var saveButton: MenuItem? = null

    override fun onOkClicked(tag: String?) {
        when (tag) {
            SportModeAlertDialog.TAG_SAVE -> viewModel.saveSportMode()
            SportModeAlertDialog.TAG_DELETE_DISPLAY -> viewModel.onDisplayDeleted()
            SportModeAlertDialog.TAG_NON_SAVED_CHANGES -> {
                viewModel.trackEditCancelEvent()
                findNavController().navigateUp()
            }
        }
    }

    override fun onDialogCanceled() {
    }

    override val viewModel: SportModeEditDisplaysViewModel by viewModels()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setHasOptionsMenu(true)
        viewModel.trackCustomizationScreenEvent()
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        val title = SportModeEditDisplaysFragmentArgs.fromBundle(
            arguments ?: throw IllegalStateException("Required fragment arguements are missing!")
        ).activityName

        viewModel.sharedViewModel.setTitle(title)
        viewModel.showDeleteDisplayAlertEvent.observeK(viewLifecycleOwner) {
            showDeleteDialogAlert()
        }

        viewModel.showSaveCompleteEvent.observeK(viewLifecycleOwner) {
            showSaveCompleteSnackbar()
            findNavController().popBackStack()
        }

        viewModel.showNonSavedChangesAlertEvent.observeK(viewLifecycleOwner) {
            showNonSavedChangesAlert()
        }

        viewModel.saveEnabled.observeNotNull(viewLifecycleOwner) { isSaveEnabled ->
            saveButton?.let {
                it.isEnabled = isSaveEnabled
            }
        }

        return super.onCreateView(inflater, container, savedInstanceState)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        val inputManager = activity?.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager?
        activity?.currentFocus?.let {
            inputManager?.hideSoftInputFromWindow(it.windowToken, InputMethodManager.HIDE_NOT_ALWAYS)
        }
    }

    @Deprecated("Deprecated in Java")
    override fun onCreateOptionsMenu(menu: Menu, inflater: MenuInflater) {
        inflater.inflate(R.menu.sport_modes, menu)
        saveButton = menu.findItem(R.id.save_sportmode)
        super.onCreateOptionsMenu(menu, inflater)
    }

    @Deprecated("Deprecated in Java")
    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            R.id.save_sportmode -> {
                viewModel.saveSportMode()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }

    private fun showDeleteDialogAlert() {
        val dialog = SportModeAlertDialog.newInstance(
            description = R.string.sport_modes_delete_display_query,
            buttonOkText = R.string.delete,
            buttonCancelText = R.string.cancel
        )
        dialog.show(childFragmentManager, SportModeAlertDialog.TAG_DELETE_DISPLAY)
    }

    private fun showNonSavedChangesAlert() {
        val dialog = SportModeAlertDialog.newInstance(
            SportModeAlertDialog.NO_VALUE,
            R.string.cancel_confirm,
            R.string.discard,
            R.string.cancel
        )
        dialog.show(childFragmentManager, SportModeAlertDialog.TAG_NON_SAVED_CHANGES)
    }

    private fun showSaveCompleteSnackbar() {
        val snackbar = Snackbar.make(
            viewDataBinding.root,
            R.string.sport_mode_save_complete,
            Snackbar.LENGTH_SHORT
        )
        snackbar.show()
    }
}
