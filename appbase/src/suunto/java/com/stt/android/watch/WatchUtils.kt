@file:JvmName("WatchUtils")

package com.stt.android.watch

import android.Manifest
import android.annotation.SuppressLint
import android.content.Context
import android.content.pm.PackageManager
import android.os.Build
import androidx.core.content.ContextCompat
import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.analytics.AnalyticsProperties
import com.stt.android.analytics.AnalyticsUserProperty
import com.stt.android.analytics.EmarsysAnalytics
import com.stt.android.domain.user.DomainUserSettings
import com.stt.android.home.settings.hrintensityzones.toCombinedHrIntensityZones
import com.suunto.connectivity.repository.entities.AskoUserSettings
import com.suunto.connectivity.util.NotificationSettingsHelper
import java.time.DayOfWeek

fun getMissingNotificationsPermissions(
    notificationSettingsHelper: NotificationSettingsHelper,
    context: Context,
    emarsysAnalytics: EmarsysAnalytics,
    amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker,
): List<String> {
    val notificationsEnabled = notificationSettingsHelper.notificationsEnabled(context)
    return if (notificationsEnabled) {
        val permissions = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
            listOf(
                Manifest.permission.READ_PHONE_STATE,
                Manifest.permission.READ_CONTACTS,
                Manifest.permission.READ_CALL_LOG
            )
        } else {
            listOf(
                Manifest.permission.READ_PHONE_STATE,
                Manifest.permission.READ_CONTACTS
            )
        }
        permissions.filter {
            ContextCompat.checkSelfPermission(context, it) == PackageManager.PERMISSION_DENIED
        }
    } else {
        emptyList()
    }.also {
        updateAnalyticsUserProperties(notificationsEnabled, it, emarsysAnalytics, amplitudeAnalyticsTracker)
    }
}

@SuppressLint("InlinedApi")
fun updateAnalyticsUserProperties(
    notificationsEnabled: Boolean,
    missingPermissions: List<String>,
    emarsysAnalytics: EmarsysAnalytics,
    amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker,
) {
    val (hasPhoneStatePermission, hasReadContactsPermission, hasCallLogPermission) =
        if (notificationsEnabled) {
            Triple(
                !missingPermissions.contains(Manifest.permission.READ_PHONE_STATE),
                !missingPermissions.contains(Manifest.permission.READ_CONTACTS),
                !missingPermissions.contains(Manifest.permission.READ_CALL_LOG)
            )
        } else {
            // Notifications permission denied, we do not know the status of others, so skip them
            Triple(null, null, null)
        }
    with(
        notificationsOnWatchProperties(
            notificationsEnabled,
            hasPhoneStatePermission,
            hasReadContactsPermission,
            hasCallLogPermission
        )
    ) {
        amplitudeAnalyticsTracker.trackUserProperties(this)
        emarsysAnalytics.trackUserProperties(this.map)
    }
}

private fun notificationsOnWatchProperties(
    notificationsEnabled: Boolean?,
    hasPhoneStatePermission: Boolean?,
    hasReadContactsPermission: Boolean?,
    hasCallLogPermission: Boolean?
): AnalyticsProperties = AnalyticsProperties().apply {
    notificationsEnabled?.let {
        putYesNo(
            AnalyticsUserProperty.NOTIFICATIONS_ACCESS_GIVEN,
            notificationsEnabled
        )
    }
    hasPhoneStatePermission?.let {
        putYesNo(
            AnalyticsUserProperty.SHOW_CALLS_ACCESS_GIVEN,
            hasPhoneStatePermission
        )
    }
    hasReadContactsPermission?.let {
        putYesNo(
            AnalyticsUserProperty.CONTACT_LIST_ACCESS_GIVEN,
            hasReadContactsPermission
        )
    }
    hasCallLogPermission?.let {
        putYesNo(
            AnalyticsUserProperty.CALL_LOGS_ACCESS_GIVEN,
            hasCallLogPermission
        )
    }
}

fun DomainUserSettings.toAskoUserSettings(): AskoUserSettings = AskoUserSettings(
    measurementUnit = this.measurementUnit,
    hrMaximum = this.hrMaximum,
    restHR = this.hrRest,
    gender = this.gender,
    height = this.height,
    weight = this.weight,
    birthDate = this.birthDate,
    email = this.email,
    phoneNumber = this.phoneNumber,
    screenBacklightSetting = this.screenBacklightSetting,
    gpsFiltering = this.gpsFiltering,
    altitudeOffset = this.altitudeOffset,
    selectedMapType = this.selectedMapType,
    notifyNewFollower = this.notifyNewFollower,
    notifyWorkoutComment = this.notifyWorkoutComment,
    notifyWorkoutFollowingShare = this.notifyWorkoutFollowingShare,
    autoApproveFollowers = this.autoApproveFollowers,
    emailDigest = this.emailDigest,
    optinAccepted = this.optinAccepted,
    optinRejected = this.optinRejected,
    optinLastShown = this.optinLastShown,
    optinShowCount = this.optinShowCount,
    analyticsUUID = this.analyticsUUID,
    country = this.country,
    countrySubdivision = this.countrySubdivision,
    language = this.language,
    realName = this.realName,
    description = this.description,
    sharingFlagPreference = this.sharingFlagPreference,
    facebookFriendJoinNotificationEnabled = this.facebookFriendJoinNotificationEnabled,
    newFollowerNotificationEnabled = this.newFollowerNotificationEnabled,
    workoutCommentNotificationEnabled = this.workoutCommentNotificationEnabled,
    workoutReactionNotificationEnabled = this.workoutReactionNotificationEnabled,
    workoutShareNotificationEnabled = this.workoutShareNotificationEnabled,
    hasOutboundPartnerConnections = this.hasOutboundPartnerConnections,
    predefinedReplies = this.predefinedReplies.toList(),
    firstDayOfTheWeek = this.firstDayOfTheWeek?.let { DayOfWeek.of(it) },
    automaticUpdateDisabledWatches = this.automaticUpdateDisabledWatches,
    combinedIntensityZones = this.combinedIntensityZones?.toCombinedHrIntensityZones(),
    lastAgeSaveTimestamp = this.lastAgeSaveTimestamp,
    lastGenderSaveTimestamp = this.lastGenderSaveTimestamp,
    lastWeightSaveTimestamp = this.lastWeightSaveTimestamp,
    lastHeightSaveTimestamp = this.lastHeightSaveTimestamp,
    lastMaxHRSaveTimestamp = this.lastMaxHRSaveTimestamp,
    lastRestHRSaveTimestamp = this.lastRestHRSaveTimestamp,
)
