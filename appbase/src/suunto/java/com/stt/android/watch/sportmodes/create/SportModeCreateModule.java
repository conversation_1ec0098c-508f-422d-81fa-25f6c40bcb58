package com.stt.android.watch.sportmodes.create;

import com.stt.android.common.ui.content.AndroidResources;
import com.stt.android.common.ui.content.Resources;
import dagger.Binds;
import dagger.Module;
import dagger.hilt.InstallIn;
import dagger.hilt.android.components.ViewModelComponent;
import dagger.hilt.android.scopes.ViewModelScoped;

@InstallIn(ViewModelComponent.class)
@Module
public abstract class SportModeCreateModule {
    @Binds
    @ViewModelScoped
    public abstract Resources bindAndroidResources(AndroidResources androidResources);
}
