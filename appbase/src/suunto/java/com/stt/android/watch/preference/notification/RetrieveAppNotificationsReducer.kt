package com.stt.android.watch.preference.notification

import android.content.Intent
import com.stt.android.watch.preference.SetupPreference
import com.stt.android.watch.preference.SetupPreferenceActivity
import com.stt.android.watch.preference.SetupPreferenceReducer
import com.stt.android.utils.activityresult.ResultLauncherActivity
import timber.log.Timber
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine

class RetrieveAppNotificationsReducer(private val activity: ResultLauncherActivity) :
    SetupPreferenceReducer {
    override val reduce: suspend (SetupPreference) -> SetupPreference = {
        suspendCoroutine { continuation ->
            val intent = Intent(activity, SetupPreferenceAppNotificationsActivity::class.java)
            intent.putExtra(SetupPreferenceActivity.EXTRA_SETUP_PREFERENCE, it)
            activity.startActivityForResult(intent) { resultCode, data ->
                Timber.d("RetrieveAppNotificationsReducer %s, %s", resultCode, data)
                (data?.getParcelableExtra<SetupPreference>(SetupPreferenceActivity.EXTRA_SETUP_PREFERENCE))?.let { updated ->
                    continuation.resume(updated)
                } ?: continuation.resume(it)
            }
        }
    }
}
