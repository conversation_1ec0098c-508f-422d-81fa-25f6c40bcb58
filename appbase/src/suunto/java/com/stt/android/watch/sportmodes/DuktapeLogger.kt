package com.stt.android.watch.sportmodes

import androidx.annotation.Keep
import timber.log.Timber

@Keep
interface DuktapeLoggerInterface {
    fun log(msg: String)
    fun error(msg: String)
    fun info(msg: String)
    fun warn(msg: String)
    fun debug(msg: String)
}

class DuktapeLogger() : DuktapeLoggerInterface {
    @Keep
    override fun log(msg: String) {
        Timber.v("$DUKTAPE_LOG_HANDLE $msg")
    }

    @Keep
    override fun error(msg: String) {
        Timber.e("$DUKTAPE_LOG_HANDLE $msg")
    }

    @Keep
    override fun info(msg: String) {
        Timber.i("$DUKTAPE_LOG_HANDLE $msg")
    }

    @Keep
    override fun warn(msg: String) {
        Timber.w("$DUKTAPE_LOG_HANDLE $msg")
    }

    @Keep
    override fun debug(msg: String) {
        Timber.d("$DUKTAPE_LOG_HANDLE $msg")
    }

    companion object {
        private const val DUKTAPE_LOG_HANDLE = "Duktape::"
    }
}
