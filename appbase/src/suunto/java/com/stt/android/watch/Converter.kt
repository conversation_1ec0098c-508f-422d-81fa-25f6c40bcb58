package com.stt.android.watch

import com.stt.android.domain.sleep.SleepTrackingMode
import com.stt.android.domain.sleep.SleepTrackingMode as DomainSleepTrackingMode
import com.suunto.connectivity.repository.commands.SleepTrackingMode as WatchSleepTrackingMode

internal fun WatchSleepTrackingMode.toDomain(): DomainSleepTrackingMode = when (this) {
    WatchSleepTrackingMode.OFF -> DomainSleepTrackingMode.OFF
    WatchSleepTrackingMode.AUTO -> DomainSleepTrackingMode.AUTO
    WatchSleepTrackingMode.MANUAL -> DomainSleepTrackingMode.MANUAL
}

internal fun DomainSleepTrackingMode.toWatch(): WatchSleepTrackingMode = when (this) {
    DomainSleepTrackingMode.OFF -> WatchSleepTrackingMode.OFF
    DomainSleepTrackingMode.AUTO -> WatchSleepTrackingMode.AUTO
    DomainSleepTrackingMode.MANUAL -> WatchSleepTrackingMode.MANUAL
}
