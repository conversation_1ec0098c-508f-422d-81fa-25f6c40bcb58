package com.stt.android.watch.watchupdates

import android.os.Bundle
import android.view.View
import androidx.appcompat.app.AlertDialog
import androidx.fragment.app.viewModels
import com.google.android.material.snackbar.Snackbar
import com.stt.android.R
import com.stt.android.common.ui.observeNotNull
import com.stt.android.common.viewstate.ViewStateListFragment2
import com.stt.android.databinding.FragmentWatchUpdatesBinding
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class WatchUpdatesFragment :
    ViewStateListFragment2<WatchUpdatesFragmentData, WatchUpdatesViewModel>() {

    private val binding: FragmentWatchUpdatesBinding get() = requireBinding()

    override val viewModel: WatchUpdatesViewModel by viewModels(
        extrasProducer = {
            // Grab activity intent extras for the saved state handle in view model
            requireActivity().defaultViewModelCreationExtras
        }
    )
    override val layoutId = R.layout.fragment_watch_updates

    interface Listener {
        fun onLearnMoreAboutUpdateClicked()

        fun onNeedHelpClicked()

        fun onDataLoadFailed()

        fun downgradeDenied()
    }

    var listener: Listener? = null

    private var cancelCurrentInstallDialogShown = false
    private var downgradeNoticeDialogShown = false

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        viewModel.actionEvent.observeNotNull(this) { event ->
            when (event) {
                is ConfirmCancelToCheckDeepLink -> showConfirmCancelToCheckDeepLink()
                is ConfirmTryingToInstallDowngrade -> showDowngradeNoticeDialog()
                is DowngradeDenied -> listener?.downgradeDenied()
                is WatchAutoUpdatesDisabled -> showWatchAutoUpdatesDisabledNotice()
                is LearnMore -> listener?.onLearnMoreAboutUpdateClicked()
                is NeedHelp -> listener?.onNeedHelpClicked()
                is DataLoadFailed -> listener?.onDataLoadFailed()
            }
        }

        var notConnectedSnackbar: Snackbar? = null
        viewModel.isWatchConnected.observeNotNull(this) { isConnected ->
            if (isConnected) {
                notConnectedSnackbar?.dismiss()
                notConnectedSnackbar = null
            } else {
                if (notConnectedSnackbar == null) {
                    notConnectedSnackbar = Snackbar.make(
                        binding.root,
                        R.string.watch_updates_watch_not_connected,
                        Snackbar.LENGTH_INDEFINITE
                    ).apply {
                        show()
                    }
                }
            }
        }

        if (savedInstanceState?.getBoolean(
                STATE_CONFIRM_CANCEL_CURRENT_DIALOG_SHOWN,
                false
            ) == true
        ) {
            viewModel.retryLoading()
        } else if (savedInstanceState?.getBoolean(
                STATE_DOWNGRADE_NOTICE_DIALOG_SHOWN,
                false
            ) == true
        ) {
            showDowngradeNoticeDialog()
        }
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        outState.putBoolean(
            STATE_CONFIRM_CANCEL_CURRENT_DIALOG_SHOWN,
            cancelCurrentInstallDialogShown
        )
        outState.putBoolean(STATE_DOWNGRADE_NOTICE_DIALOG_SHOWN, downgradeNoticeDialogShown)
    }

    private fun showConfirmCancelToCheckDeepLink() {
        if (cancelCurrentInstallDialogShown) return

        cancelCurrentInstallDialogShown = true
        AlertDialog.Builder(requireContext())
            .setMessage(getString(R.string.watch_updates_confirm_cancel_update_to_check_deeplink))
            .setNegativeButton(R.string.no) { _, _ ->
                cancelCurrentInstallDialogShown = false
                viewModel.onConfirmCheckDeeplinkWithOngoingUpdateClicked(false)
            }
            .setPositiveButton(R.string.yes) { _, _ ->
                cancelCurrentInstallDialogShown = false
                viewModel.onConfirmCheckDeeplinkWithOngoingUpdateClicked(true)
            }
            .setCancelable(false)
            .show()
    }

    private fun showDowngradeNoticeDialog() {
        if (downgradeNoticeDialogShown) return

        downgradeNoticeDialogShown = true
        AlertDialog.Builder(requireContext())
            .setTitle(getString(R.string.watch_updates_downgrade_notice_title))
            .setMessage(getString(R.string.watch_updates_downgrade_notice_body))
            .setNegativeButton(R.string.cancel) { _, _ ->
                downgradeNoticeDialogShown = false
                viewModel.onCancelDowngradeAttemptClicked()
            }
            .setPositiveButton(R.string.continue_str) { _, _ ->
                downgradeNoticeDialogShown = false
            }
            .setCancelable(false)
            .show()
    }

    private fun showWatchAutoUpdatesDisabledNotice() {
        Snackbar.make(
            binding.root,
            R.string.watch_updates_automatic_updates_disabled,
            Snackbar.LENGTH_LONG
        ).apply {
            setAction(R.string.undo) {
                viewModel.undoDisableAutomaticUpdatesForCurrentWatch()
            }

            show()
        }
    }

    companion object {
        private const val STATE_CONFIRM_CANCEL_CURRENT_DIALOG_SHOWN =
            "WatchUpdatesFragment.STATE_CONFIRM_CANCEL_CURRENT_DIALOG_SHOWN"
        private const val STATE_DOWNGRADE_NOTICE_DIALOG_SHOWN =
            "WatchUpdatesFragment.STATE_DOWNGRADE_NOTICE_DIALOG_SHOWN"
    }
}
