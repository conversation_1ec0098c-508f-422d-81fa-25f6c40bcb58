package com.stt.android.watch;

import com.stt.android.text.AndroidHtmlParser;
import com.stt.android.text.HtmlParser;
import dagger.Binds;
import dagger.Module;
import dagger.hilt.InstallIn;
import dagger.hilt.components.SingletonComponent;

@Module
@InstallIn(SingletonComponent.class)
public abstract class DeviceModule {

    @Binds
    public abstract HtmlParser bindHtmlParser(AndroidHtmlParser htmlParser);
}
