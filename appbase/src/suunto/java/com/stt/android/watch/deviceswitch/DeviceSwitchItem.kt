package com.stt.android.watch.deviceswitch

import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import com.stt.android.R
import com.stt.android.watch.WatchHelper
import com.suunto.connectivity.btscanner.ScannedSuuntoBtDevice
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoDeviceType

data class DeviceSwitchContainer(
    val currentDevice: SuuntoDeviceType? = null,
    val items: List<DeviceSwitchItem> = emptyList()
) {
    @StringRes
    fun getCurrentDeviceName(): Int {
        return WatchHelper.getStringResIdForSuuntoDeviceType(currentDevice)
    }
}

data class DeviceSwitchItem(
    val device: ScannedSuuntoBtDevice,
    val deviceSwitchClickListener: DeviceSwitchClickListener
) {

    @StringRes
    fun getDeviceName(): Int {
        return WatchHelper.getStringResIdForSuuntoDeviceType(device.deviceType)
    }

    fun getSerialNumberLastDigits(length: Int = 4): String = device.serial.takeLast(length)

    @DrawableRes
    fun getDeviceIcon(): Int {
        return if (device.deviceType.isEonComputer) {
            R.drawable.ic_device_model_eon
        } else {
            R.drawable.ic_device_model_watch
        }
    }

    fun onSwitchItemClick() {
        deviceSwitchClickListener.onDeviceSwitchClick(device)
    }
}
