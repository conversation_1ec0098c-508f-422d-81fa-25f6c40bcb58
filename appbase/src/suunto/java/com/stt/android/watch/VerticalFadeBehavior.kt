package com.stt.android.watch

import android.content.Context
import android.util.AttributeSet
import android.view.View
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.recyclerview.widget.RecyclerView
import kotlin.math.max

/**
 * VerticalFadeBehavior is a behavior that changes the child view alpha
 * value based on the target view scroll.
 */
class VerticalFadeBehavior(
    context: Context,
    attrs: AttributeSet
) : CoordinatorLayout.Behavior<View>(context, attrs) {
    override fun onDependentViewChanged(
        parent: CoordinatorLayout,
        child: View,
        dependency: View
    ): Boolean {
        // Make sure the child is shown if the dependency is not visible
        if (dependency.visibility != View.VISIBLE) {
            child.alpha = 1f
        } else {
            // Divide child height with SCROLL_LENGTH_DIVIDER to get the scroll distance
            val scrollDistance = child.height / SCROLL_LENGTH_DIVIDER

            val dependencyScrollY = if (dependency is RecyclerView) {
                dependency.computeVerticalScrollOffset()
            } else {
                dependency.scrollY
            }.toFloat()

            val alpha = max(MINIMUM_ALPHA, 1 - dependencyScrollY / scrollDistance)
            child.alpha = alpha
        }

        return super.onDependentViewChanged(parent, child, dependency)
    }

    private companion object {
        private const val SCROLL_LENGTH_DIVIDER = 4
        private const val MINIMUM_ALPHA = 0.03f
    }
}
