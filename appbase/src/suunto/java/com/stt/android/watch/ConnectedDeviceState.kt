package com.stt.android.watch

import com.stt.android.domain.device.ConnectedWatchState
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoDeviceType
import com.suunto.connectivity.watch.SpartanSyncResult

/**
 * ConnectedDeviceState represents a currently connected device.
 * Implementation is either [NoDeviceConnected] or [DeviceConnected]
 * with included device type
 */
sealed class ConnectedDeviceState

object NoDeviceConnected : ConnectedDeviceState()
data class DeviceConnected(
    val device: SuuntoDeviceType,
    val state: ConnectedWatchState,
    val syncResult: SpartanSyncResult,
    val sku: String
) : ConnectedDeviceState()
