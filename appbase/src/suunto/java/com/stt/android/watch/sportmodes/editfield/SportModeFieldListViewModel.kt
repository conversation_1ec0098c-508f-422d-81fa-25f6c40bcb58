package com.stt.android.watch.sportmodes.editfield

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.viewModelScope
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.domain.di.IoThread
import com.stt.android.domain.di.MainThread
import com.stt.android.domain.sportmodes.ChangeSportModesUseCase
import com.stt.android.domain.sportmodes.FetchSportModesUseCase
import com.stt.android.domain.sportmodes.Field
import com.stt.android.domain.sportmodes.FieldSection
import com.stt.android.watch.sportmodes.SportModeViewModel2
import dagger.hilt.android.lifecycle.HiltViewModel
import io.reactivex.Completable
import io.reactivex.Scheduler
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.rx2.await
import timber.log.Timber
import javax.inject.Inject

@HiltViewModel
class SportModeFieldListViewModel
@Inject constructor(
    savedStateHandle: SavedStateHandle,
    @IoThread private val ioThread: Scheduler,
    @MainThread private val mainThread: Scheduler,
    private val fetchSportModesUseCase: FetchSportModesUseCase,
    private val changeSportModeUseCase: ChangeSportModesUseCase,
    dispatchers: CoroutinesDispatchers
) : SportModeViewModel2(dispatchers), SportModeFieldChangeDelegate {

    private var fieldIndex: Int =
        SportModeFieldListFragmentArgs.fromSavedStateHandle(savedStateHandle).fieldIndex
    private var displayIndex: Int =
        SportModeFieldListFragmentArgs.fromSavedStateHandle(savedStateHandle).displayIndex
    var fieldId: String =
        SportModeFieldListFragmentArgs.fromSavedStateHandle(savedStateHandle).fieldId
    var displayId: String =
        SportModeFieldListFragmentArgs.fromSavedStateHandle(savedStateHandle).displayId

    private var initialSelectedSubFields: Array<out String>? =
        SportModeFieldListFragmentArgs.fromSavedStateHandle(
            savedStateHandle
        ).selectedSubFields

    private val isMultiField : Boolean
        get() = initialSelectedSubFields != null

    var sections by mutableStateOf<List<FieldSection>>(emptyList())
        private set

    var selectedIds by mutableStateOf(initialSelectedSubFields?.toList()?: listOf(fieldId))
        private set

    private val _navigateBack = MutableStateFlow(false)
    val navigateBack: StateFlow<Boolean> = _navigateBack.asStateFlow()

    override fun pickField(fieldId: String): Completable {
        return changeSportModeUseCase.changeField(
            sharedViewModel.getCurrentDisplays(),
            displayIndex,
            fieldIndex,
            fieldId
        )
            .doOnSuccess {
                sharedViewModel.setCurrentDisplays(it)
            }
            .retryWhen { errors ->
                handleError(errors)
            }
            .subscribeOn(ioThread)
            .observeOn(mainThread)
            .ignoreElement()
    }

    companion object {
        const val FIELD_INDEX = "fieldIndex"
        const val FIELD_ID = "fieldId"
        const val DISPLAY_INDEX = "displayIndex"
        const val DISPLAY_ID = "displayId"
        const val INVALID_ID = -1
    }

    fun loadData() {
        isLoading = true
        disposables.add(
            fetchSportModesUseCase.fetchFieldList(sharedViewModel.activityId, displayId, fieldIndex)
                .retryWhen { errors ->
                    handleError(errors)
                }
                .subscribeOn(ioThread)
                .observeOn(mainThread)
                .subscribe({ sections ->
                    if (sections.isEmpty()) {
                        isEmpty = true
                    } else {
                        this.sections = sections
                    }
                    isLoading = false
                }, { throwable ->
                    Timber.w(throwable, "Failed to fetch fields from the component")
                })
        )
    }

    fun save() {
        viewModelScope.launch {
            if (!isMultiField) {
                pickField(selectedIds.first()).await()
            } else {
                // The bridge doesn't support passing a list as parameter
                // So we are basically formatting the list as a List<String>
                // TODO: is better to move the logic right before calling the library?
                pickField(
                    selectedIds.joinToString(
                        prefix = "[",
                        postfix = "]"
                    ) { "\"$it\"" }).await()
            }
            _navigateBack.value = true
        }
    }

    fun setField(field: Field) {
        if (!isMultiField) {
            selectedIds = listOf(field.id)
            return
        }

        if (selectedIds.contains(field.id)) {
            if (selectedIds.size >= 2) {
                selectedIds = selectedIds.filter { it != field.id }
            }
        } else {
            selectedIds += field.id
        }
    }

    fun onBackEventHandled() {
        _navigateBack.value = false
    }
}
