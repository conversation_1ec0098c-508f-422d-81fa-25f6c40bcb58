package com.stt.android.watch.deviceswitch

import com.stt.android.common.viewstate.ViewStateEpoxyController
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.android.components.FragmentComponent

@Module
@InstallIn(FragmentComponent::class)
abstract class DeviceSwitchEpoxyControllerModule {

    @Binds
    abstract fun bindDeviceSwitchController(
        controller: DeviceSwitchEpoxyController
    ): ViewStateEpoxyController<DeviceSwitchContainer>
}
