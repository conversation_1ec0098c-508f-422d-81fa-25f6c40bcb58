package com.stt.android.watch.sportmodes.editdisplays

import android.content.Context
import android.text.Editable
import android.text.TextWatcher
import android.view.View
import android.view.inputmethod.EditorInfo
import android.view.inputmethod.InputMethodManager
import android.widget.TextView
import androidx.annotation.DrawableRes
import androidx.navigation.findNavController
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.textfield.TextInputEditText
import com.google.android.material.textfield.TextInputLayout
import com.stt.android.R
import com.stt.android.common.ui.BaseBindableItem
import com.stt.android.databinding.ItemEditDisplaysHeaderCarouselBinding
import com.stt.android.domain.sportmodes.Display
import com.stt.android.watch.WatchHelper
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoDeviceType
import com.xwray.groupie.GroupAdapter
import com.xwray.groupie.GroupieViewHolder
import com.yarolegovich.discretescrollview.DiscreteScrollView

data class SportModeEditDisplaysHeaderItem(
    val suuntoDeviceType: SuuntoDeviceType,
    val nameChangeSupported: Boolean,
    val name: String,
    val minNameLength: Int,
    val maxNameLength: Int,
    private val displays: List<Display>,
    private val maxDisplayCount: Int,
    private var currentDisplayIndex: Int,
    private val sportModeDisplayChangeListener: SportModeDisplayChangeListener,
    private val sportModeNameChangeListener: SportModeNameChangeListener,
    private val sku: String
) : BaseBindableItem<ItemEditDisplaysHeaderCarouselBinding>(), SaveDelegate, SportModeEditDisplaysClickHandler {

    override fun disableInput() {
        sportModeEditText.apply {
            val imm = context.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
            imm.hideSoftInputFromWindow(windowToken, 0)
            clearFocus()
            isEnabled = false
        }
    }

    override fun enableInput() {
        sportModeEditText.isEnabled = true
    }

    private val carouselAdapter = GroupAdapter<GroupieViewHolder>()
    lateinit var displayNameTextView: TextView
    lateinit var newDisplayButton: View
    lateinit var changeDisplayButton: View
    lateinit var sportModeTextInput: TextInputLayout
    lateinit var sportModeEditText: TextInputEditText

    private val itemChangedListener =
        DiscreteScrollView.OnItemChangedListener<RecyclerView.ViewHolder> { _, adapterPosition ->
            currentDisplayIndex = adapterPosition
            updateViewsWithCurrentDisplayIndex()
        }

    private fun updateViewsWithCurrentDisplayIndex() {
        if (currentDisplayIndex > displays.lastIndex) {
            sportModeDisplayChangeListener.onDisplayChanged(currentDisplayIndex, "")
            newDisplayButton.visibility = View.VISIBLE
            changeDisplayButton.visibility = View.GONE
            displayNameTextView.visibility = View.GONE
        } else {
            displayNameTextView.apply {
                text = context.getString(R.string.sport_modes_display, displays[currentDisplayIndex].name)
            }
            sportModeDisplayChangeListener.onDisplayChanged(currentDisplayIndex, displays[currentDisplayIndex].id)
            newDisplayButton.visibility = View.GONE
            displayNameTextView.visibility = View.VISIBLE
            changeDisplayButton.apply {
                if (displays[currentDisplayIndex].replaceable) {
                    visibility = View.VISIBLE
                    isEnabled = true
                } else {
                    visibility = View.INVISIBLE
                    isEnabled = false
                }
            }
        }
    }

    private val textWatcher = object : TextWatcher {
        override fun afterTextChanged(editable: Editable?) {
            editable?.let {
                val isValid = validateInput(it.toString())
                sportModeNameChangeListener.onNameChanged(editable.toString(), isValid)
            }
        }

        override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
        }

        override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
        }
    }

    init {
        for (i in 0 until displays.size) {
            carouselAdapter.add(SportModeEditDisplaysDisplayItem(displays[i], this))
        }
        if (displays.size < maxDisplayCount) {
            carouselAdapter.add(SportModeEditDisplaysDisplayItem(Display(), this))
        }
    }

    override fun getLayout() = R.layout.item_edit_displays_header_carousel

    override fun bind(viewBinding: ItemEditDisplaysHeaderCarouselBinding, position: Int) {
        super.bind(viewBinding, position)
        displayNameTextView = viewBinding.dataFieldsNumberText
        changeDisplayButton = viewBinding.changeText
        newDisplayButton = viewBinding.newDisplayButton
        displayNameTextView.text = displays[0].name
        sportModeTextInput = viewBinding.textInputLayout
        sportModeEditText = viewBinding.sportModeNameEditText
        viewBinding.displayList.apply {
            setItemTransformer { item, position ->
                val minAlpha = 0.5f
                val maxMinDiff = 0.5f
                val closenessToCenter = 1f - Math.abs(position)
                val alpha = minAlpha + maxMinDiff * closenessToCenter
                item.alpha = alpha
            }
            adapter = carouselAdapter
            viewBinding.bulletStrip.setDiscreteScrollView(this)
            scrollToPosition(currentDisplayIndex)
            addOnItemChangedListener(itemChangedListener)
            updateViewsWithCurrentDisplayIndex()
        }
        sportModeEditText.apply {
            addTextChangedListener(textWatcher)
            setOnEditorActionListener { view, actionId, _ ->
                if (actionId == EditorInfo.IME_ACTION_DONE) {
                    val imm = view.context.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
                    imm.hideSoftInputFromWindow(view.windowToken, 0)
                    clearFocus()
                    return@setOnEditorActionListener true
                }
                return@setOnEditorActionListener false
            }
        }
    }

    @DrawableRes
    fun getWatchDrawableResId(): Int {
        return WatchHelper.getDrawableResIdForSuuntoDeviceType(suuntoDeviceType, sku)
    }

    fun onChangeClicked(view: View) {
        sportModeDisplayChangeListener.onChangeDisplayLayoutOrFieldClicked()
        // watch sport mode displays
        // activity id
        // display index
        val action = SportModeEditDisplaysFragmentDirections.sportModeSelectDisplayAction()
        action.setDisplayIndex(currentDisplayIndex)
        view.findNavController().navigate(action)
    }

    override fun onDisplayClicked(view: View, position: Int) {
        // Proceed with display change if the clicked display equals current index
        if (position == currentDisplayIndex) {
            onChangeClicked(view)
        }
    }

    fun validateInput(input: String): Boolean {
        val byteArray = input.toByteArray(Charsets.UTF_8)
        val byteLength = byteArray.size
        return if (byteLength > maxNameLength || byteLength < minNameLength) {
            sportModeTextInput.isErrorEnabled = true
            val isTooLong = byteLength > maxNameLength
            sportModeTextInput.error = sportModeTextInput.context.getString(
                if (isTooLong) R.string.sport_mode_name_long_error else R.string.sport_mode_name_short_error
            )
            false
        } else {
            sportModeTextInput.isErrorEnabled = false
            true
        }
    }
}
