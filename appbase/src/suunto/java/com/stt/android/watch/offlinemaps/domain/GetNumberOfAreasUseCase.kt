package com.stt.android.watch.offlinemaps.domain

import com.stt.android.common.coroutines.CoroutinesDispatcherProvider
import com.stt.android.watch.offlinemaps.datasource.MapDownloadDataSource
import kotlinx.coroutines.withContext
import javax.inject.Inject

class GetNumberOfAreasUseCase @Inject constructor(
    private val dataSource: MapDownloadDataSource,
    private val dispatcherProvider: CoroutinesDispatcherProvider
) {
    suspend fun run() = withContext(dispatcherProvider.io) {
        dataSource.numberOfAreas()
    }
}
