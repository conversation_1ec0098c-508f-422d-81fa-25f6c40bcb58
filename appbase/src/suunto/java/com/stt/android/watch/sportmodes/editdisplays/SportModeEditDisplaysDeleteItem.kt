package com.stt.android.watch.sportmodes.editdisplays

import com.stt.android.R
import com.stt.android.common.ui.BaseBindableItem
import com.stt.android.databinding.ItemSportmodeDeleteButtonBinding

data class SportModeEditDisplaysDeleteItem(
    private val sportModeDisplayChangeListener: SportModeDisplayChangeListener
) : BaseBindableItem<ItemSportmodeDeleteButtonBinding>() {
    override fun getLayout() = R.layout.item_sportmode_delete_button

    fun onDeleteDisplayClicked() {
        sportModeDisplayChangeListener.onDeleteDisplayRequested()
    }
}
