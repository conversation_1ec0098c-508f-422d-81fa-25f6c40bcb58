package com.stt.android.watch.watchupdates

import android.net.Uri
import androidx.core.net.toUri
import androidx.lifecycle.LiveData
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.toLiveData
import com.stt.android.R
import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.analytics.AnalyticsEvent.WATCH_MANAGEMENT_CHECK_FOR_UPDATES
import com.stt.android.analytics.AnalyticsEvent.WATCH_MANAGEMENT_DOWNLOAD_UPDATE_FAILED
import com.stt.android.analytics.AnalyticsEvent.WATCH_MANAGEMENT_DOWNLOAD_UPDATE_LEARN_MORE
import com.stt.android.analytics.AnalyticsEvent.WATCH_MANAGEMENT_DOWNLOAD_UPDATE_STARTED
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.analytics.AnalyticsPropertyValue.WatchManagementUpdateFlowStatus
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.common.ui.ErrorEvent
import com.stt.android.common.viewstate.LoadingStateViewModel
import com.stt.android.controllers.UserSettingsController
import com.stt.android.coroutines.await
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.di.DeviceApiBaseUrl
import com.stt.android.domain.di.IoThread
import com.stt.android.domain.di.MainThread
import com.stt.android.domain.firmware.Version
import com.stt.android.ui.utils.SingleLiveEvent
import com.stt.android.utils.toV2
import com.stt.android.utils.toV2Flowable
import com.stt.android.watch.MissingCurrentWatchException
import com.stt.android.watch.SuuntoWatchModel
import com.suunto.connectivity.repository.commands.CheckForOtaUpdatesResponse
import com.suunto.connectivity.repository.commands.OtaUpdateState
import com.suunto.connectivity.repository.commands.StopOtaUpdateResponse
import com.suunto.connectivity.sdsmanager.model.MdsDeviceInfo
import com.suunto.connectivity.util.downloadUpdateFailureProperties
import com.suunto.connectivity.util.downloadUpdateStartedProperties
import com.suunto.connectivity.util.updateCheckAnalyticsProperties
import com.suunto.connectivity.util.watchUpdateLearnMoreProperties
import dagger.hilt.android.lifecycle.HiltViewModel
import io.reactivex.BackpressureStrategy
import io.reactivex.Scheduler
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.Job
import kotlinx.coroutines.cancelAndJoin
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import kotlinx.coroutines.reactive.asFlow
import kotlinx.coroutines.rx2.await
import kotlinx.coroutines.rx2.awaitFirst
import timber.log.Timber
import java.util.concurrent.TimeUnit
import java.util.concurrent.TimeoutException
import javax.inject.Inject

data class WatchUpdatesFragmentData(
    val firmwareInstalledOnWatch: VersionInfo?,
    val firmwareOnServer: VersionInfo?,
    /**
     * Selected firmware is fully prepared & ready to be installed on the watch,
     * either manually through watch's menu or automatically during night
     */
    val firmwareSelectedOnWatch: VersionInfo?,
    val otaUpdateState: OtaUpdateState,
    val progressPercentage: Int,
    val loadingDataFailed: Boolean,
    val firmwareSizeInBytes: Long,
    val watchLowBattery: Boolean,
    val onDownloadFirmwareClicked: () -> Unit,
    val waitingForFirmwareFileLoad: Boolean = false,
    val selectingFirmware: Boolean = false,
    val watchIsBusy: Boolean = false,
    val onStopOtaUpdateClicked: () -> Unit,
    val onLearnMoreClicked: () -> Unit,
    val onNeedHelpClicked: () -> Unit,
    val onConfirmInstallClicked: () -> Unit,
) {
    data class VersionInfo(
        /**
         * Either full descriptor with watch model name etc. or just the version
         */
        val versionName: String,
        val packageId: Long?,
        val versionLog: String,
    ) {
        /**
         * Defaults to false if can't be determined from the versionNames
         */
        fun isDowngradeComparedTo(other: VersionInfo?): Boolean {
            val myVersion = Version.fromWatchFirmwareName(versionName)
                ?: return false
            val otherVersion = Version.fromWatchFirmwareName(other?.versionName)
                ?: return false
            return myVersion < otherVersion
        }
    }

    val versionOnServerIsDowngradeFromInstalled: Boolean by lazy {
        firmwareOnServer?.isDowngradeComparedTo(firmwareInstalledOnWatch) ?: false
    }
}

sealed class WatchUpdatesActionEvent
data object ConfirmCancelToCheckDeepLink : WatchUpdatesActionEvent()
data class LearnMore(val data: WatchUpdatesFragmentData) : WatchUpdatesActionEvent()
data class NeedHelp(val data: WatchUpdatesFragmentData) : WatchUpdatesActionEvent()
data object DataLoadFailed : WatchUpdatesActionEvent()
data object ConfirmTryingToInstallDowngrade : WatchUpdatesActionEvent()
data object DowngradeDenied : WatchUpdatesActionEvent()
data object WatchAutoUpdatesDisabled : WatchUpdatesActionEvent()

@HiltViewModel
class WatchUpdatesViewModel
@Inject constructor(
    private val savedStateHandle: SavedStateHandle,
    @IoThread ioThread: Scheduler,
    @MainThread mainThread: Scheduler,
    coroutinesDispatchers: CoroutinesDispatchers,
    val suuntoWatchModel: SuuntoWatchModel,
    @DeviceApiBaseUrl val deviceApiBaseUrl: String,
    private val userSettingsController: UserSettingsController,
    private val amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker,
) : LoadingStateViewModel<WatchUpdatesFragmentData>(
    ioThread,
    mainThread,
    coroutinesDispatchers
) {
    private var deepLinkUrl: String?
        get() = savedStateHandle[WatchUpdatesActivity.KEY_EXTRA_DEEP_LINK_URL]
        set(value) {
            savedStateHandle[WatchUpdatesActivity.KEY_EXTRA_DEEP_LINK_URL] = value
        }

    private var observeProgressJob: Job? = null

    // User actions
    private val _actionLiveData = SingleLiveEvent<WatchUpdatesActionEvent>()
    val actionEvent: LiveData<WatchUpdatesActionEvent>
        get() = _actionLiveData

    val isWatchConnected: LiveData<Boolean> = suuntoWatchModel
        .stateChangeObservable
        .map { it.isConnected }
        .toV2Flowable()
        .onErrorReturn { false }
        .toLiveData()

    class DownloadFirmwareException : Exception()
    class WatchNotConnectedException : Exception()
    class OtaUpdateNotSupported : Exception()
    class GenericUpdateCheckFail : Exception()
    class SelectFirmwareFail : Exception()

    private var mdsDeviceInfo: MdsDeviceInfo? = null

    init {
        val errors = mapOf(
            DownloadFirmwareException::class to ErrorEvent(
                shouldHandle = false,
                errorStringRes = R.string.watch_updates_failed_to_download_update,
                canRetry = false,
                showCloseButton = false
            ),
            WatchNotConnectedException::class to ErrorEvent(
                shouldHandle = false,
                errorStringRes = R.string.watch_updates_watch_not_connected,
                canRetry = false,
                showCloseButton = false
            ),
            OtaUpdateNotSupported::class to ErrorEvent(
                shouldHandle = false,
                errorStringRes = R.string.watch_updates_ota_update_not_supported,
                canRetry = false,
                showCloseButton = false
            ),
            GenericUpdateCheckFail::class to ErrorEvent(
                shouldHandle = false,
                errorStringRes = R.string.watch_updates_failed_to_check_for_updates,
                canRetry = false,
                showCloseButton = false
            ),
            SelectFirmwareFail::class to ErrorEvent(
                shouldHandle = false,
                errorStringRes = R.string.error_generic_try_again,
                canRetry = false, // Button to retry is in the normal UI
                showCloseButton = false
            )
        )
        ErrorEvent.registerErrorEvents(errors)
    }

    inner class DataContainer {
        private var dataLoading: Boolean = true
        var currentData: WatchUpdatesFragmentData =
            WatchUpdatesFragmentData(
                firmwareInstalledOnWatch = null,
                firmwareOnServer = null,
                firmwareSelectedOnWatch = null,
                otaUpdateState = OtaUpdateState.Unknown,
                progressPercentage = 0,
                loadingDataFailed = false,
                firmwareSizeInBytes = 0,
                watchLowBattery = false,
                onDownloadFirmwareClicked = ::onDownloadFirmwareClicked,
                onStopOtaUpdateClicked = ::onStopOtaUpdateClicked,
                onLearnMoreClicked = ::onLearnMoreClicked,
                onNeedHelpClicked = ::onNeedHelpClicked,
                onConfirmInstallClicked = ::onConfirmInstallClicked
            )

        private fun notifyData(newData: WatchUpdatesFragmentData) {
            if (currentData != newData) {
                currentData = newData
                if (dataLoading) {
                    notifyLoading(currentData)
                } else {
                    notifyDataLoaded(currentData)
                }
            }
        }

        fun setDownloadInProgress(
            percentage: Int,
            firmwareSizeInBytes: Long
        ) {
            notifyData(
                currentData.copy(
                    otaUpdateState = OtaUpdateState.FirmwareTransferInProgress,
                    progressPercentage = percentage,
                    firmwareSizeInBytes = firmwareSizeInBytes
                )
            )
        }

        fun setWatchInstalledFirmware(version: String) {
            notifyData(
                currentData.copy(
                    firmwareInstalledOnWatch = WatchUpdatesFragmentData.VersionInfo(
                        versionName = version,
                        packageId = null,
                        versionLog = "",
                    )
                )
            )
        }

        fun setServerFirmware(version: String, packageId: Long?, versionLog: String) {
            notifyData(
                currentData.copy(
                    firmwareOnServer = WatchUpdatesFragmentData.VersionInfo(
                        versionName = version,
                        packageId = packageId,
                        versionLog = versionLog,
                    )
                )
            )
        }

        fun setLoadingDataFailed() {
            notifyData(currentData.copy(loadingDataFailed = true))
            _actionLiveData.value = DataLoadFailed
        }

        fun setUpdateAvailable() {
            notifyData(currentData.copy(otaUpdateState = OtaUpdateState.UpdateAvailable))
        }

        fun setDowngradeAvailable() {
            notifyData(currentData.copy(otaUpdateState = OtaUpdateState.DowngradeAvailable))
        }

        fun setDeviceUpToDate() {
            notifyData(currentData.copy(otaUpdateState = OtaUpdateState.DeviceUpToDate))
        }

        fun setWaitingForFirmwareFileLoad(waitingForFirmwareFileLoad: Boolean) {
            notifyData(currentData.copy(waitingForFirmwareFileLoad = waitingForFirmwareFileLoad))
        }

        fun setUpdateReadyToBeInstalledNeedsConfirmation(firmwareSize: Long) {
            notifyData(
                currentData.copy(
                    otaUpdateState = OtaUpdateState.UpdateReadyToInstallNeedsConfirmation,
                    firmwareSizeInBytes = firmwareSize
                )
            )
        }

        fun setDowngradeReadyToBeInstalledNeedsConfirmation(firmwareSize: Long) {
            notifyData(
                currentData.copy(
                    otaUpdateState = OtaUpdateState.DowngradeReadyToInstallNeedsConfirmation,
                    firmwareSizeInBytes = firmwareSize
                )
            )
        }

        fun setUpdateWaitingToBeInstalled(firmwareSize: Long) {
            notifyData(
                currentData.copy(
                    otaUpdateState = OtaUpdateState.UpdateWaitingToBeInstalled,
                    firmwareSizeInBytes = firmwareSize
                )
            )
        }

        fun setDowngradeWaitingToBeInstalled(firmwareSize: Long) {
            notifyData(
                currentData.copy(
                    otaUpdateState = OtaUpdateState.DowngradeWaitingToBeInstalled,
                    firmwareSizeInBytes = firmwareSize
                )
            )
        }

        @WatchManagementUpdateFlowStatus.Value
        fun getWatchManagementUpdateFlowStatus(): String? {
            return when (currentData.otaUpdateState) {
                OtaUpdateState.UpdateAvailable -> {
                    WatchManagementUpdateFlowStatus.UPDATE_FOUND
                }
                OtaUpdateState.UpdateWaitingToBeInstalled -> {
                    WatchManagementUpdateFlowStatus.UPDATE_READY_TO_INSTALL
                }
                OtaUpdateState.FirmwareTransferInProgress -> {
                    WatchManagementUpdateFlowStatus.UPDATE_BEING_DOWNLOADED
                }
                else -> null
            }
        }

        fun dataLoading(dataLoading: Boolean) {
            this.dataLoading = dataLoading
            if (dataLoading) {
                notifyLoading(currentData)
            } else {
                notifyDataLoaded(currentData)
            }
        }

        fun setWatchSelectedFirmware(
            descriptor: String?,
            packageId: Long?,
            versionLog: String,
        ) {
            val versionInfo = if (descriptor != null) {
                WatchUpdatesFragmentData.VersionInfo(
                    versionName = descriptor,
                    packageId = packageId,
                    versionLog = versionLog,
                )
            } else {
                null
            }
            notifyData(
                currentData.copy(
                    firmwareSelectedOnWatch = versionInfo
                )
            )
        }

        fun firmwareSelectionStart() {
            notifyData(currentData.copy(selectingFirmware = true))
        }

        fun firmwareSelectionFinish() {
            notifyData(currentData.copy(selectingFirmware = false))
        }

        fun watchIsBusy(isBusy: Boolean) {
            notifyData(currentData.copy(watchIsBusy = isBusy))
        }

        fun setWatchLowBattery(lowBattery: Boolean) {
            notifyData(currentData.copy(watchLowBattery = lowBattery))
        }
    }

    private val dataContainer = DataContainer()
    private var checkForOtaUpdatesResponse: CheckForOtaUpdatesResponse? = null

    init {
        load()
    }

    private fun load() {
        launch {
            observeProgressJob?.cancelAndJoin()
            val deepLink = deepLinkUrl
            if (deepLink.isNullOrEmpty()) {
                loadData()
            } else {
                loadDeepLinkData(deepLink)
            }
            if (!dataContainer.currentData.loadingDataFailed) {
                observeProgress()
            }
        }
    }

    fun onDownloadFirmwareClicked() {
        launch {
            val isDowngrade = dataContainer.currentData.versionOnServerIsDowngradeFromInstalled
            runSuspendCatching {
                dataContainer.setWaitingForFirmwareFileLoad(true)
                checkForOtaUpdatesResponse?.firmwareFileUri?.let {
                    // watch automatically upgrade if user taps update button
                    suuntoWatchModel.updateOtaManualDownloadFlag()

                    val response = suuntoWatchModel.startFirmwareTransfer(
                        it,
                        checkForOtaUpdatesResponse?.newFirmwareVersion
                    ).await()

                    if (!response.success) {
                        Timber.d("Download firmware failed: ${response.message}")
                        amplitudeAnalyticsTracker.trackEvent(
                            WATCH_MANAGEMENT_DOWNLOAD_UPDATE_FAILED,
                            downloadUpdateFailureProperties(
                                newFirmwareVersion = checkForOtaUpdatesResponse?.newFirmwareVersion,
                                mdsDeviceInfo = mdsDeviceInfo,
                                failureReason = "Download firmware failed: ${response.message}",
                                isDowngrade = isDowngrade
                            )
                        )
                        dataContainer.setWaitingForFirmwareFileLoad(false)
                        notifyError(
                            DownloadFirmwareException(),
                            dataContainer.currentData
                        )
                    } else {
                        if (isDowngrade) {
                            setAutomaticUpdatesEnabledForCurrentWatch(false)
                        }

                        amplitudeAnalyticsTracker.trackEvent(
                            WATCH_MANAGEMENT_DOWNLOAD_UPDATE_STARTED,
                            downloadUpdateStartedProperties(
                                newFirmwareVersion = checkForOtaUpdatesResponse?.newFirmwareVersion,
                                mdsDeviceInfo = mdsDeviceInfo,
                                isDowngrade = isDowngrade
                            )
                        )
                    }
                }
            }.onFailure { throwable ->
                Timber.w(throwable, "Download firmware failed")
                amplitudeAnalyticsTracker.trackEvent(
                    WATCH_MANAGEMENT_DOWNLOAD_UPDATE_FAILED,
                    downloadUpdateFailureProperties(
                        newFirmwareVersion = checkForOtaUpdatesResponse?.newFirmwareVersion,
                        mdsDeviceInfo = mdsDeviceInfo,
                        failureReason = "Download firmware failed: $throwable",
                        isDowngrade = isDowngrade
                    )
                )
                dataContainer.setWaitingForFirmwareFileLoad(false)
                notifyError(DownloadFirmwareException(), dataContainer.currentData)
            }
        }
    }

    fun onStopOtaUpdateClicked() {
        launch {
            runSuspendCatching {
                amplitudeAnalyticsTracker.trackEvent(
                    WATCH_MANAGEMENT_DOWNLOAD_UPDATE_FAILED,
                    downloadUpdateFailureProperties(
                        newFirmwareVersion = checkForOtaUpdatesResponse?.newFirmwareVersion,
                        mdsDeviceInfo = mdsDeviceInfo,
                        failureReason = "Update stopped by user",
                        isDowngrade = dataContainer.currentData.versionOnServerIsDowngradeFromInstalled
                    )
                )
                stopOtaUpdate()
            }.onFailure { throwable ->
                Timber.w(throwable, "Error in stopping ota update")
            }
        }
    }

    fun onLearnMoreClicked() {
        amplitudeAnalyticsTracker.trackEvent(
            WATCH_MANAGEMENT_DOWNLOAD_UPDATE_LEARN_MORE,
            watchUpdateLearnMoreProperties(
                newFirmwareVersion = checkForOtaUpdatesResponse?.newFirmwareVersion,
                mdsDeviceInfo = mdsDeviceInfo,
                updateFlowStatus = dataContainer.getWatchManagementUpdateFlowStatus(),
                isDowngrade = dataContainer.currentData.versionOnServerIsDowngradeFromInstalled
            )
        )
        _actionLiveData.value = LearnMore(dataContainer.currentData)
    }

    fun onNeedHelpClicked() {
        _actionLiveData.value = NeedHelp(dataContainer.currentData)
    }

    fun onConfirmInstallClicked() {
        val curData = dataContainer.currentData
        val serverFirmware = curData.firmwareOnServer
        val serverPackageId = serverFirmware?.packageId
            ?: return

        launch {
            dataContainer.firmwareSelectionStart()

            val selectFirmwareResponse = suuntoWatchModel.selectFirmware(
                serverPackageId,
                false
            )

            if (selectFirmwareResponse.success) {
                dataContainer.setWatchSelectedFirmware(
                    serverFirmware.versionName,
                    serverFirmware.packageId,
                    serverFirmware.versionLog
                )

                if (curData.versionOnServerIsDowngradeFromInstalled) {
                    setAutomaticUpdatesEnabledForCurrentWatch(false)
                    dataContainer.setDowngradeWaitingToBeInstalled(curData.firmwareSizeInBytes)
                } else {
                    dataContainer.setUpdateWaitingToBeInstalled(curData.firmwareSizeInBytes)
                }
            } else {
                // UI should revert to showing the option to select the firmware
                // after call to firmwareSelectionFinish, just notify about the error
                notifyError(SelectFirmwareFail())
            }

            dataContainer.firmwareSelectionFinish()

            if (suuntoWatchModel.supportsInstallSelectedFirmware()) {
                val installSelectedFirmwareResponse =
                    suuntoWatchModel.installSelectedFirmware(serverPackageId, false)
                if (installSelectedFirmwareResponse.success) {
                    Timber.d("Install selected firmware success")
                } else {
                    Timber.w("Install selected firmware failed: ${installSelectedFirmwareResponse.message}")
                }
            }
        }
    }

    fun onConfirmCheckDeeplinkWithOngoingUpdateClicked(cancelOngoingUpdate: Boolean) {
        launch {
            observeProgressJob?.cancelAndJoin() // make sure canceling the update doesn't update any state

            if (cancelOngoingUpdate) {
                stopOtaUpdate()
            } else {
                deepLinkUrl = null
            }

            load()
        }
    }

    fun onCancelDowngradeAttemptClicked() {
        if (deepLinkUrl != null) {
            observeProgressJob?.cancel()
            deepLinkUrl = null
            load()
        } else {
            // Generally we shouldn't be running into situations where we offer
            // a downgrade when the screen is opened without deeplink, but to prevent
            // loops of the dialog being shown handle it separately.
            _actionLiveData.value = DowngradeDenied
        }
    }

    suspend fun loadData() {
        runSuspendCatching {
            dataContainer.dataLoading(true)

            mdsDeviceInfo = suuntoWatchModel.stateChangeObservable.toV2().awaitFirst().deviceInfo
            mdsDeviceInfo?.let {
                dataContainer.setWatchInstalledFirmware(it.swVersion)
            }

            waitForWatchConnection() // Make sure watch hasn't disconnected before entering the screen
            checkWatchSelectedFirmware()
            checkWatchLowBatteryIfNeeded()

            val currentUpdateStatus =
                suuntoWatchModel.stateChangeObservable.toV2().awaitFirst().firmwareUpdateStatus
            if (!currentUpdateStatus.uploadInProgress) {
                dataContainer.setWaitingForFirmwareFileLoad(false)
                val otaUpdatesResponse = checkForNewOtaUpdates()
                if (!otaUpdatesResponse.success) {
                    sendUpdateCheckErrorAnalytics(
                        errorReason = "Check for new ota updates failed",
                        mdsDeviceInfo = mdsDeviceInfo,
                        deepLink = false
                    )
                    dataContainer.setLoadingDataFailed()
                } else {
                    checkForOtaUpdatesResponse = otaUpdatesResponse
                    handleSuccessfulCheckForOtaUpdatesResponse(
                        checkForOtaUpdatesResponse = otaUpdatesResponse,
                        deepLink = false,
                        mdsDeviceInfo = mdsDeviceInfo
                    )
                }
            }
            dataContainer.dataLoading(false)
        }.onFailure { exception ->
            Timber.w(exception, "OTA upload status check failed")
            when (exception) {
                is CancellationException -> {
                    sendUpdateCheckSuccessAnalytics(
                        result = AnalyticsPropertyValue.WatchManagementCheckForUpdatesResult.CANCEL,
                        mdsDeviceInfo = mdsDeviceInfo,
                        deepLink = false,
                        isDowngrade = null
                    )
                }

                is TimeoutException, is MissingCurrentWatchException -> {
                    showExceptionAndExit(WatchNotConnectedException())
                }

                else -> {
                    sendUpdateCheckErrorAnalytics(
                        errorReason = exception.toString(),
                        mdsDeviceInfo = mdsDeviceInfo,
                        deepLink = false
                    )
                }
            }
            dataContainer.setLoadingDataFailed()
            dataContainer.dataLoading(false)
        }
    }

    private suspend fun checkWatchLowBatteryIfNeeded() {
        runSuspendCatching {
            val lowBattery = suuntoWatchModel.getBatteryLevel().first().percentage in 0 until 20
            dataContainer.setWatchLowBattery(lowBattery)
        }.onFailure {
            Timber.w(it, "check watch low battery error")
        }
    }

    private suspend fun handleSuccessfulCheckForOtaUpdatesResponse(
        checkForOtaUpdatesResponse: CheckForOtaUpdatesResponse,
        deepLink: Boolean,
        mdsDeviceInfo: MdsDeviceInfo?
    ) {
        when (checkForOtaUpdatesResponse.otaUpdateState) {
            OtaUpdateState.UpdateAvailable -> {
                sendUpdateCheckSuccessAnalytics(
                    result = AnalyticsPropertyValue.WatchManagementCheckForUpdatesResult.UPDATE_FOUND,
                    mdsDeviceInfo = mdsDeviceInfo,
                    deepLink = deepLink,
                    isDowngrade = false
                )
                dataContainer.setUpdateAvailable()
                dataContainer.setServerFirmware(
                    checkForOtaUpdatesResponse.newFirmwareVersion,
                    checkForOtaUpdatesResponse.newFirmwarePackageId,
                    checkForOtaUpdatesResponse.newFirmwareVersionLog,
                )
            }
            OtaUpdateState.DowngradeAvailable -> {
                sendUpdateCheckSuccessAnalytics(
                    result = AnalyticsPropertyValue.WatchManagementCheckForUpdatesResult.UPDATE_FOUND,
                    mdsDeviceInfo = mdsDeviceInfo,
                    deepLink = deepLink,
                    isDowngrade = true
                )
                dataContainer.setDowngradeAvailable()
                dataContainer.setServerFirmware(
                    checkForOtaUpdatesResponse.newFirmwareVersion,
                    checkForOtaUpdatesResponse.newFirmwarePackageId,
                    checkForOtaUpdatesResponse.newFirmwareVersionLog,
                )
                _actionLiveData.value = ConfirmTryingToInstallDowngrade
            }
            OtaUpdateState.UpdateReadyToInstallNeedsConfirmation -> {
                sendUpdateCheckSuccessAnalytics(
                    result = AnalyticsPropertyValue.WatchManagementCheckForUpdatesResult.UPDATE_ALREADY_DOWNLOADED,
                    mdsDeviceInfo = mdsDeviceInfo,
                    deepLink = deepLink,
                    isDowngrade = true
                )
                dataContainer.setUpdateReadyToBeInstalledNeedsConfirmation(
                    checkForOtaUpdatesResponse.firmwareSize
                )
                dataContainer.setServerFirmware(
                    checkForOtaUpdatesResponse.newFirmwareVersion,
                    checkForOtaUpdatesResponse.newFirmwarePackageId,
                    checkForOtaUpdatesResponse.newFirmwareVersionLog,
                )
            }
            OtaUpdateState.DowngradeReadyToInstallNeedsConfirmation -> {
                sendUpdateCheckSuccessAnalytics(
                    result = AnalyticsPropertyValue.WatchManagementCheckForUpdatesResult.UPDATE_ALREADY_DOWNLOADED,
                    mdsDeviceInfo = mdsDeviceInfo,
                    deepLink = deepLink,
                    isDowngrade = true
                )
                dataContainer.setDowngradeReadyToBeInstalledNeedsConfirmation(
                    checkForOtaUpdatesResponse.firmwareSize
                )
                dataContainer.setServerFirmware(
                    checkForOtaUpdatesResponse.newFirmwareVersion,
                    checkForOtaUpdatesResponse.newFirmwarePackageId,
                    checkForOtaUpdatesResponse.newFirmwareVersionLog,
                )
                _actionLiveData.value = ConfirmTryingToInstallDowngrade
            }
            OtaUpdateState.UpdateWaitingToBeInstalled -> {
                sendUpdateCheckSuccessAnalytics(
                    result = AnalyticsPropertyValue.WatchManagementCheckForUpdatesResult.UPDATE_ALREADY_DOWNLOADED,
                    mdsDeviceInfo = mdsDeviceInfo,
                    deepLink = deepLink,
                    isDowngrade = false
                )
                dataContainer.setUpdateWaitingToBeInstalled(
                    checkForOtaUpdatesResponse.firmwareSize
                )
                dataContainer.setServerFirmware(
                    checkForOtaUpdatesResponse.newFirmwareVersion,
                    checkForOtaUpdatesResponse.newFirmwarePackageId,
                    checkForOtaUpdatesResponse.newFirmwareVersionLog,
                )
            }
            OtaUpdateState.DowngradeWaitingToBeInstalled -> {
                sendUpdateCheckSuccessAnalytics(
                    result = AnalyticsPropertyValue.WatchManagementCheckForUpdatesResult.UPDATE_ALREADY_DOWNLOADED,
                    mdsDeviceInfo = mdsDeviceInfo,
                    deepLink = deepLink,
                    isDowngrade = true
                )
                dataContainer.setDowngradeWaitingToBeInstalled(
                    checkForOtaUpdatesResponse.firmwareSize
                )
                dataContainer.setServerFirmware(
                    checkForOtaUpdatesResponse.newFirmwareVersion,
                    checkForOtaUpdatesResponse.newFirmwarePackageId,
                    checkForOtaUpdatesResponse.newFirmwareVersionLog,
                )
            }
            OtaUpdateState.DeviceUpToDate -> {
                sendUpdateCheckSuccessAnalytics(
                    result = AnalyticsPropertyValue.WatchManagementCheckForUpdatesResult.WATCH_UP_TO_DATE,
                    mdsDeviceInfo = mdsDeviceInfo,
                    deepLink = deepLink,
                    isDowngrade = false
                )
                dataContainer.setServerFirmware(
                    checkForOtaUpdatesResponse.newFirmwareVersion,
                    checkForOtaUpdatesResponse.newFirmwarePackageId,
                    checkForOtaUpdatesResponse.newFirmwareVersionLog,
                )
                dataContainer.setDeviceUpToDate()
            }
            OtaUpdateState.Unknown -> {
                sendUpdateCheckErrorAnalytics(
                    errorReason = "Unknown update state",
                    mdsDeviceInfo = mdsDeviceInfo,
                    deepLink = deepLink
                )
                if (deepLink) {
                    showExceptionAndExit(GenericUpdateCheckFail())
                }
                dataContainer.setLoadingDataFailed()
            }
            OtaUpdateState.FirmwareTransferInProgress -> {
                // No action.
                dataContainer.setServerFirmware(
                    checkForOtaUpdatesResponse.newFirmwareVersion,
                    checkForOtaUpdatesResponse.newFirmwarePackageId,
                    checkForOtaUpdatesResponse.newFirmwareVersionLog,
                )
            }
        }
    }

    private suspend fun observeProgress() {
        observeProgressJob?.cancelAndJoin()
        observeProgressJob = launch {
            var wasUploadInProgress = false
            runSuspendCatching {
                suuntoWatchModel.stateChangeObservable.toV2().toFlowable(BackpressureStrategy.BUFFER)
                    .asFlow()
                    .collect {
                        dataContainer.watchIsBusy(it.isDeviceBusy)

                        val updateStatus = it.firmwareUpdateStatus
                        if (updateStatus.firmwareVersioCurrentlyTransferred.isNotEmpty()) {
                            dataContainer.setServerFirmware(
                                updateStatus.firmwareVersioCurrentlyTransferred,
                                updateStatus.firmwarePackageIdCurrentlyTransferred,
                                updateStatus.firmwareVersionLogCurrentlyTransferred,
                            )
                        }
                        if (updateStatus.uploadInProgress) {
                            wasUploadInProgress = true
                            dataContainer.setDownloadInProgress(
                                percentage = updateStatus.progressPercentage,
                                firmwareSizeInBytes = updateStatus.fileSizeInBytes
                            )
                        } else if (wasUploadInProgress) {
                            wasUploadInProgress = false
                            val deepLink = deepLinkUrl
                            if (deepLink.isNullOrEmpty()) {
                                loadData()
                            } else {
                                loadDeepLinkData(deepLink)
                            }
                        }
                    }
            }.onFailure { e ->
                Timber.w(e, "Observing progress failed")
                dataContainer.setLoadingDataFailed()
            }
        }
    }

    private suspend fun loadDeepLinkData(deepLink: String) {
        dataContainer.dataLoading(true)
        runSuspendCatching {
            // Ensure watch exists.
            mdsDeviceInfo = suuntoWatchModel.stateChangeObservable.toV2().awaitFirst().deviceInfo
            mdsDeviceInfo?.let {
                dataContainer.setWatchInstalledFirmware(it.swVersion)
            }

            // Wait until watch connected. Application may have just started.
            waitForWatchConnection()

            // Check if watch supports OTA update.
            val watch = suuntoWatchModel.currentWatch.toV2().await()
            val otaSupported = watch.stateChangeObservable.toV2()
                .filter {
                    it.firmwareUpdateStatus.otaUpdateSupported
                }
                .map {
                    it.firmwareUpdateStatus.otaUpdateSupported
                }
                .timeout(OTA_UPDATE_SUPPORT_WAIT_SECONDS, TimeUnit.SECONDS)
                .onErrorReturn {
                    false
                }
                .firstOrError().await()
            if (!otaSupported) {
                sendUpdateCheckErrorAnalytics(
                    errorReason = "OTA not supported or OTA check failed",
                    mdsDeviceInfo = mdsDeviceInfo,
                    deepLink = true
                )
                showExceptionAndExit(OtaUpdateNotSupported())
                dataContainer.dataLoading(false)
                dataContainer.setLoadingDataFailed()
            } else {
                checkWatchSelectedFirmware()
                checkWatchLowBatteryIfNeeded()

                val currentUpdateStatus =
                    suuntoWatchModel.stateChangeObservable.toV2().awaitFirst().firmwareUpdateStatus
                if (currentUpdateStatus.uploadInProgress) {
                    Timber.d("Update is already in progress, checking deeplink would cancel it, ask from user")
                    _actionLiveData.value = ConfirmCancelToCheckDeepLink
                    return
                }

                Timber.d("Check deeplink %s with the device", deepLink)
                val uri = parseUriFromDeepLink(deepLink)
                if (uri != null) {
                    Timber.d("Firmware URL: $uri")
                    val otaUpdatesResponse = watch.checkForOtaUpdates(uri).toV2().await()
                    if (!otaUpdatesResponse.success) {
                        Timber.d("Ota update check failed with message :${otaUpdatesResponse.message}")
                        sendUpdateCheckErrorAnalytics(
                            errorReason = "OTA update response failed",
                            mdsDeviceInfo = mdsDeviceInfo,
                            deepLink = true
                        )
                        showExceptionAndExit(GenericUpdateCheckFail())
                        dataContainer.setLoadingDataFailed()
                    } else {
                        checkForOtaUpdatesResponse = otaUpdatesResponse
                        handleSuccessfulCheckForOtaUpdatesResponse(
                            checkForOtaUpdatesResponse = otaUpdatesResponse,
                            deepLink = true,
                            mdsDeviceInfo = mdsDeviceInfo
                        )
                    }
                } else {
                    Timber.d("Deep link parsing failed")
                    sendUpdateCheckErrorAnalytics(
                        errorReason = "Deep link parsing failed",
                        mdsDeviceInfo = mdsDeviceInfo,
                        deepLink = true
                    )
                    showExceptionAndExit(GenericUpdateCheckFail())
                    dataContainer.setLoadingDataFailed()
                }
            }
        }.onFailure { exception ->
            Timber.w(exception, "Error in loading deep link data")
            if (exception is CancellationException) {
                sendUpdateCheckSuccessAnalytics(
                    result = AnalyticsPropertyValue.WatchManagementCheckForUpdatesResult.CANCEL,
                    mdsDeviceInfo = mdsDeviceInfo,
                    deepLink = true,
                    isDowngrade = null
                )
            } else {
                sendUpdateCheckErrorAnalytics(
                    errorReason = exception.toString(),
                    mdsDeviceInfo = mdsDeviceInfo,
                    deepLink = true
                )
            }
            when (exception) {
                is MissingCurrentWatchException -> {
                    showExceptionAndExit(WatchNotConnectedException())
                }
                is TimeoutException -> {
                    showExceptionAndExit(WatchNotConnectedException())
                }
                else -> {
                    showExceptionAndExit(GenericUpdateCheckFail())
                }
            }
            dataContainer.setLoadingDataFailed()
        }
        dataContainer.dataLoading(false)
    }

    private suspend fun showExceptionAndExit(exception: Exception) {
        // Show spinner for a while
        delay(1000L)
        // Show watch not connected error
        notifyError(exception, dataContainer.currentData)
        // Wait a while before closing the view until error notification disappears.
        delay(3000L)
    }

    private fun parseUriFromDeepLink(deepLinkUrl: String): Uri? {
        return if (!DEEPLINK_REGEX.matches(deepLinkUrl)) {
            Timber.w("Deeplink URL $deepLinkUrl does not match regex $DEEPLINK_REGEX")
            null
        } else {
            val filename = DEEPLINK_REGEX.matchEntire(deepLinkUrl)?.groupValues?.get(1)
            if (filename == null) {
                Timber.e("Failure extracting filename from $deepLinkUrl, possible regex issue?")
                return null
            }
            return "${deviceApiBaseUrl}firmwares/$filename".toUri()
        }
    }

    private suspend fun checkForNewOtaUpdates(): CheckForOtaUpdatesResponse {
        return suuntoWatchModel.checkForNewOtaUpdates().await()
    }

    private suspend fun stopOtaUpdate(): StopOtaUpdateResponse {
        return suuntoWatchModel.stopOtaUpdate().await()
    }

    override fun retryLoading() {
        load()
    }

    private fun sendUpdateCheckErrorAnalytics(
        errorReason: String,
        mdsDeviceInfo: MdsDeviceInfo?,
        deepLink: Boolean
    ) {
        val properties = updateCheckAnalyticsProperties(
            mdsDeviceInfo = mdsDeviceInfo,
            result = AnalyticsPropertyValue.WatchManagementCheckForUpdatesResult.ERROR,
            method = if (deepLink) {
                AnalyticsPropertyValue.WatchManagementUpdateCheckMethod.DEEPLINK
            } else {
                AnalyticsPropertyValue.WatchManagementUpdateCheckMethod.MANUAL
            },
            errorReason = errorReason
        )
        amplitudeAnalyticsTracker.trackEvent(WATCH_MANAGEMENT_CHECK_FOR_UPDATES, properties)
    }

    private fun sendUpdateCheckSuccessAnalytics(
        @AnalyticsPropertyValue.WatchManagementCheckForUpdatesResult.Value result: String,
        mdsDeviceInfo: MdsDeviceInfo?,
        deepLink: Boolean,
        isDowngrade: Boolean?
    ) {
        val properties = updateCheckAnalyticsProperties(
            mdsDeviceInfo = mdsDeviceInfo,
            result = result,
            method = if (deepLink) {
                AnalyticsPropertyValue.WatchManagementUpdateCheckMethod.DEEPLINK
            } else {
                AnalyticsPropertyValue.WatchManagementUpdateCheckMethod.MANUAL
            },
            isDowngrade = isDowngrade
        )
        amplitudeAnalyticsTracker.trackEvent(WATCH_MANAGEMENT_CHECK_FOR_UPDATES, properties)
    }

    fun undoDisableAutomaticUpdatesForCurrentWatch() {
        launch {
            setAutomaticUpdatesEnabledForCurrentWatch(true)
        }
    }

    private suspend fun setAutomaticUpdatesEnabledForCurrentWatch(enabled: Boolean) {
        runSuspendCatching {
            val currentWatchSerial = suuntoWatchModel.currentWatch.await().serial
            val currentSettings = userSettingsController.settings
            val currentAutoUpdatesDisabledWatches = currentSettings.automaticUpdateDisabledWatches
            val currentWatchHasAutoUpdatesDisabled = currentAutoUpdatesDisabledWatches.contains(currentWatchSerial)
            val newAutoUpdatesDisabledWatches = if (enabled && currentWatchHasAutoUpdatesDisabled) {
                currentAutoUpdatesDisabledWatches.toMutableList().let {
                    it.remove(currentWatchSerial)
                    it.toTypedArray()
                }
            } else if (!enabled && !currentWatchHasAutoUpdatesDisabled) {
                (currentAutoUpdatesDisabledWatches + currentWatchSerial)
            } else {
                currentAutoUpdatesDisabledWatches
            }

            if (!currentAutoUpdatesDisabledWatches.contentEquals(newAutoUpdatesDisabledWatches)) {
                val updatedSettings = currentSettings.setAutomaticUpdatesDisabledWatches(
                    newAutoUpdatesDisabledWatches
                )
                userSettingsController.storeSettings(updatedSettings)

                if (!enabled) {
                    _actionLiveData.value = WatchAutoUpdatesDisabled
                }
            }
        }.onFailure { e ->
            Timber.w(e, "Error while enabling auto updates to watch")
        }
    }

    private suspend fun waitForWatchConnection() {
        val watch = suuntoWatchModel.currentWatch.toV2().await()
        // Make sure watch hasn't disconnected between entering device activity and this screen
        watch.stateChangeObservable.toV2()
            .filter {
                it.isConnected
            }
            .timeout(CONNECT_WAIT_SECONDS, TimeUnit.SECONDS)
            .firstOrError()
            .await()
    }

    private suspend fun checkWatchSelectedFirmware() {
        runSuspendCatching {
            with(suuntoWatchModel.getSelectedFirmware()) {
                if (success) {
                    dataContainer.setWatchSelectedFirmware(
                        selectedFirmwareDescriptor,
                        selectedFirmwarePackageId,
                        "",
                    )
                }
            }
        }.onFailure { e ->
            Timber.w(e, "Error getting currently selected watch firmware")
        }
    }

    companion object {
        @Suppress("RegExpRedundantEscape")
        private val DEEPLINK_REGEX = "^com[.-]sports-tracker[.-]suunto:\\/\\/ota\\/ota-fw\\/(\\S+)$".toRegex()
        private const val CONNECT_WAIT_SECONDS = 20L
        private const val OTA_UPDATE_SUPPORT_WAIT_SECONDS = 5L
    }
}
