package com.stt.android.watch.companionAssociation

import android.companion.CompanionDeviceManager
import android.content.Context
import android.content.pm.PackageManager
import com.gojuno.koptional.Optional
import com.gojuno.koptional.toOptional
import com.stt.android.FeatureFlags
import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.utils.PreferencesUtils
import com.stt.android.utils.STTConstants
import com.stt.android.watch.SuuntoWatchModel

object CompanionLinkingUtils {

    @JvmStatic
    fun createCompanionAssociationHelper(
        context: Context,
        suuntoWatchModel: SuuntoWatchModel,
        amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker,
        featureFlags: FeatureFlags
    ): Optional<CompanionAssociationHelper> =
        if (hasSystemCompanionFeature(context) &&
            isCompanionLinkingFeatureEnabled(context, featureFlags)
        ) {
            CompanionAssociationHelper(
                suuntoWatchModel,
                context.getSystemService(CompanionDeviceManager::class.java),
                amplitudeAnalyticsTracker
            )
        } else {
            null
        }.toOptional()

    @JvmStatic
    fun isCompanionLinkingFeatureEnabled(context: Context, featureFlags: FeatureFlags): Boolean {
        val testModeEnabled = PreferencesUtils.getFeatureToggleSharedPreferences(
            context,
            STTConstants.FeatureTogglePreferences.KEY_COMPANION_LINKING_TEST_MODE_FEATURE,
            STTConstants.FeatureTogglePreferences.KEY_COMPANION_LINKING_TEST_MODE_FEATURE_DEFAULT
        )
        return featureFlags.companionLinking() || testModeEnabled
    }

    @JvmStatic
    fun hasSystemCompanionFeature(context: Context): Boolean {
        return context.packageManager.hasSystemFeature(PackageManager.FEATURE_COMPANION_DEVICE_SETUP)
    }
}
