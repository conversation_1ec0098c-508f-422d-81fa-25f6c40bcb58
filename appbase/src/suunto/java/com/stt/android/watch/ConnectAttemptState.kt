package com.stt.android.watch

import com.suunto.connectivity.suuntoconnectivity.device.SuuntoDeviceType

enum class ConnectPhase {
    Cleared,

    // Spartan connect has been called, but there has not yet been any response.
    Connecting,

    // Connect succeeded
    ConnectSucceeded,

    // ConnectFailed
    ConnectFailed
}

data class ConnectAttemptState(
    val connectPhase: ConnectPhase,
    val suuntoDeviceType: SuuntoDeviceType,
    val throwable: Throwable? = null
)
