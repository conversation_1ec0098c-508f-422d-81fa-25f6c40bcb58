package com.stt.android.watch.sportmodes.selectdisplay

import androidx.lifecycle.SavedStateHandle
import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.AnalyticsEventProperty
import com.stt.android.analytics.AnalyticsProperties
import com.stt.android.domain.di.IoThread
import com.stt.android.domain.di.MainThread
import com.stt.android.domain.sportmodes.ChangeSportModesUseCase
import com.stt.android.domain.sportmodes.FetchSportModesUseCase
import com.stt.android.domain.workout.ActivityType
import com.stt.android.infomodel.getStIdForMcId
import com.stt.android.watch.sportmodes.SportModeViewModel
import com.stt.android.watch.sportmodes.editfield.SportModeSectionHeaderItem
import com.xwray.groupie.Section
import dagger.hilt.android.lifecycle.HiltViewModel
import io.reactivex.Completable
import io.reactivex.Scheduler
import io.reactivex.Single
import timber.log.Timber
import javax.inject.Inject

@HiltViewModel
class SportModeSelectDisplayViewModel
@Inject constructor(
    savedStateHandle: SavedStateHandle,
    @IoThread ioThread: Scheduler,
    @MainThread mainThread: Scheduler,
    private val fetchSportModesUseCase: FetchSportModesUseCase,
    private val changeSportModesUseCase: ChangeSportModesUseCase,
    private val amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker,
) : SportModeViewModel(ioThread, mainThread), OnDisplaySelectedDelegate {

    var displayIndex: Int =
        SportModeSelectDisplayFragmentArgs.fromSavedStateHandle(savedStateHandle).displayIndex

    override fun onDisplaySelected(displayId: String): Completable {
        return Single.fromCallable { sharedViewModel.getCurrentSettings() }
            .flatMap { watchSportModeSettings ->
                fetchSportModesUseCase.fetchMinNumberOfDisplays(watchSportModeSettings).map { watchSportModeSettings to it }
            }.flatMap { pair ->
                if (displayIndex <= sharedViewModel.currentDisplayCount - 1) {
                    changeSportModesUseCase.changeDisplay(
                        sharedViewModel.getCurrentDisplays(),
                        pair.first,
                        displayIndex,
                        displayId
                    )
                } else {
                    trackSportModeAddDisplayEvent(displayId)
                    if (pair.second > 0) {
                        sharedViewModel.lastSelectedDisplayIndex = displayIndex - pair.second + 1
                    }
                    changeSportModesUseCase.addDisplay(
                        sharedViewModel.getCurrentDisplays(),
                        pair.first,
                        displayIndex,
                        displayId
                    )
                }
            }.doOnSuccess {
                sharedViewModel.setCurrentMode(it)
            }
            .retryWhen { errors ->
                handleError(errors)
            }
            .subscribeOn(ioThread)
            .observeOn(mainThread)
            .ignoreElement()
    }

    private fun trackSportModeAddDisplayEvent(displayId: String) {
        val properties = AnalyticsProperties()
            .put(
                AnalyticsEventProperty.ACTIVITY_TYPE,
                ActivityType.valueOf(getStIdForMcId(sharedViewModel.activityId)).simpleName
            )
            .put(AnalyticsEventProperty.SPORT_MODE_DISPLAY_TYPE_WITHOUT_INDEX, displayId)
            .put(AnalyticsEventProperty.SPORT_MODE_TOTAL_DISPLAYS, sharedViewModel.currentDisplayCount + 1)
        amplitudeAnalyticsTracker.trackEvent(AnalyticsEvent.SPORT_MODE_ADD_DISPLAY, properties)
    }

    companion object {
        const val DISPLAY_INDEX = "displayIndex"
        const val INVALID_ID = -1
    }

    override fun loadData() {
        notifyDataLoading()
        disposables.add(
            fetchSportModesUseCase.fetchDisplayList(
                sharedViewModel.getCurrentDisplays(),
                sharedViewModel.activityId,
                displayIndex
            )
                .map {
                    it.map {
                        val section = Section()
                        section.setHeader(SportModeSectionHeaderItem(it.name, it.description ?: ""))
                        section.addAll(
                            it.displays.sortedWith(
                                Comparator { display1, display2 ->
                                    (display1.name).compareTo(display2.name)
                                }
                            ).map {
                                SportModeSelectDisplayItem(it.id, it.name, this, it.icon)
                            }.toList()
                        )
                        section
                    }
                }
                .retryWhen { errors ->
                    handleError(errors)
                }
                .subscribeOn(ioThread)
                .observeOn(mainThread)
                .subscribe({ items ->
                    if (items.isEmpty()) {
                        notifyEmptyState()
                    } else {
                        notifyDataLoaded(items)
                    }
                }, { Timber.w(it) })
        )
    }
}
