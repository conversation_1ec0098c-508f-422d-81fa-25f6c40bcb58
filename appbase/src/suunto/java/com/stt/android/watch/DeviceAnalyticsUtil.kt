package com.stt.android.watch

import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.AnalyticsEventProperty
import com.stt.android.analytics.AnalyticsProperties
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.analytics.EmarsysAnalytics
import com.stt.android.domain.device.DeviceInfo
import com.suunto.connectivity.suuntoconnectivity.device.AnalyticsDevicePropertyHelper
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoDeviceType
import javax.inject.Inject

/**
 * DeviceAnalyticsUtil contains analytics call for DeviceActivity and the related fragments.
 */
class DeviceAnalyticsUtil
@Inject constructor(
    private val emarsysAnalytics: EmarsysAnalytics,
    private val amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker,
) {

    fun onHelpShiftLinkClicked(articleId: String, source: String) {
        val properties = AnalyticsProperties()
            .put(AnalyticsEventProperty.SOURCE, source)
            .put(AnalyticsEventProperty.ARTICLE, articleId)
        amplitudeAnalyticsTracker.trackEvent(AnalyticsEvent.HELPSHIFT_LINK_CLICKED, properties)
    }

    fun onRestartPairing(deviceType: SuuntoDeviceType?, deviceInfo: DeviceInfo?) {
        deviceType?.let {
            val model = AnalyticsDevicePropertyHelper.getWatchModelNameForSuuntoDeviceType(deviceType)
            val properties = AnalyticsProperties()
                .put(AnalyticsEventProperty.SUUNTO_WATCH_MODEL, model)

            deviceInfo?.let {
                properties.put(AnalyticsEventProperty.SUUNTO_WATCH_SERIAL_NUMBER, deviceInfo.serial)
                    .put(AnalyticsEventProperty.SUUNTO_WATCH_FIRMWARE_VERSION, deviceInfo.fwVersion)
            }

            amplitudeAnalyticsTracker.trackEvent(AnalyticsEvent.SUUNTO_PAIRING_RESTART_PAIRING, properties)
        }
    }

    fun onShowNotificationSettings(notificationsEnabled: Boolean) {
        val trigger = if (notificationsEnabled) {
            AnalyticsPropertyValue.PhoneSettingsTrigger.NOTIFICATIONS_ON
        } else {
            AnalyticsPropertyValue.PhoneSettingsTrigger.NOTIFICATIONS_OFF
        }
        amplitudeAnalyticsTracker.trackEvent(
            AnalyticsEvent.SUUNTO_OPEN_PHONE_SETTINGS,
            AnalyticsProperties().put(AnalyticsEventProperty.TRIGGER, trigger)
        )
    }

    fun onPermissionButtonClick(button: String) {
        amplitudeAnalyticsTracker.trackEvent(
            AnalyticsEvent.PERMISSION_ENABLED_BY_BUTTON,
            AnalyticsProperties().put(AnalyticsEventProperty.BUTTON, button)
        )
    }

    fun onLocationPermissionScreenShowed(context: String) {
        amplitudeAnalyticsTracker.trackEvent(
            AnalyticsEvent.PERMISSION_SCREEN,
            AnalyticsProperties()
                .put(AnalyticsEventProperty.PERMISSION_TYPE, AnalyticsPropertyValue.PermissionType.LOCATION)
                .put(AnalyticsEventProperty.CONTEXT, context)
        )
    }

    fun onNearbyDevicesPermissionScreenShowed(context: String) {
        amplitudeAnalyticsTracker.trackEvent(
            AnalyticsEvent.PERMISSION_SCREEN,
            AnalyticsProperties()
                .put(AnalyticsEventProperty.PERMISSION_TYPE, AnalyticsPropertyValue.PermissionType.NEARBY_DEVICES)
                .put(AnalyticsEventProperty.CONTEXT, context)
        )
    }

    fun onRequestLocationPermissionsResult(context: String, result: String) {
        amplitudeAnalyticsTracker.trackEvent(
            AnalyticsEvent.PERMISSION_PROMPT_RESPONSE,
            AnalyticsProperties().put(
                AnalyticsEventProperty.PERMISSION_TYPE,
                AnalyticsPropertyValue.PermissionType.LOCATION
            ).put(AnalyticsEventProperty.CONTEXT, context).put(AnalyticsEventProperty.PERMISSION_RESULT, result)
        )
    }

    fun onRequestNearbyDevicesPermissionsResult(context: String, result: String) {
        amplitudeAnalyticsTracker.trackEvent(
            AnalyticsEvent.PERMISSION_PROMPT_RESPONSE,
            AnalyticsProperties().put(
                AnalyticsEventProperty.PERMISSION_TYPE,
                AnalyticsPropertyValue.PermissionType.NEARBY_DEVICES
            ).put(AnalyticsEventProperty.CONTEXT, context).put(AnalyticsEventProperty.PERMISSION_RESULT, result)
        )
    }

    // TODO this is probably not used and should not belong here as a wrapper as does nothing
    fun trackEvent(@AnalyticsEvent.EventName event: String) {
        amplitudeAnalyticsTracker.trackEvent(event)
    }

    fun onPermissionSkipped(context: String) {
        amplitudeAnalyticsTracker.trackEvent(
            AnalyticsEvent.PERMISSION_SKIPPED,
            AnalyticsProperties().put(AnalyticsEventProperty.CONTEXT, context)
        )
    }

    fun onSuunto7HelpShiftArticleOpened(
        isWearOsPaired: Boolean,
        source: String
    ) {
        val properties = AnalyticsProperties()
            .put(
                AnalyticsEventProperty.RELATED_WATCH_MODEL,
                AnalyticsPropertyValue.RelatedWatchModelProperty.SUUNTO7
            )
            .putYesNo(AnalyticsEventProperty.WATCH_PAIRED_ON_OS_LEVEL, isWearOsPaired)
            .put(AnalyticsEventProperty.SOURCE, source)
        amplitudeAnalyticsTracker.trackEvent(AnalyticsEvent.HELPSHIFT_ARTICLE_OPENED, properties)
    }

    fun trackAmbit3OrTraverseFoundEvent() {
        emarsysAnalytics.trackEvent(AnalyticsEvent.SUUNTO_AMBIT3_OR_TRAVERSE_FOUND)
        trackEvent(AnalyticsEvent.SUUNTO_AMBIT3_OR_TRAVERSE_FOUND)
    }
}
