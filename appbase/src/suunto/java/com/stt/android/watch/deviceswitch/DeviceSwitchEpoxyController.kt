package com.stt.android.watch.deviceswitch

import com.stt.android.common.viewstate.ViewState
import com.stt.android.common.viewstate.ViewStateEpoxyController
import com.stt.android.deviceSwitch
import javax.inject.Inject

class DeviceSwitchEpoxyController @Inject constructor() :
    ViewStateEpoxyController<DeviceSwitchContainer>() {
    override fun buildModels(viewState: ViewState<DeviceSwitchContainer?>) {
        viewState.data?.let { deviceSwitchContainer ->
            deviceSwitchContainer.items
                .distinctBy { it.device.serial } // ensure epoxy doesn't crash by adding multiple items with same id
                .forEach { item ->
                    deviceSwitch {
                        id(item.device.serial)
                        deviceSwitchItem(item)
                        currentDeviceNameRes(deviceSwitchContainer.getCurrentDeviceName())
                    }
                }
        }
    }
}
