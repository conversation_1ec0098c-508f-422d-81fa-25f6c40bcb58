package com.stt.android.watch

import com.stt.android.R

/**
 * DeviceImageInfo contains device info about device foreground and background images.
 */
data class DeviceImageInfo(
    val image: Int = R.drawable.watch_activity_ghost_white,
    private val showBackground: Boolean = false,
    private val requiresRectangleBackground: Boolean = false
) {

    val bgImage: BackgroundImage = when {
        !showBackground -> BackgroundImage.NONE
        requiresRectangleBackground -> BackgroundImage.RECTANGLE
        else -> BackgroundImage.ROUND
    }
}

/**
 * Device background image types
 */
enum class BackgroundImage {
    ROUND,
    RECTANGLE,
    NONE
}
