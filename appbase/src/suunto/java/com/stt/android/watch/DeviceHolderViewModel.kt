package com.stt.android.watch

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.content.SharedPreferences
import android.net.Uri
import android.os.Build
import androidx.activity.result.ActivityResultLauncher
import androidx.annotation.RequiresApi
import androidx.appcompat.app.AlertDialog
import androidx.core.content.edit
import androidx.core.text.buildSpannedString
import androidx.core.text.inSpans
import androidx.databinding.ObservableBoolean
import androidx.databinding.ObservableField
import androidx.databinding.ObservableInt
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.asFlow
import androidx.lifecycle.map
import androidx.lifecycle.viewModelScope
import com.jakewharton.rxrelay2.BehaviorRelay
import com.stt.android.R
import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.AnalyticsEventProperty
import com.stt.android.analytics.AnalyticsProperties
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.analytics.AnalyticsPropertyValue.NO
import com.stt.android.analytics.AnalyticsPropertyValue.YES
import com.stt.android.analytics.AnalyticsUserProperty
import com.stt.android.analytics.EmarsysAnalytics
import com.stt.android.common.ui.RxViewModel
import com.stt.android.controllers.CurrentUserController
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.data.device.DeviceInfoApi
import com.stt.android.di.FeatureTogglePreferences
import com.stt.android.di.SuuntoSharedPrefs
import com.stt.android.domain.android.DeviceFeatureStates
import com.stt.android.domain.android.FetchBluetoothEnabledUseCase
import com.stt.android.domain.android.FetchLocationEnabledUseCase
import com.stt.android.domain.android.IsLocationPermissionGrantedUseCase
import com.stt.android.domain.android.IsNearbyDevicesPermissionGrantedUseCase
import com.stt.android.domain.device.ConnectedWatchConnectionState
import com.stt.android.domain.device.ConnectedWatchState
import com.stt.android.domain.device.DeviceAboutInfoUseCase
import com.stt.android.domain.device.DeviceConnectionStateUseCase
import com.stt.android.domain.device.DeviceInfo
import com.stt.android.domain.device.DeviceInfoWear
import com.stt.android.domain.device.UploadProgressState
import com.stt.android.domain.di.IoThread
import com.stt.android.domain.di.MainThread
import com.stt.android.domain.firmware.Version
import com.stt.android.domain.firstpairing.FirstPairingInfoUseCase
import com.stt.android.domain.movescount.MovescountAppInfoUseCase
import com.stt.android.domain.sportmodes.DownloadSportModeComponentUseCase
import com.stt.android.domain.sportmodes.FetchSportModesUseCase
import com.stt.android.domain.sportmodes.InitSportModeComponentUseCase
import com.stt.android.domain.sportmodes.SupportMode
import com.stt.android.domain.workouts.autolocation.IsAutoLocationEnabledUseCase
import com.stt.android.domain.workouts.autolocation.SaveAutoLocationSettingUseCase
import com.stt.android.ui.activities.settings.watch.notifications.domain.FetchNotificationsEnabledUseCase
import com.stt.android.ui.activities.settings.watch.notifications.domain.SetNotificationsCategoryEnabledUseCase
import com.stt.android.ui.activities.settings.watch.notifications.domain.SetNotificationsEnabledUseCase
import com.stt.android.ui.fragments.settings.PrivacySettingDialog.Companion.PRIVACY_SETTING_IN_DEVICE_ACTIVITY
import com.stt.android.ui.utils.CenteredImageSpan
import com.stt.android.ui.utils.SingleLiveEvent
import com.stt.android.utils.BluetoothUtils
import com.stt.android.utils.STTConstants
import com.stt.android.utils.isBackgroundLocationPermissionGranted
import com.stt.android.utils.toV2
import com.stt.android.watch.device.CheckIfSwitchDeviceUseCase
import com.stt.android.watch.deviceswitch.DeviceSwitchClickListener
import com.stt.android.watch.deviceswitch.DeviceSwitchContainer
import com.stt.android.watch.deviceswitch.DeviceSwitchItem
import com.stt.android.watch.forcedupdate.ShouldShowOnboardingUseCase
import com.stt.android.watch.gearevent.GearEventSender
import com.stt.android.watch.pair.SetupPreferenceUseCase
import com.stt.android.watch.preference.SetupPreference
import com.stt.android.watch.sendlog.SendLogHelper
import com.suunto.connectivity.ScLib
import com.suunto.connectivity.btscanner.ScannedSuuntoBtDevice
import com.suunto.connectivity.btscanner.SuuntoDataLayerScanner
import com.suunto.connectivity.btscanner.SuuntoLeScanner
import com.suunto.connectivity.deviceid.ISuuntoDeviceCapabilityInfo
import com.suunto.connectivity.deviceid.SuuntoDeviceCapabilityInfoProvider
import com.suunto.connectivity.repository.AnalyticsUtils
import com.suunto.connectivity.repository.AppChannel
import com.suunto.connectivity.repository.AppInfo
import com.suunto.connectivity.repository.PairingState
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoBtDevice
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoDeviceType
import com.suunto.connectivity.util.NotificationSettingsHelper
import com.suunto.connectivity.watch.SpartanSyncResult
import com.suunto.connectivity.watch.WatchState
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import hu.akarnokd.rxjava.interop.RxJavaInterop
import io.reactivex.BackpressureStrategy
import io.reactivex.Completable
import io.reactivex.Flowable
import io.reactivex.Observable
import io.reactivex.Scheduler
import io.reactivex.Single
import io.reactivex.disposables.Disposable
import io.reactivex.rxkotlin.Flowables
import io.reactivex.rxkotlin.Singles
import io.reactivex.rxkotlin.subscribeBy
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.distinctUntilChangedBy
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.mapNotNull
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch
import kotlinx.coroutines.rx2.await
import kotlinx.coroutines.rx2.rxCompletable
import kotlinx.coroutines.rx2.rxSingle
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.io.File
import java.util.concurrent.TimeUnit
import java.util.concurrent.TimeoutException
import javax.inject.Inject
import kotlin.coroutines.Continuation
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine

/**
 * DeviceHolderViewModel is the view holder shared between all of the device
 * activity fragments. This view holder checks that required permissions and
 * features for device management are available and enabled. Feature and
 * permission state changes are signaled with a LiveData instance.
 */
@HiltViewModel
class DeviceHolderViewModel
@Inject constructor(
    @ApplicationContext private val context: Context,
    bluetoothUseCase: FetchBluetoothEnabledUseCase,
    locationUseCase: FetchLocationEnabledUseCase,
    private val locationPermissionUseCase: IsLocationPermissionGrantedUseCase,
    private val nearbyDevicesPermissionUseCase: IsNearbyDevicesPermissionGrantedUseCase,
    private val suuntoWatchModel: SuuntoWatchModel,
    private val deviceConnectionStateUseCase: DeviceConnectionStateUseCase,
    private val textFormatter: DeviceTextFormatter,
    private val deviceAboutInfoUseCase: DeviceAboutInfoUseCase,
    private val downloadSportModeComponentUseCase: DownloadSportModeComponentUseCase,
    private val initSportModeComponentUseCase: InitSportModeComponentUseCase,
    private val fetchSportModesUseCase: FetchSportModesUseCase,
    @SuuntoSharedPrefs private val sharedPreferences: SharedPreferences,
    @FeatureTogglePreferences private val featureTogglePreferences: SharedPreferences,
    private val gearEventSender: GearEventSender,
    private val analyticsUtil: DeviceAnalyticsUtil,
    private val movescountAppInfoUseCase: MovescountAppInfoUseCase,
    private val emarsysAnalytics: EmarsysAnalytics,
    private val amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker,
    private val notificationSettingsHelper: NotificationSettingsHelper,
    private val deviceInfoApi: DeviceInfoApi,
    private val isAutoLocationEnabledUseCase: IsAutoLocationEnabledUseCase,
    private val saveAutoLocationEnabledUseCase: SaveAutoLocationSettingUseCase,
    private val currentUserController: CurrentUserController,
    private val scLib: ScLib,
    private val featureStates: DeviceFeatureStates,
    private val suuntoLeScanner: SuuntoLeScanner,
    private val suuntoDlScanner: SuuntoDataLayerScanner,
    private val suuntoPlusNavigator: SuuntoPlusNavigator,
    private val shouldShowOnboardingUseCase: ShouldShowOnboardingUseCase,
    private val firstPairingInfoUseCase: FirstPairingInfoUseCase,
    private val sendLogHelper: SendLogHelper,
    private val weChatConnectionStateHelper: WeChatConnectionStateHelper,
    private val setNotificationsEnabledUseCase: SetNotificationsEnabledUseCase,
    private val setNotificationsCategoryEnabledUseCase: SetNotificationsCategoryEnabledUseCase,
    private val fetchNotificationsEnabledUseCase: FetchNotificationsEnabledUseCase,
    private val checkIfSwitchDeviceUseCase: CheckIfSwitchDeviceUseCase,
    private val setupPreferenceUseCase: SetupPreferenceUseCase,
    private val isRunDeviceUseCase: IsRunDeviceUseCase,
    @IoThread ioThread: Scheduler,
    @MainThread mainThread: Scheduler
) : RxViewModel(ioThread, mainThread), DeviceSwitchClickListener {

    data class PairingFailInfo(
        val throwable: Throwable?,
        val suuntoBtDevice: SuuntoBtDevice?
    )

    private val currentWatchModel = RxJavaInterop.toV2Single(suuntoWatchModel.currentWatch)
    private val syncCurrentWatch = RxJavaInterop.toV2Single(suuntoWatchModel.syncCurrentWatchFull())
    private val lastSyncResult = RxJavaInterop.toV2Single(suuntoWatchModel.lastSyncResult)
    private val stateChangeObservable =
        RxJavaInterop.toV2Observable(suuntoWatchModel.stateChangeObservable)
    private var notificationStateDisposable: Disposable? = null
    private val permissionsFlowable: Flowable<DevicePermissions>

    private var deviceDisposable: Disposable? = null
    val lastSyncText = ObservableField<CharSequence>("")
    val sportModeSupported = ObservableBoolean(false)
    val sportModeEnabled = ObservableBoolean(false)
    val suuntoPlusStoreSupported = ObservableBoolean(false)
    val suuntoPlusFeatureSelectionEnabled = ObservableBoolean(false)
    val suuntoPlusWatchfaceEnabled = ObservableBoolean(false)
    val suuntoPlusGuidesSupported = ObservableBoolean(false)
    val structuredWorkoutPlannerSupported = ObservableBoolean(false)
    val widgetCustomizationSupported = ObservableBoolean(false)
    val showWidgetCustomizationBadge = ObservableBoolean(false)
    val offlineMapsSupported = ObservableBoolean(false)
    val showOfflineMapsBadge = ObservableBoolean(false)
    val diveModeSupported = ObservableBoolean(false)
    val diveModeEnabled = ObservableBoolean(false)
    val watchNotificationsSupported = ObservableBoolean(false)
    val watchNotificationSummary = ObservableField("")
    val notificationsOffIconEnabled = ObservableBoolean(false)
    val isDataLayerDevice = ObservableBoolean(false)
    val autoLocationSettingEnabled = ObservableBoolean(false)
    val forcedUpdateRequired = ObservableBoolean(false)
    val syncWechatRunSupported = ObservableBoolean(false)
    val syncWechatRunConnected = ObservableBoolean(false)
    val keepConnectedSettingConnected = ObservableBoolean(false)

    // TODO  set actual value
    val offlineMusicSupported = ObservableBoolean(false)
    private var sportModeSupportMode = SupportMode.NOT_SUPPORTED
    var pairingFailInfo: PairingFailInfo? = null

    // LiveData instances used to hold the permission states
    val locationAllowed = MutableLiveData<Boolean>()
    val nearbyDevicesAllowed = MutableLiveData<Boolean>()
    val nearbyDevicesRequired = MutableLiveData<Boolean>()
    val bluetoothEnabled = MutableLiveData<Boolean>()
    val locationEnabled = MutableLiveData<Boolean>()
    var suuntoDeviceType: SuuntoDeviceType? = null
    var suuntoPlusStoreTitle = ObservableField("")
    var suuntoPlusStoreText = ObservableField("")
    val backgroundImageRes = ObservableInt(R.drawable.suunto_plus_store_link_ad)

    val isRunDevice
        get() = suuntoDeviceType?.isRunDevice ?: isRunDeviceUseCase()

    lateinit var onboardingResultLauncher: ActivityResultLauncher<Intent>

    /**
     * Is location permission granted by the user
     */
    private val locationAllowedRelay = BehaviorRelay.create<Boolean>()

    /**
     * Is nearby devices permission granted by the user
     */
    private val nearbyDevicesAllowedRelay = BehaviorRelay.create<Boolean>()

    /**
     * Contains the latest connected device state, either [NoDeviceConnected] or
     * [DeviceConnected]
     */
    private val connectedDeviceRelay = BehaviorRelay.create<ConnectedDeviceState>()

    /**
     * Contains current device state. State is initialized as DeviceStateLoading
     */
    val deviceStateRelay: BehaviorRelay<DeviceStateEvent> =
        BehaviorRelay.createDefault(DeviceStateLoading)

    // Device state
    private val _deviceStateEvent = MutableLiveData<DeviceStateEvent>()
    val deviceStateEvent: LiveData<DeviceStateEvent>
        get() = _deviceStateEvent

    // User actions
    private val _actionLiveData = SingleLiveEvent<DeviceActionEvent>()
    val actionEvent: LiveData<DeviceActionEvent>
        get() = _actionLiveData

    // Battery optimization and notification access check
    val batteryAndNotificationCheckEvent = SingleLiveEvent<Any>()
    private lateinit var notificationAccessDisposable: Disposable

    private val _toastLiveData = SingleLiveEvent<String>()
    val toastLiveData: LiveData<String>
        get() = _toastLiveData

    private val _openOnboardingEvent = SingleLiveEvent<SuuntoDeviceType>()
    val openOnboardingEvent: LiveData<SuuntoDeviceType>
        get() = _openOnboardingEvent

    internal lateinit var setupPreferenceResultLauncher: ActivityResultLauncher<Intent>
    internal var setupPreferenceContinuation: Continuation<SetupPreference>? = null

    /**
     * Show connection alert
     */
    val connectionAlertAvailable = MutableLiveData<Boolean>().apply { value = false }

    val autoLocationSettingAvailable: LiveData<Boolean> = deviceStateEvent.map {
        when (it) {
            is DeviceStateLoading -> false
            is DeviceStateUpdate -> it.deviceType?.isEon == true
        }
    }

    val introductionAvailable: LiveData<Boolean> = deviceStateEvent.map {
        when (it) {
            is DeviceStateLoading -> false
            is DeviceStateUpdate -> if (it.deviceType != null) WatchHelper.hasOnboarding(it.deviceType) else false
        }
    }

    val userGuideAvailable: LiveData<Boolean> = deviceStateEvent.map {
        when (it) {
            is DeviceStateLoading -> false
            is DeviceStateUpdate -> if (it.deviceType != null) WatchHelper.hasUserGuide(it.deviceType) else false
        }
    }

    val hasFirmwareUpdateAvailable: LiveData<Boolean> = deviceStateEvent.map {
        when (it) {
            is DeviceStateLoading -> false
            is DeviceStateUpdate -> it.firmwareUpdateAvailable
        }
    }

    val shouldShowUpdateWatchImage: LiveData<Boolean> = deviceStateEvent.map {
        when (it) {
            is DeviceStateLoading -> false
            is DeviceStateUpdate -> {
                it.firmwareUpdateAvailable || isForcedUpdateOngoing(it)
            }
        }
    }

    val shouldShowIntroduction: LiveData<Boolean> = deviceStateEvent.map {
        if (it is DeviceStateUpdate) {
            val suuntoDeviceType = it.deviceType
            suuntoDeviceType != null && suuntoDeviceType.isSuuntoRace && isForcedUpdateOngoing(it)
        } else {
            false
        }
    }

    private val _batteryTextFlow: MutableStateFlow<CharSequence> =
        MutableStateFlow(context.getString(R.string.watch_ui_connected))
    val batteryTextFlow: StateFlow<CharSequence> = _batteryTextFlow.asStateFlow()

    val sportModeDescriptionFlow: StateFlow<String> = suuntoWatchModel.isConnectedFlow.map {
        if (suuntoDeviceType?.isSuuntoRun == true) {
            context.getString(R.string.sport_mode_create_new_mode_description)
        } else {
            context.getString(R.string.sport_modes_header_text)
        }
    }.catch {
        emit(context.getString(R.string.sport_modes_header_text))
    }.stateIn(
        viewModelScope,
        SharingStarted.WhileSubscribed(5_000),
        context.getString(R.string.sport_modes_header_text),
    )

    val isSyncingOrBusy: LiveData<Boolean> = deviceStateEvent.map {
        when (it) {
            is DeviceStateLoading -> false
            is DeviceStateUpdate -> it.syncing || it.busy
        }
    }

    private fun isForcedUpdateOngoing(deviceStateUpdate: DeviceStateUpdate): Boolean {
        val suuntoDeviceType = deviceStateUpdate.deviceType ?: return false

        return deviceStateUpdate.watchState?.deviceInfo?.let { deviceInfo ->
            val compatibility = SuuntoDeviceCapabilityInfoProvider[suuntoDeviceType]
            compatibility.requiresForcedUpdate(deviceInfo.capabilities) && deviceStateUpdate.watchState.uploadProgressState.uploadInProgress
        } ?: false
    }

    private val foundSuuntoDevices = mutableMapOf<String, ScannedSuuntoBtDevice>()
    var isSwitching = false
        private set
    val lastKnownDeviceRelay = BehaviorRelay.create<ConnectedDeviceState>()

    val uploadProgressState: StateFlow<UploadProgressState> = deviceStateEvent
        .asFlow()
        .map {
            when (it) {
                is DeviceStateLoading -> UploadProgressState.EMPTY
                is DeviceStateUpdate -> {
                    it.watchState?.uploadProgressState ?: UploadProgressState.EMPTY
                }
            }
        }
        .catch { emit(UploadProgressState.EMPTY) }
        .distinctUntilChanged()
        .stateIn(
            viewModelScope,
            SharingStarted.WhileSubscribed(stopTimeoutMillis = 5000L),
            UploadProgressState.EMPTY
        )

    private var checkOnboardingJob: Job? = null

    private val _isNeedNotificationAccessAlert = MutableLiveData(false)
    val isNeedNotificationAccessAlert: LiveData<Boolean>
        get() = _isNeedNotificationAccessAlert

    private val _openNotificationAccess = SingleLiveEvent<Any>()
    val openNotificationAccess: LiveData<Any>
        get() = _openNotificationAccess

    private var _pendingSwitchDeviceFlow = MutableStateFlow("")
    internal val pendingSwitchDeviceFlow = _pendingSwitchDeviceFlow.asStateFlow()

    private var batteryLevelJob: Job? = null

    init {
        // Initialize location state
        nearbyDevicesRequired.value =
            Build.VERSION.SDK_INT > Build.VERSION_CODES.R || "S" == Build.VERSION.CODENAME
        locationAllowedRelay.accept(locationPermissionUseCase.foregroundLocationPermissionGranted())
        nearbyDevicesAllowedRelay.accept(nearbyDevicesPermissionUseCase.nearbyDevicesPermissionGranted())
        // Combine permission handling to Flowable which updates the
        // individual LiveData instances when one of them changes.
        permissionsFlowable = Flowables.combineLatest(
            locationUseCase.locationEnabled(),
            bluetoothUseCase.bluetoothEnabled(),
            locationAllowedRelay.toFlowable(BackpressureStrategy.LATEST),
            nearbyDevicesAllowedRelay.toFlowable(BackpressureStrategy.LATEST)
        ) { locEnabled, btEnabled, locAllowed, nearbyDevicesAllowed ->
            PermissionsInfo(
                locEnabled,
                btEnabled,
                locAllowed,
                nearbyDevicesAllowed
            )
        }
            .observeOn(mainThread)
            .doOnNext {
                locationEnabled.value = it.locationEnabled
                bluetoothEnabled.value = it.bluetoothEnabled
                locationAllowed.value = it.locationAllowed
                nearbyDevicesAllowed.value = it.nearbyDevicesAllowed
            }
            .map {
                DevicePermissions(
                    it.locationEnabled,
                    it.bluetoothEnabled,
                    it.locationAllowed,
                    it.nearbyDevicesAllowed
                )
            }

        /*
         * Combine permission Flowable and connectedDeviceRelay to create the current state.
         * Propagate the state change to UI by updating the LiveData instance.
         */
        disposables.add(
            Flowables.combineLatest(
                permissionsFlowable,
                connectedDeviceRelay.toFlowable(BackpressureStrategy.LATEST)
            ) { permissions, deviceState -> permissions to deviceState }
                .subscribe(
                    { (permissions, deviceState) ->
                        // Create state based on permissions and connected device
                        val deviceConnected = deviceState as? DeviceConnected
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                            val granted = featureStates.isBackgroundLocationGranted()
                            setSuunto3ConnectionAlertAvailability(deviceConnected?.device, granted)
                        } else if (deviceConnected?.device?.isSuunto3Family == true &&
                            !permissions.locationAllowed
                        ) {
                            setSuunto3ConnectionAlertAvailability(deviceConnected.device, false)
                        }
                        val state = DeviceStateUpdate(
                            permissions = permissions,
                            deviceType = deviceConnected?.device,
                            watchState = deviceConnected?.state,
                            syncResult = deviceConnected?.syncResult,
                            sku = deviceConnected?.sku ?: "",
                            otherDevices = DeviceSwitchContainer(
                                deviceConnected?.device,
                                getDeviceSwitchItems()
                            ),
                            switching = isSwitching
                        )

                        // Update state to subject and LiveData
                        deviceStateRelay.accept(state)
                        _deviceStateEvent.value = state
                    },
                    { e ->
                        logWarning(
                            e,
                            "Error from permission flowable or connected device relay"
                        )
                    })
        )
        checkDeviceAvailability()
        fetchNotificationsEnabled()
        checkSetupPreferenceAndSync()
        checkWeRunSupported()
        checkSetupAppInfo()
        setGuidesOrStore(false)
        checkBatteryLevel()
    }

    private fun fetchBatteryLevelInternal() {
        batteryLevelJob?.cancel()
        batteryLevelJob = viewModelScope.launch {
            suuntoWatchModel.getBatteryLevel()
                .map { batteryLevel ->
                    Timber.d("Battery level: $batteryLevel")
                    buildSpannedString {
                        inSpans(CenteredImageSpan(context, R.drawable.device_battery_level)) {
                            append("_") // placeholder
                        }
                        append("${batteryLevel.percentage}%")
                    }
                }
                .catch {
                    logWarning(it, "Fetch battery level error.")
                }
                .collect {
                    _batteryTextFlow.value = it
                }
        }
    }

    private fun checkBatteryLevel() {
        viewModelScope.launch {
            runSuspendCatching {
                _deviceStateEvent.asFlow()
                    .map { (it as? DeviceStateUpdate)?.watchState?.connectedWatchConnectionState }
                    .distinctUntilChanged()
                    .collect {
                    if (it == ConnectedWatchConnectionState.CONNECTED) {
                        fetchBatteryLevelInternal()
                    } else {
                        // The battery level will only be displayed on the DeviceConnectedFragment.
                        _batteryTextFlow.value = context.getString(R.string.watch_ui_connected)
                    }
                }
            }.onFailure {
                logWarning(it, "Check battery level error.")
                _batteryTextFlow.value = context.getString(R.string.watch_ui_connected)
            }
        }
    }

    private fun fetchNotificationsEnabled() {
        viewModelScope.launch(io) {
            runSuspendCatching {
                fetchNotificationsEnabledUseCase.run()
            }.onFailure {
                Timber.i(it, "fetch device notification state error.")
            }
        }
    }

    private fun supportedWeRun(variantName: String, sn: String) {
        launch {
            runSuspendCatching {
                val supportedWeRun = weChatConnectionStateHelper.supportedWeRun(variantName, sn)
                syncWechatRunSupported.set(supportedWeRun)
            }.onFailure { logWarning(it, "WeRun support check failed") }
        }
    }

    fun weChatConnectionState() {
        launch {
            runSuspendCatching {
                val weChatConnectionState = weChatConnectionStateHelper.weChatConnectionState()
                syncWechatRunConnected.set(weChatConnectionState)
            }.onFailure { logWarning(it, "WeChat connection state call failed.") }
        }
    }

    public override fun onCleared() {
        notificationStateDisposable?.let { if (!it.isDisposed) it.dispose() }
        super.onCleared()
        initSportModeComponentUseCase.close(TAG)
    }

    private fun setSuunto3ConnectionAlertAvailability(
        deviceType: SuuntoDeviceType?,
        granted: Boolean
    ) {
        if (deviceType?.isSuunto3Family == true) {
            connectionAlertAvailable.value = !granted
        } else {
            connectionAlertAvailable.value = false
        }
    }

    fun checkBackgroundLocationPermissionStatus(startRequestIfNeeded: Boolean) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            if (featureStates.isBackgroundLocationGranted()) {
                connectionAlertAvailable.value = false
            }
            disposables.add(
                connectedDeviceRelay
                    .firstOrError()
                    .subscribeBy(
                        onSuccess = { deviceState ->
                            val deviceConnected = deviceState as? DeviceConnected
                            val granted = featureStates.isBackgroundLocationGranted()
                            setSuunto3ConnectionAlertAvailability(deviceConnected?.device, granted)

                            // Set background location user property.
                            amplitudeAnalyticsTracker.trackUserProperty(
                                AnalyticsUserProperty.ANDROID_BACKGROUND_LOCATION_ENABLED,
                                if (granted) YES else NO
                            )
                            if (deviceConnected?.device?.isSuunto3Family == true &&
                                !isBackgroundLocationPermissionAsked(sharedPreferences) &&
                                !granted &&
                                startRequestIfNeeded
                            ) {
                                setBackgroundLocationPermissionAsked()
                                _actionLiveData.value = RequestBackgroundLocationPermission
                            }
                        },
                        onError = {
                            logWarning(it, "checkNeedForBackgroundLocationRequest failed")
                        }
                    )
            )
        } else {
            connectionAlertAvailable.value = false
        }
    }

    fun checkLocationPermissionState() {
        locationAllowedRelay.accept(locationPermissionUseCase.foregroundLocationPermissionGranted())
    }

    fun checkNearbyDevicesPermissionState() {
        val nearbyDevicesGranted = nearbyDevicesPermissionUseCase.nearbyDevicesPermissionGranted()
        featureStates.recheckBluetoothEnabled(nearbyDevicesGranted)
        nearbyDevicesAllowedRelay.accept(nearbyDevicesGranted)
    }

    fun updateWatchNotificationsSummary(context: Context) {
        notificationStateDisposable?.let { if (!it.isDisposed) it.dispose() }
        notificationStateDisposable = stateChangeObservable.map {
            it.notificationState.enabled
        }.subscribe({ watchNotificationEnabled ->
            when {
                !notificationSettingsHelper.notificationsEnabled(context) || !watchNotificationEnabled -> {
                    watchNotificationSummary.set(context.getString(R.string.notifications_action_description_allow))
                    notificationsOffIconEnabled.set(true)
                }

                getMissingNotificationsPermissions(
                    notificationSettingsHelper,
                    context,
                    emarsysAnalytics,
                    amplitudeAnalyticsTracker
                ).isNotEmpty() -> {
                    watchNotificationSummary.set(context.getString(R.string.notifications_action_description_allow_phone_calls))
                    notificationsOffIconEnabled.set(false)
                }

                else -> {
                    watchNotificationSummary.set(context.getString(R.string.notifications_action_description_notifications_ok))
                    notificationsOffIconEnabled.set(false)
                }
            }
        }, {
            Timber.v("observing state change failed.")
        })
    }

    fun updateAutoLocationSetting() {
        launch {
            val isAutoLocationEnabled = withContext(io) { isAutoLocationEnabledUseCase() }
            autoLocationSettingEnabled.set(isAutoLocationEnabled)
        }
    }

    fun disposeDevice() {
        deviceDisposable?.let {
            if (!it.isDisposed) {
                disposables.remove(it)
            }
        }
    }

    /**
     * Check connectivity library for the currently connected device. The
     * result of this check is emitted through the [deviceStateEvent]
     * LiveData instance.
     */
    fun checkDeviceAvailability() {
        if (deviceDisposable == null || deviceDisposable?.isDisposed == true) {
            deviceDisposable = deviceConnectionStateUseCase
                .connectedWatchState()
                .flatMapSingle { connectedWatchState ->
                    // Zip current model, sync result and connected device state
                    // together
                    //
                    // With this structure every watch state change is accompanied
                    // with device model, the last sync result and device sku version. This
                    // ConnectedDeviceInfo is then used to update the UI

                    val variantName = connectedWatchState.deviceInfo?.variantName
                    val deviceType = SuuntoDeviceType.fromVariantName(variantName)
                    suuntoDeviceType = deviceType
                    val deviceTypeSinge =
                        variantName?.let { Single.just(deviceType) }
                            ?: currentWatchModel.map { it.suuntoBtDevice.deviceType }
                    val skuSingle = connectedWatchState.deviceInfo?.let { Single.just(it.sku) }
                        ?: deviceInfoApi.sku()
                    Singles.zip(
                        deviceTypeSinge,
                        Single.just(connectedWatchState),
                        lastSyncResult,
                        skuSingle,
                        ::ConnectedDeviceInfo
                    )
                        .onErrorResumeNext {
                            // Timeout errors ignored and watch state subscription left alive.
                            // Todo: Should all errors but MissingCurrentWatchException be ignored?
                            if (it is TimeoutException) {
                                Timber.w(it, "Ignoring exception on getting ConnectedDeviceInfo")
                                Single.never()
                            } else {
                                Single.error(it)
                            }
                        }
                }
                .subscribeOn(ioThread)
                .observeOn(mainThread)
                .subscribe(
                    {
                        // Signal the UI when connection instability is detected
                        if (it.watchState.isConnectionInstabilityDetected()) {
                            _actionLiveData.value = ConnectionInstability
                        }

                        // Supports watch notifications?
                        it.watchState.deviceInfo?.let { deviceInfo ->
                            val fwVersion = it.watchState.deviceInfo?.fwVersion
                            val capabilities =
                                SuuntoDeviceCapabilityInfoProvider[deviceInfo.model]
                            watchNotificationsSupported.set(
                                capabilities.supportsNotifications(
                                    fwVersion
                                )
                            )
                        }

                        isDataLayerDevice.set(it.suuntoDeviceType.isDataLayerDevice)

                        val isConnected =
                            it.watchState.connectedWatchConnectionState == ConnectedWatchConnectionState
                                .CONNECTED

                        // Device connected
                        connectedDeviceRelay.accept(
                            DeviceConnected(
                                it.suuntoDeviceType,
                                it.watchState,
                                it.syncResult,
                                it.sku
                            )
                        )

                        updateLastSyncText(it.syncResult)
                        // Limit checks to normal mode to reduce calls to the backend whenever
                        // the sync state and result changes
                        if (it.watchState.isNormalState() && isConnected) {
                            checkSportModesSupport(it.suuntoDeviceType)
                        } else {
                            sportModeEnabled.set(false)
                        }
                        checkSuuntoPlusGuideAndFeatureSelectionSupport()
                        checkDiveModesSupport(isConnected)
                        checkDeviceSwitchSupport()
                        // Update device info when it is available
                        it.watchState.deviceInfo?.let { deviceInfo ->
                            onDeviceInfoAvailable(deviceInfo)
                            val capabilities =
                                SuuntoDeviceCapabilityInfoProvider[it.suuntoDeviceType]
                            checkOfflineMapsSupport(deviceInfo, capabilities)
                            checkWidgetCustomization(deviceInfo, capabilities)
                            checkForcedUpdateSupport(deviceInfo, capabilities)
                            checkOfflineMusicSupported(deviceInfo, capabilities)
                        }
                    },
                    { e ->
                        logWarning(e, "Error when checking device availability")
                        connectedDeviceRelay.accept(NoDeviceConnected)
                    }
                ).apply { disposables.add(this) }
        }
    }

    private fun checkOfflineMusicSupported(
        deviceInfo: DeviceInfo,
        capabilities: ISuuntoDeviceCapabilityInfo
    ) {
        offlineMusicSupported.set(capabilities.supportsOfflineMusic(deviceInfo.capabilities))
    }

    private fun checkSetupPreferenceAndSync() {
        launch {
            _deviceStateEvent.asFlow()
                .mapNotNull {
                    (it as? DeviceStateUpdate)?.watchState?.connectedWatchConnectionState
                }
                .distinctUntilChanged()
                .catch { e ->
                    logWarning(e, "checkSetupPreferenceAndSync error.")
                }
                .collect { state ->
                    if (state == ConnectedWatchConnectionState.CONNECTED) {
                        if (suuntoDeviceType?.isSuuntoRun == true) {
                            startSetupPreferenceIfNeeded()
                        } else {
                            Timber.d("not support setup preference")
                            onSyncNow()
                        }
                    }
                }
        }
    }

    private fun checkWeRunSupported() {
        launch {
            _deviceStateEvent.asFlow()
                .mapNotNull { (it as? DeviceStateUpdate)?.watchState }
                .distinctUntilChangedBy { it.connectedWatchConnectionState }
                .collect {
                    it.deviceInfo?.let { deviceInfo ->
                        supportedWeRun(deviceInfo.variantName, deviceInfo.serial)
                    }
                }
        }
    }

    private fun checkSetupAppInfo() {
        launch {
            _deviceStateEvent.asFlow()
                .mapNotNull {
                    (it as? DeviceStateUpdate)?.watchState?.connectedWatchConnectionState
                }
                .distinctUntilChanged()
                .catch { e ->
                    logWarning(e, "checkSetupAppInfo error.")
                }
                .collect { state ->
                    if (state == ConnectedWatchConnectionState.CONNECTED) {
                        setupAppInfo()
                    }
                }
        }
    }

    private fun startSetupPreferenceIfNeeded() {
        launch(io + CoroutineExceptionHandler { _, throwable ->
            logWarning(throwable, "setup preference error.")
            checkOnboarding()
            onSyncNow()
        }) {
            val setupPreference =
                setupPreferenceUseCase.startSetupIfNeeded(
                    setupPreferenceResultLauncher
                ) {
                    setupPreferenceContinuation = it
                }
            setupPreferenceContinuation = null
            setupPreferenceUseCase.finishSetup(setupPreference)
            checkOnboarding()
            onSyncNow()
        }
    }

    private fun updateLastSyncText(syncResult: SpartanSyncResult) {
        val lastSync = textFormatter.displaySyncResult(syncResult)

        lastSyncText.set(lastSync)
    }

    private fun onDeviceInfoAvailable(deviceInfo: DeviceInfo) {
        sharedPreferences.edit {
            putString(
                STTConstants.SuuntoPreferences.KEY_SUUNTO_PAIRED_WATCH_FW_VERSION,
                deviceInfo.fwVersion
            )
            putString(
                STTConstants.SuuntoPreferences.KEY_SUUNTO_PAIRED_WATCH_MODEL,
                deviceInfo.model
            )
            putString(
                STTConstants.SuuntoPreferences.KEY_SUUNTO_PAIRED_WATCH_HW_VERSION,
                deviceInfo.hwVersion
            )
            putString(
                STTConstants.SuuntoPreferences.KEY_SUUNTO_PAIRED_WATCH_SERIAL_NUMBER,
                deviceInfo.serial
            )
            putString(
                STTConstants.SuuntoPreferences.KEY_SUUNTO_PAIRED_WATCH_MANUFACTURER,
                deviceInfo.manufacturer
            )
            putString(
                STTConstants.SuuntoPreferences.KEY_SUUNTO_PAIRED_WATCH_SKU,
                deviceInfo.sku
            )
        }

        gearEventSender.sendDevicePairedEventIfNotSent(deviceInfo)
    }

    private fun setupAppInfo() = viewModelScope.launch(Dispatchers.Default) {
        runSuspendCatching {
            val supportsSendingAppInfo = suuntoWatchModel.supportsSendingAppInfo(true)
            if (!supportsSendingAppInfo) {
                return@runSuspendCatching
            }
            val appChannelName = context.resources.getString(R.string.app_channel_name)
            val appChannel = when (appChannelName) {
                APP_CHANNEL_SUUNTO -> AppChannel.SUUNTO
                APP_CHANNEL_SUNNTO_ZH -> AppChannel.SUUNTO_ZH
                else -> AppChannel.OTHER
            }
            val appInfo = AppInfo(channel = appChannel)
            suuntoWatchModel.setAppInfo(appInfo)
                .catch { logWarning(it, "Error while setup app info") }
                .collect()
        }.onFailure { e ->
            logWarning(e, "Failed to setup app info")
        }
    }

    //region User actions
    fun onHelpClick() {
        _actionLiveData.value = ShowHelp
    }

    fun onTransferFirmwareClick() {
        _actionLiveData.value = TransferFirmware
    }

    fun onImportWorkoutClick() {
        _actionLiveData.value = ImportWorkout
    }

    fun onGetEonSettingsClicked() {
        _actionLiveData.value = GetEonSettings
    }

    fun onSetEonSettingsClicked() {
        _actionLiveData.value = SetEonSettings
    }

    fun onWifiNetworksClicked() {
        _actionLiveData.value = ShowWifiNetworks
    }

    fun showMovescountUninstallDialog() {
        _actionLiveData.value = ShowMovescountUninstallDialog
    }

    /**
     * Show help article with given id
     */
    fun onHelpArticleClick(
        articleId: String,
        source: String
    ) {
        _actionLiveData.value = ShowHelpArticle(articleId)
        analyticsUtil.onHelpShiftLinkClicked(articleId, source)
    }

    fun onSuunto7HelpArticleClick(
        articleId: String,
        source: String,
        suunto7Source: String,
        pairingState: Boolean
    ) {
        onHelpArticleClick(articleId, source)
        analyticsUtil.onSuunto7HelpShiftArticleOpened(pairingState, suunto7Source)
    }

    fun onNotificationsClick() {
        analyticsUtil.trackEvent(AnalyticsEvent.SUUNTO_NOTIFICATIONS_ON_WATCH_SCREEN)
        emarsysAnalytics.trackEvent(AnalyticsEvent.SUUNTO_NOTIFICATIONS_ON_WATCH_SCREEN)
        _actionLiveData.value = ShowNotificationsSettings
    }

    fun onAutoLocationClick() {
        launch {
            val value = withContext(io) {
                saveAutoLocationEnabledUseCase(!autoLocationSettingEnabled.get())
                isAutoLocationEnabledUseCase()
            }
            trackAutomaticLocationPermissionToggled(value)
            autoLocationSettingEnabled.set(value)
        }
    }

    fun onSyncWechatRunClick() {
        _actionLiveData.value = ConnectWeRun(syncWechatRunConnected.get())
    }

    fun onKeepConnectedSettingsClick() {
        _actionLiveData.value = ShowKeepConnectedSettings
    }

    fun onFirmwareIntroductionLinkClick() {
        _actionLiveData.value = GetToKnowNewWatch
    }

    private fun trackAutomaticLocationPermissionToggled(value: Boolean) {
        val properties = AnalyticsProperties()
            .putOnOff(AnalyticsEventProperty.NEW_VALUE, value)
            .put(
                AnalyticsEventProperty.CONTEXT,
                AnalyticsPropertyValue.AutomaticLocationContext.WATCH_SCREEN
            )
        amplitudeAnalyticsTracker.trackEvent(
            AnalyticsEvent.LOCATION_AUTOMATIC_LOCATION_PERMISSION_TOGGLED,
            properties
        )
        amplitudeAnalyticsTracker.trackUserProperty(
            AnalyticsUserProperty.PERMISSION_AUTOMATIC_LOCATION,
            if (value) YES else NO
        )
    }

    fun onIntroductionClick() {
        val event = deviceStateEvent.value
        if (event is DeviceStateUpdate && event.deviceType != null) {
            _actionLiveData.value = ShowIntroduction(event.deviceType)
        } else {
            Timber.w("Cannot start introduction without device type")
        }
    }

    fun onUserGuideClick() {
        val event = deviceStateEvent.value
        if (event is DeviceStateUpdate && event.deviceType != null) {
            _actionLiveData.value = ShowUserGuide(event.deviceType)
        } else {
            Timber.w("Cannot start user guide without device type")
        }
    }

    fun onSyncNow() {
        disposables.add(
            syncCurrentWatch
                .subscribeOn(ioThread)
                .subscribe({}, { t ->
                    if (t is MissingCurrentWatchException) {
                        Timber.d("Unable to sync, device not connected")
                    } else {
                        Timber.w(t, "Unable to sync, other error")
                    }
                })
        )
    }

    fun onUpdateAvailableClick() {
        sendAnalyticsEventFromWatchState(
            event = AnalyticsEvent.SUUNTO_SYNC_WATCH_UPDATE,
            toAmplitude = true,
            toBraze = false,
            includeMovescountInstalledStatus = false
        )

        val deviceStatus = connectedDeviceRelay.value ?: NoDeviceConnected
        val supportsOta = if (deviceStatus is DeviceConnected) {
            val fwVersion = deviceStatus.state.deviceInfo?.fwVersion
            fwVersion != null && deviceStatus.device.supportsOtaUpdate(fwVersion)
        } else {
            false
        }

        _actionLiveData.value =
            UpdateFirmware(
                supportsOta = supportsOta,
                suuntoDeviceType = suuntoDeviceType
            )
    }

    fun onConnectionClick() {
        _actionLiveData.value = ShowPowerManagementSettings
    }

    //endregion

    //region Menu actions
    fun showAboutDevice() {
        disposables.add(
            Singles.zip(
                rxSingle { deviceAboutInfoUseCase.fetchAboutInfo() },
                currentWatchModel.map { it.suuntoBtDevice.deviceType }
                    .onErrorReturn { SuuntoDeviceType.Unrecognized }
            )
                .observeOn(mainThread)
                .subscribe({ (info, suuntoDeviceType) ->
                    _actionLiveData.value =
                        ShowAboutDevice(
                            serial = info.serial,
                            version = info.version,
                            deviceInfoWear = info.deviceInfoWear,
                            otaUpdateSupported = info.otaUpdateSupported,
                            suuntoDeviceType = suuntoDeviceType,
                            updateAvailable = (hasFirmwareUpdateAvailable.value ?: false)
                                || uploadProgressState.value.uploadInProgress,
                        )
                }, { t ->
                    logWarning(t, "Failed to to get serial and version for about device")
                    _actionLiveData.value = ShowAboutDevice(
                        serial = "",
                        version = "",
                        deviceInfoWear = DeviceInfoWear(),
                        otaUpdateSupported = false,
                        suuntoDeviceType = SuuntoDeviceType.Unrecognized,
                        updateAvailable = false,
                    )
                })
        )
    }

    internal fun sendLogs(filesDir: File, toFile: Boolean) {
        disposables.add(
            sendLogHelper.sendLogs(filesDir, toFile) {
                _actionLiveData.postValue(it)
            }
        )
    }
    //endregion

    //region Sport modes
    private fun checkSportModesSupport(suuntoDeviceType: SuuntoDeviceType) {
        if (suuntoDeviceType.isRunDevice) {
            sportModeSupported.set(true)
            disposables.add(
                suuntoWatchModel.isRunSportModeSupported(false)
                    .subscribeOn(ioThread)
                    .observeOn(mainThread)
                    .subscribe({ support ->
                        sportModeEnabled.set(support)
                    }, { t ->
                        logWarning(t, "Cannot fetch Run sport mode support")
                    })
            )
        } else {
            disposables.add(
                rxCompletable {
                    downloadSportModeComponentUseCase.downloadSportModeComponentIfNeeded()
                    initSportModeComponentUseCase.init(TAG)
                }
                    .andThen(fetchSportModesUseCase.fetchDeviceSupported())
                    .subscribeOn(ioThread)
                    .observeOn(mainThread)
                    .subscribe({ supportMode ->
                        sportModeSupportMode = supportMode
                        sportModeSupported.set(
                            supportMode === SupportMode.SUPPORTED ||
                                supportMode === SupportMode.SUPPORTED_WITHOUT_NAMECHANGE
                        )
                        sportModeEnabled.set(sportModeSupported.get())
                    }, { t ->
                        logWarning(t, "Cannot fetch sport mode support mode")
                        sportModeSupported.set(false)
                        sportModeEnabled.set(false)
                    })
            )
        }
    }

    fun showSportModesCustomization() {
        disposables.add(
            Singles.zip(
                fetchSportModesUseCase.fetchSportModeFteCompleted(),
                currentWatchModel
                    .map { spartan -> spartan.suuntoBtDevice.deviceType },
            ) { fteCompleted, deviceType ->
                CustomizeSportModes(
                    fteCompleted,
                    deviceType,
                    sportModeSupportMode,
                )
            }
                .subscribeOn(ioThread)
                .observeOn(mainThread)
                .subscribe({ customizeSportModes ->
                    _actionLiveData.value = customizeSportModes
                }, { t ->
                    logWarning(t, "Cannot fetch sport mode support mode")
                })
        )
    }
    //endregion

    //region SuuntoPlus guides
    private fun checkSuuntoPlusGuideAndFeatureSelectionSupport() {
        launch {
            runSuspendCatching {
                val guidesSupported = suuntoPlusNavigator.isSuuntoPlusGuideSyncSupported(suuntoDeviceType)
                    .firstOrNull() ?: false
                val featuresSupported =
                    suuntoPlusNavigator.isSuuntoPlusFeatureSelectionSupported()
                        .firstOrNull() ?: false
                val storeSupported =
                    suuntoPlusNavigator.isSuuntoPlusStoreSupported(suuntoDeviceType)
                val watchfaceSupported = suuntoPlusNavigator.isSuuntoPlusWatchfaceSupported()
                    .firstOrNull() ?: false
                suuntoPlusStoreSupported.set(storeSupported)
                // BES device only supports guide made using planner
                suuntoPlusGuidesSupported.set(guidesSupported)
                suuntoPlusFeatureSelectionEnabled.set(featuresSupported)
                suuntoPlusWatchfaceEnabled.set(watchfaceSupported)
                setGuidesOrStore(watchfaceSupported)
                val structuredWorkoutSupported = suuntoPlusNavigator.isStructuredWorkoutPlannerSupported(suuntoDeviceType)
                    .firstOrNull() ?: false
                structuredWorkoutPlannerSupported.set(structuredWorkoutSupported)
            }.onFailure { e ->
                logWarning(e, "Failed to check if SuuntoPlus plug-in sync is supported")
                suuntoPlusGuidesSupported.set(false)
                suuntoPlusFeatureSelectionEnabled.set(false)
                suuntoPlusWatchfaceEnabled.set(false)
                structuredWorkoutPlannerSupported.set(false)
            }
        }
    }

    private fun setGuidesOrStore(watchfaceSupported: Boolean) {
        if (isRunDevice) {
            suuntoPlusStoreText.set(context.getString(R.string.suunto_plus_store_link_text_dilu))
        } else {
            suuntoPlusStoreText.set(
                context.getString(
                    if (watchfaceSupported) R.string.suunto_plus_store_link_text
                    else R.string.suunto_plus_store_link_text_with_watch_faces
                )
            )
        }

        suuntoPlusStoreTitle.set(
            context.getString(
                if (isRunDevice) {
                    R.string.suunto_plus_store_link_title_dilu
                } else {
                    R.string.suunto_plus_store_link_title
                }
            )
        )
        backgroundImageRes.set(
            if (isRunDevice) {
                R.drawable.suunto_plus_store_link_ad_dilu
            } else {
                R.drawable.suunto_plus_store_link_ad
            }
        )
    }

    fun showStructuredWorkoutPlanner() {
        _actionLiveData.value = ShowStructuredWorkoutPlanner
    }

    fun showSuuntoPlusFeatures() {
        _actionLiveData.value = ShowSuuntoPlusFeatures
    }

    fun showSuuntoPlusWatchface() {
        _actionLiveData.value = ShowSuuntoPlusWatchface
    }

    fun showSuuntoPlusGuides() {
        _actionLiveData.value = ShowSuuntoPlusGuides
    }

    fun showSuuntoPlusStore() {
        _actionLiveData.value = ShowSuuntoPlusStore(isRunDevice)
    }
    //endregion

    //region widget customization
    private fun checkWidgetCustomization(
        deviceInfo: DeviceInfo,
        capabilities: ISuuntoDeviceCapabilityInfo
    ) {
        val supportsWidgets = capabilities.supportsWidgets(deviceInfo.capabilities)

        val widgetSupportOverride = featureTogglePreferences.getBoolean(
            STTConstants.FeatureTogglePreferences.KEY_ENABLE_WATCH_WIDGETS_WITHOUT_CAPABILITY,
            STTConstants.FeatureTogglePreferences.KEY_ENABLE_WATCH_WIDGETS_WITHOUT_CAPABILITY_DEFAULT
        )

        val hasOpenedCustomization = sharedPreferences.getBoolean(
            STTConstants.SuuntoPreferences.KEY_HAS_OPENED_WATCH_WIDGET_CUSTOMIZATION,
            false
        )

        widgetCustomizationSupported.set(supportsWidgets || widgetSupportOverride)
        showWidgetCustomizationBadge.set(!hasOpenedCustomization)
    }

    fun showWidgetCustomization() {
        _actionLiveData.value = ShowWidgetCustomization

        // Remove the badge with short delay to let the customization screen show up first
        launch {
            delay(300L)
            showWidgetCustomizationBadge.set(false)
        }
    }
    //endregion

    //region offline maps
    private fun checkOfflineMapsSupport(
        deviceInfo: DeviceInfo,
        capabilities: ISuuntoDeviceCapabilityInfo
    ) {
        val supportsOfflineMaps = capabilities.supportsOfflineMaps(deviceInfo.capabilities)

        val hasOpenedOfflineMaps = sharedPreferences.getBoolean(
            STTConstants.SuuntoPreferences.KEY_HAS_OPENED_OFFLINE_MAPS,
            false
        )

        offlineMapsSupported.set(supportsOfflineMaps)
        showOfflineMapsBadge.set(!hasOpenedOfflineMaps)
    }

    fun showOfflineMaps() {
        _actionLiveData.value = ShowOfflineMaps

        // Remove the badge with short delay to let the customization screen show up first
        launch {
            delay(300L)
            showOfflineMapsBadge.set(false)
        }
    }
    //endregion

    //region forced update
    private fun checkForcedUpdateSupport(
        deviceInfo: DeviceInfo,
        capabilities: ISuuntoDeviceCapabilityInfo
    ) {
        val requiresForcedUpdate = capabilities.requiresForcedUpdate(deviceInfo.capabilities)
        forcedUpdateRequired.set(requiresForcedUpdate)
    }
    //endregion

    //region Dive modes
    private fun checkDiveModesSupport(isConnected: Boolean) {
        val deviceStatus = connectedDeviceRelay.value ?: NoDeviceConnected
        if (deviceStatus is DeviceConnected) {
            // First check if dive mode customization is supported
            diveModeSupported.set(deviceStatus.device.isEon && isConnected)
            // Now check if it should be enabled -- it should only be enabled for eon 3.0 and above devices and not syncing
            deviceStatus.state.deviceInfo?.fwVersion?.runCatching {
                diveModeEnabled.set(Version(this) >= Version("3.0") && deviceStatus.state.isNormalState())
            }?.onFailure {
                Timber.w(it)
                diveModeSupported.set(false)
                diveModeEnabled.set(false)
            }
        } else {
            diveModeSupported.set(false)
            diveModeEnabled.set(false)
        }
    }

    fun showDiveModesCustomization() {
        val deviceStatus = connectedDeviceRelay.value ?: NoDeviceConnected
        if (deviceStatus is DeviceConnected) {
            deviceStatus.state.deviceInfo?.let {
                _actionLiveData.value = CustomizeDiveModes(
                    deviceSerial = it.serial,
                    deviceHwVersion = it.hwVersion,
                    deviceFwVersion = it.fwVersion,
                    deviceVariantName = SuuntoDeviceType.getVariantName(deviceStatus.device)
                )
            }
        }
    }
    //endregion

    //region Device Switch
    /**
     * Check for previously paired devices that are not currently connected to Suunto App
     */
    private fun checkDeviceSwitchSupport() {
        val pairedWatches = RxJavaInterop.toV2Observable(
            suuntoLeScanner.alreadyPairedDevices(omitLegacyDevices = false)
        ).mergeWith(suuntoDlScanner.scanDataLayerDevices().toObservable())

        disposables.add(
            Observable.combineLatest(
                pairedWatches,
                currentWatchModel.toObservable()
            ) { device, currentWatch ->
                device to currentWatch
            }
                .takeWhile { !isSwitching }
                .observeOn(mainThread)
                .subscribeOn(ioThread)
                .subscribe({ (scannedDevice, currentWatch) ->
                    if (scannedDevice.isWearOsNode) {
                        // Found a device that is paired with Wear OS app.
                        // Remove any previous BLE devices with the same name.
                        foundSuuntoDevices.remove(scannedDevice.macAddress)
                    }

                    // Only add the scanned device if its previously been paired, or its a WearOS
                    // device.
                    if (scannedDevice.phonePairingState == PairingState.Paired || scannedDevice.isWearOsNode) {
                        // Only add the scanned device if it isn't the currently paired watch
                        if (currentWatch.suuntoBtDevice.macAddress != scannedDevice.macAddress) {
                            foundSuuntoDevices[scannedDevice.macAddress] = scannedDevice
                        }
                    }

                    // Remove any data layer scanned devices that are not paired with WearOS app
                    if (scannedDevice.suuntoBtDevice.deviceType.isDataLayerDevice && !scannedDevice.isWearOsNode) {
                        foundSuuntoDevices.remove(scannedDevice.macAddress)
                    }

                    // Always make sure current device isn't treated as one that can be switched to
                    foundSuuntoDevices.remove(currentWatch.suuntoBtDevice.macAddress)

                    val currentState = _deviceStateEvent.value as? DeviceStateUpdate
                    if (currentState != null) {
                        val newState = currentState.copy(
                            otherDevices = DeviceSwitchContainer(
                                currentState.deviceType,
                                getDeviceSwitchItems()
                            )
                        )

                        // Update state to subject and LiveData
                        deviceStateRelay.accept(newState)
                        _deviceStateEvent.value = newState
                    }
                    Timber.d("checkDeviceSwitchSupport succeeded")
                }) {
                    logWarning(it, "checkDeviceSwitchSupport failed")
                }
        )
    }

    /**
     * Switch to a the new [device], disconnecting the current watch.
     * State changes are handled in the [connectedDeviceRelay], which will trigger
     * updates to the UI when the new device is paired and connected.
     */
    private fun switchDevice(device: ScannedSuuntoBtDevice) {
        fun updateSwitchingState(switching: Boolean) {
            isSwitching = switching
            val currentState = _deviceStateEvent.value as? DeviceStateUpdate
            if (currentState != null) {
                val newState = currentState.copy(
                    otherDevices = DeviceSwitchContainer(
                        currentState.deviceType,
                        getDeviceSwitchItems()
                    )
                )

                // Update state to subject and LiveData
                deviceStateRelay.accept(newState)
                _deviceStateEvent.postValue(newState)
            }
        }

        foundSuuntoDevices.clear()
        disposables.add(
            currentWatchModel
                .doOnSubscribe {
                    foundSuuntoDevices.clear()
                    updateSwitchingState(true)
                    lastKnownDeviceRelay.accept(connectedDeviceRelay.value ?: NoDeviceConnected)
                }
                .flatMap { spartan ->
                    RxJavaInterop.toV2Single(suuntoWatchModel.disconnect(spartan))
                }
                .flatMap { disconnected ->
                    if (disconnected) {
                        RxJavaInterop.toV2Single(suuntoWatchModel.connect(device))
                    } else {
                        Single.error(Exception())
                    }
                }
                .observeOn(mainThread)
                .subscribeOn(ioThread)
                .doFinally {
                    updateSwitchingState(false)
                }
                .subscribe({
                    Timber.d("Switching device succeeded")
                    deviceDisposable?.dispose()
                }) {
                    logWarning(it, "Switching device failed")
                }
        )
    }

    fun onPairAnotherDeviceClick() {
        _actionLiveData.value = PairAnotherDevice
    }

    fun prepareToPairAnotherDevice() {
        foundSuuntoDevices.clear()
        disposables.add(
            currentWatchModel
                .flatMap { spartan ->
                    RxJavaInterop.toV2Single(suuntoWatchModel.disconnect(spartan))
                }
                .doOnSubscribe {
                    lastKnownDeviceRelay.accept(connectedDeviceRelay.value ?: NoDeviceConnected)
                }
                .observeOn(mainThread)
                .subscribeOn(ioThread)
                .subscribe({
                    Timber.d("Disconnect device succeeded")
                }) {
                    logWarning(it, "Disconnect device failed")
                }
        )
    }

    override fun onDeviceSwitchClick(device: ScannedSuuntoBtDevice) {
        switchDevice(device)
    }

    //endregion

    fun showOfflineMusic() {
        _actionLiveData.value = ShowOfflineMusic
    }

    /**
     * Clear connection instability reported by connectivity service.
     * Clearing the instability makes connectivity service attempt to
     * reconnect immediately.
     */
    fun clearConnectionInstability(onComplete: () -> Unit) {
        // TODO Return RxJava V2 Completable from SuuntoWatchModel when WatchActivity is removed
        val clearConnectionInstability =
            RxJavaInterop.toV2Completable(suuntoWatchModel.clearConnectionInstability())

        disposables.add(
            clearConnectionInstability
                .subscribe(
                    { onComplete() },
                    { t -> logWarning(t, "Failed to clear connection instability") }
                )
        )
    }

    /**
     * Send analytics event from [WatchState]
     *
     * @param event [AnalyticsEvent] to be sent
     * @param toAmplitude if event is sent to Amplitude
     * @param toBraze if event is sent to Braze properties
     */
    fun sendAnalyticsEventFromWatchState(
        event: String,
        toAmplitude: Boolean,
        toBraze: Boolean,
        includeMovescountInstalledStatus: Boolean
    ) {
        disposables.add(
            stateChangeObservable
                .firstElement()
                .observeOn(ioThread)
                .subscribe({ watchState ->

                    val properties = AnalyticsUtils.createDeviceProperties(watchState)
                    val mvInstalledPropKey =
                        AnalyticsUserProperty.SUUNTO_MOVESCOUNT_APP_INSTALLED_ON_SAME_PHONE

                    if (toAmplitude) {
                        if (includeMovescountInstalledStatus) {
                            val movescountInstalled =
                                movescountAppInfoUseCase.isMovescountAppInstalledOnPhone()
                            properties.putYesNo(mvInstalledPropKey, movescountInstalled)
                            amplitudeAnalyticsTracker.trackUserProperty(
                                mvInstalledPropKey,
                                if (movescountInstalled) YES else NO
                            )
                        }
                        amplitudeAnalyticsTracker.trackEvent(event, properties)
                    }

                    if (toBraze) {
                        if (includeMovescountInstalledStatus) {
                            val movescountInstalled =
                                movescountAppInfoUseCase.isMovescountAppInstalledOnPhone()
                            properties.putYesNo(mvInstalledPropKey, movescountInstalled)
                            emarsysAnalytics.trackStringUserProperty(
                                mvInstalledPropKey,
                                if (movescountInstalled) YES else NO
                            )
                        }
                        emarsysAnalytics.trackEventWithProperties(event, properties.map)
                    }
                }, { throwable ->
                    if (throwable is MissingCurrentWatchException) {
                        if (toAmplitude) {
                            amplitudeAnalyticsTracker.trackEvent(event)
                        }
                        if (toBraze) {
                            emarsysAnalytics.trackEvent(event)
                        }
                        Timber.i("Missing current watch")
                    } else {
                        Timber.w(throwable, "Unable to send analytics event")
                    }
                })
        )
    }

    /**
     * Schedule check for battery optimization and notification access. When ready the check will be initiated through
     * the batteryAndNotificationCheckEvent LiveData instance.
     */
    fun scheduleBatteryAndNotificationCheck() {
        cancelBatteryNotificationCheck()
        notificationAccessDisposable = Completable
            .timer(NOTIFICATION_ACCESS_DIALOG_DELAY_SECONDS, TimeUnit.SECONDS)
            .subscribeOn(ioThread)
            .observeOn(mainThread)
            .subscribe(
                { batteryAndNotificationCheckEvent.call() },
                { e -> Timber.w(e, "Error while scheduling battery and notification check") }
            )
        disposables.add(notificationAccessDisposable)
    }

    /**
     * Cancel scheduled battery notification check
     */
    fun cancelBatteryNotificationCheck() {
        if (::notificationAccessDisposable.isInitialized) {
            notificationAccessDisposable.dispose()
            disposables.remove(notificationAccessDisposable)
        }
    }

    /**
     * Signal activity that firmware update is required for device
     */
    fun requireFirmwareUpdate(deviceType: SuuntoDeviceType) {
        _actionLiveData.value = ShowFirmwareUpdateRequired(deviceType)
    }

    fun onUnpairCompleted(device: SuuntoBtDevice) {
        if (device.deviceType.isSuunto7) {
            _actionLiveData.value = ShowPostUnpairWarning(device.deviceType)
        }
    }

    fun isNeedForCompanionAssociationAsked(): Boolean {
        return sharedPreferences.getBoolean(
            COMPANION_DEVICE_ASSOCIATION_ASKED_IN_DEVICE_ACTIVITY,
            false
        )
    }

    fun setNeedForCompanionAssociationAsked() {
        sharedPreferences.edit {
            putBoolean(COMPANION_DEVICE_ASSOCIATION_ASKED_IN_DEVICE_ACTIVITY, true)
        }
    }

    fun setCompanionAssociation(associated: Boolean) {
        keepConnectedSettingConnected.set(associated)
    }

    fun clearNeedForCompanionAssociationAsked() {
        sharedPreferences.edit {
            putBoolean(COMPANION_DEVICE_ASSOCIATION_ASKED_IN_DEVICE_ACTIVITY, false)
        }
    }

    private fun setBackgroundLocationPermissionAsked() {
        sharedPreferences.edit {
            putBoolean(BACKGROUND_LOCATION_PERMISSION_REQUESTED_ONCE, true)
        }
    }

    @SuppressLint("NullSafeMutableLiveData")
    private fun checkOnboarding() {
        checkOnboardingJob?.cancel()
        checkOnboardingJob = launch(io) {
            runSuspendCatching {
                val notOnboardedDevice = shouldShowOnboardingUseCase.checkNotOnboardedDevice()
                if (notOnboardedDevice != null) {
                    firstPairingInfoUseCase.markOnboardingShownAtLeastOnce(notOnboardedDevice)
                        .await()
                    _openOnboardingEvent.postValue(notOnboardedDevice)
                }
            }.onFailure { e ->
                Timber.d(e, "Failed to check show onboarding: ${e.message}")
            }
        }
    }

    fun startFirmwareFileTransfer(fileUri: Uri) {
        disposables.add(
            suuntoWatchModel.startFirmwareTransfer(fileUri, null)
                .subscribeOn(ioThread)
                .observeOn(mainThread)
                .subscribe(
                    { response ->
                        if (response.success) {
                            _toastLiveData.value = "Firmware file transfer started"
                        } else {
                            _toastLiveData.value = "Firmware file error: " + response.message
                        }
                    },
                    { e ->
                        logWarning(e, "Firmware transfer start error.")
                        _toastLiveData.value = "Firmware transfer start error."
                    }
                )
        )
    }

    fun importWorkoutFromFile(workoutFileUri: Uri) {
        disposables.add(
            scLib.importWorkoutFromFile(workoutFileUri).toV2()
                .onErrorComplete()
                .subscribe()
        )
    }

    fun getEonSettings(context: Context) {
        val dir: File? = context.getExternalFilesDir("eon-settings")
        val eonSettingsFile = File(dir, "eon_settings.xml")
        val uri = Uri.fromFile(eonSettingsFile)
        disposables.add(
            suuntoWatchModel.getSettingsFile(uri)
                .subscribeOn(ioThread)
                .observeOn(mainThread)
                .doOnSubscribe {
                    _toastLiveData.postValue("Getting settings.. wait 20s or something.")
                }
                .subscribeBy(onError = { e ->
                    logWarning(e, "Unable to fetch settings file")
                    _toastLiveData.value = e.toString()
                }, onSuccess = {
                    if (it.successful) {
                        _actionLiveData.value = ShowEonSettingsShareDialog(eonSettingsFile)
                    } else {
                        Timber.w("Unable to get settings file: ${it.errorMessage}")
                        val errorText = it.errorMessage ?: "Unable to get settings file"
                        _toastLiveData.value = errorText
                    }
                })
        )
    }

    fun setEonSettings(eonSettingsFile: Uri) {
        disposables.add(
            suuntoWatchModel.setSettingsFile(eonSettingsFile)
                .subscribeOn(ioThread)
                .observeOn(mainThread)
                .doOnSubscribe {
                    _toastLiveData.postValue("Setting settings. Takes time. EON reboots on success.")
                }
                .subscribeBy(onError = { e ->
                    logWarning(e, "Unable to set settings file")
                    _toastLiveData.value = e.toString()
                }, onSuccess = {
                    if (it.successful) {
                        Timber.d("Setting file successfully set.")
                        _toastLiveData.value = "Setting file successfully set."
                    } else {
                        Timber.w("Unable to set settings file: ${it.errorMessage}")
                        val errorText = it.errorMessage ?: "Unable to set settings file"
                        _toastLiveData.value = errorText
                    }
                })
        )
    }

    fun isCurrentUserFieldTester(): Boolean {
        return currentUserController.isFieldTester
    }

    private fun getDeviceSwitchItems() = foundSuuntoDevices.map {
        DeviceSwitchItem(
            device = it.value,
            deviceSwitchClickListener = this
        )
    }

    private fun logWarning(throwable: Throwable, message: String) {
        if (throwable is MissingCurrentWatchException) {
            Timber.i("Missing current watch")
        } else {
            Timber.w(throwable, message)
        }
    }

    fun isPrivacyAlreadyPrompted() =
        sharedPreferences.getBoolean(PRIVACY_SETTING_IN_DEVICE_ACTIVITY, false)

    fun setNeedNotificationAccessAlert(enabled: Boolean) {
        _isNeedNotificationAccessAlert.postValue(enabled)
    }

    fun setNotificationAccessDialogAsked(asked: Boolean) = sharedPreferences.edit {
        putBoolean(NOTIFICATION_ACCESS_DIALOG_REQUESTED_ONCE, asked)
    }

    fun isNotificationAccessDialogAsked() =
        sharedPreferences.getBoolean(NOTIFICATION_ACCESS_DIALOG_REQUESTED_ONCE, false)

    fun checkNotificationAccessNeedAlert(context: Context): Boolean {
        val shouldPrompt = watchNotificationsSupported.get() &&
            !notificationSettingsHelper.notificationsEnabled(context)
        val dialogAsked = isNotificationAccessDialogAsked()
        val alertDismissed = isNotificationAccessAlertDismissed()
        return shouldPrompt && dialogAsked && !alertDismissed
    }

    fun openNotificationAccess() {
        _openNotificationAccess.call()
    }

    fun setNotificationAccessAlertDismissed(dismissed: Boolean) = sharedPreferences.edit {
        putBoolean(NOTIFICATION_ACCESS_ALERT_DISMISSED, dismissed)
    }

    fun isNotificationAccessAlertDismissed() =
        sharedPreferences.getBoolean(NOTIFICATION_ACCESS_ALERT_DISMISSED, false)

    fun dismissNotificationAccessAlert() {
        setNotificationAccessAlertDismissed(true)
        setNeedNotificationAccessAlert(false)
    }

    fun trySetNotificationsEnabled(context: Context) {
        if (!notificationSettingsHelper.notificationsEnabled(context) || !watchNotificationsSupported.get()) return
        viewModelScope.launch { setNotificationsEnabledUseCase.run(true) }
    }

    fun trySetNotificationsCategoryEnabled(context: Context) {
        if (!notificationSettingsHelper.notificationsEnabled(context) || !watchNotificationsSupported.get()) return
        viewModelScope.launch {
            setNotificationsCategoryEnabledUseCase.run(call = true, sms = true, application = true)
        }
    }

    fun trySwitchDevice(context: Context, macAddress: String?) {
        Timber.d("try switch device, empty? ${macAddress.isNullOrEmpty()}")
        viewModelScope.launch {
            val needSwitch = checkIfSwitchDeviceUseCase.run(macAddress) && checkIfSwitch(context)
            if (needSwitch) {
                prepareToPairAnotherDevice()
            }
            Timber.d("try switch device need switch: $needSwitch")
            _pendingSwitchDeviceFlow.emit(macAddress ?: "")
        }
    }

    fun clearPendingSwitchDevice() {
        _pendingSwitchDeviceFlow.tryEmit("")
    }

    private suspend fun checkIfSwitch(context: Context): Boolean {
        return suspendCoroutine { continuation ->
            AlertDialog.Builder(context)
                .setTitle(R.string.device_switch_check_dialog_title)
                .setMessage(R.string.device_switch_check_dialog_message)
                .setPositiveButton(
                    R.string.yes
                ) { _, _ ->
                    continuation.resume(true)
                }
                .setNegativeButton(
                    R.string.no
                ) { _, _ ->
                    continuation.resume(false)
                }
                .setOnCancelListener {
                    continuation.resume(false)
                }
                .create()
                .show()
        }
    }

    companion object {
        private const val TAG = "DeviceHolderViewModel"
        private val NOTIFICATION_ACCESS_DIALOG_DELAY_SECONDS = TimeUnit.SECONDS.toSeconds(5)
        private const val COMPANION_DEVICE_ASSOCIATION_ASKED_IN_DEVICE_ACTIVITY =
            "companion_device_association_asked_in_device_activity"
        private const val BACKGROUND_LOCATION_PERMISSION_REQUESTED_ONCE =
            "background_location_permission_requested_once"
        private const val BACKGROUND_LOCATION_PERMISSION_REQUESTED_ONCE_ON_CONNECTED_GPS =
            "background_location_permission_requested_once_on_connected_gps"
        private const val NOTIFICATION_ACCESS_DIALOG_REQUESTED_ONCE =
            "notification_access_dialog_requested_once"
        private const val NOTIFICATION_ACCESS_ALERT_DISMISSED =
            "notification_access_alert_dismissed"
        private const val APP_CHANNEL_SUUNTO = "Suunto"
        private const val APP_CHANNEL_SUNNTO_ZH = "Suunto_ZH"

        @JvmStatic
        fun isBackgroundLocationPermissionAsked(suuntoPreferences: SharedPreferences): Boolean {
            return suuntoPreferences.getBoolean(
                BACKGROUND_LOCATION_PERMISSION_REQUESTED_ONCE,
                false
            )
        }

        @RequiresApi(Build.VERSION_CODES.Q)
        @JvmStatic
        fun shouldRequestBackgroundLocationPermissionOnConnectedGps(
            suuntoPreferences: SharedPreferences,
            context: Context
        ): Boolean {
            if (BluetoothUtils.isBluetoothSupported(context) ||
                context.isBackgroundLocationPermissionGranted()
            ) {
                return false
            } else {
                if (!suuntoPreferences.getBoolean(
                        BACKGROUND_LOCATION_PERMISSION_REQUESTED_ONCE_ON_CONNECTED_GPS,
                        false
                    )
                ) {
                    suuntoPreferences.edit {
                        putBoolean(BACKGROUND_LOCATION_PERMISSION_REQUESTED_ONCE, false)
                            .putBoolean(
                                BACKGROUND_LOCATION_PERMISSION_REQUESTED_ONCE_ON_CONNECTED_GPS,
                                true
                            )
                    }
                    return true
                } else {
                    return false
                }
            }
        }
    }

    private data class ConnectedDeviceInfo(
        val suuntoDeviceType: SuuntoDeviceType,
        val watchState: ConnectedWatchState,
        val syncResult: SpartanSyncResult,
        val sku: String
    )

    private data class PermissionsInfo(
        val locationEnabled: Boolean,
        val bluetoothEnabled: Boolean,
        val locationAllowed: Boolean,
        val nearbyDevicesAllowed: Boolean
    )
}
