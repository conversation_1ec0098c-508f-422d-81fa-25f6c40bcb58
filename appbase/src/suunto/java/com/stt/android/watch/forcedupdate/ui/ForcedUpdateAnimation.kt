package com.stt.android.watch.forcedupdate.ui

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import com.airbnb.lottie.compose.LottieAnimation
import com.airbnb.lottie.compose.LottieCompositionSpec
import com.airbnb.lottie.compose.LottieConstants
import com.airbnb.lottie.compose.rememberLottieComposition

@Composable
fun ForcedUpdateAnimation(
    modifier: Modifier = Modifier
) {
    val lottieComposition = rememberLottieComposition(
        LottieCompositionSpec.Asset("forced_update.json"),
        imageAssetsFolder = "images"
    )
    LottieAnimation(
        composition = lottieComposition.value,
        modifier = modifier,
        iterations = LottieConstants.IterateForever,
    )
}
