package com.stt.android.watch.permission

import android.Manifest
import android.bluetooth.BluetoothAdapter
import android.content.ActivityNotFoundException
import android.content.Intent
import android.os.Bundle
import android.transition.AutoTransition
import android.transition.Transition
import android.transition.TransitionManager
import android.view.View
import androidx.appcompat.app.AlertDialog
import androidx.constraintlayout.widget.ConstraintSet
import androidx.fragment.app.viewModels
import com.stt.android.R
import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.AnalyticsEventProperty
import com.stt.android.analytics.AnalyticsProperties
import com.stt.android.common.ui.observeNotNull
import com.stt.android.databinding.FragmentDevicePermissionBinding
import com.stt.android.extensions.getLocationPermissionResult
import com.stt.android.extensions.isNearbyDevicesPermissionGrantedResult
import com.stt.android.extensions.openAppSettings
import com.stt.android.watch.DeviceFragment
import com.stt.android.watch.DeviceStateUpdate
import dagger.hilt.android.AndroidEntryPoint
import pub.devrel.easypermissions.EasyPermissions
import timber.log.Timber
import javax.inject.Inject

@AndroidEntryPoint
class DevicePermissionFragment : DeviceFragment() {
    override fun getLayoutResId() = R.layout.fragment_device_permission
    override val viewModel: DevicePermissionViewModel by viewModels()
    private val viewDataBinding: FragmentDevicePermissionBinding get() = requireBinding()

    private val nearbyDevicesPermissions =
        arrayOf("android.permission.BLUETOOTH_SCAN", "android.permission.BLUETOOTH_CONNECT")

    private var permissionAlertDialog: AlertDialog? = null

    @Inject
    lateinit var amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker

    override fun getNavId() = R.id.devicePermissionFragment

    private val animationRunnable: Runnable = Runnable {
        if (!isAdded) return@Runnable

        /**
         * Listen for animation end event and navigate away from this fragment
         */
        val listener = object : Transition.TransitionListener {
            override fun onTransitionEnd(transition: Transition?) {
                // Move back to scanning/device view
                navigateTo(R.id.deviceConnectingFragment)
            }

            override fun onTransitionResume(transition: Transition?) {
            }

            override fun onTransitionPause(transition: Transition?) {
            }

            override fun onTransitionCancel(transition: Transition?) {
            }

            override fun onTransitionStart(transition: Transition?) {
            }
        }

        val constraintLayout = viewDataBinding.devicePermissionConstraints
        val newConstraintSet = ConstraintSet()
        newConstraintSet.clone(constraintLayout)
        newConstraintSet.constrainHeight(
            R.id.device_background_space,
            resources.getDimensionPixelSize(R.dimen.device_image_height)
        )

        // Start the animation and navigate away from this fragment when the animation ends.
        val transition = AutoTransition()
        transition.duration = ANIMATION_DURATION
        transition.addListener(listener)
        TransitionManager.beginDelayedTransition(
            constraintLayout,
            transition
        )
        newConstraintSet.applyTo(constraintLayout)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        // Handle click events
        viewModel.permissionEvent.observeNotNull(viewLifecycleOwner) { event ->
            when (event) {
                PermissionEvent.ENABLE_BLUETOOTH -> {
                    val intent = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
                    startActivityForResult(intent, REQUEST_ENABLE_BT)
                }

                PermissionEvent.ENABLE_LOCATION -> {
                    val intent = Intent(android.provider.Settings.ACTION_LOCATION_SOURCE_SETTINGS)
                    try {
                        startActivityForResult(intent, REQUEST_CODE_LOCATION_PROVIDER_SETTINGS)
                    } catch (e: ActivityNotFoundException) {
                        Timber.d("Can't open location source provider settings")
                    }
                }

                PermissionEvent.ALLOW_LOCATION -> {
                    requestPermissions(
                        arrayOf(Manifest.permission.ACCESS_FINE_LOCATION),
                        REQUEST_ACCESS_LOCATION
                    )
                    viewModel.onLocationPermissionScreenShowed()
                }

                PermissionEvent.ALLOW_NEARBY_DEVICES -> {
                    requestPermissions(
                        nearbyDevicesPermissions,
                        REQUEST_ACCESS_NEARBY_DEVICES
                    )
                    viewModel.onNearbyDevicesPermissionScreenShowed()
                }
            }
        }

        sendAnalytics()
    }

    override fun onDestroyView() {
        viewDataBinding.root.removeCallbacks(animationRunnable)
        super.onDestroyView()
    }

    @Deprecated("Deprecated in Java")
    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (requestCode == REQUEST_ACCESS_LOCATION) {
            if (EasyPermissions.hasPermissions(requireActivity(), *permissions)) {
                sharedViewModel.checkLocationPermissionState()
                context?.getLocationPermissionResult()?.let {
                    viewModel.onRequestLocationPermissionsResult(it)
                }
            } else if (EasyPermissions.somePermissionPermanentlyDenied(
                    this,
                    permissions.toList()
                )
            ) {
                dismissPermissionAlertDialog()
                permissionAlertDialog = AlertDialog.Builder(requireActivity())
                    .setTitle(R.string.allow_location)
                    .setMessage(R.string.ble_need_location_access)
                    .setPositiveButton(R.string.settings) { _, _ ->
                        requireActivity().openAppSettings()
                    }.setNegativeButton(R.string.cancel, null).show()
            }
        } else if (requestCode == REQUEST_ACCESS_NEARBY_DEVICES) {
            if (EasyPermissions.hasPermissions(requireActivity(), *permissions)) {
                sharedViewModel.checkNearbyDevicesPermissionState()
                context?.applicationContext?.let { appContext ->
                    val result = appContext.isNearbyDevicesPermissionGrantedResult()
                    viewModel.onRequestNearbyDevicesPermissionsResult(result)
                }
            } else if (EasyPermissions.somePermissionPermanentlyDenied(
                    this,
                    permissions.toList()
                )
            ) {
                dismissPermissionAlertDialog()
                permissionAlertDialog = AlertDialog.Builder(requireActivity())
                    .setTitle(R.string.allow_nearby_devices)
                    .setMessage(R.string.permission_required)
                    .setPositiveButton(R.string.settings) { _, _ ->
                        requireActivity().openAppSettings()
                    }.setNegativeButton(R.string.cancel, null).show()
            }
        }
    }

    private fun dismissPermissionAlertDialog() {
        permissionAlertDialog?.dismiss()
        permissionAlertDialog = null
    }

    override fun onDetach() {
        dismissPermissionAlertDialog()
        super.onDetach()
    }

    override fun onStateUpdate(state: DeviceStateUpdate) {
        if (state.permissions.allPermissionsAvailable) {
            // Animate title text moving down with an custom constrain set.
            // Start the animation with a delay to make sure the changed
            // permission/feature binding is updated to the view
            viewDataBinding.root.postDelayed(animationRunnable, ANIMATION_DELAY)
        }
    }

    @Deprecated("Deprecated in Java")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == REQUEST_CODE_LOCATION_EXPLANATION) {
            requestPermissions(
                arrayOf(Manifest.permission.ACCESS_FINE_LOCATION),
                REQUEST_ACCESS_LOCATION
            )
        }
    }

    override fun navigateAutomatically(fragmentNavId: Int): Boolean {
        // Handle navigation always manually
        return false
    }

    private fun sendAnalytics() {
        amplitudeAnalyticsTracker.trackEvent(
            AnalyticsEvent.SUUNTO_BLUETOOTH_OFF_SCREEN,
            AnalyticsProperties()
                .putYesNo(
                    AnalyticsEventProperty.SUUNTO_LOCATION_PERMISSION,
                    sharedViewModel.locationAllowed.value ?: false
                )
                .putOnOff(
                    AnalyticsEventProperty.SUUNTO_BLUETOOTH_SETTING,
                    sharedViewModel.bluetoothEnabled.value ?: false
                )
                .putYesNo(
                    AnalyticsEventProperty.SUUNTO_LOCATION_PROVIDER,
                    sharedViewModel.locationEnabled.value ?: false
                )
        )
    }

    override fun onResume() {
        super.onResume()
        sharedViewModel.checkLocationPermissionState()
        sharedViewModel.checkNearbyDevicesPermissionState()
        sharedViewModel.setNeedNotificationAccessAlert(false)
    }

    companion object {
        private const val ANIMATION_DELAY = 100L
        private const val ANIMATION_DURATION = 500L
        const val REQUEST_CODE_LOCATION_PROVIDER_SETTINGS = 1001
        const val REQUEST_ENABLE_BT = 1002
        const val REQUEST_ACCESS_LOCATION = 1003
        const val REQUEST_CODE_LOCATION_EXPLANATION = 1004
        const val REQUEST_ACCESS_NEARBY_DEVICES = 1005
    }
}
