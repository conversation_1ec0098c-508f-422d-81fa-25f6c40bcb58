package com.stt.android.watch.systemevents

import android.content.Context
import androidx.work.BackoffPolicy
import androidx.work.Constraints
import androidx.work.CoroutineWorker
import androidx.work.ExistingWorkPolicy
import androidx.work.ListenableWorker
import androidx.work.NetworkType
import androidx.work.OneTimeWorkRequest
import androidx.work.WorkManager
import androidx.work.WorkerParameters
import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.AnalyticsEventProperty
import com.stt.android.analytics.AnalyticsProperties
import com.stt.android.backgroundwork.CoroutineWorkerAssistedFactory
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.data.device.DeviceInfoApi
import com.stt.android.data.systemevents.SystemEventsRepository
import com.stt.android.exceptions.remote.HttpException
import com.stt.android.exceptions.remote.ServerError
import com.stt.android.watch.MissingCurrentWatchException
import com.stt.android.worker.ifNotAlreadyScheduled
import com.suunto.connectivity.systemevents.SuuntoSystemEventsAnalysisEvent
import timber.log.Timber
import java.util.concurrent.TimeUnit
import javax.inject.Inject

class SystemEventsRemoteSyncJob(
    private val systemEventsRepository: SystemEventsRepository,
    private val deviceInfoProvider: DeviceInfoApi,
    private val amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker,
    appContext: Context,
    params: WorkerParameters
) : CoroutineWorker(appContext, params) {

    class Factory
    @Inject constructor(
        private val systemEventsRepository: SystemEventsRepository,
        private val deviceInfoProvider: DeviceInfoApi,
        private val amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker,
    ) : CoroutineWorkerAssistedFactory {
        override fun create(context: Context, params: WorkerParameters): ListenableWorker {
            return SystemEventsRemoteSyncJob(
                systemEventsRepository,
                deviceInfoProvider,
                amplitudeAnalyticsTracker,
                context,
                params,
            )
        }
    }

    override suspend fun doWork(): Result {
        return runSuspendCatching {
            Timber.d("Starting SystemEventsRemoteSyncJob")
            val macAddress = deviceInfoProvider.macAddress()
            val systemEventsFiles = systemEventsRepository.fetchSystemEventsFiles(macAddress)
            for (file in systemEventsFiles) {
                Timber.d("Syncing file ${file.name}")
                val systemEventsResponse = systemEventsRepository.fetchSystemEvents(file)
                if (systemEventsResponse == null) {
                    Timber.d("Nothing to sync, remove file and skip")
                    systemEventsRepository.removeSyncedEvents(file)
                    continue
                }
                // remove the timeline useless information and group events by common header
                // events are sorted chronologically from oldest to newest
                val events: List<SuuntoSystemEventsAnalysisEvent> =
                    systemEventsResponse.samples.map { sample ->
                        sample.attributes.values
                    }.flatten()
                        .groupBy({ it.header }, { it.events })
                        .mapValues { (_, values) -> values.flatten().sortedBy { it.timeISO8601 } }
                        .map { SuuntoSystemEventsAnalysisEvent(it.key, it.value) }
                for (event in events) {
                    Timber.d("Syncing ${event.events.count()} events for file ${file.name}")
                    systemEventsRepository.saveSystemEvents(event)
                }
                Timber.d("Remove file ${file.name}")
                systemEventsRepository.removeSyncedEvents(file)
            }
            Timber.d("SystemEventsRemoteSyncJob completed successfully")
            sendAnalyticsEvent(201)
            Result.success()
        }.getOrElse { e ->
            if (e is MissingCurrentWatchException) {
                Timber.i("Error while running SystemEventsRemoteSyncJob, Missing current watch")
            } else {
                Timber.w(e, "Error while running SystemEventsRemoteSyncJob")
            }
            val code = (e as? HttpException)?.code ?: -1
            sendAnalyticsEvent(code)
            if (e is ServerError) Result.retry() else Result.failure()
        }
    }

    private suspend fun sendAnalyticsEvent(resultCode: Int) {
        runSuspendCatching {
            val variant = deviceInfoProvider.variant()
            val version = deviceInfoProvider.version()
            amplitudeAnalyticsTracker.trackEvent(
                AnalyticsEvent.SUUNTO_DEBUG_SYSTEM_EVENT,
                AnalyticsProperties().apply {
                    put(AnalyticsEventProperty.SUUNTO_WATCH_MODEL, variant)
                    put(AnalyticsEventProperty.SUUNTO_WATCH_FIRMWARE_VERSION, version)
                    put(AnalyticsEventProperty.SUUNTO_SYSTEM_EVENT_RESULT_CODE, resultCode)
                }
            )
        }.onFailure {
            Timber.w(it, "An error has occurred while sending debug system event")
        }
    }

    companion object {
        const val TAG = "SystemEventsRemoteSyncJob"

        fun schedule(workManager: WorkManager) {
            workManager.ifNotAlreadyScheduled(TAG) {
                workManager.enqueueUniqueWork(
                    TAG,
                    ExistingWorkPolicy.APPEND_OR_REPLACE,
                    OneTimeWorkRequest.Builder(SystemEventsRemoteSyncJob::class.java)
                        .setConstraints(
                            Constraints.Builder()
                                .setRequiredNetworkType(NetworkType.CONNECTED)
                                .build()
                        )
                        .setBackoffCriteria(BackoffPolicy.EXPONENTIAL, 1, TimeUnit.MINUTES)
                        .build()
                )
            }
        }
    }
}
