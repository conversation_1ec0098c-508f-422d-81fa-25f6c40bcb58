package com.stt.android.watch.offlinemaps.domain

import com.stt.android.common.coroutines.CoroutinesDispatcherProvider
import com.stt.android.watch.offlinemaps.datasource.MapDownloadDataSource
import kotlinx.coroutines.withContext
import javax.inject.Inject

class NotifyAreaUnderDownloadDeletedUseCase @Inject constructor(
    private val dataSource: MapDownloadDataSource,
    private val dispatcherProvider: CoroutinesDispatcherProvider,
) {
    suspend operator fun invoke(regionId: String) = withContext(dispatcherProvider.io) {
        dataSource.notifyAreaUnderDownloadDeleted(regionId)
    }
}
