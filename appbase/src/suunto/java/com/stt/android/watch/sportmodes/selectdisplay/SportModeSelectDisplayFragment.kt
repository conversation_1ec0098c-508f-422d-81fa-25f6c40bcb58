package com.stt.android.watch.sportmodes.selectdisplay

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.viewModels
import com.stt.android.R
import com.stt.android.watch.sportmodes.SportModeFragment
import com.stt.android.watch.sportmodes.list.ToolbarDelegate
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class SportModeSelectDisplayFragment : SportModeFragment<SportModeSelectDisplayViewModel>() {
    override val viewModel: SportModeSelectDisplayViewModel by viewModels()

    @Inject
    lateinit var actionModeDelegate: ToolbarDelegate

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        viewModel.sharedViewModel.setTitle(getString(R.string.sport_modes_display_selection_title))
        actionModeDelegate.changeToolbarToPickMode()
        return super.onCreateView(inflater, container, savedInstanceState)
    }

    override fun onDestroy() {
        super.onDestroy()
        actionModeDelegate.changeToolbarToDefaultMode()
    }
}
