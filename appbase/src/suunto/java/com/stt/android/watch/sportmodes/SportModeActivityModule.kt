package com.stt.android.watch.sportmodes

import com.stt.android.data.sportmodes.SportModeComponentManager
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

// SportModeComponentManagerImpl is injected as @Singleton
@InstallIn(SingletonComponent::class)
@Module
abstract class SportModeActivityModule {

    @Binds
    abstract fun bindSportModeComponentManager(manager: SportModeComponentManagerImpl): SportModeComponentManager
}
