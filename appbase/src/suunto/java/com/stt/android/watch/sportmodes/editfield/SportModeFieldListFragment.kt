package com.stt.android.watch.sportmodes.editfield

import android.os.Bundle
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.Divider
import androidx.compose.material3.Icon
import androidx.compose.material3.ListItem
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.ViewCompositionStrategy
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.findNavController
import com.stt.android.R
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.dividerColor
import com.stt.android.compose.theme.lightGrey
import com.stt.android.compose.theme.material3.body
import com.stt.android.compose.theme.material3.bodySmallBold
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.util.setContentWithM3Theme
import com.stt.android.domain.sportmodes.Field
import com.stt.android.domain.sportmodes.FieldSection
import com.stt.android.watch.sportmodes.SportModeHolderViewModel
import com.stt.android.watch.sportmodes.list.ToolbarDelegate
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class SportModeFieldListFragment : Fragment() {
    private val viewModel: SportModeFieldListViewModel by viewModels()

    @Inject
    lateinit var actionModeDelegate: ToolbarDelegate

    private var saveButton: MenuItem? = null

    @Deprecated("Deprecated in Java")
    override fun onCreateOptionsMenu(menu: Menu, inflater: MenuInflater) {
        inflater.inflate(R.menu.sport_modes, menu)
        saveButton = menu.findItem(R.id.save_sportmode)
        super.onCreateOptionsMenu(menu, inflater)
    }

    @Deprecated("Deprecated in Java")
    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            R.id.save_sportmode -> {
                viewModel.save()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setHasOptionsMenu(true)
        val sharedViewModel =
            ViewModelProvider(requireActivity())[SportModeHolderViewModel::class.java]

        viewModel.sharedViewModel = sharedViewModel
        viewModel.loadData()
    }

    override fun onDestroy() {
        super.onDestroy()
        actionModeDelegate.changeToolbarToDefaultMode()
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        viewModel.sharedViewModel.setTitle(getString(R.string.sport_modes_field_selection_title))
        actionModeDelegate.changeToolbarToPickMode()
        return ComposeView(requireContext()).apply {
            setViewCompositionStrategy(
                ViewCompositionStrategy.DisposeOnLifecycleDestroyed(viewLifecycleOwner)
            )

            setContentWithM3Theme {
                val sections = viewModel.sections
                val selectedIds = viewModel.selectedIds
                val navigateBack by viewModel.navigateBack.collectAsState()
                
                LaunchedEffect(navigateBack) {
                    if (navigateBack) {
                        val navController = findNavController()
                        navController.popBackStack(
                            R.id.sportModeEditDisplaysFragment,
                            false
                        )
                        viewModel.onBackEventHandled()
                    }
                }
                Body(
                    sections = sections,
                    selectedIds = selectedIds,
                    onFieldClick = {
                        viewModel.setField(it)
                    },
                )
            }
        }
    }
}

@Composable
private fun Body(
    sections: List<FieldSection>,
    onFieldClick: (Field) -> Unit,
    selectedIds: List<String>,
    modifier: Modifier = Modifier
) {
    Scaffold(
        modifier = modifier,
    ) {
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(it)
        ) {
            items(sections) { section ->
                FieldSectionItem(
                    section = section,
                    onFieldClick = onFieldClick,
                    selectedIds = selectedIds
                )
            }
        }
    }
}

@Composable
private fun FieldSectionItem(
    section: FieldSection,
    selectedIds: List<String>,
    onFieldClick: (Field) -> Unit,
    modifier: Modifier = Modifier
) {

    Column(modifier = modifier.fillMaxWidth()) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(50.dp)
                .background(MaterialTheme.colorScheme.lightGrey)
                .padding(horizontal = MaterialTheme.spacing.medium),
            contentAlignment = Alignment.CenterStart
        ) {
            Text(text = section.name, style = MaterialTheme.typography.bodySmallBold)
        }
        section.fields.forEach {
            FieldItem(
                text = it.name,
                isSelected = it.id in selectedIds,
                onFieldClick = { onFieldClick(it) }
            )
            Divider(thickness = 1.dp, color = MaterialTheme.colorScheme.dividerColor)
        }
    }
}

@Composable
private fun FieldItem(
    text: String,
    isSelected: Boolean,
    onFieldClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    ListItem(
        headlineContent = {
            Text(text = text, style = MaterialTheme.typography.body)
        },
        trailingContent = {
            if (isSelected) {
                Icon(
                    painter = painterResource(R.drawable.icon_check),
                    contentDescription = null,
                )
            }
        },
        modifier = modifier
            .fillMaxWidth()
            .clickable { onFieldClick() },
    )
}

@Preview(showBackground = true)
@Composable
private fun BodyPreview() {
    M3AppTheme {
        Body(
            sections = listOf(
                FieldSection(
                    id = "TEXT_SECTION_1",
                    name = "Section 1",
                    fields = listOf(
                        Field(
                            id = "TEXT_FIELD_1",
                            name = "Field 1",
                            subFields = listOf()
                        ),
                        Field(
                            id = "TEXT_FIELD_2",
                            name = "Field 2",
                            subFields = listOf()
                        )
                    )
                ),
                FieldSection(
                    id = "TEXT_SECTION_2",
                    name = "Section 2",
                    fields = listOf(
                        Field(
                            id = "TEXT_FIELD_3",
                            name = "Field 3",
                            subFields = listOf()
                        ),
                        Field(
                            id = "TEXT_FIELD_4",
                            name = "Field 4",
                            subFields = listOf()
                        )
                    )
                )
            ),
            onFieldClick = {},
            selectedIds = listOf("TEXT_FIELD_1")
        )
    }
}
