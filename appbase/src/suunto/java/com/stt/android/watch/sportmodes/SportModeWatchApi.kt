package com.stt.android.watch.sportmodes

import com.stt.android.data.sportmodes.SportModesApi
import com.stt.android.watch.SuuntoWatchModel
import hu.akarnokd.rxjava.interop.RxJavaInterop
import io.reactivex.Completable
import io.reactivex.Single
import java.util.concurrent.TimeUnit
import javax.inject.Inject

class SportModeWatchApi
@Inject constructor(
    val suuntoWatchModel: SuuntoWatchModel
) : SportModesApi {
    override fun fetchSportModeGroup(modeId: Int): Single<String> {
        return RxJavaInterop.toV2Single(
            suuntoWatchModel.currentWatch
                .flatMap { spartan ->
                    spartan.sportModesSpartanWrapper.fetchSportMode(modeId)
                }
        )
            .timeout(40L, TimeUnit.SECONDS)
    }

    override fun fetchSportModes(): Single<String> {
        return RxJavaInterop.toV2Single(
            suuntoWatchModel.currentWatch
                .flatMap { spartan ->
                    spartan.sportModesSpartanWrapper.fetchSportModes()
                }
        )
            .timeout(40L, TimeUnit.SECONDS)
    }

    override fun fetchSportModeSettings(modeId: Int): Single<String> {
        return RxJavaInterop.toV2Single(
            suuntoWatchModel.currentWatch
                .flatMap { spartan ->
                    spartan.sportModesSpartanWrapper.fetchSportModeSettings(modeId)
                }
        )
            .timeout(20L, TimeUnit.SECONDS)
    }

    override fun fetchSportModeDisplays(modeId: Int): Single<String> {
        return RxJavaInterop.toV2Single(
            suuntoWatchModel.currentWatch.flatMap { spartan ->
                spartan.sportModesSpartanWrapper.fetchSportModeDisplays(modeId)
            }
        )
            .timeout(20L, TimeUnit.SECONDS)
    }

    override fun putSportModeSettings(modeId: Int, json: String): Completable {
        return RxJavaInterop.toV2Completable(
            suuntoWatchModel.currentWatch.flatMapCompletable { spartan ->
                spartan.sportModesSpartanWrapper.setSportModeSettings(modeId, json)
            }
        )
            .timeout(30L, TimeUnit.SECONDS)
    }

    override fun putSportModeDisplays(modeId: Int, json: String): Completable {
        return RxJavaInterop.toV2Completable(
            suuntoWatchModel.currentWatch.flatMapCompletable { spartan ->
                spartan.sportModesSpartanWrapper.setSportModeDisplays(modeId, json)
            }
        )
            .timeout(30L, TimeUnit.SECONDS)
    }

    override fun postSportModeGroup(groupId: Int?, json: String?): Completable {
        return if (groupId == null || json == null) {
            io.reactivex.Completable.complete()
        } else {
            RxJavaInterop.toV2Completable(
                suuntoWatchModel.currentWatch.flatMapCompletable { spartan ->
                    spartan.sportModesSpartanWrapper.setSportModeGroups(groupId, json)
                }
            )
        }
            .timeout(30L, TimeUnit.SECONDS)
    }

    override fun deleteSportModeGroup(groupId: Int): Completable {
        return RxJavaInterop.toV2Completable(
            suuntoWatchModel.currentWatch.flatMapCompletable { spartan ->
                spartan.sportModesSpartanWrapper.deleteSportModeGroup(groupId)
            }
        )
            .timeout(20L, TimeUnit.SECONDS)
    }
}
