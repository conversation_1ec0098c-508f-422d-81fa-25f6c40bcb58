package com.stt.android.watch.sportmodes.list

import com.stt.android.domain.sportmodes.SportModeHeader

/**
 * Listener that fires event whenever sportmode is selected
 */
interface OnSportModeSelectedListener {
    /**
     * @param item the selected [SportModeItem]
     */
    fun onSportModeItemClicked(item: SportModeItem)

    /**
     * Deletes the sport mode
     * @param sportModeItem the sport mode item to delete
     */
    fun deleteSportMode(sportModeItem: SportModeItem)

    /**
     * Notifies that a multisport mode should be edited
     * @param sportModeHeader the sport mode header to edit
     */
    fun onMultisportModeEditRequested(sportModeHeader: SportModeHeader?)

    /**
     * Notifies that the deletion state has changed
     */
    fun changeInDeletion(inDeletion: Boolean)
}
