package com.stt.android.watch

import android.content.Context
import android.content.Intent

interface SuuntoPlusStoreNavigator {
    fun newSuuntoPlusStoreIntent(context: Context): Intent

    fun newSuuntoPlusStoreGuidesPageIntent(
        context: Context,
        noBackStack: Boolean = false
    ): Intent

    fun newSuuntoPlusStoreGuideDetailsIntent(
        context: Context,
        guideId: String,
        noBackStack: Boolean = false
    ): Intent

    fun newSuuntoPlusStoreSportAppDetailsIntent(context: Context, sportsAppId: String): Intent

    fun newSuuntoPlusStorePartnersPageIntent(
        context: Context,
        noBackStack: Boolean = false
    ): Intent

    fun newSuuntoPlusStoreTrainingPlanIntent(
        context: Context,
        planId: String,
        noBackStack: Boolean = false
    ): Intent
}
