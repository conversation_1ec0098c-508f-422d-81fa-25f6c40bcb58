package com.stt.android.watch.forcedupdate.ui

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Scaffold
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.material.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.stt.android.R
import com.stt.android.compose.component.SuuntoIconButton
import com.stt.android.compose.layout.ContentCenteringColumn
import com.stt.android.compose.theme.SuuntoIcons
import com.stt.android.compose.theme.bodyLarge
import com.stt.android.compose.theme.bodyXLargeBold
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.widgets.PrimaryButton

@Composable
fun ForcedUpdateScreen(
    displayName: String,
    onBackPressed: () -> Unit,
    modifier: Modifier = Modifier
) {
    Scaffold(
        modifier = modifier,
        topBar = {
            TopAppBar(
                modifier = Modifier,
                contentColor = MaterialTheme.colors.onSurface,
                backgroundColor = MaterialTheme.colors.surface,
                title = {
                    Text(text = displayName.uppercase())
                },
                elevation = 0.dp,
                navigationIcon = {
                    SuuntoIconButton(
                        icon = SuuntoIcons.ActionBack,
                        onClick = onBackPressed,
                        contentDescription = stringResource(R.string.back),
                    )
                }
            )
        }
    ) { internalPadding ->
        ContentCenteringColumn(Modifier.padding(internalPadding)) {
            Surface {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Top,
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(horizontal = MaterialTheme.spacing.large)
                ) {
                    Box(
                        modifier = Modifier
                            .weight(1f)
                    ) {
                        ForcedUpdateAnimation(
                            modifier = Modifier
                                .fillMaxWidth()
                        )
                    }

                    Text(
                        stringResource(id = R.string.forced_update_title),
                        style = MaterialTheme.typography.bodyXLargeBold,
                        textAlign = TextAlign.Center
                    )

                    Spacer(modifier = Modifier.height(MaterialTheme.spacing.medium))

                    Text(
                        stringResource(id = R.string.forced_update_info),
                        style = MaterialTheme.typography.bodyLarge,
                        textAlign = TextAlign.Center
                    )
                    PrimaryButton(
                        onClick = onBackPressed,
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(MaterialTheme.spacing.medium)
                    ) {
                        Text(
                            text = stringResource(id = R.string.ok),
                            style = MaterialTheme.typography.bodyLarge,
                        )
                    }
                }
            }
        }
    }
}
