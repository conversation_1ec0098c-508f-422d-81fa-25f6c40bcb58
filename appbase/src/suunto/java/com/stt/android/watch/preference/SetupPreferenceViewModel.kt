package com.stt.android.watch.preference

import android.content.SharedPreferences
import androidx.lifecycle.viewModelScope
import com.stt.android.common.coroutines.CoroutineViewModel
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.activitydata.goals.FetchEnergyGoalUseCase
import com.stt.android.domain.activitydata.goals.FetchSleepGoalUseCase
import com.stt.android.domain.activitydata.goals.FetchStepsGoalUseCase
import com.stt.android.home.settings.goalsettings.GoalSettingsViewModel.Companion.defaultEnergyGoal
import com.stt.android.home.settings.goalsettings.GoalSettingsViewModel.Companion.defaultSleepGoal
import com.stt.android.home.settings.goalsettings.GoalSettingsViewModel.Companion.defaultStepsGoal
import com.stt.android.ui.activities.settings.helper.PredefinedRepliesHelper
import com.stt.android.utils.STTConstants
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.async
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject
import kotlin.math.roundToInt

@HiltViewModel
class SetupPreferenceViewModel
@Inject constructor(
    private val predefinedRepliesHelper: PredefinedRepliesHelper,
    private val sharedPreferences: SharedPreferences,
    private val fetchStepsGoalUseCase: FetchStepsGoalUseCase,
    private val fetchEnergyGoalUseCase: FetchEnergyGoalUseCase,
    private val fetchSleepGoalUseCase: FetchSleepGoalUseCase,
    dispatchers: CoroutinesDispatchers,
) : CoroutineViewModel(dispatchers) {
    private var _stateFlow: MutableStateFlow<SetupPreference> =
        MutableStateFlow(SetupPreference())
    internal val stateFlow = _stateFlow.asStateFlow()

    private val _reducerFlow: MutableSharedFlow<SetupPreferenceReducer> = MutableSharedFlow()
    private val reducerFlow = _reducerFlow.asSharedFlow()

    init {
        viewModelScope.launch {
            reducerFlow.collect {
                _stateFlow.value = withContext(io) {
                    it.reduce(_stateFlow.value)
                }
            }
        }
        initialPreference()
    }

    private fun initialPreference() {
        viewModelScope.launch(io) {
            val weeklyTraining = sharedPreferences.getInt(
                STTConstants.SuuntoPreferences.KEY_SUUNTO_WATCH_WEEKLY_TARGET_DURATION,
                STTConstants.SuuntoPreferences.KEY_SUUNTO_WATCH_WEEKLY_TARGET_DURATION_DEFAULT,
            )
            val stepsDefer = async {
                runSuspendCatching {
                    fetchStepsGoalUseCase.fetchLocalStepsGoal()
                }.getOrNull()?.takeIf { it > 0 } ?: defaultStepsGoal
            }
            val energyDefer = async {
                runSuspendCatching {
                    fetchEnergyGoalUseCase.fetchLocalEnergyGoal().inKcal.roundToInt()
                }.getOrNull()?.takeIf { it > 0 } ?: defaultEnergyGoal
            }
            val sleepDefer = async {
                runSuspendCatching {
                    fetchSleepGoalUseCase.fetchLocalSleepGoal()
                }.getOrNull()?.inWholeSeconds?.toInt()?.takeIf { it > 0 } ?: defaultSleepGoal
            }
            _stateFlow.update {
                it.copy(
                    notifications = PreferenceNotification(
                        predefinedReplies = predefinedRepliesHelper.predefinedReplies,
                    ),
                    goalSetting = PreferenceGoalSetting(
                        weeklyTraining = weeklyTraining,
                        dailyKcal = energyDefer.await(),
                        dailySteps = stepsDefer.await(),
                        dailySleep = sleepDefer.await(),
                    )
                )
            }
        }
    }

    fun invokeReducer(reducer: SetupPreferenceReducer) {
        viewModelScope.launch {
            _reducerFlow.emit(reducer)
        }
    }
}

