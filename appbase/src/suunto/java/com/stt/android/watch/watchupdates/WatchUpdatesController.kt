package com.stt.android.watch.watchupdates

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.view.View
import android.view.ViewGroup
import android.webkit.WebChromeClient
import android.webkit.WebResourceRequest
import android.webkit.WebView
import android.webkit.WebViewClient
import android.widget.FrameLayout
import android.widget.ProgressBar
import androidx.core.net.toUri
import androidx.core.view.updateLayoutParams
import androidx.databinding.BindingAdapter
import com.stt.android.R
import com.stt.android.common.viewstate.ViewState
import com.stt.android.common.viewstate.ViewStateEpoxyController
import com.stt.android.databinding.ViewholderWatchupdatesVersionBinding
import com.stt.android.settingsHeaderItem
import com.stt.android.utils.PreferencesUtils
import com.stt.android.utils.STTConstants
import com.stt.android.watchupdatesButton
import com.stt.android.watchupdatesFootertext
import com.stt.android.watchupdatesLearnMore
import com.stt.android.watchupdatesLoadingWait
import com.stt.android.watchupdatesProgress
import com.stt.android.watchupdatesSoftwareUpToDate
import com.stt.android.watchupdatesText
import com.stt.android.watchupdatesVersion
import com.stt.android.watchupdatesVersionLog
import com.suunto.connectivity.repository.commands.OtaUpdateState
import dagger.hilt.android.qualifiers.ActivityContext
import java.text.DecimalFormat
import javax.inject.Inject
import kotlin.math.roundToInt

class WatchUpdatesController
@Inject constructor(
    @ActivityContext private val activityContext: Context
) :
    ViewStateEpoxyController<WatchUpdatesFragmentData?>() {
    override fun buildModels(viewState: ViewState<WatchUpdatesFragmentData?>) {
        if (viewState.isLoading()) {
            createLoadingModel()
        } else if (viewState.data?.selectingFirmware == true) {
            createSelectingFirmwareModel()
        } else {
            viewState.data?.let { data ->
                val showSelectedFwInfo = data.firmwareSelectedOnWatch != null &&
                    data.firmwareSelectedOnWatch.packageId != data.firmwareOnServer?.packageId

                when (data.otaUpdateState) {
                    OtaUpdateState.UpdateAvailable,
                    OtaUpdateState.DowngradeAvailable -> {
                        data.firmwareOnServer?.let {
                            createFirmwareAvailableModel(it, data)
                        }
                    }
                    OtaUpdateState.FirmwareTransferInProgress -> {
                        createDownloadInProgressModel(data)
                    }
                    OtaUpdateState.DeviceUpToDate -> {
                        if (!showSelectedFwInfo) {
                            createDeviceUpToDateModel(data)
                        }
                    }
                    OtaUpdateState.UpdateReadyToInstallNeedsConfirmation,
                    OtaUpdateState.DowngradeReadyToInstallNeedsConfirmation -> {
                        data.firmwareOnServer?.let {
                            createFirmwareReadyToInstallNeedsConfirmationModel(it, data)
                        }
                    }
                    OtaUpdateState.UpdateWaitingToBeInstalled,
                    OtaUpdateState.DowngradeWaitingToBeInstalled -> {
                        data.firmwareSelectedOnWatch?.let {
                            createFirmwareWaitingToBeInstalledModel(it, data)
                        }
                    }
                    OtaUpdateState.Unknown -> {
                        // Unknown states can happen at least when FW transfer is revving up,
                        // a proper state should be emited later so keep showing the spinner.
                        createLoadingModel()
                    }
                }

                if (showSelectedFwInfo) {
                    createFirmwareWaitingToBeInstalledModel(
                        versionToBeInstalled = data.firmwareSelectedOnWatch!!, // showSelectedFwInfo can't be true if null
                        data = data,
                        idSuffix = "selectedFW"
                    )
                }
            }
        }
    }

    private fun createLoadingModel() {
        watchupdatesLoadingWait {
            id("Loading wait")
            description(R.string.watch_updates_checking_for_updates)
        }
    }

    private fun createSelectingFirmwareModel() {
        watchupdatesLoadingWait {
            id("Loading wait")
        }
    }

    private fun createDownloadInProgressModel(data: WatchUpdatesFragmentData) {
        headerItem(data.versionOnServerIsDowngradeFromInstalled)
        versionInfo(data.firmwareOnServer)

        val progressDescription = if (data.watchIsBusy) {
            R.string.watch_ui_status_watch_busy
        } else {
            R.string.watch_updates_update_loading
        }
        watchupdatesProgress {
            id("Progress")
            progressValue(data.progressPercentage)
            statustext("${data.progressPercentage} %")
            description(progressDescription)
        }

        watchupdatesFootertext {
            id("footer")
            text(R.string.watch_updates_download_information)
        }

        val showOtaUpdateStopButton = PreferencesUtils.getFeatureToggleSharedPreferences(
            activityContext,
            STTConstants.FeatureTogglePreferences.KEY_OTA_UPDATE_STOP_BUTTON,
            STTConstants.FeatureTogglePreferences.KEY_OTA_UPDATE_STOP_BUTTON_DEFAULT
        )
        if (showOtaUpdateStopButton) {
            val stopButtonTextRes = if (data.versionOnServerIsDowngradeFromInstalled) {
                R.string.watch_updates_stop_downgrade
            } else {
                R.string.watch_updates_stop_update
            }

            watchupdatesText {
                id("stop")
                text(stopButtonTextRes)
                onClickListener { _, _, _, _ ->
                    data.onStopOtaUpdateClicked()
                }
                clickable(true)
            }
        }

        versionInfoLog(data.firmwareOnServer)
        learnMoreButton(data)
    }

    private fun createDeviceUpToDateModel(data: WatchUpdatesFragmentData) {
        watchupdatesSoftwareUpToDate {
            id("upToDate")
            version(data.firmwareInstalledOnWatch?.versionName ?: "")
        }
        versionInfoLog(data.firmwareOnServer, "")
        learnMoreButton(data)
    }

    private fun createFirmwareAvailableModel(
        versionToBeInstalled: WatchUpdatesFragmentData.VersionInfo,
        data: WatchUpdatesFragmentData,
        idSuffix: String = ""
    ) {
        val isDowngrade = versionToBeInstalled.isDowngradeComparedTo(data.firmwareInstalledOnWatch)
        headerItem(isDowngrade = isDowngrade, idSuffix = idSuffix)
        versionInfo(versionInfo = versionToBeInstalled, idSuffix = idSuffix)

        val buttonTextResId = if (isDowngrade) {
            R.string.watch_updates_download_downgrade
        } else {
            R.string.watch_updates_download_update
        }

        if (!data.watchLowBattery) {
            watchupdatesButton {
                id("button$idSuffix")
                text(buttonTextResId)
                onClickListener { _, _, _, _ ->
                    data.onDownloadFirmwareClicked()
                }
                waitState(data.waitingForFirmwareFileLoad)
            }

            watchupdatesFootertext {
                id("footer")
                text(R.string.watch_updates_download_information)
            }
        } else {
            watchupdatesText {
                id("low_battery$idSuffix")
                text(R.string.watch_updates_low_battery)
            }
        }
        versionInfoLog(versionInfo = versionToBeInstalled, idSuffix = idSuffix)
        learnMoreButton(data = data, idSuffix = idSuffix)
    }

    private fun createFirmwareReadyToInstallNeedsConfirmationModel(
        versionToBeInstalled: WatchUpdatesFragmentData.VersionInfo,
        data: WatchUpdatesFragmentData,
        idSuffix: String = ""
    ) {
        val isDowngrade = versionToBeInstalled.isDowngradeComparedTo(data.firmwareInstalledOnWatch)
        headerItem(isDowngrade = isDowngrade, idSuffix = idSuffix)
        versionInfo(versionInfo = versionToBeInstalled, idSuffix = idSuffix)
        val confirmTextResId = if (data.watchLowBattery) {
            R.string.watch_updates_low_battery
        } else if (isDowngrade) {
            R.string.watch_updates_confirm_downgrade_text
        } else {
            R.string.watch_updates_confirm_update_text
        }
        watchupdatesText {
            id("confirm_install_firmware$idSuffix")
            text(confirmTextResId)
        }

        if (!data.watchLowBattery) {
            val buttonTextResId = if (isDowngrade) {
                R.string.watch_updates_install_downgrade
            } else {
                R.string.watch_updates_install_update
            }
            watchupdatesButton {
                id("button$idSuffix")
                text(buttonTextResId)
                onClickListener { _, _, _, _ ->
                    data.onConfirmInstallClicked()
                }
            }
        }
        versionInfoLog(versionInfo = versionToBeInstalled, idSuffix = idSuffix)
        learnMoreButton(data = data, idSuffix = idSuffix)
    }

    private fun createFirmwareWaitingToBeInstalledModel(
        versionToBeInstalled: WatchUpdatesFragmentData.VersionInfo,
        data: WatchUpdatesFragmentData,
        idSuffix: String = ""
    ) {
        val isDowngrade = versionToBeInstalled.isDowngradeComparedTo(data.firmwareInstalledOnWatch)
        headerItem(isDowngrade = isDowngrade, idSuffix = idSuffix)
        versionInfo(versionInfo = versionToBeInstalled, idSuffix = idSuffix)
        versionInfoLog(versionInfo = versionToBeInstalled, idSuffix = idSuffix)
        learnMoreButton(data = data, idSuffix = idSuffix)

        val (installHelpTextRes, checklistHeaderTextRes) = if (isDowngrade) {
            R.string.watch_updates_install_help_downgrade_text to R.string.watch_updates_install_help_checklist_downgrade_header
        } else {
            R.string.watch_updates_install_help_update_text to R.string.watch_updates_install_help_checklist_update_header
        }

        watchupdatesText {
            id("install_help$idSuffix")
            text(installHelpTextRes)
        }

        watchupdatesText {
            id("install_help_checklist_header$idSuffix")
            text(checklistHeaderTextRes)
            removeBottomPadding(true)
        }

        watchupdatesText {
            id("install_help_checklist$idSuffix")
            text(R.string.watch_updates_install_help_checklist)
        }

        watchupdatesButton {
            id("button$idSuffix")
            text(R.string.watch_updates_need_help)
            onClickListener { _, _, _, _ ->
                data.onNeedHelpClicked()
            }
        }
    }

    private fun headerItem(
        isDowngrade: Boolean = false,
        idSuffix: String = ""
    ) {
        val textRes = if (isDowngrade) {
            R.string.watch_updates_downgrade_available
        } else {
            R.string.watch_updates_update_availale
        }

        settingsHeaderItem {
            id("Header$idSuffix")
            title(textRes)
        }
    }

    private fun versionInfo(
        versionInfo: WatchUpdatesFragmentData.VersionInfo?,
        idSuffix: String = ""
    ) {
        watchupdatesVersion {
            id("version$idSuffix")
            version(versionInfo?.versionName ?: "-")
            this.onBind { _, view, _ ->
                val binding = view.dataBinding as ViewholderWatchupdatesVersionBinding
                binding.versionText.isSelected = true
            }
        }
    }

    private fun versionInfoLog(
        versionInfo: WatchUpdatesFragmentData.VersionInfo?,
        idSuffix: String = ""
    ) {
        if (versionInfo?.versionLog?.isNotBlank() == true) {
            watchupdatesVersionLog {
                id("versionLog$idSuffix")
                versionLog(versionInfo.versionLog)
                activity(activityContext as Activity)
            }
        }
    }

    private fun learnMoreButton(
        data: WatchUpdatesFragmentData,
        idSuffix: String = ""
    ) {
        watchupdatesLearnMore {
            id("learn_more$idSuffix")
            onClickListener { _, _, _, _ ->
                data.onLearnMoreClicked()
            }
        }
    }

    private fun sizeInMegabytes(sizeInBytes: Long): String {
        val df = DecimalFormat("0.00")
        val inMegaBytes = sizeInBytes.toFloat() / (1024f * 1024f)
        return df.format(inMegaBytes)
    }
}

@SuppressLint("SetJavaScriptEnabled")
@BindingAdapter("activity", "versionLog")
fun setVersionInfoLog(webView: WebView, activity: Activity, versionLog: String) {
    with(webView) {
        settings.javaScriptEnabled = true
        settings.domStorageEnabled = true
        settings.mediaPlaybackRequiresUserGesture = false

        webChromeClient = object : WebChromeClient() {
            private var customView: View? = null
            private var customViewCallback: CustomViewCallback? = null
            private var originalOrientation: Int = 0
            private var fullscreenContainer: FrameLayout? = null

            override fun onShowCustomView(view: View?, callback: CustomViewCallback?) {
                if (customView != null) {
                    onHideCustomView()
                }

                customView = view
                customViewCallback = callback
                originalOrientation = activity.requestedOrientation

                activity.window.decorView.systemUiVisibility = (
                    View.SYSTEM_UI_FLAG_FULLSCREEN
                        or View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                        or View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
                    )

                fullscreenContainer = FrameLayout(activity).apply {
                    layoutParams = FrameLayout.LayoutParams(
                        ViewGroup.LayoutParams.MATCH_PARENT,
                        ViewGroup.LayoutParams.MATCH_PARENT
                    )
                    addView(view)
                }

                (activity.window.decorView as FrameLayout).addView(fullscreenContainer)
                webView.visibility = View.GONE
            }

            override fun onHideCustomView() {
                customView?.let {
                    (activity.window.decorView as FrameLayout).removeView(fullscreenContainer)
                    fullscreenContainer = null
                    customView = null
                    webView.visibility = View.VISIBLE
                    customViewCallback?.onCustomViewHidden()
                    activity.requestedOrientation = originalOrientation
                    activity.window.decorView.systemUiVisibility = View.SYSTEM_UI_FLAG_VISIBLE
                }
            }

            override fun getVideoLoadingProgressView(): View {
                return ProgressBar(activity)
            }
        }

        webViewClient = object : WebViewClient() {
            override fun shouldOverrideUrlLoading(
                view: WebView?,
                request: WebResourceRequest?
            ): Boolean {
                val url = request?.url.toString()
                if (url.startsWith("http") || url.startsWith("https")) {
                    runCatching {
                        val uri = url.toUri()
                        @Suppress("UnsafeImplicitIntentLaunch")
                        activity.startActivity(Intent(Intent.ACTION_VIEW).setData(uri))
                    }
                    return true
                }

                return false
            }

            override fun onPageFinished(view: WebView?, url: String?) {
                view?.post {
                    view.evaluateJavascript(
                        "(function() { return document.body.scrollHeight; })();"
                    ) { heightStr ->
                        heightStr?.toIntOrNull()?.let { height ->
                            view.updateLayoutParams {
                                this.height =
                                    (view.context.resources.displayMetrics.density * height).roundToInt()
                            }
                        }
                    }
                }
            }
        }
        loadDataWithBaseURL(
            null,
            versionLog,
            "text/html",
            "UTF-8",
            null
        )
    }
}

