package com.stt.android.watch

import android.content.res.Resources
import com.stt.android.R
import com.stt.android.domain.device.ConnectedWatchSyncState
import com.stt.android.domain.device.SyncState
import com.stt.android.text.HtmlParser
import com.stt.android.ui.utils.TextFormatter
import com.stt.android.utils.ZoneSenseUtils
import com.stt.android.watch.pair.Paired
import com.stt.android.watch.pair.PairingDone
import com.stt.android.watch.pair.PairingStateEvent
import com.suunto.connectivity.util.failedMoveSyncsFromWatchToPhone
import com.suunto.connectivity.util.newMovesSyncedFromWatchToPhone
import com.suunto.connectivity.watch.SpartanSyncResult
import javax.inject.Inject

/**
 * DeviceTextFormatter is an injectable helper class for formatting complex
 * strings in device fragments.
 */
class DeviceTextFormatter
@Inject constructor(
    private val resources: Resources,
    private val htmlParser: HtmlParser,
    private val zoneSenseUtils: ZoneSenseUtils,
) {
    /**
     * Create String describing the given synchronization state
     */
    fun formatSyncText(syncState: ConnectedWatchSyncState): String {
        return when (syncState.state) {
            SyncState.SYNCING_WORKOUTS -> {
                if (syncState.stepCount == 0) {
                    ""
                } else {
                    val moves = resources
                        .getQuantityString(
                            R.plurals.watch_ui_workout_quantity,
                            syncState.stepCount,
                            syncState.stepCount
                        )
                    "${syncState.step}/$moves"
                }
            }
            SyncState.CHECKING_FOR_NEW_GPS,
            SyncState.SYNCING_GPS -> resources.getString(R.string.watch_ui_syncing_gps)
            SyncState.SYNCING_POIS -> resources.getString(R.string.my_pois)
            SyncState.SYNCING_ROUTES -> resources.getString(R.string.routes)
            SyncState.SYNCING_TREND_DATA, SyncState.SYNCING_SLEEP, SyncState.SYNCING_RECOVERY_DATA ->
                resources.getString(R.string.watch_ui_syncing_247_data)
            SyncState.SYNCING_SUUNTO_PLUS_GUIDES -> resources.getString(R.string.watch_ui_syncing_suunto_plus)
            SyncState.SYNCING_SETTINGS -> resources.getString(R.string.watch_ui_syncing_settings)
            SyncState.SYNCING_WEATHER -> resources.getString(R.string.watch_ui_syncing_weather)
            SyncState.SYNCING_TRAINING_ZONE -> resources.getString(R.string.watch_ui_syncing_training_zone)
            SyncState.SYNCING_ZONE_SENSE -> if (zoneSenseUtils.isZoneSenseEnabled()) resources.getString(R.string.watch_ui_syncing_zone_sense) else ""
            else -> ""
        }
    }

    private fun getLastSyncedDateText(time: Long): String {
        // e.g. "Last synced: just now"
        return (
            resources.getString(R.string.watch_ui_last_synced) +
                ": " +
                TextFormatter.formatRelativeDateSpan(resources, time)
            )
    }

    private fun getSyncFailures(totalFailedMoves: Int, totalFailedRoutes: Int): String {
        // Info about failed moves and routes
        return when {
            totalFailedMoves > 0 && totalFailedRoutes > 0 -> {
                val failedMovesQuantityString =
                    resources.getQuantityString(R.plurals.watch_ui_workout_quantity, totalFailedMoves, totalFailedMoves)
                val failedRoutesQuantityString =
                    resources.getQuantityString(R.plurals.watch_ui_routes_quantity, totalFailedRoutes, totalFailedRoutes)
                // e.g. 2 moves and 1 route did not sync
                htmlParser.stripTags(
                    resources.getString(
                        R.string.watch_ui_syncing_fail_routes_and_workouts,
                        failedMovesQuantityString,
                        failedRoutesQuantityString
                    )
                )
            }
            totalFailedMoves > 0 -> {
                // e.g. 2 moves did not sync
                htmlParser.stripTags(
                    resources.getQuantityString(
                        R.plurals.watch_ui_syncing_fail_workouts,
                        totalFailedMoves,
                        totalFailedMoves
                    )
                )
            }
            totalFailedRoutes > 0 -> {
                // e.g. 1 route did not sync
                htmlParser.stripTags(
                    resources.getQuantityString(
                        R.plurals.watch_ui_syncing_fail_routes,
                        totalFailedRoutes,
                        totalFailedRoutes
                    )
                )
            }
            else -> {
                ""
            }
        }
    }

    fun displaySyncResult(syncResult: SpartanSyncResult): String {
        val totalMoves = newMovesSyncedFromWatchToPhone(syncResult)
        val totalRoutes = /* TODO */ 0

        // "Last synced: just now"
        val lastSyncDateInfoText = getLastSyncedDateText(syncResult.syncEndTimestamp)
        val fullMessage = StringBuilder(lastSyncDateInfoText)

        // Info about total moves and routes
        val movesQuantityString =
            resources.getQuantityString(R.plurals.watch_ui_workout_quantity, totalMoves, totalMoves)
        val routesQuantityString =
            resources.getQuantityString(R.plurals.watch_ui_routes_quantity, totalRoutes, totalRoutes)

        if (totalMoves > 0 && totalRoutes > 0) {
            fullMessage.append('\n')
            fullMessage.append(
                resources.getString(
                    R.string.watch_ui_syncing_routes_and_workouts,
                    movesQuantityString,
                    routesQuantityString
                )
            )
        } else if (totalMoves > 0) {
            fullMessage.append('\n')
            fullMessage.append(movesQuantityString)
        } else if (totalRoutes > 0) {
            fullMessage.append('\n')
            fullMessage.append(routesQuantityString)
        }

        // Failures
        val totalFailedMoves = failedMoveSyncsFromWatchToPhone(syncResult)
        val totalFailedRoutes = /* TODO */ 0

        if (totalFailedMoves > 0 || totalFailedRoutes > 0) {
            fullMessage.append('\n')
            fullMessage.append(getSyncFailures(totalFailedMoves, totalFailedRoutes))
        }

        return fullMessage.toString()
    }

    fun formatPairingTitle(event: PairingStateEvent): String {
        return when (event) {
            is Paired -> {
                val deviceTypeName = resources.getString(WatchHelper.getStringResIdForSuuntoDeviceType(event.deviceType))
                resources.getString(R.string.device_ui_status_paired, deviceTypeName)
            }
            is PairingDone -> {
                val deviceTypeName = resources.getString(WatchHelper.getStringResIdForSuuntoDeviceType(event.deviceType))
                resources.getString(R.string.device_ui_status_paired, deviceTypeName)
            }
            else -> resources.getString(R.string.watch_ui_status_pairing)
        }
    }

    fun formatConnectingInstructionMessage(isDataLayerDevice: Boolean): String {
        return resources.getString(
            if (isDataLayerDevice) {
                R.string.device_ui_status_connecting_message_data_layer_device
            } else {
                R.string.device_ui_status_connecting_message
            }
        )
    }
}
