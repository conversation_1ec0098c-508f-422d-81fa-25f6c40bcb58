package com.stt.android.watch.sportmodes.selectdisplay

import android.net.Uri
import android.view.View
import androidx.navigation.findNavController
import coil3.load
import com.stt.android.R
import com.stt.android.common.ui.ClickableItem
import com.stt.android.data.source.local.sportmodes.SportModesFileStorage.Companion.SPORT_MODE_PATH
import com.stt.android.databinding.ItemSportmodeSelectDisplayBinding
import com.stt.android.domain.sportmodes.Icon
import com.xwray.groupie.databinding.GroupieViewHolder
import io.reactivex.disposables.Disposable
import timber.log.Timber
import java.io.File

data class SportModeSelectDisplayItem(
    private val displayId: String,
    val displayName: String,
    private val displaySelectedDelegate: OnDisplaySelectedDelegate,
    private val displayIcon: Icon
) : ClickableItem<ItemSportmodeSelectDisplayBinding>() {
    var disposable: Disposable? = null

    override fun getLayout() = R.layout.item_sportmode_select_display

    override fun onClick(view: View) {
        disposable = displaySelectedDelegate.onDisplaySelected(displayId)
            .subscribe({
                val navController = view.findNavController()
                navController.popBackStack(R.id.sportModeEditDisplaysFragment, false)
            }, {
                Timber.w(it)
            })
    }

    override fun bind(viewBinding: ItemSportmodeSelectDisplayBinding, position: Int) {
        super.bind(viewBinding, position)
        val context = viewBinding.icon.context
        val uri = Uri.fromFile(File(context.filesDir.absolutePath + SPORT_MODE_PATH + displayIcon.values))
        viewBinding.icon.load(uri)
    }

    override fun unbind(holder: GroupieViewHolder<ItemSportmodeSelectDisplayBinding>) {
        super.unbind(holder)
        disposable?.let {
            if (!it.isDisposed) {
                it.dispose()
            }
        }
    }
}
