package com.stt.android.watch.sportmodes.selectdisplay

import io.reactivex.Completable

/**
 * Listener that fires event whenever display is selected
 */
interface OnDisplaySelectedDelegate {
    /**
     * Notifies view model that we have seleted following display instead of current one
     * @param displayId The ID of the selected display
     */
    fun onDisplaySelected(displayId: String): Completable
}
