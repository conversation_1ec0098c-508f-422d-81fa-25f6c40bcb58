package com.stt.android.watch

import android.content.Context
import android.content.Intent

interface WatchWidgetCustomizationNavigator {
    /**
     * @param finishAnimations - Parameters to be given to overridePendingTransition when the
     *   widget customization Activity finishes. First is resource ID to the re-appearing
     *   Activity's entering animation and second is this widget customization Activity's
     *   exiting animation's resource ID. 0 can be used for no animation.
     */
    fun newWatchWidgetCustomizationIntent(
        context: Context,
        analyticsSource: String,
        finishAnimations: Pair<Int, Int>? = null,
        fromOnboarding: Boolean = false,
    ): Intent
}
