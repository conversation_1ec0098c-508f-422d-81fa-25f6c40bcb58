package com.stt.android.watch.offlinemusic

import com.stt.android.coroutines.await
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.utils.awaitFirstNonNull
import com.stt.android.utils.toV2Flowable
import com.stt.android.watch.MissingCurrentWatchException
import com.stt.android.watch.SuuntoWatchModel
import com.suunto.connectivity.deviceid.SuuntoDeviceCapabilityInfoProvider
import com.suunto.connectivity.offlinemusic.MusicInfo
import com.suunto.connectivity.offlinemusic.PlayList
import com.suunto.connectivity.offlinemusic.PlayListHeader
import com.suunto.connectivity.offlinemusic.PlayListSort
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.reactive.asFlow
import timber.log.Timber
import javax.inject.Inject

class OfflineMusicWatchApi @Inject constructor(
    private val suuntoWatchModel: SuuntoWatchModel
) {
    private fun logWarning(throwable: Throwable, message: String) {
        if (throwable is MissingCurrentWatchException) {
            Timber.i("$message, Missing current watch")
        } else {
            Timber.w(throwable, message)
        }
    }

    suspend fun fetchOfflineAllPlayLists(): List<PlayListHeader> =
        runSuspendCatching {
            suuntoWatchModel.currentWatch
                .flatMap {
                    it.suuntoRepositoryClient
                        .offlineMusicQueryConsumer
                        .getOfflineAllPlayList(it.suuntoBtDevice.macAddress)
                }.await()
        }.getOrElse {
            logWarning(it, "Can't fetch allPlayList.")
            emptyList()
        }

    suspend fun fetchOfflineAllSongPlayList(): PlayList? =
        runSuspendCatching {
            suuntoWatchModel.currentWatch
                .flatMap {
                    it.suuntoRepositoryClient
                        .offlineMusicQueryConsumer
                        .getAllSongPlayList(it.suuntoBtDevice.macAddress)
                }.await()
        }.getOrElse {
            logWarning(it, "Can't fetch allSongPlayList.")
            null
        }

    suspend fun fetchOfflinePlayListDetail(playListId: Long): PlayList? =
        runSuspendCatching {
            suuntoWatchModel.currentWatch
                .flatMap {
                    it.suuntoRepositoryClient
                        .offlineMusicQueryConsumer
                        .getPlayListDetail(it.suuntoBtDevice.macAddress, playListId)
                }.await()
        }.getOrElse {
            logWarning(it, "Can't fetch playList detail:$playListId")
            null
        }

    suspend fun fetchOfflineMusicInfo(musicKey: Long): MusicInfo? =
        runSuspendCatching {
            suuntoWatchModel.currentWatch
                .flatMap {
                    it.suuntoRepositoryClient
                        .offlineMusicQueryConsumer
                        .getMusicInfo(it.suuntoBtDevice.macAddress, musicKey)
                }.await()
        }.getOrElse {
            logWarning(it, "Can't fetch musicInfo: $musicKey")
            null
        }

    suspend fun deleteOfflinePlayList(playListId: Long): Boolean =
        runSuspendCatching {
            suuntoWatchModel.currentWatch
                .flatMap {
                    it.suuntoRepositoryClient
                        .offlineMusicQueryConsumer
                        .deletePlayList(it.suuntoBtDevice.macAddress, playListId)
                }.await()
        }.getOrElse {
            logWarning(it, "Can't delete playList: $playListId")
            false
        }

    suspend fun createOfflinePlayList(playList: PlayList): Boolean =
        runSuspendCatching {
            suuntoWatchModel.currentWatch
                .flatMap {
                    it.suuntoRepositoryClient
                        .offlineMusicQueryConsumer
                        .createPlayList(it.suuntoBtDevice.macAddress, playList)
                }.await()
        }.getOrElse {
            logWarning(it, "Can't create playList: $playList")
            false
        }

    suspend fun updateOfflinePlayList(playList: PlayList): Boolean =
        runSuspendCatching {
            suuntoWatchModel.currentWatch
                .flatMap {
                    it.suuntoRepositoryClient
                        .offlineMusicQueryConsumer
                        .updatePlayList(it.suuntoBtDevice.macAddress, playList)
                }.await()
        }.getOrElse {
            logWarning(it, "Can't update playList: $playList")
            false
        }

    suspend fun sortOfflinePlayLists(sorts: List<PlayListSort>): Boolean =
        runSuspendCatching {
            suuntoWatchModel.currentWatch
                .flatMap {
                    it.suuntoRepositoryClient
                        .offlineMusicQueryConsumer
                        .sortPlayLists(it.suuntoBtDevice.macAddress, sorts)
                }.await()
        }.getOrElse {
            logWarning(it, "Can't sort playLists: $sorts")
            false
        }

    suspend fun subscribeOfflineMusicUpdate(): Flow<Boolean> =
        suuntoWatchModel.currentWatch
            .flatMapObservable {
                it.suuntoRepositoryClient
                    .offlineMusicQueryConsumer
                    .observeMusicUpdate(it.suuntoBtDevice.macAddress)
            }.toV2Flowable().asFlow()

    suspend fun supportsOfflineMusic(): Boolean = runSuspendCatching {
        val spartan = suuntoWatchModel.currentWatch()
        val watchState = spartan.stateChangeObservable.awaitFirstNonNull()
        val type = spartan.suuntoBtDevice.deviceType
        SuuntoDeviceCapabilityInfoProvider[type].supportsOfflineMusic(watchState?.deviceInfo?.capabilities)
    }.getOrElse {
        logWarning(it, "Can't check if support offline music.")
        false
    }

    suspend fun getOfflineMusicVersion(): Int =
        runSuspendCatching {
            suuntoWatchModel.currentWatch
                .flatMap {
                    it.suuntoRepositoryClient
                        .offlineMusicQueryConsumer
                        .getOfflineMusicVersion(it.suuntoBtDevice.macAddress)
                }.await()
        }.getOrElse {
            logWarning(it, "Can't get offline music version.")
            0
        }

}
