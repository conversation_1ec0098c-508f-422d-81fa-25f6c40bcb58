package com.stt.android.watch.sportmodes.create.multisport.composable

import androidx.compose.animation.core.animateDpAsState
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Icon
import androidx.compose.material3.Checkbox
import androidx.compose.material3.CheckboxDefaults
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.TextField
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.rememberUpdatedState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalHapticFeedback
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.zIndex
import com.stt.android.R
import com.stt.android.compose.component.SuuntoActivityIcon
import com.stt.android.compose.modifiers.clickableThrottleFirst
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.dividerColor
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.material3.body
import com.stt.android.compose.theme.material3.bodyBold
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.util.DraggableItem
import com.stt.android.compose.util.dragContainer
import com.stt.android.compose.util.rememberDragDropState
import com.stt.android.compose.widgets.LoadingContent
import com.stt.android.domain.workout.ActivityType
import androidx.compose.ui.hapticfeedback.HapticFeedbackType
import com.stt.android.compose.theme.mediumGrey
import com.stt.android.watch.sportmodes.create.multisport.MultisportChild
import com.stt.android.watch.sportmodes.create.multisport.MultisportModeEditIntent
import com.stt.android.watch.sportmodes.create.multisport.MultisportModeEditViewState
import com.stt.android.core.R as CR

@Composable
internal fun ConfirmContent(
    state: MultisportModeEditViewState.Confirm,
    onIntent: (MultisportModeEditIntent) -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
) {
    Box(modifier = modifier.fillMaxSize()) {
        Column(
            modifier = Modifier
                .verticalScroll(rememberScrollState())
                .fillMaxSize()
        ) {
            TextField(
                value = state.name,
                onValueChange = {
                    onIntent(MultisportModeEditIntent.SetName(it))
                },
                label = { Text(text = stringResource(R.string.sport_modes_name_your_sport_mode)) },
                colors = TextFieldDefaults.colors().copy(
                    focusedContainerColor = Color.Transparent,
                    unfocusedContainerColor = Color.Transparent,
                    errorContainerColor = Color.Transparent,
                    focusedIndicatorColor = MaterialTheme.colorScheme.primary,
                    unfocusedIndicatorColor = MaterialTheme.colorScheme.dividerColor,
                    focusedLabelColor = MaterialTheme.colorScheme.secondary,
                    unfocusedLabelColor = MaterialTheme.colorScheme.secondary,
                ),
                modifier = Modifier.fillMaxWidth(),
                isError = state.nameErrorTips != 0,
                singleLine = true,
            )

            state.nameErrorTips.takeIf { it != 0 }?.let {
                Text(
                    stringResource(it),
                    color = MaterialTheme.colorScheme.error,
                    style = MaterialTheme.typography.bodySmall,
                    modifier = Modifier.padding(
                        top = MaterialTheme.spacing.small,
                        bottom = MaterialTheme.spacing.medium,
                        start = MaterialTheme.spacing.medium,
                    ),
                )
            }

            ChildrenActivityPart(
                multisportChildren = state.selectedChildren.filterNot { it.isTransition },
                editing = state.editing,
                onIntent = onIntent,
                modifier = Modifier.padding(top = MaterialTheme.spacing.medium),
                enabled = enabled,
            )

            HorizontalDivider(thickness = MaterialTheme.spacing.small)

            TransitionsPart(
                selected = state.enableTransitions,
                onIntent = onIntent,
                enabled = state.canEnableTransitions,
            )
        }

        if (state.loading) {
            LoadingContent(
                isLoading = true,
                color = MaterialTheme.colorScheme.primary,
            )
        }
    }
}

@Composable
private fun ChildrenActivityPart(
    multisportChildren: List<MultisportChild>,
    editing: Boolean,
    onIntent: (MultisportModeEditIntent) -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
) {
    val listState = rememberLazyListState()
    val haptic = LocalHapticFeedback.current
    val currentEditing by rememberUpdatedState(editing)
    val currentMultisportChildren by rememberUpdatedState(multisportChildren)

    val dragDropState = rememberDragDropState(
        lazyListState = listState,
        canMove = { index ->
            currentEditing && index < currentMultisportChildren.size
        },
        onMove = { fromIndex, toIndex ->
            onIntent(MultisportModeEditIntent.ReorderChildren(fromIndex, toIndex))
        }
    )

    Column(modifier = modifier.fillMaxWidth()) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(
                    start = MaterialTheme.spacing.medium,
                    end = MaterialTheme.spacing.xsmall,
                ),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = stringResource(R.string.list).uppercase(),
                style = MaterialTheme.typography.bodyBold,
                color = MaterialTheme.colorScheme.onSurface,
            )

            TextButton(
                onClick = { onIntent(MultisportModeEditIntent.ToggleEditing) }
            ) {
                Text(
                    text = stringResource(id = if (editing) R.string.save else R.string.edit).uppercase(),
                    style = MaterialTheme.typography.bodyBold,
                    color = MaterialTheme.colorScheme.primary,
                )
            }
        }

        HorizontalDivider()

        LazyColumn(
            state = listState,
            modifier = Modifier
                .fillMaxWidth()
                .height(57.dp.times(multisportChildren.size))
                .then(
                    if (editing) {
                        Modifier.dragContainer(
                            dragDropState = dragDropState,
                            onDragStarted = {
                                haptic.performHapticFeedback(HapticFeedbackType.LongPress)
                            },
                            onDragInterrupted = {}
                        )
                    } else {
                        Modifier
                    }
                )
        ) {
            itemsIndexed(multisportChildren, key = { _, item -> item.hashCode() }) { index, item ->
                DraggableItem(
                    dragDropState = dragDropState,
                    index = index
                ) { isDragging ->
                    val elevation = animateDpAsState(
                        targetValue = if (isDragging) 8.dp else 0.dp,
                        label = "drag_elevation"
                    )

                    Surface(
                        modifier = Modifier.zIndex(if (isDragging) 1f else 0f),
                        shadowElevation = elevation.value,
                        color = Color.Transparent
                    ) {
                        EditableMultisportChildItem(
                            multisportChild = item,
                            onIntent = onIntent,
                            editing = editing,
                            isDragging = isDragging,
                            enabled = enabled,
                        )
                    }
                }

                HorizontalDivider()
            }
        }
    }
}

@Composable
private fun EditableMultisportChildItem(
    multisportChild: MultisportChild,
    onIntent: (MultisportModeEditIntent) -> Unit,
    editing: Boolean,
    modifier: Modifier = Modifier,
    isDragging: Boolean = false,
    enabled: Boolean = true,
) {
    val contentColor = MaterialTheme.colorScheme.onSurface
    val containerColor =
        if (isDragging) MaterialTheme.colorScheme.secondaryContainer else MaterialTheme.colorScheme.surface
    val resources = LocalContext.current.resources
    val haptic = LocalHapticFeedback.current

    if (isDragging) {
        LaunchedEffect(Unit) {
            haptic.performHapticFeedback(HapticFeedbackType.LongPress)
        }
    }

    Row(
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.medium),
        modifier = modifier
            .fillMaxWidth()
            .background(
                containerColor.copy(
                    alpha = if (enabled) 1f else 0.5f
                )
            )
            .then(
                if (!editing || isDragging) {
                    Modifier
                } else {
                    Modifier.clickableThrottleFirst(enabled = enabled) {
                        onIntent(MultisportModeEditIntent.SelectChild(multisportChild))
                    }
                }
            )
            .padding(MaterialTheme.spacing.medium),
    ) {
        if (editing) {
            Icon(
                painter = painterResource(id = R.drawable.ic_drag_and_drop),
                contentDescription = null,
                tint = Color.Unspecified,
                modifier = Modifier.size(MaterialTheme.iconSizes.small),
            )
        }

        multisportChild.activityType?.let { activityType ->
            SuuntoActivityIcon(
                activityTypeId = activityType.id,
                iconSize = MaterialTheme.iconSizes.small,
            )
        }

        Text(
            text = multisportChild.getLocalizedName(resources),
            style = MaterialTheme.typography.bodyLarge,
            color = contentColor,
            modifier = Modifier.weight(1f),
        )

        if (editing) {
            Icon(
                painter = painterResource(id = CR.drawable.ic_chevron_right_small_outline),
                contentDescription = null,
                tint = contentColor,
                modifier = Modifier.size(MaterialTheme.iconSizes.small),
            )
        }
    }
}

@Composable
private fun TransitionsPart(
    selected: Boolean,
    onIntent: (MultisportModeEditIntent) -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
) {
    Column(modifier = modifier.fillMaxWidth()) {

        Text(
            text = stringResource(R.string.enable_transitions_title).uppercase(),
            style = MaterialTheme.typography.bodyBold,
            color = MaterialTheme.colorScheme.onSurface,
            modifier = Modifier.padding(MaterialTheme.spacing.medium),
        )

        HorizontalDivider()

        Text(
            text = stringResource(R.string.max_3_sports),
            style = MaterialTheme.typography.bodyLarge,
            color = MaterialTheme.colorScheme.onSurface,
            modifier = Modifier.padding(MaterialTheme.spacing.medium),
        )

        Row(
            modifier = Modifier
                .fillMaxWidth()
                .alpha(if (enabled) 1f else 0.5f)
                .clickableThrottleFirst(enabled = enabled) {
                    onIntent(
                        MultisportModeEditIntent.SetEnableTransitions(
                            !selected
                        )
                    )
                }
                .padding(MaterialTheme.spacing.medium),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Column(
                verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.xsmall),
                modifier = Modifier.weight(1f),
            ) {
                Text(
                    text = stringResource(R.string.enable_transitions_title),
                    style = MaterialTheme.typography.bodyLarge,
                    color = MaterialTheme.colorScheme.onSurface,
                )
                Text(
                    text = stringResource(R.string.enable_transitions_tips),
                    style = MaterialTheme.typography.body,
                    color = MaterialTheme.colorScheme.secondary,
                )
            }

            Checkbox(
                checked = selected,
                onCheckedChange = null,
                colors = CheckboxDefaults.colors(
                    uncheckedColor = MaterialTheme.colorScheme.mediumGrey,
                ),
            )
        }

        HorizontalDivider()
    }
}

@Preview
@Composable
private fun ConfirmContentPreview() {
    M3AppTheme {
        ConfirmContent(
            state = MultisportModeEditViewState.Confirm(
                parentActivityType = ActivityType.MULTISPORT,
                selectedChildren = previewChildren.take(4),
                name = "My custom Swimrun mode",
                enableTransitions = true,
                loading = true,
                editing = true,
                nameErrorTips = R.string.sport_mode_name_long_error,
            ),
            onIntent = {},
        )
    }
}
