package com.stt.android.watch.forcedupdate

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.compose.setContent
import androidx.appcompat.app.AppCompatActivity
import com.stt.android.compose.theme.AppTheme
import com.stt.android.watch.forcedupdate.ui.ForcedUpdateScreen
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoDeviceType

class ForcedUpdateInfoActivity : AppCompatActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        val displayName = intent.getStringExtra(EXTRA_DISPLAY_NAME) ?: ""
        setContent {
            AppTheme {
                ForcedUpdateScreen(displayName = displayName, ::finish)
            }
        }
    }

    companion object {
        const val EXTRA_DISPLAY_NAME = "displayName"
        @JvmStatic
        fun newStartIntent(context: Context, deviceType: SuuntoDeviceType): Intent {
            return Intent(context, ForcedUpdateInfoActivity::class.java).apply {
                putExtra(EXTRA_DISPLAY_NAME, deviceType.displayName)
            }
        }
    }
}
