package com.stt.android.watch

import androidx.lifecycle.MutableLiveData
import com.stt.android.common.ui.RxViewModel
import com.stt.android.domain.di.IoThread
import com.stt.android.domain.di.MainThread
import io.reactivex.Scheduler

/**
 * DeviceViewModel is an abstract base ViewModel class which
 * provides the shared DeviceHolderViewModel instance
 */
abstract class DeviceViewModel
constructor(
    @IoThread ioThread: Scheduler,
    @MainThread mainThread: Scheduler
) : RxViewModel(ioThread, mainThread) {

    /**
     * Info on how to render the currently connected device image
     */
    val deviceImageInfo = MutableLiveData<DeviceImageInfo>().apply { value = DeviceImageInfo() }

    /**
     * Use a backing property for sharedViewModel to make sure the
     * onSharedViewModelInitialized is called when the sharedViewModel is
     * initialized. Backing property is required because lateinit doesn't allow
     * custom setters.
     */
    private var _sharedViewModel: DeviceHolderViewModel? = null
    var sharedViewModel: DeviceHolderViewModel
        get() = _sharedViewModel ?: throw UninitializedPropertyAccessException()
        set(value) {
            _sharedViewModel = value
            onSharedViewModelInitialized()
        }

    /**
     * Should connection alert be shown in UI?
     */
    fun connectionAlertAvailable(): MutableLiveData<Boolean> {
        return sharedViewModel.connectionAlertAvailable
    }

    /**
     * onSharedViewModelInitialized is called after the sharedViewModel is set.
     *
     * Once this method is called, it is guaranteed that sharedViewModel exists.
     */
    private fun onSharedViewModelInitialized() {
        disposables.add(
            sharedViewModel.deviceStateRelay
                .observeOn(mainThread)
                .subscribe { event ->
                    val imageInfo = when (event) {
                        is DeviceStateUpdate -> {
                            onDeviceStateUpdate(event)
                            if (event.registered) {
                                val imageResource = WatchHelper.getDrawableResIdForSuuntoDeviceType(
                                    event.deviceType,
                                    event.sku
                                )
                                DeviceImageInfo(imageResource, true, event.deviceType?.isEonComputer == true)
                            } else {
                                DeviceImageInfo()
                            }
                        }
                        else -> DeviceImageInfo()
                    }
                    deviceImageInfo.value = imageInfo
                }
        )
    }

    /**
     * onDeviceStateUpdate is called when device state is updated. The current
     * device state is provided as a parameter.
     *
     * @param state Updated device state
     */
    protected open fun onDeviceStateUpdate(state: DeviceStateUpdate) {
    }
}
