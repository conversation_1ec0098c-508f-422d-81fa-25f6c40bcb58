package com.stt.android.watch.sportmodes.editdisplays

import android.view.View
import android.widget.LinearLayout
import androidx.compose.animation.core.animateDpAsState
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material.ContentAlpha
import androidx.compose.material.Icon
import androidx.compose.material.LocalContentAlpha
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.material3.Divider
import androidx.compose.material3.ListItem
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.hapticfeedback.HapticFeedbackType
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.LocalHapticFeedback
import androidx.compose.ui.platform.LocalView
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.navigation.findNavController
import com.stt.android.R
import com.stt.android.common.ui.BaseBindableItem
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.body
import com.stt.android.compose.theme.dividerColor
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.util.DraggableItem
import com.stt.android.compose.util.dragContainer
import com.stt.android.compose.util.rememberDragDropState
import com.stt.android.compose.util.setContentWithTheme
import com.stt.android.databinding.ItemEditSportmodeFieldsBinding
import com.stt.android.domain.sportmodes.Field
import kotlinx.collections.immutable.persistentListOf

data class SportModeEditFields(
    val displayId: String,
    val displayIndex: Int,
    val isReadOnly: Boolean,
    val sportModeDisplayChangeDelegate: SportModeDisplayChangeListener,
    private val fields: List<Field>,
) : BaseBindableItem<ItemEditSportmodeFieldsBinding>() {
    override fun getLayout(): Int = R.layout.item_edit_sportmode_fields

    override fun bind(viewBinding: ItemEditSportmodeFieldsBinding, position: Int) {
        super.bind(viewBinding, position)
        viewBinding.fieldsContainer.run {
            removeAllViews()
            fields.forEachIndexed { index, field ->
                addView(createFieldItemView(index, field))
            }
        }
    }

    private fun LinearLayout.createFieldItemView(
        index: Int,
        field: Field
    ): View {
        return ComposeView(
            context = context,
        ).apply {
            layoutParams = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.WRAP_CONTENT,
            )
            setContentWithTheme {
                val localView = LocalView.current
                val fieldName = "${index + 1}. ${field.name}"
                val onEditClick: (fieldId: String, fieldIndex: Int, subFieldsIds: List<Field>?) -> Unit =
                    { fieldId, fieldIndex, subFieldsIds ->
                        sportModeDisplayChangeDelegate.onChangeDisplayLayoutOrFieldClicked()
                        val action =
                            SportModeEditDisplaysFragmentDirections.sportModeFieldListAction()
                        action.setFieldId(fieldId)
                        action.setFieldIndex(fieldIndex)
                        action.setDisplayId(displayId)
                        action.setDisplayIndex(displayIndex)
                        action.setSelectedSubFields(subFieldsIds?.map { it.id }
                            ?.toTypedArray())
                        localView.findNavController().navigate(action)
                    }
                CompositionLocalProvider(LocalContentAlpha provides if (isReadOnly) ContentAlpha.disabled else ContentAlpha.high) {
                    if (!field.isMultiField) {
                        SingleField(
                            fieldName = fieldName,
                            onEditClick = {
                                onEditClick(field.id, index, null)
                            },
                            isReadOnly = isReadOnly
                        )
                    } else {
                        MultiFieldContainer(
                            multiFieldName = fieldName,
                            onEditClick = {
                                onEditClick(field.id, index, field.subFields)
                            },
                            subFields = field.subFields,
                            isReadOnly = isReadOnly
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun MultiFieldContainer(
    multiFieldName: String,
    onEditClick: () -> Unit,
    isReadOnly: Boolean,
    modifier: Modifier = Modifier,
    subFields: List<Field>? = null,
) {
    require(subFields != null) { "Sub field is null in a multi fields field" }
    val haptic = LocalHapticFeedback.current
    var currentSubFields by remember {
        mutableStateOf(subFields)
    }
    val listState = rememberLazyListState()
    val dragDropState = rememberDragDropState(
        lazyListState = listState,
        canMove = { !isReadOnly },
        canMoveOver = { true },
        onMove = { fromIndex: Int, toIndex: Int ->
            if (fromIndex !in 0..currentSubFields.size || toIndex !in 0..currentSubFields.size) return@rememberDragDropState

            currentSubFields = currentSubFields.toMutableList().apply {
                add(toIndex, removeAt(fromIndex))
            }
        },
    )
    Column(
        modifier = modifier
            .fillMaxWidth()
    ) {
        Field(
            name = multiFieldName,
            onEditClick = onEditClick,
            isReadOnly = isReadOnly
        )
        Divider(thickness = 1.dp, color = MaterialTheme.colors.dividerColor)
        LazyColumn(
            state = listState,
            modifier = Modifier
                .fillMaxWidth()
                .height(57.dp.times(currentSubFields.size))
                .dragContainer(dragDropState)
        ) {
            itemsIndexed(currentSubFields, { _, item -> item.id }) { index, item ->
                DraggableItem(dragDropState = dragDropState, index = index) { isDragging ->
                    LaunchedEffect(isDragging) {
                        haptic.performHapticFeedback(HapticFeedbackType.LongPress)
                    }
                    val elevation = animateDpAsState(
                        targetValue = if (isDragging) 8.dp else 0.dp,
                        label = "drag"
                    )
                    SubFieldItem(
                        text = item.name,
                        elevation = elevation.value
                    )
                }
                Divider(thickness = 1.dp, color = MaterialTheme.colors.dividerColor)
            }
        }
    }
}

@Composable
private fun Field(
    name: String,
    onEditClick: () -> Unit,
    isReadOnly: Boolean,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .fillMaxWidth()
            .height(50.dp)
            .background(Color.White)
            .clickable(enabled = !isReadOnly) { onEditClick() }
            .padding(
                start = MaterialTheme.spacing.medium,
                end = MaterialTheme.spacing.xsmall,
            ),
        contentAlignment = Alignment.CenterStart
    ) {
        Row(verticalAlignment = Alignment.CenterVertically) {
            Text(text = name, style = MaterialTheme.typography.body)
            Spacer(modifier = Modifier.weight(1f))
            Icon(
                painter = painterResource(id = R.drawable.chevron_right),
                contentDescription = null
            )
        }
    }
}

@Composable
private fun SingleField(
    fieldName: String,
    onEditClick: () -> Unit,
    isReadOnly: Boolean,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
    ) {
        Field(
            name = fieldName,
            onEditClick = onEditClick,
            isReadOnly = isReadOnly
        )
        Divider(thickness = 1.dp, color = MaterialTheme.colors.dividerColor)
    }
}

@Composable
private fun SubFieldItem(
    text: String,
    modifier: Modifier = Modifier,
    elevation: Dp = 20.dp
) {
    ListItem(
        headlineContent = {
            Row(verticalAlignment = Alignment.CenterVertically) {
                Icon(
                    painter = painterResource(id = R.drawable.drag_and_drop_outline),
                    contentDescription = null
                )
                Text(
                    text = text,
                    style = MaterialTheme.typography.body,
                    modifier = Modifier.padding(horizontal = MaterialTheme.spacing.small)
                )
            }
        },
        modifier = modifier
            .fillMaxWidth()
            .background(Color.White),
        tonalElevation = elevation,
        shadowElevation = elevation
    )
}

@Preview(showBackground = true)
@Composable
private fun BodyPreview() {
    AppTheme {
        Column {
            persistentListOf(
                Field(
                    id = "TEXT_FIELD_1", name = "Field 1", subFields = listOf(
                        Field("sub1", "Sub 1", null),
                        Field("sub2", "Sub 2", null),
                    )
                ), Field(
                    id = "TEXT_FIELD_2", name = "Field 2", subFields = listOf()
                )
            ).forEach { field ->
                if (field.isMultiField) {
                    MultiFieldContainer(
                        multiFieldName = field.name,
                        onEditClick = {},
                        subFields = field.subFields,
                        isReadOnly = false,
                    )
                } else {
                    SingleField(
                        fieldName = field.name,
                        onEditClick = {},
                        isReadOnly = false,
                    )
                }
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun MultiFieldContainerPreview() {
    AppTheme {
        MultiFieldContainer(
            subFields = listOf(
                Field(
                    id = "TEXT_FIELD_1", name = "Field 1", subFields = listOf()
                ), Field(
                    id = "TEXT_FIELD_2", name = "Field 2", subFields = listOf()
                )
            ),
            multiFieldName = "1. Switchable field",
            onEditClick = {},
            isReadOnly = true
        )
    }
}
