package com.stt.android.watch

import com.stt.android.domain.device.ConnectedWatchConnectionState
import com.stt.android.domain.device.ConnectedWatchState
import com.stt.android.domain.device.SyncState
import com.stt.android.watch.deviceswitch.DeviceSwitchContainer
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoDeviceType
import com.suunto.connectivity.util.isSyncSuccess
import com.suunto.connectivity.watch.SpartanSyncResult

/**
 * DeviceStateEvent depicts a state change event.
 */
sealed class DeviceStateEvent

/**
 * DeviceStateLoading implies that all required underlying state information is
 * not yet loaded or available
 */
object DeviceStateLoading : DeviceStateEvent()

/**
 * DeviceStateUpdate is a state event which means that device state has updated
 * and all required state information is loaded and available
 */
data class DeviceStateUpdate(
    val permissions: DevicePermissions,
    val deviceType: SuuntoDeviceType?,
    val watchState: ConnectedWatchState?,
    val syncResult: SpartanSyncResult?,
    val sku: String,
    val otherDevices: DeviceSwitchContainer = DeviceSwitchContainer(),
    val switching: Boolean
) : DeviceStateEvent() {

    val busy = watchState?.isBusy == true
    val registered = watchState?.isRegistered == true
    val pairing = !registered && watchState?.connectedWatchConnectionState == ConnectedWatchConnectionState.CONNECTING
    val syncing = registered && watchState?.connectedWatchSyncState?.state != SyncState.NOT_SYNCING
    val syncSuccess = syncResult != null && isSyncSuccess(syncResult)
    val firmwareUpdateAvailable = watchState?.isFWUpdateAvailable ?: false
    val firmwareUpdateRequired = watchState?.isFWUpdateRequired == true

    // For registered devices, bluetooth permissions are enough to show the device views.
    val permissionsRequired = (!registered && !permissions.allPermissionsAvailable) ||
        (registered && !permissions.bluetoothPermissionsAvailable)
}

data class DevicePermissions(
    val locationEnabled: Boolean,
    val bluetoothEnabled: Boolean,
    val locationAllowed: Boolean,
    val nearbyDevicesAllowed: Boolean,
) {
    val allPermissionsAvailable = locationEnabled && bluetoothEnabled && locationAllowed && nearbyDevicesAllowed
    val bluetoothPermissionsAvailable = bluetoothEnabled && nearbyDevicesAllowed
}
