package com.stt.android.watch

import com.stt.android.domain.routes.GetRoutesUseCase
import javax.inject.Inject

class IsWatchRouteListFullUseCase @Inject constructor(
    private val getRoutesUseCase: GetRoutesUseCase,
) {
    suspend fun isWatchRouteListFull(): Boolean =
        getRoutesUseCase.getWatchEnabledRouteCount() >= MAXIMUM_ROUTES_IN_WATCH

    companion object {
        // We don't know the actual size of the watch's route list, so we use a hardcoded value
        // As of 10.8.22 this is also how iOS does this (maximumRoutesInWatch in RouteService.swift)
        private const val MAXIMUM_ROUTES_IN_WATCH = 15
    }
}
