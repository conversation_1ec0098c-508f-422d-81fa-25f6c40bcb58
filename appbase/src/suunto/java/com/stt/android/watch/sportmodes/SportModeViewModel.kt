package com.stt.android.watch.sportmodes

import com.stt.android.R
import com.stt.android.analytics.AnalyticsEventProperty
import com.stt.android.analytics.AnalyticsProperties
import com.stt.android.common.ui.EditableViewModel
import com.stt.android.common.ui.ErrorEvent
import com.stt.android.domain.di.IoThread
import com.stt.android.domain.di.MainThread
import com.stt.android.watch.sportmodes.errors.SaveFailedException
import com.suunto.connectivity.suuntoconnectivity.device.AnalyticsDevicePropertyHelper
import io.reactivex.Scheduler
import kotlin.reflect.KClass

abstract class SportModeViewModel(
    @IoThread ioThread: Scheduler,
    @MainThread mainThread: Scheduler
) : EditableViewModel(ioThread, mainThread) {
    lateinit var sharedViewModel: SportModeHolderViewModel

    init {
        val errors = mapOf<KClass<out Exception>, ErrorEvent>(
            SaveFailedException::class to ErrorEvent(true, R.string.sport_mode_error_saving, true)
        )
        ErrorEvent.registerErrorEvents(errors)
    }

    fun defaultWatchProperties(): AnalyticsProperties {
        return AnalyticsProperties()
            .put(
                AnalyticsEventProperty.SUUNTO_WATCH_MODEL,
                AnalyticsDevicePropertyHelper.getWatchModelNameForSuuntoDeviceType(sharedViewModel.suuntoDeviceType)
            )
            .put(
                AnalyticsEventProperty.SUUNTO_WATCH_FIRMWARE_VERSION,
                sharedViewModel.watchFirmware
            )
    }

    fun defaultWatchPropertiesWithSerial(): AnalyticsProperties {
        return defaultWatchProperties()
            .put(AnalyticsEventProperty.SUUNTO_WATCH_SERIAL_NUMBER, sharedViewModel.watchSerialNumber)
    }
}
