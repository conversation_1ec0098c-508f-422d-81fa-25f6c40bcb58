package com.stt.android.watch.preference

import android.content.SharedPreferences
import androidx.core.content.edit
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.UserSettingsController
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.activitydata.goals.SetEnergyGoalUseCase
import com.stt.android.domain.activitydata.goals.SetSleepGoalUseCase
import com.stt.android.domain.activitydata.goals.SetStepsGoalUseCase
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.domain.user.Sex
import com.stt.android.home.settings.userprofile.UserProfileUseCase
import com.stt.android.ui.activities.settings.helper.PredefinedRepliesHelper
import com.stt.android.ui.activities.settings.watch.notifications.domain.SetNotificationsCategoryEnabledUseCase
import com.stt.android.ui.activities.settings.watch.notifications.domain.SetNotificationsEnabledUseCase
import com.stt.android.utils.STTConstants
import com.stt.android.watch.MissingCurrentWatchException
import com.stt.android.watch.SuuntoWatchModel
import com.suunto.algorithms.data.Energy.Companion.kcal
import com.suunto.connectivity.notifications.NotificationsSettings
import com.suunto.connectivity.settings.UnitSystem
import com.suunto.connectivity.settings.WearDirection
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.time.DayOfWeek
import java.time.Instant
import java.time.ZoneId
import java.util.Locale
import javax.inject.Inject
import kotlin.time.Duration.Companion.seconds

class SetupPreferenceSaveUseCase @Inject constructor(
    private val predefinedRepliesHelper: PredefinedRepliesHelper,
    private val setNotificationsEnabledUseCase: SetNotificationsEnabledUseCase,
    private val setNotificationsCategoryEnabledUseCase: SetNotificationsCategoryEnabledUseCase,
    private val sharedPreferences: SharedPreferences,
    private val userSettingsController: UserSettingsController,
    private val setStepsGoalUseCase: SetStepsGoalUseCase,
    private val setEnergyGoalUseCase: SetEnergyGoalUseCase,
    private val setSleepGoalUseCase: SetSleepGoalUseCase,
    private val suuntoWatchModel: SuuntoWatchModel,
    private val userProfileUseCase: UserProfileUseCase,
    private val coroutinesDispatchers: CoroutinesDispatchers,
) {
    suspend fun saveSetupPreference(setupPreference: SetupPreference) {
        withContext(coroutinesDispatchers.io) {
            with(setupPreference) {
                saveNotifications()
                saveDirection()
                savePersonalInfo()
                saveGoals()
            }
        }
    }

    private suspend fun SetupPreference.saveNotifications() {
        with(notifications) {
            setNotificationsEnabledUseCase.run(enable)
            setNotificationsCategoryEnabledUseCase.run(
                call = callEnable,
                sms = msgEnable,
                application = appEnable
            )
            predefinedRepliesHelper.predefinedReplies = predefinedReplies
            appNotifications.forEach { item ->
                suuntoWatchModel.getNotificationSettings()?.let {
                    setNotificationsEnabledForApp(
                        it,
                        item.packageName,
                        item.enable
                    )
                }
            }
            Timber.d("notification settings saved")
        }
    }

    private suspend fun SetupPreference.saveDirection() {
        sharedPreferences.edit {
            putString(
                STTConstants.SuuntoPreferences.KEY_WATCH_WEARING_DIRECTION,
                direction.name
            )
        }
        val saved = suuntoWatchModel.setDeviceWearDirection(
            if (direction == PreferenceDirection.LEFT) {
                WearDirection.LEFT
            } else {
                WearDirection.RIGHT
            }
        )
        if (saved) {
            Timber.d("wearing direction saved")
        }
    }

    private suspend fun SetupPreference.savePersonalInfo() {
        with(personalInfo) {
            val setting = userSettingsController.settings
            runSuspendCatching {
                userSettingsController.storeSettings(
                    setting.setUserProfile(
                        height.toInt() / 100f,
                        weight.toInt(),
                        Sex.valueOf(gender.uppercase(Locale.US)),
                        birthYear.toLong(),
                        setting.hrMaximum,
                        setting.hrRest
                    ).setMeasurementUnit(
                        MeasurementUnit.valueOf(measureUnit.uppercase(Locale.US))
                    ).setFirstDayOfTheWeek(
                        DayOfWeek.valueOf(startOfWeek.uppercase(Locale.US))
                    )
                )
                userProfileUseCase.updateUserHeight(height.toInt())
                userProfileUseCase.updateUserWeight(weight.toInt() / 1000f)
                userProfileUseCase.updateUserGender(gender)
                val birthYear = Instant.ofEpochMilli(birthYear.toLong())
                    .atZone(ZoneId.systemDefault())
                    .toLocalDate()
                    .year
                userProfileUseCase.updateUserBirthYear(birthYear)
                userProfileUseCase.updateUserUnitSystem(
                    UnitSystem.valueOf(measureUnit.uppercase(Locale.US))
                )
                Timber.d("personal info saved")
            }.onFailure {
                if (it is MissingCurrentWatchException) {
                    Timber.i("Missing current watch")
                } else {
                    Timber.w(it, "save personal info error")
                }
            }
        }
    }

    private suspend fun SetupPreference.saveGoals() {
        with(goalSetting) {
            sharedPreferences.edit {
                putInt(
                    STTConstants.SuuntoPreferences.KEY_SUUNTO_WATCH_WEEKLY_TARGET_DURATION,
                    weeklyTraining
                )
            }
            runSuspendCatching {
                suuntoWatchModel.setWeeklyTargetDuration(weeklyTraining.toFloat())
                setStepsGoalUseCase.setStepsGoal(dailySteps)
                setEnergyGoalUseCase.setEnergyGoal(dailyKcal.kcal)
                setSleepGoalUseCase.setSleepGoal(dailySleep.seconds)
                Timber.d("goal settings saved")
            }.onFailure {
                Timber.w(it, "save goal settings error")
            }
        }
    }

    private fun setNotificationsEnabledForApp(
        notificationSettings: NotificationsSettings,
        packageName: String,
        enabled: Boolean
    ) {
        if (enabled) {
            notificationSettings.enablePackage(packageName).await()
        } else {
            notificationSettings.disablePackage(packageName).await()
        }
    }
}
