package com.stt.android.watch.sportmodes

import androidx.annotation.WorkerThread
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import com.jakewharton.rxrelay2.PublishRelay
import com.stt.android.common.coroutines.CoroutineViewModel
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.common.ui.ErrorEvent
import io.reactivex.BackpressureStrategy
import io.reactivex.Flowable
import io.reactivex.disposables.CompositeDisposable
import timber.log.Timber

open class SportModeViewModel2(dispatchers: CoroutinesDispatchers) : CoroutineViewModel(dispatchers) {
    lateinit var sharedViewModel: SportModeHolderViewModel

    protected val disposables = CompositeDisposable()
    private val retryRelay = PublishRelay.create<Any>()

    var isLoading by mutableStateOf(true)
        protected set

    var isEmpty by mutableStateOf(false)
        protected set

    @WorkerThread
    fun handleError(error: Flowable<Throwable>): Flowable<Any> {
        return error.switchMap { throwable ->
            Timber.d(throwable, "resetOnError called")
            val errorEvent = ErrorEvent.get(throwable::class)
            if (errorEvent.shouldHandle) {
                Timber.v("Showing error snackbar")
//                _errorEvent.postValue(errorEvent)
                retryRelay.toFlowable(BackpressureStrategy.LATEST)
            } else {
                Timber.v("Unhandled error, propagating")
                Flowable.error<Throwable>(throwable)
            }
        }
    }

    override fun onCleared() {
        super.onCleared()
        disposables.clear()
    }
}
