package com.stt.android.watch.preference.notification

import com.stt.android.watch.preference.SetupPreference
import com.stt.android.watch.preference.SetupPreferenceReducer

class EnableNotificationReducer(private val enable: Boolean) : SetupPreferenceReducer {
    override val reduce: suspend (SetupPreference) -> SetupPreference = {
        it.copy(
            notifications = it.notifications.copy(
                enable = enable,
                appEnable = enable,
                msgEnable = enable,
                callEnable = enable,
            )
        )
    }
}
