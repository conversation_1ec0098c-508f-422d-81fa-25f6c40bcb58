package com.stt.android.watch.watchupdates;

import com.stt.android.common.viewstate.ViewStateEpoxyController;
import dagger.Binds;
import dagger.Module;
import dagger.hilt.InstallIn;
import dagger.hilt.android.components.FragmentComponent;

@Module
@InstallIn(FragmentComponent.class)
public abstract class WatchUpdatesModule {

    @Binds
    public abstract ViewStateEpoxyController<WatchUpdatesFragmentData>
    bindWatchUpdatesController(
        WatchUpdatesController controller
    );
}
