package com.stt.android.watch.sportmodes

import android.annotation.SuppressLint
import android.os.Bundle
import android.widget.TextView
import androidx.activity.viewModels
import androidx.navigation.findNavController
import androidx.navigation.fragment.NavHostFragment
import com.google.android.material.snackbar.Snackbar
import com.stt.android.R
import com.stt.android.ThemeColors
import com.stt.android.common.ui.ViewModelActivity2
import com.stt.android.common.ui.observeNotNull
import com.stt.android.compose.theme.SuuntoIcons
import com.stt.android.databinding.ActivitySportmodesBinding
import com.stt.android.domain.device.ConnectedWatchConnectionState
import com.stt.android.domain.device.ConnectedWatchState
import com.stt.android.domain.device.SyncState
import com.stt.android.utils.HarmonyUtils
import com.stt.android.watch.sportmodes.list.ActionModeEvent
import com.stt.android.watch.sportmodes.list.SportModeActionMode
import com.stt.android.window.setFlagsAndColors
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class SportModeActivity : ViewModelActivity2() {
    override val viewModel: SportModeHolderViewModel by viewModels()

    private val viewDataBinding: ActivitySportmodesBinding get() = requireBinding()

    private var stateSnackbar: Snackbar? = null

    private fun finishActionMode() {
        sportModeActionModeWrapper.finishActionMode()
    }

    private fun changeStatusBarToDark() {
        window.setFlagsAndColors(darkMode = true)
    }

    private fun changeStatusBarToWhite() {
        window.setFlagsAndColors(darkMode = false)
    }

    private fun changeToolbarToPickMode() {
        viewDataBinding.toolbarSportmodes.apply {
            setBackgroundColor(ThemeColors.primaryTextColor(context))
            val backgroundColor = ThemeColors.resolveColor(context, android.R.attr.colorBackground)
            setTitleTextColor(backgroundColor)

            supportActionBar?.setHomeAsUpIndicator(SuuntoIcons.ActionClose.resource)
            navigationIcon?.setTint(backgroundColor)
        }
        changeStatusBarToDark()
    }

    private fun changeToolbarToDefaultMode() {
        viewDataBinding.toolbarSportmodes.apply {
            setBackgroundColor(ThemeColors.resolveColor(context, android.R.attr.colorBackground))
            setTitleTextColor(ThemeColors.primaryTextColor(context))
            supportActionBar?.setHomeAsUpIndicator(SuuntoIcons.ActionBack.resource)
        }
        changeStatusBarToWhite()
    }

    override fun getLayoutResId() = R.layout.activity_sportmodes

    @Inject
    internal lateinit var sportModeActionModeWrapper: SportModeActionMode

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        val navHostFragment =
            supportFragmentManager.findFragmentById(R.id.sport_modes_host_fragment) as NavHostFragment
        val navController = navHostFragment.navController
        navController.setGraph(
            if (viewModel.fteCompleted) R.navigation.sport_mode_graph else R.navigation.sport_mode_graph_fte,
            // extras are needed in FTE to use the device model
            startDestinationArgs = intent.extras
        )
        configureToolbar()

        viewModel.connectedWatchState.observeNotNull(this) { connectedWatchState ->
            updateStateSnackbar(connectedWatchState)
        }

        viewModel.actionModeEvent.observeNotNull(this) { event ->
            when (event) {
                is ActionModeEvent.FinishActionMode -> finishActionMode()
                is ActionModeEvent.ChangeToolbarToPickMode -> changeToolbarToPickMode()
                is ActionModeEvent.ChangeToolbarToDefaultMode -> changeToolbarToDefaultMode()
            }
        }

        savedInstanceState?.let { bundle ->
            if (bundle.getBoolean(KEY_ACTION_MODE_WAS_ENABLED, false)) {
                bundle.getString(KEY_ACTION_MODE_TITLE_RES)?.let {
                }
            }
        }

        viewModel.title.observeNotNull(this) { title ->
            supportActionBar?.let {
                it.title = title
            }
        }
    }

    override fun onSupportNavigateUp(): Boolean {
        val navController = findNavController(R.id.sport_modes_host_fragment)
        // Check if user tries to go back without saving changes
        if (viewModel.shouldShowNonSavedChangesAlert(navController.currentDestination?.id)) {
            return false
        } else if (navController.currentDestination?.id == R.id.sportModeListFragment) {
            // Check if it is HarmonyOs system
            // HarmonyOs system does not have custom animation
            if (HarmonyUtils.isHarmonyOs()) {
                finish()
                return false
            }
            return super.onSupportNavigateUp()
        } else {
            return findNavController(R.id.sport_modes_host_fragment).navigateUp() || super.onSupportNavigateUp()
        }
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        outState.putBoolean(
            KEY_ACTION_MODE_WAS_ENABLED,
            sportModeActionModeWrapper.isActionModeActive()
        )
        // Example of title: %d selected
        // This will be used later with String.format()
        outState.putString(KEY_ACTION_MODE_TITLE_RES, sportModeActionModeWrapper.title)
    }

    @SuppressLint("MissingSuperCall")
    @Deprecated("Deprecated in Java")
    override fun onBackPressed() {
        onSupportNavigateUp()
    }

    private fun configureToolbar() {
        setSupportActionBar(viewDataBinding.toolbarSportmodes)

        // TODO check if this can be done via navigation component instead
        viewDataBinding.toolbarSportmodes.setNavigationOnClickListener {
            onSupportNavigateUp()
        }

        supportActionBar?.let {
            it.setDisplayShowHomeEnabled(false)
            it.setDisplayHomeAsUpEnabled(true)
        }
    }

    private fun updateStateSnackbar(connectedWatchState: ConnectedWatchState) {
        if (connectedWatchState.isNormalState()) {
            // Hide snackbar if we are in the normal state
            stateSnackbar?.dismiss()
        } else {
            val isEditDisplayFragment = findNavController(R.id.sport_modes_host_fragment).let {
                it.currentDestination?.id == R.id.sportModeEditDisplaysFragment
            }
            val resId = if (connectedWatchState.isBusy) {
                // Busy state
                R.string.sport_mode_watch_busy
            } else if (connectedWatchState.connectedWatchSyncState.state != SyncState.NOT_SYNCING) {
                // Syncing state
                if (isEditDisplayFragment) {
                    R.string.sport_mode_watch_syncing_edit_display_screen
                } else {
                    R.string.sport_mode_watch_syncing
                }
            } else {
                when (connectedWatchState.connectedWatchConnectionState) {
                    // Connection issues state
                    ConnectedWatchConnectionState.RECONNECTING -> {
                        if (isEditDisplayFragment) {
                            R.string.sport_mode_watch_disconnected_edit_display_screen
                        } else {
                            R.string.sport_mode_watch_disconnected
                        }
                    }

                    ConnectedWatchConnectionState.DISCONNECTED -> {
                        if (isEditDisplayFragment) {
                            R.string.sport_mode_watch_disconnected_edit_display_screen
                        } else {
                            R.string.sport_mode_watch_disconnected
                        }
                    }

                    else -> R.string.error_generic
                }
            }
            val isShown = stateSnackbar?.isShown ?: false
            if (isShown) {
                stateSnackbar?.setText(resId)
            } else {
                updateStateSnackbar(resId)
                stateSnackbar?.show()
            }
        }
    }

    /**
     * Initialize the snackbar
     * Modifying Default Properties
     */
    private fun updateStateSnackbar(resId: Int) {
        stateSnackbar = Snackbar.make(
            viewDataBinding.root,
            resId,
            Snackbar.LENGTH_INDEFINITE
        )
        stateSnackbar
            ?.view
            ?.findViewById<TextView>(com.google.android.material.R.id.snackbar_text)
            ?.apply {
                maxLines = 3
            }
    }

    companion object {
        const val SUPPORT_MODE = "SupportMode"
        const val FTE_COMPLETED = "FteCompleted"
        const val SUUNTO_DEVICE_TYPE = "SuuntoDeviceType"
        const val WATCH_MODEL = "WatchModel"
        const val WATCH_FIRMWARE = "WatchFirmware"
        const val WATCH_SERIAL_NUMBER = "WatchSerialNumber"
        const val KEY_ACTION_MODE_WAS_ENABLED = "KEY_ACTION_MODE_WAS_ENABLED"
        const val KEY_ACTION_MODE_TITLE_RES = "KEY_ACTION_MODE_TITLE_RES"
    }
}
