package com.stt.android.featuretoggle

import com.stt.android.BuildConfig
import com.stt.android.utils.STTConstants.FeatureTogglePreferences
import com.stt.android.utils.STTConstants.FeatureTogglePreferences.KEY_COMPANION_LINKING_TEST_MODE_FEATURE_DEFAULT
import com.stt.android.utils.STTConstants.FeatureTogglePreferences.KEY_DISABLE_SUUNTO_PLUS_WATCH_SYNC_DEFAULT
import com.stt.android.utils.STTConstants.FeatureTogglePreferences.KEY_DIVE_PLANNER_DEFAULT
import com.stt.android.utils.STTConstants.FeatureTogglePreferences.KEY_ENABLE_BADGES_FEATURE_DEFAULT
import com.stt.android.utils.STTConstants.FeatureTogglePreferences.KEY_ENABLE_DILU_ONBOARDING_WATCH_FACES_DEFAULT
import com.stt.android.utils.STTConstants.FeatureTogglePreferences.KEY_ENABLE_HEADSET_RUNNING_DEFAULT
import com.stt.android.utils.STTConstants.FeatureTogglePreferences.KEY_ENABLE_SELECT_SPORTS_DEFAULT
import com.stt.android.utils.STTConstants.FeatureTogglePreferences.KEY_ENABLE_WATCH_WIDGETS_WITHOUT_CAPABILITY_DEFAULT
import com.stt.android.utils.STTConstants.FeatureTogglePreferences.KEY_ONLY_FOR_TESTING_PURPOSES_HRV_GRAPH_DEFAULT
import com.stt.android.utils.STTConstants.FeatureTogglePreferences.KEY_OTA_UPDATE_STOP_BUTTON_DEFAULT
import com.stt.android.utils.STTConstants.FeatureTogglePreferences.KEY_SHOW_SUUNTO_HEADSET_MAC_DISPLAY_DEFAULT
import com.stt.android.utils.STTConstants.FeatureTogglePreferences.KEY_SHOW_WATCH_DEBUG_LOCATION_OPTION_DEFAULT
import com.stt.android.utils.STTConstants.FeatureTogglePreferences.KEY_SIGN_UP_QUESTIONNAIRE_DEFAULT
import com.stt.android.utils.STTConstants.FeatureTogglePreferences.KEY_TEST_MENSTRUAL_CYCLE_REMINDER_DEFAULT
import com.stt.android.utils.STTConstants.FeatureTogglePreferences.KEY_USE_HELPSHIFT_TEST_APP_DEFAULT

fun featureKeys(isFieldTester: Boolean) = listOfNotNull(
    FeatureTogglePreferences.KEY_USE_HELPSHIFT_TEST_APP,
    FeatureTogglePreferences.KEY_COMPANION_LINKING_TEST_MODE_FEATURE,
    FeatureTogglePreferences.KEY_OTA_UPDATE_STOP_BUTTON,
    FeatureTogglePreferences.KEY_SIGN_UP_QUESTIONNAIRE,
    FeatureTogglePreferences.KEY_DIVE_PLANNER,
    FeatureTogglePreferences.KEY_ENABLE_SELECT_SPORTS,
    FeatureTogglePreferences.KEY_ENABLE_WATCH_WIDGETS_WITHOUT_CAPABILITY,
    FeatureTogglePreferences.KEY_DISABLE_SUUNTO_PLUS_WATCH_SYNC,
    FeatureTogglePreferences.KEY_ONLY_FOR_TESTING_PURPOSES_HRV_GRAPH,
    FeatureTogglePreferences.KEY_SHOW_SUUNTO_HEADSET_MAC_DISPLAY.takeIf { isFieldTester },
    FeatureTogglePreferences.KEY_SHOW_WATCH_DEBUG_LOCATION_OPTION,
    FeatureTogglePreferences.KEY_TEST_MENSTRUAL_CYCLE_REMINDER,
    FeatureTogglePreferences.KEY_ENABLE_DILU_ONBOARDING_WATCH_FACES,
    FeatureTogglePreferences.KEY_ENABLE_ZONE_SENSE_DEBUG_INFO,
    FeatureTogglePreferences.KEY_ENABLE_HEADSET_RUNNING,
    FeatureTogglePreferences.KEY_ENABLE_NEW_WIDGETS,
    FeatureTogglePreferences.KEY_ENABLE_MARKETING_BANNER_API_RATE,
    FeatureTogglePreferences.KEY_ENABLE_TOP_ROUTE_FEATURES,
    FeatureTogglePreferences.KEY_ENABLE_WORKOUT_VALUE_GROUPS,
    FeatureTogglePreferences.KEY_ENABLE_SU09,
    FeatureTogglePreferences.KEY_GOOGLE_LOGIN,
    FeatureTogglePreferences.KEY_ENABLE_FIELD_TESTER_FOR_DEBUG.takeIf { BuildConfig.DEBUG }, // We want to make sure this is only available for Debug build for obvious reasons.
    FeatureTogglePreferences.KEY_ENABLE_NEW_HEADSET_SETTING_UI,
    FeatureTogglePreferences.KEY_ENABLE_SUUNTO_PLUS_FEEDBACK,
    FeatureTogglePreferences.KEY_CLIMB_GUIDANCE_2,
    FeatureTogglePreferences.KEY_ENABLE_DAY_VIEW_V2,
    FeatureTogglePreferences.KEY_ENABLE_BADGES_FEATURE,
    FeatureTogglePreferences.KEY_OFFLINE_MAP_FOR_MOBILE,
)

// Suunto app specific FeatureItems
fun getStaticFeatures(key: String): FeatureItem {
    return when (key) {
        FeatureTogglePreferences.KEY_USE_HELPSHIFT_TEST_APP ->
            FeatureItem("Helpshift test mode", key, KEY_USE_HELPSHIFT_TEST_APP_DEFAULT, true)

        FeatureTogglePreferences.KEY_COMPANION_LINKING_TEST_MODE_FEATURE ->
            FeatureItem(
                "Companion linking test mode",
                key,
                KEY_COMPANION_LINKING_TEST_MODE_FEATURE_DEFAULT,
                false
            )

        FeatureTogglePreferences.KEY_OTA_UPDATE_STOP_BUTTON ->
            FeatureItem("OTA update stop button", key, KEY_OTA_UPDATE_STOP_BUTTON_DEFAULT, false)

        FeatureTogglePreferences.KEY_SIGN_UP_QUESTIONNAIRE ->
            FeatureItem(
                "Always show questionnaire in sign-up",
                key,
                KEY_SIGN_UP_QUESTIONNAIRE_DEFAULT,
                requireProcessKill = false
            )

        FeatureTogglePreferences.KEY_DIVE_PLANNER ->
            FeatureItem("Dive planner", key, KEY_DIVE_PLANNER_DEFAULT, requireProcessKill = true)

        FeatureTogglePreferences.KEY_ENABLE_SELECT_SPORTS ->
            FeatureItem(
                "Enable 'Select sports' in onboarding",
                key,
                KEY_ENABLE_SELECT_SPORTS_DEFAULT,
                requireProcessKill = false
            )

        FeatureTogglePreferences.KEY_ENABLE_WATCH_WIDGETS_WITHOUT_CAPABILITY ->
            FeatureItem(
                "Enable watch widgets for unsupported devices",
                key,
                KEY_ENABLE_WATCH_WIDGETS_WITHOUT_CAPABILITY_DEFAULT,
                requireProcessKill = false
            )

        FeatureTogglePreferences.KEY_DISABLE_SUUNTO_PLUS_WATCH_SYNC ->
            FeatureItem(
                "Disable syncing SuuntoPlus™ apps to watch",
                key,
                KEY_DISABLE_SUUNTO_PLUS_WATCH_SYNC_DEFAULT,
                requireProcessKill = true
            )

        FeatureTogglePreferences.KEY_ONLY_FOR_TESTING_PURPOSES_HRV_GRAPH ->
            FeatureItem(
                name = "Show HRV graph",
                key = key,
                enabled = KEY_ONLY_FOR_TESTING_PURPOSES_HRV_GRAPH_DEFAULT,
                requireProcessKill = false
            )

        FeatureTogglePreferences.KEY_SHOW_SUUNTO_HEADSET_MAC_DISPLAY ->
            FeatureItem("mac display for eagle feature", key, KEY_SHOW_SUUNTO_HEADSET_MAC_DISPLAY_DEFAULT)

        FeatureTogglePreferences.KEY_SHOW_WATCH_DEBUG_LOCATION_OPTION ->
            FeatureItem("Show 'Debug location' menu option", key, KEY_SHOW_WATCH_DEBUG_LOCATION_OPTION_DEFAULT
            )

        FeatureTogglePreferences.KEY_TEST_MENSTRUAL_CYCLE_REMINDER ->
            FeatureItem("Test menstrual cycle reminder", key, KEY_TEST_MENSTRUAL_CYCLE_REMINDER_DEFAULT)

        FeatureTogglePreferences.KEY_ENABLE_DILU_ONBOARDING_WATCH_FACES ->
            FeatureItem("Enable DiLu onboarding watch faces", key, KEY_ENABLE_DILU_ONBOARDING_WATCH_FACES_DEFAULT)

        FeatureTogglePreferences.KEY_ENABLE_ZONE_SENSE_DEBUG_INFO ->
            FeatureItem("Enable ZoneSense debug info", key, FeatureTogglePreferences.KEY_ENABLE_ZONE_SENSE_DEBUG_INFO_DEFAULT)

        FeatureTogglePreferences.KEY_ENABLE_HEADSET_RUNNING ->
            FeatureItem("Enable Headset Running", key, KEY_ENABLE_HEADSET_RUNNING_DEFAULT)

        FeatureTogglePreferences.KEY_ENABLE_NEW_WIDGETS ->
            FeatureItem("Enable new widgets", key, FeatureTogglePreferences.KEY_ENABLE_NEW_WIDGETS_DEFAULT)

        FeatureTogglePreferences.KEY_ENABLE_MARKETING_BANNER_API_RATE ->
            FeatureItem("Enable marketing banner API rate limitation", key, FeatureTogglePreferences.KEY_ENABLE_MARKETING_BANNER_API_RATE_DEFAULT)

        FeatureTogglePreferences.KEY_ENABLE_TOP_ROUTE_FEATURES ->
            FeatureItem("Enable TopRoutes features", key, FeatureTogglePreferences.KEY_ENABLE_TOP_ROUTE_FEATURES_DEFAULT)

        FeatureTogglePreferences.KEY_ENABLE_WORKOUT_VALUE_GROUPS ->
            FeatureItem("Enable workout value groups", key, FeatureTogglePreferences.KEY_ENABLE_WORKOUT_VALUE_GROUPS_DEFAULT)

        FeatureTogglePreferences.KEY_ENABLE_SU09 ->
            FeatureItem("Enable SU09", key, FeatureTogglePreferences.KEY_ENABLE_SU09_DEFAULT)

        FeatureTogglePreferences.KEY_GOOGLE_LOGIN ->
            FeatureItem("Enable Google login", key, FeatureTogglePreferences.KEY_GOOGLE_LOGIN_DEFAULT)

        FeatureTogglePreferences.KEY_ENABLE_FIELD_TESTER_FOR_DEBUG ->
            FeatureItem(
                name = "Enable field tester role (for debugging only)",
                key = key,
                enabled = FeatureTogglePreferences.KEY_ENABLE_FIELD_TESTER_FOR_DEBUG_DEFAULT,
                requireProcessKill = true,
            )

        FeatureTogglePreferences.KEY_ENABLE_NEW_HEADSET_SETTING_UI ->
            FeatureItem("Enable new headset setting UI", key, FeatureTogglePreferences.KEY_ENABLE_NEW_HEADSET_SETTING_UI_DEFAULT)

        FeatureTogglePreferences.KEY_ENABLE_SUUNTO_PLUS_FEEDBACK ->
            FeatureItem("Enable SuuntoPlus feedback", key, FeatureTogglePreferences.KEY_ENABLE_SUUNTO_PLUS_FEEDBACK_DEFAULT)

        FeatureTogglePreferences.KEY_CLIMB_GUIDANCE_2 ->
            FeatureItem("Enable climb guidance 2", key, FeatureTogglePreferences.KEY_CLIMB_GUIDANCE_2_DEFAULT)

        FeatureTogglePreferences.KEY_ENABLE_DAY_VIEW_V2 ->
            FeatureItem("Enable Day View V2", key, FeatureTogglePreferences.KEY_ENABLE_DAY_VIEW_V2_DEFAULT)

        FeatureTogglePreferences.KEY_ENABLE_BADGES_FEATURE ->
            FeatureItem("Enable Badges Features", key, KEY_ENABLE_BADGES_FEATURE_DEFAULT,true)

        FeatureTogglePreferences.KEY_OFFLINE_MAP_FOR_MOBILE ->
            FeatureItem("Enable offline map for mobile", key, FeatureTogglePreferences.KEY_OFFLINE_MAP_FOR_MOBILE_DEFAULT)

        else -> throw IllegalArgumentException("Unknown key")
    }
}
