package com.stt.android.notifications

import android.app.NotificationChannel
import android.app.NotificationManager
import android.content.Context
import com.stt.android.R
import javax.inject.Inject

/**
 * Notification channels specific to Suunto.
 */
class NotificationChannels @Inject constructor() : BaseNotificationChannels() {
    override fun createChannels(context: Context, notificationManager: NotificationManager) {
        super.createChannels(context, notificationManager)

        // Create Suunto specific notification channels here

        notificationManager.createNotificationChannel(
            NotificationChannel(
                CHANNEL_ID_ACTIVITY_SYNCED,
                context.getString(R.string.notification_channel_activity_synced),
                NotificationManager.IMPORTANCE_HIGH
            )
                .apply {
                    group = CHANNEL_GROUP_ID_MY_ACTIVITIES
                }
        )
    }
}
