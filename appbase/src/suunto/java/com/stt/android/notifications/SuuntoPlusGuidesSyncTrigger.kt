package com.stt.android.notifications

import com.stt.android.notifications.noui.SyncTrigger
import com.stt.android.watch.background.SuuntoPlusGuideRemoteSyncJobLauncher
import javax.inject.Inject

class SuuntoPlusGuidesSyncTrigger @Inject constructor(
    private val suuntoPlusGuideRemoteSyncJobLauncher: SuuntoPlusGuideRemoteSyncJobLauncher,
) : SyncTrigger {
    override suspend fun triggerSync() {
        suuntoPlusGuideRemoteSyncJobLauncher.enqueueRemoteSyncJob()
    }
}
