package com.stt.android.home.settings

import android.content.ContentProvider
import android.content.ContentValues
import android.content.SharedPreferences
import android.content.UriMatcher
import android.database.Cursor
import android.database.MatrixCursor
import android.net.Uri
import androidx.core.content.edit
import androidx.core.net.toUri
import com.stt.android.AppConfig
import com.stt.android.STTApplication
import com.stt.android.di.WatchSettingsPreferences
import com.stt.android.utils.STTConstants
import com.stt.android.utils.takeIfNotEmpty
import dagger.hilt.EntryPoint
import dagger.hilt.InstallIn
import dagger.hilt.android.EntryPointAccessors
import dagger.hilt.components.SingletonComponent
import kotlinx.collections.immutable.persistentSetOf
import javax.inject.Inject

@InstallIn(SingletonComponent::class)
@EntryPoint
interface WatchSettingsProviderMainProcessEntryPoint {
    fun inject(watchSettingsContentProvider: WatchSettingsContentProvider)
}

class WatchSettingsContentProvider : ContentProvider() {

    @Inject
    @WatchSettingsPreferences
    lateinit var watchSettingsSharedPreferences: SharedPreferences

    override fun onCreate(): Boolean {
        val sttApplication = context?.applicationContext as? STTApplication
        sttApplication?.injection
            ?: throw RuntimeException("STTApplication not found by SettingsContentProvider, instead found: ${context?.applicationContext}")
        EntryPointAccessors.fromApplication<WatchSettingsProviderMainProcessEntryPoint>(sttApplication)
            .inject(this)
        return true
    }

    override fun query(
        uri: Uri,
        projection: Array<out String>?,
        selection: String?,
        selectionArgs: Array<out String>?,
        sortOrder: String?
    ): Cursor? {
        when (MATCHER.match(uri)) {
            HR_INTENSITY_ZONES_DIR -> {
                val values = KEYS.map { watchSettingsSharedPreferences.getString(it, null) }
                val cursor = MatrixCursor(KEYS.toTypedArray(), 1)
                cursor.addRow(values)
                return cursor
            }
        }
        return null
    }

    override fun getType(uri: Uri): String? {
        return null
    }

    override fun insert(uri: Uri, values: ContentValues?): Uri? {
        when (MATCHER.match(uri)) {
            HR_INTENSITY_ZONES_DIR -> {
                val entries = values?.keySet()?.filter { KEYS.contains(it) }
                    ?.map { it to values.getAsString(it) }?.takeIfNotEmpty()
                if (entries.isNullOrEmpty()) {
                    watchSettingsSharedPreferences.edit { clear() }
                } else {
                    watchSettingsSharedPreferences.edit {
                        entries.forEach { (key, value) -> putString(key, value) }
                    }
                }
                context?.contentResolver?.notifyChange(uri, null)
                return uri
            }
        }
        return null
    }

    override fun delete(uri: Uri, selection: String?, selectionArgs: Array<out String>?): Int {
        throw UnsupportedOperationException("Delete is not supported, use insert instead!")
    }

    override fun update(
        uri: Uri,
        values: ContentValues?,
        selection: String?,
        selectionArgs: Array<out String>?
    ): Int {
        throw UnsupportedOperationException("Update is not supported, use insert instead!")
    }

    companion object {
        const val HR_ZONES_TYPE = STTConstants.WatchSettingsPreferences.KEY_HR_ZONE_TYPE
        const val INTENSITY_ZONES = STTConstants.WatchSettingsPreferences.KEY_INTENSITY_ZONES

        private const val AUTHORITY = "${AppConfig.APPLICATION_ID}.settings.provider"
        private val CONTENT_URI = "content://$AUTHORITY".toUri()
        private const val HR_INTENSITY_ZONES_PATH = "hr_intensity_zones"
        private const val HR_INTENSITY_ZONES_DIR = 100
        private val KEYS = persistentSetOf(HR_ZONES_TYPE, INTENSITY_ZONES)
        private val MATCHER = UriMatcher(UriMatcher.NO_MATCH).apply {
            addURI(AUTHORITY, HR_INTENSITY_ZONES_PATH, HR_INTENSITY_ZONES_DIR)
        }

        val HR_INTENSITY_ZONES: Uri =
            CONTENT_URI.buildUpon().appendPath(HR_INTENSITY_ZONES_PATH).build()
    }
}
