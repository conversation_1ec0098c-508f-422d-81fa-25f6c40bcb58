package com.stt.android.home.dashboard.activitydata.widget

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.stt.android.compose.theme.material3.bodyMega
import com.stt.android.compose.theme.nearWhite
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.widgets.WheelPicker
import com.stt.android.home.dashboard.activitydata.Bedtime

@Composable
fun TimeRangePicker(
    bedtime: Bedtime,
    onBedtimeUpdate: (bedtime: Bedtime) -> Unit,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier = modifier,
        horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.large),
    ) {
        TimePicker(
            modifier = Modifier.weight(1f),
            hour = bedtime.startHour,
            minute = bedtime.startMinute,
            onTimeChange = { hour, minute ->
                onBedtimeUpdate(bedtime.copy(startHour = hour, startMinute = minute))
            },
        )
        TimePicker(
            modifier = Modifier.weight(1f),
            hour = bedtime.endHour,
            minute = bedtime.endMinute,
            onTimeChange = { hour, minute ->
                onBedtimeUpdate(bedtime.copy(endHour = hour, endMinute = minute))
            },
        )
    }
}

@Composable
private fun TimePicker(
    hour: Int,
    minute: Int,
    onTimeChange: (hour: Int, minute: Int) -> Unit,
    modifier: Modifier = Modifier,
    pickerHeight: Dp = 150.dp,
    indicatorHeight: Dp = 35.dp,
    visibleCount: Int = 5,
) {
    val selectedHourIndex = remember(hour) {
        hour % 24
    }
    val selectedMinuteIndex = remember(minute) {
        minute / 15
    }
    Box(modifier = modifier.height(pickerHeight)) {
        Box(
            modifier = Modifier
                .align(Alignment.Center)
                .background(
                    color = MaterialTheme.colorScheme.nearWhite,
                    shape = RoundedCornerShape(4.dp),
                )
                .fillMaxWidth()
                .height(indicatorHeight),
        )
        Row(modifier = Modifier.fillMaxSize()) {
            Spacer(modifier = Modifier.weight(.5f))
            TimeUnitPicker(
                modifier = Modifier
                    .weight(1f)
                    .fillMaxHeight(),
                values = (0..23).toList(),
                selectedIndex = selectedHourIndex,
                onSelectedIndexChange = { index ->
                    onTimeChange(index, selectedMinuteIndex * 15)
                },
                visibleCount = visibleCount,
            )
            TimeUnitPicker(
                modifier = Modifier
                    .weight(1f)
                    .fillMaxHeight(),
                values = (0..45 step 15).toList(),
                selectedIndex = selectedMinuteIndex,
                onSelectedIndexChange = { index ->
                    onTimeChange(selectedHourIndex, index * 15)
                },
                visibleCount = visibleCount,
            )
            Spacer(modifier = Modifier.weight(.5f))
        }
    }
}

@Composable
private fun TimeUnitPicker(
    values: List<Int>,
    selectedIndex: Int,
    onSelectedIndexChange: (Int) -> Unit,
    visibleCount: Int,
    modifier: Modifier = Modifier,
) {
    WheelPicker(
        modifier = modifier,
        data = values,
        selectIndex = selectedIndex,
        visibleCount = visibleCount,
        enableScale = true,
        onSelect = { index, _ ->
            onSelectedIndexChange(index)
        },
    ) { value ->
        Text(
            text = value.toString(),
            style = MaterialTheme.typography.bodyMega,
            color = MaterialTheme.colorScheme.onSurface,
        )
    }
}
