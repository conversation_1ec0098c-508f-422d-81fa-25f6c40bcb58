package com.stt.android.home.dashboard

import android.os.Bundle
import android.view.View
import androidx.core.os.bundleOf
import com.stt.android.common.ui.observeK
import com.stt.android.home.dashboard.bottomsheet.WeeklyGoalBottomSheetFragment
import com.stt.android.home.dayview.DayViewActivity
import com.stt.android.home.dayview.DayViewItemType
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class DashboardGridFragment : BaseDashboardGridFragment() {

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        viewModel.resourcesClickedEvent.observeK(viewLifecycleOwner) {
            requireActivity().startActivity(
                DayViewActivity.newStartIntent(
                    requireContext(),
                    initialItemToScrollTo = DayViewItemType.RECOVERY
                )
            )
        }
        viewModel.sleepClickedEvent.observeK(viewLifecycleOwner) {
            requireActivity().startActivity(
                DayViewActivity.newStartIntent(
                    requireContext(),
                    initialItemToScrollTo = DayViewItemType.SLEEP
                )
            )
        }
        viewModel.goalClickedEvent.observeK(viewLifecycleOwner) {
            WeeklyGoalBottomSheetFragment().show(
                childFragmentManager,
                WeeklyGoalBottomSheetFragment.FRAGMENT_TAG
            )
        }
        viewModel.minimumHeartRateClickedEvent.observeK(viewLifecycleOwner) {
            requireActivity().startActivity(
                DayViewActivity.newStartIntent(
                    requireContext(),
                    initialItemToScrollTo = DayViewItemType.HEART_RATE
                )
            )
        }
    }

    companion object {
        fun newInstance(pagePosition: Int) = DashboardGridFragment().apply {
            arguments = bundleOf(
                ARG_PAGE_POSITION to pagePosition
            )
        }
    }
}
