package com.stt.android.home.dashboard.toolbar;

import android.annotation.SuppressLint;
import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothManager;
import android.content.Context;
import android.content.SharedPreferences;
import android.os.Build;
import androidx.annotation.NonNull;
import com.stt.android.analytics.AmplitudeAnalyticsTracker;
import com.stt.android.analytics.AnalyticsEvent;
import com.stt.android.analytics.AnalyticsEventProperty;
import com.stt.android.analytics.AnalyticsProperties;
import com.stt.android.analytics.AnalyticsUserProperty;
import com.stt.android.analytics.EmarsysAnalytics;
import com.stt.android.analytics.FirebaseAnalyticsTracker;
import com.stt.android.analytics.FirebaseUserProperty;
import com.stt.android.controllers.CurrentUserController;
import com.stt.android.controllers.FeedController;
import com.stt.android.di.SuuntoSharedPrefs;
import com.stt.android.domain.firmware.CheckForNewerFirmwareUseCase;
import com.stt.android.domain.firmware.Version;
import com.stt.android.domain.routes.Route;
import com.stt.android.domain.workout.ActivityType;
import com.stt.android.domain.workouts.WorkoutHeader;
import com.stt.android.home.dashboardv2.HeadsetInfoLoader;
import com.stt.android.offlinemaps.analytics.OfflineMapsAnalytics;
import com.stt.android.social.userprofile.HeadsetNavigator;
import com.stt.android.utils.BluetoothUtils;
import com.stt.android.utils.LocationPermissionsKt;
import com.stt.android.utils.STTConstants;
import com.stt.android.watch.DeviceHolderViewModel;
import com.stt.android.watch.MissingCurrentWatchException;
import com.stt.android.watch.SuuntoWatchModel;
import com.stt.android.workouts.RecordWorkoutModel;
import com.stt.android.workouts.TrackingState;
import com.suunto.connectivity.Spartan;
import com.suunto.connectivity.deviceid.ISuuntoDeviceCapabilityInfo;
import com.suunto.connectivity.deviceid.SuuntoDeviceCapabilityInfoProvider;
import com.suunto.connectivity.sdsmanager.model.MdsDeviceInfo;
import com.suunto.connectivity.suuntoconnectivity.device.AnalyticsDevicePropertyHelper;
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoDeviceType;
import com.suunto.connectivity.sync.SyncState;
import com.suunto.connectivity.util.FirmwareVersion;
import com.suunto.connectivity.watch.SpartanSyncResult;
import com.suunto.connectivity.watch.WatchState;
import dagger.hilt.android.scopes.ActivityScoped;
import hu.akarnokd.rxjava.interop.RxJavaInterop;
import io.reactivex.Observable;
import io.reactivex.disposables.Disposable;
import io.reactivex.functions.BiFunction;
import java.util.ArrayList;
import java.util.List;
import javax.inject.Inject;
import kotlin.Pair;
import kotlin.coroutines.EmptyCoroutineContext;
import kotlinx.coroutines.rx2.RxConvertKt;
import rx.Single;
import rx.Subscription;
import rx.android.schedulers.AndroidSchedulers;
import rx.schedulers.Schedulers;
import rx.subscriptions.CompositeSubscription;
import timber.log.Timber;

@ActivityScoped
public class DashboardToolbarPresenter
    extends BaseDashboardToolbarPresenter<WatchDashboardToolbarView> {
    final SuuntoWatchModel suuntoWatchModel;
    private final CompositeSubscription observeWatchStateSubscription = new CompositeSubscription();
    private final EmarsysAnalytics emarsysAnalytics;
    private final RecordWorkoutModel recordWorkoutModel;
    private final SharedPreferences suuntoPreferences;
    private final Context applicationContext;
    private final OfflineMapsAnalytics offlineMapsAnalytics;
    private final FirebaseAnalyticsTracker firebaseAnalyticsTracker;
    private final CheckForNewerFirmwareUseCase checkForNewerFirmwareUseCase;
    private final HeadsetNavigator headsetNavigator;
    private final HeadsetInfoLoader headsetInfoLoader;
    private Disposable checkHeadphoneSupportDisposable;

    @Inject
    public DashboardToolbarPresenter(
        Context applicationContext,
        CurrentUserController currentUserController,
        FeedController feedController,
        SuuntoWatchModel suuntoWatchModel,
        EmarsysAnalytics emarsysAnalytics,
        RecordWorkoutModel recordWorkoutModel,
        @SuuntoSharedPrefs SharedPreferences suuntoPreferences,
        OfflineMapsAnalytics offlineMapsAnalytics,
        FirebaseAnalyticsTracker firebaseAnalyticsTracker,
        AmplitudeAnalyticsTracker amplitudeAnalyticsTracker,
        CheckForNewerFirmwareUseCase checkForNewerFirmwareUseCase,
        HeadsetNavigator headsetNavigator,
        HeadsetInfoLoader headsetInfoLoader
    ) {
        super(applicationContext, currentUserController, feedController, amplitudeAnalyticsTracker);
        this.suuntoWatchModel = suuntoWatchModel;
        this.emarsysAnalytics = emarsysAnalytics;
        this.recordWorkoutModel = recordWorkoutModel;
        this.suuntoPreferences = suuntoPreferences;
        this.applicationContext = applicationContext;
        this.offlineMapsAnalytics = offlineMapsAnalytics;
        this.firebaseAnalyticsTracker = firebaseAnalyticsTracker;
        this.checkForNewerFirmwareUseCase = checkForNewerFirmwareUseCase;
        this.headsetNavigator = headsetNavigator;
        this.headsetInfoLoader = headsetInfoLoader;
    }

    @Override
    protected void onViewTaken() {
        super.onViewTaken();
        getCurrentWatch();
        checkOfflineMapsSupport();
        WatchDashboardToolbarView view = getViewWeakRef();
        if (view != null) {
            view.onBluetoothSupported(BluetoothUtils.isBluetoothSupported(applicationContext));
            view.setAddWorkoutButtonClickListener(isOngoingWorkout());
            view.setOfflineMapsAnalytics(offlineMapsAnalytics);
            checkHeadphonesSupport();
        }
    }

    public void onHeadsetButtonClicked(Context context) {
        if (headsetNavigator != null) {
            headsetNavigator.launchHeadsetActivity(context);
        }
    }

    public SuuntoWatchModel getSuuntoWatchModel() {
        return suuntoWatchModel;
    }

    private boolean isOngoingWorkout() {
        TrackingState trackingState = recordWorkoutModel.getRecordingState();
        return switch (trackingState) {
            case RECORDING, PAUSED, AUTO_PAUSED -> true;
            default -> false;
        };
    }

    void handleContinueOngoingWorkout() {
        ActivityType activityType = recordWorkoutModel.getActivityType();
        WorkoutHeader followWorkoutHeader = recordWorkoutModel.getFollowWorkoutHeader();
        WorkoutHeader ghostWorkoutHeader = recordWorkoutModel.getGhostWorkoutHeader();
        Route followRoute = recordWorkoutModel.getFollowRoute();
        boolean shouldCheckGPS = !activityType.isIndoor();

        if (followWorkoutHeader != null) {
            informContinueFollowWorkout(shouldCheckGPS, activityType, followWorkoutHeader);
        } else if (ghostWorkoutHeader != null) {
            informContinueGhostWorkout(shouldCheckGPS, activityType, ghostWorkoutHeader);
        } else if (followRoute != null) {
            informContinueFollowRoute(shouldCheckGPS, activityType, followRoute);
        } else {
            informContinueWorkout(shouldCheckGPS, activityType);
        }
    }

    private void informContinueFollowRoute(boolean shouldCheckGPS, ActivityType activityType,
        Route followRoute) {
        WatchDashboardToolbarView view = getViewWeakRef();
        if (view != null) {
            view.continueFollowRoute(shouldCheckGPS, activityType, followRoute);
        }
    }

    private void informContinueWorkout(boolean shouldCheckGPS, ActivityType activityType) {
        WatchDashboardToolbarView view = getViewWeakRef();
        if (view != null) {
            view.continueWorkout(shouldCheckGPS, activityType);
        }
    }

    private void informContinueGhostWorkout(boolean shouldCheckGPS, ActivityType activityType,
        WorkoutHeader ghostWorkoutHeader) {
        WatchDashboardToolbarView view = getViewWeakRef();
        if (view != null) {
            view.continueGhostWorkout(shouldCheckGPS, activityType, ghostWorkoutHeader);
        }
    }

    private void informContinueFollowWorkout(boolean shouldCheckGPS, ActivityType activityType,
        WorkoutHeader followWorkoutHeader) {
        WatchDashboardToolbarView view = getViewWeakRef();
        if (view != null) {
            view.continueFollowWorkout(shouldCheckGPS, activityType, followWorkoutHeader);
        }
    }

    void getCurrentWatch() {
        Subscription getWatchSubscription = suuntoWatchModel.getCurrentWatch()
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe(this::observeWatchStateChanges, throwable -> {
                if (throwable instanceof MissingCurrentWatchException) {
                    Timber.i("Missing current watch");
                    WatchDashboardToolbarView view = getViewWeakRef();
                    if (view != null && !view.isBluetoothEnabled()) {
                        view.onBluetoothDisabled(false);
                    } else if (view != null) {
                        view.onWatchNotPaired();
                    }
                } else {
                    // Not much we can do, fail
                    Timber.w(throwable, "Error during getCurrentWatch");
                }
            });
        subscription.add(getWatchSubscription);
    }

    void checkOfflineMapsSupport() {
        subscription.add(RxJavaInterop.toV1Single(suuntoWatchModel.supportsOfflineMaps(true))
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe(
                hasCapability -> {
                    WatchDashboardToolbarView view = getViewWeakRef();
                    if (view != null) {
                        view.onOfflineMapsCapabilityCheck(hasCapability);
                    }
                },
                error -> {
                    if (error instanceof MissingCurrentWatchException) {
                        Timber.d("Missing current watch");
                    } else {
                        Timber.w(error, "Unable to check offline maps support.");
                    }
                }
            )
        );
    }

    void observeWatchStateChanges(Spartan spartan) {
        observeWatchStateSubscription.clear();
        SuuntoDeviceType deviceType = spartan.getSuuntoBtDevice().getDeviceType();
        boolean isEonComputer = deviceType.isEonComputer();
        observeWatchStateSubscription.add(
            suuntoWatchModel.getStateChangeObservable()
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(watchState -> {
                        WatchState.ConnectionState connectionState =
                            watchState.getConnectionState();
                        WatchDashboardToolbarView toolbarView = getViewWeakRef();
                        if (toolbarView != null && !toolbarView.isBluetoothEnabled()) {
                            toolbarView.onBluetoothDisabled(isEonComputer);
                        } else if (!watchState.isPaired()) {
                            WatchDashboardToolbarView view = getViewWeakRef();
                            if (view != null) {
                                view.onWatchNotPaired();
                            }
                        } else if (connectionState == WatchState.ConnectionState.CONNECTING) {
                            WatchDashboardToolbarView view = getViewWeakRef();
                            if (view != null) {
                                view.onWatchReconnecting(isEonComputer);
                            }
                        } else if (watchState.getSyncState().getState()
                            != SyncState.NOT_SYNCING) {
                            // We're not connecting but syncing (there are multiple syncing states)
                            WatchDashboardToolbarView view = getViewWeakRef();
                            if (view != null) {
                                view.onWatchSyncing(isEonComputer);
                            }
                        } else if (connectionState == WatchState.ConnectionState.DISCONNECTED) {
                            WatchDashboardToolbarView view = getViewWeakRef();
                            if (view != null) {
                                view.onWatchNotConnected(isEonComputer);
                            }
                        } else if (connectionState == WatchState.ConnectionState.RECONNECTING) {
                            WatchDashboardToolbarView view = getViewWeakRef();
                            if (view != null) {
                                view.onWatchReconnecting(isEonComputer);
                            }
                        } else if (watchState.isDeviceBusy()) {
                            WatchDashboardToolbarView view = getViewWeakRef();
                            if (view != null) {
                                view.onWatchBusy();
                            }
                        } else if (connectionState == WatchState.ConnectionState.CONNECTED) {
                            // We're just connected and no syncing happening so let's show the
                            // latest
                            // sync result available
                            showLatestSyncStatus(watchState, isEonComputer);
                        }
                    }, throwable -> {
                        if (throwable instanceof MissingCurrentWatchException) {
                            WatchDashboardToolbarView view = getViewWeakRef();
                            if (view != null) {
                                view.onWatchNotPaired();
                            }
                        } else {
                            Timber.w(new UnsupportedOperationException(
                                "Oops we lost stream to watch state changes"));
                        }
                    }
                )
        );
    }

    private Single<Boolean> isFirmwareUpdateAvailable(MdsDeviceInfo deviceInfo) {
        if (deviceInfo != null) {
            SuuntoDeviceType deviceType = SuuntoDeviceType.fromVariantName(deviceInfo.getVariant());
            if (!deviceType.supportsOtaUpdate(deviceInfo.getSwVersion())) {
                return RxJavaInterop.toV1Single(checkForNewerFirmwareUseCase.isUpdateAvailable(
                    deviceInfo.getVariant(),
                    deviceInfo.getHwCompatibilityId(),
                    deviceInfo.getSwVersion(),
                    deviceInfo.getProductVersion(),
                    deviceType.isSuunto7(),
                    deviceType.supportOtaUpdateCheck(deviceInfo)
                ).first(false));
            } else {
                return suuntoWatchModel.checkForNewOtaUpdatesRx().map((checkForOtaUpdatesResponse ->
                    checkForOtaUpdatesResponse != null
                        && checkForOtaUpdatesResponse.getOtaUpdateState().hasUpdate()));
            }
        } else {
            return Single.just(false);
        }
    }

    private void showLatestSyncStatus(
        @NonNull WatchState watchState,
        boolean isEonComputer
    ) {
        Subscription lastSyncResultSubscription = Single.zip(
                suuntoWatchModel.getLastSyncResult(),
                suuntoWatchModel.getCurrentWatch().onErrorResumeNext(Single.just(null)),
                isFirmwareUpdateAvailable(watchState.getDeviceInfo()).onErrorResumeNext(
                    Single.just(false)),
                LatestSyncStatus::new
            )
            .observeOn(AndroidSchedulers.mainThread())
            .doOnSuccess(latestSyncStatus -> {
                WatchDashboardToolbarView view = getViewWeakRef();
                SpartanSyncResult syncResult = latestSyncStatus.getSyncResult();
                Spartan spartan = latestSyncStatus.getSpartan();
                boolean isFwUpdateAvailable = latestSyncStatus.isFWUpdateAvailable();
                if (view != null) {
                    if (syncResult.getLogbookResult()
                        .getLogbookResult().isFailed()) {
                        view.onWatchFail(isEonComputer);
                    } else {
                        final boolean showConnectionAlert;
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                            showConnectionAlert = spartan != null &&
                                spartan.getSuuntoBtDevice().getDeviceType().isSuunto3Family() &&
                                !LocationPermissionsKt.isBackgroundLocationPermissionGranted(
                                    applicationContext) &&
                                !DeviceHolderViewModel.isBackgroundLocationPermissionAsked(
                                    suuntoPreferences);
                        } else {
                            showConnectionAlert = false;
                        }
                        if (showConnectionAlert) {
                            view.onWatchFail(isEonComputer);
                        } else {
                            if (requiresForcedUpdate(watchState)
                                && watchState.getFirmwareUpdateStatus().getUploadInProgress()) {
                                // Forced update ongoing, show update icon
                                view.onUpdateAvailable(isEonComputer);
                            } else if (isFwUpdateAvailable) {
                                view.onUpdateAvailable(isEonComputer);
                            } else {
                                view.onWatchConnected(isEonComputer);
                            }
                        }
                    }
                }
            })
            .doOnError(throwable -> {
                if (throwable instanceof MissingCurrentWatchException) {
                    WatchDashboardToolbarView view = getViewWeakRef();
                    if (view != null) {
                        view.onWatchNotPaired();
                    }
                }
            })
            .subscribe(latestSyncStatus -> {
                SpartanSyncResult syncResult = latestSyncStatus.getSyncResult();
                if (!syncResult.getLogbookResult().getLogbookResult().isFailed()) {
                    setSleepThresholdLimitState(watchState);
                    trackUserPropertiesToAnalyticsWhenConnected(watchState);
                }
            }, error -> Timber.w(error, "Error while showLatestSyncStatus"));
        subscription.add(lastSyncResultSubscription);
    }

    private boolean requiresForcedUpdate(WatchState watchState) {
        MdsDeviceInfo deviceInfo = watchState.getDeviceInfo();
        if (deviceInfo != null) {
            String variant = deviceInfo.getVariant();
            SuuntoDeviceType deviceType = SuuntoDeviceType.fromVariantName(variant);
            ISuuntoDeviceCapabilityInfo capabilities =
                SuuntoDeviceCapabilityInfoProvider.get(deviceType);
            return capabilities.requiresForcedUpdate(deviceInfo);
        } else {
            return false;
        }
    }

    @Override
    protected void onViewDropped() {
        super.onViewDropped();
        observeWatchStateSubscription.clear();
        if (checkHeadphoneSupportDisposable != null) {
            checkHeadphoneSupportDisposable.dispose();
        }
    }

    void bluetoothEnabled(boolean enabled) {
        WatchDashboardToolbarView view = getViewWeakRef();
        if (view != null && !enabled) {
            view.onBluetoothDisabled(false);
        } else {
            getCurrentWatch();
            checkOfflineMapsSupport();
        }
    }

    private void setSleepThresholdLimitState(@NonNull WatchState state) {
        MdsDeviceInfo deviceInfo = state.getDeviceInfo();
        if (deviceInfo != null) {
            String variant = deviceInfo.getVariant();
            SuuntoDeviceType deviceType = SuuntoDeviceType.fromVariantName(variant);
            ISuuntoDeviceCapabilityInfo capabilities =
                SuuntoDeviceCapabilityInfoProvider.get(deviceType);
            if (capabilities.supportsSleepData(deviceInfo)) {
                String firmwareVersion = deviceInfo.getSwVersion();
                boolean firstbeatLimits =
                    FirmwareVersion.isNewerThanOrEqualToReference(firmwareVersion,
                        FirmwareVersion.FIRSTBEAT_SLEEP_THRESHOLD);
                suuntoPreferences.edit()
                    .putBoolean(STTConstants.SuuntoPreferences.KEY_FIRSTBEAT_SLEEP_THRESHOLD,
                        firstbeatLimits)
                    .apply();
            }
        }
    }

    private void trackUserPropertiesToAnalyticsWhenConnected(
        @NonNull WatchState state
    ) {
        MdsDeviceInfo deviceInfo = state.getDeviceInfo();
        if (deviceInfo != null) {
            String model = AnalyticsDevicePropertyHelper.getWatchModelNameForVariantName(
                deviceInfo.getVariant());
            // Send watch model as a String array so that it can be utilized in Braze
            List<String> modelAsArray = new ArrayList<>();
            modelAsArray.add(model);
            String swVersion = deviceInfo.getSwVersion();
            AnalyticsProperties properties = new AnalyticsProperties()
                .put(AnalyticsUserProperty.SUUNTO_WATCH_MODELS, modelAsArray)
                .put(AnalyticsUserProperty.SUUNTO_WATCH_FIRMWARE_VERSIONS, swVersion);

            emarsysAnalytics.trackUserProperties(properties.getMap());
            amplitudeAnalyticsTracker.trackUserProperties(properties);
            firebaseAnalyticsTracker.trackUserProperty(FirebaseUserProperty.SUUNTO_WATCH_MODEL,
                model);
            firebaseAnalyticsTracker.trackUserProperty(
                FirebaseUserProperty.SUUNTO_WATCH_FIRMWARE_VERSIONS, swVersion);

            try {
                Version version = new Version(swVersion);
                firebaseAnalyticsTracker.trackEvent(AnalyticsEvent.SUUNTO_WATCH_FIRMWARE_VERSIONS,
                    new AnalyticsProperties()
                        .put(AnalyticsEventProperty.MAJOR, version.getMajor())
                        .put(AnalyticsEventProperty.MINOR, version.getMinor())
                        .put(AnalyticsEventProperty.HOTFIX, version.getHotfix())
                );
            } catch (Exception e) {
                Timber.w(e, "Error parsing version: %s", swVersion);
            }
        }
    }

    public void checkHeadphonesSupport() {
        checkHeadphoneSupportDisposable = Observable.zip(getBondedDevice(), getLastHeadphoneMac(),
                (BiFunction<List<BluetoothDevice>, String, Pair<List<BluetoothDevice>, String>>) Pair::new)
            .subscribeOn(io.reactivex.schedulers.Schedulers.io())
            .observeOn(io.reactivex.android.schedulers.AndroidSchedulers.mainThread())
            .subscribe(headphoneData -> {
                boolean showHeadphone = headphoneData.getFirst().stream().anyMatch(
                    bluetoothDevice -> bluetoothDevice.getAddress()
                        .equals(headphoneData.getSecond()));
                if (view != null) {
                    view.showHeadphoneIcon(showHeadphone);
                }
            }, throwable -> {
                Timber.w(throwable, "check headphone support failed");
                if (view != null) {
                    view.showHeadphoneIcon(false);
                }
            });
    }

    @SuppressLint("MissingPermission")
    private Observable<List<BluetoothDevice>> getBondedDevice() {
        return Observable.fromCallable(() -> {
            BluetoothManager bluetoothManager =
                (BluetoothManager) applicationContext.getSystemService(
                    Context.BLUETOOTH_SERVICE);
            return bluetoothManager.getAdapter().getBondedDevices().stream().toList();
        });
    }

    private Observable<String> getLastHeadphoneMac() {
        return RxConvertKt.asObservable(headsetInfoLoader.fetchLastPairedHeadsetMac(),
            EmptyCoroutineContext.INSTANCE);
    }
}
