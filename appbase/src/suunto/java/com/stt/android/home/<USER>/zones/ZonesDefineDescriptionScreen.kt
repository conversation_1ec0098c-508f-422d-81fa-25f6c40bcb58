package com.stt.android.home.settings.zones

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import com.stt.android.BuildConfig
import com.stt.android.R
import com.stt.android.compose.theme.material3.body
import com.stt.android.compose.theme.material3.bodyLargeBold
import com.stt.android.compose.theme.nearBlack
import com.stt.android.compose.theme.spacing
import com.stt.android.usecases.startup.LowPriorityStartupUseCase.Companion.STORE_NAME_CHINA

@Composable
fun ZonesDefineDescriptionScreen (
    onWechatClick: () -> Unit,
    onBackClick: () -> Unit,
    zonesDefineDescriptionType: ZonesDefineDescriptionType,
    modifier: Modifier = Modifier,
) {
    Scaffold(modifier = modifier, topBar = {
        ZonesDescriptionTopBar(
            title = stringResource(id = zonesDefineDescriptionType.titleResId),
            onBackClick = onBackClick
        )
    }) { paddingValues ->
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .background(MaterialTheme.colorScheme.surface)
                .padding(paddingValues)
        ) {
            item {
                Text(
                    modifier = Modifier.padding(MaterialTheme.spacing.medium),
                    text = stringResource(zonesDefineDescriptionType.prefaceTextId),
                    color = MaterialTheme.colorScheme.nearBlack,
                    style = MaterialTheme.typography.body
                )

                Image(
                    contentScale = ContentScale.Fit,
                    modifier = Modifier.padding(horizontal = MaterialTheme.spacing.medium)
                        .fillMaxWidth()
                        .aspectRatio(16f / 9f),
                    painter = painterResource(zonesDefineDescriptionType.imageResId),
                    contentDescription = null,
                )
            }
            items(zonesDefineDescriptionType.zonesDefineDescriptionItems) {
                HelpItem(
                    title = stringResource(id = it.titleResId),
                    content = stringResource(id = it.contentResId),
                    titleColor = colorResource(id = it.titleColorResId),
                    modifier = Modifier.padding(top = MaterialTheme.spacing.large)
                )
            }

            if (BuildConfig.FLAVOR_store == STORE_NAME_CHINA) {
                item {
                    WeChatInfoRow(onWechatClick = onWechatClick)
                }
            } else {
                item {
                    Spacer(modifier = Modifier.height(MaterialTheme.spacing.large))
                }
            }
        }
    }
}

@Composable
private fun HelpItem(title: String, content: String, titleColor: Color, modifier: Modifier = Modifier) {
    Column(modifier = modifier.padding(horizontal = MaterialTheme.spacing.medium,)) {
        Text(
            text = title,
            color = titleColor,
            style = MaterialTheme.typography.bodyLargeBold
        )
        Text(
            modifier = Modifier.padding(top = MaterialTheme.spacing.xsmall),
            text = content,
            color = MaterialTheme.colorScheme.nearBlack,
            style = MaterialTheme.typography.body
        )
    }
}

@Preview
@Composable
private fun ZonesDefineDescriptionPreview() {
    ZonesDefineDescriptionScreen(onWechatClick = {}, onBackClick = {}, zonesDefineDescriptionType = ZonesDefineDescriptionType.MAX_HR_ZONES_TYPE)
}
