package com.stt.android.home.settings

import android.app.Application
import com.stt.android.di.initializer.AppInitializer
import com.stt.android.home.settings.WatchSettingsContentProvider.Companion.HR_INTENSITY_ZONES
import com.stt.android.home.settings.hrintensityzones.HrIntensityZonesContentObserver
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class WatchSettingsObserverInitializer @Inject constructor(
    private val hrIntensityZonesContentObserver: HrIntensityZonesContentObserver,
) : AppInitializer {

    override fun init(app: Application) {
        app.contentResolver.registerContentObserver(
            HR_INTENSITY_ZONES,
            true,
            hrIntensityZonesContentObserver
        )
    }
}
