package com.stt.android.home.dashboard.activitydata

import com.stt.android.domain.activitydata.ActivityDataType

interface BottomSheetCancelListener {

    /**
     * Invoked when a bottomsheet dialog is canceled
     * @param activityDataType activity data type with default value which is
     * [ActivityDataType.DEFAULT_ACTIVITY_DATA_VALUE]] and a goal which was set in
     * dialog.
     * @param valueChanged was value actually changed
     */
    fun onBottomSheetCancel(activityDataType: ActivityDataType, valueChanged: Boolean)
}
