package com.stt.android.home.settings

import androidx.activity.viewModels
import androidx.appcompat.app.AlertDialog
import com.stt.android.R
import com.stt.android.watch.sendlog.SendLogViewModel
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class SettingsActivityWithLogWarning : SettingsActivity() {
    private val sendLogViewModel by viewModels<SendLogViewModel>()

    override fun onSupportNavigateUp(): Boolean {
        if (supportFragmentManager.backStackEntryCount == 0
            && sendLogViewModel.isSendingLog()
        ) {
            showExitDialog()
        } else {
            onBackPressed()
        }
        return true
    }

    private fun showExitDialog() {
        AlertDialog.Builder(this)
            .setTitle(R.string.assessment_attention)
            .setMessage(R.string.settings_exit_tips)
            .setPositiveButton(R.string.exit) { _, _ ->
                finish()
            }
            .setNegativeButton(R.string.cancel, null)
            .show()
    }
}
