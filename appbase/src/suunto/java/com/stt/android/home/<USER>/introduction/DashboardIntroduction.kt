package com.stt.android.home.dashboardv2.introduction

import com.stt.android.R
import com.stt.android.tutorial.api.model.Tutorial

internal fun createDashboardTutorial(): List<Tutorial> = listOf(
    Tutorial(
        image = R.drawable.new_home_onboarding_intro_1,
        video = null,
        title = R.string.new_home_onboarding_title_1,
        description = R.string.new_home_onboarding_description_1,
        primaryButton = R.string.new_home_onboarding_button_1,
        secondaryButton = null,
    ),
    Tutorial(
        image = null,
        video = R.raw.new_home_onboarding_intro_2,
        title = R.string.new_home_onboarding_title_2,
        description = R.string.new_home_onboarding_description_2,
        primaryButton = null,
        secondaryButton = null,
    ),
    Tutorial(
        image = null,
        video = R.raw.new_home_onboarding_intro_3,
        title = R.string.new_home_onboarding_title_3,
        description = R.string.new_home_onboarding_description_3,
        primaryButton = null,
        secondaryButton = null,
    ),
    Tutorial(
        image = null,
        video = R.raw.new_home_onboarding_intro_4,
        title = R.string.new_home_onboarding_title_4,
        description = R.string.new_home_onboarding_description_4,
        primaryButton = null,
        secondaryButton = null,
    ),
    Tutorial(
        image = null,
        video = R.raw.new_home_onboarding_intro_5,
        title = R.string.new_home_onboarding_title_5,
        description = R.string.new_home_onboarding_description_5,
        primaryButton = null,
        secondaryButton = null,
    ),
    Tutorial(
        image = null,
        video = R.raw.new_home_onboarding_intro_6,
        title = R.string.new_home_onboarding_title_6,
        description = R.string.new_home_onboarding_description_6,
        primaryButton = R.string.new_home_onboarding_button_6,
        secondaryButton = null,
    ),
)
