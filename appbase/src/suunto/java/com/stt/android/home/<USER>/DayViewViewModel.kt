package com.stt.android.home.dayviewv2

import android.content.Context
import android.content.SharedPreferences
import androidx.core.content.edit
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.stt.android.R
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.data.RELEASED_YEAR
import com.stt.android.data.toEpochMilli
import com.stt.android.di.DayViewPreferences
import com.stt.android.home.dayviewv2.usecase.CreateDayViewDataUseCase
import com.stt.android.ui.utils.TextFormatter
import com.stt.android.utils.CalendarProvider
import com.stt.android.utils.CalendarUtils
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch
import java.time.LocalDate
import java.time.ZoneId
import java.time.temporal.ChronoUnit
import java.time.temporal.TemporalAdjusters
import javax.inject.Inject

@HiltViewModel
class DayViewViewModel @Inject constructor(
    @field:ApplicationContext private val context: Context,
    private val createDayViewDataUseCase: CreateDayViewDataUseCase,
    @field:DayViewPreferences private val dayViewPreferences: SharedPreferences,
    private val calendarProvider: CalendarProvider,
    private val dispatchers: CoroutinesDispatchers,
    savedStateHandle: SavedStateHandle,
) : ViewModel() {
    private val initialDate = savedStateHandle.get<LocalDate>(EXTRA_INITIAL_DATE)!!

    private val today = LocalDate.now()
    private val firstDate = LocalDate.of(RELEASED_YEAR, 1, 1)
    private val firstDayOfTheWeek = calendarProvider.getFirstDayOfWeek()
    private val maxPage = ChronoUnit.DAYS.between(firstDate, today).toInt() + 1

    private val _uiState = MutableStateFlow<DayViewUiState>(DayViewUiState.Initial)
    val uiState: StateFlow<DayViewUiState> = _uiState

    val currentDate: LocalDate?
        get() = (_uiState.value as? DayViewUiState.Loaded)?.date

    init {
        selectDate(initialDate)
    }

    fun onDateSelected(date: LocalDate) = selectDate(date)

    fun onPageSelected(pageIndex: Int) = selectDate(firstDate.plusDays(pageIndex.toLong()))

    fun onExpandUpdated(expandable: DayViewExpandable, expanded: Boolean) {
        dayViewPreferences.edit { putBoolean(expandable.key, expanded) }
    }

    private fun selectDate(date: LocalDate) {
        if (date < firstDate || date > today) return

        viewModelScope.launch(dispatchers.io) {
            val weekStartDate = date.with(TemporalAdjusters.previousOrSame(firstDayOfTheWeek))
            val pageIndex = ChronoUnit.DAYS.between(firstDate, date).toInt()
            DayViewUiState.Loaded(
                title = date.formatTitle(),
                date = date,
                weekStartDate = weekStartDate,
                weekDayLabels = CalendarUtils.buildDayOfWeekLabels(
                    date,
                    calendarProvider.getDayOfWeekField(),
                ),
                weekDayStates = buildWeekDayStates(date, weekStartDate),
                pageIndex = pageIndex,
                pageCount = maxPage,
                dayViewDataMap = listOf(pageIndex - 1, pageIndex, pageIndex + 1)
                    .filter { it in 0 until maxPage }
                    .associateWith {
                        // TODO: should not await
                        createDayViewDataUseCase(firstDate.plusDays(it.toLong()))
                            .stateIn(viewModelScope)
                    },
            ).let { _uiState.tryEmit(it) }
        }
    }

    private fun LocalDate.formatTitle() = when (this) {
        today -> context.getString(R.string.today)
        today.minusDays(1L) -> context.getString(R.string.yesterday)
        else -> TextFormatter.formatDate(
            context,
            atStartOfDay(ZoneId.systemDefault()).toEpochMilli(),
        )
    }

    private fun buildWeekDayStates(
        selectedDate: LocalDate,
        weekStartDate: LocalDate
    ) = (0 until 7).map { index ->
        val date = weekStartDate.plusDays(index.toLong())
        when {
            date == selectedDate -> WeekDayState.Selected
            date < firstDate || date > today -> WeekDayState.Disabled
            else -> WeekDayState.Normal
        }
    }

    companion object {
        const val EXTRA_INITIAL_DATE = "initial_date"
    }
}
