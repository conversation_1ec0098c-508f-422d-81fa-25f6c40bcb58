package com.stt.android.home.dashboardnew.customization

import com.stt.android.R
import com.stt.android.di.AllAvailableWidgetTypes
import com.stt.android.di.PremiumRequiredWidgetTypes
import com.stt.android.domain.user.subscription.IsSubscribedToPremiumUseCase
import com.stt.android.home.dashboard.widget.WidgetType
import com.stt.android.home.dashboard.widget.customization.CheckWidgetPlacementAllowedUseCase
import com.stt.android.home.dashboard.widget.customization.SelectedDashboardWidgetsRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
class SelectNewDashboardWidgetDialogViewModel @Inject constructor(
    selectedDashboardWidgetsRepository: SelectedDashboardWidgetsRepository,
    isSubscribedToPremiumUseCase: IsSubscribedToPremiumUseCase,
    checkWidgetPlacementAllowedUseCase: CheckWidgetPlacementAllowedUseCase,
    @AllAvailableWidgetTypes allAvailableWidgetTypes: Set<WidgetType>,
    @PremiumRequiredWidgetTypes premiumRequiredWidgetTypes: Set<WidgetType>
) : BaseSelectNewDashboardWidgetDialogViewModel(
    selectedDashboardWidgetsRepository,
    isSubscribedToPremiumUseCase,
    checkWidgetPlacementAllowedUseCase,
    allAvailableWidgetTypes,
    premiumRequiredWidgetTypes
) {

    override fun getDashboardWidgetInfo(widgetType: WidgetType, isSubscribedToPremium: Boolean): DashboardWidgetInfo? = when (widgetType) {
        WidgetType.CALORIES -> DashboardWidgetInfo(
            widgetType = widgetType,
            nameRes = R.string.dashboard_widget_calories_name,
            descriptionRes = R.string.dashboard_widget_description_calories,
            previewImageRes = R.drawable.dashboard_widget_preview_calories,
            availableWithAllDevices = false,
            showPremiumRequired = !isSubscribedToPremium && premiumOnlyWidgetTypes.contains(widgetType)
        )
        WidgetType.RESOURCES -> DashboardWidgetInfo(
            widgetType = widgetType,
            nameRes = R.string.dashboard_widget_resources_name,
            descriptionRes = R.string.dashboard_widget_description_resources,
            previewImageRes = R.drawable.dashboard_widget_preview_resources,
            availableWithAllDevices = false,
            showPremiumRequired = !isSubscribedToPremium && premiumOnlyWidgetTypes.contains(widgetType)
        )
        WidgetType.SLEEP -> DashboardWidgetInfo(
            widgetType = widgetType,
            nameRes = R.string.dashboard_widget_sleep_name,
            descriptionRes = R.string.dashboard_widget_description_sleep,
            previewImageRes = R.drawable.dashboard_widget_preview_sleep,
            availableWithAllDevices = false,
            showPremiumRequired = !isSubscribedToPremium && premiumOnlyWidgetTypes.contains(widgetType)
        )
        WidgetType.SLEEP_HRV -> DashboardWidgetInfo(
            widgetType = widgetType,
            nameRes = R.string.sleep_hrv_title,
            descriptionRes = R.string.dashboard_widget_sleep_hrv_description,
            previewImageRes = R.drawable.dashboard_widget_preview_sleep_hrv,
            availableWithAllDevices = false,
            showPremiumRequired = !isSubscribedToPremium && premiumOnlyWidgetTypes.contains(widgetType)
        )
        WidgetType.STEPS -> DashboardWidgetInfo(
            widgetType = widgetType,
            nameRes = R.string.dashboard_widget_steps_name,
            descriptionRes = R.string.dashboard_widget_description_steps,
            previewImageRes = R.drawable.dashboard_widget_preview_steps,
            availableWithAllDevices = false,
            showPremiumRequired = !isSubscribedToPremium && premiumOnlyWidgetTypes.contains(widgetType)
        )
        WidgetType.MINIMUM_HEART_RATE -> DashboardWidgetInfo(
            widgetType = widgetType,
            nameRes = R.string.dashboard_widget_minimum_heart_rate_selection_list_name,
            descriptionRes = R.string.dashboard_widget_description_minimum_heart_rate,
            previewImageRes = R.drawable.dashboard_widget_preview_minimum_heart_rate,
            availableWithAllDevices = false,
            showPremiumRequired = !isSubscribedToPremium && premiumOnlyWidgetTypes.contains(widgetType)
        )
        else -> super.getDashboardWidgetInfo(widgetType, isSubscribedToPremium)
    }
}
