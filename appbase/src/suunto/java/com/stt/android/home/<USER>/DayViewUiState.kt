package com.stt.android.home.dayviewv2

import androidx.annotation.ColorRes
import androidx.annotation.DrawableRes
import androidx.annotation.PluralsRes
import androidx.annotation.StringRes
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.utils.STTConstants.DayViewPreferences.KEY_BLOOD_OXYGEN_EXPANDED
import com.stt.android.utils.STTConstants.DayViewPreferences.KEY_CALORIES_EXPANDED
import com.stt.android.utils.STTConstants.DayViewPreferences.KEY_HEART_RATE_EXPANDED
import com.stt.android.utils.STTConstants.DayViewPreferences.KEY_RESOURCES_EXPANDED
import com.stt.android.utils.STTConstants.DayViewPreferences.KEY_SLEEP_EXPANDED
import com.stt.android.utils.STTConstants.DayViewPreferences.KEY_STEPS_EXPANDED
import kotlinx.coroutines.flow.StateFlow
import java.time.LocalDate

sealed class DayViewUiState {
    data object Initial : DayViewUiState()

    data class Loaded(
        val title: String,
        val date: LocalDate,
        val weekStartDate: LocalDate,
        val weekDayLabels: List<String>,
        val weekDayStates: List<WeekDayState>,
        val pageIndex: Int,
        val pageCount: Int,
        val dayViewDataMap: Map<Int, StateFlow<DayViewData>>
    ) : DayViewUiState()
}

sealed class WeekDayState {
    data object Normal : WeekDayState()
    data object Selected : WeekDayState()
    data object Disabled : WeekDayState()
}

sealed class DayViewExpandable(val key: String) {
    data object Hr : DayViewExpandable(KEY_HEART_RATE_EXPANDED)
    data object SpO2 : DayViewExpandable(KEY_BLOOD_OXYGEN_EXPANDED)
    data object Resources : DayViewExpandable(KEY_RESOURCES_EXPANDED)
    data object Sleep : DayViewExpandable(KEY_SLEEP_EXPANDED)
    data object Steps : DayViewExpandable(KEY_STEPS_EXPANDED)
    data object Calories : DayViewExpandable(KEY_CALORIES_EXPANDED)
}

data class WorkoutStatistic(
    @field:DrawableRes val iconRes: Int,
    @field:StringRes val labelRes: Int?,
    @field:PluralsRes val labelQuantityRes: Int? = null,
    @field:StringRes val unitRes: Int?,
    val value: String,
    val quantity: Int = 0,
)

data class TrendChartSeries(
    @field:ColorRes val colorRes: Int,
    val x: List<Long>,
    val y: List<Double>,
)

data class TrendChartAxisRange(
    val minX: Long,
    val maxX: Long,
    val minY: Double,
    val maxY: Double,
)

data class TrendChartData(
    val series: List<TrendChartSeries>,
    val axisRange: TrendChartAxisRange,
)

data class HeartRateViewData(
    val max: Int,
    val min: Int,
    val expanded: Boolean,
    val chartData: TrendChartData?,
)

data class SpO2ViewData(
    val average: Int,
    val averageAltitude: Int?,
    @field:StringRes val altitudeUnitRes: Int,
    val expanded: Boolean,
    val chartData: TrendChartData?,
)

data class StepsViewData(
    val steps: Int,
    val goal: Int,
    val expanded: Boolean,
    val chartData: TrendChartData?,
)

data class CaloriesViewData(
    val activeCalories: Int,
    val totalCalories: Int,
    val goal: Int,
    val expanded: Boolean,
    val chartData: TrendChartData?,
)

data class DayViewData(
    val date: LocalDate,
    val statistics: List<WorkoutStatistic>,
    val isPeriodDay: Boolean,
    val workouts: List<WorkoutHeader>,
    val heartRateViewData: HeartRateViewData?,
    val spO2ViewData: SpO2ViewData?,
    val resourcesChartData: TrendChartData?,
    val stepsViewData: StepsViewData?,
    val caloriesViewData: CaloriesViewData?,
)
