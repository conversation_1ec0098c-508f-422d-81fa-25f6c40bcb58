package com.stt.android.home.dayviewv2.widget

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.pluralStringResource
import androidx.compose.ui.res.stringResource
import com.stt.android.compose.theme.material3.body
import com.stt.android.compose.theme.material3.bodyMegaBold
import com.stt.android.compose.theme.spacing
import com.stt.android.home.dayviewv2.WorkoutStatistic

@Composable
fun WorkoutStatisticView(
    statistic: WorkoutStatistic,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier = modifier.padding(MaterialTheme.spacing.medium),
        horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.medium),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Icon(
            painter = painterResource(statistic.iconRes),
            contentDescription = null,
            tint = Color.Unspecified,
        )
        Column(
            modifier = Modifier.weight(1f),
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.xsmall),
        ) {
            Text(
                text = statistic.value,
                style = MaterialTheme.typography.bodyMegaBold,
                color = MaterialTheme.colorScheme.onSurface,
            )
            Text(
                text = listOfNotNull(
                    statistic.labelRes?.let {
                        stringResource(it)
                    },
                    statistic.labelQuantityRes?.let {
                        pluralStringResource(it, statistic.quantity)
                    },
                    statistic.unitRes?.let {
                        stringResource(it)
                    },
                ).joinToString(" "),
                style = MaterialTheme.typography.body,
                color = MaterialTheme.colorScheme.secondary,
            )
        }
    }
}
