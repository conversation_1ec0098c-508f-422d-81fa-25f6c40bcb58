package com.stt.android.home.dayviewv2.usecase

import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.menstrualcycle.ShowCycleDayHelper
import com.stt.android.menstrualcycle.domain.MenstrualCycleType
import com.stt.android.menstrualcycle.domain.ObservableMenstrualCycleListUseCase
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.map
import java.time.LocalDate
import javax.inject.Inject

class FetchDailyMenstrualCycleUseCase @Inject constructor(
    private val observableMenstrualCycleListUseCase: ObservableMenstrualCycleListUseCase,
    private val showCycleDayHelper: ShowCycleDayHelper,
    private val dispatchers: CoroutinesDispatchers,
) {
    operator fun invoke(date: LocalDate): Flow<Pair<Boolean, String?>> {
        return observableMenstrualCycleListUseCase(MenstrualCycleType.HISTORICAL)
            .map { menstrualCycles ->
                val periodDay = menstrualCycles.any { it.includedDates.contains(date) }
                val cycleDays = showCycleDayHelper.getFromMenstrualCycleDays(menstrualCycles, date)
                Pair(periodDay, cycleDays)
            }
            .flowOn(dispatchers.io)
    }
}
