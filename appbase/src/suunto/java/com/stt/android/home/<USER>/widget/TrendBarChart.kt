package com.stt.android.home.dayviewv2.widget

import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.unit.dp
import com.patrykandpatrick.vico.compose.cartesian.CartesianChartHost
import com.patrykandpatrick.vico.compose.cartesian.layer.grouped
import com.patrykandpatrick.vico.compose.cartesian.layer.rememberColumnCartesianLayer
import com.patrykandpatrick.vico.compose.cartesian.rememberCartesianChart
import com.patrykandpatrick.vico.compose.cartesian.rememberVicoZoomState
import com.patrykandpatrick.vico.compose.common.component.rememberLineComponent
import com.patrykandpatrick.vico.compose.common.fill
import com.patrykandpatrick.vico.core.cartesian.Zoom
import com.patrykandpatrick.vico.core.cartesian.axis.Axis
import com.patrykandpatrick.vico.core.cartesian.data.CartesianChartModelProducer
import com.patrykandpatrick.vico.core.cartesian.data.columnSeries
import com.patrykandpatrick.vico.core.cartesian.layer.ColumnCartesianLayer
import com.patrykandpatrick.vico.core.common.shape.CorneredShape
import com.stt.android.home.dayviewv2.TrendChartData

@Composable
fun TrendBarChart(
    chartData: TrendChartData,
    modifier: Modifier = Modifier,
) {
    val modelProducer = remember { CartesianChartModelProducer() }
    LaunchedEffect(Unit) {
        modelProducer.runTransaction {
            columnSeries { chartData.series.forEach { series(it.x, it.y) } }
        }
    }

    CartesianChartHost(
        modifier = modifier,
        chart = rememberCartesianChart(
            rememberColumnCartesianLayer(
                columnProvider = ColumnCartesianLayer.ColumnProvider.series(
                    columns = chartData.series.map {
                        rememberLineComponent(
                            fill = fill(color = colorResource(it.colorRes)),
                            thickness = 16.dp,
                            shape = CorneredShape.rounded(topLeftPercent = 50, topRightPercent = 50)
                        )
                    }
                ),
                rangeProvider = rememberFixedRangeProvider(chartData.axisRange),
                verticalAxisPosition = Axis.Position.Vertical.End,
                mergeMode = { ColumnCartesianLayer.MergeMode.grouped() },
                columnCollectionSpacing = 16.dp,
            ),
            startAxis = null,
            endAxis = rememberDailyEndAxis(),
            bottomAxis = rememberDailyBottomAxis(),
            getXStep = { 100.0 * 6.0 }, // 6 hours
        ),
        modelProducer = modelProducer,
        zoomState = rememberVicoZoomState(
            zoomEnabled = false,
            initialZoom = Zoom.Content,
        ),
    )
}
