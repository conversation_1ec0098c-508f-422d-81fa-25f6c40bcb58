package com.stt.android.home.dayviewv2.usecase

import com.amersports.formatter.Unit
import com.amersports.formatter.unit.jscience.JScienceUnitConverter
import com.stt.android.R
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.home.dayviewv2.CaloriesViewData
import com.stt.android.home.dayviewv2.DayViewData
import com.stt.android.home.dayviewv2.HeartRateViewData
import com.stt.android.home.dayviewv2.SpO2ViewData
import com.stt.android.home.dayviewv2.StepsViewData
import com.stt.android.home.dayviewv2.WorkoutStatistic
import com.stt.android.infomodel.SummaryItem
import com.stt.android.mapping.InfoModelFormatter
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.flowOn
import java.time.LocalDate
import javax.inject.Inject
import com.stt.android.core.R as CR

class CreateDayViewDataUseCase @Inject constructor(
    private val fetchDailyWorkoutsUseCase: FetchDailyWorkoutsUseCase,
    private val fetchDailyMenstrualCycleUseCase: FetchDailyMenstrualCycleUseCase,
    private val fetchDailyHeartRateUseCase: FetchDailyHeartRateUseCase,
    private val fetchDailySpO2UseCase: FetchDailySpO2UseCase,
    private val fetchDailyStepsUseCase: FetchDailyStepsUseCase,
    private val fetchDailyCaloriesUseCase: FetchDailyCaloriesUseCase,
    private val infoModelFormatter: InfoModelFormatter,
    private val unitConverter: JScienceUnitConverter,
    private val dispatchers: CoroutinesDispatchers,
) {
    @Suppress("UNCHECKED_CAST")
    operator fun invoke(date: LocalDate): Flow<DayViewData> {
        return combine(
            fetchDailyWorkoutsUseCase(date),
            fetchDailyMenstrualCycleUseCase(date),
            fetchDailyHeartRateUseCase(date),
            fetchDailySpO2UseCase(date),
            fetchDailyStepsUseCase(date),
            fetchDailyCaloriesUseCase(date),
        ) {
            val workouts = it[0] as List<WorkoutHeader>
            val (isPeriodDay, menstrualCycleDays) = it[1] as Pair<Boolean, String?>
            val heartRateViewData = it[2] as HeartRateViewData?
            val spO2ViewData = it[3] as SpO2ViewData?
            val stepsViewData = it[4] as StepsViewData?
            val caloriesViewData = it[5] as CaloriesViewData?
            DayViewData(
                date = date,
                statistics = buildStatistics(workouts, menstrualCycleDays),
                isPeriodDay = isPeriodDay,
                workouts = workouts,
                heartRateViewData = heartRateViewData,
                spO2ViewData = spO2ViewData,
                resourcesChartData = null,
                stepsViewData = stepsViewData,
                caloriesViewData = caloriesViewData,
            )
        }.flowOn(dispatchers.io)
    }

    private fun buildStatistics(
        workouts: List<WorkoutHeader>,
        menstrualCycleDays: String?,
    ) = buildList {
        add(
            WorkoutStatistic(
                iconRes = R.drawable.ic_statistic_activities,
                labelRes = null,
                labelQuantityRes = R.plurals.workouts_plural_without_quantity,
                unitRes = null,
                value = workouts.size.toString(),
                quantity = workouts.size,
            )
        )
        val durationValue = runCatching {
            val duration = workouts.sumOf { it.totalTime }
            infoModelFormatter.formatAccumulatedTotalDuration(duration)
        }.getOrNull()
        add(
            WorkoutStatistic(
                iconRes = R.drawable.ic_statistic_duration,
                labelRes = R.string.duration,
                unitRes = CR.string.hour,
                value = durationValue ?: "--",
            )
        )
        val energyValue = runCatching {
            val joules = unitConverter.convert(
                workouts.sumOf { it.energyConsumption },
                Unit.KCAL,
                Unit.J,
            )
            infoModelFormatter.formatValue(SummaryItem.ENERGY, joules)
        }.getOrNull()
        add(
            WorkoutStatistic(
                iconRes = R.drawable.ic_statistic_calories,
                labelRes = R.string.energy,
                unitRes = CR.string.kcal,
                value = energyValue?.value ?: "--",
            )
        )
        menstrualCycleDays?.let {
            add(
                WorkoutStatistic(
                    iconRes = R.drawable.ic_statistic_menstrual_cycle,
                    labelRes = R.string.day_view_cycle_day,
                    unitRes = null,
                    value = it,
                )
            )
        }
    }
}
