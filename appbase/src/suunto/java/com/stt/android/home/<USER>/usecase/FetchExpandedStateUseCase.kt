package com.stt.android.home.dayviewv2.usecase

import android.content.SharedPreferences
import android.content.SharedPreferences.OnSharedPreferenceChangeListener
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.di.DayViewPreferences
import com.stt.android.home.dayviewv2.DayViewExpandable
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.flow.flowOn
import javax.inject.Inject

class FetchExpandedStateUseCase @Inject constructor(
    @field:DayViewPreferences private val dayViewPreferences: SharedPreferences,
    private val dispatchers: CoroutinesDispatchers,
) {
    operator fun invoke(expandable: DayViewExpandable, defValue: Boolean = true) = callbackFlow {
        send(dayViewPreferences.getBoolean(expandable.key, defValue))

        val listener = OnSharedPreferenceChangeListener { prefs, changedKey ->
            if (changedKey == expandable.key) {
                trySend(prefs.getBoolean(expandable.key, defValue))
            }
        }

        dayViewPreferences.registerOnSharedPreferenceChangeListener(listener)

        awaitClose {
            dayViewPreferences.unregisterOnSharedPreferenceChangeListener(listener)
        }
    }.flowOn(dispatchers.io)
}
