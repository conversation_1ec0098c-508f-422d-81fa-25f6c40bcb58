package com.stt.android.home.settings

import android.content.Context
import android.util.AttributeSet
import com.stt.android.R
import com.stt.android.STTApplication
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.UserSettingsController
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.home.settings.userprofile.UserProfileUseCase
import com.stt.android.home.settings.userprofile.observeUserProfileCapabilities
import com.stt.android.home.settings.wheel.WheelFragmentColumn
import com.stt.android.home.settings.wheel.WheelFragmentData
import com.stt.android.watch.MissingCurrentWatchException
import com.stt.android.watch.SuuntoWatchModel
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject
import kotlin.math.max

/**
 * This preference is Visible for Dilu.
 */
class RestHeartPickerDialogPreference @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = android.R.attr.preferenceStyle,
    defStyleRes: Int = defStyleAttr
) : BaseHeartPickerDialogPreference(context, attrs, defStyleAttr, defStyleRes) {
    @Inject
    lateinit var suuntoWatchModel: SuuntoWatchModel
    @Inject
    lateinit var userProfileUseCase: UserProfileUseCase

    @Inject
    lateinit var userSettingsController: UserSettingsController

    @Inject
    lateinit var dispatchers: CoroutinesDispatchers

    private val coroutineScope: CoroutineScope
    private var observerJob: Job? = null
    private var setWatchJob: Job? = null

    init {
        STTApplication.getComponent().inject(this)
        coroutineScope = CoroutineScope(SupervisorJob() + dispatchers.main)
    }

    override val defaultWheelData: WheelFragmentData
        get() = WheelFragmentData(
            title = title.toString(),
            columns = listOf(getDefaultWheelFragmentColumn())
        )

    private fun getDefaultWheelFragmentColumn(): WheelFragmentColumn {
        val restHrMaxDefaultValue = getHeartRangeMaxValue()
        val heartRange = (REST_HR_MIN_DEFAULT_VALUE..restHrMaxDefaultValue)
        val defaultIndex = max(heartRange.indexOf(REST_HR_DEFAULT_VALUE), 0)
        return WheelFragmentColumn(
            textList = heartRange.map { it.toString() },
            defaultIndex = defaultIndex,
            unit = context.getString(R.string.heart_unit)
        )
    }

    private fun getHeartRangeMaxValue(): Int {
        val heartRangeMaxValue = userSettingsController.settings.hrMaximum - 21
        val restHrMaxDefaultValue = if (heartRangeMaxValue <= REST_HR_MIN_DEFAULT_VALUE) {
            REST_HR_DEFAULT_VALUE
        } else {
            heartRangeMaxValue
        }
        return restHrMaxDefaultValue
    }

    override fun onAttached() {
        super.onAttached()
        observerJob =
            observeUserProfileCapabilities(userProfileUseCase, coroutineScope, dispatchers)

    }

    override fun onDetached() {
        observerJob?.cancel()
        setWatchJob?.cancel()
        super.onDetached()
    }

    override fun persistString(value: String): Boolean {
        if (!super.persistString(value)) {
            return false
        }
        Timber.d("RestHeartPickerDialogPreference persistString:%s", value)
        setWatchJob = coroutineScope.launch(dispatchers.io) {
            runSuspendCatching {
                userProfileUseCase.updateUserRestHeart(value.toInt())
            }.onSuccess {
                Timber.d("setUserRestHR successful.")
            }.onFailure { e ->
                if (e is MissingCurrentWatchException) {
                    Timber.i("Missing current watch")
                } else {
                    Timber.w(e, "setUserRestHR error.")
                }
            }
        }
        return true
    }

    companion object {
        private const val REST_HR_MIN_DEFAULT_VALUE = 30
        private const val REST_HR_DEFAULT_VALUE = 50
    }
}
