package com.stt.android.home.dayviewv2.widget

import android.annotation.SuppressLint
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.unit.dp
import com.patrykandpatrick.vico.compose.cartesian.axis.rememberAxisGuidelineComponent
import com.patrykandpatrick.vico.compose.cartesian.axis.rememberAxisLabelComponent
import com.patrykandpatrick.vico.compose.cartesian.axis.rememberAxisLineComponent
import com.patrykandpatrick.vico.compose.cartesian.axis.rememberBottom
import com.patrykandpatrick.vico.compose.cartesian.axis.rememberEnd
import com.patrykandpatrick.vico.compose.common.fill
import com.patrykandpatrick.vico.compose.common.insets
import com.patrykandpatrick.vico.core.cartesian.CartesianMeasuringContext
import com.patrykandpatrick.vico.core.cartesian.axis.Axis
import com.patrykandpatrick.vico.core.cartesian.axis.HorizontalAxis
import com.patrykandpatrick.vico.core.cartesian.axis.VerticalAxis
import com.patrykandpatrick.vico.core.cartesian.data.CartesianLayerRangeProvider
import com.patrykandpatrick.vico.core.cartesian.data.CartesianValueFormatter
import com.stt.android.compose.theme.cloudyGrey
import com.stt.android.compose.theme.mediumGrey
import com.stt.android.home.dayviewv2.TrendChartAxisRange
import java.time.LocalDateTime
import java.time.OffsetDateTime

@Composable
fun rememberDailyBottomAxis() = HorizontalAxis.rememberBottom(
    label = rememberAxisLabelComponent(
        color = MaterialTheme.colorScheme.onSurface,
        textSize = MaterialTheme.typography.bodySmall.fontSize,
        padding = insets(),
    ),
    valueFormatter = remember { TimeFormatter() },
    line = rememberAxisLineComponent(
        fill = fill(MaterialTheme.colorScheme.mediumGrey),
        thickness = 1.dp,
    ),
    guideline = null,
    itemPlacer = remember {
        HorizontalAxis.ItemPlacer.aligned(
            spacing = { 36 },
            addExtremeLabelPadding = true,
        )
    },
)

@SuppressLint("RestrictedApi")
@Composable
fun rememberDailyEndAxis() = VerticalAxis.rememberEnd(
    line = null,
    tick = null,
    guideline = rememberAxisGuidelineComponent(
        fill = fill(MaterialTheme.colorScheme.cloudyGrey),
        thickness = 1.dp,
    ),
    itemPlacer = remember { VerticalAxis.ItemPlacer.count(count = { 4 }) },
    valueFormatter = CartesianValueFormatter.Default,
)

@Composable
fun rememberFixedRangeProvider(axisRange: TrendChartAxisRange) = with(axisRange) {
    remember(this) {
        CartesianLayerRangeProvider.fixed(
            minX = minX.toDouble(),
            maxX = maxX.toDouble(),
            minY = minY,
            maxY = maxY,
        )
    }
}

private class TimeFormatter : CartesianValueFormatter {
    override fun format(
        context: CartesianMeasuringContext,
        value: Double,
        verticalAxisPosition: Axis.Position.Vertical?
    ): CharSequence = buildString {
        val zoneOffset = OffsetDateTime.now().offset
        val dateTime = LocalDateTime.ofEpochSecond(value.toLong(), 0, zoneOffset)
        append(dateTime.hour.toString().padStart(2, '0'))
        append(':')
        append(dateTime.minute.toString().padStart(2, '0'))
    }
}
