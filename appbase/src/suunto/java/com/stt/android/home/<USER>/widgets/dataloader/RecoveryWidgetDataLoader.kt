package com.stt.android.home.dashboardv2.widgets.dataloader

import android.content.Context
import com.stt.android.R
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.domain.diary.insights.FetchRecoveryStateScoreUseCase
import com.stt.android.home.dashboardv2.ui.widgets.common.generateWidgetTitle
import com.stt.android.home.dashboardv2.widgets.RecoveryWidgetInfo
import com.stt.android.utils.titleForWidget
import com.stt.android.utils.zoneColorRes
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.map
import java.time.LocalDate
import javax.inject.Inject

internal class RecoveryWidgetDataLoader @Inject constructor(
    private val context: Context,
    private val fetchRecoveryStateScoreUseCase: FetchRecoveryStateScoreUseCase,
    private val coroutinesDispatchers: CoroutinesDispatchers,
) : WidgetDataLoader<RecoveryWidgetInfo>() {
    override suspend fun load(param: Param): WidgetData<RecoveryWidgetInfo> {
        val today = LocalDate.now()
        val flow = fetchRecoveryStateScoreUseCase.invoke(
            fromDate = today,
            toDate = today,
            includeContributors = false,
        )
            .flowOn(coroutinesDispatchers.io)
            .map { list ->
                val recoveryStateScore =
                    list.lastOrNull()?.recoveryStateData?.takeIf { it.recoveryScore > 0 }
                recoveryStateScore?.let {
                    RecoveryWidgetInfo(
                        progress = recoveryStateScore.recoveryScore / 100f,
                        headerRes = R.string.dashboard_widget_recovery_name,
                        colorRes = R.color.recovery_state_recovering,
                        iconRes = R.drawable.ic_diary_tab_recovery,
                        title = generateWidgetTitle(recoveryStateScore.recoveryScore.toString(), "%"),
                        subtitle = context.getString(recoveryStateScore.recoveryZone.titleForWidget()),
                        progressColor = recoveryStateScore.recoveryZone.zoneColorRes(),
                    )
                } ?: RecoveryWidgetInfo(
                    progress = 0f,
                    headerRes = R.string.dashboard_widget_recovery_name,
                    colorRes = R.color.recovery_state_recovering,
                    iconRes = R.drawable.ic_diary_tab_recovery,
                    title = generateWidgetTitle("0", "%"),
                    subtitle = context.getString(R.string.widget_no_data_subtitle),
                    progressColor = R.color.transparent,
                )
            }
        return WidgetData(
            widgetType = param.type,
            editMode = param.editMode,
            widgetInfo = flow,
        )
    }
}
