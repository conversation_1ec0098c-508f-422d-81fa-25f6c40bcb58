package com.stt.android.home.dashboard.widget.goal

import android.content.SharedPreferences
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.WorkoutHeaderController
import com.stt.android.controllers.currentUserWorkoutUpdated
import com.stt.android.domain.goaldefinition.GoalDefinition
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.utils.CalendarProvider
import com.stt.android.utils.STTConstants
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onStart
import java.util.Calendar
import javax.inject.Inject
import kotlin.math.floor

class WatchWeeklyGoalWidgetDataFetcher @Inject constructor(
    private val currentUserController: CurrentUserController,
    private val workoutHeaderController: WorkoutHeaderController,
    private val calendarProvider: CalendarProvider,
    private val sharedPreferences: SharedPreferences,
    private val coroutinesDispatchers: CoroutinesDispatchers,
) : WeeklyGoalWidgetDataFetcher {
    override fun weeklyGoalWidgetDataFlow(): Flow<GoalWidgetData> {
        val (since, till) = getWeekPeriod()
        fun findWorkoutHeaders(): List<WorkoutHeader> {
            return workoutHeaderController.findWorkoutHeaders(currentUserController.username, null, since, till)
        }
        val workoutsFlow = workoutHeaderController.currentUserWorkoutUpdated
            .map { findWorkoutHeaders() }
            .onStart { emit(findWorkoutHeaders()) }
            .flowOn(coroutinesDispatchers.io)

        // Currently relies on the old full page goal wheel to trigger watch syncs and such
        val targetTimeFlow = callbackFlow {
            fun emitTargetTime() {
                val currentStoredTarget = sharedPreferences.getInt(
                    STTConstants.SuuntoPreferences.KEY_SUUNTO_WATCH_WEEKLY_TARGET_DURATION,
                    STTConstants.SuuntoPreferences.KEY_SUUNTO_WATCH_WEEKLY_TARGET_DURATION_DEFAULT
                )
                trySend(currentStoredTarget)
            }

            val sharedPrefsListener = SharedPreferences.OnSharedPreferenceChangeListener { _, key ->
                if (key == STTConstants.SuuntoPreferences.KEY_SUUNTO_WATCH_WEEKLY_TARGET_DURATION) {
                    emitTargetTime()
                }
            }

            sharedPreferences.registerOnSharedPreferenceChangeListener(sharedPrefsListener)
            emitTargetTime()

            awaitClose {
                sharedPreferences.unregisterOnSharedPreferenceChangeListener(sharedPrefsListener)
            }
        }

        return combine(
            workoutsFlow,
            targetTimeFlow
        ) { workouts, targetTime ->
            val achievedTime = floor(workouts.sumOf { it.totalTime }).toInt()

            GoalWidgetData(
                GoalDefinition(
                    id = 0,
                    userName = currentUserController.username,
                    type = GoalDefinition.Type.DURATION,
                    period = GoalDefinition.Period.WEEKLY,
                    target = targetTime
                ),
                achievedTime,
            )
        }
    }

    private fun getWeekPeriod(): Pair<Long, Long> {
        val startTime: Long
        val endTime: Long

        val calendar = calendarProvider.getCalendar()
        calendar.set(Calendar.HOUR_OF_DAY, 0)
        calendar.set(Calendar.MINUTE, 0)
        calendar.set(Calendar.SECOND, 0)
        calendar.set(Calendar.MILLISECOND, 0)
        calendar.set(Calendar.DAY_OF_WEEK, calendar.firstDayOfWeek)
        startTime = calendar.timeInMillis

        calendar.add(Calendar.DATE, 7)
        calendar.add(Calendar.MILLISECOND, -1)
        endTime = calendar.timeInMillis

        return Pair(startTime, endTime)
    }
}
