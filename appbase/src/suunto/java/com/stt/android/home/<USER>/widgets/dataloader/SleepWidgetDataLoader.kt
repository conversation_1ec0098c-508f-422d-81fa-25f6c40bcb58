package com.stt.android.home.dashboardv2.widgets.dataloader

import android.content.Context
import com.stt.android.domain.activitydata.goals.FetchSleepGoalUseCase
import com.stt.android.domain.diary.toLocalDate
import com.stt.android.domain.sleep.FetchSleepUseCase
import com.stt.android.home.dashboardv2.ui.widgets.common.NO_DATA_VALUE
import com.stt.android.home.dashboardv2.ui.widgets.common.formatWidgetDurationTitle
import com.stt.android.home.dashboardv2.ui.widgets.common.generateDurationTargetSubtitle
import com.stt.android.home.dashboardv2.widgets.Period
import com.stt.android.home.dashboardv2.widgets.SleepWidgetInfo
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.utils.iterator
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.map
import javax.inject.Inject

internal class SleepWidgetDataLoader @Inject constructor(
    private val context: Context,
    private val fetchSleepUseCase: FetchSleepUseCase,
    private val fetchSleepGoalUseCase: FetchSleepGoalUseCase,
    private val infoModelFormatter: InfoModelFormatter,
) : WidgetDataLoader<SleepWidgetInfo>() {
    override suspend fun load(param: Param): WidgetData<SleepWidgetInfo> {
        val period = Period.Last7Days
        val firstDay = period.beginDate
        val lastDay = period.endDate
        val flow = combine(
            fetchSleepGoalUseCase.fetchSleepGoal(),
            fetchSleepUseCase.fetchSleeps(firstDay, lastDay)
                .map { sleeps -> sleeps.groupBy { it.timestamp.toLocalDate() } },
        ) { sleepGoal, dailySleeps ->
            val sleepGoalInSeconds = sleepGoal.inWholeSeconds
            val nightSleepAndNaps = buildList {
                for (day in firstDay..lastDay) {
                    val sleepForDay = dailySleeps[day]?.firstOrNull()
                    val nightSleep = sleepForDay?.longSleep?.sleepDuration?.inWholeSeconds?.toFloat()
                    val nap = sleepForDay?.getMergedNap()?.duration?.inWholeSeconds?.toFloat()

                    if (nightSleep == null && nap == null) {
                        add(NO_SLEEP_VALUE)
                    } else {
                        add((nightSleep ?: 0f) to (nap ?: 0f))
                    }
                }
            }

            val lastNightSleep = nightSleepAndNaps.last()
            val lastNightSleepSeconds = (lastNightSleep.first + lastNightSleep.second).toDouble()
            val title = lastNightSleepSeconds.coerceAtLeast(0.0).formatWidgetDurationTitle(context)
            val subtitle = generateDurationTargetSubtitle(context, lastNightSleepSeconds - sleepGoalInSeconds, infoModelFormatter)
            val progresses = nightSleepAndNaps.map { (night, nap) ->
                if (night < 0 && nap < 0) {
                    NO_SLEEP_VALUE
                } else {
                    var nightProgress = night / sleepGoalInSeconds
                    var napProgress = nap / sleepGoalInSeconds
                    val totalProgress = nightProgress + napProgress
                    if (totalProgress > 1f) {
                        nightProgress /= totalProgress
                        napProgress /= totalProgress
                    }
                    nightProgress to napProgress
                }
            }
            SleepWidgetInfo(
                period = period,
                nightSleepAndNapProgresses = progresses,
                title = title,
                subtitle = subtitle,
            )
        }

        return WidgetData(
            widgetType = param.type,
            editMode = param.editMode,
            widgetInfo = flow,
        )
    }

    private companion object {
        val NO_SLEEP_VALUE: Pair<Float, Float> = NO_DATA_VALUE to NO_DATA_VALUE
    }
}
