package com.stt.android.home.dashboard.activitydata

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.view.WindowManager
import androidx.fragment.app.DialogFragment
import com.stt.android.R
import com.stt.android.databinding.TimeRangePickerFragmentBinding
/**
 * A DialogFragment that show 2 time pickers for range. One is start time. The other is end time.
 * todo get rid of TabHost
 */
class TimeRangePickerFragment : DialogFragment() {

    private var onTimeRangeSelectedListener: OnTimeRangeSelectedListener? = null

    private var binding: TimeRangePickerFragmentBinding? = null

    fun initialize(callback: OnTimeRangeSelectedListener) {
        onTimeRangeSelectedListener = callback
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = TimeRangePickerFragmentBinding.inflate(inflater, container, false)
        binding?.setTimeRangeButton?.setOnClickListener { onSetTimeRangeButtonClicked() }
        dialog?.window?.requestFeature(Window.FEATURE_NO_TITLE)
        setTabHost()
        setInitialValuesForTimePickers()
        return binding?.root
    }

    override fun onDestroyView() {
        super.onDestroyView()
        binding = null
    }

    private fun setInitialValuesForTimePickers() {
        arguments?.let {
            it.getParcelable<Bedtime>(TIME_PICKER_PRESET_INFO)?.apply {
                binding?.startTimePicker?.apply {
                    minuteInterval = minutesInterval
                    minute = startMinute
                    hour = startHour
                }
                binding?.endTimePicker?.apply {
                    minuteInterval = minutesInterval
                    minute = endMinute
                    hour = endHour
                }
            }
        }
    }

    override fun isCancelable(): Boolean {
        return false
    }

    private fun setTabHost() {
        binding?.tabHost?.apply {
            setup()
            val startDatePage = newTabSpec(START_TAB_TAG)
            startDatePage.setContent(R.id.startTimeGroup)
            startDatePage.setIndicator(getString(R.string.all_from))
            addTab(startDatePage)
            val endDatePage = newTabSpec(END_TAB_FRAGMENT)
            endDatePage.setContent(R.id.endTimeGroup)
            endDatePage.setIndicator(getString(R.string.all_to))
            addTab(endDatePage)
        }
    }

    override fun onStart() {
        super.onStart()
        dialog?.window?.setLayout(
            WindowManager.LayoutParams.MATCH_PARENT,
            WindowManager.LayoutParams.WRAP_CONTENT
        )
    }

    private fun onSetTimeRangeButtonClicked() {
        dismiss()
        // Manually entered hour or minute from keyboard is not saved if input focus is not cleared
        binding?.run {
            startTimePicker.clearFocus()
            endTimePicker.clearFocus()
            onTimeRangeSelectedListener?.onTimeRangeSelected(
                Bedtime(
                    startTimePicker.hour,
                    startTimePicker.minute,
                    endTimePicker.hour,
                    endTimePicker.minute
                )
            )
        }
    }

    interface OnTimeRangeSelectedListener {
        fun onTimeRangeSelected(bedtime: Bedtime)
    }

    companion object {
        private const val START_TAB_TAG = "start"
        private const val END_TAB_FRAGMENT = "end"
        const val TIME_PICKER_PRESET_INFO = "TimeRangePickerFragment.TIME_PICKER_PRESET_INFO"

        fun newInstance(
            callback: OnTimeRangeSelectedListener,
            bedtime: Bedtime
        ): TimeRangePickerFragment {
            return TimeRangePickerFragment().apply {
                initialize(callback)
                arguments = Bundle().apply {
                    putParcelable(TIME_PICKER_PRESET_INFO, bedtime)
                }
            }
        }
    }
}
