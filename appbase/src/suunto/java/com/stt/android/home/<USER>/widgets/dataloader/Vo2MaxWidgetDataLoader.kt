package com.stt.android.home.dashboardv2.widgets.dataloader

import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.WorkoutHeaderController
import com.stt.android.controllers.currentUserWorkoutUpdated
import com.stt.android.domain.diary.tss.GetLatestVo2MaxUseCase
import com.stt.android.home.dashboardv2.usecase.DashboardVo2MaxUseCase
import com.stt.android.home.dashboardv2.widgets.Vo2MaxWidgetInfo
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onStart
import javax.inject.Inject

internal class Vo2MaxWidgetDataLoader @Inject constructor(
    private val currentUserController: CurrentUserController,
    private val workoutHeaderController: WorkoutHeaderController,
    private val getLatestVo2MaxUseCase: GetLatestVo2MaxUseCase,
    private val dashboardVo2MaxUseCase: DashboardVo2MaxUseCase,
    private val coroutinesDispatchers: CoroutinesDispatchers,
) : WidgetDataLoader<Vo2MaxWidgetInfo>() {
    override suspend fun load(param: Param): WidgetData<Vo2MaxWidgetInfo> =
        WidgetData(
            widgetType = param.type,
            editMode = param.editMode,
            widgetInfo = workoutHeaderController.currentUserWorkoutUpdated
                .onStart { emit(Unit) }
                .map {
                    val fitnessExtension =
                        getLatestVo2MaxUseCase.getFitnessExtensionWithLatestVo2Max(
                            currentUserController.username
                        )
                    val workoutStartTimestamp = fitnessExtension?.workoutId?.let {
                        workoutHeaderController.findByIdsForCurrentUser(listOf(fitnessExtension.workoutId))
                            .first().startTime
                    }
                    val vo2Max = fitnessExtension?.vo2Max
                    val (state, items) = dashboardVo2MaxUseCase.getStateAndRangeItemList(vo2Max)
                    Vo2MaxWidgetInfo(
                        latestVo2Max = vo2Max,
                        latestVo2MaxDate = workoutStartTimestamp,
                        state = state,
                        rangeItemList = items,
                    )
                }
                .flowOn(coroutinesDispatchers.io),
        )
}
