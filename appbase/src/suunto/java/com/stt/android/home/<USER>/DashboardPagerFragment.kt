package com.stt.android.home.dashboardnew

import com.stt.android.R
import com.stt.android.utils.BluetoothUtils
import com.stt.android.utils.PermissionUtils
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class DashboardPagerFragment : BaseDashboardPagerFragment() {
    override fun onStart() {
        super.onStart()

        if (!BluetoothUtils.isBluetoothSupported(requireContext())) {
            // RequestPermissionFragment is not show. Request location permission for Map Widget.
            requestLocationPermissionForMap()
        }
    }

    private fun requestLocationPermissionForMap() {
        PermissionUtils.requestPermissionsIfNeeded(
            this,
            PermissionUtils.LOCATION_PERMISSIONS,
            getString(R.string.location_permissions_rationale_map),
            LOCATION_PERMISSION_REQUEST_CODE
        )
    }

    companion object {
        private const val LOCATION_PERMISSION_REQUEST_CODE = 301
    }
}
