package com.stt.android.home.devicetype

import android.content.Context
import androidx.lifecycle.ViewModel
import com.stt.android.R
import com.stt.android.utils.toV2
import com.stt.android.watch.SuuntoWatchModel
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.rx2.asFlow
import javax.inject.Inject

@HiltViewModel
class DeviceTypeSelectViewModel @Inject constructor(
    @ApplicationContext private val context: Context,
    suuntoWatchModel: SuuntoWatchModel,
) : ViewModel() {
    val itemListFlow = suuntoWatchModel.currentWatch.map { it.suuntoBtDevice.deviceType }.toV2()
        .toObservable()
        .asFlow()
        .map { it.displayName }
        .catch { emit("") }
        .map { deviceName ->
            listOf(
                DeviceTypeListItem(
                    deviceType = DeviceType.WATCH,
                    text = context.getString(R.string.device_type_select_watch),
                    tips = deviceName.takeIf { it.isNotBlank() }?.let { name ->
                        context.getString(
                            R.string.device_type_select_watch_or_dive_computer_tips,
                            name,
                        )
                    } ?: "",
                ),
                DeviceTypeListItem(
                    deviceType = DeviceType.HEADPHONES,
                    text = context.getString(R.string.device_type_select_headphones),
                ),
            )
        }
}
