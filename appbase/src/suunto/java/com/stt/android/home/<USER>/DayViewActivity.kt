package com.stt.android.home.dayviewv2

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.enableEdgeToEdge
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.ui.Modifier
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.analytics.AnalyticsEvent.SUUNTO_DAY_EDIT_GOALS_BUTTON
import com.stt.android.analytics.AnalyticsPropertyValue.DayDetailsScreenSourceOptions.DAY_DETAILS_SCREEN
import com.stt.android.analytics.EmarsysAnalytics
import com.stt.android.compose.util.setContentWithM3Theme
import com.stt.android.data.toEpochMilli
import com.stt.android.di.navigation.WorkoutDetailsRewriteNavigator
import com.stt.android.domain.user.MenstrualCycleRegularity
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.home.InsertMenstrualCycleViewModel
import com.stt.android.home.settings.goalsettings.GoalSettingsActivity
import com.stt.android.home.settings.goalsettings.GoalSettingsActivity.Companion.EDIT_GOAL_RESULT_CODE_CHANGED
import com.stt.android.menstrualcycle.regularity.MenstrualCycleRegularitySheetCreator
import com.stt.android.ui.activities.WorkoutEditDetailsActivity
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import java.time.LocalDate
import java.time.ZoneId
import javax.inject.Inject

@AndroidEntryPoint
class DayViewActivity : AppCompatActivity() {

    private val viewModel: DayViewViewModel by viewModels()

    private val insertMenstrualCycleViewModel: InsertMenstrualCycleViewModel by viewModels()
    private val deleteMenstrualCycleViewModel: DeleteMenstrualCycleViewModel by viewModels()

    @Inject
    lateinit var menstrualCycleRegularitySheetCreator: MenstrualCycleRegularitySheetCreator

    @Inject
    lateinit var rewriteNavigator: WorkoutDetailsRewriteNavigator

    @Inject
    lateinit var amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker

    @Inject
    lateinit var emarsysAnalytics: EmarsysAnalytics

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()

        observeMenstrualCycleRegularityChange()

        setContentWithM3Theme {
            DayViewScreen(
                modifier = Modifier.fillMaxSize(),
                viewModel = viewModel,
                insertMenstrualCycleViewModel = insertMenstrualCycleViewModel,
                deleteMenstrualCycleViewModel = deleteMenstrualCycleViewModel,
                onNavigateUp = { finish() },
                onAddManualWorkoutClicked = ::onAddManualWorkoutClicked,
                onWorkoutClicked = ::onWorkoutClicked,
                onTargetSettingClicked = ::onTargetSettingClicked,
            )
        }
    }

    private fun observeMenstrualCycleRegularityChange() {
        lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) {
                insertMenstrualCycleViewModel.menstrualCycleRegularityChanged.collectLatest {
                    showMenstrualCycleRegularitySheet(it)
                }
            }
        }

        lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) {
                deleteMenstrualCycleViewModel.menstrualCycleRegularityChanged.collectLatest {
                    showMenstrualCycleRegularitySheet(it)
                }
            }
        }
    }

    private fun showMenstrualCycleRegularitySheet(regularity: MenstrualCycleRegularity) {
        menstrualCycleRegularitySheetCreator.create(regularity)
            ?.show(supportFragmentManager, regularity.name)
    }

    private fun onAddManualWorkoutClicked() {
        val date = viewModel.currentDate ?: return
        val startMillis = date.atStartOfDay(ZoneId.systemDefault()).withHour(12).toEpochMilli()
        startActivity(WorkoutEditDetailsActivity.newStartIntentNewWithStartTime(this, startMillis))
    }

    private fun onWorkoutClicked(workoutHeader: WorkoutHeader) {
        rewriteNavigator.navigate(
            context = this,
            username = workoutHeader.username,
            workoutId = workoutHeader.id,
            workoutKey = workoutHeader.key,
            analyticsSource = DAY_DETAILS_SCREEN,
            isFromNotification = false,
            showCommentsDialog = false,
        )
    }

    private fun onTargetSettingClicked() {
        startActivityForResult(
            GoalSettingsActivity.newStartIntent(this),
            EDIT_GOAL_REQUEST_CODE,
        )
        amplitudeAnalyticsTracker.trackEvent(SUUNTO_DAY_EDIT_GOALS_BUTTON)
        emarsysAnalytics.trackEvent(SUUNTO_DAY_EDIT_GOALS_BUTTON)
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        when (requestCode) {
            EDIT_GOAL_REQUEST_CODE -> {
                if (resultCode == EDIT_GOAL_RESULT_CODE_CHANGED) {
                    // TODO reload data if goal change can't be observed
                }
            }

            else -> super.onActivityResult(requestCode, resultCode, data)
        }
    }

    companion object {
        private const val EDIT_GOAL_REQUEST_CODE = 1

        fun newIntent(context: Context, date: LocalDate): Intent {
            return Intent(context, DayViewActivity::class.java).apply {
                putExtra(DayViewViewModel.EXTRA_INITIAL_DATE, date)
            }
        }
    }
}
