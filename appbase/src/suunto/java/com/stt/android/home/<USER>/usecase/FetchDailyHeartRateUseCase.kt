package com.stt.android.home.dayviewv2.usecase

import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.home.dayviewv2.DayViewExpandable
import com.stt.android.home.dayviewv2.HeartRateViewData
import com.stt.android.home.dayviewv2.TrendChartAxisRange
import com.stt.android.home.dayviewv2.TrendChartData
import com.stt.android.home.dayviewv2.TrendChartSeries
import com.stt.android.utils.splitByCompareLast
import com.suunto.algorithms.data.HeartRate.Companion.hz
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.flowOn
import java.time.LocalDate
import javax.inject.Inject
import kotlin.math.roundToInt
import kotlin.time.Duration.Companion.minutes
import com.stt.android.core.R as CR

class FetchDailyHeartRateUseCase @Inject constructor(
    private val fetchDailyTrendDataUseCase: FetchDailyTrendDataUseCase,
    private val fetchExpandedStateUseCase: FetchExpandedStateUseCase,
    private val dispatchers: CoroutinesDispatchers,
) {
    operator fun invoke(date: LocalDate): Flow<HeartRateViewData?> = combine(
        fetchDailyTrendDataUseCase(date),
        fetchExpandedStateUseCase(DayViewExpandable.Hr),
    ) { trendData, expanded ->
        val samples = trendData.filter { it.hasHr }.toMutableList()

        if (samples.isEmpty()) return@combine null

        val hrSamples = samples.mapNotNull { it.hr }
        val hrMin = listOf(
            hrSamples,
            samples.mapNotNull { sample -> sample.hrMin?.takeIf { it > 0f } },
        ).flatten().min()
        val hrMax = listOf(
            hrSamples,
            samples.mapNotNull { sample -> sample.hrMax?.takeIf { it > 0f } },
        ).flatten().max()

        if (samples.none { it.hr == hrMin }) {
            val index = samples.indexOfFirst { it.hrMin == hrMin }
            samples[index] = samples[index].copy(hr = hrMin)
        }

        if (samples.none { it.hr == hrMax }) {
            val index = samples.indexOfFirst { it.hrMax == hrMax }
            samples[index] = samples[index].copy(hr = hrMax)
        }

        val series = samples
            .splitByCompareLast { e1, e2 -> e2.timestamp - e1.timestamp > TIME_DELTA_IN_MILLI }
            .map { subset ->
                TrendChartSeries(
                    colorRes = CR.color.bright_red,
                    x = subset.map { it.timeISO8601.toEpochSecond() },
                    y = subset.map { it.hr!!.hz.inBpm },
                )
            }

        val minBpm = hrMin.hz.inBpm.roundToInt()
        val maxBpm = hrMax.hz.inBpm.roundToInt()
        val (minX, maxX) = date.epochSecondRange
        val (minY, maxY) = adjustBpmRange(minBpm, maxBpm)
        val chartData = TrendChartData(
            series = series,
            axisRange = TrendChartAxisRange(
                minX = minX,
                maxX = maxX,
                minY = minY.toDouble(),
                maxY = maxY.toDouble(),
            ),
        )

        HeartRateViewData(
            min = minBpm,
            max = maxBpm,
            expanded = expanded,
            chartData = chartData,
        )
    }.flowOn(dispatchers.io)

    companion object {
        private val TIME_DELTA_IN_MILLI = 30.minutes.inWholeMilliseconds
    }
}
