package com.stt.android.home.dashboard.activitydata

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.activitydata.goals.FetchBedtimeEndUseCase
import com.stt.android.domain.activitydata.goals.FetchBedtimeStartUseCase
import com.stt.android.domain.activitydata.goals.SetSleepGoalUseCase
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.launch
import timber.log.Timber
import java.util.Locale
import java.util.concurrent.TimeUnit
import javax.inject.Inject

class SleepDailyTargetViewModel @Inject constructor(
    private val fetchBedtimeStartUseCase: FetchBedtimeStartUseCase,
    private val fetchBedtimeEndUseCase: FetchBedtimeEndUseCase,
    private val saveSleepGoalUseCase: SetSleepGoalUseCase,
    private val coroutinesDispatchers: CoroutinesDispatchers,
) : ViewModel() {
    sealed interface ViewData {
        data object Loading : ViewData

        data class Loaded(
            val bedtime: Bedtime,
            val bedtimeText: String,
        ) : ViewData

        data object Error : ViewData
    }

    private val _viewData: MutableStateFlow<ViewData> = MutableStateFlow(ViewData.Loading)
    val viewData: StateFlow<ViewData> = _viewData.asStateFlow()

    private var saveBedtimeJob: Job? = null

    init {
        fetchBedtime()
    }

    private fun fetchBedtime() {
        combine(
            fetchBedtimeStartUseCase(),
            fetchBedtimeEndUseCase(),
        ) { start, end ->
            val (startHour, startMinute) = getHoursMinutes(start)
            val (endHour, endMinute) = getHoursMinutes(end)
            val bedtime = Bedtime(
                startHour = startHour,
                startMinute = startMinute,
                endHour = endHour,
                endMinute = endMinute,
            )
            val bedtimeText = String.format(Locale.US, "%02d:%02d–%02d:%02d", startHour, startMinute, endHour, endMinute)
            _viewData.value = ViewData.Loaded(
                bedtime = bedtime,
                bedtimeText = bedtimeText,
            )
        }.catch { e ->
            Timber.w(e, "Error while loading bedtime")
            _viewData.value = ViewData.Error
        }.launchIn(viewModelScope)
    }

    private fun getHoursMinutes(seconds: Int): Pair<Int, Int> {
        val hours = TimeUnit.SECONDS.toHours(seconds.toLong())
        val minutes = TimeUnit.SECONDS.toMinutes(seconds.toLong()) % 60
        return Pair(hours.toInt(), minutes.toInt())
    }

    fun saveBedtime(bedtime: Bedtime) {
        saveBedtimeJob?.cancel()
        saveBedtimeJob = viewModelScope.launch(coroutinesDispatchers.io) {
            delay(100)
            runSuspendCatching {
                val startTime = getSeconds(bedtime.startHour, bedtime.startMinute)
                val endTime = getSeconds(bedtime.endHour, bedtime.endMinute)
                saveSleepGoalUseCase.setBedtimes(startTime, endTime)
            }.onFailure { e ->
                Timber.w(e, "Error while setting bedtime")
            }
        }
    }

    private fun getSeconds(hour: Int, minute: Int): Int {
        return (
            TimeUnit.HOURS.toSeconds(hour.toLong()) +
                TimeUnit.MINUTES.toSeconds(minute.toLong())
            ).toInt()
    }
}
