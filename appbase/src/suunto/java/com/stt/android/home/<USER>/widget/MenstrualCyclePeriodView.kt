package com.stt.android.home.dayviewv2.widget

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Icon
import androidx.compose.material3.LocalMinimumInteractiveComponentSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.semantics.role
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.stt.android.R
import com.stt.android.compose.theme.SuuntoIcons
import com.stt.android.compose.theme.lightGrey
import com.stt.android.compose.theme.material3.body
import com.stt.android.compose.theme.spacing

@Composable
fun MenstrualCyclePeriodView(
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Box(modifier = modifier) {
        CompositionLocalProvider(LocalMinimumInteractiveComponentSize provides Dp.Unspecified) {
            Surface(
                modifier = Modifier.semantics { role = Role.Button },
                onClick = onClick,
                shape = CircleShape,
                color = MaterialTheme.colorScheme.lightGrey,
                contentColor = MaterialTheme.colorScheme.onSurface,
            ) {
                Row(
                    modifier = Modifier.padding(all = MaterialTheme.spacing.small),
                    horizontalArrangement = Arrangement.Center,
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    Text(
                        text = stringResource(R.string.day_view_period_day),
                        style = MaterialTheme.typography.body,
                    )
                    Spacer(modifier = Modifier.width(MaterialTheme.spacing.xsmall))
                    Icon(
                        modifier = Modifier.size(18.dp),
                        painter = SuuntoIcons.ActionCloseCircleFilled.asPainter(),
                        contentDescription = null,
                    )
                }
            }
        }
    }
}
