package com.stt.android.home.dayviewv2.widget

import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.size
import androidx.compose.material.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.layout.onLayoutRectChanged
import androidx.compose.ui.platform.LocalWindowInfo
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.LifecycleStartEffect
import com.stt.android.compose.theme.cloudyGrey
import com.stt.android.compose.theme.iconSizes

@Composable
fun GoalWheel(
    value: Int,
    goal: Int,
    icon: Painter,
    color: Color,
    modifier: Modifier = Modifier,
) {
    val windowInfo = LocalWindowInfo.current

    val backgroundColor = MaterialTheme.colorScheme.cloudyGrey

    var visible by remember { mutableStateOf(false) }
    var targetValue by remember { mutableFloatStateOf(0f) }
    val progress by animateFloatAsState(targetValue = targetValue)

    Box(
        modifier = modifier
            .offset(y = 4.dp)
            .onLayoutRectChanged(debounceMillis = 0L) {
                val startX = it.positionInWindow.x
                val endX = it.positionInWindow.x + it.width
                visible = (0..windowInfo.containerSize.width).intersect(startX..endX).isNotEmpty()
            },
        contentAlignment = Alignment.Center,
    ) {
        Canvas(modifier = Modifier.size(44.dp)) {
            val radius = size.minDimension / 2f

            drawArc(
                color = backgroundColor,
                startAngle = 135f,
                sweepAngle = 270f,
                useCenter = false,
                topLeft = center.copy(x = center.x - radius, y = center.y - radius),
                size = size.copy(width = radius * 2, height = radius * 2),
                style = Stroke(width = 5.5f.dp.toPx(), cap = StrokeCap.Round)
            )

            drawArc(
                color = color,
                startAngle = 135f,
                sweepAngle = progress * 270f,
                useCenter = false,
                topLeft = center.copy(x = center.x - radius, y = center.y - radius),
                size = size.copy(width = radius * 2, height = radius * 2),
                style = Stroke(width = 5.5f.dp.toPx(), cap = StrokeCap.Round)
            )
        }
        Icon(
            modifier = Modifier.size(MaterialTheme.iconSizes.small),
            painter = icon,
            contentDescription = null,
            tint = color,
        )
    }

    LifecycleStartEffect(visible, value, goal) {
        targetValue = if (visible && goal > 0) {
            (value.toFloat() / goal).coerceAtMost(1f)
        } else 0f
        onStopOrDispose {
            targetValue = 0f
        }
    }
}
