package com.stt.android.home.settings

import android.annotation.SuppressLint
import android.content.Context
import android.content.res.TypedArray
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.compose.ui.platform.ComposeView
import androidx.preference.Preference
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.stt.android.R
import com.stt.android.compose.util.setContentWithTheme
import com.stt.android.home.settings.wheel.WheelFragmentData
import com.stt.android.home.settings.wheel.WheelPickerBottomSheetContent
import timber.log.Timber
import java.util.Locale
import kotlin.math.roundToInt

abstract class BaseHeartPickerDialogPreference @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = android.R.attr.preferenceStyle,
    defStyleRes: Int = defStyleAttr
) : Preference(context, attrs, defStyleAttr, defStyleRes) {

    abstract val defaultWheelData: WheelFragmentData

    override fun onSetInitialValue(defaultValue: Any?) {
        super.onSetInitialValue(defaultValue)
        val defaultHeart = defaultWheelData.columns.firstOrNull()?.run {
            getPersistedString(this.textList[this.defaultIndex])
        }
        showDefaultSummary(defaultHeart)
    }

    override fun onGetDefaultValue(a: TypedArray, index: Int): Any? {
        return a.getString(index)
    }

    override fun onClick() {
        showHRPickerDialog()
    }

    private fun showDefaultSummary(defaultValue: Any?) {
        Timber.d("BaseHeartPickerDialogPreference showDefaultSummary:%s", defaultValue)
        defaultWheelData.columns.firstOrNull()?.let { wheelData ->
            val summaryIndex =
                if (defaultValue == null || !wheelData.textList.contains(defaultValue)) {
                    wheelData.defaultIndex
                } else {
                    wheelData.textList.indexOf(defaultValue)
                }
            val heartValue = wheelData.textList[summaryIndex]
            setSummary(getHeartWithUnit(heartValue))
        }
    }

    @SuppressLint("InflateParams")
    private fun showHRPickerDialog() {
        val wheelFragmentData = defaultWheelData.columns.firstOrNull()?.run {
            val defaultValue = this.textList[this.defaultIndex]
            val heartValue = getPersistedString(defaultValue)
            val selectIndex = if (this.textList.contains(heartValue)) {
                this.textList.indexOf(heartValue)
            } else {
                this.defaultIndex
            }
            val wheelColumn = this.copy(defaultIndex = selectIndex)
            defaultWheelData.copy(columns = listOf(wheelColumn))
        } ?: return

        val displayHeightPx = context.resources.displayMetrics.heightPixels
        val visibleHeightPx = context.resources.getFraction(
            R.fraction.bottom_sheet_picker_height_fraction,
            displayHeightPx,
            displayHeightPx
        ).roundToInt()
        val bottomSheetDialog = BottomSheetDialog(context).also { dialog ->
            dialog.behavior.peekHeight = visibleHeightPx
        }
        val composeView =
            LayoutInflater.from(context).inflate(R.layout.compose_bottom_sheet, null) as ComposeView
        composeView.setContentWithTheme {
            WheelPickerBottomSheetContent(
                data = wheelFragmentData,
                onDoneClick = { indices ->
                    Timber.d("indices:$indices")
                    val heartValue = defaultWheelData.columns.first().run {
                        this.textList[indices.first()]
                    }
                    persistString(heartValue)
                    setSummary(getHeartWithUnit(heartValue))
                    bottomSheetDialog.dismiss()
                },
                onDragging = { dragging ->
                    bottomSheetDialog.behavior.isDraggable = !dragging
                }
            )
        }
        bottomSheetDialog.setContentView(composeView)
        bottomSheetDialog.show()
    }

    private fun getHeartWithUnit(heartValue: String): String {
        return String.format(
            Locale.getDefault(), "%s %s",
            heartValue,
            context.getString(R.string.heart_unit)
        )
    }
}
