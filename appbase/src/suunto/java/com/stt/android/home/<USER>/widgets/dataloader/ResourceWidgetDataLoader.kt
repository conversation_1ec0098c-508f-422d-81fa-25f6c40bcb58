package com.stt.android.home.dashboardv2.widgets.dataloader

import android.content.Context
import com.stt.android.R
import com.stt.android.data.TimeUtils
import com.stt.android.data.toEpochMilli
import com.stt.android.domain.activitydata.dailyvalues.FetchDailyRecoveryDataUseCase
import com.stt.android.domain.recovery.RecoveryData
import com.stt.android.domain.recovery.RecoveryDataRepository
import com.stt.android.domain.sleep.FetchSleepUseCase
import com.stt.android.home.dashboardv2.ui.widgets.common.NO_DATA_VALUE
import com.stt.android.home.dashboardv2.ui.widgets.common.generateWidgetTitle
import com.stt.android.home.dashboardv2.widgets.ResourceWidgetInfo
import com.stt.android.ui.utils.TextFormatter
import com.stt.android.utils.iterator
import com.stt.android.utils.lastRecoverDataForSleep
import com.stt.android.utils.toEpochMilli
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEmpty
import java.time.LocalDate
import java.time.LocalTime
import java.time.ZoneId
import java.time.ZonedDateTime
import javax.inject.Inject
import kotlin.math.roundToInt

internal class ResourceWidgetDataLoader @Inject constructor(
    private val context: Context,
    private val recoveryDataRepository: RecoveryDataRepository,
    private val fetchDailyRecoveryDataUseCase: FetchDailyRecoveryDataUseCase,
    private val fetchSleepUseCase: FetchSleepUseCase,
) : WidgetDataLoader<ResourceWidgetInfo>() {
    override suspend fun load(param: Param): WidgetData<ResourceWidgetInfo> {
        val lastDay = LocalDate.now()
        val firstDay = lastDay.minusDays(6L)

        val endFetchRangeMillis = lastDay
            .atTime(LocalTime.MAX)
            .toEpochMilli()
        // Query part of the day before earliest shown day in case user didn't sleep past midnight
        // or if they woke after midnight before new recovery data was collected
        val startFetchRangeMillis = firstDay
            .minusDays(1)
            .atTime(12, 0)
            .toEpochMilli()

        val flow = combine(
            recoveryDataRepository.fetchRecoveryDataForDateRange(startFetchRangeMillis, endFetchRangeMillis)
                .onEmpty { emit(emptyList()) }
                .map { it.sortedBy(RecoveryData::timestamp) },
            fetchSleepUseCase.fetchSleeps(firstDay, lastDay)
                .map { sleeps -> sleeps.groupBy { TimeUtils.epochToLocalZonedDateTime(it.timestamp).toLocalDate() } },
            fetchDailyRecoveryDataUseCase.fetchCurrentBalance(NO_DATA_VALUE),
        ) { recoveryData, sleepData, balanceData ->
            val dailyWakeupBalanceOrigin = buildList {
                for (date in firstDay..lastDay) {
                    val balance = sleepData[date]
                        ?.map { it.longSleep?.wokeUp }
                        ?.firstOrNull()
                        ?.let { recoveryData.lastRecoverDataForSleep(it) }
                        ?.balance
                        ?: NO_DATA_VALUE
                    add(balance.toDouble())
                }
            }
            val dailyWakeupBalance = generateDailyBarProgresses(dailyWakeupBalanceOrigin)
            if (dailyWakeupBalance.none { it > 0.0F }) {
                return@combine ResourceWidgetInfo(
                    subheader = context.getString(R.string.widget_now),
                    progresses = dailyWakeupBalance,
                    title = generateWidgetTitle("0", "%"),
                    subtitle = context.getString(R.string.widget_no_data_subtitle),
                )
            }

            var newestBalanceTimestamp = ZonedDateTime.now()
            var newestBalance = balanceData
            var newestBalanceIsNow = true

            // NO_BALANCE_VALUE is the default value for fetchCurrentBalance if watch is not connected,
            // If we don't have a current balance value from the watch show last value from the db
            val newestRecoveryData = recoveryData.lastOrNull()
            if (newestBalance == NO_DATA_VALUE) {
                newestBalanceIsNow = false
                if (newestRecoveryData != null &&
                    lastDay == newestRecoveryData.timeISO8601.toLocalDate()
                ) {
                    newestBalanceTimestamp = newestRecoveryData
                        .timeISO8601
                        .withZoneSameInstant(ZoneId.systemDefault())
                    newestBalance = newestRecoveryData.balance
                } else {
                    newestBalanceTimestamp = null
                }
            }

            ResourceWidgetInfo(
                subheader = if (newestBalanceIsNow) {
                    context.getString(R.string.widget_now)
                } else newestBalanceTimestamp?.let {
                    TextFormatter.formatTime(context, it.toEpochMilli())
                } ?: context.getString(R.string.widget_now),
                progresses = dailyWakeupBalance.toList(),
                title = newestBalanceTimestamp?.let {
                    generateWidgetTitle("${newestBalance.times(100f).roundToInt()}", "%")
                } ?: generateWidgetTitle("0", "%"),
                subtitle = context.getString(newestBalance.stress()),
            )
        }

        return WidgetData(
            widgetType = param.type,
            editMode = param.editMode,
            widgetInfo = flow,
        )
    }

    private fun Float.stress() = if (this == NO_DATA_VALUE) {
        R.string.widget_no_data_subtitle
    } else {
        when (this.times(100f).roundToInt()) {
            in 0 .. 19 -> R.string.resource_widget_stress_state_active
            in 20..50 -> R.string.resource_widget_stress_state_inactive
            in 51..80 -> R.string.resource_widget_stress_state_stressful
            else -> R.string.resource_widget_stress_state_recovering
        }
    }
}
