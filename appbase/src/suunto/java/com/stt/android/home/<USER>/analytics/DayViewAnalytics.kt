package com.stt.android.home.dayview.analytics

import com.stt.android.TestOpen
import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.AnalyticsEventProperty
import com.stt.android.analytics.AnalyticsProperties
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.analytics.AnalyticsPropertyValue.DayViewDetails.NO_SPO2_MEASURED
import com.stt.android.analytics.EmarsysAnalytics
import com.stt.android.data.TimeUtils
import com.stt.android.domain.activitydata.dailyvalues.StressState
import com.stt.android.home.dayview.DayType
import com.stt.android.home.dayview.DayViewData
import com.stt.android.home.diary.analytics.analyticsProperty
import com.stt.android.home.diary.analytics.calculateStressStateDurationForAnalytics
import com.stt.android.home.diary.analytics.highestValuePerc
import com.stt.android.home.diary.analytics.highestValueTimeHHMM
import java.time.Clock
import java.time.Duration
import java.time.Instant
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.ZonedDateTime
import java.time.temporal.ChronoUnit
import java.util.Locale
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import kotlin.math.roundToInt
import kotlin.math.roundToLong

/**
 * Class for sending analytics data from day view
 */
@TestOpen
class DayViewAnalytics
@Inject constructor(
    private val emarsysAnalytics: EmarsysAnalytics,
    private val amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker,
    private val clock: Clock
) {
    /**
     * Send analytics events about Day view usage and shown data
     *
     * @param dayViewData [DayViewData] for the currently focused in day view
     * @param source String describing how the user ended up on this date
     * (see [AnalyticsPropertyValue.DayDetailsScreenSourceOptions])
     */
    fun sendDayViewAnalytics(dayViewData: DayViewData, source: String?) {
        val now = ZonedDateTime.now()
        val startOfDay: ZonedDateTime =
            Instant.ofEpochMilli(dayViewData.startOfDay).atZone(ZoneId.systemDefault())
        val daysSince = ChronoUnit.DAYS.between(startOfDay, now).toInt()

        val activityCalories = dayViewData.workouts.sumOf { it.energyConsumption }.roundToInt()
        val activityDuration = TimeUnit.MINUTES.convert(
            dayViewData.workouts.sumOf { it.totalTime }.roundToLong(),
            TimeUnit.SECONDS
        )

        val trendDataAggregated = dayViewData.trendDataAggregated
        val calories = trendDataAggregated.sumOf { it.energy.inKcal }
            .roundToInt()
            .toString()
        val sleepSeconds = dayViewData.sleep
            .filter { it.sleep.hasLongSleep }
            .sumOf { it.sleep.totalSleepDuration.inWholeSeconds }
            .toFloat()
        // String format to fulfill analytic spec: sleep amount for the day, in hours, rounded up to
        // 2 decimals. Locale.US is needed to format the value with a dot e.g. 8.75 instead of 8,75.
        val sleep = sleepSeconds.takeIf { it > 0f }?.let {
            String.format(Locale.US, "%.2f", it.div(Duration.ofHours(1).seconds))
        } ?: DayViewAnalyticsData.NO_SLEEP_TRACKED

        val dayType = when (dayViewData.getDayType(TimeUtils.getTodayStartTime(clock))) {
            is DayType.ActiveDay -> AnalyticsPropertyValue.DayType.ACTIVE
            is DayType.TrainingDay -> AnalyticsPropertyValue.DayType.TRAINING
            is DayType.RestDay -> AnalyticsPropertyValue.DayType.REST
            is DayType.IncompleteDay -> AnalyticsPropertyValue.DayType.INCOMPLETE
            else -> AnalyticsPropertyValue.DayType.UNKNOWN
        }

        val stressStatesWithDuration = StressState.entries
            .filter { it != StressState.INVALID }
            .fold(mutableMapOf<String, String>()) { map, state ->
                state.analyticsProperty?.let { analyticsProperty ->
                    map[analyticsProperty] =
                        dayViewData.recoveryData.calculateStressStateDurationForAnalytics(state)
                }
                map
            }
        val trendDataSamples = dayViewData.trendDataSamples
        val spO2Average = trendDataAggregated.mapNotNull { it.spo2 }.map { it * 100 }.average()
        val spo2Percentages = trendDataSamples.mapNotNull { it.spo2 }.map { it * 100 }
        val analyticsData = DayViewAnalyticsData(
            calories = calories,
            steps = trendDataAggregated.sumOf { it.steps },
            sleep = sleep,
            activityCount = dayViewData.workouts.size,
            activityDuration = activityDuration,
            activityCalories = activityCalories,
            localHour = LocalDateTime.now().hour, // the user's timezone's local hour
            daysSince = daysSince,
            highestResourcesValue = dayViewData.recoveryData.highestValuePerc(),
            highestResourcesTimeHHMM = dayViewData.recoveryData.highestValueTimeHHMM(),
            stressStatesWithDuration = stressStatesWithDuration,
            dayType = dayType,
            spO2Avg = if (spO2Average.isNaN()) null else spO2Average.roundToInt(),
            spO2Min = spo2Percentages.minOrNull()
                ?.roundToInt(),
            spO2Max = spo2Percentages.maxOrNull()
                ?.roundToInt()
        )
        sendAnalytics(analyticsData, source)
    }

    private fun sendAnalytics(analyticsData: DayViewAnalyticsData, source: String?) {
        val properties = AnalyticsProperties()
        properties.put(AnalyticsEventProperty.DAY_DETAILS_SOURCE, source)
            .put(AnalyticsEventProperty.DAY_DETAILS_DAYS_SINCE, analyticsData.daysSince)
            .put(AnalyticsEventProperty.DAY_DETAILS_LOCAL_HOUR, analyticsData.localHour)
            .put(AnalyticsEventProperty.DAY_DETAILS_CALORIES, analyticsData.calories)
            .put(AnalyticsEventProperty.DAY_DETAILS_STEPS, analyticsData.steps)
            .put(AnalyticsEventProperty.DAY_DETAILS_SLEEP, analyticsData.sleep)
            .put(AnalyticsEventProperty.WORKOUTS, analyticsData.activityCount)
            .put(AnalyticsEventProperty.WORKOUTS_DURATION, analyticsData.activityDuration)
            .put(AnalyticsEventProperty.WORKOUTS_CALORIES, analyticsData.activityCalories)
            .put(AnalyticsEventProperty.DAY_TYPE, analyticsData.dayType)
            .put(
                AnalyticsEventProperty.STRESS_AND_RECOVERY_HIGHEST_VALUE,
                analyticsData.highestResourcesValue
            )
            .put(
                AnalyticsEventProperty.STRESS_AND_RECOVERY_HIGHEST_VALUE_TIME,
                analyticsData.highestResourcesTimeHHMM
            )
            .put(
                AnalyticsEventProperty.DAY_DETAILS_SPO2AVG,
                analyticsData.spO2Avg ?: NO_SPO2_MEASURED
            )
            .put(
                AnalyticsEventProperty.DAY_DETAILS_SPO2MAX,
                analyticsData.spO2Max ?: NO_SPO2_MEASURED
            )
            .put(
                AnalyticsEventProperty.DAY_DETAILS_SPO2MIN,
                analyticsData.spO2Min ?: NO_SPO2_MEASURED
            )

        for ((prop, value) in analyticsData.stressStatesWithDuration) {
            properties.put(prop, value)
        }

        amplitudeAnalyticsTracker.trackEvent(AnalyticsEvent.SUUNTO_DAY_DETAILS_SCREEN, properties)
        emarsysAnalytics.trackEvent(AnalyticsEvent.SUUNTO_DAY_DETAILS_SCREEN)
    }
}
