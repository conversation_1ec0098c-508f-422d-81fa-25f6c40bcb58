package com.stt.android.home.dashboard.activitydata

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.SeekBar
import com.stt.android.databinding.WeeklyGoalBottomSheetBinding
import com.stt.android.ui.utils.SmartBottomSheetDialogFragment
import com.stt.android.ui.utils.TextFormatter
import dagger.hilt.android.AndroidEntryPoint
import java.util.Locale
import com.stt.android.core.R as CR

@AndroidEntryPoint
class DurationWeeklyGoalBottomSheetDialogFragment : SmartBottomSheetDialogFragment() {
    
    private var binding: WeeklyGoalBottomSheetBinding? = null
    
    private val minDurationHours = SEEKBAR_DURATION_IN_HOURS_MIN
    private val maxDurationHours = SEEKBAR_DURATION_IN_HOURS_MAX
    
    fun interface GoalUpdateListener {
        fun goalUpdated(goal: Int)
    }
    
    var goalUpdateListener: GoalUpdateListener? = null
    private var valueChanged = false
    private var currentDuration = DEFAULT_WEEKLY_GOAL

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = WeeklyGoalBottomSheetBinding.inflate(inflater, container, false)
        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setupInitialState()
        setupEventListeners()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        binding = null
    }

    private fun setupInitialState() {
        binding?.apply {
            groupTrainingPlan.visibility = View.GONE
            
            groupTrainingDurationSettings.visibility = View.VISIBLE
            
            val hour = getString(CR.string.hour)
            trainingDurationMin.text = String.format(Locale.US, "%d %s", minDurationHours, hour)
            trainingDurationMax.text = String.format(Locale.US, "%d %s", maxDurationHours, hour)
            
            val initialGoal = arguments?.getInt(BaseDailyTargetBottomSheetDialogFragment.KEY_ACTIVITY_DATA_GOAL, DEFAULT_WEEKLY_GOAL) ?: DEFAULT_WEEKLY_GOAL
            
            currentDuration = initialGoal
            
            updateDurationTextView(currentDuration)
            seekbarTrainingDuration.min = minDurationHours
            seekbarTrainingDuration.max = maxDurationHours
            seekbarTrainingDuration.progress = durationToProgress(currentDuration)
        }
    }

    private fun setupEventListeners() {
        binding?.seekbarTrainingDuration?.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar, progress: Int, fromUser: Boolean) {
                if (fromUser) {
                    val duration = progressToDuration(progress)
                    updateDurationTextView(duration)
                    valueChanged = true
                    currentDuration = duration
                    goalUpdateListener?.goalUpdated(duration)
                }
            }

            override fun onStartTrackingTouch(seekBar: SeekBar) {
            }

            override fun onStopTrackingTouch(seekBar: SeekBar) {

            }
        })
    }
    
    private fun updateDurationTextView(seconds: Int) {
        val duration = formatTimeValue(seconds)
        binding?.textviewTrainingDuration?.text = duration
    }
    
    private fun formatTimeValue(seconds: Int): String {
        val resources = requireContext().resources
        return TextFormatter.formatElapsedTimeWithUnit(resources, seconds.toLong())
    }
    
    private fun durationToProgress(durationSeconds: Int): Int {
        val durationHours = durationSeconds / 3600
        return if (durationHours < minDurationHours) {
            minDurationHours
        } else {
            maxOf(minOf(durationHours, maxDurationHours), minDurationHours)
        }
    }
    
    private fun progressToDuration(progress: Int): Int {
        return progress * 3600
    }

    companion object {
        const val SEEKBAR_DURATION_IN_HOURS_MIN = 1
        const val SEEKBAR_DURATION_IN_HOURS_MAX = 25
        
        const val DEFAULT_WEEKLY_GOAL = 10800
        
        const val FRAGMENT_TAG = "DurationWeeklyGoalBottomSheetDialogFragment.FRAGMENT_TAG"
    }
} 
