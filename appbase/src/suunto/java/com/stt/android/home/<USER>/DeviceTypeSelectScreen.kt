package com.stt.android.home.devicetype

import android.annotation.SuppressLint
import androidx.activity.compose.LocalOnBackPressedDispatcherOwner
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.Divider
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Scaffold
import androidx.compose.material.Text
import androidx.compose.material.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.hilt.navigation.compose.hiltViewModel
import com.stt.android.R
import com.stt.android.compose.component.SuuntoIconButton
import com.stt.android.compose.modifiers.clickableThrottleFirst
import com.stt.android.compose.modifiers.narrowContent
import com.stt.android.compose.theme.SuuntoIcons
import com.stt.android.compose.theme.body
import com.stt.android.compose.theme.bodyLarge
import com.stt.android.compose.theme.darkGrey
import com.stt.android.compose.theme.dividerColor
import com.stt.android.compose.theme.nearWhite
import com.stt.android.compose.theme.spacing

@SuppressLint("UnusedMaterialScaffoldPaddingParameter")
@Composable
fun DeviceTypeSelectScreen(
    onItemClick: (DeviceType) -> Unit,
    modifier: Modifier = Modifier,
    viewModel: DeviceTypeSelectViewModel = hiltViewModel(),
) {
    val onBackPressedDispatcher =
        LocalOnBackPressedDispatcherOwner.current?.onBackPressedDispatcher
    val itemList: List<DeviceTypeListItem> by viewModel.itemListFlow.collectAsState(emptyList())
    Scaffold(
        modifier = modifier,
        topBar = {
            DeviceTypeSelectTopAppBar(onBackClick = {
                onBackPressedDispatcher?.onBackPressed()
            })
        },
        backgroundColor = Color.White,
    ) {
        DeviceTypeList(
            itemList = itemList,
            onItemClick = onItemClick,
        )
    }
}

@Composable
private fun DeviceTypeSelectTopAppBar(
    onBackClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    TopAppBar(
        title = {
            Text(
                text = stringResource(R.string.device_type_select_title).uppercase(),
            )
        },
        navigationIcon = {
            SuuntoIconButton(
                icon = SuuntoIcons.ActionBack,
                onClick = onBackClick,
                contentDescription = stringResource(R.string.back),
            )
        },
        backgroundColor = Color.White,
        modifier = modifier,
    )
}

@Composable
private fun DeviceTypeList(
    itemList: List<DeviceTypeListItem>,
    onItemClick: (DeviceType) -> Unit,
    modifier: Modifier = Modifier,
) {
    LazyColumn(modifier = modifier.narrowContent()) {
        item(key = "header") {
            HeaderItem()
        }
        items(itemList, key = { it.deviceType }) { item ->
            DeviceTypeItem(
                item = item,
                onItemClick = onItemClick,
            )
        }
    }
}

@Composable
private fun HeaderItem(
    modifier: Modifier = Modifier,
) {
    Box(
        modifier = modifier
            .background(color = MaterialTheme.colors.nearWhite)
            .fillMaxWidth()
            .padding(MaterialTheme.spacing.medium),
    ) {
        Text(
            text = stringResource(R.string.device_type_select_tips),
            style = MaterialTheme.typography.bodyLarge,
            color = MaterialTheme.colors.onSurface,
        )
    }
}

@Composable
private fun DeviceTypeItem(
    item: DeviceTypeListItem,
    onItemClick: (DeviceType) -> Unit,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .clickableThrottleFirst {
                onItemClick(item.deviceType)
            },
        horizontalAlignment = Alignment.Start,
    ) {
        Column(
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.xsmall),
            modifier = Modifier.padding(MaterialTheme.spacing.medium)
        ) {
            Text(
                text = item.text,
                style = MaterialTheme.typography.bodyLarge,
                color = MaterialTheme.colors.onSurface,
            )
            if (item.tips.isNotBlank()) {
                Text(
                    text = item.tips,
                    style = MaterialTheme.typography.body,
                    color = MaterialTheme.colors.darkGrey,
                )
            }
        }
        Divider(color = MaterialTheme.colors.dividerColor)
    }
}

@Preview
@Composable
private fun DeviceTypeListPreview() {
    DeviceTypeList(
        itemList = listOf(
            DeviceTypeListItem(
                deviceType = DeviceType.WATCH,
                text = "Watch or dive computer",
                tips = "This will disconnect Suunto Ocean",
            ),
            DeviceTypeListItem(
                deviceType = DeviceType.HEADPHONES,
                text = "Headphones",
            ),
        ),
        onItemClick = {}
    )
}
