package com.stt.android.home.dashboard.activitydata

import android.content.DialogInterface
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import com.stt.android.R
import com.stt.android.domain.activitydata.ActivityDataType
import dagger.hilt.android.AndroidEntryPoint
import java.util.Locale

@AndroidEntryPoint
class StepsDailyTargetBottomSheetDialogFragment : BaseDailyTargetBottomSheetDialogFragment() {
    override val seekbarThreshold = 30000
    override val seekbarSmallStep = 1000
    override val seekbarBigStep = 5000
    override val maxActivityDataValue = 100000
    override val minActivityDataValue = 1000
    override val TAG = "com.stt.android.home.dashboard.activitydata.StepsDailyTargetBottomSheetDialogFragment"

    private var goalMaxTextView: TextView? = null
    private var goalMinTextView: TextView? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        val view = super.onCreateView(inflater, container, savedInstanceState)
        goalMaxTextView = view.findViewById(R.id.goalMax)
        goalMinTextView = view.findViewById(R.id.goalMin)
        goalMaxTextView?.text = maxActivityDataValue.toString()
        goalMinTextView?.text = minActivityDataValue.toString()
        return view
    }

    override fun onDestroyView() {
        super.onDestroyView()
        goalMaxTextView = null
        goalMinTextView = null
    }

    override fun setSeekbarValueText(currentVal: Int) {
        seekbarValue?.text = String.format(Locale.US, "%d", currentGoal)
    }

    override fun getBottomsheetTitleText(): Int {
        return R.string.daily_goal_setting_main_title_steps
    }

    override fun getSeekbarTitleText(): Int {
        return R.string.daily_goal_setting_title_1_steps
    }

    override fun getLayoutId(): Int {
        return R.layout.bottomsheet_daily_target_steps
    }

    override fun onCancel(dialog: DialogInterface) {
        cancelListener?.onBottomSheetCancel(ActivityDataType.Steps(goal = currentGoal), valueChanged)
        cancelListener = null
    }
}
