package com.stt.android.home.dashboardv2.widgets.dataloader

import android.content.Context
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.domain.restheartrate.FetchRestHeartRateUseCase
import com.stt.android.home.dashboardv2.widgets.Last7DaysHeartRateWidgetInfo
import com.stt.android.home.dashboardv2.widgets.Period
import com.suunto.algorithms.data.HeartRate
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.map
import javax.inject.Inject

internal class RestingHeartRateWidgetDataLoader @Inject constructor(
    private val context: Context,
    private val fetchRestHeartRateUseCase: FetchRestHeartRateUseCase,
    private val coroutinesDispatchers: CoroutinesDispatchers,
) : WidgetDataLoader<Last7DaysHeartRateWidgetInfo>() {
    override suspend fun load(param: Param): WidgetData<Last7DaysHeartRateWidgetInfo> {
        val period = Period.Last7Days
        val firstDay = period.beginDate
        val lastDay = period.endDate
        val flow = fetchRestHeartRateUseCase.fetchRestHeartRateForDateRange(
            fromDate = firstDay,
            toDate = lastDay,
        )
            .flowOn(coroutinesDispatchers.io)
            .map { list ->
                val restHrByDate = list.groupBy { it.localDate }.mapValues { it.value.first() }
                val dailyMinHrs = (0..6).map {
                    val date = firstDay.plusDays(it.toLong())
                    restHrByDate[date]?.restHeartRate ?: HeartRate.ZERO
                }
                generateLast7DaysHeartRateInfo(context, dailyMinHrs, param.type)
            }
        return WidgetData(
            widgetType = param.type,
            editMode = param.editMode,
            widgetInfo = flow,
        )
    }
}
