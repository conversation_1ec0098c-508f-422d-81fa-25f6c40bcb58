package com.stt.android.home.dashboard.widget.suunto247

import com.stt.android.data.TimeUtils
import com.stt.android.domain.activitydata.ActivityDataType
import com.stt.android.domain.activitydata.goals.FetchSleepGoalUseCase
import com.stt.android.domain.sleep.FetchSleepUseCase
import com.stt.android.utils.iterator
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.onEmpty
import java.time.Instant
import java.time.LocalDate
import java.time.ZoneId
import java.time.ZonedDateTime
import javax.inject.Inject
import kotlin.time.Duration
import kotlin.time.Duration.Companion.seconds

class SleepWidgetDataFetcher @Inject constructor(
    private val fetchSleepUseCase: FetchSleepUseCase,
    private val fetchSleepGoalUseCase: FetchSleepGoalUseCase
) {
    fun sleepWidgetDataFlow(lastDay: LocalDate): Flow<SleepWidgetData> {
        val firstDay = lastDay.minusDays(6L)
        val sleepInDateRangeFlow =
            fetchSleepUseCase.fetchSleeps(firstDay, lastDay)
                .onEmpty { emit(emptyList()) }
        val sleepGoalFlow = fetchSleepGoalUseCase.fetchSleepGoal()
            .onEmpty { emit(ActivityDataType.SleepDuration().goal.seconds) }
            .catch { emit(ActivityDataType.SleepDuration().goal.seconds) }

        return combine(sleepInDateRangeFlow, sleepGoalFlow) { sleepInDateRange, sleepGoal ->
            val dailySleepData = sleepInDateRange.groupBy {
                TimeUtils.epochToLocalZonedDateTime(it.timestamp).toLocalDate()
            }
            val nightSleepAndNaps = mutableListOf<Pair<Float, Float>>()
            var lastDayFellAsleepTimestamp: ZonedDateTime? = null
            var lastDayWokeUpTimestamp: ZonedDateTime? = null
            var lastNightSleepTimeSeconds: Float? = null
            var totalSleepTimeSum = Duration.ZERO
            var numDaysWithData = 0
            var lastNapSeconds: Float? = null
            for (day in firstDay..lastDay) {
                val sleepForDay = dailySleepData[day]?.firstOrNull()
                val nightSleep = sleepForDay?.longSleep?.sleepDuration?.inWholeSeconds?.toFloat()
                val nap = sleepForDay?.getMergedNap()?.duration?.inWholeSeconds?.toFloat()

                if (nightSleep == null && nap == null) {
                    nightSleepAndNaps.add(SleepWidgetData.NO_SLEEP_VALUE)
                } else {
                    nightSleepAndNaps.add((nightSleep ?: 0f) to (nap ?: 0f))
                    totalSleepTimeSum += sleepForDay.totalSleepDuration
                    numDaysWithData++

                }

                if (day == lastDay) {
                    lastNightSleepTimeSeconds = sleepForDay?.longSleep?.sleepDuration?.inWholeSeconds?.toFloat()
                    lastDayFellAsleepTimestamp = sleepForDay?.longSleep?.fellAsleep?.let {
                        ZonedDateTime.ofInstant(
                            Instant.ofEpochMilli(it),
                            ZoneId.systemDefault()
                        )
                    }
                    lastDayWokeUpTimestamp = sleepForDay?.longSleep?.wokeUp?.let {
                        ZonedDateTime.ofInstant(
                            Instant.ofEpochMilli(it),
                            ZoneId.systemDefault()
                        )
                    }
                    lastNapSeconds = sleepForDay?.getMergedNap()?.duration?.inWholeSeconds?.toFloat()
                }
            }

            val averageFromDaysWithData = if (numDaysWithData != 0) {
                totalSleepTimeSum.inWholeSeconds.toFloat() / numDaysWithData
            } else {
                0f
            }

            SleepWidgetData(
                nightSleepAndNapSeconds = nightSleepAndNaps,
                averageSleepSecondsInPeriod = averageFromDaysWithData,
                numDaysWithSleepDataInPeriod = numDaysWithData,
                lastNightFellAsleep = lastDayFellAsleepTimestamp,
                lastNightWokeUp = lastDayWokeUpTimestamp,
                lastNightSleepTimeSeconds = lastNightSleepTimeSeconds,
                sleepGoalSeconds = sleepGoal.inWholeSeconds.toInt(),
                lastDay = lastDay,
                lastNapSeconds = lastNapSeconds
            )
        }
    }
}
