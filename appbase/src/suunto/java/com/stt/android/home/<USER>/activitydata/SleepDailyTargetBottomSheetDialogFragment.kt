package com.stt.android.home.dashboard.activitydata

import android.annotation.SuppressLint
import android.content.DialogInterface
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.core.view.isVisible
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.stt.android.R
import com.stt.android.data.TimeUtils
import com.stt.android.domain.activitydata.ActivityDataType
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import java.util.Locale
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import com.stt.android.core.R as CR

@AndroidEntryPoint
class SleepDailyTargetBottomSheetDialogFragment :
    BaseDailyTargetBottomSheetDialogFragment(),
    TimeRangePickerFragment.OnTimeRangeSelectedListener {
    // Note that for sleep seekbar we don't have threshold. These values are added for compatibility with base class
    override val seekbarThreshold = TimeUnit.MINUTES.toSeconds(15).toInt()
    override val seekbarSmallStep = TimeUnit.MINUTES.toSeconds(15).toInt()
    override val seekbarBigStep = TimeUnit.MINUTES.toSeconds(15).toInt()
    override val maxActivityDataValue = TimeUnit.HOURS.toSeconds(12).toInt()
    override val minActivityDataValue = TimeUnit.HOURS.toSeconds(4).toInt()
    override val TAG = "com.stt.android.home.dashboard.activitydata.SleepDailyTargetBottomSheetDialogFragment"

    private var goalMaxTextView: TextView? = null
    private var goalMinTextView: TextView? = null
    private var bedtimes: TextView? = null
    private var bedtimeTitle: TextView? = null
    private var bedtimeDivider: View? = null

    @Inject
    lateinit var viewModel: SleepDailyTargetViewModel

    private var bedtime = Bedtime(0, 0, 0, 0, 0)

    var bedtimeClickListener: ((Bedtime) -> Unit)? = null
        set(value) {
            field = value
            bedtimeDivider?.isVisible = value != null
        }

    @SuppressLint("ClickableViewAccessibility")
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        val view = super.onCreateView(inflater, container, savedInstanceState)
        goalMaxTextView = view.findViewById(R.id.goalMax)
        goalMinTextView = view.findViewById(R.id.goalMin)
        goalMaxTextView?.text = TimeUtils.getTimeStringAsHourAndMinute(
            3600L * 12L,
            getString(CR.string.hour),
            getString(CR.string.minute)
        )
        goalMinTextView?.text = TimeUtils.getTimeStringAsHourAndMinute(
            3600L * 4L,
            getString(CR.string.hour),
            getString(CR.string.minute)
        )
        bedtimes = view.findViewById(R.id.bedtimes)
        bedtimeTitle = view.findViewById(R.id.bedtimeTitle)
        bedtimeDivider = view.findViewById(R.id.bedtimeDivider)
        setupBedtime()
        return view
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) {
                viewModel.viewData
                    .onEach { viewData ->
                        when (viewData) {
                            SleepDailyTargetViewModel.ViewData.Loading,
                            SleepDailyTargetViewModel.ViewData.Error -> Unit // TODO Handle error case
                            is SleepDailyTargetViewModel.ViewData.Loaded -> {
                                bedtime = viewData.bedtime
                                bedtimes?.text = viewData.bedtimeText
                            }
                        }
                    }.launchIn(lifecycleScope)
            }
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        goalMaxTextView = null
        goalMinTextView = null
        bedtimes = null
        bedtimeTitle = null
        bedtimeDivider = null
    }

    private fun setupBedtime() {
        val showBedtime = arguments?.getBoolean(ARG_SHOW_BEDTIME, false) ?: false
        if (showBedtime) {
            bedtimeTitle?.visibility = View.VISIBLE
            bedtimes?.visibility = View.VISIBLE
            bedtimes?.setOnClickListener { onBedTimesClick() }
        } else {
            bedtimeTitle?.visibility = View.GONE
            bedtimes?.visibility = View.GONE
        }
    }

    override fun getBottomsheetTitleText(): Int {
        return R.string.daily_goal_setting_main_title_sleep
    }

    override fun getSeekbarTitleText(): Int {
        return R.string.daily_goal_setting_title_1_sleep
    }

    override fun setSeekbarValueText(currentVal: Int) {
        context?.let {
            seekbarValue?.text = TimeUtils.getTimeStringAsHourAndMinute(
                currentVal.toLong(),
                getString(CR.string.hour),
                getString(CR.string.minute)
            )
        }
    }

    override fun getLayoutId(): Int {
        return R.layout.bottomsheet_daily_target_sleep
    }

    private fun onBedTimesClick() {
        if (!isAdded) return

        bedtimeClickListener?.let {
            it(bedtime)
        } ?: run {
            val dateRangePickerFragment = TimeRangePickerFragment.newInstance(this, bedtime)
            dateRangePickerFragment.show(parentFragmentManager, TIME_RANGE_PICKER_TAG)
        }
    }

    override fun onTimeRangeSelected(bedtime: Bedtime) {
        this.bedtime = bedtime
        bedtimes?.text = String.format(
            Locale.US,
            "%02d:%02d-%02d:%02d",
            bedtime.startHour,
            bedtime.startMinute,
            bedtime.endHour,
            bedtime.endMinute
        )
        viewModel.saveBedtime(bedtime)
    }

    override fun onCancel(dialog: DialogInterface) {
        cancelListener?.onBottomSheetCancel(ActivityDataType.SleepDuration(goal = currentGoal), valueChanged)
        cancelListener = null
    }

    companion object {
        const val TIME_RANGE_PICKER_TAG = "datePicker"
        const val ARG_SHOW_BEDTIME = "ARG_SHOW_BEDTIME"

        fun newInstance(
            showBedTime: Boolean
        ): SleepDailyTargetBottomSheetDialogFragment {
            return SleepDailyTargetBottomSheetDialogFragment().apply {
                arguments = Bundle().apply {
                    putBoolean(ARG_SHOW_BEDTIME, showBedTime)
                }
            }
        }
    }
}
