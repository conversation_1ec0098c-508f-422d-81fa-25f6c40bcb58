package com.stt.android.home.dashboardnew.customization.guidance

import com.stt.android.data.workout.WorkoutHeaderRepository
import com.stt.android.domain.recovery.RecoveryDataRepository
import com.stt.android.domain.sleep.SleepRepository
import com.stt.android.domain.trenddata.TrendDataRepository
import javax.inject.Inject

class CheckDashboardCustomizationGuidanceConditionsMetUseCase @Inject constructor(
    workoutHeaderRepository: WorkoutHeaderRepository,
    private val trendDataRepository: TrendDataRepository,
    private val sleepRepository: SleepRepository,
    private val recoveryDataRepository: RecoveryDataRepository,
) : BaseCheckDashboardCustomizationGuidanceConditionsMetUseCase(workoutHeaderRepository) {
    override suspend fun areConditionsMet(): Boolean {
        if (super.areConditionsMet()) {
            return true
        }

        val numDaysWithTrendData = trendDataRepository.fetchNumDaysWithTrendData(
            NUM_DAYS_WITH_ACTIVITY_DATA_THRESHOLD
        )
        if (numDaysWithTrendData >= NUM_DAYS_WITH_ACTIVITY_DATA_THRESHOLD) {
            return true
        }

        val numDaysWithSleepData = sleepRepository.fetchNumDaysWithSleepData(
            NUM_DAYS_WITH_ACTIVITY_DATA_THRESHOLD
        )
        if (numDaysWithSleepData >= NUM_DAYS_WITH_ACTIVITY_DATA_THRESHOLD) {
            return true
        }

        val numDaysWithRecoveryData = recoveryDataRepository.fetchNumDaysWithRecoveryData(
            NUM_DAYS_WITH_ACTIVITY_DATA_THRESHOLD
        )
        return numDaysWithRecoveryData >= NUM_DAYS_WITH_ACTIVITY_DATA_THRESHOLD
    }

    companion object {
        private const val NUM_DAYS_WITH_ACTIVITY_DATA_THRESHOLD = 5
    }
}
