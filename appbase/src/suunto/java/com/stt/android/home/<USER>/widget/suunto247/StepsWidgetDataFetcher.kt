package com.stt.android.home.dashboard.widget.suunto247

import com.stt.android.domain.activitydata.ActivityDataType
import com.stt.android.domain.activitydata.dailyvalues.ActivityDataDailyRepository
import com.stt.android.domain.activitydata.goals.ActivityDataGoalRepository
import com.stt.android.domain.trenddata.TrendDataRepository
import com.stt.android.home.dashboard.widget.suunto247.StepsWidgetData.Companion.NO_STEPS_DATA
import com.stt.android.utils.iterator
import com.stt.android.utils.toEpochMilli
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.onEmpty
import java.time.LocalDate
import javax.inject.Inject
import kotlin.math.roundToInt

class StepsWidgetDataFetcher @Inject constructor(
    private val trendDataRepository: TrendDataRepository,
    private val activityDataDailyRepository: ActivityDataDailyRepository,
    private val activityDataGoalRepository: ActivityDataGoalRepository,
) {
    fun stepsWidgetDataFlow(lastDay: LocalDate): Flow<StepsWidgetData> {
        val firstDay = lastDay.minusDays(6)

        val lastDayMillisMillis = lastDay
            .atStartOfDay()
            .toEpochMilli()

        val firstDayMillis = firstDay
            .atStartOfDay()
            .toEpochMilli()

        val pastWeekTrendDataFlow = trendDataRepository.fetchTrendDataForDateRange(
            firstDayMillis,
            lastDayMillisMillis,
            true
        ).onEmpty { emit(emptyList()) }
        val todayStepsFlow = activityDataDailyRepository.fetchSteps()
            .catch { emit(NO_STEPS_DATA) }
            .onEmpty { emit(NO_STEPS_DATA) }
        val stepGoalFlow = activityDataGoalRepository.fetchStepsGoal()
            .catch { emit(ActivityDataType.Steps().goal) }
            .onEmpty { emit(ActivityDataType.Steps().goal) }

        return combine(
            pastWeekTrendDataFlow,
            todayStepsFlow,
            stepGoalFlow
        ) { pastWeekTrendData, todaySteps, stepGoal ->
            val pastWeekDataByDate = pastWeekTrendData.groupBy { it.timeISO8601.toLocalDate() }
            val dailySteps = mutableListOf<Int>()
            for (day in firstDay..lastDay) {
                val stepsForDay = pastWeekDataByDate[day]?.sumOf { it.steps } ?: 0

                if (stepsForDay > 0) {
                    dailySteps.add(stepsForDay)
                } else {
                    dailySteps.add(NO_STEPS_DATA)
                }
            }

            // Replace today's steps value from DB with one from watch if available
            if (todaySteps > 0) {
                dailySteps.removeAt(dailySteps.lastIndex)
                dailySteps.add(todaySteps)
            }

            val daysWithData = dailySteps.count { it != NO_STEPS_DATA }
            val average = if (daysWithData > 0) {
                val sumOfRealValues =
                    dailySteps.filterNot { it == NO_STEPS_DATA }.sum()
                (sumOfRealValues.toFloat() / daysWithData).roundToInt()
            } else {
                0
            }

            StepsWidgetData(
                dailySteps = dailySteps,
                todaySteps = dailySteps.last(),
                stepGoal = stepGoal,
                average = average,
                daysWithData = daysWithData,
                lastDay = lastDay
            )
        }
    }
}
