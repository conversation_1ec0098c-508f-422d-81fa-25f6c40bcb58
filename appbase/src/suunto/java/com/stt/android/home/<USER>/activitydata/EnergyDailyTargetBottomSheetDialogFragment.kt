package com.stt.android.home.dashboard.activitydata

import android.content.Context
import android.content.DialogInterface
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.SeekBar
import android.widget.TextView
import androidx.annotation.StringRes
import com.stt.android.R
import com.stt.android.domain.activitydata.ActivityDataType
import com.suunto.algorithms.data.Energy
import com.suunto.algorithms.data.Energy.Companion.kcal
import dagger.hilt.android.AndroidEntryPoint
import java.util.Locale
import javax.inject.Inject
import kotlin.math.roundToInt
import com.stt.android.core.R as CR

@AndroidEntryPoint
class EnergyDailyTargetBottomSheetDialogFragment : BaseDailyTargetBottomSheetDialogFragment(), EnergyDailyTargetView {

    override val seekbarThreshold = 2000
    override val seekbarSmallStep = 10
    override val seekbarBigStep = 100
    override val maxActivityDataValue = 5000
    override val minActivityDataValue = 10
    override val TAG = "com.stt.android.home.dashboard.activitydata.EnergyDailyTargetBottomSheetDialogFragment"

    private var goalMaxTextView: TextView? = null
    private var goalMinTextView: TextView? = null
    private var metabolicEnergyTextView: TextView? = null

    @Inject
    lateinit var presenter: EnergyDailyTargetPresenter

    lateinit var bmrTextString: String

    private var metabolicEnergyValue: Energy = 1800.kcal

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        val view = super.onCreateView(inflater, container, savedInstanceState)

        goalMaxTextView = view.findViewById(R.id.goalMax)
        goalMinTextView = view.findViewById(R.id.goalMin)
        metabolicEnergyTextView = view.findViewById(R.id.bmrText)

        bmrTextString = getString(R.string.daily_goal_setting_energy_detail)
        goalMaxTextView?.text = maxActivityDataValue.toString()
        goalMinTextView?.text = minActivityDataValue.toString()
        presenter.getBmr()
        return view
    }

    override fun onDestroyView() {
        super.onDestroyView()
        goalMaxTextView = null
        goalMinTextView = null
        metabolicEnergyTextView = null
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        presenter.takeView(this)
    }

    override fun setSeekbarValueText(currentVal: Int) {
        seekbarValue?.text = String.format(Locale.US, "%d %s", currentGoal, getString(CR.string.kcal))
    }

    @StringRes
    override fun getBottomsheetTitleText(): Int {
        return R.string.daily_goal_setting_main_title_energy
    }

    @StringRes
    override fun getSeekbarTitleText(): Int {
        return R.string.daily_goal_setting_title_1_energy
    }

    override fun getLayoutId(): Int {
        return R.layout.bottomsheet_daily_target_energy
    }

    override fun onCancel(dialog: DialogInterface) {
        cancelListener?.onBottomSheetCancel(ActivityDataType.Energy(goal = currentGoal), valueChanged)
        cancelListener = null
    }

    override fun updateMetabolicEnergyText() {
        val bmr = metabolicEnergyValue.inKcal.roundToInt()
        metabolicEnergyTextView?.text = String.format(bmrTextString, currentGoal + bmr, bmr)
    }

    override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
        super.onProgressChanged(seekBar, progress, fromUser)
        seekBar?.progress?.let { updateMetabolicEnergyText() }
    }

    override fun setMetabolicEnergyValue(value: Energy) {
        metabolicEnergyValue = value
        updateMetabolicEnergyText()
    }
}
