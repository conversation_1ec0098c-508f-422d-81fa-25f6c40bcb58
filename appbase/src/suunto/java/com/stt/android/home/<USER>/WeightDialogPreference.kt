package com.stt.android.home.settings

import android.content.Context
import android.util.AttributeSet
import com.stt.android.STTApplication
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.home.settings.userprofile.UserProfileUseCase
import com.stt.android.home.settings.userprofile.observeUserProfileCapabilities
import com.stt.android.watch.MissingCurrentWatchException
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

/*
* BigDecimal is used to limit the precision to one decimal. If the precision is not limited,
* the unit conversion might return undesirably long Strings. Also, now the String form of the
* weight will match the weight that was input, so if you set your weight as 70, you will see
* 70, when you go modify it. Also, if you set it to 70.1, you will see that when you return
* to the weight setting. If you set it to 70.15, it will be rounded up to 70.2.
*/
class WeightDialogPreference @JvmOverloads constructor(
    context: Context?,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = android.R.attr.preferenceStyle,
    defStyleRes: Int = defStyleAttr
) : BaseWeightDialogPreference(context, attrs, defStyleAttr, defStyleRes) {

    @Inject
    lateinit var userProfileUseCase: UserProfileUseCase

    @Inject
    lateinit var dispatchers: CoroutinesDispatchers

    private val coroutineScope: CoroutineScope
    private var observerJob: Job? = null
    private var setWatchJob: Job? = null

    init {
        STTApplication.getComponent().inject(this)
        coroutineScope = CoroutineScope(SupervisorJob() + dispatchers.main)
    }

    override fun onAttached() {
        super.onAttached()
        observerJob =
            observeUserProfileCapabilities(userProfileUseCase, coroutineScope, dispatchers)
    }

    override fun onDetached() {
        observerJob?.cancel()
        setWatchJob?.cancel()
        super.onDetached()
    }

    override fun persistString(value: String): Boolean {
        if (!super.persistString(value)) {
            return false
        }

        setWatchJob = coroutineScope.launch(dispatchers.io) {
            runSuspendCatching {
                userProfileUseCase.updateUserWeight(convertToKilograms(value))
            }.onSuccess {
                Timber.d("setUserWeight successful.")
            }.onFailure { e ->
                if (e !is MissingCurrentWatchException) {
                    Timber.w(e, "setUserWeight error.")
                }
            }
        }
        return true
    }
}
