package com.stt.android.home.dayviewv2.widget

import androidx.compose.animation.animateContentSize
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.AnnotatedString
import com.stt.android.R
import com.stt.android.compose.modifiers.clickable
import com.stt.android.compose.theme.dividerColor
import com.stt.android.compose.theme.material3.body
import com.stt.android.compose.theme.material3.bodyXLargeBold
import com.stt.android.compose.theme.spacing

@Composable
fun ChartSection(
    leading: @Composable () -> Unit,
    title: String,
    summary: String,
    expanded: Boolean,
    onExpandUpdate: (<PERSON><PERSON>an) -> Unit,
    modifier: Modifier = Modifier,
    content: @Composable () -> Unit,
) = ChartSection(
    leading = leading,
    content = content,
    title = AnnotatedString(title),
    summary = summary,
    expanded = expanded,
    onExpandUpdate = onExpandUpdate,
    modifier = modifier,
)

@Composable
fun ChartSection(
    leading: @Composable () -> Unit,
    title: AnnotatedString,
    summary: String,
    expanded: Boolean,
    onExpandUpdate: (Boolean) -> Unit,
    modifier: Modifier = Modifier,
    content: @Composable () -> Unit,
) {
    Column(modifier = modifier.animateContentSize()) {
        Row(
            modifier = Modifier
                .clickable(onClick = { onExpandUpdate(!expanded) })
                .fillMaxWidth()
                .padding(MaterialTheme.spacing.medium),
            horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.medium),
            verticalAlignment = Alignment.CenterVertically
        ) {
            leading()
            Column(
                modifier = Modifier.weight(1f),
                verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.xsmall),
            ) {
                Text(
                    text = title,
                    style = MaterialTheme.typography.bodyXLargeBold,
                    color = MaterialTheme.colorScheme.onSurface,
                )
                Text(
                    text = summary,
                    style = MaterialTheme.typography.body,
                    color = MaterialTheme.colorScheme.secondary,
                )
            }
            val iconRes = if (expanded) {
                R.drawable.ic_chevron_up_24
            } else {
                R.drawable.ic_chevron_down_24
            }
            Icon(
                painter = painterResource(iconRes),
                contentDescription = null,
                tint = MaterialTheme.colorScheme.onSurface,
            )
        }
        if (expanded) {
            content()
        } else {
            HorizontalDivider(color = MaterialTheme.colorScheme.dividerColor)
        }
    }
}
