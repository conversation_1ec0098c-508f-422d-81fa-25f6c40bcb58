package com.stt.android.home.dashboard.activitydata

import android.os.Parcel
import android.os.Parcelable
import com.stt.android.ui.components.TimePickerWithMinuteInterval

data class Bedtime(
    val startHour: Int,
    val startMinute: Int,
    val endHour: Int,
    val endMinute: Int,
    val minutesInterval: Int = TimePickerWithMinuteInterval.DEFAULT_MINUTE_INTERVAL
) : Parcelable {
    constructor(parcel: Parcel) : this(
        parcel.readInt(),
        parcel.readInt(),
        parcel.readInt(),
        parcel.readInt(),
        parcel.readInt()
    )

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeInt(startHour)
        parcel.writeInt(startMinute)
        parcel.writeInt(endHour)
        parcel.writeInt(endMinute)
        parcel.writeInt(minutesInterval)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<Bedtime> {
        override fun createFromParcel(parcel: Parcel): Bedtime {
            return Bedtime(parcel)
        }

        override fun newArray(size: Int): Array<Bedtime?> {
            return arrayOfNulls(size)
        }
    }
}
