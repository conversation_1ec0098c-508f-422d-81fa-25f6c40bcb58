package com.stt.android.home.dashboard

import android.content.SharedPreferences
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.CurrentUserController
import com.stt.android.di.ExploreMapPreferences
import com.stt.android.domain.di.IoThread
import com.stt.android.domain.di.MainThread
import com.stt.android.domain.diarycalendar.GetWorkoutStatisticsWithSummaryUseCase
import com.stt.android.domain.user.subscription.IsSubscribedToPremiumUseCase
import com.stt.android.home.dashboard.widget.WidgetDataFetcher
import com.stt.android.home.dashboard.widget.customization.CheckPremiumSubscriptionForWidgetTypeUseCase
import com.stt.android.home.dashboard.widget.customization.CheckWidgetPlacementAllowedUseCase
import com.stt.android.home.dashboard.widget.customization.SelectedDashboardWidgetsRepository
import com.stt.android.home.dashboardnew.customization.guidance.DashboardCustomizationGuidanceTrigger
import com.stt.android.home.diary.diarycalendar.DiaryCalendarListContainerBuilder
import com.stt.android.maps.location.SuuntoLocationSource
import com.stt.android.menstrualcycle.domain.ObservableMenstrualCycleUpdateUseCase
import com.stt.android.models.MapSelectionModel
import com.stt.android.utils.CalendarProvider
import com.stt.android.utils.toV2
import com.stt.android.watch.GetWatchCapabilities
import com.stt.android.watch.SuuntoWatchModel
import dagger.hilt.android.lifecycle.HiltViewModel
import io.reactivex.Scheduler
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.reactive.asFlow
import javax.inject.Inject

@HiltViewModel
class DashboardGridViewModel @Inject constructor(
    currentUserController: CurrentUserController,
    getWorkoutStatisticsWithSummaryUseCase: GetWorkoutStatisticsWithSummaryUseCase,
    diaryCalendarListContainerBuilder: DiaryCalendarListContainerBuilder,
    mapSelectionModel: MapSelectionModel,
    dashboardAnalytics: DashboardAnalytics,
    locationSource: SuuntoLocationSource,
    @ExploreMapPreferences private val exploreMapPreferences: SharedPreferences,
    widgetDataFetcher: WidgetDataFetcher,
    private val suuntoWatchModel: SuuntoWatchModel,
    calendarProvider: CalendarProvider,
    selectedDashboardWidgetsRepository: SelectedDashboardWidgetsRepository,
    checkWidgetPlacementAllowedUseCase: CheckWidgetPlacementAllowedUseCase,
    isSubscribedToPremiumUseCase: IsSubscribedToPremiumUseCase,
    checkPremiumSubscriptionForWidgetTypeUseCase: CheckPremiumSubscriptionForWidgetTypeUseCase,
    dashboardCustomizationGuidanceTrigger: DashboardCustomizationGuidanceTrigger,
    private val watchCapabilities: GetWatchCapabilities,
    observableMenstrualCycleUpdateUseCase: ObservableMenstrualCycleUpdateUseCase,
    sharedPreferences: SharedPreferences,
    @IoThread ioThread: Scheduler,
    @MainThread mainThread: Scheduler,
    coroutinesDispatchers: CoroutinesDispatchers
) : BaseDashboardGridViewModel(
    currentUserController,
    getWorkoutStatisticsWithSummaryUseCase,
    diaryCalendarListContainerBuilder,
    mapSelectionModel,
    dashboardAnalytics,
    locationSource,
    exploreMapPreferences,
    widgetDataFetcher,
    calendarProvider,
    selectedDashboardWidgetsRepository,
    checkWidgetPlacementAllowedUseCase,
    isSubscribedToPremiumUseCase,
    checkPremiumSubscriptionForWidgetTypeUseCase,
    dashboardCustomizationGuidanceTrigger,
    observableMenstrualCycleUpdateUseCase,
    sharedPreferences,
    ioThread,
    mainThread,
    coroutinesDispatchers
) {

    override suspend fun isHrvSupported(): Boolean = runCatching {
        watchCapabilities.getCapabilitiesForAllWatches()
            .any { it.capabilities?.supportsHrv == true }
    }.getOrElse { false }

    override fun useNoWatchPairedInWidgetAnalytics() = suuntoWatchModel.currentWatch
        .toV2()
        .toFlowable()
        .asFlow()
        .map { false } // emits when watch paired -> tell analytics to use real widget data availability statuses
        .catch { emit(true) } // throws when no watch paired -> tell analytics to use no watch paired status
}
