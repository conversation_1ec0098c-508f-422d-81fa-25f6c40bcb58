package com.stt.android.home.dayviewv2.usecase

import com.stt.android.R
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.domain.EnergyUtil
import com.stt.android.domain.activitydata.dailyvalues.FetchDailyEnergyUseCase
import com.stt.android.domain.activitydata.goals.FetchEnergyGoalUseCase
import com.stt.android.home.dayviewv2.CaloriesViewData
import com.stt.android.home.dayviewv2.DayViewExpandable
import com.stt.android.home.dayviewv2.TrendChartAxisRange
import com.stt.android.home.dayviewv2.TrendChartData
import com.stt.android.home.dayviewv2.TrendChartSeries
import com.suunto.algorithms.data.Energy
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.flowOn
import java.time.Clock
import java.time.LocalDate
import javax.inject.Inject
import kotlin.math.roundToInt

class FetchDailyCaloriesUseCase @Inject constructor(
    private val fetchDailyTrendDataUseCase: FetchDailyTrendDataUseCase,
    private val fetchDailyEnergyUseCase: FetchDailyEnergyUseCase,
    private val fetchEnergyGoalUseCase: FetchEnergyGoalUseCase,
    private val fetchExpandedStateUseCase: FetchExpandedStateUseCase,
    private val dispatchers: CoroutinesDispatchers,
    private val clock: Clock,
) {
    operator fun invoke(date: LocalDate): Flow<CaloriesViewData?> = combine(
        fetchDailyTrendDataUseCase(date),
        fetchDailyEnergyUseCase.fetchMetabolicEnergy(),
        fetchDailyEnergyUseCase.fetchEnergy(),
        fetchEnergyGoalUseCase.fetchEnergyGoal(),
        fetchExpandedStateUseCase(DayViewExpandable.Calories),
    ) { trendData, bmr, dailyEnergy, energyGoal, expanded ->
        val samples = trendData.filter { it.energy.inKcal > 0.0 }

        val activeEnergy = if (date != LocalDate.now()) {
            samples.map { it.energy }.reduceOrNull { sum, value -> sum + value } ?: Energy.ZERO
        } else dailyEnergy

        if (activeEnergy.inCal <= 0.0) return@combine null

        val chartData = if (samples.isNotEmpty()) {
            val series = TrendChartSeries(
                colorRes = R.color.activity_data_energy,
                x = samples.map { it.timeISO8601.toEpochSecond() },
                y = samples.map { it.energy.inKcal },
            )

            val (minX, maxX) = date.epochSecondRange
            TrendChartData(
                series = listOf(series),
                axisRange = TrendChartAxisRange(
                    minX = minX,
                    maxX = maxX,
                    minY = 0.0,
                    maxY = series.y.max().toInt().ceilY(100).toDouble(),
                )
            )
        } else null

        val scaledBmr = if (date == LocalDate.now()) {
            // For the current day, scale BMR according to the hour of day
            EnergyUtil.scaledBmrForToday(bmr, clock)
        } else bmr

        CaloriesViewData(
            activeCalories = activeEnergy.inKcal.roundToInt(),
            totalCalories = (activeEnergy.inKcal + scaledBmr.inKcal).roundToInt(),
            goal = energyGoal.inKcal.roundToInt(),
            expanded = expanded,
            chartData = chartData,
        )
    }.flowOn(dispatchers.io)
}
