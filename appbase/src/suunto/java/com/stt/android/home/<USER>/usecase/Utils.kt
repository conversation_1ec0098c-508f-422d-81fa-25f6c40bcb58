package com.stt.android.home.dayviewv2.usecase

import java.time.LocalDate
import java.time.ZoneId
import kotlin.math.ceil

val LocalDate.epochSecondRange: Pair<Long, Long>
    get() = Pair(
        atStartOfDay(ZoneId.systemDefault()).toEpochSecond(),
        plusDays(1).atStartOfDay(ZoneId.systemDefault()).toEpochSecond(),
    )

fun Int.ceilY(step: Int, multiplier: Float = 3f): Int {
    return (ceil(this / multiplier / step).coerceAtLeast(1f) * multiplier * step).toInt()
}

fun adjustBpmRange(min: Int, max: Int): Pair<Int, Int> {
    val adjustedMax = max.ceilY(10, multiplier = 1f)
    val diff = (adjustedMax - min).ceilY(10)
    return Pair(adjustedMax - diff, adjustedMax)
}

fun adjustPercentRange(min: Int, max: Int): Pair<Int, Int> {
    val adjustedMax = max.ceilY(5, multiplier = 1f)
    val diff = (adjustedMax - min).ceilY(5)
    return Pair(adjustedMax - diff, adjustedMax)
}
