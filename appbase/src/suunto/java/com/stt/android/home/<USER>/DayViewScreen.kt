package com.stt.android.home.dayviewv2

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.WindowInsetsSides
import androidx.compose.foundation.layout.asPaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.only
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.systemBars
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.GridItemSpan
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.withStyle
import com.stt.android.R
import com.stt.android.compose.component.SuuntoTopBar
import com.stt.android.compose.layout.ContentCenteringColumn
import com.stt.android.compose.theme.SuuntoIcons
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.material3.bodySmallBold
import com.stt.android.compose.theme.spacing
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.home.InsertMenstrualCycleViewModel
import com.stt.android.home.dayviewv2.widget.ChartSection
import com.stt.android.home.dayviewv2.widget.GoalWheel
import com.stt.android.home.dayviewv2.widget.MenstrualCyclePeriodView
import com.stt.android.home.dayviewv2.widget.TargetSettingView
import com.stt.android.home.dayviewv2.widget.TrendBarChart
import com.stt.android.home.dayviewv2.widget.TrendDotChart
import com.stt.android.home.dayviewv2.widget.TrendLineChart
import com.stt.android.home.dayviewv2.widget.WeekDayView
import com.stt.android.home.dayviewv2.widget.WorkoutStatisticView
import com.stt.android.menstrualcycle.LoggedFrom
import com.stt.android.ui.components.workout.WorkoutCard
import com.stt.android.ui.components.workout.WorkoutCardViewModel
import java.time.LocalDate
import com.stt.android.core.R as CR

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DayViewScreen(
    viewModel: DayViewViewModel,
    insertMenstrualCycleViewModel: InsertMenstrualCycleViewModel,
    deleteMenstrualCycleViewModel: DeleteMenstrualCycleViewModel,
    onNavigateUp: () -> Unit,
    onAddManualWorkoutClicked: () -> Unit,
    onWorkoutClicked: (WorkoutHeader) -> Unit,
    onTargetSettingClicked: () -> Unit,
    modifier: Modifier = Modifier,
) {
    val uiState by viewModel.uiState.collectAsState()

    var addPeriodDate by rememberSaveable { mutableStateOf<LocalDate?>(null) }

    var showOverflowMenu by rememberSaveable { mutableStateOf(false) }

    Scaffold(
        modifier = modifier,
        topBar = {
            SuuntoTopBar(
                title = (uiState as? DayViewUiState.Loaded)?.title ?: "",
                onNavigationClick = onNavigateUp,
                actions = {
                    IconButton(onClick = { showOverflowMenu = true }) {
                        Icon(
                            modifier = Modifier.size(MaterialTheme.iconSizes.small),
                            painter = SuuntoIcons.ActionPlus.asPainter(),
                            contentDescription = null,
                        )
                    }
                    DropdownMenu(
                        expanded = showOverflowMenu,
                        onDismissRequest = { showOverflowMenu = false },
                        containerColor = MaterialTheme.colorScheme.surface,
                    ) {
                        addPeriodDate?.let { date ->
                            DropdownMenuItem(
                                text = { Text(text = stringResource(R.string.toolbar_add_period)) },
                                onClick = {
                                    showOverflowMenu = false
                                    insertMenstrualCycleViewModel.addDateToMenstrualCycle(
                                        date,
                                        LoggedFrom.DAY_VIEW,
                                    )
                                },
                            )
                        }
                        DropdownMenuItem(
                            text = { Text(text = stringResource(R.string.toolbar_add_manual_workout)) },
                            onClick = {
                                showOverflowMenu = false
                                onAddManualWorkoutClicked()
                            },
                        )
                    }
                },
            )
        },
        contentWindowInsets = WindowInsets.systemBars.only(WindowInsetsSides.Horizontal + WindowInsetsSides.Top),
    ) { padding ->
        ContentCenteringColumn(modifier = Modifier.padding(padding)) {
            when (val uiState = uiState) {
                DayViewUiState.Initial -> Unit
                is DayViewUiState.Loaded -> DayViewLoaded(
                    modifier = Modifier.fillMaxSize(),
                    uiState = uiState,
                    onDateSelected = viewModel::onDateSelected,
                    onPageSelected = viewModel::onPageSelected,
                    onWorkoutClicked = onWorkoutClicked,
                    onAddPeriodDateUpdated = { addPeriodDate = it },
                    onPeriodDayClicked = deleteMenstrualCycleViewModel::deletePeriodDay,
                    onExpandUpdated = viewModel::onExpandUpdated,
                    onTargetSettingClicked = onTargetSettingClicked,
                )
            }
        }
    }
}

@Composable
private fun DayViewLoaded(
    uiState: DayViewUiState.Loaded,
    onDateSelected: (LocalDate) -> Unit,
    onPageSelected: (Int) -> Unit,
    onWorkoutClicked: (WorkoutHeader) -> Unit,
    onAddPeriodDateUpdated: (LocalDate?) -> Unit,
    onPeriodDayClicked: (LocalDate) -> Unit,
    onExpandUpdated: (DayViewExpandable, Boolean) -> Unit,
    onTargetSettingClicked: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Column(modifier = modifier.background(MaterialTheme.colorScheme.surface)) {
        WeekDayView(
            modifier = Modifier.fillMaxWidth(),
            weekStartDate = uiState.weekStartDate,
            weekDayLabels = uiState.weekDayLabels,
            weekDayStates = uiState.weekDayStates,
            onDateClick = onDateSelected,
        )

        val pagerState = rememberPagerState(initialPage = uiState.pageIndex) { uiState.pageCount }
        HorizontalPager(
            modifier = Modifier
                .fillMaxWidth()
                .weight(1f),
            state = pagerState,
        ) { page ->
            val viewData = uiState.dayViewDataMap[page]?.collectAsState()?.value
            viewData?.let {
                DayViewContent(
                    modifier = Modifier.fillMaxSize(),
                    viewData = it,
                    onWorkoutClicked = onWorkoutClicked,
                    onPeriodDayClicked = { onPeriodDayClicked(it.date) },
                    onExpandUpdated = onExpandUpdated,
                    onTargetSettingClicked = onTargetSettingClicked,
                )
            }
            DisposableEffect(pagerState.currentPage, viewData?.isPeriodDay, viewData?.date) {
                if (pagerState.currentPage == page) {
                    val date = if (viewData?.isPeriodDay != true) {
                        viewData?.date
                    } else null
                    onAddPeriodDateUpdated(date)
                }
                onDispose {}
            }
        }

        LaunchedEffect(uiState.pageIndex) {
            if (pagerState.currentPage != uiState.pageIndex) {
                pagerState.scrollToPage(uiState.pageIndex)
            }
        }

        DisposableEffect(pagerState.currentPage) {
            if (pagerState.currentPage != uiState.pageIndex) {
                onPageSelected(pagerState.currentPage)
            }
            onDispose {}
        }
    }
}

@Composable
private fun DayViewContent(
    viewData: DayViewData,
    onWorkoutClicked: (WorkoutHeader) -> Unit,
    onPeriodDayClicked: () -> Unit,
    onExpandUpdated: (DayViewExpandable, Boolean) -> Unit,
    onTargetSettingClicked: () -> Unit,
    modifier: Modifier = Modifier,
) {
    LazyVerticalGrid(
        modifier = modifier,
        columns = GridCells.Fixed(2),
        contentPadding = WindowInsets.systemBars.only(WindowInsetsSides.Bottom).asPaddingValues(),
    ) {
        items(
            viewData.statistics,
            span = { GridItemSpan(1) },
            key = { "statistic-${it.iconRes}" },
            contentType = { "statistic" }
        ) { statistic ->
            WorkoutStatisticView(statistic = statistic)
        }
        if (viewData.isPeriodDay) {
            item(
                span = { GridItemSpan(2) },
                key = "menstrual-cycle-period",
            ) {
                MenstrualCyclePeriodView(
                    modifier = Modifier.padding(horizontal = MaterialTheme.spacing.medium),
                    onClick = onPeriodDayClicked,
                )
            }
        }
        item(span = { GridItemSpan(2) }) {
            Spacer(modifier = Modifier.height(MaterialTheme.spacing.medium))
        }
        items(
            viewData.workouts,
            span = { GridItemSpan(2) },
            key = { "workout-${it.id}" },
            contentType = { "workout" },
        ) { workoutHeader ->
            WorkoutCard(
                modifier = Modifier
                    .padding(horizontal = MaterialTheme.spacing.medium)
                    .padding(bottom = MaterialTheme.spacing.medium),
                workoutHeader = workoutHeader,
                configuration = WorkoutCardViewModel.Configuration(showDate = false),
                onClick = { onWorkoutClicked(workoutHeader) },
            )
        }
        viewData.heartRateViewData?.let { heartRateViewData ->
            item(
                span = { GridItemSpan(2) },
                key = "heart-rate-chart",
            ) {
                ChartSection(
                    modifier = Modifier.fillMaxWidth(),
                    leading = {
                        Icon(
                            modifier = Modifier.size(MaterialTheme.iconSizes.medium),
                            painter = painterResource(R.drawable.ic_activity_data_daily_hr),
                            contentDescription = null,
                            tint = Color.Unspecified,
                        )
                    },
                    title = buildAnnotatedString {
                        append("${heartRateViewData.min}-${heartRateViewData.max}")
                        withStyle(MaterialTheme.typography.bodySmallBold.toSpanStyle()) {
                            append(" " + stringResource(CR.string.bpm))
                        }
                    },
                    summary = stringResource(R.string.heart_rate),
                    expanded = heartRateViewData.expanded,
                    onExpandUpdate = { onExpandUpdated(DayViewExpandable.Hr, it) },
                ) {
                    heartRateViewData.chartData?.let { chartData ->
                        TrendLineChart(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(
                                    horizontal = MaterialTheme.spacing.medium,
                                    vertical = MaterialTheme.spacing.small,
                                ),
                            chartData = chartData,
                        )
                    }
                }
            }
        }
        viewData.spO2ViewData?.let { spO2ViewData ->
            item(
                span = { GridItemSpan(2) },
                key = "spo2-chart",
            ) {
                ChartSection(
                    modifier = Modifier.fillMaxWidth(),
                    leading = {
                        Icon(
                            modifier = Modifier.size(MaterialTheme.iconSizes.medium),
                            painter = painterResource(R.drawable.ic_spo2_icon),
                            contentDescription = null,
                            tint = Color.Unspecified,
                        )
                    },
                    title = buildAnnotatedString {
                        append(spO2ViewData.average.toString())
                        withStyle(MaterialTheme.typography.bodySmallBold.toSpanStyle()) {
                            append(" %")
                        }
                    },
                    summary = buildList {
                        add(stringResource(R.string.diary_item_spo2_unit_header))
                        spO2ViewData.averageAltitude?.let {
                            add(stringResource(CR.string.all_altitude))
                            add(it.toString())
                            add(stringResource(spO2ViewData.altitudeUnitRes))
                        }
                    }.joinToString(" "),
                    expanded = spO2ViewData.expanded,
                    onExpandUpdate = { onExpandUpdated(DayViewExpandable.SpO2, it) },
                ) {
                    spO2ViewData.chartData?.let { chartData ->
                        TrendDotChart(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(
                                    horizontal = MaterialTheme.spacing.medium,
                                    vertical = MaterialTheme.spacing.small,
                                ),
                            chartData = chartData,
                        )
                    }
                }
            }
        }
        viewData.stepsViewData?.let { stepsViewData ->
            item(
                span = { GridItemSpan(2) },
                key = "steps-chart",
            ) {
                ChartSection(
                    modifier = Modifier.fillMaxWidth(),
                    leading = {
                        GoalWheel(
                            value = stepsViewData.steps,
                            goal = stepsViewData.goal,
                            color = colorResource(R.color.activity_data_steps),
                            icon = painterResource(R.drawable.ic_dashboard_widget_steps),
                        )
                    },
                    title = stepsViewData.steps.toString(),
                    summary = stringResource(R.string.diary_item_steps_unit_header),
                    expanded = stepsViewData.expanded,
                    onExpandUpdate = { onExpandUpdated(DayViewExpandable.Steps, it) },
                ) {
                    stepsViewData.chartData?.let { chartData ->
                        TrendBarChart(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(
                                    horizontal = MaterialTheme.spacing.medium,
                                    vertical = MaterialTheme.spacing.small,
                                ),
                            chartData = chartData,
                        )
                    }
                }
            }
        }
        viewData.caloriesViewData?.let { caloriesViewData ->
            item(
                span = { GridItemSpan(2) },
                key = "calories-chart",
            ) {
                ChartSection(
                    modifier = Modifier.fillMaxWidth(),
                    leading = {
                        GoalWheel(
                            value = caloriesViewData.activeCalories,
                            goal = caloriesViewData.goal,
                            color = colorResource(R.color.activity_data_energy),
                            icon = painterResource(R.drawable.ic_dashboard_widget_calories),
                        )
                    },
                    title = caloriesViewData.activeCalories.toString(),
                    summary = listOf(
                        stringResource(R.string.diary_item_total_calories_prefix),
                        caloriesViewData.totalCalories.toString(),
                        stringResource(CR.string.kcal),
                    ).joinToString(" "),
                    expanded = caloriesViewData.expanded,
                    onExpandUpdate = { onExpandUpdated(DayViewExpandable.Calories, it) },
                ) {
                    caloriesViewData.chartData?.let { chartData ->
                        TrendBarChart(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(
                                    horizontal = MaterialTheme.spacing.medium,
                                    vertical = MaterialTheme.spacing.small,
                                ),
                            chartData = chartData,
                        )
                    }
                }
            }
        }
        item(
            span = { GridItemSpan(2) },
            key = "target-setting",
        ) {
            TargetSettingView(
                modifier = Modifier.padding(horizontal = MaterialTheme.spacing.medium),
                onClick = onTargetSettingClicked,
            )
        }
    }
}
