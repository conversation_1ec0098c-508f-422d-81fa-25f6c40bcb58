package com.stt.android.home.settings

import android.content.Context
import android.util.AttributeSet
import com.stt.android.STTApplication
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.watch.MissingCurrentWatchException
import com.stt.android.watch.SuuntoWatchModel
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

class MeasurementUnitPreference: BaseMeasurementUnitPreference {
    @Inject
    lateinit var suuntoWatchModel: SuuntoWatchModel

    @Inject
    lateinit var coroutinesDispatchers: CoroutinesDispatchers

    private lateinit var coroutineScope: CoroutineScope

    constructor(context: Context) : this(context, null)
    constructor(context: Context, attrs: AttributeSet?) : this(context, attrs, android.R.attr.preferenceStyle)
    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : this(context, attrs, defStyleAttr, 0)
    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int, defStyleRes: Int): super(context, attrs, defStyleAttr, defStyleRes)

    init {
        STTApplication.getComponent().inject(this)
    }

    override fun onAttached() {
        super.onAttached()

        coroutineScope = CoroutineScope(coroutinesDispatchers.main + SupervisorJob())

        // Measurement unit should be fetched from the watch when a watch is paired.
        coroutineScope.launch {
            runSuspendCatching {
                val isPaired = suuntoWatchModel.watchStates()
                    .first()
                    .isPaired
                isEnabled = !isPaired
            }.onFailure { e ->
                if (e is MissingCurrentWatchException) {
                    isEnabled = true
                } else {
                    Timber.w(e, "Error while checking if a watch is paired")
                }
            }
        }
    }

    override fun onDetached() {
        if (::coroutineScope.isInitialized) {
            coroutineScope.cancel()
        }
        super.onDetached()
    }
}
