package com.stt.android.home.dashboard.bottomsheet

import android.content.Context
import android.content.DialogInterface
import android.content.SharedPreferences
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.SeekBar
import androidx.core.content.edit
import com.stt.android.databinding.WeeklyGoalBottomSheetBinding
import com.stt.android.ui.utils.SmartBottomSheetDialogFragment
import com.stt.android.ui.utils.TextFormatter
import com.stt.android.utils.STTConstants
import dagger.hilt.android.AndroidEntryPoint
import java.util.Locale
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import com.stt.android.core.R as CR

/**
 * todo refactor to viewbinding and viewmodels
 */
@AndroidEntryPoint
class WeeklyGoalBottomSheetFragment :
    SmartBottomSheetDialogFragment(),
    WeeklyGoalBottomSheetView {

    @Suppress("DEPRECATION")
    @Inject
    lateinit var presenter: WeeklyGoalBottomSheetPresenter

    @Inject
    lateinit var sharedPreferences: SharedPreferences

    lateinit var listener: BottomSheetUpdateListener

    private var binding: WeeklyGoalBottomSheetBinding? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = WeeklyGoalBottomSheetBinding.inflate(inflater, container, false)
        return binding?.root
    }

    override fun onDestroyView() {
        super.onDestroyView()
        binding = null
    }

    @Deprecated("Deprecated in Java")
    override fun onActivityCreated(savedInstanceState: Bundle?) {
        super.onActivityCreated(savedInstanceState)

        setInitialState()
        attachEventListeners()
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)

        if (context is BottomSheetUpdateListener) {
            listener = context
        } else {
            throw IllegalArgumentException(
                (context.javaClass.canonicalName ?: "WeeklyGoalBottomSheetFragment context") +
                    " should implement " +
                    BottomSheetUpdateListener::class.java.canonicalName
            )
        }
    }

    override fun onStart() {
        super.onStart()
        presenter.takeView(this)
    }

    override fun onStop() {
        super.onStop()
        presenter.dropView()
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)

        listener.onWeeklyTargetSettingDismissed()
    }

    private fun setInitialState() = binding?.run {
        val hour = getString(CR.string.hour)
        trainingDurationMin.text = String.format(Locale.US, "%d %s", SEEKBAR_DURATION_IN_HOURS_MIN, hour)
        trainingDurationMax.text = String.format(Locale.US, "%d %s", SEEKBAR_DURATION_IN_HOURS_MAX, hour)

        val seekbarInitialValue = sharedPreferences.getInt(
            STTConstants.SuuntoPreferences
                .KEY_SUUNTO_WATCH_WEEKLY_TARGET_DURATION,
            WeeklyGoalBottomSheetPresenter.DEFAULT_WEEKLY_GOAL
        )

        val coachEnabledInitialValue = sharedPreferences.getBoolean(
            STTConstants
                .SuuntoPreferences.KEY_SUUNTO_WATCH_COACH_ENABLED,
            false
        )

        setTrainingDurationGroupVisible(!coachEnabledInitialValue)
        setTrainingPlanTextViewVisible(coachEnabledInitialValue)
        switchTrainingPlan.isChecked = coachEnabledInitialValue

        updateDurationTextView(seekbarInitialValue)
        seekbarTrainingDuration.max = SEEKBAR_DURATION_IN_HOURS_MAX
        seekbarTrainingDuration.progress = presenter.durationToProgress(seekbarInitialValue)
    }

    private fun attachEventListeners() = binding?.run {
        switchTrainingPlan.setOnCheckedChangeListener { _, isChecked ->
            presenter
                .trainingGuidanceSwitchChange(isChecked)
        }

        seekbarTrainingDuration.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar, progress: Int, fromUser: Boolean) {
                if (fromUser) {
                    presenter.seekBarProgressChanged(progress)
                }
            }

            override fun onStartTrackingTouch(seekBar: SeekBar) {}

            override fun onStopTrackingTouch(seekBar: SeekBar) {
                presenter.seekBarStopTrackingTouch(seekBar.progress)
            }
        })
    }

    override fun setTrainingDurationGroupVisible(visible: Boolean) {
        val visibility = if (visible) View.VISIBLE else View.GONE
        binding?.groupTrainingDurationSettings?.visibility = visibility
    }

    override fun setTrainingPlanGroupVisible(visible: Boolean) {
        val visibility = if (visible) View.VISIBLE else View.GONE
        binding?.groupTrainingPlan?.visibility = visibility
    }

    override fun setTrainingPlanTextViewVisible(visible: Boolean) {
        val visibility = if (visible) View.VISIBLE else View.GONE
        binding?.textviewTrainingPlanEnabled?.visibility = visibility
    }

    override fun setWeeklyTargetSyncNeededToSharedPreferences(syncNeeded: Boolean) {
        sharedPreferences.edit {
            putBoolean(STTConstants.SuuntoPreferences.KEY_WATCH_WEEKLY_TARGET_SYNC_NEEDED, syncNeeded)
        }
    }

    override fun setCoachEnabledSyncNeededToSharedPreferences(syncNeeded: Boolean) {
        sharedPreferences.edit {
            putBoolean(STTConstants.SuuntoPreferences.KEY_WATCH_COACH_ENABLED_SYNC_NEEDED, syncNeeded)
        }
    }

    override fun updateDurationTextView(value: Int) {
        val duration = TextFormatter.formatElapsedTimeWithUnit(resources, value.toLong())
        binding?.textviewTrainingDuration?.text = duration
    }

    override fun setWeeklyTargetToSharedPreferences(seconds: Int) {
        sharedPreferences.edit {
            putInt(STTConstants.SuuntoPreferences.KEY_SUUNTO_WATCH_WEEKLY_TARGET_DURATION, seconds)
        }
    }

    override fun setCoachEnabledToSharedPreferences(coachEnabled: Boolean) {
        sharedPreferences.edit {
            putBoolean(STTConstants.SuuntoPreferences.KEY_SUUNTO_WATCH_COACH_ENABLED, coachEnabled)
        }
    }

    override fun notifyListenerWeeklyTargetChanged(weeklyTarget: Int) {
        listener.onWeeklyTargetDurationChanged(weeklyTarget)
    }

    override fun notifyListenerCoachEnabledChanged(coachEnabled: Boolean) {
        listener.onCoachEnabled(coachEnabled)
    }

    interface BottomSheetUpdateListener {
        fun onWeeklyTargetDurationChanged(weeklyTarget: Int)
        fun onWeeklyTargetSettingDismissed()
        fun onCoachEnabled(enabled: Boolean)
    }

    companion object {
        const val FRAGMENT_TAG = "WeeklyGoalBottomSheetFragment.FRAGMENT_TAG"
        const val SEEKBAR_DURATION_IN_HOURS_MIN = 1
        const val SEEKBAR_DURATION_IN_HOURS_MAX = 25
    }

    override fun getWeeklyTargetDurationFromSharedPrefs(): Int {
        return sharedPreferences.getInt(
            STTConstants.SuuntoPreferences
                .KEY_SUUNTO_WATCH_WEEKLY_TARGET_DURATION,
            TimeUnit.HOURS.toSeconds(3).toInt()
        )
    }
}
