package com.stt.android.home.dashboard.activitydata

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.SeekBar
import android.widget.TextView
import androidx.annotation.LayoutRes
import com.stt.android.R
import com.stt.android.ui.utils.SmartBottomSheetDialogFragment

// Note: derived classes should use @AndroidEntryPoint annotation
abstract class BaseDailyTargetBottomSheetDialogFragment :
    SmartBottomSheetDialogFragment(),
    SeekBar.OnSeekBarChangeListener {
    fun interface GoalUpdateListener {
        fun goalUpdated(goal: Int)
    }

    protected var bottomSheetTitle: TextView? = null
    protected var seekbarTitle: TextView? = null
    protected var seekbarValue: TextView? = null
    protected var seekBar: SeekBar? = null

    abstract val maxActivityDataValue: Int
    abstract val minActivityDataValue: Int
    abstract val seekbarThreshold: Int
    abstract val seekbarSmallStep: Int
    abstract val seekbarBigStep: Int
    abstract val TAG: String

    var goalUpdateListener: GoalUpdateListener? = null

    protected var currentGoal: Int = 0

    protected var cancelListener: BottomSheetCancelListener? = null

    protected var valueChanged = false

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        val view = inflater.inflate(getLayoutId(), container, false)

        // Use findViewById instead of view binding due to inherited fragments that override
        // layout ID
        bottomSheetTitle = view.findViewById(R.id.bottomSheetTitle)
        seekbarTitle = view.findViewById(R.id.seekbarTitle)
        seekbarValue = view.findViewById(R.id.seekbarValue)
        seekBar = view.findViewById(R.id.seekBar)

        bottomSheetTitle?.setText(getBottomsheetTitleText())
        seekbarTitle?.setText(getSeekbarTitleText())
        seekBar?.setOnSeekBarChangeListener(this)
        seekBar?.max = maxActivityDataValue - minActivityDataValue
        arguments?.getInt(KEY_ACTIVITY_DATA_GOAL)?.let {
            seekBar?.progress = it - minActivityDataValue
        }
        return view
    }

    override fun onDestroyView() {
        super.onDestroyView()

        bottomSheetTitle = null
        seekbarTitle = null
        seekbarValue = null
        seekBar = null
    }

    abstract fun setSeekbarValueText(currentVal: Int)

    abstract fun getBottomsheetTitleText(): Int

    abstract fun getSeekbarTitleText(): Int

    private fun getSeekbarStepValue(progress: Int): Int {
        return if (progress in 0 until seekbarThreshold - minActivityDataValue) seekbarSmallStep else seekbarBigStep
    }

    @LayoutRes
    abstract fun getLayoutId(): Int

    override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
        if (fromUser) {
            // Changes done by user
            val incrementSize = getSeekbarStepValue(progress)
            currentGoal = ((progress + minActivityDataValue) / incrementSize) * incrementSize
            valueChanged = true
            goalUpdateListener?.goalUpdated(currentGoal)
        } else {
            // First time showing seekbar
            arguments?.getInt(KEY_ACTIVITY_DATA_GOAL)?.let {
                currentGoal = it
            }
        }
        setSeekbarValueText(currentGoal)
    }

    override fun onStartTrackingTouch(seekBar: SeekBar?) {
    }

    override fun onStopTrackingTouch(seekBar: SeekBar?) {
    }

    fun setBottomSheetCancelListener(listener: BottomSheetCancelListener) {
        cancelListener = listener
    }

    companion object {
        const val KEY_ACTIVITY_DATA_GOAL =
            "com.stt.android.home.dashboard.activitydata.BaseDailyTargetBottomSheetDialogFragment.GOAL"
    }
}
