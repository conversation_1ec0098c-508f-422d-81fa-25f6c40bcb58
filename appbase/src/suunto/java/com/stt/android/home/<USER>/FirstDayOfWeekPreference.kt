package com.stt.android.home.settings

import android.content.Context
import android.util.AttributeSet
import com.stt.android.STTApplication
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.watch.MissingCurrentWatchException
import com.stt.android.watch.SuuntoWatchModel
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch
import timber.log.Timber
import java.time.DayOfWeek
import java.util.Locale
import javax.inject.Inject

class FirstDayOfWeekPreference : TitleListPreference {
    @Inject
    lateinit var suuntoWatchModel: SuuntoWatchModel

    private val coroutineScope: CoroutineScope

    constructor(context: Context) : this(context, null)
    constructor(context: Context, attrs: AttributeSet?) : this(context, attrs, android.R.attr.preferenceStyle)
    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : this(context, attrs, defStyleAttr, 0)
    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int, defStyleRes: Int): super(context, attrs, defStyleAttr, defStyleRes)

    init {
        STTApplication.getComponent().inject(this)
        coroutineScope  = CoroutineScope(SupervisorJob())
    }

    override fun onDetached() {
        coroutineScope.cancel()

        super.onDetached()
    }

    override fun persistString(value: String): Boolean {
        if (!super.persistString(value)) {
            return false
        }

        coroutineScope.launch {
            runSuspendCatching {
                suuntoWatchModel.sendFirstDayOfWeek(DayOfWeek.valueOf(value.uppercase(Locale.US)))
            }.onFailure { e ->
                if (e !is MissingCurrentWatchException) {
                    Timber.w(e, "Failed to send first day of week")
                }
            }
        }

        return true
    }
}
