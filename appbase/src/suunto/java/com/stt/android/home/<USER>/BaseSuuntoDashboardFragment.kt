package com.stt.android.home.dashboard

import android.os.Bundle
import android.view.View
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.stt.android.common.ui.observeNotNull
import com.stt.android.social.userprofile.HeadsetNavigator
import com.stt.android.social.userprofile.HeadsetRedDot
import com.stt.android.utils.toEpochMilli
import com.stt.android.watch.DeviceOnboardingNavigator
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoDeviceType
import kotlinx.coroutines.launch
import java.time.LocalDateTime
import javax.inject.Inject

abstract class BaseSuuntoDashboardFragment : BaseDashboardFragment() {
    @Inject
    lateinit var deviceOnboardingNavigator: DeviceOnboardingNavigator

    @Inject
    lateinit var headsetNavigator: HeadsetNavigator

    @Inject
    lateinit var headsetRedDot: HeadsetRedDot

    private val headsetRedDotLiveData = MutableLiveData(false)

    // If no headphones have been connected, we don't need to start the service
    private var syncHeadphoneServiceStarted: Boolean = false

    private var stopTimestamp = 0L

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        viewModel.openOnboardingEvent.observeNotNull(viewLifecycleOwner) {
            openOnboardingActivity(it)
        }
        launchHeadphoneSyncDataService()
    }

    override fun onStart() {
        super.onStart()
        viewModel.checkOnboarding()
        headsetRedDotLiveData.value = headsetRedDot.showHeadsetRedDot()
    }

    override fun getHeadsetRedDotLiveData() = headsetRedDotLiveData

    private fun openOnboardingActivity(suuntoDeviceType: SuuntoDeviceType) {
        deviceOnboardingNavigator.newOnboardingActivityIntent(
            requireContext(),
            suuntoDeviceType
        )?.let { startActivity(it) }
    }

    override fun onStop() {
        super.onStop()
        stopTimestamp = LocalDateTime.now().toEpochMilli()
    }

    private fun launchHeadphoneSyncDataService() {
        lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) {
                // avoid launch headphone service frequently
                if (LocalDateTime.now()
                        .toEpochMilli() - stopTimestamp > LAUNCH_HEADPHONE_SERVICE_INTERVAL
                ) {
                    headsetNavigator.launchSyncDataService(requireContext())
                    syncHeadphoneServiceStarted = true
                }
            }
        }
    }

    override fun onDestroy() {
        if (syncHeadphoneServiceStarted)
            headsetNavigator.stopSyncDataService(requireContext())
        super.onDestroy()
    }

    companion object {
        private const val LAUNCH_HEADPHONE_SERVICE_INTERVAL = 30 * 1000L // unit: ms
    }
}
