package com.stt.android.home.dashboardv2

import android.os.Bundle
import android.view.View
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.analytics.AnalyticsPropertyValue.SuuntoDiaryGraphToggleSource.HOME_SCREEN_DASHBOARD_ACTIVITIES
import com.stt.android.chart.api.ChartNavigator
import com.stt.android.chart.api.model.ChartContent
import com.stt.android.chart.api.model.ChartGranularity
import com.stt.android.chart.api.model.ChartStyle
import com.stt.android.diary.DiaryNavigator
import com.stt.android.diary.recovery.RecoveryNavigator
import com.stt.android.domain.diary.models.GraphTimeRange
import com.stt.android.home.dashboardv2.widgets.ActivityDistanceWidgetInfo
import com.stt.android.home.dashboardv2.widgets.ActivityDurationWidgetInfo
import com.stt.android.home.dashboardv2.widgets.ActivitySpeedWidgetInfo
import com.stt.android.home.dashboardv2.widgets.ActivityTimesWidgetInfo
import com.stt.android.home.dashboardv2.widgets.ActivityWidgetInfo
import com.stt.android.home.dashboardv2.widgets.AscentWidgetInfo
import com.stt.android.home.dashboardv2.widgets.CalendarWidgetInfo
import com.stt.android.home.dashboardv2.widgets.CalorieWidgetInfo
import com.stt.android.home.dashboardv2.widgets.CommuteWidgetInfo
import com.stt.android.home.dashboardv2.widgets.DailyHeartRateWidgetInfo
import com.stt.android.home.dashboardv2.widgets.DurationWidgetInfo
import com.stt.android.home.dashboardv2.widgets.HrvWidgetInfo
import com.stt.android.home.dashboardv2.widgets.Last7DaysHeartRateWidgetInfo
import com.stt.android.home.dashboardv2.widgets.MapWidgetInfo
import com.stt.android.home.dashboardv2.widgets.MenstrualPeriodWidgetInfo
import com.stt.android.home.dashboardv2.widgets.ProgressWidgetInfo
import com.stt.android.home.dashboardv2.widgets.RecoveryWidgetInfo
import com.stt.android.home.dashboardv2.widgets.ResourceWidgetInfo
import com.stt.android.home.dashboardv2.widgets.SleepWidgetInfo
import com.stt.android.home.dashboardv2.widgets.StepWidgetInfo
import com.stt.android.home.dashboardv2.widgets.TSSWidgetInfo
import com.stt.android.home.dashboardv2.widgets.Vo2MaxWidgetInfo
import com.stt.android.home.dashboardv2.widgets.WidgetInfo
import com.stt.android.home.dashboardv2.widgets.WidgetType
import com.stt.android.home.devicetype.DeviceTypeSelectActivity
import com.stt.android.home.diary.diarycalendar.DiaryCalendarListContainer
import com.stt.android.offlinemaps.OfflineMapsSelectionActivity
import com.stt.android.offlinemaps.analytics.OfflineMapsAnalytics
import com.stt.android.social.userprofile.HeadsetNavigator
import com.stt.android.ui.map.selection.MyTracksGranularity
import com.stt.android.utils.toEpochMilli
import com.stt.android.watch.DeviceActivity
import com.stt.android.watch.DeviceOnboardingNavigator
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoDeviceType
import kotlinx.coroutines.launch
import java.time.LocalDateTime
import javax.inject.Inject

internal abstract class BaseSuuntoDashboardFragment : BaseDashboardFragment() {

    @Inject
    lateinit var deviceOnboardingNavigator: DeviceOnboardingNavigator

    @Inject
    lateinit var headsetNavigator: HeadsetNavigator

    @Inject
    lateinit var offlineMapsAnalytics: OfflineMapsAnalytics

    @Inject
    lateinit var chartNavigator: ChartNavigator

    @Inject
    lateinit var diaryNavigator: DiaryNavigator

    @Inject
    lateinit var recoveryNavigator: RecoveryNavigator

    // If no headphones have been connected, we don't need to start the service
    private var syncHeadphoneServiceStarted: Boolean = false

    private var stopTimestamp = 0L

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        viewLifecycleOwner.lifecycleScope.launch {
            viewModel.openDeviceOnboardingFlow
                .collect(::openDeviceOnboardingActivity)
        }
        launchHeadphoneSyncDataService()
    }

    private fun openDeviceOnboardingActivity(suuntoDeviceType: SuuntoDeviceType) {
        deviceOnboardingNavigator.newOnboardingActivityIntent(requireContext(), suuntoDeviceType)
            ?.let { startActivity(it) }
    }

    override fun onStart() {
        super.onStart()
        viewModel.checkIfDeviceOnboardingIsNeeded()
        toolbarViewModel.observeWatchState()
        toolbarViewModel.checkSupportOfflineMaps()
    }

    override fun onStop() {
        super.onStop()
        stopTimestamp = LocalDateTime.now().toEpochMilli()
    }

    private fun launchHeadphoneSyncDataService() {
        lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) {
                toolbarViewModel.showHeadsetEntrance.collect { show ->
                    // avoid launch headphone service frequently
                    if (show && LocalDateTime.now()
                            .toEpochMilli() - stopTimestamp > LAUNCH_HEADPHONE_SERVICE_INTERVAL
                    ) {
                        headsetNavigator.launchSyncDataService(requireContext())
                        syncHeadphoneServiceStarted = true
                    }
                }
            }
        }
    }

    override fun onDestroy() {
        if (syncHeadphoneServiceStarted)
            headsetNavigator.stopSyncDataService(requireContext())
        super.onDestroy()
    }

    override fun openWatchDevice() {
        val context = requireContext()
        context.startActivity(DeviceActivity.newStartIntent(context))
    }

    override fun openHeadsetDevice() {
        headsetNavigator.launchHeadsetActivity(requireContext())
    }

    override fun downloadOfflineMap() {
        val context = requireContext()
        context.startActivity(
            OfflineMapsSelectionActivity.newStartIntent(
                context,
                AnalyticsPropertyValue.DownloadMapsScreenSource.HOME_SCREEN_MENU,
                offlineMapsAnalytics,
            )
        )
    }

    override fun pairDevice() {
        val context = requireContext()
        context.startActivity(
            DeviceTypeSelectActivity.newStartIntent(context)
        )
    }

    override fun openWidget(widgetInfo: WidgetInfo) {
        val chartContent: ChartContent
        val chartGranularity: ChartGranularity
        when (widgetInfo) {
            is ActivityDistanceWidgetInfo,
            is ActivityDurationWidgetInfo,
            is ActivitySpeedWidgetInfo,
            is ActivityTimesWidgetInfo -> {
                openDiaryCalendar(
                    granularity = DiaryCalendarListContainer.Granularity.WEEK,
                    showActivitiesList = true,
                    source = HOME_SCREEN_DASHBOARD_ACTIVITIES,
                )
                return
            }
            is ActivityWidgetInfo -> {
                val timeRange = when (widgetInfo.granularityType) {
                    MyTracksGranularity.Type.THIS_WEEK -> GraphTimeRange.CURRENT_WEEK
                    MyTracksGranularity.Type.THIS_MONTH -> GraphTimeRange.CURRENT_MONTH
                    MyTracksGranularity.Type.LAST_30_DAYS -> GraphTimeRange.THIRTY_DAYS
                    else -> GraphTimeRange.CURRENT_WEEK
                }
                diaryNavigator.openActivityScreen(requireContext(), timeRange)
                return
            }
            is AscentWidgetInfo -> {
                chartContent = ChartContent.ASCENT
                chartGranularity = ChartGranularity.WEEKLY
            }
            is CalendarWidgetInfo -> {
                val timeRange = when (widgetInfo.granularityType) {
                    MyTracksGranularity.Type.THIS_WEEK -> GraphTimeRange.CURRENT_WEEK
                    MyTracksGranularity.Type.THIS_MONTH -> GraphTimeRange.CURRENT_MONTH
                    MyTracksGranularity.Type.LAST_30_DAYS -> GraphTimeRange.THIRTY_DAYS
                    else -> GraphTimeRange.CURRENT_WEEK
                }
                diaryNavigator.openCalendarScreen(requireContext(), timeRange)
                return
            }
            is CalorieWidgetInfo -> {
                chartContent = ChartContent.CALORIES
                chartGranularity = ChartGranularity.DAILY
            }
            is DailyHeartRateWidgetInfo -> {
                chartContent = ChartContent.HEART_RATE
                chartGranularity = ChartGranularity.DAILY
            }
            is CommuteWidgetInfo -> {
                chartContent = ChartContent.COMMUTE
                chartGranularity = ChartGranularity.MONTHLY
            }
            is DurationWidgetInfo -> {
                chartContent = ChartContent.DURATION
                chartGranularity = when (widgetInfo.period.mapGranularity()) {
                    DiaryCalendarListContainer.Granularity.WEEK -> ChartGranularity.WEEKLY
                    DiaryCalendarListContainer.Granularity.MONTH -> ChartGranularity.MONTHLY
                    DiaryCalendarListContainer.Granularity.LAST_30_DAYS -> ChartGranularity.THIRTY_DAYS
                    DiaryCalendarListContainer.Granularity.YEAR -> ChartGranularity.YEARLY
                    null -> ChartGranularity.SEVEN_DAYS
                }
            }
            is HrvWidgetInfo -> {
                chartContent = ChartContent.HRV
                chartGranularity = ChartGranularity.WEEKLY
            }
            is MapWidgetInfo -> {
                val timeRange = when (widgetInfo.granularityType) {
                    MyTracksGranularity.Type.THIS_WEEK -> GraphTimeRange.CURRENT_WEEK
                    MyTracksGranularity.Type.THIS_MONTH -> GraphTimeRange.CURRENT_MONTH
                    MyTracksGranularity.Type.LAST_30_DAYS -> GraphTimeRange.THIRTY_DAYS
                    else -> GraphTimeRange.CURRENT_WEEK
                }
                diaryNavigator.openMapScreen(requireContext(), timeRange)
                return
            }
            is MenstrualPeriodWidgetInfo -> {
                logMenstrualCycle()
                return
            }
            is Last7DaysHeartRateWidgetInfo -> {
                chartContent = when (widgetInfo.widgetType) {
                    WidgetType.MINIMUM_HEART_RATE -> ChartContent.MINIMUM_HEART_RATE
                    WidgetType.MINIMUM_SLEEP_HEART_RATE -> ChartContent.SLEEPING_MINIMUM_HEART_RATE
                    WidgetType.RESTING_HEART_RATE -> ChartContent.RESTING_HEART_RATE
                    else -> throw IllegalArgumentException("${widgetInfo.widgetType} not supported for Last7DaysHeartRateWidgetInfo")
                }
                chartGranularity = ChartGranularity.SEVEN_DAYS
            }
            is ProgressWidgetInfo -> {
                diaryNavigator.openProgressScreen(requireContext())
                return
            }
            is RecoveryWidgetInfo -> {
                recoveryNavigator.launchRecoveryV2(requireContext())
                return
            }
            is ResourceWidgetInfo -> {
                chartContent = ChartContent.RESOURCES
                chartGranularity = ChartGranularity.DAILY
            }
            is SleepWidgetInfo -> {
                chartContent = ChartContent.SLEEP
                chartGranularity = ChartGranularity.DAILY
            }

            is StepWidgetInfo -> {
                chartContent = ChartContent.STEPS
                chartGranularity = ChartGranularity.DAILY
            }
            is TSSWidgetInfo -> {
                chartContent = ChartContent.TSS
                chartGranularity = ChartGranularity.WEEKLY
            }
            is Vo2MaxWidgetInfo -> {
                chartContent = ChartContent.VO2MAX
                chartGranularity = ChartGranularity.SIX_WEEKS
            }
        }

        chartNavigator.openChartScreen(
            context = requireContext(),
            chartContent = chartContent,
            chartStyle = ChartStyle.SINGLE,
            chartGranularity = chartGranularity,
            source = AnalyticsPropertyValue.WidgetDetailPageExposureSourceProperty.DASHBOARD,
        )
    }

    companion object {
        private const val LAUNCH_HEADPHONE_SERVICE_INTERVAL = 30 * 1000L // unit: ms
    }
}
