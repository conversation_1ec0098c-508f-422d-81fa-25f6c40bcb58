package com.stt.android.home.dashboardv2.ui

import androidx.annotation.StringRes
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.airbnb.lottie.compose.LottieAnimation
import com.airbnb.lottie.compose.LottieCompositionSpec
import com.airbnb.lottie.compose.LottieConstants
import com.airbnb.lottie.compose.rememberLottieComposition
import com.stt.android.appversion.AppVersionViewModel
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.lightGrey
import com.stt.android.compose.theme.spacing
import com.stt.android.home.dashboardv2.HeadphoneViewData
import com.stt.android.home.dashboardv2.ToolbarViewModel

@Composable
internal fun FlavorDashboardToolbar(
    toolbarViewModel: ToolbarViewModel,
    appVersionViewModel: AppVersionViewModel,
    viewEvent: (DashboardScreenViewEvent) -> Unit,
    onUserClick: (String) -> Unit,
    modifier: Modifier = Modifier,
) {
    val viewData = toolbarViewModel.viewState.collectAsState()
    val watchState = toolbarViewModel.watchState.collectAsState()
    val showHeadsetEntrance = toolbarViewModel.showHeadsetEntrance.collectAsState(initial = false)
    val headphoneState = toolbarViewModel.headphoneState.collectAsState()
    val newVersion by appVersionViewModel.newVersionLive.observeAsState()
    Row(
        modifier = modifier,
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.small),
    ) {
        Spacer(modifier = Modifier.width(MaterialTheme.spacing.small))

        ToolbarWatch(
            stateData = watchState.value,
            onClick = { viewEvent(DashboardScreenViewEvent.OpenWatchDevice) },
        )

        if (showHeadsetEntrance.value) {
            ToolbarHeadphone(
                headphoneState.value,
                onClick = { viewEvent(DashboardScreenViewEvent.OpenHeadsetDevice) },
            )
        }

        DashboardToolbar(
            viewData = viewData.value,
            showRedDot = newVersion != null,
            viewEvent = viewEvent,
            onUserClick = onUserClick,
            modifier = Modifier
                .weight(1f),
        )
    }
}

@Composable
private fun ToolbarHeadphone(
    headphoneViewData: HeadphoneViewData,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    when (headphoneViewData) {
        is HeadphoneViewData.HeadphoneConnecting ->
            WatchWithAnimation(
                animationFileNameResId = headphoneViewData.animationFileNameResId,
                onClick = onClick,
                modifier = modifier
            )

        is HeadphoneViewData.HeadphoneConnected ->
            BarIcon(
                icon = headphoneViewData.iconResId,
                onClick = onClick,
                modifier = modifier
            )

        is HeadphoneViewData.HeadphoneDisConnected ->
            BarIcon(
                icon = headphoneViewData.iconResId,
                onClick = onClick,
                modifier = modifier
            )

        is HeadphoneViewData.Default ->
            BarIcon(
                icon = headphoneViewData.iconResId,
                onClick = onClick,
                modifier = modifier
            )

        is HeadphoneViewData.BluetoothDisabled ->
            BarIcon(
                icon = headphoneViewData.iconResId,
                onClick = onClick,
                modifier = modifier
            )
    }
}

@Composable
private fun ToolbarWatch(
    stateData: ToolbarViewModel.WatchStateViewData,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    when (stateData) {
        is ToolbarViewModel.WatchStateViewData.WatchReconnecting ->
            WatchWithAnimation(
                animationFileNameResId = stateData.animationFileNameResId,
                onClick = onClick,
                modifier = modifier
            )

        is ToolbarViewModel.WatchStateViewData.WatchSyncing ->
            WatchWithAnimation(
                animationFileNameResId = stateData.animationFileNameResId,
                onClick = onClick,
                modifier = modifier
            )

        is ToolbarViewModel.WatchStateViewData.BluetoothDisabled ->
            BarIcon(
                icon = stateData.iconResId,
                onClick = onClick,
                modifier = modifier
            )

        is ToolbarViewModel.WatchStateViewData.WatchNotPaired ->
            BarIcon(
                icon = stateData.iconResId,
                onClick = onClick,
                modifier = modifier
            )

        is ToolbarViewModel.WatchStateViewData.WatchNotConnected ->
            BarIcon(
                icon = stateData.iconResId,
                onClick = onClick,
                modifier = modifier
            )

        is ToolbarViewModel.WatchStateViewData.WatchBusy ->
            BarIcon(
                icon = stateData.iconResId,
                onClick = onClick,
                modifier = modifier
            )

        is ToolbarViewModel.WatchStateViewData.WatchFail ->
            BarIcon(
                icon = stateData.iconResId,
                onClick = onClick,
                modifier = modifier
            )

        is ToolbarViewModel.WatchStateViewData.WatchConnected ->
            BarIcon(
                icon = stateData.iconResId,
                onClick = onClick,
                modifier = modifier
            )

        is ToolbarViewModel.WatchStateViewData.UpdateAvailable ->
            BarIcon(
                icon = stateData.iconResId,
                onClick = onClick,
                modifier = modifier
            )
    }
}

@Composable
private fun WatchWithAnimation(
    @StringRes animationFileNameResId: Int,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    val lottieComposition = rememberLottieComposition(
        LottieCompositionSpec.Asset(stringResource(animationFileNameResId)),
        imageAssetsFolder = "images"
    )

    Box(
        modifier = modifier
            .size(MaterialTheme.iconSizes.large)
            .border(
                border = BorderStroke(1.dp, MaterialTheme.colorScheme.lightGrey),
                shape = CircleShape
            )
    ) {
        IconButton(
            onClick = onClick,
            modifier = Modifier
                .align(Alignment.Center)
        ) {
            LottieAnimation(
                composition = lottieComposition.value,
                iterations = LottieConstants.IterateForever,
                modifier = Modifier
                    .size(MaterialTheme.iconSizes.small)
            )
        }
    }
}
