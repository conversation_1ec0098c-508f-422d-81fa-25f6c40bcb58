package com.stt.android.home.dayviewv2.usecase

import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.data.toEpochMilli
import com.stt.android.domain.trenddata.FetchTrendDataUseCase
import com.stt.android.domain.trenddata.TrendData
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.map
import java.time.LocalDate
import java.time.ZoneId
import javax.inject.Inject

class FetchDailyTrendDataUseCase @Inject constructor(
    private val fetchTrendDataUseCase: FetchTrendDataUseCase,
    private val dispatchers: CoroutinesDispatchers,
) {
    operator fun invoke(date: LocalDate): Flow<List<TrendData>> {
        val startMillis = date.atStartOfDay(ZoneId.systemDefault()).toEpochMilli()
        val endMillis = date.plusDays(1).atStartOfDay(ZoneId.systemDefault()).toEpochMilli()
        return fetchTrendDataUseCase.fetchTrendDataForDateRange(startMillis, endMillis, false)
            .map { data ->
                data
                    .filter { it.timestamp in startMillis until endMillis }
                    .sortedBy { it.timestamp }
            }
            .flowOn(dispatchers.io)
    }
}
