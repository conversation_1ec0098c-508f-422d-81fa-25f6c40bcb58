package com.stt.android.home.dayviewv2.usecase

import com.stt.android.R
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.UserSettingsController
import com.stt.android.home.dayviewv2.DayViewExpandable
import com.stt.android.home.dayviewv2.SpO2ViewData
import com.stt.android.home.dayviewv2.TrendChartAxisRange
import com.stt.android.home.dayviewv2.TrendChartData
import com.stt.android.home.dayviewv2.TrendChartSeries
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.flowOn
import java.time.LocalDate
import javax.inject.Inject
import kotlin.math.roundToInt

class FetchDailySpO2UseCase @Inject constructor(
    private val fetchDailyTrendDataUseCase: FetchDailyTrendDataUseCase,
    private val fetchExpandedStateUseCase: FetchExpandedStateUseCase,
    private val userSettingsController: UserSettingsController,
    private val dispatchers: CoroutinesDispatchers,
) {
    operator fun invoke(date: LocalDate): Flow<SpO2ViewData?> = combine(
        fetchDailyTrendDataUseCase(date),
        fetchExpandedStateUseCase(DayViewExpandable.SpO2),
    ) { trendData, expanded ->
        val samples = trendData.filter { (it.spo2 ?: 0f) > 0f }

        if (samples.isEmpty()) return@combine null

        val series = TrendChartSeries(
            colorRes = R.color.sleep_chart_spo2,
            x = samples.map { it.timeISO8601.toEpochSecond() },
            y = samples.map { it.spo2!! * 100.0 },
        )

        val (minX, maxX) = date.epochSecondRange
        val (minY, maxY) = adjustPercentRange(
            series.y.min().roundToInt(),
            series.y.max().roundToInt(),
        )
        val chartData = TrendChartData(
            series = listOf(series),
            axisRange = TrendChartAxisRange(
                minX = minX,
                maxX = maxX,
                minY = minY.toDouble(),
                maxY = maxY.toDouble(),
            ),
        )

        val measurementUnit = userSettingsController.settings.measurementUnit
        val averageAltitude = samples.mapNotNull { it.altitude }
            .average()
            .takeIf { !it.isNaN() }
            ?.let { measurementUnit.toAltitudeUnit(it) }
            ?.roundToInt()

        SpO2ViewData(
            average = series.y.average().roundToInt(),
            averageAltitude = averageAltitude,
            altitudeUnitRes = measurementUnit.altitudeUnit,
            expanded = expanded,
            chartData = chartData,
        )
    }.flowOn(dispatchers.io)
}
