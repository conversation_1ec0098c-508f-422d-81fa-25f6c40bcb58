package com.stt.android.home.dashboardv2

import android.content.SharedPreferences
import androidx.lifecycle.viewModelScope
import com.stt.android.analytics.EmarsysAnalytics
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.CurrentUserController
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.firstpairing.FirstPairingInfoUseCase
import com.stt.android.domain.terms.NeedAcceptTermsUseCase
import com.stt.android.home.dashboardv2.repository.DashboardConfigRepository
import com.stt.android.models.MapSelectionModel
import com.stt.android.refreshable.Refreshables
import com.stt.android.watch.forcedupdate.ShouldShowOnboardingUseCase
import com.stt.android.workouts.RecordWorkoutModel
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoDeviceType
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.rx2.await
import timber.log.Timber

internal abstract class BaseSuuntoDashboardViewModel(
    refreshables: Refreshables,
    dashboardConfigRepository: DashboardConfigRepository,
    currentUserController: CurrentUserController,
    emarsysAnalytics: EmarsysAnalytics,
    emarsysAppName: String,
    private val firstPairingInfoUseCase: FirstPairingInfoUseCase,
    private val shouldShowOnboardingUseCase: ShouldShowOnboardingUseCase,
    private val coroutinesDispatchers: CoroutinesDispatchers,
    recordWorkoutModel: RecordWorkoutModel,
    mapSelectionModel: MapSelectionModel,
    exploreMapPreferences: SharedPreferences,
    needAcceptTermsUseCase: NeedAcceptTermsUseCase,
) : BaseDashboardViewModel(
    refreshables = refreshables,
    dashboardConfigRepository = dashboardConfigRepository,
    currentUserController = currentUserController,
    mapSelectionModel = mapSelectionModel,
    exploreMapPreferences = exploreMapPreferences,
    needAcceptTermsUseCase = needAcceptTermsUseCase,
    recordWorkoutModel = recordWorkoutModel,
    emarsysAnalytics = emarsysAnalytics,
    emarsysAppName = emarsysAppName,
    showStartWorkoutFab = false,
) {
    private val _openDeviceOnboardingFlow: MutableSharedFlow<SuuntoDeviceType> = MutableSharedFlow()
    val openDeviceOnboardingFlow: SharedFlow<SuuntoDeviceType> =
        _openDeviceOnboardingFlow.asSharedFlow()

    private var checkOnboardingJob: Job? = null

    fun checkIfDeviceOnboardingIsNeeded() {
        checkOnboardingJob?.cancel()
        checkOnboardingJob = viewModelScope.launch(coroutinesDispatchers.io) {
            runSuspendCatching {
                val notOnboardedDevice = shouldShowOnboardingUseCase.checkNotOnboardedDevice()
                if (notOnboardedDevice != null) {
                    firstPairingInfoUseCase.markOnboardingShownAtLeastOnce(notOnboardedDevice)
                        .await()
                    _openDeviceOnboardingFlow.emit(notOnboardedDevice)
                }
            }.onFailure { e ->
                Timber.w(e, "Failed to check if device onboarding is needed")
            }
        }
    }
}
