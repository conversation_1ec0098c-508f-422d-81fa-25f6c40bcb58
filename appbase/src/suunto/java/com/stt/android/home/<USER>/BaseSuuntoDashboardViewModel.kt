package com.stt.android.home.dashboard

import android.annotation.SuppressLint
import android.app.Application
import android.content.SharedPreferences
import androidx.lifecycle.LiveData
import androidx.work.WorkManager
import com.stt.android.analytics.EmarsysAnalytics
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.ReactionModel
import com.stt.android.controllers.SummaryExtensionDataModel
import com.stt.android.controllers.WorkoutHeaderController
import com.stt.android.domain.di.IoThread
import com.stt.android.domain.di.MainThread
import com.stt.android.domain.firstpairing.FirstPairingInfoUseCase
import com.stt.android.domain.marketing.MarketingBannerUseCase
import com.stt.android.domain.terms.NeedAcceptTermsUseCase
import com.stt.android.domain.user.subscription.IsSubscribedToPremiumUseCase
import com.stt.android.eventtracking.EventTracker
import com.stt.android.home.dashboard.card.ExploreCardLoader
import com.stt.android.home.dashboard.card.SportieCardLoader
import com.stt.android.home.dashboard.card.WelcomeCardLoader
import com.stt.android.home.dashboard.card.WorkoutCardLoader
import com.stt.android.refreshable.Refreshables
import com.stt.android.ui.utils.SingleLiveEvent
import com.stt.android.watch.forcedupdate.ShouldShowOnboardingUseCase
import com.stt.android.workouts.SyncSingleWorkoutUseCase
import com.stt.android.workouts.sharepreview.customshare.WorkoutShareHelper
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoDeviceType
import io.reactivex.Scheduler
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import kotlinx.coroutines.rx2.await
import timber.log.Timber

abstract class BaseSuuntoDashboardViewModel(
    dashboardAnalytics: DashboardAnalytics,
    workoutHeaderController: WorkoutHeaderController,
    currentUserController: CurrentUserController,
    sharedPreferences: SharedPreferences,
    reactionModel: ReactionModel,
    needAcceptTermsUseCase: NeedAcceptTermsUseCase,
    appContext: Application,
    workManager: WorkManager,
    syncSingleWorkoutUseCase: SyncSingleWorkoutUseCase,
    refreshables: Refreshables,
    emarsysAnalytics: EmarsysAnalytics,
    isSubscribedToPremiumUseCase: IsSubscribedToPremiumUseCase,
    workoutCardLoader: WorkoutCardLoader,
    exploreCardLoader: ExploreCardLoader,
    sportieCardLoader: SportieCardLoader,
    welcomeCardLoader: WelcomeCardLoader,
    summaryExtensionDataModel: SummaryExtensionDataModel,
    @IoThread ioThread: Scheduler,
    @MainThread mainThread: Scheduler,
    private val firstPairingInfoUseCase: FirstPairingInfoUseCase,
    private val shouldShowOnboardingUseCase: ShouldShowOnboardingUseCase,
    workoutShareHelper: WorkoutShareHelper,
    marketingBannerUseCase: MarketingBannerUseCase,
    eventTracker: EventTracker,
    coroutinesDispatchers: CoroutinesDispatchers,
) : BaseDashboardViewModel(
    dashboardAnalytics,
    workoutHeaderController,
    currentUserController,
    sharedPreferences,
    reactionModel,
    needAcceptTermsUseCase,
    appContext,
    workManager,
    syncSingleWorkoutUseCase,
    refreshables,
    emarsysAnalytics,
    isSubscribedToPremiumUseCase,
    workoutShareHelper,
    workoutCardLoader,
    exploreCardLoader,
    sportieCardLoader,
    welcomeCardLoader,
    summaryExtensionDataModel,
    marketingBannerUseCase,
    eventTracker,
    coroutinesDispatchers,
    ioThread,
    mainThread,
) {
    private var checkOnboardingJob: Job? = null

    private val _openOnboardingEvent = SingleLiveEvent<SuuntoDeviceType>()
    val openOnboardingEvent: LiveData<SuuntoDeviceType>
        get() = _openOnboardingEvent

    @SuppressLint("NullSafeMutableLiveData")
    fun checkOnboarding() {
        checkOnboardingJob?.cancel()
        checkOnboardingJob = launch(io) {
            try {
                val notOnboardedDevice = shouldShowOnboardingUseCase.checkNotOnboardedDevice()
                if (notOnboardedDevice != null) {
                    firstPairingInfoUseCase.markOnboardingShownAtLeastOnce(notOnboardedDevice)
                        .await()
                    _openOnboardingEvent.postValue(notOnboardedDevice)
                }
            } catch (e: Exception) {
                Timber.d(e, "Failed to check show onboarding: ${e.message}")
            }
        }
    }
}
