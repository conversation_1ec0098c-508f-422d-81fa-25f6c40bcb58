package com.stt.android.home.dayviewv2.widget

import android.graphics.Paint
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.unit.dp
import com.patrykandpatrick.vico.compose.cartesian.CartesianChartHost
import com.patrykandpatrick.vico.compose.cartesian.layer.continuous
import com.patrykandpatrick.vico.compose.cartesian.layer.rememberLineCartesianLayer
import com.patrykandpatrick.vico.compose.cartesian.rememberCartesianChart
import com.patrykandpatrick.vico.compose.cartesian.rememberVicoZoomState
import com.patrykandpatrick.vico.compose.common.fill
import com.patrykandpatrick.vico.core.cartesian.Zoom
import com.patrykandpatrick.vico.core.cartesian.axis.Axis
import com.patrykandpatrick.vico.core.cartesian.data.CartesianChartModelProducer
import com.patrykandpatrick.vico.core.cartesian.data.lineSeries
import com.patrykandpatrick.vico.core.cartesian.layer.LineCartesianLayer
import com.stt.android.home.dayviewv2.TrendChartData

@Composable
fun TrendLineChart(
    chartData: TrendChartData,
    modifier: Modifier = Modifier,
) {
    val modelProducer = remember { CartesianChartModelProducer() }
    LaunchedEffect(Unit) {
        modelProducer.runTransaction {
            lineSeries { chartData.series.forEach { series(it.x, it.y) } }
        }
    }

    CartesianChartHost(
        modifier = modifier,
        chart = rememberCartesianChart(
            *chartData.series.map {
                rememberLineCartesianLayer(
                    lineProvider = LineCartesianLayer.LineProvider.series(
                        rememberRoundedLine(
                            fill = LineCartesianLayer.LineFill.single(
                                fill(color = colorResource(it.colorRes))
                            ),
                            stroke = LineCartesianLayer.LineStroke.continuous(cap = StrokeCap.Round),
                            areaFill = LineCartesianLayer.AreaFill.single(
                                fill(color = colorResource(it.colorRes).copy(alpha = 0.1f))
                            ),
                        ),
                    ),
                    rangeProvider = rememberFixedRangeProvider(chartData.axisRange),
                    verticalAxisPosition = Axis.Position.Vertical.End,
                    pointSpacing = 16.dp,
                )
            }.toTypedArray(),
            startAxis = null,
            endAxis = rememberDailyEndAxis(),
            bottomAxis = rememberDailyBottomAxis(),
            getXStep = { 100.0 * 6.0 }, // 6 hours
        ),
        modelProducer = modelProducer,
        zoomState = rememberVicoZoomState(
            zoomEnabled = false,
            initialZoom = Zoom.Content,
        ),
    )
}

@Composable
private fun rememberRoundedLine(
    fill: LineCartesianLayer.LineFill,
    stroke: LineCartesianLayer.LineStroke,
    areaFill: LineCartesianLayer.AreaFill,
): LineCartesianLayer.Line = remember(fill, stroke, areaFill) {
    RoundedLine(fill, stroke, areaFill)
}

private class RoundedLine(
    fill: LineCartesianLayer.LineFill,
    stroke: LineCartesianLayer.LineStroke,
    areaFill: LineCartesianLayer.AreaFill,
) : LineCartesianLayer.Line(fill, stroke, areaFill) {
    init {
        linePaint.strokeJoin = Paint.Join.ROUND
    }
}
