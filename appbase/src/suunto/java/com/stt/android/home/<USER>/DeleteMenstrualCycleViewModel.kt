package com.stt.android.home.dayviewv2

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.work.WorkManager
import com.stt.android.controllers.UserSettingsController
import com.stt.android.domain.user.MenstrualCycleRegularity
import com.stt.android.menstrualcycle.CycleAction
import com.stt.android.menstrualcycle.LoggedFrom
import com.stt.android.menstrualcycle.MenstrualCycleAnalyticsUtils
import com.stt.android.menstrualcycle.MenstrualCycleRemoteSyncJob
import com.stt.android.menstrualcycle.domain.DeleteDateFromMenstrualCycleUseCase
import com.stt.android.menstrualcycle.domain.FetchMenstrualCycleRegularityUseCase
import com.stt.android.menstrualcycle.domain.toCalc
import com.stt.android.menstrualcycle.domain.toDomain
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.launch
import java.time.LocalDate
import javax.inject.Inject

@HiltViewModel
class DeleteMenstrualCycleViewModel @Inject constructor(
    private val deleteDateFromMenstrualCycleUseCase: DeleteDateFromMenstrualCycleUseCase,
    private val fetchMenstrualCycleRegularityUseCase: FetchMenstrualCycleRegularityUseCase,
    private val menstrualCycleAnalyticsUtils: MenstrualCycleAnalyticsUtils,
    private val userSettingsController: UserSettingsController,
    private val workManager: dagger.Lazy<WorkManager>,
) : ViewModel() {

    private val _menstrualCycleRegularityChanged = MutableSharedFlow<MenstrualCycleRegularity>()
    val menstrualCycleRegularityChanged: SharedFlow<MenstrualCycleRegularity>
        get() = _menstrualCycleRegularityChanged.asSharedFlow()

    fun deletePeriodDay(date: LocalDate) = viewModelScope.launch {
        val deletedDateResult = deleteDateFromMenstrualCycleUseCase.run(
            DeleteDateFromMenstrualCycleUseCase.Params(date)
        )

        val menstrualCycleSettings = userSettingsController.settings.getMenstrualCycleSetting()
        val regularityInSettings = menstrualCycleSettings?.cycleRegularity
        val newRegularity = fetchMenstrualCycleRegularityUseCase(
            FetchMenstrualCycleRegularityUseCase.Params(
                menstrualCycleSettings?.cycleLength,
                regularityInSettings?.toCalc()
            )
        )?.toDomain()
        if (newRegularity != null && regularityInSettings != newRegularity) {
            _menstrualCycleRegularityChanged.emit(newRegularity)
        }

        deletedDateResult?.let {
            menstrualCycleAnalyticsUtils.trackDeleted(
                LoggedFrom.DAY_VIEW,
                if (it.any { menstrualCycle -> menstrualCycle.includedDates.isNotEmpty() }) CycleAction.DELETE_DAY else CycleAction.DELETE_CYCLE,
                newRegularity ?: MenstrualCycleRegularity.NOT_SURE
            )
        }

        MenstrualCycleRemoteSyncJob.schedule(workManager.get())
    }
}
