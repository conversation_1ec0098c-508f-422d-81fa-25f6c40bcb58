package com.stt.android.home.dashboard.bottomsheet

import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.AnalyticsEventProperty
import com.stt.android.analytics.AnalyticsProperties
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.analytics.AnalyticsUserProperty
import com.stt.android.analytics.EmarsysAnalytics
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.home.dashboard.bottomsheet.WeeklyGoalBottomSheetFragment.Companion.SEEKBAR_DURATION_IN_HOURS_MIN
import com.stt.android.presenters.MVPPresenter
import com.stt.android.watch.MissingCurrentWatchException
import com.stt.android.watch.SuuntoWatchModel
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoDeviceType
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import timber.log.Timber
import java.util.concurrent.TimeUnit
import javax.inject.Inject

@Suppress("DEPRECATION")
@Deprecated("get rid of presenters")
class WeeklyGoalBottomSheetPresenter @Inject constructor(
    private val suuntoWatchModel: SuuntoWatchModel,
    private val emarsysAnalytics: EmarsysAnalytics,
    private val amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker,
    coroutinesDispatchers: CoroutinesDispatchers,
) : MVPPresenter<WeeklyGoalBottomSheetView>() {
    private val coroutineScope = CoroutineScope(coroutinesDispatchers.main + SupervisorJob())

    override fun onViewTaken() {
        super.onViewTaken()
        getWatchVersion()
    }

    private fun getWatchVersion() {
        coroutineScope.launch {
            runSuspendCatching {
                val hasAdaptiveTrainingGuidance = suuntoWatchModel.currentWatch()
                    .suuntoBtDevice
                    .deviceType
                    .let(SuuntoDeviceType::hasAdaptiveTrainingGuidance)
                getView()?.setTrainingPlanGroupVisible(hasAdaptiveTrainingGuidance)
            }.onFailure { e ->
                if (e is MissingCurrentWatchException) {
                    Timber.i("Missing current watch")
                } else {
                    Timber.w(e, "Error in loading current watch")
                }
            }
        }
    }

    internal fun trainingGuidanceSwitchChange(isChecked: Boolean) {
        val view = getView()
        view?.apply {
            notifyListenerCoachEnabledChanged(isChecked)
            setTrainingDurationGroupVisible(!isChecked)
            setTrainingPlanTextViewVisible(isChecked)
            setCoachEnabledToSharedPreferences(isChecked)
        }
        setCoachEnabledToWatch(isChecked)
        sendCoachToggledEvent(isChecked)
        trackCoachEnabledUserProperty(isChecked)
    }

    internal fun seekBarStopTrackingTouch(progress: Int) {
        val weeklyTargetInSeconds = progressToDuration(progress)
        sendWeeklyTargetSetEvent(weeklyTargetInSeconds)

        val view = getView()
        view?.apply {
            setWeeklyTargetToSharedPreferences(weeklyTargetInSeconds)
            notifyListenerWeeklyTargetChanged(weeklyTargetInSeconds)
        }

        setWeeklyTargetToWatch(weeklyTargetInSeconds)
    }

    internal fun seekBarProgressChanged(value: Int) {
        val view = getView()
        view?.updateDurationTextView(progressToDuration(value))
    }

    private fun setWeeklyTargetToWatch(duration: Int) {
        coroutineScope.launch {
            runSuspendCatching {
                suuntoWatchModel.setWeeklyTargetDuration(duration.toFloat())
                Timber.d("Successfully set weekly target to the watch")
                setWeeklyTargetSyncNeeded(false)
            }.onFailure { e ->
                Timber.w(e, "Unable to set weekly target to the watch")
                setWeeklyTargetSyncNeeded(true)
            }
        }
    }

    private fun setWeeklyTargetSyncNeeded(syncNeeded: Boolean) {
        val view = getView()
        view?.setWeeklyTargetSyncNeededToSharedPreferences(syncNeeded)
    }

    private fun setCoachEnabledSyncNeeded(syncNeeded: Boolean) {
        val view = getView()
        view?.setCoachEnabledSyncNeededToSharedPreferences(syncNeeded)
    }

    private fun setCoachEnabledToWatch(coachEnabled: Boolean) {
        coroutineScope.launch {
            runSuspendCatching {
                suuntoWatchModel.setAdaptiveCoachEnabled(coachEnabled)
                Timber.d("Successfully set coach enabled to the watch")
                setCoachEnabledSyncNeeded(false)
            }.onFailure { e ->
                Timber.w(e, "Unable to set coach enabled to the watch")
                setCoachEnabledSyncNeeded(true)
            }
        }
    }

    /**
     * Convert seekbar progress to weekly target duration.
     * Seekbar values: 1h to 25h
     *
     * @param progress seekbar progress position
     * from: SEEKBAR_DURATION_IN_HOURS_MIN to: SEEKBAR_DURATION_IN_HOURS_MAX
     * @return weekly target duration in seconds
     */
    private fun progressToDuration(progress: Int): Int {
        val oneHour = 3600
        if (progress <= SEEKBAR_DURATION_IN_HOURS_MIN) {
            return SEEKBAR_DURATION_IN_HOURS_MIN * oneHour
        }
        return progress * oneHour
    }

    /**
     * Convert weekly target duration to seekbar progress.
     *
     * @param duration weekly target duration in seconds
     * @return seekbar progress position
     */
    internal fun durationToProgress(duration: Int): Int {
        val oneHour = 3600
        if ((duration / oneHour) <= SEEKBAR_DURATION_IN_HOURS_MIN) {
            return SEEKBAR_DURATION_IN_HOURS_MIN
        }
        return duration / oneHour
    }

    private fun sendCoachToggledEvent(isChecked: Boolean) {
        val trainingPlanProperties = AnalyticsProperties().put(
            AnalyticsEventProperty.SUUNTO_GOAL_NEW_SETTING,
            if (isChecked) AnalyticsPropertyValue.ON else AnalyticsPropertyValue.OFF
        )

        amplitudeAnalyticsTracker.trackEvent(AnalyticsEvent.SUUNTO_TRAINING_PLAN_TOGGLED, trainingPlanProperties)
    }

    private fun sendWeeklyTargetSetEvent(newWeeklyTarget: Int) {
        val properties = AnalyticsProperties().apply {
            put(AnalyticsEventProperty.SUUNTO_GOAL_TYPE, AnalyticsPropertyValue.GoalType.DURATION)
            put(AnalyticsEventProperty.SUUNTO_GOAL_OLD, getWeeklyTargetDuration())
            put(AnalyticsEventProperty.SUUNTO_GOAL_NEW, newWeeklyTarget)
        }

        amplitudeAnalyticsTracker.trackEvent(AnalyticsEvent.GOAL_SET_NEW, properties)
        emarsysAnalytics.trackEventWithProperties(AnalyticsEvent.GOAL_SET_NEW, properties.map)
    }

    private fun getWeeklyTargetDuration(): Int {
        val view = getView()
        return view?.getWeeklyTargetDurationFromSharedPrefs() ?: DEFAULT_WEEKLY_GOAL
    }

    private fun trackCoachEnabledUserProperty(isChecked: Boolean) {
        val userProperty = if (isChecked) AnalyticsPropertyValue.ON else AnalyticsPropertyValue.OFF
        amplitudeAnalyticsTracker.trackUserProperty(
            AnalyticsUserProperty
                .SUUNTO_TRAINING_PLAN_IN_USE,
            userProperty
        )
    }

    companion object {
        // Default weekly goal is 3 hours
        val DEFAULT_WEEKLY_GOAL: Int = TimeUnit.HOURS.toSeconds(3).toInt()
    }
}
