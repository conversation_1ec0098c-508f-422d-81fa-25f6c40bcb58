package com.stt.android.home.dayviewv2.usecase

import com.stt.android.R
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.domain.activitydata.dailyvalues.FetchDailyStepsUseCase
import com.stt.android.domain.activitydata.goals.FetchStepsGoalUseCase
import com.stt.android.home.dayviewv2.DayViewExpandable
import com.stt.android.home.dayviewv2.StepsViewData
import com.stt.android.home.dayviewv2.TrendChartAxisRange
import com.stt.android.home.dayviewv2.TrendChartData
import com.stt.android.home.dayviewv2.TrendChartSeries
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.flowOn
import java.time.LocalDate
import javax.inject.Inject

class FetchDailyStepsUseCase @Inject constructor(
    private val fetchDailyTrendDataUseCase: FetchDailyTrendDataUseCase,
    private val fetchDailyStepsUseCase: FetchDailyStepsUseCase,
    private val fetchStepsGoalUseCase: FetchStepsGoalUseCase,
    private val fetchExpandedStateUseCase: FetchExpandedStateUseCase,
    private val dispatchers: CoroutinesDispatchers,
) {
    operator fun invoke(date: LocalDate): Flow<StepsViewData?> = combine(
        fetchDailyTrendDataUseCase(date),
        fetchDailyStepsUseCase.fetchSteps(),
        fetchStepsGoalUseCase.fetchStepsGoal(),
        fetchExpandedStateUseCase(DayViewExpandable.Steps),
    ) { trendData, dailySteps, stepsGoal, expanded ->
        val samples = trendData.filter { it.steps > 0 }

        val steps = if (date != LocalDate.now()) {
            samples.sumOf { it.steps }
        } else dailySteps

        if (steps <= 0) return@combine null

        val chartData = if (samples.isNotEmpty()) {
            val series = TrendChartSeries(
                colorRes = R.color.activity_data_steps,
                x = samples.map { it.timeISO8601.toEpochSecond() },
                y = samples.map { it.steps.toDouble() },
            )

            val (minX, maxX) = date.epochSecondRange
            TrendChartData(
                series = listOf(series),
                axisRange = TrendChartAxisRange(
                    minX = minX,
                    maxX = maxX,
                    minY = 0.0,
                    maxY = series.y.max().toInt().ceilY(100).toDouble(),
                ),
            )
        } else null

        StepsViewData(
            steps = steps,
            goal = stepsGoal,
            expanded = expanded,
            chartData = chartData,
        )
    }.flowOn(dispatchers.io)
}
