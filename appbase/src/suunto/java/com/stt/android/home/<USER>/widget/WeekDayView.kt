package com.stt.android.home.dayviewv2.widget

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.indication
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.ripple
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.stt.android.compose.theme.cloudyGrey
import com.stt.android.compose.theme.material3.body
import com.stt.android.compose.theme.material3.bodyBold
import com.stt.android.compose.theme.spacing
import com.stt.android.home.dayviewv2.WeekDayState
import java.time.LocalDate

@Composable
fun WeekDayView(
    weekStartDate: LocalDate,
    weekDayLabels: List<String>,
    weekDayStates: List<WeekDayState>,
    onDateClick: (LocalDate) -> Unit,
    modifier: Modifier = Modifier,
) {
    Row(modifier = modifier) {
        for (i in 0..<7) {
            val date = weekStartDate.plusDays(i.toLong())
            WeekDayItem(
                modifier = Modifier,
                date = date,
                label = weekDayLabels[i],
                state = weekDayStates[i],
                onClick = { onDateClick(date) },
            )
        }
    }
}

@Composable
private fun RowScope.WeekDayItem(
    date: LocalDate,
    label: String,
    state: WeekDayState,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    val interactionSource = remember { MutableInteractionSource() }
    Column(
        modifier = modifier
            .clickable(
                onClick = onClick,
                interactionSource = interactionSource,
                indication = null,
                enabled = state == WeekDayState.Normal,
            )
            .weight(1f)
            .padding(vertical = MaterialTheme.spacing.small),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.small),
    ) {
        Text(
            text = label,
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurface,
        )
        val selected = state == WeekDayState.Selected
        Text(
            modifier = Modifier
                .clip(CircleShape)
                .indication(
                    interactionSource = interactionSource,
                    indication = ripple(),
                )
                .background(if (selected) MaterialTheme.colorScheme.primary else Color.Transparent)
                .widthIn(min = 36.dp)
                .padding(
                    horizontal = MaterialTheme.spacing.small,
                    vertical = MaterialTheme.spacing.xsmall,
                ),
            text = date.dayOfMonth.toString(),
            style = with(MaterialTheme.typography) { if (selected) bodyBold else body },
            color = with(MaterialTheme.colorScheme) {
                when (state) {
                    WeekDayState.Selected -> onPrimary
                    WeekDayState.Normal -> onSurface
                    WeekDayState.Disabled -> cloudyGrey
                }
            },
            textAlign = TextAlign.Center,
        )
    }
}
