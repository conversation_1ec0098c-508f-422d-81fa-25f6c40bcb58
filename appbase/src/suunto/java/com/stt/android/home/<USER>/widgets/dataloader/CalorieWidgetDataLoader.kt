package com.stt.android.home.dashboardv2.widgets.dataloader

import android.content.Context
import com.stt.android.R
import com.stt.android.domain.activitydata.dailyvalues.ActivityDataDailyRepository
import com.stt.android.domain.activitydata.goals.ActivityDataGoalRepository
import com.stt.android.domain.trenddata.TrendDataRepository
import com.stt.android.home.dashboardv2.ui.widgets.common.generateTargetSubtitle
import com.stt.android.home.dashboardv2.ui.widgets.common.generateWidgetTitle
import com.stt.android.home.dashboardv2.widgets.CalorieWidgetInfo
import com.stt.android.utils.toEpochMilli
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEmpty
import java.time.LocalDate
import javax.inject.Inject
import kotlin.math.roundToInt
import com.stt.android.core.R as CR

internal class CalorieWidgetDataLoader @Inject constructor(
    private val context: Context,
    private val activityDataDailyRepository: ActivityDataDailyRepository,
    private val activityDataGoalRepository: ActivityDataGoalRepository,
    private val trendDataRepository: TrendDataRepository,
) : WidgetDataLoader<CalorieWidgetInfo>() {
    override suspend fun load(param: Param): WidgetData<CalorieWidgetInfo> = run {
        val todayMillis = LocalDate.now().atStartOfDay().toEpochMilli()
        WidgetData(
            widgetType = param.type,
            editMode = param.editMode,
            widgetInfo = combine(
                activityDataGoalRepository.fetchEnergyGoal()
                    .map { it.inKcal.roundToInt() },
                activityDataDailyRepository.fetchEnergy()
                    .map { it.inKcal.roundToInt() },
                trendDataRepository.fetchTrendDataForDateRange(
                    todayMillis,
                    todayMillis,
                    true,
                ).onEmpty { emit(emptyList()) },
            ) { goal, dailyEnergy, todayTrend ->
                val energy = dailyEnergy.takeIf { it > 0 }
                    ?: todayTrend.lastOrNull()?.energy?.inKcal?.roundToInt()
                    ?: 0
                val progress = (energy.toFloat() / goal).coerceIn(0f, 1f)
                val kcal = context.getString(CR.string.kcal)
                val title = generateWidgetTitle(energy.toString(), kcal)
                val subtitle = generateTargetSubtitle(
                    context = context,
                    completed = energy,
                    target = goal,
                    postfixRes = R.string.widget_of_target,
                    unit = kcal,
                )
                CalorieWidgetInfo(
                    progress = progress,
                    title = title,
                    subtitle = subtitle,
                )
            },
        )
    }
}
