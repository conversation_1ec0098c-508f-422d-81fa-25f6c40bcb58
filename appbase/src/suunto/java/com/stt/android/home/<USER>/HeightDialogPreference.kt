package com.stt.android.home.settings

import android.content.Context
import android.util.AttributeSet
import com.stt.android.STTApplication
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.home.settings.userprofile.UserProfileUseCase
import com.stt.android.home.settings.userprofile.observeUserProfileCapabilities
import com.stt.android.watch.MissingCurrentWatchException
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

/**
 * This preference is Visible for Dilu.
 */
class HeightDialogPreference @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = android.R.attr.preferenceStyle,
    defStyleRes: Int = defStyleAttr
) : BaseHeightDialogPreference(context, attrs, defStyleAttr, defStyleRes) {

    @Inject
    lateinit var userProfileUseCase: UserProfileUseCase

    @Inject
    lateinit var dispatchers: CoroutinesDispatchers

    private val coroutineScope: CoroutineScope
    private var observerJob: Job? = null
    private var setWatchJob: Job? = null
    init {
        STTApplication.getComponent().inject(this)
        coroutineScope = CoroutineScope(SupervisorJob() + dispatchers.main)
    }

    override fun onAttached() {
        super.onAttached()
        observerJob =
            observeUserProfileCapabilities(userProfileUseCase, coroutineScope, dispatchers)
    }

    override fun onDetached() {
        observerJob?.cancel()
        setWatchJob?.cancel()
        super.onDetached()
    }

    override fun persistString(value: String): Boolean {
        if (!super.persistString(value)) {
            return false
        }
        setWatchJob = coroutineScope.launch {
            runSuspendCatching {
                userProfileUseCase.updateUserHeight(value.toInt())
            }.onSuccess {
                Timber.d("setUserHeight successful.")
            }.onFailure { e ->
                if (e !is MissingCurrentWatchException) {
                    Timber.w(e, "setUserHeight error.")
                }
            }
        }
        return true
    }
}
