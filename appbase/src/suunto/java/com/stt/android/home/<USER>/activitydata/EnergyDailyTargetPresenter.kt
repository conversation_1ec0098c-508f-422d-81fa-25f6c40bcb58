package com.stt.android.home.dashboard.activitydata

import com.stt.android.domain.activitydata.dailyvalues.FetchDailyEnergyUseCase
import com.stt.android.presenters.BasePresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import kotlinx.coroutines.rx2.asObservable
import timber.log.Timber
import javax.inject.Inject

class EnergyDailyTargetPresenter
@Inject constructor(
    private val fetchEnergyUseCase: FetchDailyEnergyUseCase
) : BasePresenter<EnergyDailyTargetView>() {

    fun getBmr() {
        disposable.add(
            fetchEnergyUseCase.fetchMetabolicEnergy()
                .asObservable()
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(
                    { value ->
                        view?.setMetabolicEnergyValue(value)
                    },
                    { Timber.d("Failed to fetch bmr") }
                )
        )
    }
}
