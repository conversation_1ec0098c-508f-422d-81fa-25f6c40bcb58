package com.stt.android.home.dashboardnew.weeklygoal

import android.content.SharedPreferences
import androidx.core.content.edit
import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.analytics.AnalyticsUserProperty
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.utils.STTConstants
import com.stt.android.watch.MissingCurrentWatchException
import com.stt.android.watch.SuuntoWatchModel
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoDeviceType
import com.suunto.connectivity.sync.SyncState
import com.suunto.connectivity.watch.WatchState
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import timber.log.Timber
import javax.inject.Inject
import kotlin.math.floor

class SyncWeeklyGoalWithWatchUseCase @Inject constructor(
    private val suuntoWatchModel: SuuntoWatchModel,
    private val sharedPreferences: SharedPreferences,
    private val amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker,
) {
    fun syncWeeklyGoalBetweenWatchAndSharedPrefs(coroutineScope: CoroutineScope) {
        suuntoWatchModel.watchStates()
            .distinctUntilChanged { old, new ->
                old.connectionState == new.connectionState &&
                    old.syncState.state == new.syncState.state
            }
            .filter { it.syncState.state == SyncState.NOT_SYNCING }
            .onEach { watchState ->
                if (!watchState.isPaired) {
                    setCoachEnabledToSharedPreferences(false)
                }
                if (watchState.connectionState == WatchState.ConnectionState.CONNECTED) {
                    watchState.deviceInfo?.apply {
                        if (SuuntoDeviceType.hasAdaptiveTrainingGuidance(variant = variant)) {
                            handleAdaptiveTrainingWeeklyTargetAndCoach()
                        } else {
                            handleNormalWeeklyTargetAndDisableCoach()
                        }
                    }
                }
            }
            .catch { e ->
                handleWatchException(e, "Error observing watch state")
                setCoachEnabledToSharedPreferences(false)
            }
            .launchIn(coroutineScope)
    }

    private fun handleWatchException(throwable: Throwable, message: String) {
        if (throwable is MissingCurrentWatchException) {
            Timber.w("$message: missing watch")
        } else {
            Timber.w(throwable, message)
        }
    }

    private suspend fun handleNormalWeeklyTargetAndDisableCoach() {
        setCoachEnabledToSharedPreferences(false)

        if (getWeeklyTargetSyncNeededFromSharedPreferences()) {
            val duration = getWeeklyTargetDurationFromSharedPrefs()
            setWeeklyTargetToWatch(duration)
        } else {
            syncWeeklyTargetDurationToSharedPrefs()
        }

        amplitudeAnalyticsTracker.trackUserProperty(
            AnalyticsUserProperty.SUUNTO_TRAINING_PLAN_IN_USE,
            AnalyticsPropertyValue.SUUNTO_NOT_BIANCA_USER
        )
    }

    private suspend fun handleAdaptiveTrainingWeeklyTargetAndCoach() {
        val coachEnabled = getCoachEnabledFromSharedPreferences()

        if (getCoachEnabledSyncNeededFromSharedPreferences()) {
            setCoachEnabledToWatch(coachEnabled)
        }

        // Set WeeklyTarget only if coach is disabled
        if (!coachEnabled && getWeeklyTargetSyncNeededFromSharedPreferences()) {
            val duration = getWeeklyTargetDurationFromSharedPrefs()
            setWeeklyTargetToWatch(duration)
        } else {
            syncWeeklyTargetDurationToSharedPrefs()
            syncCoachEnabledToSharedPrefs()
        }
    }

    private suspend fun syncWeeklyTargetDurationToSharedPrefs() {
        runSuspendCatching {
            val watchWeeklyTargetDuration = suuntoWatchModel.weeklyTargetDuration()
            if (watchWeeklyTargetDuration > 0.0F) {
                setWeeklyTargetToSharedPreferences(floor(watchWeeklyTargetDuration).toInt())
            }
        }.onFailure { e ->
            handleWatchException(e, "Error in loading goal wheel values")
        }
    }

    private suspend fun syncCoachEnabledToSharedPrefs() {
        runSuspendCatching {
            val coachEnabled = suuntoWatchModel.adaptiveCoachEnabled()
            setCoachEnabledToSharedPreferences(coachEnabled)
        }.onFailure { e ->
            handleWatchException(e, "Error in loading coach enabled setting")
        }
    }

    private suspend fun setWeeklyTargetToWatch(duration: Int) {
        runSuspendCatching {
            suuntoWatchModel.setWeeklyTargetDuration(duration.toFloat())
            Timber.d("Successfully set weekly target to the watch")
            setWeeklyTargetSyncNeededToSharedPreferences(false)
        }.onFailure { e ->
            handleWatchException(e, "Unable to set weekly target to the watch")
            setWeeklyTargetSyncNeededToSharedPreferences(true)
        }
    }

    private suspend fun setCoachEnabledToWatch(coachEnabled: Boolean) {
        runSuspendCatching {
            suuntoWatchModel.setAdaptiveCoachEnabled(coachEnabled)
            Timber.d("Successfully set coach enabled to the watch")
            setCoachEnabledSyncNeededToSharedPreferences(false)
        }.onFailure { e ->
            handleWatchException(e, "Unable to set coach enabled to the watch")
            setCoachEnabledSyncNeededToSharedPreferences(true)
        }
    }

    private fun setWeeklyTargetToSharedPreferences(weeklyTarget: Int) {
        sharedPreferences.edit {
            putInt(STTConstants.SuuntoPreferences.KEY_SUUNTO_WATCH_WEEKLY_TARGET_DURATION, weeklyTarget)
        }
    }

    private fun getWeeklyTargetDurationFromSharedPrefs(): Int {
        return sharedPreferences.getInt(
            STTConstants.SuuntoPreferences.KEY_SUUNTO_WATCH_WEEKLY_TARGET_DURATION,
            STTConstants.SuuntoPreferences.KEY_SUUNTO_WATCH_WEEKLY_TARGET_DURATION_DEFAULT
        )
    }

    private fun setWeeklyTargetSyncNeededToSharedPreferences(syncNeeded: Boolean) {
        sharedPreferences.edit {
            putBoolean(STTConstants.SuuntoPreferences.KEY_WATCH_WEEKLY_TARGET_SYNC_NEEDED, syncNeeded)
        }
    }

    private fun getWeeklyTargetSyncNeededFromSharedPreferences(): Boolean {
        return sharedPreferences.getBoolean(
            STTConstants.SuuntoPreferences.KEY_WATCH_WEEKLY_TARGET_SYNC_NEEDED,
            false
        )
    }

    private fun setCoachEnabledToSharedPreferences(enabled: Boolean) {
        sharedPreferences.edit {
            putBoolean(
                STTConstants.SuuntoPreferences.KEY_SUUNTO_WATCH_COACH_ENABLED,
                enabled
            )
        }
    }

    private fun getCoachEnabledFromSharedPreferences(): Boolean {
        return sharedPreferences.getBoolean(
            STTConstants.SuuntoPreferences.KEY_SUUNTO_WATCH_COACH_ENABLED,
            false
        )
    }

    private fun setCoachEnabledSyncNeededToSharedPreferences(syncNeeded: Boolean) {
        sharedPreferences.edit {
            putBoolean(STTConstants.SuuntoPreferences.KEY_WATCH_COACH_ENABLED_SYNC_NEEDED, syncNeeded)
        }
    }

    private fun getCoachEnabledSyncNeededFromSharedPreferences(): Boolean {
        return sharedPreferences.getBoolean(
            STTConstants.SuuntoPreferences.KEY_WATCH_COACH_ENABLED_SYNC_NEEDED,
            false
        )
    }
}
