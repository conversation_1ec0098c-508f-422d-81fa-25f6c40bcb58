package com.stt.android.home.devicetype

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.result.contract.ActivityResultContract
import androidx.appcompat.app.AppCompatActivity
import com.stt.android.compose.util.setContentWithTheme
import com.stt.android.social.userprofile.HeadsetNavigator
import com.stt.android.social.userprofile.HeadsetRedDot
import com.stt.android.watch.DeviceActivity
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class DeviceTypeSelectActivity : AppCompatActivity() {

    @Inject
    lateinit var headsetNavigator: HeadsetNavigator

    @Inject
    lateinit var headsetRedDot: HeadsetRedDot

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentWithTheme {
            DeviceTypeSelectScreen(
                onItemClick = ::onDeviceType,
            )
        }
    }

    private fun onDeviceType(type: DeviceType) {
        val forPairAnotherDevice = intent.getBooleanExtra(EXTRA_FOR_PAIR_ANOTHER_DEVICE, false)
        if (forPairAnotherDevice) {
            setResult(RESULT_OK, Intent().apply {
                putExtra(EXTRA_DEVICE_TYPE, type)
            })
        } else {
            when (type) {
                DeviceType.WATCH -> startActivity(DeviceActivity.newIntentForPairing(this))
                DeviceType.HEADPHONES -> {
                    headsetRedDot.markHeadsetRedDotShow()
                    headsetNavigator.launchHeadsetActivity(this)
                }
            }
        }
        finish()
    }

    class ResultContract : ActivityResultContract<Unit, DeviceType?>() {
        override fun createIntent(context: Context, input: Unit): Intent =
            Intent(context, DeviceTypeSelectActivity::class.java).apply {
                putExtra(EXTRA_FOR_PAIR_ANOTHER_DEVICE, true)
            }

        override fun parseResult(resultCode: Int, intent: Intent?): DeviceType? {
            if (resultCode != RESULT_OK) return null
            return intent?.getSerializableExtra(EXTRA_DEVICE_TYPE) as? DeviceType
        }
    }

    companion object {
        private const val EXTRA_FOR_PAIR_ANOTHER_DEVICE = "for_pair_another_device"
        private const val EXTRA_DEVICE_TYPE = "device_type"

        @JvmStatic
        fun newStartIntent(context: Context) = Intent(context, DeviceTypeSelectActivity::class.java)
    }
}
