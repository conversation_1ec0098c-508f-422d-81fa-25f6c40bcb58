package com.stt.android.home.dashboard.widget.suunto247

import com.stt.android.data.TimeUtils
import com.stt.android.domain.activitydata.dailyvalues.FetchDailyRecoveryDataUseCase
import com.stt.android.domain.recovery.RecoveryDataRepository
import com.stt.android.domain.sleep.FetchSleepUseCase
import com.stt.android.home.dashboard.widget.suunto247.ResourcesWidgetData.Companion.NO_BALANCE_VALUE
import com.stt.android.utils.iterator
import com.stt.android.utils.lastRecoverDataForSleep
import com.stt.android.utils.toEpochMilli
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.onEmpty
import java.time.LocalDate
import java.time.LocalTime
import java.time.ZoneId
import java.time.ZonedDateTime
import javax.inject.Inject

class ResourcesWidgetDataFetcher @Inject constructor(
    private val recoveryDataRepository: RecoveryDataRepository,
    private val fetchDailyRecoveryDataUseCase: FetchDailyRecoveryDataUseCase,
    private val fetchSleepUseCase: FetchSleepUseCase,
) {
    fun resourcesWidgetDataFlow(lastDay: LocalDate): Flow<ResourcesWidgetData> {
        val firstDay = lastDay.minusDays(6L)

        val endFetchRangeMillis = lastDay
            .atTime(LocalTime.MAX)
            .toEpochMilli()
        // Query part of the day before earliest shown day in case user didn't sleep past midnight
        // or if they woke after midnight before new recovery data was collected
        val startFetchRangeMillis = firstDay
            .minusDays(1)
            .atTime(12, 0)
            .toEpochMilli()

        val recoveryDataInDateRangeFlow = recoveryDataRepository
            .fetchRecoveryDataForDateRange(startFetchRangeMillis, endFetchRangeMillis)
            .onEmpty { emit(emptyList()) }
        val sleepDataInDateRangeFlow = fetchSleepUseCase
            .fetchSleeps(firstDay, lastDay)
            .onEmpty { emit(emptyList()) }
        val newestBalanceFlow = fetchDailyRecoveryDataUseCase.fetchCurrentBalance(NO_BALANCE_VALUE)
            .catch { emit(NO_BALANCE_VALUE) }
            .onEmpty { emit(NO_BALANCE_VALUE) }

        return combine(
            recoveryDataInDateRangeFlow,
            sleepDataInDateRangeFlow,
            newestBalanceFlow
        ) { recoveryDataInDateRange, sleepDataInDateRange, newestStoredBalance ->
            val recoveryData = recoveryDataInDateRange.sortedBy { it.timestamp }
            val sleepData = sleepDataInDateRange.groupBy {
                TimeUtils.epochToLocalZonedDateTime(it.timestamp).toLocalDate()
            }

            val dailyWakeupBalance = mutableListOf<Float>()
            var wakeupBalancesSum = 0f
            var numDaysWithData = 0

            for (date in firstDay..lastDay) {
                val wakeUpTime = sleepData[date]?.map { it.longSleep?.wokeUp }?.firstOrNull()
                val wakeUpRecoveryData =
                    wakeUpTime?.let { recoveryData.lastRecoverDataForSleep(it) }

                if (wakeUpRecoveryData != null) {
                    dailyWakeupBalance.add(wakeUpRecoveryData.balance)
                    wakeupBalancesSum += wakeUpRecoveryData.balance
                    numDaysWithData++
                } else {
                    dailyWakeupBalance.add(NO_BALANCE_VALUE)
                }
            }

            val averageWakeUpBalance: Float =
                if (numDaysWithData > 0) (wakeupBalancesSum / numDaysWithData) else 0f

            var newestBalanceIsNow = true
            var newestBalanceTimestamp = ZonedDateTime.now()
            var newestBalance: Float? = newestStoredBalance

            // NO_BALANCE_VALUE is the default value for fetchCurrentBalance if watch is not connected,
            // If we don't have a current balance value from the watch show last value from the db
            val newestRecoveryData = recoveryData.lastOrNull()
            if (newestBalance == NO_BALANCE_VALUE) {
                newestBalanceIsNow = false

                if (newestRecoveryData != null &&
                    lastDay == newestRecoveryData.timeISO8601.toLocalDate()
                ) {
                    newestBalanceTimestamp = newestRecoveryData
                        .timeISO8601
                        .withZoneSameInstant(ZoneId.systemDefault())
                    newestBalance = newestRecoveryData.balance
                } else {
                    newestBalanceTimestamp = null
                    newestBalance = null
                }
            }

            ResourcesWidgetData(
                recoveryBalances = dailyWakeupBalance.toList(),
                newestBalance = newestBalance,
                newestBalanceTimestamp = newestBalanceTimestamp,
                newestBalanceIsNow = newestBalanceIsNow,
                averageBalance = averageWakeUpBalance,
                numDaysWithData = numDaysWithData,
                lastDay = lastDay
            )
        }
    }
}
