package com.stt.android.home.dashboardv2.widgets.dataloader

import android.content.Context
import androidx.compose.ui.text.AnnotatedString
import com.stt.android.R
import com.stt.android.data.toEpochMilli
import com.stt.android.domain.trenddata.TrendData
import com.stt.android.domain.trenddata.TrendDataRepository
import com.stt.android.home.dashboardv2.ui.widgets.common.NO_DATA_VALUE
import com.stt.android.home.dashboardv2.ui.widgets.common.generateWidgetTitle
import com.stt.android.home.dashboardv2.widgets.DailyHeartRateWidgetInfo
import com.stt.android.utils.atEndOfDay
import com.stt.android.utils.averageOrNull
import com.stt.android.utils.toEpochMilli
import com.suunto.algorithms.data.HeartRate.Companion.hz
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.map
import timber.log.Timber
import java.time.LocalDate
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import kotlin.math.roundToInt
import com.stt.android.core.R as CR

internal class DailyHeartRateWidgetDataLoader @Inject constructor(
    private val context: Context,
    private val trendDataRepository: TrendDataRepository,
) : WidgetDataLoader<DailyHeartRateWidgetInfo>() {

    override suspend fun load(param: Param): WidgetData<DailyHeartRateWidgetInfo> {
        val today = LocalDate.now()
        val startOfDayMillis = today.atStartOfDay().toEpochMilli()

        val trendDataFlow = trendDataRepository.fetchTrendDataForDateRange(
            fromTimestamp = startOfDayMillis,
            toTimestamp = startOfDayMillis,
            aggregated = false
        )

        return WidgetData(
            widgetType = param.type,
            editMode = param.editMode,
            widgetInfo = trendDataFlow
                .map { periodTrendData ->
                    mapDailyHeartRateWidgetInfo(today, periodTrendData)
                }
                .catch { e ->
                    Timber.w(e, "Error loading heart rate widget data")
                }
        )
    }

    private fun mapDailyHeartRateWidgetInfo(
        today: LocalDate,
        periodTrendData: List<TrendData>
    ): DailyHeartRateWidgetInfo {
        val trendDateByTenSeconds = groupTrendDataByTenMinutes(today, periodTrendData)
        val avgHrBy10Seconds = trendDateByTenSeconds.values.map { trends ->
            if (trends.all { it.hr == null }) {
                NO_DATA_VALUE
            } else {
                trends.mapNotNull { it.hr }.average().toFloat()
            }
        }
        val notNullHrList = periodTrendData.mapNotNull { it.hr }
        val allAvg = notNullHrList.averageOrNull() ?: NO_DATA_VALUE
        val maxHr = notNullHrList.maxOrNull() ?: NO_DATA_VALUE
        val minHr = notNullHrList.minOrNull() ?: NO_DATA_VALUE
        val progresses = avgHrBy10Seconds.map { value ->
            if (value < 0) {
                NO_DATA_VALUE
            } else if (maxHr == minHr) {
                1f // All values are same, show it top
            } else {
                1f * (value - minHr) / (maxHr - minHr)
            }
        }

        val unit = context.getString(CR.string.bpm)
        val title = if (minHr > 0) {
            generateWidgetTitle("${minHr.hz.inBpm.roundToInt()}-${maxHr.hz.inBpm.roundToInt()}", unit)
        } else {
            AnnotatedString(
                context.getString(R.string.widget_no_data_title)
            )
        }
        val subtitle = if (allAvg > 0) {
            context.getString(R.string.widget_avg_heart_rate, allAvg.hz.inBpm.roundToInt())
        } else {
            context.getString(R.string.widget_no_data_subtitle)
        }

        return DailyHeartRateWidgetInfo(
            progresses = progresses,
            title = title,
            subtitle = subtitle,
        )
    }

    private fun groupTrendDataByTenMinutes(today: LocalDate, trendDatas: List<TrendData>): Map<Long, List<TrendData>> {
        val interval = TimeUnit.MINUTES.toMillis(10)
        val todayStartMillis = today.atStartOfDay().toEpochMilli()
        val todayEndMillis = today.atEndOfDay().toEpochMilli()
        val timeFrameToTrendDatas = mutableMapOf<Long, MutableList<TrendData>>()

        for (time in todayStartMillis until todayEndMillis step interval) {
            timeFrameToTrendDatas[time] = mutableListOf()
        }

        trendDatas.forEach { trendData ->
            val timestamp = trendData.timeISO8601.toEpochMilli()
            val groupIndex = (timestamp - todayStartMillis) / interval
            val groupStartTime = todayStartMillis + groupIndex * interval
            timeFrameToTrendDatas.computeIfAbsent(groupStartTime) { mutableListOf() }.add(trendData)
        }

        return timeFrameToTrendDatas
    }
}
