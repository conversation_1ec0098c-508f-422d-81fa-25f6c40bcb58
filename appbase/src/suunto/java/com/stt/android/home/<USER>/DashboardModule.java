package com.stt.android.home.dashboard;

import com.stt.android.newfeed.FeedTopBannerNavigator;
import com.stt.android.newfeed.FeedTopBannerNavigatorNoOp;
import dagger.Binds;
import dagger.Module;
import dagger.hilt.InstallIn;
import dagger.hilt.components.SingletonComponent;

@Module
@InstallIn(SingletonComponent.class)
public abstract class DashboardModule {

    @Binds
    public abstract FeedTopBannerNavigator bindFeedTopBannerNavigator(
        FeedTopBannerNavigatorNoOp feedTopBannerNavigatorNoOp
    );
}
