package com.stt.android.home.dashboardv2

import android.annotation.SuppressLint
import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothManager
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.SharedPreferences
import android.os.Build
import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.lifecycle.viewModelScope
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.stt.android.R
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.FeedController
import com.stt.android.coroutines.await
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.di.FeatureTogglePreferences
import com.stt.android.di.SuuntoSharedPrefs
import com.stt.android.domain.android.DeviceFeatureStates
import com.stt.android.domain.firmware.CheckForNewerFirmwareUseCase
import com.stt.android.home.dashboardv2.HeadphoneViewData.Companion.HEADPHONE_STATE_UPDATE_ACTION
import com.stt.android.home.dashboardv2.HeadphoneViewData.Companion.KEY_HEADPHONE_STATE
import com.stt.android.home.dashboardv2.ui.DashboardScreenViewEvent
import com.stt.android.utils.STTConstants
import com.stt.android.utils.isNearbyDevicesPermissionGranted
import com.stt.android.watch.DeviceHolderViewModel.Companion.isBackgroundLocationPermissionAsked
import com.stt.android.watch.MissingCurrentWatchException
import com.stt.android.watch.SuuntoWatchModel
import com.suunto.connectivity.deviceid.SuuntoDeviceCapabilityInfoProvider
import com.suunto.connectivity.sdsmanager.model.MdsDeviceInfo
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoDeviceType.Companion.fromVariantName
import com.suunto.connectivity.sync.SyncState
import com.suunto.connectivity.watch.WatchState
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.launch
import kotlinx.coroutines.reactive.asFlow
import kotlinx.coroutines.rx2.await
import timber.log.Timber
import javax.inject.Inject

internal enum class ToolbarSubMenu(
    @StringRes val label: Int,
    val clickEvent: DashboardScreenViewEvent,
) {
    LOG_PERIOD(R.string.toolbar_log_period, DashboardScreenViewEvent.LogMenstrualCycle),
    ADD_WORKOUT_MANUALLY(R.string.toolbar_add_manual_workout, DashboardScreenViewEvent.AddWorkoutManually),
    RECORD_WITH_APP(R.string.toolbar_record_workout, DashboardScreenViewEvent.RecordWorkout),
    CREATE_INTERVAL_WORKOUT(R.string.dashboard_plus_button_create_interval_workout, DashboardScreenViewEvent.CreateIntervalWorkout),
    DOWNLOAD_OFFLINE_MAP(R.string.download_offline_maps, DashboardScreenViewEvent.DownloadOfflineMap),
    PLAN_DIVE(R.string.toolbar_plan_dive, DashboardScreenViewEvent.PlanDive),
    PAIR_DEVICE(R.string.device_pair_device_menu_item, DashboardScreenViewEvent.PairDevice),
    ;
}

// TODO This is unfortunately used by SyncHeadphoneDataService from headset module.
sealed class HeadphoneViewData(
    @DrawableRes val iconResId: Int = 0,
    val type: Int = 0,
    @StringRes val animationFileNameResId: Int = 0
) {
    data object HeadphoneConnected :
        HeadphoneViewData(iconResId = R.drawable.icon_headphone_connected, type = 1)

    data object HeadphoneConnecting :
        HeadphoneViewData(type = 2, animationFileNameResId = R.string.headphone_anim_connecting)

    data object HeadphoneDisConnected :
        HeadphoneViewData(iconResId = R.drawable.icon_headphone_disconnected, type = 3)

    data object Default : HeadphoneViewData(iconResId = R.drawable.ic_headphones_toolbar)

    data object BluetoothDisabled :
        HeadphoneViewData(iconResId = R.drawable.icon_bluetooth_disconnected, type = -1)

    companion object {
        const val HEADPHONE_STATE_UPDATE_ACTION =
            "com.stt.android.suunto.headset.HEADPHONE_STATE_UPDATE"
        const val KEY_HEADPHONE_STATE = "com.stt.android.suunto.headset.KEY_HEADPHONE_STATE"
    }
}

@HiltViewModel
internal class ToolbarViewModel @Inject constructor(
    currentUserController: CurrentUserController,
    private val suuntoWatchModel: SuuntoWatchModel,
    @FeatureTogglePreferences private val featureTogglePreferences: SharedPreferences,
    private val deviceFeatureStates: DeviceFeatureStates,
    private val checkForNewerFirmwareUseCase: CheckForNewerFirmwareUseCase,
    @SuuntoSharedPrefs private val suuntoPreferences: SharedPreferences,
    @ApplicationContext private val applicationContext: Context,
    feedController: FeedController,
    dispatchers: CoroutinesDispatchers,
    headsetInfoLoader: HeadsetInfoLoader,
) : BaseToolbarViewModel(currentUserController, feedController, dispatchers) {

    sealed class WatchStateViewData {
        class BluetoothDisabled(@DrawableRes val iconResId: Int) : WatchStateViewData()

        class WatchNotPaired(@DrawableRes val iconResId: Int) : WatchStateViewData()

        class WatchReconnecting(@StringRes val animationFileNameResId: Int) : WatchStateViewData()

        class WatchSyncing(@StringRes val animationFileNameResId: Int) : WatchStateViewData()

        class WatchNotConnected(@DrawableRes val iconResId: Int) : WatchStateViewData()

        class WatchBusy(@DrawableRes val iconResId: Int) : WatchStateViewData()

        class WatchFail(@DrawableRes val iconResId: Int) : WatchStateViewData()

        class WatchConnected(@DrawableRes val iconResId: Int) : WatchStateViewData()

        class UpdateAvailable(@DrawableRes val iconResId: Int) : WatchStateViewData()
    }

    private var observeWatchStateJob: Job? = null
    private val _watchState: MutableStateFlow<WatchStateViewData> = MutableStateFlow(
        WatchStateViewData.WatchNotConnected(R.drawable.toolbar_watch_not_paired)
    )
    val watchState: StateFlow<WatchStateViewData> = _watchState.asStateFlow()

    private val _headphoneState: MutableStateFlow<HeadphoneViewData> = MutableStateFlow(
        HeadphoneViewData.Default
    )
    val headphoneState: StateFlow<HeadphoneViewData> = _headphoneState.asStateFlow()

    private val bluetoothBondedDevices = MutableStateFlow(fetchBluetoothBondedSafety())
    private var bluetoothEnabled = false

    val showHeadsetEntrance = combine(
        bluetoothBondedDevices.asStateFlow(),
        headsetInfoLoader.fetchLastPairedHeadsetMac()
    ) { bondedDevices, pairedHeadsetMac ->
        bondedDevices.any { it.address == pairedHeadsetMac }
    }

    private val bluetoothConnectionReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            if (intent?.action == BluetoothAdapter.ACTION_STATE_CHANGED) {
                val bluetoothState = intent.getIntExtra(BluetoothAdapter.EXTRA_STATE, -1)
                when (bluetoothState) {
                    BluetoothAdapter.STATE_ON -> {
                        bluetoothEnabled = true
                        bluetoothBondedDevices.value = fetchBluetoothBondedSafety()
                        _headphoneState.value = HeadphoneViewData.Default
                    }

                    BluetoothAdapter.STATE_OFF -> {
                        bluetoothEnabled = false
                        _headphoneState.value = HeadphoneViewData.BluetoothDisabled
                    }
                    // avoid calling fetchBluetoothBondedSafety() when the bluetooth is turning off
                    BluetoothAdapter.STATE_TURNING_OFF -> bluetoothEnabled = false
                }
            }

            if (bluetoothEnabled && intent?.action == BluetoothAdapter.ACTION_CONNECTION_STATE_CHANGED) {
                bluetoothBondedDevices.value = fetchBluetoothBondedSafety()
            }
        }
    }

    private val headphoneStateUpdateReceiver: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            if (intent?.action == HEADPHONE_STATE_UPDATE_ACTION) {
                val state = intent.getIntExtra(KEY_HEADPHONE_STATE, 0)
                when (state) {
                    HeadphoneViewData.HeadphoneConnecting.type -> _headphoneState.value =
                        HeadphoneViewData.HeadphoneConnecting

                    HeadphoneViewData.HeadphoneConnected.type -> _headphoneState.value =
                        HeadphoneViewData.HeadphoneConnected

                    HeadphoneViewData.HeadphoneDisConnected.type -> _headphoneState.value =
                        HeadphoneViewData.HeadphoneDisConnected
                }
            }
        }
    }

    init {
        loadMoreMenus()
        registerBluetoothBondedReceiver()
        registerHeadphoneStateUpdateReceiver()
    }

    private fun registerBluetoothBondedReceiver() {
        val filter = IntentFilter()
        filter.addAction(BluetoothAdapter.ACTION_CONNECTION_STATE_CHANGED)
        filter.addAction(BluetoothAdapter.ACTION_STATE_CHANGED)
        applicationContext.registerReceiver(bluetoothConnectionReceiver, filter)
    }

    private fun unregisterBluetoothBondedReceiver() {
        applicationContext.unregisterReceiver(bluetoothConnectionReceiver)
    }

    private fun registerHeadphoneStateUpdateReceiver() {
        val filter = IntentFilter()
        filter.addAction(HEADPHONE_STATE_UPDATE_ACTION)
        LocalBroadcastManager.getInstance(applicationContext).registerReceiver(headphoneStateUpdateReceiver, filter)
    }

    private fun unregisterHeadphoneStateUpdateReceiver() {
        LocalBroadcastManager.getInstance(applicationContext).unregisterReceiver(headphoneStateUpdateReceiver)
    }

    @SuppressLint("MissingPermission")
    private fun fetchBluetoothBondedSafety() = runCatching {
        val bluetoothManager =
            applicationContext.getSystemService(Context.BLUETOOTH_SERVICE) as BluetoothManager
        bluetoothEnabled = bluetoothManager.adapter.isEnabled
        bluetoothManager.adapter.bondedDevices.toList()
    }.getOrElse { emptyList() }

    private fun loadMoreMenus() {
        viewModelScope.launch {
            runSuspendCatching {
                val menusOnMore = ToolbarSubMenu.entries.toMutableSet()

                val isDivePlannerEnabled = featureTogglePreferences.getBoolean(
                    STTConstants.FeatureTogglePreferences.KEY_DIVE_PLANNER,
                    STTConstants.FeatureTogglePreferences.KEY_DIVE_PLANNER_DEFAULT
                )
                if (!isDivePlannerEnabled) {
                    menusOnMore.remove(ToolbarSubMenu.PLAN_DIVE)
                }

                menusOnMore.addOrRemove(
                    ToolbarSubMenu.DOWNLOAD_OFFLINE_MAP,
                    suuntoWatchModel.supportsOfflineMaps(false).await()
                )

                updateMoreMenus(menusOnMore.toList())
            }
        }
    }

    fun checkSupportOfflineMaps() {
        viewModelScope.launch {
            runSuspendCatching {
                addOrRemoveOfflineMapsMenu(
                    suuntoWatchModel.supportsOfflineMaps(false).await()
                )
            }
        }
    }

    private fun addOrRemoveOfflineMapsMenu(add: Boolean) {
        updateMoreMenus(
            viewState.value.menusOnMore.toMutableSet()
                .addOrRemove(ToolbarSubMenu.DOWNLOAD_OFFLINE_MAP, add)
                .toList()
        )
    }

    private fun MutableSet<ToolbarSubMenu>.addOrRemove(
        subMenu: ToolbarSubMenu,
        add: Boolean
    ): MutableSet<ToolbarSubMenu> {
        if (!add) {
            this.remove(subMenu)
        } else if (!contains(subMenu)) {
            add(subMenu)
        }
        return this
    }

    fun observeWatchState() {
        observeWatchStateJob?.cancel()
        observeWatchStateJob = viewModelScope.launch {
            runSuspendCatching {
                val deviceType = suuntoWatchModel.currentWatch().suuntoBtDevice.getDeviceType()
                val isEonComputer = deviceType.isEonComputer
                if (!isBluetoothEnabled()) {
                    _watchState.value =
                        WatchStateViewData.BluetoothDisabled(if (isEonComputer) R.drawable.eon_bluetooth_off_fill else R.drawable.watch_bluetooth_off_fill)
                    return@runSuspendCatching
                }
                suuntoWatchModel.watchStates()
                    .distinctUntilChanged()
                    .collect { watchState ->
                        val connectionState = watchState.connectionState
                        if (!watchState.isPaired) {
                            _watchState.value =
                                WatchStateViewData.WatchNotPaired(R.drawable.toolbar_watch_not_paired)
                        } else if (connectionState == WatchState.ConnectionState.CONNECTING || connectionState == WatchState.ConnectionState.RECONNECTING) {
                            _watchState.value =
                                WatchStateViewData.WatchReconnecting(if (isEonComputer) R.string.toolbar_looking_for_eon else R.string.toolbar_looking_for_watch)
                        } else if (watchState.syncState.state != SyncState.NOT_SYNCING) {
                            _watchState.value =
                                WatchStateViewData.WatchSyncing(if (isEonComputer) R.string.toolbar_eon_syncing else R.string.toolbar_watch_syncing)
                        } else if (connectionState == WatchState.ConnectionState.DISCONNECTED) {
                            _watchState.value =
                                WatchStateViewData.WatchNotConnected(if (isEonComputer) R.drawable.eon_not_connected_fill else R.drawable.watch_not_connected_fill)
                        } else if (watchState.isDeviceBusy) {
                            _watchState.value =
                                WatchStateViewData.WatchBusy(R.drawable.toolbar_watch_busy_icon)
                        } else if (connectionState == WatchState.ConnectionState.CONNECTED) {
                            // We're just connected and no syncing happening so let's show the latest sync result available
                            showLatestSyncStatus(watchState, isEonComputer);
                        }
                    }
            }.onFailure { throwable ->
                if (throwable is MissingCurrentWatchException) {
                    _watchState.value =
                        WatchStateViewData.WatchNotPaired(R.drawable.toolbar_watch_not_paired)
                } else {
                    Timber.w(UnsupportedOperationException("Oops we lost stream to watch state changes"))
                }
            }
        }
    }

    private fun showLatestSyncStatus(watchState: WatchState, isEonComputer: Boolean) {
        viewModelScope.launch {
            runSuspendCatching {
                val syncResult = suuntoWatchModel.lastSyncResult.await()
                val spartan =
                    runSuspendCatching { suuntoWatchModel.currentWatch.await() }.getOrNull()
                val isFwUpdateAvailable =
                    runSuspendCatching { isFirmwareUpdateAvailable(watchState.deviceInfo) }.getOrElse { false }
                _watchState.value = if (syncResult.logbookResult.logbookResult.isFailed()) {
                    WatchStateViewData.WatchFail(if (isEonComputer) R.drawable.eon_sync_failed_fill else R.drawable.watch_sync_failed_fill)
                } else {
                    val showConnectionAlert = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                        spartan != null && spartan.suuntoBtDevice.deviceType.isSuunto3Family &&
                            !deviceFeatureStates.isBackgroundLocationGranted() &&
                            !isBackgroundLocationPermissionAsked(suuntoPreferences)
                    } else {
                        false
                    }
                    if (showConnectionAlert) {
                        WatchStateViewData.WatchFail(if (isEonComputer) R.drawable.eon_sync_failed_fill else R.drawable.watch_sync_failed_fill)
                    } else {
                        if ((requiresForcedUpdate(watchState) && watchState.firmwareUpdateStatus.uploadInProgress) || isFwUpdateAvailable) {
                            // Forced update ongoing, show update icon
                            WatchStateViewData.UpdateAvailable(if (isEonComputer) R.drawable.eon_firmware_update_fill else R.drawable.watch_firmware_update_fill)
                        } else {
                            WatchStateViewData.WatchConnected(if (isEonComputer) R.drawable.eon_connected_fill else R.drawable.watch_connected_fill)
                        }
                    }
                }
            }.onFailure { throwable ->
                if (throwable is MissingCurrentWatchException) {
                    _watchState.value =
                        WatchStateViewData.WatchNotPaired(R.drawable.toolbar_watch_not_paired)
                }
            }
        }
    }

    private suspend fun isFirmwareUpdateAvailable(deviceInfo: MdsDeviceInfo?): Boolean =
        deviceInfo?.let {
            val deviceType = fromVariantName(deviceInfo.variant)
            if (!deviceType.supportsOtaUpdate(deviceInfo.swVersion)) {
                checkForNewerFirmwareUseCase.isUpdateAvailable(
                    deviceInfo.variant,
                    deviceInfo.hwCompatibilityId,
                    deviceInfo.swVersion,
                    deviceInfo.productVersion,
                    deviceType.isSuunto7,
                    deviceType.supportOtaUpdateCheck(deviceInfo)
                ).asFlow().firstOrNull() ?: false
            } else {
                val checkForOtaUpdatesResponse =
                    runSuspendCatching {
                        suuntoWatchModel.checkForNewOtaUpdates().await()
                    }.getOrNull()
                checkForOtaUpdatesResponse != null && checkForOtaUpdatesResponse.otaUpdateState.hasUpdate()
            }
        } ?: false

    private fun requiresForcedUpdate(watchState: WatchState): Boolean =
        watchState.deviceInfo?.let { deviceInfo ->
            val variant = deviceInfo.variant
            val deviceType = fromVariantName(variant)
            SuuntoDeviceCapabilityInfoProvider[deviceType].requiresForcedUpdate(deviceInfo)
        } ?: false

    private fun isBluetoothEnabled(): Boolean {
        val nearbyDevicesGranted = applicationContext.isNearbyDevicesPermissionGranted()
        val bluetoothManager =
            applicationContext.getSystemService(Context.BLUETOOTH_SERVICE) as BluetoothManager
        return nearbyDevicesGranted && bluetoothManager.adapter?.isEnabled == true
    }

    override fun onCleared() {
        super.onCleared()
        observeWatchStateJob?.cancel()
        unregisterBluetoothBondedReceiver()
        unregisterHeadphoneStateUpdateReceiver()
    }
}
