package com.stt.android.home.dayviewv2.usecase

import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.WorkoutHeaderController
import com.stt.android.controllers.currentUserWorkoutUpdated
import com.stt.android.data.toEpochMilli
import com.stt.android.domain.workouts.WorkoutHeader
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onStart
import java.time.LocalDate
import java.time.ZoneId
import javax.inject.Inject

class FetchDailyWorkoutsUseCase @Inject constructor(
    private val currentUserController: CurrentUserController,
    private val fetchWorkoutsController: WorkoutHeaderController,
    private val dispatchers: CoroutinesDispatchers,
) {
    /**
     * Get workout headers for given date.
     * New lists are emitted if there are new workouts or changes to existing ones.
     *
     * @param date
     * @return Flow of lists of [WorkoutHeader].
     */
    operator fun invoke(date: LocalDate): Flow<List<WorkoutHeader>> {
        val startMillis = date.atStartOfDay(ZoneId.systemDefault()).toEpochMilli()
        val endMillis = date.plusDays(1).atStartOfDay(ZoneId.systemDefault()).toEpochMilli() - 1L
        return fetchWorkoutsController.currentUserWorkoutUpdated
            .onStart { emit(Unit) }
            .map {
                fetchWorkoutsController.findNotDeletedWorkoutHeaders(
                    currentUserController.username,
                    null,
                    startMillis,
                    endMillis,
                )
            }
            .distinctUntilChanged()
            .flowOn(dispatchers.io)
    }
}
