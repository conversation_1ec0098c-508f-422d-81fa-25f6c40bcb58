package com.stt.android.social.userprofileV2

import android.content.SharedPreferences
import androidx.lifecycle.lifecycleScope
import com.stt.android.R
import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.AnalyticsEventProperty
import com.stt.android.analytics.AnalyticsProperties
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.di.SuuntoSharedPrefs
import com.stt.android.home.feedback.FeedbackActivity
import com.stt.android.home.settings.connectedservices.ConnectedServicesActivity
import com.stt.android.social.userprofile.ChatBotActivity
import com.stt.android.social.userprofile.HeadsetNavigator
import com.stt.android.social.userprofile.HeadsetRedDot
import com.stt.android.social.userprofile.SupportActivity
import com.stt.android.usecases.GetHumanReadablePairedWatchModelNameUseCase
import com.stt.android.usecases.NoPairedWatchModelFoundException
import com.stt.android.utils.STTConstants
import com.stt.android.utils.SupportUrlData
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

abstract class SuuntoUserProfileActivity : BaseUserProfileActivity(){
    @Inject
    lateinit var headsetNavigator: HeadsetNavigator

    @Inject
    lateinit var headsetRedDot: HeadsetRedDot

    @Inject
    lateinit var amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker

    @Inject
    @SuuntoSharedPrefs
    lateinit var sharedPreferences: SharedPreferences

    @Inject
    lateinit var getHumanReadablePairedWatchModelNameUseCase: GetHumanReadablePairedWatchModelNameUseCase

    override fun getSettingMenuList(): List<SettingMenuInfo> {
        return listOf(
            SettingMenuInfo(
                SettingMenuType.HEADPHONES,
                R.drawable.ic_headphones,
                getString(R.string.device_type_select_headphones),
            ),
            SettingMenuInfo(
                SettingMenuType.SETTING,
                R.drawable.ic_settings,
                getString(R.string.settings),
            ),
            SettingMenuInfo(
                SettingMenuType.FIND_PEOPLE,
                R.drawable.ic_find_people,
                getString(R.string.find_friends),
            ),
            SettingMenuInfo(
                SettingMenuType.FEEDBACK,
                R.drawable.ic_feedback,
                getString(R.string.feedback),
            ),
            SettingMenuInfo(
                SettingMenuType.CHAT_BOT,
                R.drawable.ic_chat_bot,
                getString(R.string.user_profile_support_chat_title),
            ),
            SettingMenuInfo(
                SettingMenuType.PARTNER_SERVICE,
                R.drawable.ic_partner,
                getString(R.string.partner_connections_title),
            ),
            SettingMenuInfo(
                SettingMenuType.SUPPORT,
                R.drawable.ic_support,
                getString(R.string.settings_help),
            ),
        )
    }

    override fun onPartnerServicesClicked() {
        if (!clickEventThrottler.checkAcceptEvent()) return
        openPartnerServicesActivity()
    }

    private fun openPartnerServicesActivity() {
        startActivity(
            ConnectedServicesActivity.newIntent(
                context = this,
                source = AnalyticsPropertyValue.ConnectedServicesListSource.PROFILE_SCREEN
            )
        )
    }

    override fun onPremiumSubscriptionClicked(isSubscribed: Boolean) {
        // Do nothing
    }

    override fun onMyHeadsetClicked() {
        headsetNavigator.launchHeadsetActivity(this)
    }

    override fun provideHeadsetRedDot(): HeadsetRedDot {
        return headsetRedDot
    }

    override fun onSupportClicked() {
        if (!clickEventThrottler.checkAcceptEvent()) return

        lifecycleScope.launch {
            runSuspendCatching {
                val metadata = loadSupportMetadataUseCase.getMetadataJson()
                SupportActivity.newIntent(
                    this@SuuntoUserProfileActivity,
                    SupportUrlData.supportUrl[STTConstants.HelpShiftPublishId.SUUNTO_MAIN_SUPPORT],
                    metadata,
                    getString(R.string.settings_help)
                )
            }.onFailure { e ->
                Timber.e(e, "Failed to load valid subscription")
            }
        }
    }

    override fun onChatBotClicked() {
        startActivity(ChatBotActivity.newIntent(this))
        trackAnalytics(event = AnalyticsEvent.SUPPORT_CHAT_BOT)
    }

    override fun onFeedbackClicked() {
        if (!clickEventThrottler.checkAcceptEvent()) return
        startActivity(FeedbackActivity.newIntent(this))
    }


    override fun trackAnalytics(event: String) {
        val watchModel: String? = try {
            getHumanReadablePairedWatchModelNameUseCase()
        } catch (e: NoPairedWatchModelFoundException) {
            null
        }

        AnalyticsProperties()
            .apply {
                put(AnalyticsEventProperty.SUUNTO_WATCH_MODEL, watchModel)
            }.let {
                amplitudeAnalyticsTracker.trackEvent(event, it)
            }
    }
}
