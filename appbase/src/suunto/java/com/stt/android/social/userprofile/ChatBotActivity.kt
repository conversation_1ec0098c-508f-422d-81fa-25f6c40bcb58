package com.stt.android.social.userprofile

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.net.Uri
import android.os.Bundle
import android.webkit.WebChromeClient
import android.webkit.WebResourceError
import android.webkit.WebResourceRequest
import android.webkit.WebView
import android.webkit.WebViewClient
import androidx.activity.OnBackPressedCallback
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.Toolbar
import androidx.core.graphics.drawable.toDrawable
import androidx.core.net.toUri
import androidx.core.view.isVisible
import com.stt.android.R
import com.stt.android.databinding.ActivityChatBotBinding
import com.stt.android.usecases.GetHumanReadablePairedWatchModelNameUseCase
import com.stt.android.usecases.NoPairedWatchModelFoundException
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber
import java.util.Locale
import javax.inject.Inject

@AndroidEntryPoint
class ChatBotActivity : AppCompatActivity() {
    @Inject
    lateinit var getHumanReadablePairedWatchModelNameUseCase: GetHumanReadablePairedWatchModelNameUseCase

    private var webView: WebView? = null

    @SuppressLint("SetJavaScriptEnabled")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        val binding = ActivityChatBotBinding.inflate(layoutInflater)
        setContentView(binding.root)
        setActionbar(binding.toolbar)
        webView = binding.webView
        binding.webView.loadUrl(makeChatBotUrl())
        binding.webView.settings.javaScriptEnabled = true
        binding.webView.webChromeClient = object : WebChromeClient() {
            override fun onProgressChanged(view: WebView?, newProgress: Int) {
                super.onProgressChanged(view, newProgress)
                if (isDestroyed) return
                if (newProgress == 100) {
                    binding.progressBar.isVisible = false
                }
            }
        }
        binding.webView.webViewClient = object : WebViewClient() {
            override fun onPageFinished(view: WebView?, url: String?) {
                super.onPageFinished(view, url)
                if (isDestroyed) return
                Timber.d("webView page finished")
            }

            override fun onReceivedError(
                view: WebView?,
                request: WebResourceRequest?,
                error: WebResourceError?
            ) {
                super.onReceivedError(view, request, error)
                if (isDestroyed) return
                binding.progressBar.isVisible = false
                binding.error.isVisible = false
                Timber.w("webView load error:${error?.description}")
            }
        }
        onBackPressedDispatcher.addCallback(
            this,
            object : OnBackPressedCallback(true) {
                override fun handleOnBackPressed() {
                    finish()
                }
            }
        )
    }

    override fun onDestroy() {
        super.onDestroy()
        webView?.destroy()
        webView = null
    }

    private fun setActionbar(toolbar: Toolbar) {
        setSupportActionBar(toolbar)
        toolbar.setNavigationOnClickListener {
            finish()
        }
        // Nasty hack to hide the logo and make the title visible from the component used
        toolbar.logo = Color.TRANSPARENT.toDrawable()
        supportActionBar?.apply {
            title = getString(R.string.support_chat_screen_title)
        }
    }

    private fun makeChatBotUrl(): String {
        val product: String? = try {
            getHumanReadablePairedWatchModelNameUseCase()
        } catch (e: NoPairedWatchModelFoundException) {
            null
        }
        val localeLanguageCode = Locale.getDefault().language

        return getString(R.string.support_chat_bot_url).toUri()
            .buildUpon()
            .appendPath(localeLanguageCode) // If the language is not supported by the backend, English is used
            .appendPath(getString(R.string.support_chat_bot_knowledge_database_id))
            .appendQueryParameterIfNotNull("product", product)
            .build()
            .toString()
    }

    companion object {
        fun newIntent(context: Context): Intent = Intent(context, ChatBotActivity::class.java)
    }
}


private fun Uri.Builder.appendQueryParameterIfNotNull(key: String, value: String?): Uri.Builder {
    return if (value != null) {
        appendQueryParameter(key, value)
    } else {
        this
    }
}
