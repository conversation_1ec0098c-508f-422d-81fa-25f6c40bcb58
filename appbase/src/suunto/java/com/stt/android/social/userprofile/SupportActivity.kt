package com.stt.android.social.userprofile

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.view.View
import android.webkit.JavascriptInterface
import android.webkit.WebChromeClient
import android.webkit.WebResourceError
import android.webkit.WebResourceRequest
import android.webkit.WebView
import android.webkit.WebViewClient
import androidx.activity.OnBackPressedCallback
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.Toolbar
import androidx.core.graphics.drawable.toDrawable
import com.stt.android.R
import com.stt.android.databinding.ActivitySupportBinding
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber

@AndroidEntryPoint
class SupportActivity : AppCompatActivity() {
    private var metadata = ""
    private var webView: WebView? = null
    private var titleString = ""

    @SuppressLint("SetJavaScriptEnabled")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        val supportUrl = intent.getStringExtra(SUPPORT_URL)
        metadata = intent.getStringExtra(SUPPORT_METADATA) ?: ""
        titleString = intent.getStringExtra(SHOW_TITLE) ?: ""
        Timber.d("metaData value is:$metadata")
        Timber.d("supportUrl value is:$supportUrl")
        val binding =
            ActivitySupportBinding.inflate(layoutInflater)
        setContentView(binding.root)
        binding.toolbar.title = getString(R.string.settings_help)
        setActionbar(binding.toolbar)
        webView = binding.webView
        binding.webView.loadUrl(supportUrl ?: "")
        binding.webView.settings.javaScriptEnabled = true
        binding.webView.settings.domStorageEnabled = true
        binding.webView.addJavascriptInterface(this, "js_metadata")
        binding.webView.webChromeClient = object : WebChromeClient() {
            override fun onProgressChanged(view: WebView?, newProgress: Int) {
                super.onProgressChanged(view, newProgress)
                if (isDestroyed) return
                if (newProgress == 100) {
                    binding.progressBar.visibility = View.GONE
                }
            }
        }
        binding.webView.webViewClient = object : WebViewClient() {
            override fun shouldOverrideUrlLoading(
                view: WebView?,
                request: WebResourceRequest?
            ): Boolean {
                // fix Redirect to http://...
                var url = request?.url.toString()
                if (url.startsWith("http://")) {
                    url = url.replaceFirst("http://", "https://")
                }
                view?.loadUrl(url)
                return true
            }

            override fun onPageFinished(view: WebView?, url: String?) {
                super.onPageFinished(view, url)
                if (isDestroyed) return
                Timber.d("webView page finished")
            }

            override fun onReceivedError(
                view: WebView?,
                request: WebResourceRequest?,
                error: WebResourceError?
            ) {
                super.onReceivedError(view, request, error)
                if (isDestroyed) return
                binding.progressBar.visibility = View.GONE
                Timber.w("webView load error:${error?.description}")
            }
        }
        onBackPressedDispatcher.addCallback(
            this,
            object : OnBackPressedCallback(true) {
                override fun handleOnBackPressed() {
                    handleBack()
                }
            }
        )
    }

    override fun onDestroy() {
        super.onDestroy()
        webView?.removeJavascriptInterface("js_metadata")
        webView?.destroy()
        webView = null
    }

    @JavascriptInterface
    fun getMetadata(): String {
        Timber.d("js call getMetaData()")
        return metadata
    }

    private fun setActionbar(toolbar: Toolbar) {
        setSupportActionBar(toolbar)
        if (titleString.isNotEmpty()) {
            toolbar.logo = Color.TRANSPARENT.toDrawable()
        }
        supportActionBar?.apply {
            title = titleString
        }
        toolbar.setNavigationOnClickListener {
            handleBack()
        }
    }

    private fun handleBack() {
        finish()
    }

    companion object {
        const val SUPPORT_URL = "SUPPORT_URL"
        const val SUPPORT_METADATA = "SUPPORT_METADATA"
        const val SHOW_TITLE = "SHOW_TITLE"
        fun newIntent(context: Context, supportUrl: String?, metaData: String? = null, title: String) {
            val intent = Intent(context, SupportActivity::class.java)
            intent.putExtra(SUPPORT_URL, supportUrl)
            intent.putExtra(SUPPORT_METADATA, metaData)
            intent.putExtra(SHOW_TITLE, title)
            context.startActivity(intent)
        }
    }
}
