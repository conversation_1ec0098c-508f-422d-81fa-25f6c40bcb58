package com.stt.android.tooltips

import android.app.Activity
import android.content.SharedPreferences
import com.github.xizzhu.simpletooltip.ToolTipView
import com.stt.android.di.TooltipPreferences
import com.stt.android.ui.utils.ToolTipHelper
import com.stt.android.utils.STTConstants
import timber.log.Timber
import javax.inject.Inject

class Tooltip @Inject constructor(
    @TooltipPreferences private val tooltipPreferences: SharedPreferences
) : BaseTooltip(tooltipPreferences) {
    /**
     * Show post-pairing tooltip on dashboard view if a) a new watch was just paired and b) it has not yet been shown
     * for current user yet. When logging out and pairing a new device, the tool tip will be shown again.
     *
     * When actually showing a tool tip, this method returns a [ToolTipView] so that it can be dismissed. When there is
     * no need to show the tool tip, the returned value is null.
     */
    fun showPostPairingTooltipIfNeeded(activity: Activity, isEon: Boolean): ToolTipView? {
        try {
            val alreadyShown = ToolTipHelper.hasShownToolTip(
                activity,
                ToolTipHelper.KEY_FIND_YOUR_WATCH_HERE_TOOL_TIP
            )
            if (shouldShowTooltip(STTConstants.TooltipPreferences.KEY_SUUNTO_SHOULD_SHOW_POST_PAIRING_TOOLTIP) && !alreadyShown) {
                markToolTipAsShown(
                    activity,
                    ToolTipHelper.KEY_FIND_YOUR_WATCH_HERE_TOOL_TIP
                )
                return TooltipWrapper.showPostPairingTooltip(activity, isEon)
            }
        } catch (exception: Exception) {
            Timber.w(exception, "Show post-pairing tooltip failed")
        }

        return null
    }
}
