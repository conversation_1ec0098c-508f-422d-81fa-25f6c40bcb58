package com.stt.android.tooltips

import android.app.Activity
import android.view.View
import android.widget.FrameLayout
import androidx.annotation.StringRes
import com.github.xizzhu.simpletooltip.ToolTipView
import com.stt.android.R
import com.stt.android.ui.utils.ToolTipHelper

class TooltipWrapper {

    companion object {

        @JvmStatic
        fun showPostPairingTooltip(activity: Activity, isEon: Boolean): ToolTipView? {
            val targetView = activity.findViewById<View>(R.id.dashboardWatchContainer) ?: return null
            val parentLayout = activity.findViewById<FrameLayout>(R.id.tooltipContainer) ?: return null

            @StringRes val textRes = if (isEon) {
                R.string.dashboard_toolbar_eon_device_tooltip
            } else {
                R.string.dashboard_toolbar_watch_tooltip
            }

            return ToolTipHelper.createToolTipView(activity, targetView, parentLayout, textRes).apply {
                show()
            }
        }
    }
}
