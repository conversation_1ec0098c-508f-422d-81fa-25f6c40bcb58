package com.stt.android;

import android.bluetooth.BluetoothManager;
import android.content.Context;
import android.content.SharedPreferences;
import com.stt.android.help.BaseSupportHelper;
import com.stt.android.help.SupportHelper;
import com.stt.android.home.dashboardv2.ShowBuyPremiumTopBannerNoOp;
import com.stt.android.home.dashboardv2.premium.ShowBuyPremiumTopBanner;
import com.stt.android.home.diary.DiaryTabUtilModule;
import com.suunto.connectivity.Connectivity;
import com.suunto.connectivity.ScLib;
import com.suunto.connectivity.btscanner.SuuntoLeScanner;
import dagger.Binds;
import dagger.Module;
import dagger.Provides;
import javax.inject.Named;
import javax.inject.Singleton;

@Module(includes = { DiaryTabUtilModule.class })
public abstract class STTBrandFlavourModule {

    private static final String SUUNTO = "Suunto";

    @Singleton
    @Provides
    @Named(SUUNTO)
    public static SharedPreferences provideSuuntoSharedPrefs(Context context) {
        return context.getSharedPreferences(SUUNTO, Context.MODE_PRIVATE);
    }

    @Singleton
    @Provides
    public static ScLib provideScLibImpl(Context context) {
        return Connectivity.scLibBuilder(context).build();
    }

    @Singleton
    @Provides
    public static SuuntoLeScanner provideSuuntoLeScanner(Context context, BluetoothManager bluetoothManager) {
        return SuuntoLeScanner.withContext(context, bluetoothManager);
    }

    @Singleton
    @Provides
    public static BluetoothManager provideBluetoothManager(Context context) {
        return (BluetoothManager) context.getSystemService(Context.BLUETOOTH_SERVICE);
    }

    @Binds
    public abstract BaseSupportHelper bindSupportHelper(SupportHelper supportHelper);

    @Binds
    public abstract ShowBuyPremiumTopBanner bindShowBuyPremiumTopBanner(ShowBuyPremiumTopBannerNoOp showBuyPremiumTopBanner);
}
