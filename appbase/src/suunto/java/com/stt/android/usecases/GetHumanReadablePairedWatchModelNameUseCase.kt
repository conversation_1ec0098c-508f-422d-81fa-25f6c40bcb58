package com.stt.android.usecases

import android.content.SharedPreferences
import com.stt.android.di.SuuntoSharedPrefs
import com.stt.android.utils.STTConstants
import com.suunto.connectivity.suuntoconnectivity.device.AnalyticsDevicePropertyHelper
import javax.inject.Inject

class GetHumanReadablePairedWatchModelNameUseCase @Inject constructor(
    @SuuntoSharedPrefs private val sharedPreferences: SharedPreferences
) {
    /**
     * @return Public name of the watch, for example: Suunto Race
     */
    @Throws(NoPairedWatchModelFoundException::class)
    operator fun invoke(): String {
        val watchVariantName: String = sharedPreferences.getString(
            STTConstants.SuuntoPreferences.KEY_SUUNTO_PAIRED_WATCH_MODEL,
            null
        ) ?: throw NoPairedWatchModelFoundException()

        return AnalyticsDevicePropertyHelper.getWatchModelNameForVariantName(watchVariantName)
    }
}

class NoPairedWatchModelFoundException : Exception()
