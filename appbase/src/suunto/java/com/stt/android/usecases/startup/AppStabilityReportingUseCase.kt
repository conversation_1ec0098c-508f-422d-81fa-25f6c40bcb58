package com.stt.android.usecases.startup

import android.content.Context
import android.content.SharedPreferences
import androidx.core.content.edit
import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.analytics.AnalyticsEventProperty
import com.stt.android.analytics.EmarsysAnalytics
import com.stt.android.domain.di.IoThread
import com.stt.android.domain.di.MainThread
import com.stt.android.home.PREFERENCES_KEY_GPS_TRACKING_INTERRUPTED
import com.stt.android.location.LocationModel
import com.stt.android.watch.MissingCurrentWatchException
import com.stt.android.watch.SuuntoWatchModel
import com.suunto.connectivity.repository.commands.ServiceStabilityResponse
import io.reactivex.Scheduler
import io.reactivex.Single
import timber.log.Timber
import javax.inject.Inject

class AppStabilityReportingUseCase
@Inject constructor(
    emarsysAnalytics: EmarsysAnalytics,
    amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker,
    @IoThread private val ioThread: Scheduler,
    @MainThread mainThread: Scheduler,
    private val sharedPreferences: SharedPreferences,
    private val suuntoWatchModel: SuuntoWatchModel,
    context: Context
) : AppStabilityReportingUseCaseBase(
    emarsysAnalytics,
    amplitudeAnalyticsTracker,
    ioThread,
    mainThread,
    sharedPreferences,
    context
) {

    override fun stabilityProperties(): Single<MutableMap<String, Any>> {
        val gpsStopped = sharedPreferences.getBoolean(LocationModel.GPS_ACTIVE, false)
        return suuntoWatchModel.getServiceStability()
            .map { response: ServiceStabilityResponse ->
                val properties: MutableMap<String, Any> = HashMap()
                properties[AnalyticsEventProperty.CONNNECTED_GPS_TRACKING_INTERRUPTED] =
                    response.latestAssistedGpsTrackingFailed
                val startInfo = response.serviceStartInformation
                if (startInfo != null && startInfo.restartCounter > 1) {
                    val averageStartDelta = (
                        startInfo.latestStartTime - startInfo
                            .firstStartTime
                        ) /
                        startInfo.restartCounter
                    properties[AnalyticsEventProperty.CONNECTIVITY_RESTART_DELTA_MS] =
                        averageStartDelta
                    properties[AnalyticsEventProperty.CONNECTIVITY_RESTART_COUNT] =
                        startInfo.restartCounter
                }
                sharedPreferences.edit {
                    putBoolean(
                        PREFERENCES_KEY_GPS_TRACKING_INTERRUPTED,
                        gpsStopped || response.latestAssistedGpsTrackingFailed
                    )
                }
                properties
            }
            .onErrorReturn { throwable ->
                if (throwable is MissingCurrentWatchException) {
                    Timber.i("Missing current watch")
                } else {
                    Timber.w(throwable, "Error in getServiceStability")
                }
                val properties: MutableMap<String, Any> = HashMap()
                properties[AnalyticsEventProperty.CONNNECTED_GPS_TRACKING_INTERRUPTED] = false
                sharedPreferences.edit {
                    putBoolean(
                        PREFERENCES_KEY_GPS_TRACKING_INTERRUPTED,
                        gpsStopped
                    )
                }
                properties
            }
            .zipWith(super.stabilityProperties()) { suuntoProperties, stProperties ->
                stProperties.putAll(suuntoProperties)
                stProperties
            }
    }
}
