package com.stt.android.systemwidget

import android.app.Application
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.work.WorkManager
import com.stt.android.di.initializer.AppInitializer
import com.stt.android.watch.background.SyncResultJobDispatcher
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class Suunto247SystemWidgetDataChangeListener @Inject constructor(
    private val localBroadcastManager: LocalBroadcastManager,
    private val workManager: WorkManager
) : AppInitializer {
    override fun init(app: Application) {
        localBroadcastManager.registerReceiver(
            object : BroadcastReceiver() {
                override fun onReceive(context: Context, intent: Intent) {
                    when (intent.action) {
                        SyncResultJobDispatcher.RECOVERY_DATA_LOCALLY_SYNCED -> {
                            FetchDailyRecoveryAndScheduleWidgetUpdateJob.enqueueIfWidgetsAdded(
                                context,
                                workManager
                            )
                        }
                        SyncResultJobDispatcher.SLEEP_DATA_LOCALLY_SYNCED -> {
                            SleepDashboardWidgetAsSystemWidgetProvider.sendUpdateBroadcast(context)
                        }
                        SyncResultJobDispatcher.TREND_DATA_LOCALLY_SYNCED -> {
                            FetchDailyEnergyAndScheduleWidgetUpdateJob.enqueueIfWidgetsAdded(
                                context,
                                workManager
                            )
                            FetchDailyStepsAndScheduleWidgetUpdateJob.enqueueIfWidgetsAdded(
                                context,
                                workManager
                            )
                            MinimumHeartRateDashboardWidgetAsSystemWidgetProvider.sendUpdateBroadcast(context)
                        }
                    }
                }
            },
            IntentFilter().apply {
                addAction(SyncResultJobDispatcher.RECOVERY_DATA_LOCALLY_SYNCED)
                addAction(SyncResultJobDispatcher.SLEEP_DATA_LOCALLY_SYNCED)
                addAction(SyncResultJobDispatcher.TREND_DATA_LOCALLY_SYNCED)
            }
        )
    }
}
