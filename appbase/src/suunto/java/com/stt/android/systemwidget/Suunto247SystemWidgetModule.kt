package com.stt.android.systemwidget

import com.stt.android.backgroundwork.CoroutineWorkerAssistedFactory
import com.stt.android.backgroundwork.WorkerKey
import com.stt.android.di.initializer.AppInitializer
import dagger.Binds
import dagger.Module
import dagger.multibindings.IntoMap
import dagger.multibindings.IntoSet

@Module
abstract class Suunto247SystemWidgetModule {
    @Binds
    @IntoMap
    @WorkerKey(FetchDailyEnergyAndScheduleWidgetUpdateJob::class)
    abstract fun bindFetchDailyEnergyAndScheduleWidgetUpdateJob(factory: FetchDailyEnergyAndScheduleWidgetUpdateJob.Factory): CoroutineWorkerAssistedFactory

    @Binds
    @IntoMap
    @WorkerKey(FetchDailyRecoveryAndScheduleWidgetUpdateJob::class)
    abstract fun bindFetchDailyRecoveryAndScheduleWidgetUpdateJob(factory: FetchDailyRecoveryAndScheduleWidgetUpdateJob.Factory): CoroutineWorkerAssistedFactory

    @Binds
    @IntoMap
    @WorkerKey(FetchDailyStepsAndScheduleWidgetUpdateJob::class)
    abstract fun bindFetchDailyStepsAndScheduleWidgetUpdateJob(factory: FetchDailyStepsAndScheduleWidgetUpdateJob.Factory): CoroutineWorkerAssistedFactory

    @Binds
    @IntoSet
    abstract fun bindSuunto247SystemWidgetDataChangeListener(suunto247SystemWidgetDataChangeListener: Suunto247SystemWidgetDataChangeListener): AppInitializer
}
