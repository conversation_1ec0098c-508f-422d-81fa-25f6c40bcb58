package com.stt.android.systemwidget

import android.content.Context
import androidx.work.CoroutineWorker
import androidx.work.ExistingPeriodicWorkPolicy
import androidx.work.ExistingWorkPolicy
import androidx.work.ListenableWorker
import androidx.work.OneTimeWorkRequest
import androidx.work.PeriodicWorkRequestBuilder
import androidx.work.WorkManager
import androidx.work.WorkerParameters
import com.stt.android.backgroundwork.CoroutineWorkerAssistedFactory
import com.stt.android.coroutines.runSuspendCatchingWithTimeout
import com.stt.android.domain.activitydata.dailyvalues.FetchDailyEnergyUseCase
import com.stt.android.domain.activitydata.dailyvalues.FetchDailyRecoveryDataUseCase
import com.stt.android.domain.activitydata.dailyvalues.FetchDailyStepsUseCase
import kotlinx.coroutines.TimeoutCancellationException
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.flow.take
import timber.log.Timber
import java.util.concurrent.TimeUnit
import javax.inject.Inject

class FetchDailyEnergyAndScheduleWidgetUpdateJob(
    context: Context,
    params: WorkerParameters,
    private val fetchDailyEnergyUseCase: FetchDailyEnergyUseCase
) : CoroutineWorker(context, params) {
    override suspend fun doWork(): Result {
        // UseCase returns a cached value and then checks the watch,
        // wait 30s for potential update from watch and then tell the widgets to update
        // knowing that the cache has a up-to-date value
        // Update the widgets even on error as the cached value or the non-daily data might
        // still be newer than what is currently shown in the widgets
        runSuspendCatchingWithTimeout(30_000) {
            fetchDailyEnergyUseCase
                .fetchEnergy()
                .take(2)
                .collect()
        }.onFailure { e ->
            if (e is TimeoutCancellationException) {
                Timber.d(e, "Timed out fetching daily energy data from watch for system widgets")
            } else {
                Timber.w(e, "Error fetching daily energy data from watch for system widgets")
            }
        }

        if (!isStopped) {
            CaloriesDashboardWidgetAsSystemWidgetProvider.sendUpdateBroadcast(applicationContext)
        }

        return Result.success()
    }

    class Factory @Inject constructor(
        private val fetchDailyEnergyUseCase: FetchDailyEnergyUseCase
    ) : CoroutineWorkerAssistedFactory {
        override fun create(context: Context, params: WorkerParameters): ListenableWorker {
            return FetchDailyEnergyAndScheduleWidgetUpdateJob(
                context = context,
                params = params,
                fetchDailyEnergyUseCase = fetchDailyEnergyUseCase,
            )
        }
    }

    companion object {
        private const val WORK_NAME = "FetchDailyEnergyAndScheduleWidgetUpdateJob"
        private const val PERIODIC_WORK_NAME = "FetchDailyEnergyAndScheduleWidgetUpdateJob_Periodic"

        fun enqueueIfWidgetsAdded(context: Context, workManager: WorkManager) {
            if (CaloriesDashboardWidgetAsSystemWidgetProvider.widgetsAdded(context)) {
                workManager.enqueueUniqueWork(
                    WORK_NAME,
                    ExistingWorkPolicy.REPLACE,
                    OneTimeWorkRequest.from(FetchDailyEnergyAndScheduleWidgetUpdateJob::class.java)
                )
            }
        }

        fun enqueuePeriodic(workManager: WorkManager) {
            workManager.enqueueUniquePeriodicWork(
                PERIODIC_WORK_NAME,
                ExistingPeriodicWorkPolicy.KEEP,
                PeriodicWorkRequestBuilder<FetchDailyEnergyAndScheduleWidgetUpdateJob>(
                    repeatInterval = 60L,
                    repeatIntervalTimeUnit = TimeUnit.MINUTES,
                    flexTimeInterval = 30L,
                    flexTimeIntervalUnit = TimeUnit.MINUTES
                )
                    .setInitialDelay(30L, TimeUnit.MINUTES)
                    .build()
            )
        }

        fun cancelPeriodic(workManager: WorkManager) {
            workManager.cancelUniqueWork(PERIODIC_WORK_NAME)
        }
    }
}

class FetchDailyRecoveryAndScheduleWidgetUpdateJob(
    context: Context,
    params: WorkerParameters,
    private val fetchDailyRecoveryDataUseCase: FetchDailyRecoveryDataUseCase
) : CoroutineWorker(context, params) {
    override suspend fun doWork(): Result {
        // UseCase returns a cached value and then checks the watch,
        // wait 30s for potential update from watch and then tell the widgets to update
        // knowing that the cache has a up-to-date value
        // Update the widgets even on error as the cached value or the non-daily data might
        // still be newer than what is currently shown in the widgets
        runSuspendCatchingWithTimeout(30_000) {
            fetchDailyRecoveryDataUseCase
                .fetchCurrentBalance(-1f)
                .take(2)
                .collect()
        }.onFailure { e ->
            if (e is TimeoutCancellationException) {
                Timber.d(e, "Timed out fetching daily recovery data from watch for system widgets")
            } else {
                Timber.w(e, "Error fetching daily recovery data from watch for system widgets")
            }
        }

        if (!isStopped) {
            ResourcesDashboardWidgetAsSystemWidgetProvider.sendUpdateBroadcast(applicationContext)
        }

        return Result.success()
    }

    class Factory @Inject constructor(
        private val fetchDailyRecoveryDataUseCase: FetchDailyRecoveryDataUseCase
    ) : CoroutineWorkerAssistedFactory {
        override fun create(context: Context, params: WorkerParameters): ListenableWorker {
            return FetchDailyRecoveryAndScheduleWidgetUpdateJob(
                context = context,
                params = params,
                fetchDailyRecoveryDataUseCase = fetchDailyRecoveryDataUseCase,
            )
        }
    }

    companion object {
        private const val WORK_NAME = "FetchDailyRecoveryAndScheduleWidgetUpdateJob"
        private const val PERIODIC_WORK_NAME =
            "FetchDailyRecoveryAndScheduleWidgetUpdateJob_Periodic"

        fun enqueueIfWidgetsAdded(context: Context, workManager: WorkManager) {
            if (ResourcesDashboardWidgetAsSystemWidgetProvider.widgetsAdded(context)) {
                workManager.enqueueUniqueWork(
                    WORK_NAME,
                    ExistingWorkPolicy.REPLACE,
                    OneTimeWorkRequest.from(FetchDailyRecoveryAndScheduleWidgetUpdateJob::class.java)
                )
            }
        }

        fun enqueuePeriodic(workManager: WorkManager) {
            workManager.enqueueUniquePeriodicWork(
                PERIODIC_WORK_NAME,
                ExistingPeriodicWorkPolicy.KEEP,
                PeriodicWorkRequestBuilder<FetchDailyRecoveryAndScheduleWidgetUpdateJob>(
                    repeatInterval = 60L,
                    repeatIntervalTimeUnit = TimeUnit.MINUTES,
                    flexTimeInterval = 30L,
                    flexTimeIntervalUnit = TimeUnit.MINUTES
                )
                    .setInitialDelay(30L, TimeUnit.MINUTES)
                    .build()
            )
        }

        fun cancelPeriodic(workManager: WorkManager) {
            workManager.cancelUniqueWork(PERIODIC_WORK_NAME)
        }
    }
}

class FetchDailyStepsAndScheduleWidgetUpdateJob(
    context: Context,
    params: WorkerParameters,
    private val fetchDailyStepsUseCase: FetchDailyStepsUseCase
) : CoroutineWorker(context, params) {
    override suspend fun doWork(): Result {
        // UseCase returns a cached value and then checks the watch,
        // wait 30s for potential update from watch and then tell the widgets to update
        // knowing that the cache has a up-to-date value
        // Update the widgets even on error as the cached value or the non-daily data might
        // still be newer than what is currently shown in the widgets
        runSuspendCatchingWithTimeout(30_000) {
            fetchDailyStepsUseCase
                .fetchSteps()
                .take(2)
                .collect()
        }.onFailure { e ->
            if (e is TimeoutCancellationException) {
                Timber.d(e, "Timed out fetching daily steps data from watch for system widgets")
            } else {
                Timber.w(e, "Error fetching daily steps data from watch for system widgets")
            }
        }

        if (!isStopped) {
            StepsDashboardWidgetAsSystemWidgetProvider.sendUpdateBroadcast(applicationContext)
        }

        return Result.success()
    }

    class Factory @Inject constructor(
        private val fetchDailyStepsUseCase: FetchDailyStepsUseCase
    ) : CoroutineWorkerAssistedFactory {
        override fun create(context: Context, params: WorkerParameters): ListenableWorker {
            return FetchDailyStepsAndScheduleWidgetUpdateJob(
                context = context,
                params = params,
                fetchDailyStepsUseCase = fetchDailyStepsUseCase,
            )
        }
    }

    companion object {
        private const val WORK_NAME = "FetchDailyStepsAndScheduleWidgetUpdateJob"
        private const val PERIODIC_WORK_NAME = "FetchDailyStepsAndScheduleWidgetUpdateJob_Periodic"

        fun enqueueIfWidgetsAdded(context: Context, workManager: WorkManager) {
            if (StepsDashboardWidgetAsSystemWidgetProvider.widgetsAdded(context)) {
                workManager.enqueueUniqueWork(
                    WORK_NAME,
                    ExistingWorkPolicy.REPLACE,
                    OneTimeWorkRequest.from(FetchDailyStepsAndScheduleWidgetUpdateJob::class.java)
                )
            }
        }

        fun enqueuePeriodic(workManager: WorkManager) {
            workManager.enqueueUniquePeriodicWork(
                PERIODIC_WORK_NAME,
                ExistingPeriodicWorkPolicy.KEEP,
                PeriodicWorkRequestBuilder<FetchDailyStepsAndScheduleWidgetUpdateJob>(
                    repeatInterval = 60L,
                    repeatIntervalTimeUnit = TimeUnit.MINUTES,
                    flexTimeInterval = 30L,
                    flexTimeIntervalUnit = TimeUnit.MINUTES
                )
                    .setInitialDelay(30L, TimeUnit.MINUTES)
                    .build()
            )
        }

        fun cancelPeriodic(workManager: WorkManager) {
            workManager.cancelUniqueWork(PERIODIC_WORK_NAME)
        }
    }
}
