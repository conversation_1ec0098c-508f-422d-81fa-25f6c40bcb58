package com.stt.android.systemwidget

import android.appwidget.AppWidgetManager
import android.content.ComponentName
import android.content.Context
import android.util.Size
import androidx.work.WorkManager
import com.stt.android.coroutines.nullOnError
import com.stt.android.home.dashboard.widget.suunto247.CaloriesWidget
import com.stt.android.home.dashboard.widget.suunto247.CaloriesWidgetData
import com.stt.android.home.dashboard.widget.suunto247.CaloriesWidgetDataFetcher
import com.stt.android.home.dashboard.widget.suunto247.MinimumHeartRateWidget
import com.stt.android.home.dashboard.widget.suunto247.MinimumHeartRateWidgetData
import com.stt.android.home.dashboard.widget.suunto247.MinimumHeartRateWidgetDataFetcher
import com.stt.android.home.dashboard.widget.suunto247.ResourcesWidget
import com.stt.android.home.dashboard.widget.suunto247.ResourcesWidgetData
import com.stt.android.home.dashboard.widget.suunto247.ResourcesWidgetDataFetcher
import com.stt.android.home.dashboard.widget.suunto247.SleepWidget
import com.stt.android.home.dashboard.widget.suunto247.SleepWidgetData
import com.stt.android.home.dashboard.widget.suunto247.SleepWidgetDataFetcher
import com.stt.android.home.dashboard.widget.suunto247.StepsWidget
import com.stt.android.home.dashboard.widget.suunto247.StepsWidgetData
import com.stt.android.home.dashboard.widget.suunto247.StepsWidgetDataFetcher
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.flow.first
import java.time.LocalDate
import javax.inject.Inject
import javax.inject.Provider

@AndroidEntryPoint
class CaloriesDashboardWidgetAsSystemWidgetProvider :
    DashboardWidgetAsSystemWidgetProvider<CaloriesWidgetData>() {
    @Inject
    lateinit var caloriesWidgetDataFetcher: CaloriesWidgetDataFetcher

    @Inject
    lateinit var workManagerProvider: Provider<WorkManager>

    override val widgetType: SystemWidgetType = SystemWidgetType.CALORIES

    override suspend fun fetchWidgetData() = caloriesWidgetDataFetcher
        .caloriesWidgetDataFlow(LocalDate.now())
        .nullOnError()
        .first()

    override fun createWidgetView(
        widgetData: CaloriesWidgetData?,
        size: Size,
        context: Context
    ) = CaloriesWidget(context).apply {
        postInit()
        data = widgetData
        displayedAsEnabled = true
        customizationModeEnabled = false
        showRemoveButton = false
        val heightMode = getHeightMode(size.height, context)
        setHideDescriptionText(heightMode.ordinal < HeightMode.FULL.ordinal)
        setHideBars(heightMode == HeightMode.MINIMAL)
        setHeightKeepingBarchartProportionalHeight(size.height)
        setWidth(size.width)
        bindProps()
        executePendingBindings()
    }

    override fun onEnabled(context: Context) {
        FetchDailyEnergyAndScheduleWidgetUpdateJob.enqueuePeriodic(workManagerProvider.get())
    }

    override fun onDisabled(context: Context) {
        FetchDailyEnergyAndScheduleWidgetUpdateJob.cancelPeriodic(workManagerProvider.get())
    }

    companion object {
        fun sendUpdateBroadcast(context: Context) {
            sendUpdateBroadcast(context, CaloriesDashboardWidgetAsSystemWidgetProvider::class)
        }

        fun widgetsAdded(context: Context): Boolean {
            val appWidgetManager =
                context.getSystemService(Context.APPWIDGET_SERVICE) as AppWidgetManager
            val widgetIds = appWidgetManager.getAppWidgetIds(
                ComponentName(
                    context,
                    CaloriesDashboardWidgetAsSystemWidgetProvider::class.java
                )
            )
            return widgetIds.isNotEmpty()
        }
    }
}

@AndroidEntryPoint
class ResourcesDashboardWidgetAsSystemWidgetProvider :
    DashboardWidgetAsSystemWidgetProvider<ResourcesWidgetData>() {
    @Inject
    lateinit var resourcesWidgetDataFetcher: ResourcesWidgetDataFetcher

    @Inject
    lateinit var workManagerProvider: Provider<WorkManager>

    override val widgetType: SystemWidgetType = SystemWidgetType.RESOURCES

    override suspend fun fetchWidgetData() = resourcesWidgetDataFetcher
        .resourcesWidgetDataFlow(LocalDate.now()).nullOnError().first()

    override fun createWidgetView(
        widgetData: ResourcesWidgetData?,
        size: Size,
        context: Context
    ) = ResourcesWidget(context).apply {
        postInit()
        data = widgetData
        displayedAsEnabled = true
        customizationModeEnabled = false
        showRemoveButton = false
        val heightMode = getHeightMode(size.height, context)
        setHideDescriptionText(heightMode.ordinal < HeightMode.FULL.ordinal)
        setHideBars(heightMode == HeightMode.MINIMAL)
        setHeightKeepingBarchartProportionalHeight(size.height)
        setWidth(size.width)
        bindProps()
        executePendingBindings()
    }

    override fun onEnabled(context: Context) {
        FetchDailyRecoveryAndScheduleWidgetUpdateJob.enqueuePeriodic(workManagerProvider.get())
    }

    override fun onDisabled(context: Context) {
        FetchDailyRecoveryAndScheduleWidgetUpdateJob.cancelPeriodic(workManagerProvider.get())
    }

    companion object {
        fun sendUpdateBroadcast(context: Context) {
            sendUpdateBroadcast(context, ResourcesDashboardWidgetAsSystemWidgetProvider::class)
        }

        fun widgetsAdded(context: Context): Boolean {
            val appWidgetManager =
                context.getSystemService(Context.APPWIDGET_SERVICE) as AppWidgetManager
            val widgetIds = appWidgetManager.getAppWidgetIds(
                ComponentName(
                    context,
                    ResourcesDashboardWidgetAsSystemWidgetProvider::class.java
                )
            )
            return widgetIds.isNotEmpty()
        }
    }
}

@AndroidEntryPoint
class SleepDashboardWidgetAsSystemWidgetProvider :
    DashboardWidgetAsSystemWidgetProvider<SleepWidgetData>() {
    @Inject
    lateinit var sleepWidgetDataFetcher: SleepWidgetDataFetcher

    override val widgetType: SystemWidgetType = SystemWidgetType.SLEEP

    override suspend fun fetchWidgetData() = sleepWidgetDataFetcher
        .sleepWidgetDataFlow(LocalDate.now()).nullOnError().first()

    override fun createWidgetView(
        widgetData: SleepWidgetData?,
        size: Size,
        context: Context
    ) = SleepWidget(context).apply {
        postInit()
        data = widgetData
        displayedAsEnabled = true
        customizationModeEnabled = false
        showRemoveButton = false
        val heightMode = getHeightMode(size.height, context)
        setHideDescriptionText(heightMode.ordinal < HeightMode.FULL.ordinal)
        setHideBars(heightMode == HeightMode.MINIMAL)
        setHeightKeepingBarchartProportionalHeight(size.height)
        setWidth(size.width)
        bindProps()
        executePendingBindings()
    }

    companion object {
        fun sendUpdateBroadcast(context: Context) {
            sendUpdateBroadcast(context, SleepDashboardWidgetAsSystemWidgetProvider::class)
        }
    }
}

@AndroidEntryPoint
class StepsDashboardWidgetAsSystemWidgetProvider :
    DashboardWidgetAsSystemWidgetProvider<StepsWidgetData>() {
    @Inject
    lateinit var stepsWidgetDataFetcher: StepsWidgetDataFetcher

    @Inject
    lateinit var workManagerProvider: Provider<WorkManager>

    override val widgetType: SystemWidgetType = SystemWidgetType.STEPS

    override suspend fun fetchWidgetData() = stepsWidgetDataFetcher
        .stepsWidgetDataFlow(LocalDate.now()).nullOnError().first()

    override fun createWidgetView(
        widgetData: StepsWidgetData?,
        size: Size,
        context: Context
    ) = StepsWidget(context).apply {
        postInit()
        data = widgetData
        displayedAsEnabled = true
        customizationModeEnabled = false
        showRemoveButton = false
        val heightMode = getHeightMode(size.height, context)
        setHideDescriptionText(heightMode.ordinal < HeightMode.FULL.ordinal)
        setHideBars(heightMode == HeightMode.MINIMAL)
        setHeightKeepingBarchartProportionalHeight(size.height)
        setWidth(size.width)
        bindProps()
        executePendingBindings()
    }

    override fun onEnabled(context: Context) {
        FetchDailyStepsAndScheduleWidgetUpdateJob.enqueuePeriodic(workManagerProvider.get())
    }

    override fun onDisabled(context: Context) {
        FetchDailyStepsAndScheduleWidgetUpdateJob.cancelPeriodic(workManagerProvider.get())
    }

    companion object {
        fun sendUpdateBroadcast(context: Context) {
            sendUpdateBroadcast(context, StepsDashboardWidgetAsSystemWidgetProvider::class)
        }

        fun widgetsAdded(context: Context): Boolean {
            val appWidgetManager =
                context.getSystemService(Context.APPWIDGET_SERVICE) as AppWidgetManager
            val widgetIds = appWidgetManager.getAppWidgetIds(
                ComponentName(
                    context,
                    StepsDashboardWidgetAsSystemWidgetProvider::class.java
                )
            )
            return widgetIds.isNotEmpty()
        }
    }
}

@AndroidEntryPoint
class MinimumHeartRateDashboardWidgetAsSystemWidgetProvider :
    DashboardWidgetAsSystemWidgetProvider<MinimumHeartRateWidgetData>() {
    @Inject
    lateinit var minimumHeartRateWidgetDataFetcher: MinimumHeartRateWidgetDataFetcher

    @Inject
    lateinit var workManagerProvider: Provider<WorkManager>

    override val widgetType: SystemWidgetType = SystemWidgetType.MINIMUM_HEART_RATE

    override suspend fun fetchWidgetData() = minimumHeartRateWidgetDataFetcher
        .minimumHeartRateWidgetDataFlow(LocalDate.now()).nullOnError().first()

    override fun createWidgetView(
        widgetData: MinimumHeartRateWidgetData?,
        size: Size,
        context: Context
    ) = MinimumHeartRateWidget(context).apply {
        data = widgetData
        displayedAsEnabled = true
        customizationModeEnabled = false
        showRemoveButton = false
        val heightMode = getHeightMode(size.height, context)
        setHideDescriptionText(heightMode.ordinal < HeightMode.FULL.ordinal)
        setHideChart(heightMode == HeightMode.MINIMAL)
        setHeightKeepingChartProportionalHeight(size.height)
        setWidth(size.width)
        bindData()
        executePendingBindings()
    }

    companion object {
        fun sendUpdateBroadcast(context: Context) {
            sendUpdateBroadcast(context, MinimumHeartRateDashboardWidgetAsSystemWidgetProvider::class)
        }
    }
}
