package com.stt.android.premium

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier

// No-op in Suunto, see PremiumRequiredToAccessHandlerImpl.kt in ST flavor for
// proper implementation.
@Composable
fun BuyPremiumToGetAccess(
    description: String,
    premiumPromotionNavigator: PremiumPromotionNavigator,
    modifier: Modifier = Modifier,
    onCloseClickListener: () -> Unit = { },
    closeFromButton: Boolean = false,
    closeFromTouchOutside: Boolean = false
) {
    // Do nothing
}
