package com.stt.android.premium.featurepromotion

import androidx.compose.foundation.layout.BoxWithConstraintsScope
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.Dp

@Composable
fun PremiumPromotionDialog(
    openFeaturePromotion: () -> Unit,
    modifier: Modifier = Modifier,
    dismissDialog: (() -> Unit)? = null,
    content: @Composable ColumnScope.(scope: BoxWithConstraintsScope, mainTextMargin: Dp) -> Unit
) {

}
