package com.stt.android.common.recyclerview

import androidx.annotation.VisibleForTesting
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import timber.log.Timber

/**
 * A [RecyclerView] listener that intercepts scroll and triggers a callback whenever
 * we need to load more data.
 * @param threshold The amount of items before the end of the list that will trigger loading more data
 */
abstract class OnLoadMoreScrollListener
@JvmOverloads constructor(private val threshold: Int = 0) : RecyclerView.OnScrollListener() {
    private var loading = false
    private var totalCountBeforeLoad = 0

    override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
        val layoutManager = recyclerView.layoutManager as LinearLayoutManager
        val totalCount = layoutManager.itemCount
        val lastVisibleItemPosition = layoutManager.findLastVisibleItemPosition()

        loadMoreIfNeeded(lastVisibleItemPosition, totalCount)
    }

    @Suppress("MemberVisibilityCanBePrivate")
    @VisibleForTesting(otherwise = VisibleForTesting.PRIVATE)
    internal fun loadMoreIfNeeded(lastVisibleItemPosition: Int, totalCount: Int) {
        if (!loading && hasReachedListBottomThreshold(lastVisibleItemPosition, totalCount)) {
            loading = true
            totalCountBeforeLoad = totalCount
            Timber.v("Loading more...")
            onLoadMore()
        } else if (hasLoadedMore(totalCount)) {
            loading = false
        }
    }

    private fun hasLoadedMore(totalCount: Int) = loading && totalCount > totalCountBeforeLoad

    private fun hasReachedListBottomThreshold(lastVisibleItemPosition: Int, totalCount: Int) =
        lastVisibleItemPosition == totalCount - threshold - 1

    protected abstract fun onLoadMore()
}
