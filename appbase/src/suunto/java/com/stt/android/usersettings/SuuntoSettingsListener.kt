package com.stt.android.usersettings

import android.app.Application
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.UserSettingsController
import com.stt.android.coroutines.LoggingExceptionHandler
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.di.initializer.AppInitializer
import com.stt.android.domain.user.UserSettings
import com.stt.android.home.dashboardnew.weeklygoal.SyncWeeklyGoalWithWatchUseCase
import com.stt.android.watch.MissingCurrentWatchException
import com.stt.android.watch.SuuntoWatchModel
import dagger.Lazy
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * TODO this is not a great approach, user settings should be refactored to be stored in a room DB
 */
@Singleton
class SuuntoSettingsListener @Inject constructor(
    private val userSettingsController: UserSettingsController,
    private val suuntoWatchModel: Lazy<SuuntoWatchModel>,
    private val syncWeeklyGoalWithWatchUseCase: SyncWeeklyGoalWithWatchUseCase,
    private val coroutinesDispatchers: CoroutinesDispatchers,
) : AppInitializer, UserSettingsController.UpdateListener {
    private val scope = CoroutineScope(coroutinesDispatchers.main + SupervisorJob() + LoggingExceptionHandler)

    override fun init(app: Application) {
        userSettingsController.addUpdateListener(this)
        checkDefaultPredefinedReplies(app)
        syncWatchAndAppWeeklyWorkoutTarget()
    }

    private fun checkDefaultPredefinedReplies(app: Application) {
        scope.launch(coroutinesDispatchers.io) {
            // let's delay a bit to make sure all AppInitializers are correctly initialised
            delay(500)
            val predefinedReplies = userSettingsController.settings.predefinedReplies
            if (predefinedReplies.isNullOrEmpty()) {
                val newSettings = userSettingsController.settings.setPredefinedReplies(
                    UserSettings.getDefaultPredefinedAnswers(app)
                )
                userSettingsController.storeSettings(newSettings)
            }
        }
    }

    override fun onSettingsStoredToPreferences(didLocalChanges: Boolean) {
        scope.launch(coroutinesDispatchers.io) {
            runSuspendCatching {
                suuntoWatchModel.get().saveUserSettings(
                    userSettingsController.settings.toDataSourceEntity()
                )
            }.onFailure {
                if (it is MissingCurrentWatchException) {
                    Timber.i("Missing current watch")
                } else {
                    Timber.w(it, "Error sending user settings to connectivity process")
                }
            }
        }
    }

    private fun syncWatchAndAppWeeklyWorkoutTarget() {
        syncWeeklyGoalWithWatchUseCase.syncWeeklyGoalBetweenWatchAndSharedPrefs(scope)
    }
}
