package com.stt.android.analytics

import com.stt.android.extensions.capitalize
import java.util.Locale

internal fun sendConnectedServiceConnectionSuccessfulEvent(
    serviceName: String,
    autoSelected: Boolean,
    emarsysAnalytics: EmarsysAnalytics,
    amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker
) {
    val brazeEvent = AnalyticsEvent.SUUNTO_CONNECTED_SERVICE_CONNECTION_SUCCESSFUL

    // amplitude event is different because backend sends it already when connection is successful
    // but we want to make sure we track the autoSelect
    val amplitudeEvent = AnalyticsEvent.SUUNTO_CONNECTED_SERVICE_CONNECTION_SUCCESSFUL_ON_CLIENT

    val properties = AnalyticsProperties().put(
        AnalyticsEventProperty.SUUNTO_CONNECTED_SERVICE,
        serviceNameForAnalytics(serviceName)
    )
    if (autoSelected) {
        properties.put(
            AnalyticsEventProperty.SOURCE,
            AnalyticsPropertyValue.PartnerConnectionFlowSource.DEEPLINK
        )
    }

    emarsysAnalytics.trackEventWithProperties(brazeEvent, properties.map)
    amplitudeAnalyticsTracker.trackEvent(amplitudeEvent, properties)
}

internal fun sendConnectedServiceConnectionDisconnectedBrazeEvent(
    serviceName: String,
    emarsysAnalytics: EmarsysAnalytics
) {
    val event = AnalyticsEvent.SUUNTO_CONNECTED_SERVICE_CONNECTION_DISCONNECTED
    val properties = AnalyticsProperties().put(
        AnalyticsEventProperty.SUUNTO_CONNECTED_SERVICE,
        serviceNameForAnalytics(serviceName)
    )

    emarsysAnalytics.trackEventWithProperties(event, properties.map)
}

internal fun serviceNameForAnalytics(serviceName: String): String {
    // Analytics expects service names where only the first letter is capitalized.
    return serviceName.lowercase(Locale.US).capitalize(Locale.US)
}
