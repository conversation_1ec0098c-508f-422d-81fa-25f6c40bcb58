package com.stt.android.workouts.sharepreview

import android.app.Activity
import android.content.Context
import android.net.Uri
import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.analytics.EmarsysAnalytics
import com.stt.android.analytics.FirebaseAnalyticsTracker
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.multimedia.MediaStoreUtils
import com.stt.android.multimedia.sportie.SportieSelection
import com.stt.android.multimedia.sportie.SportieShareSource
import com.stt.android.multimedia.sportie.SportieShareType
import com.stt.android.workouts.sharepreview.customshare.ShareTarget
import com.stt.android.workouts.sharepreview.customshare.WorkoutShareHelper
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import timber.log.Timber
import java.io.FileOutputStream
import java.util.UUID
import javax.inject.Inject
import kotlin.coroutines.CoroutineContext

open class WorkoutShareHelperImpl
@Inject constructor(
    override val emarsysAnalytics: EmarsysAnalytics,
    override val firebaseAnalyticsTracker: FirebaseAnalyticsTracker,
    override val amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker,
    protected val dispatchers: CoroutinesDispatchers
) : WorkoutShareHelper, CoroutineScope {

    override val coroutineContext: CoroutineContext = dispatchers.io + SupervisorJob()

    override fun hasCustomIntentHandling(): Boolean = false
    override fun getCustomShareLinkTargets(context: Context): List<ShareTarget> = listOf()

    override fun getCustomShareImageTargets(context: Context): List<ShareTarget> = listOf(ShareTarget.DelegateToOS)

    override fun getCustomShareVideoTargets(context: Context): List<ShareTarget> = listOf(ShareTarget.DelegateToOS)

    override fun shareLinkToCustomTarget(
        activity: Activity,
        customTarget: ShareTarget.CustomTarget,
        workoutHeader: WorkoutHeader,
        source: SportieShareSource
    ) {
        // Do nothing
    }

    override fun shareImageToCustomTarget(
        activity: Activity,
        customTarget: ShareTarget.CustomTarget,
        header: WorkoutHeader,
        imageUri: Uri,
        sportieSelection: SportieSelection
    ) {

    }

    override fun shareVideoToCustomTarget(
        activity: Activity,
        customTarget: ShareTarget.CustomTarget,
        videoUri: Uri,
        shareType: SportieShareType,
    ) {
        // Do nothing
    }

    override fun shareImageToOS(
        activity: Activity,
        workoutHeader: WorkoutHeader,
        imageUri: Uri,
        sportieSelection: SportieSelection,
        numPhotosAdded: Int
    ) {
        launch {
            try {
                // share to os need save image to media, otherwise share can't work
                val mediaImageUri = saveToMedia(activity, imageUri)
                sendImplicitImageShareIntent(
                    activity,
                    workoutHeader,
                    mediaImageUri,
                    sportieSelection,
                    numPhotosAdded
                )
            } catch (e: Exception) {
                Timber.w(e, "share image to OS fail: ${e.message}")
            }
        }
    }

    override fun shareVideoToOS(activity: Activity, videoUri: Uri, shareType: SportieShareType) {
        launch {
            sendImplicitVideoShareIntent(activity, videoUri, shareType)
        }
    }

    override fun saveToPhotoAlbum(context: Context, imageUri: Uri) {
        launch {
            try {
                saveToMedia(context, imageUri)
            } catch (e: Exception) {
                Timber.w(e, "save to picture fail: ${e.message}")
            }
        }
    }

    private fun saveToMedia(context: Context, imageUri: Uri): Uri {
        val displayName = UUID.randomUUID().toString().take(8).plus(".jpg")
        val mediaImageUri =
            MediaStoreUtils.saveMediaToMediaStore(context.contentResolver, displayName) { fileDescriptor ->
                FileOutputStream(fileDescriptor.fileDescriptor).use { outputStream ->
                    context.contentResolver.openInputStream(imageUri).use {
                        outputStream.write(it?.readBytes())
                    }
                }
            }
        return mediaImageUri
    }
}
