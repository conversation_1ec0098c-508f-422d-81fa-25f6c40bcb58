package com.stt.android.billing

import android.app.Activity
import com.stt.android.domain.subscriptions.DomainPurchase
import com.stt.android.domain.subscriptions.PlayBillingHandler
import com.stt.android.domain.subscriptions.SubscriptionWithPrice
import javax.inject.Inject

class PlayBillingHandlerNoOp @Inject constructor() : PlayBillingHandler {
    override fun addListener(listener: PlayBillingHandler.Listener) {
        // Do nothing
    }

    override fun removeListener(listener: PlayBillingHandler.Listener) {
        // Do nothing
    }

    override suspend fun querySubscriptionPrices(skus: List<String>): List<SubscriptionWithPrice> =
        emptyList()

    override suspend fun launchBillingFlow(activity: Activity, username: String, sku: String) {
        // Do nothing
    }

    override suspend fun getPendingPurchases(): List<DomainPurchase> = emptyList()

    override suspend fun acknowledgePurchase(token: String) {
        // Do nothing
    }
}
