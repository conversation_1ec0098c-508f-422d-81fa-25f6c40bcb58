package com.stt.android.billing

import com.stt.android.controllers.SubscriptionInfoController
import com.stt.android.controllers.SubscriptionInfoControllerDataSource
import com.stt.android.controllers.SubscriptionItemControllerDataSource
import com.stt.android.domain.subscriptions.PlayBillingHandler
import com.stt.android.domain.subscriptions.SubscriptionInfoRepository
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@Module
@InstallIn(SingletonComponent::class)
abstract class BillingModule {
    @Binds
    abstract fun bindPlayBillingHandler(playBillingHandlerNoOp: PlayBillingHandlerNoOp): PlayBillingHandler

    @Binds
    abstract fun bindSubscriptionInfoRepository(subscriptionInfoRepositoryNoOp: SubscriptionInfoRepositoryNoOp): SubscriptionInfoRepository

    @Binds
    abstract fun bindSubscriptionItemController(subscriptionItemController: SubscriptionItemControllerNoOp): SubscriptionItemControllerDataSource

    @Binds
    abstract fun bindSubscriptionInfoController(subscriptionInfoController: SubscriptionInfoController): SubscriptionInfoControllerDataSource
}
