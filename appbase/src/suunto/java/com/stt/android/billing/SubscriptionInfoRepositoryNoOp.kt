package com.stt.android.billing

import com.stt.android.domain.subscriptions.DomainSubscriptionInfo
import com.stt.android.domain.subscriptions.SubscriptionInfoRepository
import javax.inject.Inject

class SubscriptionInfoRepositoryNoOp @Inject constructor() : SubscriptionInfoRepository {
    override suspend fun fetchAvailableSubscriptions(localOnly: Boolean): List<DomainSubscriptionInfo> =
        emptyList()

    override suspend fun replaceLocalSubscriptions(list: List<DomainSubscriptionInfo>) {
        // Do nothing
    }
}
