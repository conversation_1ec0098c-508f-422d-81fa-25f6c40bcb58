package com.stt.android.logs;

import android.content.Context;
import com.stt.android.R;
import com.stt.android.common.coroutines.CoroutinesDispatchers;
import com.stt.android.controllers.CurrentUserController;
import com.stt.android.network.interfaces.ANetworkProvider;
import com.stt.android.remote.di.BaseUrlConfiguration;
import dagger.Module;
import dagger.Provides;
import dagger.hilt.InstallIn;
import dagger.hilt.android.qualifiers.ApplicationContext;
import dagger.hilt.components.SingletonComponent;

@Module
@InstallIn(SingletonComponent.class)
public abstract class LogUploaderModule {
    @Provides
    static LogUploader provideLogUploader(
        @ApplicationContext Context appContext,
        ANetworkProvider networkProvider,
        CurrentUserController currentUserController,
        CoroutinesDispatchers coroutinesDispatchers,
        BaseUrlConfiguration baseUrlConfiguration
    ) {
        return new LogUploader(appContext, networkProvider,
            baseUrlConfiguration.getJucheUploadUrl() + "loguploader",
            appContext.getString(R.string.juche_app_name),
            appContext.getString(R.string.juche_app_key),
            currentUserController.getUsername(), coroutinesDispatchers);
    }
}
