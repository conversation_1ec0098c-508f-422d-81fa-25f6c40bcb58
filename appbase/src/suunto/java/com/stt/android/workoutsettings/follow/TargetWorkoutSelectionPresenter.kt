package com.stt.android.workoutsettings.follow

import android.content.Context
import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.WorkoutHeaderController
import com.stt.android.domain.routes.GetRoutesUseCase
import com.stt.android.domain.routes.Route
import com.stt.android.maps.location.SuuntoLocationSource
import com.stt.android.models.SimilarWorkoutModel
import dagger.hilt.android.scopes.ActivityScoped
import javax.inject.Inject

@ActivityScoped
class TargetWorkoutSelectionPresenter
@Inject constructor(
    appContext: Context,
    workoutHeaderController: WorkoutHeaderController,
    currentUserController: CurrentUserController,
    similarWorkoutModel: SimilarWorkoutModel,
    getRoutesUseCase: GetRoutesUseCase,
    suuntoLocationSource: SuuntoLocationSource
) : BaseTargetWorkoutSelectionPresenter(
    appContext,
    workoutHeader<PERSON><PERSON>roller,
    currentUserController,
    similarWorkoutModel,
    getRoutesUseCase,
    suuntoLocationSource
) {

    override fun onAddToWatchToggled(position: Int, route: Route?, isChecked: Boolean) {
        // NO-OP
    }
}
