package com.stt.android.workoutsettings.follow

import android.content.res.Resources
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.CompoundButton
import com.stt.android.controllers.UserSettingsController
import com.stt.android.domain.routes.Route
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.workoutsettings.WorkoutSettingsActivity

class RouteCardHolder(
    inflater: LayoutInflater,
    parent: ViewGroup,
    activity: WorkoutSettingsActivity,
    resources: Resources,
    userSettingsController: UserSettingsController,
    infoModelFormatter: InfoModelFormatter,
    onAddToWatchToggledListener: OnAddToWatchToggledListener?
) : WorkoutSelectionRouteCardHolder(
    inflater,
    parent,
    activity,
    resources,
    userSettingsController,
    infoModelFormatter
) {

    private var route: Route? = null

    private val checkedListener = CompoundButton.OnCheckedChangeListener { _, isChecked: Boolean ->
        if (route != null) {
            onAddToWatchToggledListener?.onAddToWatchToggled(bindingAdapterPosition, route, isChecked)
        }
    }

    override fun bind(routeCard: RouteCard, mapCacheWidth: Int, mapCacheHeight: Int) {
        super.bind(routeCard, mapCacheWidth, mapCacheHeight)
        route = routeCard.routeData
        if (routeCard.syncableWithDeviceData) {
            binding.addToWatchViewGroup.visibility = View.VISIBLE
            binding.addToWatchView.setup(route, routeCard.watchRouteListFull, checkedListener)
        } else {
            binding.addToWatchViewGroup.visibility = View.GONE
        }
    }
}
