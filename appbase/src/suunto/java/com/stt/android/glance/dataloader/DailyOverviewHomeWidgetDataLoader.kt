package com.stt.android.glance.dataloader

import android.content.Context
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.activitydata.ActivityDataType
import com.stt.android.domain.activitydata.dailyvalues.ActivityDataDailyRepository
import com.stt.android.domain.activitydata.goals.ActivityDataGoalRepository
import com.stt.android.domain.activitydata.goals.FetchSleepGoalUseCase
import com.stt.android.domain.diary.toLocalDate
import com.stt.android.domain.sleep.FetchSleepUseCase
import com.stt.android.domain.trenddata.TrendDataRepository
import com.stt.android.glance.data.DailyOverviewHomeWidgetInfo
import com.stt.android.utils.toEpochMilli
import com.suunto.algorithms.data.Energy.Companion.kcal
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.map
import java.time.LocalDate
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import kotlin.math.roundToInt
import kotlin.math.roundToLong
import kotlin.time.Duration
import com.stt.android.core.R as CR

class DailyOverviewHomeWidgetDataLoader @Inject constructor(
    private val context: Context,
    private val fetchSleepUseCase: FetchSleepUseCase,
    private val fetchSleepGoalUseCase: FetchSleepGoalUseCase,
    private val trendDataRepository: TrendDataRepository,
    private val activityDataDailyRepository: ActivityDataDailyRepository,
    private val activityDataGoalRepository: ActivityDataGoalRepository,
) {
    suspend fun load(): DailyOverviewHomeWidgetInfo = coroutineScope {
        val sleepDataDeferred = async { loadSleepData() }
        val caloriesDataDeferred = async { loadCaloriesData() }
        val stepsDataDeferred = async { loadStepsData() }

        val sleepData = sleepDataDeferred.await()
        val caloriesData = caloriesDataDeferred.await()
        val stepsData = stepsDataDeferred.await()

        DailyOverviewHomeWidgetInfo(
            sleepData.first,
            sleepData.second,
            caloriesData.first,
            caloriesData.second,
            stepsData.first,
            stepsData.second,
        )
    }

    private suspend fun loadSleepData(): Pair<Float, String> = coroutineScope {
        val today = LocalDate.now()
        val sleepInDateRangeDeferred = async {
            runSuspendCatching {
                fetchSleepUseCase.fetchSleeps(today, today)
                    .first()
            }.getOrNull() ?: emptyList()
        }
        val sleepGoalDeferred = async {
            runSuspendCatching {
                fetchSleepGoalUseCase.fetchSleepGoal()
                    .first()
                    .inWholeSeconds
                    .toInt()
            }.getOrNull() ?: ActivityDataType.SleepDuration().goal
        }
        val sleepInDateRange = sleepInDateRangeDeferred.await()
        val sleepGoal = sleepGoalDeferred.await()
        val dailySleepData = sleepInDateRange.groupBy {
            it.timestamp.toLocalDate()
        }
        val sleepSeconds = dailySleepData[today]?.firstOrNull()
            ?.let { sleep -> (sleep.longSleep?.sleepDuration ?: Duration.ZERO) + (sleep.getMergedNap()?.duration ?: Duration.ZERO) }
            ?.inWholeSeconds
            ?.toFloat()
            ?: 0.0F

        if (sleepSeconds <= 0f) {
            Pair(0f, "--")
        } else {
            Pair((sleepSeconds / sleepGoal).coerceAtMost(1f), sleepSeconds.formatDuration(context))
        }
    }

    private suspend fun loadCaloriesData(): Pair<Float, String> = coroutineScope {
        val today = LocalDate.now()
        val todayMillis = today.atStartOfDay().toEpochMilli()
        val trendTodayCaloriesDeferred = async {
            runSuspendCatching {
                trendDataRepository.fetchTrendDataForDateRange(todayMillis, todayMillis, true)
                    .first()
                    .groupBy { it.timeISO8601.toLocalDate() }[today]
                    ?.sumOf { it.energy.inKcal }
                    ?.kcal
            }.getOrNull()
        }
        val todayCaloriesDeferred = async {
            runSuspendCatching {
                activityDataDailyRepository.fetchEnergy()
                    .first()
            }.getOrNull()
        }
        val calorieGoalKcalDeferred = async {
            runSuspendCatching {
                activityDataGoalRepository.fetchEnergyGoal()
                    .map { it.inKcal.roundToInt() }
                    .first()
            }.getOrNull()?.takeIf { it > 0 } ?: ActivityDataType.Energy().goal
        }

        val todayCaloriesKcal = (todayCaloriesDeferred.await()
            ?.takeIf { it.inCal > 0.0 }
            ?: trendTodayCaloriesDeferred.await())
            ?.inKcal
        val calorieGoalKcal = calorieGoalKcalDeferred.await()

        if (todayCaloriesKcal == null || todayCaloriesKcal <= 0.0) {
            Pair(0f, "--")
        } else {
            Pair(
                (todayCaloriesKcal.toFloat() / calorieGoalKcal).coerceAtMost(1f),
                "${todayCaloriesKcal.roundToInt()} ${context.getString(CR.string.kcal)}"
            )
        }
    }

    private suspend fun loadStepsData(): Pair<Float, String> = coroutineScope {
        val today = LocalDate.now()
        val todayMillis = today.atStartOfDay().toEpochMilli()
        val trendTodayStepsDeferred = async {
            runSuspendCatching {
                trendDataRepository.fetchTrendDataForDateRange(todayMillis, todayMillis, true)
                    .first()
                    .groupBy { it.timeISO8601.toLocalDate() }[today]
                    ?.sumOf { it.steps }
            }.getOrNull()
        }
        val todayStepsDeferred = async {
            runSuspendCatching {
                activityDataDailyRepository.fetchSteps().first()
            }.getOrNull()
        }
        val stepGoalDeferred = async {
            runSuspendCatching {
                activityDataGoalRepository.fetchStepsGoal().first()
            }.getOrNull() ?: ActivityDataType.Steps().goal
        }

        val todaySteps = todayStepsDeferred.await()?.takeIf { it > 0 }
            ?: trendTodayStepsDeferred.await()
        val stepGoal = stepGoalDeferred.await()

        if (todaySteps == null || todaySteps <= 0) {
            Pair(0f, "--")
        } else {
            Pair((todaySteps.toFloat() / stepGoal).coerceAtMost(1f), todaySteps.toString())
        }
    }

    internal fun Float.formatDuration(context: Context): String {
        val (hours, minutes) = secondsToHourMinute()
        val hourUnit = context.getString(CR.string.hour)
        val minuteUnit = context.getString(CR.string.minute)
        return if (hours == 0L) {
            "$minutes$minuteUnit"
        } else if (minutes == 0L) {
            "$hours$hourUnit"
        } else {
            "$hours$hourUnit $minutes$minuteUnit"
        }
    }

    private fun Float.secondsToHourMinute(): Pair<Long, Long> {
        val seconds = roundToLong()
        var hours = TimeUnit.SECONDS.toHours(seconds)
        val secondsWithRounding = seconds + TimeUnit.MINUTES.toSeconds(1) / 2
        var minutes =
            TimeUnit.SECONDS.toMinutes(secondsWithRounding) - TimeUnit.HOURS.toMinutes(hours)
        if (minutes == TimeUnit.HOURS.toMinutes(1)) {
            minutes = 0
            hours += 1
        }
        return Pair(hours, minutes)
    }
}
