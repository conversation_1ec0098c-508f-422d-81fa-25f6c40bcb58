package com.stt.android

import android.app.PendingIntent
import android.app.TaskStackBuilder
import android.content.BroadcastReceiver
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.pm.PackageManager
import androidx.core.content.ContextCompat
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ProcessLifecycleOwner
import com.google.firebase.FirebaseApp
import com.stt.android.di.connectivity.ConnectivityProcessEntryPoint
import com.stt.android.ui.activities.settings.PowerManagementSettingsActivity
import com.stt.android.utils.BluetoothUtils
import com.stt.android.utils.STTConstants
import com.stt.android.utils.STTConstants.ExtraKeys.ANALYTICS_UUID
import com.stt.android.utils.currentProcessName
import com.stt.android.watch.companionAssociation.CompanionAssociationUIContext
import com.suunto.connectivity.notifications.NotificationHook
import com.suunto.connectivity.notifications.notificationHookInstance
import com.suunto.connectivity.repository.SuuntoRepositoryInitProvider
import com.suunto.connectivity.repository.SuuntoRepositoryService
import com.suunto.connectivity.suuntoconnectivity.SuuntoServiceStartOnBootReceiver
import dagger.hilt.android.EntryPointAccessors
import timber.log.Timber

abstract class SuuntoApplication : STTApplication() {

    // "improper" injection. This is meant to be initialised and used by connectivity process only
    private val connectivityInjection: ConnectivityProcessEntryPoint by lazy {
        initAtProcessStart()
        Timber.d("Connectivity process injected")
        EntryPointAccessors.fromApplication(this)
    }

    private val isConnectivityProcess: Boolean
        get() = currentProcessName?.contains("suuntoconnectivity") == true

    /**
     * Broadcast receiver for receiving Analytics UUID in connectivity process.
     */
    private val analyticsUUIDReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            Timber.d(
                "Connectivity process received analytics UUID %s",
                intent.getStringExtra(ANALYTICS_UUID)
            )
            // setAnalyticsUUID is calling Firebase, ensure everything it is initialized
            FirebaseApp.initializeApp(this@SuuntoApplication)
            connectivityInjection.analyticsRuntimeHook().setAnalyticsUUID(
                intent.getStringExtra(
                    ANALYTICS_UUID
                )
            )
        }
    }

    override fun onCreateMainProcess() {
        super.onCreateMainProcess()
        startOrDisableConnectivityProcess()
    }

    override fun onMainProcessInjected() {
        // Empty body
    }

    override fun onCreateSecondaryProcess() {
        super.onCreateSecondaryProcess()
        if (isConnectivityProcess) {
            ContextCompat.registerReceiver(
                this,
                analyticsUUIDReceiver,
                IntentFilter(STTConstants.BroadcastActions.ANALYTICS_UUID_CHANGED),
                ContextCompat.RECEIVER_EXPORTED
            )
            connectivityInjection.connectivityProcessInitializers().forEach { it.init(this) }
        }

        notificationHookInstance = object : NotificationHook {
            override fun getPendingIntentForWatchConnectionNotificationTap(): PendingIntent {
                val intent = PowerManagementSettingsActivity.newStartIntent(
                    this@SuuntoApplication,
                    CompanionAssociationUIContext.INITIATED_FROM_SERVICE_NOTIFICATION
                )
                return TaskStackBuilder.create(this@SuuntoApplication).run {
                    addNextIntentWithParentStack(intent)
                    getPendingIntent(
                        0,
                        PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
                    )
                }
            }
        }
    }

    private fun startOrDisableConnectivityProcess() {
        if (BluetoothUtils.isBluetoothSupported(this)) {
            ProcessLifecycleOwner.get().lifecycle.addObserver(object : DefaultLifecycleObserver {
                override fun onResume(owner: LifecycleOwner) {
                    super.onResume(owner)
                    Timber.d("Starting SuuntoRepositoryService...")
                    SuuntoRepositoryService.startService(this@SuuntoApplication, true, "SuuntoApplication")
                }
            })
            return
        }

        // Prevent connectivity process starting on the device boot
        Timber.d("Disabling redundant components running on the connectivity process.")
        disableComponent(ComponentName(this, SuuntoServiceStartOnBootReceiver::class.java))
        disableComponent(ComponentName(this, SuuntoRepositoryInitProvider::class.java))
    }

    private fun disableComponent(component: ComponentName) {
        packageManager.setComponentEnabledSetting(
            component,
            PackageManager.COMPONENT_ENABLED_STATE_DISABLED,
            0
        )
    }
}
