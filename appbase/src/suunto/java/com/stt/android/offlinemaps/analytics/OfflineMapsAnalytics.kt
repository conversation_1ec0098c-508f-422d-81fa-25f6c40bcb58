package com.stt.android.offlinemaps.analytics

import android.content.SharedPreferences
import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.AnalyticsEventProperty
import com.stt.android.analytics.AnalyticsProperties
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.di.SuuntoSharedPrefs
import com.stt.android.home.explore.offlinemaps.OfflineMapsTabAnalytics
import com.stt.android.offlinemaps.datasource.OfflineRegionRepository
import com.stt.android.utils.STTConstants
import com.suunto.connectivity.suuntoconnectivity.device.AnalyticsDevicePropertyHelper
import javax.inject.Inject

interface OfflineMapsAnalytics {
    fun trackDownloadMapsScreen(source: String)
    fun trackSearchScreen()
    fun trackDownloadStarted(
        regionName: String,
        ongoingRegionDownloads: Int,
    )

    fun trackDownloadCancelled(
        regionName: String,
        ongoingRegionDownloads: Int,
    )

    fun trackDownloadDeleted(
        regionName: String,
    )

    fun trackDownloadBlocked(
        issueType: String
    )

    fun trackMapsLibraryScreen(
        source: String,
        downloadedRegions: Int
    )
}

class DefaultOfflineMapsAnalytics @Inject constructor(
    private val offlineRegionRepository: OfflineRegionRepository,
    @SuuntoSharedPrefs private val sharedPreferences: SharedPreferences,
    private val amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker,
) : OfflineMapsAnalytics, OfflineMapsTabAnalytics {
    override fun trackDownloadMapsScreen(source: String) = amplitudeAnalyticsTracker.trackEvent(
        AnalyticsEvent.WATCH_MAPS_DOWNLOAD_MAPS_SCREEN,
        AnalyticsProperties().apply {
            put(AnalyticsEventProperty.SOURCE, source)
        }
    )

    override fun trackSearchScreen() = amplitudeAnalyticsTracker.trackEvent(
        AnalyticsEvent.WATCH_MAPS_SEARCH_LOCATION
    )

    override fun trackDownloadStarted(
        regionName: String,
        ongoingRegionDownloads: Int,
    ) = amplitudeAnalyticsTracker.trackEvent(
        AnalyticsEvent.WATCH_MAPS_DOWNLOAD_MAP_STARTED,
        getWatchProperties().apply {
            put(AnalyticsEventProperty.REGION_SELECTED, regionName)
            put(AnalyticsEventProperty.NUMBER_OF_MAPS_DOWNLOADING, ongoingRegionDownloads)
            put(AnalyticsEventProperty.WIFI_SETUP, getHasWifiSetup())
        }
    )

    override fun trackDownloadCancelled(
        regionName: String,
        ongoingRegionDownloads: Int,
    ) = amplitudeAnalyticsTracker.trackEvent(
        AnalyticsEvent.WATCH_MAPS_CANCEL_DOWNLOAD,
        getWatchProperties().apply {
            put(AnalyticsEventProperty.REGION_SELECTED, regionName)
            put(AnalyticsEventProperty.NUMBER_OF_MAPS_DOWNLOADING, ongoingRegionDownloads)
            put(AnalyticsEventProperty.WIFI_SETUP, getHasWifiSetup())
        }
    )

    override fun trackDownloadDeleted(
        regionName: String,
    ) = amplitudeAnalyticsTracker.trackEvent(
        AnalyticsEvent.WATCH_MAPS_DELETE_REGION,
        getWatchProperties().apply {
            put(AnalyticsEventProperty.REGION_SELECTED, regionName)
            put(AnalyticsEventProperty.WIFI_SETUP, getHasWifiSetup())
        }
    )

    override fun trackDownloadBlocked(
        issueType: String
    ) = amplitudeAnalyticsTracker.trackEvent(
        AnalyticsEvent.WATCH_MAPS_DOWNLOAD_MAP_BLOCKED,
        getWatchProperties().apply {
            put(AnalyticsEventProperty.ISSUE_TYPE, issueType)
        }
    )

    override fun trackMapsLibraryScreen(
        source: String,
        downloadedRegions: Int
    ) = amplitudeAnalyticsTracker.trackEvent(
        AnalyticsEvent.WATCH_MAPS_LIBRARY_SCREEN,
        getWatchProperties().apply {
            put(AnalyticsEventProperty.SOURCE, source)
            put(AnalyticsEventProperty.NUMBER_OF_MAPS_DOWNLOADED, downloadedRegions)
        }
    )

    override suspend fun trackMapLibraryScreenFromMap() {
        runSuspendCatching {
            trackMapsLibraryScreen(
                source = AnalyticsPropertyValue.MapsLibraryScreenSource.MAP_SCREEN,
                downloadedRegions = offlineRegionRepository.getLibrary().count { it.downloaded }
            )
        }
    }

    private fun getWatchProperties(): AnalyticsProperties {
        val firmwareVersion: String? = sharedPreferences.getString(
            STTConstants.SuuntoPreferences.KEY_SUUNTO_PAIRED_WATCH_FW_VERSION,
            "N/A"
        )
        val watchVariantName: String? = sharedPreferences.getString(
            STTConstants.SuuntoPreferences.KEY_SUUNTO_PAIRED_WATCH_MODEL,
            "N/A"
        )
        val watchModel: String? = watchVariantName?.let {
            AnalyticsDevicePropertyHelper.getWatchModelNameForVariantName(it)
        }
        val watchSerial: String? = sharedPreferences.getString(
            STTConstants.SuuntoPreferences.KEY_SUUNTO_PAIRED_WATCH_SERIAL_NUMBER,
            "N/A"
        )

        return AnalyticsProperties().apply {
            put(AnalyticsEventProperty.SUUNTO_WATCH_FIRMWARE_VERSION, firmwareVersion)
            put(AnalyticsEventProperty.SUUNTO_WATCH_MODEL, watchModel)
            put(AnalyticsEventProperty.SUUNTO_WATCH_SERIAL_NUMBER, watchSerial)
        }
    }

    private fun getHasWifiSetup(): String {
        val hasWifiSetup =
            if (sharedPreferences.contains(STTConstants.SuuntoPreferences.KEY_WATCH_HAS_WIFI_SETUP_FOR_ANALYTICS)) {
                sharedPreferences.getBoolean(
                    STTConstants.SuuntoPreferences.KEY_WATCH_HAS_WIFI_SETUP_FOR_ANALYTICS,
                    false
                )
            } else {
                null
            }

        return when (hasWifiSetup) {
            true -> AnalyticsPropertyValue.YES
            false -> AnalyticsPropertyValue.NO
            null -> AnalyticsPropertyValue.UNKNOWN
        }
    }
}
