package com.stt.android.offlinemaps

import androidx.compose.runtime.Stable
import com.stt.android.offlinemaps.entity.FreeSpaceAvailable
import com.stt.android.offlinemaps.entity.OfflineMapDownloadTarget
import com.stt.android.offlinemaps.entity.OfflineRegionDownloadError
import com.stt.android.offlinemaps.entity.OfflineRegionListData
import com.stt.android.offlinemaps.entity.OfflineRegionResult
import kotlinx.collections.immutable.ImmutableSet

@Stable
internal sealed interface OfflineMapsSelectionViewState {
    data object Loading : OfflineMapsSelectionViewState

    data class Loaded(
        val supportsOfflineMapsOnMobile: <PERSON><PERSON>an,
        val selectedDownloadTargets: ImmutableSet<OfflineMapDownloadTarget>,
        val showBatteryInfoWithPendingDownload: Boolean,
        val showWifiDisabledInfoWithPendingDownload: <PERSON><PERSON><PERSON>,
        val showWifiSetupInfoWithPendingDownload: Boolean,
        val catalogue: OfflineRegionListData.Catalogue,
        val selectedRegionGroup: OfflineRegionResult.OfflineRegionGroup?,
        val selectedRegion: OfflineRegionResult.OfflineRegion?,
        val showDownloadInfo: Boolean,
        val downloadError: OfflineRegionDownloadError?,
        val cancellingDownload: Boolean,
        val requestingDownload: Boolean,
        val updateAvailableCount: Int,
        val freeSpaceAvailable: FreeSpaceAvailable?,
    ) : OfflineMapsSelectionViewState
}
