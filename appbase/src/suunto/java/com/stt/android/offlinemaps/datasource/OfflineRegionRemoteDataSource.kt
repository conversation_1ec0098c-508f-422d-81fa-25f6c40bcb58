package com.stt.android.offlinemaps.datasource

import com.google.android.gms.maps.model.LatLng
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.offlinemaps.entity.OfflineRegionListData
import com.stt.android.offlinemaps.entity.OfflineRegionResult
import com.stt.android.offlinemaps.entity.OfflineRegionSearchResult
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.PersistentList
import kotlinx.collections.immutable.toPersistentList
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.text.Collator
import javax.inject.Inject

class OfflineRegionRemoteDataSource @Inject constructor(
    private val offlineRegionRestApi: OfflineRegionRestApi,
    private val unit: MeasurementUnit
) : OfflineRegionDataSource {
    override suspend fun getCatalogue(
        deviceSerial: String,
        latLng: LatLng?,
        capabilities: String?,
        includeNearbyGroups: Boolean,
    ): OfflineRegionListData.Catalogue {
        val catalogue = offlineRegionRestApi.getCatalogue(
            latitude = latLng?.latitude,
            longitude = latLng?.longitude,
            includeNearbyGroups = includeNearbyGroups,
            capabilities = capabilities,
        ).payloadOrThrow()
        return withContext(Dispatchers.Default) {
            val groups = catalogue.offlineRegionGroups(deviceSerial)
            OfflineRegionListData.Catalogue(
                nearby = catalogue.nearbyRegions(deviceSerial, groups),
                groups = groups
            )
        }
    }

    private fun RemoteOfflineRegionCatalogue.offlineRegionGroups(
        deviceSerial: String
    ) = groups.map { group ->
        val remoteRegions = group.regions.mapNotNull { regionId ->
            regions.firstOrNull { it.id == regionId.id }
        }
        OfflineRegionResult.OfflineRegionGroup(
            id = group.id,
            name = group.name,
            size = remoteRegions.sumOf { it.storageSizeForWatch },
            regions = remoteRegions
                .map { it.toOfflineRegion(deviceSerial, unit, group.name, group.batchDownloadAllowed, group.maskUrl) }
                .sortedWith { region1, region2 ->
                    Collator.getInstance().compare(region1.name, region2.name)
                }
                .toPersistentList(),
            batchDownloadAllowed = group.batchDownloadAllowed
        )
    }
        .sortedWith { group1, group2 ->
            Collator.getInstance().compare(group1.name, group2.name)
        }
        .toPersistentList()

    private fun RemoteOfflineRegionCatalogue.nearbyRegions(
        deviceSerial: String,
        localGroups: PersistentList<OfflineRegionResult.OfflineRegionGroup>
    ) =
        nearbyRegions
            .mapNotNull { remoteNearby ->
                if (remoteNearby.type == RemoteResultType.GROUP) {
                    localGroups.firstOrNull { it.id == remoteNearby.id }
                } else {
                    regions.firstOrNull { it.id == remoteNearby.id }?.let { region ->
                        val currentGroup = groups
                            .firstOrNull { group ->
                                group.regions.any { it.id == region.id }
                            }
                        region.toOfflineRegion(deviceSerial, unit, currentGroup?.name, currentGroup?.batchDownloadAllowed, currentGroup?.maskUrl)
                    }
                }
            }
            .toPersistentList()

    override suspend fun putDownloadOrder(
        deviceSerial: String,
        regionId: String,
        capabilities: String?,
        groupName: String?,
    ): OfflineRegionResult.OfflineRegion = offlineRegionRestApi.putDownloadOrder(
        downloadOrderRequest = DownloadOrderRequest(
            deviceSerialNumber = deviceSerial,
            regionId = regionId,
            capabilities = capabilities,
        ),
    )
        .payloadOrThrow()
        .run { toOfflineRegion(deviceSerial, unit, groupName) }

    override suspend fun search(searchTerm: String): ImmutableList<OfflineRegionSearchResult> =
        offlineRegionRestApi.search(searchTerm = searchTerm.trim(), scopes = "REGIONS,GROUPS")
            .payloadOrThrow().results
            .map {
                OfflineRegionSearchResult(
                    id = it.id,
                    name = it.name,
                    highlightResult = it.highlightResult,
                    type = it.type,
                )
            }.toPersistentList()

    override suspend fun getLibrary(
        deviceSerial: String,
        capabilities: String?,
    ): ImmutableList<OfflineRegionResult.OfflineRegion> =
        offlineRegionRestApi.getLibrary(
            deviceSerial = deviceSerial,
            capabilities = capabilities,
        )
            .payloadOrThrow()
            .map { it.toOfflineRegion(deviceSerial, unit) }
            .sortedWith { region1, region2 ->
                Collator.getInstance().compare(region1.name, region2.name)
            }
            .toPersistentList()

    override suspend fun getCatalogueRegion(
        deviceSerial: String,
        regionId: String,
        capabilities: String?,
        groupName: String?,
    ): OfflineRegionResult.OfflineRegion {
        return offlineRegionRestApi.getCatalogueRegion(
            regionId = regionId,
            deviceSerial = deviceSerial,
            capabilities = capabilities,
        )
            .payloadOrThrow()
            .run { toOfflineRegion(deviceSerial, unit, groupName) }
    }

    override suspend fun getLibraryRegion(
        deviceSerial: String,
        regionId: String,
        capabilities: String?,
    ): OfflineRegionResult.OfflineRegion {
        return offlineRegionRestApi.getLibraryRegion(
            regionId = regionId,
            deviceSerial = deviceSerial,
            capabilities = capabilities,
        )
            .payloadOrThrow()
            .run { toOfflineRegion(deviceSerial, unit) }
    }

    override suspend fun deleteRegion(
        deviceSerial: String,
        region: OfflineRegionResult.OfflineRegion,
        capabilities: String?,
    ): OfflineRegionResult.OfflineRegion {
        return if (region.deleteRequested) {
            offlineRegionRestApi.cancelDeleteRequest(
                regionId = region.id,
                deviceSerial = deviceSerial,
                capabilities = capabilities
            )
        } else {
            offlineRegionRestApi.deleteRegion(
                regionId = region.id,
                deviceSerial = deviceSerial,
                capabilities = capabilities
            )
        }
            .payloadOrThrow()
            .run { toOfflineRegion(deviceSerial, unit, region.groupName) }
    }

    override suspend fun resetLibrary(
        deviceSerial: String,
        capabilities: String?,
    ): ImmutableList<OfflineRegionResult.OfflineRegion> {
        return offlineRegionRestApi.resetLibrary(
            body = LibraryResetBody(deviceSerialNumber = deviceSerial),
            capabilities = capabilities
        )
            .payloadOrThrow()
            .map { it.toOfflineRegion(deviceSerial, unit) }
            .toPersistentList()
    }
}
