package com.stt.android.offlinemaps

import com.stt.android.offlinemaps.entity.OfflineMapDownloadTarget
import com.stt.android.offlinemaps.entity.OfflineRegionResult

internal sealed interface OfflineMapsSelectionViewEvent {
    data object NavigateUp : OfflineMapsSelectionViewEvent

    data object ShowDownloadInfo : OfflineMapsSelectionViewEvent

    data object DismissDownloadInfo : OfflineMapsSelectionViewEvent

    data class DownloadOfflineRegion(
        val regionId: String,
    ) : OfflineMapsSelectionViewEvent

    data class CancelDownloadingOfflineRegion(
        val regionId: String,
    ) : OfflineMapsSelectionViewEvent

    data object MarkDownloadErrorAsShown : OfflineMapsSelectionViewEvent

    data class SelectOfflineRegion(
        val region: OfflineRegionResult.OfflineRegion,
    ) : OfflineMapsSelectionViewEvent

    data class SelectOfflineRegionGroup(
        val regionGroup: OfflineRegionResult.OfflineRegionGroup,
    ) : OfflineMapsSelectionViewEvent

    data object DismissWifiSetupInfo : OfflineMapsSelectionViewEvent

    data object DismissWifiDisabledInfo : OfflineMapsSelectionViewEvent

    data object DismissBatteryInfo : OfflineMapsSelectionViewEvent

    data object TrackSearchModeEnabled : OfflineMapsSelectionViewEvent

    data class SelectDownloadTarget(
        val target: OfflineMapDownloadTarget,
    ) : OfflineMapsSelectionViewEvent

    data class DeselectDownloadTarget(
        val target: OfflineMapDownloadTarget,
    ) : OfflineMapsSelectionViewEvent
}

internal typealias OfflineMapsSelectionEventHandler = (OfflineMapsSelectionViewEvent) -> Unit
