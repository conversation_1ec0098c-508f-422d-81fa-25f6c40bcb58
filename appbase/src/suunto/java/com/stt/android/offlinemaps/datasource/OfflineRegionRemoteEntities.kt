package com.stt.android.offlinemaps.datasource

import android.os.Parcelable
import com.google.android.gms.maps.model.LatLng
import com.google.android.gms.maps.model.LatLngBounds
import com.google.gson.annotations.SerializedName
import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.offlinemaps.entity.OfflineMapDownloadTarget
import com.stt.android.offlinemaps.entity.OfflineRegionResult
import kotlinx.collections.immutable.persistentMapOf
import kotlinx.collections.immutable.toImmutableList
import kotlinx.collections.immutable.toPersistentList
import kotlinx.parcelize.Parcelize
import com.stt.android.core.R as CR

@JsonClass(generateAdapter = true)
data class RemoteOfflineRegionCatalogue(
    @Json(name = "nearbyRegions")
    val nearbyRegions: List<RemoteNearby>,
    @Json(name = "groups")
    val groups: List<RemoteOfflineRegionGroup>,
    @Json(name = "regions")
    val regions: List<RemoteOfflineRegion>
)

@JsonClass(generateAdapter = true)
data class RemoteOfflineRegionGroup(
    @Json(name = "id")
    val id: String,
    @Json(name = "name")
    val name: String,
    @Json(name = "regions")
    val regions: List<RemoteId>,
    @Json(name = "maskUrl")
    val maskUrl: String?,
    @Json(name = "batchDownloadAllowed")
    val batchDownloadAllowed: Boolean?
)

@JsonClass(generateAdapter = true)
data class RemoteNearby(
    @Json(name = "id")
    val id: String,
    @Json(name = "type")
    val type: RemoteResultType?
)

@JsonClass(generateAdapter = true)
data class RemoteId(
    @Json(name = "id")
    val id: String
)

@JsonClass(generateAdapter = true)
data class RemoteOfflineRegion(
    @Json(name = "id")
    val id: String,
    @Json(name = "name")
    val name: String,
    @Json(name = "area")
    val area: Double?,
    @Json(name = "sources")
    val sources: List<RemoteOfflineRegionSource>,
    @Json(name = "description")
    val description: String?,
    @Json(name = "boundaryUrl")
    val boundaryUrl: String?,
    @Json(name = "maskUrl")
    val maskUrl: String?,
    @Json(name = "bbox")
    val bounds: RemoteBounds?,
    @Json(name = "downloadOrders")
    val downloadOrders: List<DownloadOrder>?,
    @Json(name = "styles")
    val styles: List<RemoteId> = emptyList(),
    @Json(name = "adjacentRegions")
    val adjacentRegions: List<RemoteId> = emptyList(),
    @Json(name = "adjacentMaskUrl")
    val adjacentMaskUrl: String?,
    @Json(name = "centerPoint")
    val centerPoint: RemoteLatLng?
)

@JsonClass(generateAdapter = true)
data class RemoteLibraryOfflineRegion(
    @Json(name = "id")
    val id: String,
    @Json(name = "name")
    val name: String,
    @Json(name = "description")
    val description: String?,
    @Json(name = "area")
    val area: Double?,
    @Json(name = "bbox")
    val bounds: RemoteBounds?,
    @Json(name = "boundaryUrl")
    val boundaryUrl: String?,
    @Json(name = "downloadedSize")
    val downloadedSize: Long?,
    @Json(name = "downloadCompletedAt")
    val downloadCompletedAt: Long?,
    @Json(name = "lastModifiedDate")
    val lastModifiedDate: Long,
    @Json(name = "maskUrl")
    val maskUrl: String?,
    @Json(name = "storageSize")
    val storageSize: Long?,
    @Json(name = "transferSize")
    val transferSize: Long?,
    @Json(name = "status")
    val status: OfflineRegionStatus,
    @Json(name = "styles")
    val styles: List<RemoteId> = emptyList()
)

@JsonClass(generateAdapter = true)
data class RemoteBounds(
    @Json(name = "southwest")
    val southwest: RemoteLatLng,
    @Json(name = "northeast")
    val northeast: RemoteLatLng
)

@JsonClass(generateAdapter = true)
data class RemoteLatLng(
    @Json(name = "latitude")
    val latitude: Double,
    @Json(name = "longitude")
    val longitude: Double
)

@JsonClass(generateAdapter = true)
data class RemoteOfflineRegionSource(
    @Json(name = "type")
    val type: String?,
    @Json(name = "storageSize")
    val storageSize: Long?,
    @Json(name = "transferSize")
    val transferSize: Long?,
    @Json(name = "watchPreferred")
    val watchPreferred: Boolean?
)

private const val TILE_TYPE_FOR_WATCH: String = "SMTF_V1"
private const val TILE_TYPE_FOR_MOBILE: String = "SMTF_V1_GZ"

val RemoteOfflineRegion.storageSizeForWatch: Long
    get() = sources.firstOrNull { it.watchPreferred == true }?.storageSize
        ?: sources.firstOrNull { it.type == TILE_TYPE_FOR_WATCH }?.storageSize
        ?: sources.firstOrNull()?.storageSize
        ?: 0L

val RemoteOfflineRegion.transferSizeForWatch: Long
    get() = sources.firstOrNull { it.watchPreferred == true }?.transferSize
        ?: sources.firstOrNull { it.type == TILE_TYPE_FOR_WATCH }?.transferSize
        ?: sources.firstOrNull()?.transferSize
        ?: 0L

val RemoteOfflineRegion.storageSizeForMobile: Long
    get() = sources.firstOrNull { it.type == TILE_TYPE_FOR_MOBILE }?.storageSize
        ?: 0L

val RemoteOfflineRegion.transferSizeForMobile: Long
    get() = sources.firstOrNull { it.type == TILE_TYPE_FOR_MOBILE }?.transferSize
        ?: 0L

@JsonClass(generateAdapter = true)
@Parcelize
data class DownloadOrder(
    @Json(name = "deviceSerialNumber")
    val deviceSerialNumber: String,
    @Json(name = "downloadCompletedAt")
    val downloadCompletedAt: Long?,
    @Json(name = "status")
    val status: OfflineRegionStatus,
    @Json(name = "downloadedSize")
    val downloadedSize: Long?,
    @Json(name = "sourceTypeUsed")
    val sourceTypeUsed: String?,
) : Parcelable

@JsonClass(generateAdapter = false)
enum class OfflineRegionStatus(val value: String) {
    /**
     * Download requested (app ordered download)
     */
    @SerializedName("REQUESTED")
    REQUESTED("REQUESTED"),

    /**
     * Download in progress (when watch started)
     */
    @SerializedName("IN_PROGRESS")
    IN_PROGRESS("IN_PROGRESS"),

    /**
     * Download finished (watch confirmed it’s finished)
     */
    @SerializedName("FINISHED")
    FINISHED("FINISHED"),

    /**
     * Delete requested (app requested delete)
     */
    @SerializedName("DELETE_REQUESTED")
    DELETE_REQUESTED("DELETE_REQUESTED"),

    /** When watch confirmed deletion => remove the item */
    @SerializedName("DELETE_FINISHED")
    DELETE_FINISHED("DELETE_FINISHED"),

    /**
     * Map was updated after map had been requested or downloaded
     */
    @SerializedName("UPDATE_AVAILABLE")
    UPDATE_AVAILABLE("UPDATE_AVAILABLE"),

    /**
     * Updating maps in progress (watch reported that it saw an update and downloading it)
     */
    @SerializedName("UPDATE_IN_PROGRESS")
    UPDATE_IN_PROGRESS("UPDATE_IN_PROGRESS"),

    /**
     * Watch reported that download/update failed
     */
    @SerializedName("FAILED")
    FAILED("FAILED")
}

@JsonClass(generateAdapter = true)
data class DownloadOrderRequest(
    @Json(name = "deviceSerialNumber")
    val deviceSerialNumber: String,
    @Json(name = "regionId")
    val regionId: String,
    @Json(name = "watchCapabilities")
    val capabilities: String?
)

@JsonClass(generateAdapter = true)
data class SearchResults(
    @Json(name = "results")
    val results: List<SearchResult>
)

@JsonClass(generateAdapter = true)
data class SearchResult(
    @Json(name = "highlightResult")
    val highlightResult: String,
    @Json(name = "id")
    val id: String,
    @Json(name = "name")
    val name: String,
    @Json(name = "type")
    val type: RemoteResultType,
)

@JsonClass(generateAdapter = false)
enum class RemoteResultType(val value: String) {
    @SerializedName("REGION")
    REGION("REGION"),

    @SerializedName("GROUP")
    GROUP("GROUP")
}

@JsonClass(generateAdapter = true)
data class LibraryResetBody(
    @Json(name = "deviceSerialNumber")
    val deviceSerialNumber: String,
)

fun RemoteOfflineRegion.toOfflineRegion(
    deviceSerial: String,
    unit: MeasurementUnit,
    groupName: String? = null,
    batchDownloadAllowed: Boolean? = null,
    groupMaskUrl: String? = null
): OfflineRegionResult.OfflineRegion {
    val (value, unitRes) = if (unit == MeasurementUnit.IMPERIAL) {
        squareKmToSquareMi(area ?: 0.0) to CR.string.square_miles
    } else {
        area to CR.string.square_kilometers
    }
    return OfflineRegionResult.OfflineRegion(
        id = id,
        name = name,
        sizes = persistentMapOf(
            OfflineMapDownloadTarget.WATCH to OfflineRegionResult.OfflineRegion.Size(
                storageSizeInBytes = storageSizeForWatch,
                transferSizeInBytes = transferSizeForWatch,
            ),
            OfflineMapDownloadTarget.MOBILE to OfflineRegionResult.OfflineRegion.Size(
                storageSizeInBytes = storageSizeForMobile,
                transferSizeInBytes = transferSizeForMobile,
            ),
        ),
        area = value,
        areaUnitRes = unitRes,
        description = description,
        bounds = bounds?.let {
            LatLngBounds(
                LatLng(bounds.southwest.latitude, bounds.southwest.longitude),
                LatLng(bounds.northeast.latitude, bounds.northeast.longitude),
            )
        },
        boundaryUrl = boundaryUrl,
        maskUrl = if (batchDownloadAllowed == true) {
            groupMaskUrl
        } else {
            maskUrl
        },
        downloadOrder = downloadOrders?.firstOrNull { it.deviceSerialNumber == deviceSerial },
        styleIds = styles.map { it.id }.toPersistentList(),
        groupName = groupName,
        adjacentRegions = adjacentRegions.map(RemoteId::id).toImmutableList(),
        adjacentMaskUrl = adjacentMaskUrl,
        centerPoint = if (centerPoint != null) {
            LatLng(centerPoint.latitude, centerPoint.longitude)
        } else {
            null
        },
        batchDownloadAllowed = batchDownloadAllowed
    )
}

fun RemoteLibraryOfflineRegion.toOfflineRegion(
    deviceSerial: String,
    unit: MeasurementUnit
): OfflineRegionResult.OfflineRegion {
    val (value, unitRes) = if (unit == MeasurementUnit.IMPERIAL) {
        squareKmToSquareMi(area ?: 0.0) to CR.string.square_miles
    } else {
        area to CR.string.square_kilometers
    }
    return OfflineRegionResult.OfflineRegion(
        id = id,
        name = name,
        sizes = persistentMapOf(
            OfflineMapDownloadTarget.WATCH to OfflineRegionResult.OfflineRegion.Size(
                storageSizeInBytes = storageSize,
                transferSizeInBytes = transferSize,
            ),
        ),
        area = value,
        areaUnitRes = unitRes,
        description = description,
        bounds = if (bounds != null) {
            LatLngBounds(
                LatLng(bounds.southwest.latitude, bounds.southwest.longitude),
                LatLng(bounds.northeast.latitude, bounds.northeast.longitude)
            )
        } else {
            null
        },
        boundaryUrl = boundaryUrl,
        maskUrl = maskUrl,
        downloadOrder = DownloadOrder(
            deviceSerialNumber = deviceSerial,
            downloadCompletedAt = downloadCompletedAt,
            status = status,
            downloadedSize = downloadedSize,
            sourceTypeUsed = null
        ),
        styleIds = styles.map { it.id }.toPersistentList(),
    )
}

private fun squareKmToSquareMi(squareKm: Double) =
    squareKm / 2.589988
