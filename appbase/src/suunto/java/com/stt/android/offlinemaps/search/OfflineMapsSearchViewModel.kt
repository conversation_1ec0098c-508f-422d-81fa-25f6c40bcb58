package com.stt.android.offlinemaps.search

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.offlinemaps.datasource.OfflineRegionRepository
import com.stt.android.offlinemaps.datasource.RemoteResultType
import com.stt.android.offlinemaps.entity.OfflineRegionListData
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.toPersistentList
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import timber.log.Timber
import javax.inject.Inject
import kotlin.time.Duration.Companion.milliseconds

@HiltViewModel
internal class OfflineMapsSearchViewModel @Inject constructor(
    private val offlineRegionRepository: OfflineRegionRepository,
) : ViewModel() {
    private val searchTerm: MutableStateFlow<String> = MutableStateFlow("")
    private val catalogue: MutableStateFlow<OfflineRegionListData.Catalogue> = MutableStateFlow(OfflineRegionListData.Catalogue())

    private val _viewState: MutableStateFlow<OfflineMapsSearchViewState> = MutableStateFlow(OfflineMapsSearchViewState.NoSearchTerm)
    val viewState: StateFlow<OfflineMapsSearchViewState> = _viewState.asStateFlow()

    init {
        observeSearch()
    }

    private fun observeSearch() {
        combine(
            searchTerm,
            catalogue,
            ::Pair,
        )
            .debounce(300.milliseconds)
            .onEach { (searchTerm, catalogue) ->
                if (searchTerm.isBlank() || catalogue.groups.isEmpty()) {
                    _viewState.value = OfflineMapsSearchViewState.NoSearchTerm
                    return@onEach
                }

                if (_viewState.value !is OfflineMapsSearchViewState.MatchesFound) {
                    _viewState.value = OfflineMapsSearchViewState.Searching(searchTerm)
                }

                val searchResult = runSuspendCatching {
                    val regionMap = catalogue.groups
                        .flatMap { it.regions }
                        .associateBy { it.id }
                    val groups = catalogue.groups
                    offlineRegionRepository.search(searchTerm)
                        .mapNotNull { searchResult ->
                            if (searchResult.type == RemoteResultType.REGION) {
                                regionMap[searchResult.id]
                            } else {
                                groups.firstOrNull { it.id == searchResult.id }
                            }
                        }.toPersistentList()
                }.getOrElse { e ->
                    Timber.w(e, "Failed to search with: $searchTerm")
                    persistentListOf()
                }
                _viewState.value = if (searchResult.isEmpty()) {
                    OfflineMapsSearchViewState.NoMatchFound(searchTerm)
                } else {
                    OfflineMapsSearchViewState.MatchesFound(searchTerm, searchResult)
                }
            }
            .launchIn(viewModelScope)
    }

    fun handleEvent(event: OfflineMapsSearchViewEvent) {
        when (event) {
            is OfflineMapsSearchViewEvent.ResetSearchTerm -> searchTerm.value = ""
            is OfflineMapsSearchViewEvent.UpdateCatalogue -> catalogue.value = event.catalogue
            is OfflineMapsSearchViewEvent.UpdateSearchTerm -> searchTerm.value = event.searchTerm
        }
    }
}
