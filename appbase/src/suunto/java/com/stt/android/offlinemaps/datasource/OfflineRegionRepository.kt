package com.stt.android.offlinemaps.datasource

import com.google.android.gms.maps.model.LatLng
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.coroutines.await
import com.stt.android.data.source.local.suuntoplusguide.WatchCapabilitiesDao
import com.stt.android.offlinemaps.entity.OfflineRegionListData
import com.stt.android.offlinemaps.entity.OfflineRegionResult
import com.stt.android.offlinemaps.entity.OfflineRegionSearchResult
import com.stt.android.watch.SuuntoWatchModel
import kotlinx.collections.immutable.ImmutableList
import kotlinx.coroutines.withContext
import javax.inject.Inject

interface OfflineRegionRepository {
    suspend fun getOfflineRegionCatalogue(
        latLng: LatLng? = null
    ): OfflineRegionListData.Catalogue

    suspend fun getCatalogueRegion(
        regionId: String,
        groupName: String? = null,
    ): OfflineRegionResult.OfflineRegion

    suspend fun getLibrary(): ImmutableList<OfflineRegionResult.OfflineRegion>

    suspend fun getLibraryRegion(
        regionId: String,
    ): OfflineRegionResult.OfflineRegion

    suspend fun search(searchTerm: String): ImmutableList<OfflineRegionSearchResult>
}

interface OfflineRegionDownloadRepository {
    suspend fun downloadOfflineRegion(
        regionId: String,
        groupName: String? = null
    ): OfflineRegionResult.OfflineRegion

    suspend fun deleteDownload(
        region: OfflineRegionResult.OfflineRegion
    ): OfflineRegionResult.OfflineRegion

    suspend fun getMapStorageSize(): Long?
}

class DefaultOfflineRegionRepository @Inject constructor(
    private val offlineRegionDataSource: OfflineRegionDataSource,
    private val watchCapabilitiesDao: WatchCapabilitiesDao,
    private val suuntoWatchModel: SuuntoWatchModel,
    private val dispatchers: CoroutinesDispatchers,
) : OfflineRegionRepository, OfflineRegionDownloadRepository {

    private lateinit var _watchCapabilities: List<String>
    private lateinit var _watchCapabilitiesAsString: String

    private suspend fun getWatchCapabilities() =
        if (::_watchCapabilitiesAsString.isInitialized) {
            _watchCapabilitiesAsString
        } else {
            val serial = suuntoWatchModel.currentWatch.await().serial
            _watchCapabilities = watchCapabilitiesDao.findForSerial(serial)?.capabilities
                ?: emptyList()
            _watchCapabilities.joinToString(separator = ",")
                .also { _watchCapabilitiesAsString = it }
        }

    //region OfflineRegionRepository

    override suspend fun getOfflineRegionCatalogue(
        latLng: LatLng?
    ): OfflineRegionListData.Catalogue {
        val currentWatch = suuntoWatchModel.currentWatch.await()
        return offlineRegionDataSource.getCatalogue(
            deviceSerial = currentWatch.serial,
            capabilities = getWatchCapabilities(),
            latLng = latLng,
            includeNearbyGroups = true,
        )
    }

    override suspend fun getCatalogueRegion(
        regionId: String,
        groupName: String?,
    ): OfflineRegionResult.OfflineRegion {
        val currentWatch = suuntoWatchModel.currentWatch.await()
        return offlineRegionDataSource.getCatalogueRegion(
            deviceSerial = currentWatch.serial,
            regionId = regionId,
            capabilities = getWatchCapabilities(),
            groupName = groupName,
        )
    }

    override suspend fun getLibrary(): ImmutableList<OfflineRegionResult.OfflineRegion> {
        val currentWatch = suuntoWatchModel.currentWatch.await()
        return offlineRegionDataSource.getLibrary(
            deviceSerial = currentWatch.serial,
            capabilities = getWatchCapabilities(),
        )
    }

    override suspend fun getLibraryRegion(
        regionId: String,
    ): OfflineRegionResult.OfflineRegion {
        val currentWatch = suuntoWatchModel.currentWatch.await()
        return offlineRegionDataSource.getLibraryRegion(
            deviceSerial = currentWatch.serial,
            regionId = regionId,
            capabilities = getWatchCapabilities(),
        )
    }

    override suspend fun search(searchTerm: String): ImmutableList<OfflineRegionSearchResult> =
        offlineRegionDataSource.search(searchTerm)

    //endregion

    //region OfflineRegionDownloadRepository

    override suspend fun downloadOfflineRegion(
        regionId: String,
        groupName: String?
    ): OfflineRegionResult.OfflineRegion {
        val currentWatch = suuntoWatchModel.currentWatch.await()
        return offlineRegionDataSource.putDownloadOrder(
            deviceSerial = currentWatch.serial,
            regionId = regionId,
            capabilities = getWatchCapabilities(),
            groupName = groupName
        )
    }

    // Can be used to delete downloaded maps from the watch or to cancel ongoing download
    override suspend fun deleteDownload(
        region: OfflineRegionResult.OfflineRegion
    ): OfflineRegionResult.OfflineRegion {
        val currentWatch = suuntoWatchModel.currentWatch.await()
        return offlineRegionDataSource.deleteRegion(
            deviceSerial = currentWatch.serial,
            region = region,
            capabilities = getWatchCapabilities()
        )
    }

    override suspend fun getMapStorageSize(): Long? = withContext(dispatchers.computation) {
        val prefix = "feat_mapstorage_"
        getWatchCapabilities()
        _watchCapabilities.firstOrNull { it.startsWith(prefix) }
            ?.let { capability ->
                capability.substringAfter(prefix).toLong() * 1000_000L
            }
    }

    //endregion
}
