package com.stt.android.offlinemaps

import android.annotation.SuppressLint
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.compose.LocalLifecycleOwner
import androidx.lifecycle.repeatOnLifecycle
import androidx.navigation.NavController
import kotlinx.coroutines.flow.SharedFlow

internal sealed interface OfflineMapsSelectionNavigationAction {
    fun NavController.navigate()

    data object NavigateUp : OfflineMapsSelectionNavigationAction {
        override fun NavController.navigate() {
            navigateUp()
        }
    }

    data object OpenPreview : OfflineMapsSelectionNavigationAction {
        override fun NavController.navigate() {
            popBackStack(
                route = OfflineMapsSelectionNavigation.OFFLINE_REGION_PREVIEW_ROUTE,
                inclusive = true,
            )
            navigate(OfflineMapsSelectionNavigation.OFFLINE_REGION_PREVIEW_ROUTE)
        }
    }

    data object OpenRegionGroup : OfflineMapsSelectionNavigationAction {
        override fun NavController.navigate() {
            popBackStack(
                route = OfflineMapsSelectionNavigation.OFFLINE_REGIONS_ROUTE,
                inclusive = true,
            )
            navigate(OfflineMapsSelectionNavigation.OFFLINE_REGIONS_ROUTE)
        }
    }
}

@SuppressLint("ComposableNaming")
@Composable
internal fun SharedFlow<OfflineMapsSelectionNavigationAction>.navigateOnResumed(
    navigate: (OfflineMapsSelectionNavigationAction) -> Unit,
) {
    val lifecycleOwner = LocalLifecycleOwner.current
    LaunchedEffect(this, lifecycleOwner) {
        lifecycleOwner.repeatOnLifecycle(Lifecycle.State.RESUMED) {
            collect(navigate)
        }
    }
}
