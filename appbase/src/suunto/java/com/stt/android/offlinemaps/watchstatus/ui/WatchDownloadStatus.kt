package com.stt.android.offlinemaps.watchstatus.ui

import android.content.Context
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Alignment.Companion.Center
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.tooling.preview.PreviewParameterProvider
import androidx.compose.ui.unit.dp
import com.stt.android.R
import com.stt.android.compose.component.SuuntoTextButton
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.material3.body
import com.stt.android.compose.theme.material3.header
import com.stt.android.compose.theme.spacing
import com.stt.android.offlinemaps.watchstatus.entities.WatchDownloadStatus
import com.stt.android.offlinemaps.watchstatus.entities.icon
import com.stt.android.offlinemaps.watchstatus.entities.subtitle
import com.stt.android.offlinemaps.watchstatus.entities.title
import com.stt.android.watch.wifi.WifiNetworksActivity

@Composable
internal fun WatchDownloadStatus(
    status: WatchDownloadStatus,
    modifier: Modifier = Modifier,
) {
    if (status == WatchDownloadStatus.UNKNOWN) {
        Box(
            modifier = Modifier.fillMaxWidth()
                .height(80.dp),
        ) {
            CircularProgressIndicator(
                modifier = Modifier.align(Center),
            )
        }
        return
    }

    Row(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = MaterialTheme.spacing.medium)
            .height(80.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Image(
            painter = painterResource(id = status.icon),
            contentDescription = null,
            modifier = Modifier.size(MaterialTheme.iconSizes.xlarge)
        )

        Column(
            modifier = Modifier
                .weight(1.0F)
                .padding(start = MaterialTheme.spacing.medium)
        ) {
            Text(
                text = stringResource(id = status.title),
                style = MaterialTheme.typography.header
            )

            Text(
                text = stringResource(id = status.subtitle),
                style = MaterialTheme.typography.body,
                color = MaterialTheme.colorScheme.secondary,
            )
        }

        val onClick = getClickHandler(status, LocalContext.current)
        if (onClick != null) {
            SuuntoTextButton(
                text = if (status == WatchDownloadStatus.WIRELESS_NETWORK_DISABLED) {
                    R.string.enable
                } else {
                    R.string.add
                },
                onClick = onClick,
                uppercase = false,
            )
        }
    }
}

private fun getClickHandler(
    status: WatchDownloadStatus,
    context: Context,
): (() -> Unit)? = when (status) {
    WatchDownloadStatus.WIRELESS_NETWORK_DISABLED -> {
        {
            context.startActivity(WifiNetworksActivity.newStartIntent(context))
        }
    }

    WatchDownloadStatus.WIFI_SETUP_MISSING -> {
        {
            context.startActivity(WifiNetworksActivity.newStartIntentToAddNetwork(context))
        }
    }

    WatchDownloadStatus.UNKNOWN,
    WatchDownloadStatus.NOT_CONNECTED_TO_APP,
    WatchDownloadStatus.BUSY,
    WatchDownloadStatus.NOT_CHARGING,
    WatchDownloadStatus.READY -> null
}

@Preview(showBackground = true)
@Composable
private fun WatchStatusPreview(
    @PreviewParameter(WatchStatusParamsProvider::class) downloadStatus: WatchDownloadStatus,
) {
    M3AppTheme {
        WatchDownloadStatus(
            status = downloadStatus,
            modifier = Modifier.fillMaxWidth()
        )
    }
}

private class WatchStatusParamsProvider : PreviewParameterProvider<WatchDownloadStatus> {
    override val values: Sequence<WatchDownloadStatus> =
        WatchDownloadStatus.entries.asSequence()
}
