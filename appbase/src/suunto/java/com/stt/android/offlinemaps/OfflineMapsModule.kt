package com.stt.android.offlinemaps

import android.app.Application
import com.squareup.moshi.Moshi
import com.stt.android.home.explore.offlinemaps.OfflineMapsTabAnalytics
import com.stt.android.offlinemaps.analytics.DefaultOfflineMapsAnalytics
import com.stt.android.offlinemaps.analytics.OfflineMapsAnalytics
import com.stt.android.offlinemaps.common.OfflineRegionDownloadOperators
import com.stt.android.offlinemaps.common.OfflineRegionDownloadOperatorsDelegate
import com.stt.android.offlinemaps.datasource.DefaultOfflineRegionRepository
import com.stt.android.offlinemaps.datasource.OfflineRegionDataSource
import com.stt.android.offlinemaps.datasource.OfflineRegionDownloadRepository
import com.stt.android.offlinemaps.datasource.OfflineRegionRemoteDataSource
import com.stt.android.offlinemaps.datasource.OfflineRegionRepository
import com.stt.android.offlinemaps.datasource.OfflineRegionRestApi
import com.stt.android.remote.AuthProvider
import com.stt.android.remote.BaseUrl
import com.stt.android.remote.SharedOkHttpClient
import com.stt.android.remote.UserAgent
import com.stt.android.remote.di.BrandOkHttpConfigFactory
import com.stt.android.remote.di.RestApiFactory
import com.stt.android.watch.offlinemaps.datasource.MapDownloadDataSource
import com.stt.android.watch.offlinemaps.datasource.WatchMapDownloadDataSource
import dagger.Binds
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import okhttp3.Cache
import okhttp3.OkHttpClient
import java.io.File

@Module
@InstallIn(SingletonComponent::class)
abstract class OfflineMapsModule {

    @Binds
    abstract fun bindDownloadOperators(
        downloadOperatorsDelegate: OfflineRegionDownloadOperatorsDelegate
    ): OfflineRegionDownloadOperators

    @Binds
    abstract fun bindMapDownloadDataSource(
        dataSource: WatchMapDownloadDataSource
    ): MapDownloadDataSource

    @Binds
    abstract fun bindOfflineRegionDataSource(
        dataSource: OfflineRegionRemoteDataSource
    ): OfflineRegionDataSource

    @Binds
    abstract fun bindOfflineRegionRepository(
        repository: DefaultOfflineRegionRepository
    ): OfflineRegionRepository

    @Binds
    abstract fun bindOfflineRegionDownloadRepository(
        repository: DefaultOfflineRegionRepository
    ): OfflineRegionDownloadRepository

    @Binds
    abstract fun bindOfflineMapsAnalytics(
        analytics: DefaultOfflineMapsAnalytics
    ): OfflineMapsAnalytics

    @Binds
    abstract fun bindOfflineMapsTabAnalytics(
        analytics: DefaultOfflineMapsAnalytics
    ): OfflineMapsTabAnalytics

    companion object {
        @Provides
        fun provideOfflineRegionRestApi(
            @SharedOkHttpClient sharedClient: OkHttpClient,
            @BaseUrl baseUrl: String,
            @UserAgent userAgent: String,
            authProvider: AuthProvider,
            moshi: Moshi,
            application: Application
        ) = RestApiFactory.buildRestApi(
            sharedClient,
            baseUrl,
            OfflineRegionRestApi::class.java,
            BrandOkHttpConfigFactory.getStOkHttpConfig(authProvider, userAgent),
            moshi,
            Cache(File(application.cacheDir, "offline-region-cache"), 1024 * 1024 * 2)
        )
    }
}
