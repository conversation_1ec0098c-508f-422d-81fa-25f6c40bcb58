package com.stt.android.offlinemaps.search

import com.stt.android.offlinemaps.entity.OfflineRegionListData

internal sealed interface OfflineMapsSearchViewEvent {
    data object ResetSearchTerm : OfflineMapsSearchViewEvent

    data class UpdateCatalogue(
        val catalogue: OfflineRegionListData.Catalogue,
    ) : OfflineMapsSearchViewEvent

    data class UpdateSearchTerm(
        val searchTerm: String,
    ) : OfflineMapsSearchViewEvent
}

internal typealias OfflineMapsSearchEventHandler = (OfflineMapsSearchViewEvent) -> Unit
