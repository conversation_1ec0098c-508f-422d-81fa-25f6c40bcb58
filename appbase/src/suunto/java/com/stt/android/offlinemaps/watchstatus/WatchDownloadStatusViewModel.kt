package com.stt.android.offlinemaps.watchstatus

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.stt.android.domain.watch.IsWatchBusyUseCase
import com.stt.android.domain.watch.IsWatchConnectedUseCase
import com.stt.android.offlinemaps.watchstatus.entities.WatchDownloadStatus
import com.stt.android.watch.device.GetUsbCableStateUseCase
import com.stt.android.watch.wifi.domain.GetSavedWifiNetworksCountUseCase
import com.stt.android.watch.wifi.domain.GetWifiEnabledUseCase
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

@HiltViewModel
internal class WatchDownloadStatusViewModel @Inject constructor(
    private val isWatchConnectedUseCase: IsWatchConnectedUseCase,
    private val getSavedWifiNetworksCountUseCase: GetSavedWifiNetworksCountUseCase,
    private val isWatchBusyUseCase: IsWatchBusyUseCase,
    private val wifiEnabledUseCase: GetWifiEnabledUseCase,
    private val getUsbCableStateUseCase: GetUsbCableStateUseCase,
) : ViewModel() {
    private val _watchDownloadStatus: MutableStateFlow<WatchDownloadStatus> = MutableStateFlow(WatchDownloadStatus.UNKNOWN)
    val watchDownloadStatus: StateFlow<WatchDownloadStatus> = _watchDownloadStatus.asStateFlow()

    init {
        observeWatchDownloadStatus()
}

    private fun observeWatchDownloadStatus() {
        viewModelScope.launch {
            isWatchConnectedUseCase.invoke()
                .catch { e -> Timber.w(e, "Unable to get watch connection state.") }
                .flatMapLatest { isWatchConnected ->
                    if (!isWatchConnected) {
                        return@flatMapLatest flowOf(WatchDownloadStatus.NOT_CONNECTED_TO_APP)
                    }

                    combine(
                        isWatchBusyUseCase()
                            .catch { Timber.w(it, "Failed to get watch busy state.") },
                        wifiEnabledUseCase()
                            .catch { Timber.w(it, "Failed to get wifi enabled state.") },
                        getSavedWifiNetworksCountUseCase()
                            .map { it > 0 }
                            .catch { Timber.w(it, "Failed to get saved networks count.") },
                        getUsbCableStateUseCase()
                            .map { it.connected }
                            .catch { Timber.w(it, "Failed to get usb cable state.") }
                    ) { isBusy, wifiEnabled, hasSavedWifi, cableConnected ->
                        when {
                            isBusy -> WatchDownloadStatus.BUSY
                            !wifiEnabled -> WatchDownloadStatus.WIRELESS_NETWORK_DISABLED
                            !hasSavedWifi -> WatchDownloadStatus.WIFI_SETUP_MISSING
                            !cableConnected -> WatchDownloadStatus.NOT_CHARGING
                            else -> WatchDownloadStatus.READY
                        }
                    }
                }.collectLatest { status ->
                    _watchDownloadStatus.value = status
                }
        }
    }
}
