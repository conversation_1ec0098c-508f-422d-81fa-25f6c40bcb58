package com.stt.android.qrcode

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.MenuItem
import android.view.WindowManager
import androidx.activity.result.contract.ActivityResultContract
import androidx.activity.viewModels
import androidx.camera.core.*
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.core.content.ContextCompat
import androidx.core.view.doOnLayout
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import com.google.common.util.concurrent.ListenableFuture
import com.stt.android.R
import com.stt.android.common.ui.ViewModelActivity2
import com.stt.android.databinding.ActivityQrcodeScanBinding
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.util.concurrent.Executors

class QRCodeScanActivity : ViewModelActivity2() {
    override val viewModel: QRCodeScanViewModel by viewModels()
    private val viewBinding: ActivityQrcodeScanBinding get() = requireBinding()
    override fun getLayoutResId(): Int = R.layout.activity_qrcode_scan

    private lateinit var cameraProviderFuture: ListenableFuture<ProcessCameraProvider>

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(viewBinding.root)
        setSupportActionBar(findViewById(R.id.toolbar))
        setTitle(R.string.device_scan_qrcode_title)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        cameraProviderFuture = ProcessCameraProvider.getInstance(this)
        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
        viewBinding.overlay.doOnLayout {
            cameraProviderFuture.addListener({
                val cameraProvider = cameraProviderFuture.get()
                bindScan(cameraProvider)
            }, ContextCompat.getMainExecutor(this@QRCodeScanActivity))
        }
    }

    override fun onPause() {
        super.onPause()
        viewModel.torchOn.set(false)
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        if (item.itemId == android.R.id.home) {
            finish()
            return true
        }
        return super.onOptionsItemSelected(item)
    }

    override fun onDestroy() {
        super.onDestroy()
        window.clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
    }

    @SuppressLint("UnsafeExperimentalUsageError")
    private fun bindScan(cameraProvider: ProcessCameraProvider) {
        val targetSchemes = intent.getStringArrayExtra(TARGET_SCHEME_ARRAY)
            ?: throw UnsupportedOperationException("Need target scheme array")
        val preview = Preview.Builder().build().apply {
            setSurfaceProvider(viewBinding.previewView.surfaceProvider)
        }
        val cameraSelector: CameraSelector = CameraSelector.Builder()
            .requireLensFacing(CameraSelector.LENS_FACING_BACK)
            .build()
        val imageAnalysis = ImageAnalysis.Builder()
            .setBackpressureStrategy(ImageAnalysis.STRATEGY_KEEP_ONLY_LATEST)
            .build()
        val analyser = QRCodeAnalyser(this, viewBinding.overlay, { result ->
            targetSchemes.any { result.startsWith(it) }
        }) { barcodeResult ->
            cameraProvider.unbindAll()
            lifecycleScope.launch {
                delay(1000) // For showing the UI that the scan has ended
                setResult(Activity.RESULT_OK, Intent().apply {
                    putExtra(SCAN_RESULT, barcodeResult.mapCatching { it.rawValue })
                })
                finish()
            }
        }
        imageAnalysis.setAnalyzer(Executors.newSingleThreadExecutor(), analyser)
        viewModel.camera = cameraProvider.bindToLifecycle(
            this as LifecycleOwner,
            cameraSelector,
            imageAnalysis,
            preview
        )
    }

    class ResultContract : ActivityResultContract<Array<String>, Result<String>>() {
        override fun createIntent(context: Context, input: Array<String>): Intent = Intent().apply {
            setClass(context, QRCodeScanActivity::class.java)
            putExtra(TARGET_SCHEME_ARRAY, input)
        }

        override fun parseResult(resultCode: Int, intent: Intent?): Result<String> {
            if (resultCode == RESULT_OK) {
                (intent?.getSerializableExtra(SCAN_RESULT) as? Result<String>)?.let {
                    return it
                }
                    ?: return Result.failure(QRCodeScanFailureException("No available QR code scanned"))
            }
            return Result.failure(QRCodeScanFailureException("Scan QR code canceled"))
        }
    }

    companion object {
        private const val SCAN_RESULT = "com.stt.android.SCAN_RESULT"
        private const val TARGET_SCHEME_ARRAY = "target_scheme_array"
    }
}
