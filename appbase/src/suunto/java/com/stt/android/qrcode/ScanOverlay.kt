package com.stt.android.qrcode

import android.animation.ObjectAnimator
import android.content.Context
import android.content.res.Resources
import android.graphics.*
import android.util.AttributeSet
import android.util.TypedValue
import android.view.View
import com.stt.android.R


class ScanOverlay @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    private val rectWidth = 286f.toPx()
    private val innerWidth = 270f.toPx()
    private val lineRangeWidth = 260f.toPx()
    private val lineRangeHeight = 200f.toPx()
    private val rectRadius = 12f.toPx().toFloat()
    private val marginTop = 107f.toPx()
    private val resultCircleInnerRadius = 22f.toPx().toFloat()
    private val resultCircleOuterRadius = 24f.toPx().toFloat()

    private val animator = ObjectAnimator.ofFloat(
        this,
        "floatYFraction",
        0f,
        1f
    ).apply {
        duration = 2000
        repeatMode = ObjectAnimator.REVERSE
        repeatCount = ObjectAnimator.INFINITE
    }

    private val lineBitmap: Bitmap by lazy {
        BitmapFactory.decodeResource(resources, R.drawable.device_scan_qrcode_line)
    }
    private val rectBitmap: Bitmap by lazy {
        BitmapFactory.decodeResource(resources, R.drawable.device_scan_qrcode_rect)
    }

    private var resultRect: RectF? = null
    private var showLine = true

    private val contentPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        style = Paint.Style.FILL
        color = context.getColor(R.color.suunto_blue)
        strokeWidth = 3f.toPx().toFloat()
    }
    private val backgroundPaint: Paint = Paint().apply {
        color = 0x80303030.toInt()
    }
    private val backgroundPath: Path = Path()

    private val bitmapFromRect: Rect = Rect()
    private val bitmapToRect: Rect = Rect()
    private val backgroundRect: RectF = RectF()

    private var floatYFraction = 0f
        set(value) {
            field = value
            invalidate()
        }

    val scanningRect: RectF = RectF()

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        val cx = width / 2
        val cy = marginTop + rectWidth / 2

        // background with hole
        backgroundRect.set(0f, 0f, width.toFloat(), height.toFloat())
        backgroundPath.addRect(
            backgroundRect,
            Path.Direction.CW
        )
        scanningRect.set(
            cx - innerWidth / 2f,
            cy - innerWidth / 2f,
            cx + innerWidth / 2f,
            cy + innerWidth / 2f
        )
        backgroundPath.addRoundRect(
            scanningRect,
            rectRadius,
            rectRadius,
            Path.Direction.CCW
        )
        canvas.drawPath(backgroundPath, backgroundPaint)
        backgroundPath.reset()

        // scanning rect
        bitmapFromRect.set(0, 0, rectBitmap.width, rectBitmap.height)
        bitmapToRect.set(
            cx - rectWidth / 2,
            cy - rectWidth / 2,
            cx + rectWidth / 2,
            cy + rectWidth / 2
        )
        canvas.drawBitmap(rectBitmap, bitmapFromRect, bitmapToRect, contentPaint)

        // scanning line
        if (showLine) {
            bitmapFromRect.set(0, 0, lineBitmap.width, lineBitmap.height)
            bitmapToRect.set(
                cx - lineRangeWidth / 2,
                (cy - lineRangeHeight / 2 + lineRangeHeight * floatYFraction).toInt(),
                cx + lineRangeWidth / 2,
                (cy - lineRangeHeight / 2 + lineRangeHeight * floatYFraction + lineRangeHeight * lineBitmap.height / lineBitmap.width).toInt(),
            )
            canvas.drawBitmap(lineBitmap, bitmapFromRect, bitmapToRect, contentPaint)
        }

        // result point
        resultRect?.let { rect ->
            contentPaint.color = Color.WHITE
            canvas.drawCircle(
                scanningRect.centerX(),
                scanningRect.centerY(),
                resultCircleOuterRadius,
                contentPaint
            )
            contentPaint.color = context.getColor(R.color.suunto_blue)
            canvas.drawCircle(
                scanningRect.centerX(),
                scanningRect.centerY(),
                resultCircleInnerRadius,
                contentPaint
            )
        }

    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        animator.start()
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        animator.cancel()
    }

    private fun Float.toPx(): Int {
        val resources = Resources.getSystem()
        return TypedValue.applyDimension(
            TypedValue.COMPLEX_UNIT_DIP,
            this,
            resources.displayMetrics
        ).toInt()
    }

    fun addRect(rect: RectF) {
        showLine = false
        resultRect = rect
        animator.cancel()
        invalidate()
    }
}
