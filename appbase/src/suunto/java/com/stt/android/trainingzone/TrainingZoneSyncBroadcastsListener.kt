package com.stt.android.trainingzone

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.stt.android.coroutines.await
import com.stt.android.coroutines.throttleLatest
import com.stt.android.utils.STTConstants
import com.stt.android.watch.SuuntoWatchModel
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.channels.BufferOverflow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class TrainingZoneSyncBroadcastsListener
@Inject constructor(
    localBroadcastManager: LocalBroadcastManager,
    private val suuntoWatchModel: SuuntoWatchModel
) {
    private val scope = CoroutineScope(Dispatchers.IO)
    private val _broadcastAction = MutableSharedFlow<String?>(
        replay = 1,
        onBufferOverflow = BufferOverflow.DROP_OLDEST
    )

    private val receiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            intent.action?.let { action ->
                _broadcastAction.tryEmit(action)
            }
        }
    }

    private val intentFilter = IntentFilter().apply {
        addAction(STTConstants.BroadcastActions.WORKOUT_DELETED)
        addAction(STTConstants.BroadcastActions.WORKOUT_UPDATED)
        addAction(STTConstants.BroadcastActions.WORKOUT_SYNCED)
        addAction(STTConstants.BroadcastActions.WORKOUT_SAVED)
        addAction(STTConstants.BroadcastActions.WORKOUT_FETCHED)
        addAction(STTConstants.BroadcastActions.MANUAL_WORKOUT_SAVED)
        addAction(STTConstants.BroadcastActions.USER_SETTINGS_FIRST_DAY_OF_WEEK_CHANGED)
    }

    init {
        localBroadcastManager.registerReceiver(receiver, intentFilter)
        scope.launch {
            _broadcastAction
                .throttleLatest(TRAINING_ZONE_SYNC_THROTTLE_TIME_MILLIS)
                .filterNotNull()
                .catch {
                    Timber.d(
                        it,
                        "Catch exception while listening to training zone sync broadcast action"
                    )
                }
                .collectLatest { action ->
                    Timber.d("Broadcast $action is received")
                    runCatching {
                        val syncResponse = suuntoWatchModel.requestTrainingZoneSync().await()
                        Timber.d("Training zone sync success=${syncResponse.success}, errorMessage=${syncResponse.errorMessage}")
                    }.onFailure {
                        Timber.d(it, "Failed to sync training zone")
                    }
                }
        }
    }

    companion object {
        private const val TRAINING_ZONE_SYNC_THROTTLE_TIME_MILLIS = 5000L
    }
}
