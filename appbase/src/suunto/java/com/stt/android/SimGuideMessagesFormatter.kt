package com.stt.android

import android.content.res.Resources
import com.soy.algorithms.planner.GuideMessagesFormatter
import com.soy.algorithms.planner.WorkoutStep
import com.stt.android.infomodel.SummaryItem
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.utils.displayNameResource
import com.suunto.algorithms.data.HeartRate.Companion.bpm
import timber.log.Timber
import java.util.Locale
import javax.inject.Inject
import kotlin.math.roundToInt
import com.stt.android.R as BaseR

class SimGuideMessagesFormatter
@Inject constructor(
    private val resources: Resources,
    private val infoModelFormatter: InfoModelFormatter
) : GuideMessagesFormatter {
    override fun formatCadence(valueHz: Double): Pair<String, String> =
        formatWithUnit(SummaryItem.AVGCADENCE, valueHz)

    override fun formatDistance(valueMeters: Double): Pair<String, String> =
        formatWithUnit(SummaryItem.DISTANCE, valueMeters)

    override fun formatDuration(valueSeconds: Double): String {
        val totalSeconds = valueSeconds.roundToInt()
        val hours = totalSeconds / 60 / 60
        val minutes = (totalSeconds / 60) % 60
        val seconds = totalSeconds % 60

        return if (hours > 0) {
            String.format(Locale.ROOT, "%d:%02d'%02d", hours, minutes, seconds)
        } else {
            String.format(Locale.ROOT, "%d'%02d", minutes, seconds)
        }
    }

    override fun formatHeartRate(valueBPM: Double): Pair<String, String> =
        formatWithUnit(SummaryItem.AVGHEARTRATE, valueBPM.bpm.inHz)

    override fun formatPaceFromSpeed(valueMPS: Double): Pair<String, String> {
        val paceAsSeconds = 60.0 * infoModelFormatter.unit.toPaceUnit(valueMPS)
        return formatDuration(paceAsSeconds) to resources.getString(infoModelFormatter.unit.paceUnit)
    }

    override fun formatPower(valueWatts: Double): Pair<String, String> =
        formatWithUnit(SummaryItem.AVGPOWER, valueWatts)

    override fun formatSpeed(valueMPS: Double): Pair<String, String> =
        formatWithUnit(SummaryItem.AVGSPEED, valueMPS)

    override fun formatSwimmingPaceFromSpeed(valueMPS: Double): Pair<String, String> =
        formatWithUnit(SummaryItem.AVGSWIMPACE, valueMPS)

    override fun getFinishedLocalized(): String =
        resources.getString(BaseR.string.workout_planner_guide_notification_finished)


    override fun getGuideEndedLocalized(): String =
        resources.getString(BaseR.string.workout_planner_guide_notification_end)

    override fun getNextLocalized(): String =
        resources.getString(BaseR.string.workout_planner_guide_notification_next)

    override fun getPhaseLocalized(phase: WorkoutStep.Exercise.Phase): String =
        resources.getString(phase.displayNameResource)

    override fun getSplitNameLocalized(i: Int): String = "Split $i" // TODO localize when race plans are supported

    override fun getStepsLocalized(count: Int): String =
        resources.getString(BaseR.string.workout_planner_guide_notification_steps)

    override fun getTargetLocalized(): String =
        resources.getString(BaseR.string.workout_planner_guide_notification_target)

    override fun getUntilLapButtonLocalized(): String =
        resources.getString(BaseR.string.workout_planner_guide_step_duration_until_lap_button_press)

    private fun formatWithUnit(summaryItem: SummaryItem, value: Double): Pair<String, String> =
        try {
            infoModelFormatter.formatValue(summaryItem, value).let {
                it.value.orEmpty() to it.getUnitLabel(infoModelFormatter.context)
            }
        } catch (e: Exception) {
            Timber.w(e, "Failed to format $summaryItem value $value")
            "" to resources.getString(BaseR.string.rpm)
        }
}
