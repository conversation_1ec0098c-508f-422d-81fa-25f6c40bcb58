<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
    <uses-permission android:name="android.permission.REQUEST_COMPANION_RUN_IN_BACKGROUND" />
    <uses-feature android:required="false" android:name="android.software.companion_device_setup" />
    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />
    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES"
        tools:ignore="QueryAllPackagesPermission" />

    <application>
        <receiver
            android:name=".systemwidget.CaloriesDashboardWidgetAsSystemWidgetProvider"
            android:label="@string/dashboard_widget_calories_name"
            android:exported="true">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>
            <meta-data android:name="android.appwidget.provider"
                android:resource="@xml/systemwidget_calories_info" />
        </receiver>
        
        <receiver android:name=".systemwidget.ResourcesDashboardWidgetAsSystemWidgetProvider"
            android:label="@string/dashboard_widget_resources_name"
            android:exported="true">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>
            <meta-data android:name="android.appwidget.provider"
                android:resource="@xml/systemwidget_resources_info" />
        </receiver>

        <receiver android:name=".systemwidget.SleepDashboardWidgetAsSystemWidgetProvider"
            android:label="@string/dashboard_widget_sleep_name"
            android:exported="true">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>
            <meta-data android:name="android.appwidget.provider"
                android:resource="@xml/systemwidget_sleep_info" />
        </receiver>

        <receiver android:name=".systemwidget.StepsDashboardWidgetAsSystemWidgetProvider"
            android:label="@string/dashboard_widget_steps_name"
            android:exported="true">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>
            <meta-data android:name="android.appwidget.provider"
                android:resource="@xml/systemwidget_steps_info" />
        </receiver>

        <receiver android:name=".systemwidget.MinimumHeartRateDashboardWidgetAsSystemWidgetProvider"
            android:label="@string/dashboard_widget_minimum_heart_rate_selection_list_name"
            android:exported="true">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>
            <meta-data android:name="android.appwidget.provider"
                android:resource="@xml/systemwidget_minimum_heart_rate_info" />
        </receiver>

        <receiver
            android:name=".glance.DailyOverviewHomeWidgetReceiver"
            android:exported="true"
            android:label="@string/home_widget_daily_overview_name">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>
            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/home_widget_daily_overview" />
        </receiver>

        <activity android:name="com.stt.android.social.userprofile.SupportActivity"
            android:theme="@style/WhiteTheme"
            />
        <activity android:name="com.stt.android.social.userprofile.ChatBotActivity"
            android:theme="@style/WhiteTheme"
            />
        <activity android:name=".ui.activities.settings.ManageConnectionActivity"
            android:theme="@style/WhiteTheme"/>
        <activity
            android:label="@string/new_email_address"
            android:name="com.stt.android.newemail.NewEmailActivity"
            android:theme="@style/WhiteTheme"/>

        <activity android:name=".home.devicetype.DeviceTypeSelectActivity"
            android:theme="@style/WhiteTheme" />

        <!-- The designer suggests that this page be forced to be vertical to avoid UI issues -->
        <activity
            android:name=".qrcode.QRCodeScanActivity"
            android:stateNotNeeded="true"
            android:screenOrientation="portrait"
            android:configChanges="orientation|screenSize"
            android:theme="@style/WhiteTheme"
            tools:ignore="DiscouragedApi" />

        <activity android:name=".watch.preference.SetupPreferenceActivity"
            android:configChanges="orientation|screenSize"
            android:theme="@style/WhiteTheme" />

        <activity
            android:name=".watch.preference.notification.SetupPreferenceAppNotificationsActivity"
            android:theme="@style/WhiteTheme" />

        <activity
            android:label="@string/feedback"
            android:name="com.stt.android.home.feedback.FeedbackActivity"
            android:configChanges="orientation|screenSize"
            android:theme="@style/WhiteTheme" />
    </application>
</manifest>
