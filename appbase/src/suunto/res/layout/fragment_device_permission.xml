<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <import type="android.view.View" />
        <variable
            name="viewModel"
            type="com.stt.android.watch.permission.DevicePermissionViewModel" />
    </data>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <ImageView
            android:id="@+id/device_background"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/device_image_height"
            android:layout_gravity="center_horizontal"
            android:src="@drawable/watch_activity_ghost_white" />

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:fillViewport="true">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/device_permission_constraints"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingEnd="@dimen/padding"
                android:paddingStart="@dimen/padding">

                <Space
                    android:id="@+id/device_background_space"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_marginBottom="@dimen/padding"
                    app:layout_constraintBottom_toTopOf="@+id/device_permission_needed_textview"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHeight_max="@dimen/device_image_height"
                    app:layout_constraintHeight_min="@dimen/device_image_height_half"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/device_permission_needed_textview"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="@dimen/bigger_padding"
                    android:gravity="center"
                    android:lineSpacingMultiplier="1.3"
                    android:text="@string/permission_required"
                    app:layout_constraintBottom_toTopOf="@+id/device_need_location_access_textview"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    style="@style/HeaderLabel.Large" />

                <TextView
                    android:id="@+id/device_need_location_access_textview"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="@dimen/bigger_padding"
                    android:layout_marginEnd="@dimen/item_spacing"
                    android:layout_marginStart="@dimen/item_spacing"
                    android:gravity="center"
                    android:lineSpacingMultiplier="1.3"
                    android:text="@string/ble_need_location_access"
                    app:layout_constraintBottom_toTopOf="@+id/device_allow_location_button"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    style="@style/Body.Medium" />

                <Button
                    android:id="@+id/device_allow_location_button"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="@dimen/bigger_padding"
                    android:layout_marginEnd="@dimen/item_spacing"
                    android:layout_marginStart="@dimen/item_spacing"
                    android:onClick="@{() -> viewModel.onAllowLocationClick()}"
                    android:text="@string/allow_location"
                    android:textAllCaps="true"
                    android:visibility="@{safeUnbox(viewModel.sharedViewModel.locationAllowed) ? View.INVISIBLE : View.VISIBLE}"
                    app:layout_constraintBottom_toTopOf="@+id/device_turn_on_location_button"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    tools:visibility="invisible"
                    style="@style/Button.Primary" />

                <TextView
                    android:id="@+id/device_allow_location_textview"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/allow_location"
                    android:textAllCaps="true"
                    app:layout_constraintBottom_toBottomOf="@id/device_allow_location_button"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="@id/device_allow_location_button"
                    style="@style/HeaderLabel.Medium" />

                <ImageView
                    android:id="@+id/device_allow_location_imageview"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/item_spacing"
                    android:tint="?android:attr/textColorPrimary"
                    app:layout_constraintBottom_toBottomOf="@id/device_allow_location_button"
                    app:layout_constraintStart_toEndOf="@+id/device_allow_location_textview"
                    app:layout_constraintTop_toTopOf="@id/device_allow_location_button"
                    app:srcCompat="@drawable/ic_check_white_24" />

                <androidx.constraintlayout.widget.Group
                    android:id="@+id/device_allow_location_group"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:visibility="@{safeUnbox(viewModel.sharedViewModel.locationAllowed) ? View.VISIBLE : View.GONE}"
                    app:constraint_referenced_ids="device_allow_location_textview,device_allow_location_imageview" />

                <Button
                    android:id="@+id/device_turn_on_location_button"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="@dimen/bigger_padding"
                    android:layout_marginEnd="@dimen/item_spacing"
                    android:layout_marginStart="@dimen/item_spacing"
                    android:onClick="@{() -> viewModel.onEnableLocationClick()}"
                    android:text="@string/turn_on_location"
                    android:textAllCaps="true"
                    android:visibility="@{safeUnbox(viewModel.sharedViewModel.locationEnabled) ? View.INVISIBLE : View.VISIBLE}"
                    app:layout_constraintBottom_toTopOf="@+id/device_allow_nearby_devices_button"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    tools:visibility="visible"
                    style="@style/Button.Primary" />

                <TextView
                    android:id="@+id/device_turn_on_location_textview"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/turn_on_location"
                    android:textAllCaps="true"
                    app:layout_constraintBottom_toBottomOf="@id/device_turn_on_location_button"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="@id/device_turn_on_location_button"
                    style="@style/HeaderLabel.Medium" />

                <ImageView
                    android:id="@+id/device_turn_on_location_imageview"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/item_spacing"
                    android:tint="?android:attr/textColorPrimary"
                    app:layout_constraintBottom_toBottomOf="@id/device_turn_on_location_button"
                    app:layout_constraintStart_toEndOf="@+id/device_turn_on_location_textview"
                    app:layout_constraintTop_toTopOf="@id/device_turn_on_location_button"
                    app:srcCompat="@drawable/ic_check_white_24" />

                <androidx.constraintlayout.widget.Group
                    android:id="@+id/device_location_on_group"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:visibility="@{safeUnbox(viewModel.sharedViewModel.locationEnabled) ? View.VISIBLE : View.GONE}"
                    app:constraint_referenced_ids="device_turn_on_location_textview,device_turn_on_location_imageview" />

                <Button
                    android:id="@+id/device_allow_nearby_devices_button"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="@dimen/bottommost_item_margin"
                    android:layout_marginEnd="@dimen/item_spacing"
                    android:layout_marginStart="@dimen/item_spacing"
                    android:onClick="@{() -> viewModel.onAllowNearbyDevices()}"
                    android:text="@string/allow_nearby_devices"
                    android:textAllCaps="true"
                    android:visibility="@{safeUnbox(viewModel.sharedViewModel.nearbyDevicesRequired) ? safeUnbox(viewModel.sharedViewModel.nearbyDevicesAllowed) ? View.INVISIBLE : View.VISIBLE : View.GONE}"
                    app:layout_constraintBottom_toTopOf="@id/device_turn_on_bluetooth_button"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    tools:visibility="visible"
                    style="@style/Button.Primary" />

                <TextView
                    android:id="@+id/device_allow_nearby_devices_textview"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/allow_nearby_devices"
                    android:textAllCaps="true"
                    app:layout_constraintBottom_toBottomOf="@id/device_allow_nearby_devices_button"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="@id/device_allow_nearby_devices_button"
                    style="@style/HeaderLabel.Medium" />

                <ImageView
                    android:id="@+id/device_allow_nearby_devices_imageview"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/item_spacing"
                    android:tint="@color/black"
                    app:layout_constraintBottom_toBottomOf="@id/device_allow_nearby_devices_button"
                    app:layout_constraintStart_toEndOf="@id/device_allow_nearby_devices_textview"
                    app:layout_constraintTop_toTopOf="@id/device_allow_nearby_devices_button"
                    app:srcCompat="@drawable/ic_check_white_24" />

                <androidx.constraintlayout.widget.Group
                    android:id="@+id/device_nearby_devices_group"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:visibility="@{safeUnbox(viewModel.sharedViewModel.nearbyDevicesAllowed) &amp;&amp; safeUnbox(viewModel.sharedViewModel.nearbyDevicesRequired) ? View.VISIBLE : View.GONE}"
                    app:constraint_referenced_ids="device_allow_nearby_devices_textview,device_allow_nearby_devices_imageview" />

                <Button
                    android:id="@+id/device_turn_on_bluetooth_button"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="@dimen/bottommost_item_margin"
                    android:layout_marginEnd="@dimen/item_spacing"
                    android:layout_marginStart="@dimen/item_spacing"
                    android:onClick="@{() -> viewModel.onEnableBluetoothClick()}"
                    android:text="@string/turn_on_bluetooth"
                    android:textAllCaps="true"
                    android:visibility="@{safeUnbox(viewModel.sharedViewModel.bluetoothEnabled) || safeUnbox(!viewModel.sharedViewModel.nearbyDevicesAllowed) ? View.INVISIBLE : View.VISIBLE}"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    tools:visibility="visible"
                    style="@style/Button.Primary" />

                <TextView
                    android:id="@+id/device_turn_on_bluetooth_textview"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/turn_on_bluetooth"
                    android:textAllCaps="true"
                    app:layout_constraintBottom_toBottomOf="@id/device_turn_on_bluetooth_button"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="@id/device_turn_on_bluetooth_button"
                    style="@style/HeaderLabel.Medium" />

                <ImageView
                    android:id="@+id/turn_on_bluetooth_imageview"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/item_spacing"
                    android:tint="@color/black"
                    app:layout_constraintBottom_toBottomOf="@id/device_turn_on_bluetooth_button"
                    app:layout_constraintStart_toEndOf="@id/device_turn_on_bluetooth_textview"
                    app:layout_constraintTop_toTopOf="@id/device_turn_on_bluetooth_button"
                    app:srcCompat="@drawable/ic_check_white_24" />

                <androidx.constraintlayout.widget.Group
                    android:id="@+id/device_bluetooth_group"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:visibility="@{safeUnbox(viewModel.sharedViewModel.bluetoothEnabled) ? View.VISIBLE : View.GONE}"
                    app:constraint_referenced_ids="device_turn_on_bluetooth_textview,turn_on_bluetooth_imageview" />

            </androidx.constraintlayout.widget.ConstraintLayout>
        </ScrollView>

    </FrameLayout>
</layout>
