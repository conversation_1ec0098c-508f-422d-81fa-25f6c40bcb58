{"nm": "Voice", "ddd": 0, "h": 13, "w": 31, "meta": {"g": "LottieFiles Figma v86"}, "layers": [{"ty": 0, "nm": "box", "sr": 1, "st": 0, "op": 91.3, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [14.6, 6.6], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [15.4, 6.6], "t": 18}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [15.4, 6.6], "t": 36}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [15.4, 6.6], "t": 54}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [15.4, 6.6], "t": 72}, {"s": [14.6, 6.6], "t": 90}]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [16, 6.6], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [15.4, 6.6], "t": 18}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [15.4, 6.6], "t": 36}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [15.4, 6.6], "t": 54}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [15.4, 6.6], "t": 72}, {"s": [16, 6.6], "t": 90}]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "w": 31, "h": 13.199999809265137, "refId": "1", "ind": 1}], "v": "5.7.0", "fr": 60, "op": 90.3, "ip": 0, "assets": [{"nm": "[Asset] box", "id": "1", "layers": [{"ty": 0, "nm": "bars", "sr": 1, "st": 0, "op": 91.3, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [14.6, 6.6], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [15.4, 6.6], "t": 18}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [15.4, 6.6], "t": 36}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [15.4, 6.6], "t": 54}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [15.4, 6.6], "t": 72}, {"s": [14.6, 6.6], "t": 90}]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [14.6, 6.6], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [15.4, 6.6], "t": 18}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [15.4, 6.6], "t": 36}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [15.4, 6.6], "t": 54}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [15.4, 6.6], "t": 72}, {"s": [14.6, 6.6], "t": 90}]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "w": 31, "h": 13.199999809265137, "refId": "2", "ind": 1}, {"ty": 4, "nm": "box Bg", "sr": 1, "st": 0, "op": 91.3, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [14.6, 6.6], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [15.4, 6.6], "t": 18}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [15.4, 6.6], "t": 36}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [15.4, 6.6], "t": 54}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [15.4, 6.6], "t": 72}, {"s": [14.6, 6.6], "t": 90}]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [14.6, 6.6], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [15.4, 6.6], "t": 18}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [15.4, 6.6], "t": 36}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [15.4, 6.6], "t": 54}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [15.4, 6.6], "t": 72}, {"s": [14.6, 6.6], "t": 90}]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "shapes": [{"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[29.2, 0], [29.2, 13.2], [0, 13.2], [0, 0]]}], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[30.8, 0], [30.8, 13.2], [0, 13.2], [0, 0]]}], "t": 18}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[30.8, 0], [30.8, 13.2], [0, 13.2], [0, 0]]}], "t": 36}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[30.8, 0], [30.8, 13.2], [0, 13.2], [0, 0]]}], "t": 54}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[30.8, 0], [30.8, 13.2], [0, 13.2], [0, 0]]}], "t": 72}, {"s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[29.2, 0], [29.2, 13.2], [0, 13.2], [0, 0]]}], "t": 90}]}}], "ind": 2}]}, {"nm": "[Asset] bars", "id": "2", "layers": [{"ty": 4, "nm": "bar", "sr": 1, "st": 0, "op": 91.3, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.73, 0.73], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.73, 2.2], "t": 18}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.73, 4.4], "t": 36}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.73, 2.57], "t": 54}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.73, 4.77], "t": 72}, {"s": [0.73, 0.73], "t": 90}]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [28.47, 6.6], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [30.07, 6.6], "t": 18}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [30.07, 6.6], "t": 36}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [30.07, 6.6], "t": 54}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [30.07, 6.6], "t": 72}, {"s": [28.47, 6.6], "t": 90}]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "shapes": [{"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, -0.4], [0, 0], [0.4, 0], [0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0]], "o": [[0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0], [0, -0.4], [0, 0], [0.4, 0]], "v": [[1.47, 0.73], [1.47, 0.74], [0.74, 1.47], [0.73, 1.47], [0, 0.74], [0, 0.73], [0.73, 0], [0.74, 0]]}], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, -0.4], [0, 0], [0.4, 0], [0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0]], "o": [[0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0], [0, -0.4], [0, 0], [0.4, 0]], "v": [[1.47, 0.73], [1.47, 3.67], [0.74, 4.4], [0.73, 4.4], [0, 3.67], [0, 0.73], [0.73, 0], [0.74, 0]]}], "t": 18}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, -0.4], [0, 0], [0.4, 0], [0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0]], "o": [[0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0], [0, -0.4], [0, 0], [0.4, 0]], "v": [[1.47, 0.73], [1.47, 8.07], [0.74, 8.8], [0.73, 8.8], [0, 8.07], [0, 0.73], [0.73, 0], [0.74, 0]]}], "t": 36}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, -0.4], [0, 0], [0.4, 0], [0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0]], "o": [[0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0], [0, -0.4], [0, 0], [0.4, 0]], "v": [[1.47, 0.73], [1.47, 4.4], [0.74, 5.13], [0.73, 5.13], [0, 4.4], [0, 0.73], [0.73, 0], [0.74, 0]]}], "t": 54}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, -0.4], [0, 0], [0.4, 0], [0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0]], "o": [[0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0], [0, -0.4], [0, 0], [0.4, 0]], "v": [[1.47, 0.73], [1.47, 8.8], [0.74, 9.53], [0.73, 9.53], [0, 8.8], [0, 0.73], [0.73, 0], [0.74, 0]]}], "t": 72}, {"s": [{"c": true, "i": [[0, -0.4], [0, 0], [0.4, 0], [0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0]], "o": [[0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0], [0, -0.4], [0, 0], [0.4, 0]], "v": [[1.47, 0.73], [1.47, 0.74], [0.74, 1.47], [0.73, 1.47], [0, 0.74], [0, 0.73], [0.73, 0], [0.74, 0]]}], "t": 90}]}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "", "c": {"a": 0, "k": [0.6745, 0.6863, 0.7138]}, "r": 1, "o": {"a": 0, "k": 100}}], "ind": 1}, {"ty": 4, "nm": "bar", "sr": 1, "st": 0, "op": 91.3, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.73, 0.73], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.73, 4.4], "t": 18}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.73, 1.1], "t": 36}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.73, 2.93], "t": 54}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.73, 2.2], "t": 72}, {"s": [0.73, 0.73], "t": 90}]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [25, 6.6], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [26.4, 6.6], "t": 18}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [26.4, 6.6], "t": 36}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [26.4, 6.6], "t": 54}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [26.4, 6.6], "t": 72}, {"s": [25, 6.6], "t": 90}]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "shapes": [{"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, -0.4], [0, 0], [0.4, 0], [0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0]], "o": [[0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0], [0, -0.4], [0, 0], [0.4, 0]], "v": [[1.47, 0.73], [1.47, 0.74], [0.74, 1.47], [0.73, 1.47], [0, 0.74], [0, 0.73], [0.73, 0], [0.74, 0]]}], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, -0.4], [0, 0], [0.4, 0], [0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0]], "o": [[0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0], [0, -0.4], [0, 0], [0.4, 0]], "v": [[1.47, 0.73], [1.47, 8.07], [0.74, 8.8], [0.73, 8.8], [0, 8.07], [0, 0.73], [0.73, 0], [0.74, 0]]}], "t": 18}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, -0.4], [0, 0], [0.4, 0], [0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0]], "o": [[0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0], [0, -0.4], [0, 0], [0.4, 0]], "v": [[1.47, 0.73], [1.47, 1.47], [0.74, 2.2], [0.73, 2.2], [0, 1.47], [0, 0.73], [0.73, 0], [0.74, 0]]}], "t": 36}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, -0.4], [0, 0], [0.4, 0], [0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0]], "o": [[0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0], [0, -0.4], [0, 0], [0.4, 0]], "v": [[1.47, 0.73], [1.47, 5.14], [0.74, 5.87], [0.73, 5.87], [0, 5.14], [0, 0.73], [0.73, 0], [0.74, 0]]}], "t": 54}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, -0.4], [0, 0], [0.4, 0], [0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0]], "o": [[0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0], [0, -0.4], [0, 0], [0.4, 0]], "v": [[1.47, 0.73], [1.47, 3.67], [0.74, 4.4], [0.73, 4.4], [0, 3.67], [0, 0.73], [0.73, 0], [0.74, 0]]}], "t": 72}, {"s": [{"c": true, "i": [[0, -0.4], [0, 0], [0.4, 0], [0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0]], "o": [[0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0], [0, -0.4], [0, 0], [0.4, 0]], "v": [[1.47, 0.73], [1.47, 0.74], [0.74, 1.47], [0.73, 1.47], [0, 0.74], [0, 0.73], [0.73, 0], [0.74, 0]]}], "t": 90}]}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "", "c": {"a": 0, "k": [0.6745, 0.6863, 0.7138]}, "r": 1, "o": {"a": 0, "k": 100}}], "ind": 2}, {"ty": 4, "nm": "bar", "sr": 1, "st": 0, "op": 91.3, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.73, 0.73], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.73, 2.57], "t": 18}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.73, 3.3], "t": 36}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.73, 1.47], "t": 54}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.73, 1.83], "t": 72}, {"s": [0.73, 0.73], "t": 90}]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [21.53, 6.6], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [22.73, 6.6], "t": 18}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [22.73, 6.6], "t": 36}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [22.73, 6.6], "t": 54}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [22.73, 6.6], "t": 72}, {"s": [21.53, 6.6], "t": 90}]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "shapes": [{"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, -0.4], [0, 0], [0.4, 0], [0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0]], "o": [[0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0], [0, -0.4], [0, 0], [0.4, 0]], "v": [[1.47, 0.73], [1.47, 0.74], [0.74, 1.47], [0.73, 1.47], [0, 0.74], [0, 0.73], [0.73, 0], [0.74, 0]]}], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, -0.4], [0, 0], [0.4, 0], [0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0]], "o": [[0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0], [0, -0.4], [0, 0], [0.4, 0]], "v": [[1.47, 0.73], [1.47, 4.4], [0.74, 5.13], [0.73, 5.13], [0, 4.4], [0, 0.73], [0.73, 0], [0.74, 0]]}], "t": 18}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, -0.4], [0, 0], [0.4, 0], [0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0]], "o": [[0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0], [0, -0.4], [0, 0], [0.4, 0]], "v": [[1.47, 0.73], [1.47, 5.87], [0.74, 6.6], [0.73, 6.6], [0, 5.87], [0, 0.73], [0.73, 0], [0.74, 0]]}], "t": 36}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, -0.4], [0, 0], [0.4, 0], [0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0]], "o": [[0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0], [0, -0.4], [0, 0], [0.4, 0]], "v": [[1.47, 0.73], [1.47, 2.2], [0.74, 2.93], [0.73, 2.93], [0, 2.2], [0, 0.73], [0.73, 0], [0.74, 0]]}], "t": 54}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, -0.4], [0, 0], [0.4, 0], [0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0]], "o": [[0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0], [0, -0.4], [0, 0], [0.4, 0]], "v": [[1.47, 0.73], [1.47, 2.94], [0.74, 3.67], [0.73, 3.67], [0, 2.94], [0, 0.73], [0.73, 0], [0.74, 0]]}], "t": 72}, {"s": [{"c": true, "i": [[0, -0.4], [0, 0], [0.4, 0], [0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0]], "o": [[0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0], [0, -0.4], [0, 0], [0.4, 0]], "v": [[1.47, 0.73], [1.47, 0.74], [0.74, 1.47], [0.73, 1.47], [0, 0.74], [0, 0.73], [0.73, 0], [0.74, 0]]}], "t": 90}]}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "", "c": {"a": 0, "k": [0.6745, 0.6863, 0.7138]}, "r": 1, "o": {"a": 0, "k": 100}}], "ind": 3}, {"ty": 4, "nm": "bar", "sr": 1, "st": 0, "op": 91.3, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.73, 0.73], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.73, 5.87], "t": 18}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.73, 1.83], "t": 36}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.73, 3.67], "t": 54}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.73, 4.4], "t": 72}, {"s": [0.73, 0.73], "t": 90}]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [18.07, 6.6], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [19.07, 6.6], "t": 18}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [19.07, 6.6], "t": 36}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [19.07, 6.6], "t": 54}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [19.07, 6.6], "t": 72}, {"s": [18.07, 6.6], "t": 90}]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "shapes": [{"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, -0.4], [0, 0], [0.4, 0], [0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0]], "o": [[0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0], [0, -0.4], [0, 0], [0.4, 0]], "v": [[1.47, 0.73], [1.47, 0.74], [0.74, 1.47], [0.73, 1.47], [0, 0.74], [0, 0.73], [0.73, 0], [0.74, 0]]}], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, -0.4], [0, 0], [0.4, 0], [0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0]], "o": [[0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0], [0, -0.4], [0, 0], [0.4, 0]], "v": [[1.47, 0.73], [1.47, 11], [0.74, 11.73], [0.73, 11.73], [0, 11], [0, 0.73], [0.73, 0], [0.74, 0]]}], "t": 18}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, -0.4], [0, 0], [0.4, 0], [0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0]], "o": [[0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0], [0, -0.4], [0, 0], [0.4, 0]], "v": [[1.47, 0.73], [1.47, 2.94], [0.74, 3.67], [0.73, 3.67], [0, 2.94], [0, 0.73], [0.73, 0], [0.74, 0]]}], "t": 36}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, -0.4], [0, 0], [0.4, 0], [0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0]], "o": [[0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0], [0, -0.4], [0, 0], [0.4, 0]], "v": [[1.47, 0.73], [1.47, 6.6], [0.74, 7.33], [0.73, 7.33], [0, 6.6], [0, 0.73], [0.73, 0], [0.74, 0]]}], "t": 54}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, -0.4], [0, 0], [0.4, 0], [0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0]], "o": [[0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0], [0, -0.4], [0, 0], [0.4, 0]], "v": [[1.47, 0.73], [1.47, 8.07], [0.74, 8.8], [0.73, 8.8], [0, 8.07], [0, 0.73], [0.73, 0], [0.74, 0]]}], "t": 72}, {"s": [{"c": true, "i": [[0, -0.4], [0, 0], [0.4, 0], [0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0]], "o": [[0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0], [0, -0.4], [0, 0], [0.4, 0]], "v": [[1.47, 0.73], [1.47, 0.74], [0.74, 1.47], [0.73, 1.47], [0, 0.74], [0, 0.73], [0.73, 0], [0.74, 0]]}], "t": 90}]}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "", "c": {"a": 0, "k": [0.6745, 0.6863, 0.7138]}, "r": 1, "o": {"a": 0, "k": 100}}], "ind": 4}, {"ty": 4, "nm": "bar", "sr": 1, "st": 0, "op": 91.3, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.73, 0.73], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.73, 6.6], "t": 18}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.73, 2.93], "t": 36}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.73, 6.6], "t": 54}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.73, 6.23], "t": 72}, {"s": [0.73, 0.73], "t": 90}]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [14.6, 6.6], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [15.4, 6.6], "t": 18}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [15.4, 6.6], "t": 36}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [15.4, 6.6], "t": 54}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [15.4, 6.6], "t": 72}, {"s": [14.6, 6.6], "t": 90}]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "shapes": [{"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, -0.4], [0, 0], [0.4, 0], [0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0]], "o": [[0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0], [0, -0.4], [0, 0], [0.4, 0]], "v": [[1.47, 0.73], [1.47, 0.74], [0.74, 1.47], [0.73, 1.47], [0, 0.74], [0, 0.73], [0.73, 0], [0.74, 0]]}], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, -0.4], [0, 0], [0.4, 0], [0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0]], "o": [[0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0], [0, -0.4], [0, 0], [0.4, 0]], "v": [[1.47, 0.73], [1.47, 12.47], [0.74, 13.2], [0.73, 13.2], [0, 12.47], [0, 0.73], [0.73, 0], [0.74, 0]]}], "t": 18}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, -0.4], [0, 0], [0.4, 0], [0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0]], "o": [[0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0], [0, -0.4], [0, 0], [0.4, 0]], "v": [[1.47, 0.73], [1.47, 5.14], [0.74, 5.87], [0.73, 5.87], [0, 5.14], [0, 0.73], [0.73, 0], [0.74, 0]]}], "t": 36}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, -0.4], [0, 0], [0.4, 0], [0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0]], "o": [[0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0], [0, -0.4], [0, 0], [0.4, 0]], "v": [[1.47, 0.73], [1.47, 12.47], [0.74, 13.2], [0.73, 13.2], [0, 12.47], [0, 0.73], [0.73, 0], [0.74, 0]]}], "t": 54}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, -0.4], [0, 0], [0.4, 0], [0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0]], "o": [[0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0], [0, -0.4], [0, 0], [0.4, 0]], "v": [[1.47, 0.73], [1.47, 11.74], [0.74, 12.47], [0.73, 12.47], [0, 11.74], [0, 0.73], [0.73, 0], [0.74, 0]]}], "t": 72}, {"s": [{"c": true, "i": [[0, -0.4], [0, 0], [0.4, 0], [0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0]], "o": [[0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0], [0, -0.4], [0, 0], [0.4, 0]], "v": [[1.47, 0.73], [1.47, 0.74], [0.74, 1.47], [0.73, 1.47], [0, 0.74], [0, 0.73], [0.73, 0], [0.74, 0]]}], "t": 90}]}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "", "c": {"a": 0, "k": [0.6745, 0.6863, 0.7138]}, "r": 1, "o": {"a": 0, "k": 100}}], "ind": 5}, {"ty": 4, "nm": "bar", "sr": 1, "st": 0, "op": 91.3, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.73, 0.73], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.73, 2.93], "t": 18}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.73, 5.87], "t": 36}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.73, 2.93], "t": 54}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.73, 0.73], "t": 72}, {"s": [0.73, 0.73], "t": 90}]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [11.13, 6.6], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [11.73, 6.6], "t": 18}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [11.73, 6.6], "t": 36}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [11.73, 6.6], "t": 54}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [11.73, 6.6], "t": 72}, {"s": [11.13, 6.6], "t": 90}]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "shapes": [{"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, -0.4], [0, 0], [0.4, 0], [0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0]], "o": [[0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0], [0, -0.4], [0, 0], [0.4, 0]], "v": [[1.47, 0.73], [1.47, 0.74], [0.74, 1.47], [0.73, 1.47], [0, 0.74], [0, 0.73], [0.73, 0], [0.74, 0]]}], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, -0.4], [0, 0], [0.4, 0], [0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0]], "o": [[0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0], [0, -0.4], [0, 0], [0.4, 0]], "v": [[1.47, 0.73], [1.47, 5.14], [0.74, 5.87], [0.73, 5.87], [0, 5.14], [0, 0.73], [0.73, 0], [0.74, 0]]}], "t": 18}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, -0.4], [0, 0], [0.4, 0], [0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0]], "o": [[0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0], [0, -0.4], [0, 0], [0.4, 0]], "v": [[1.47, 0.73], [1.47, 11], [0.74, 11.73], [0.73, 11.73], [0, 11], [0, 0.73], [0.73, 0], [0.74, 0]]}], "t": 36}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, -0.4], [0, 0], [0.4, 0], [0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0]], "o": [[0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0], [0, -0.4], [0, 0], [0.4, 0]], "v": [[1.47, 0.73], [1.47, 5.14], [0.74, 5.87], [0.73, 5.87], [0, 5.14], [0, 0.73], [0.73, 0], [0.74, 0]]}], "t": 54}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, -0.4], [0, 0], [0.4, 0], [0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0]], "o": [[0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0], [0, -0.4], [0, 0], [0.4, 0]], "v": [[1.47, 0.73], [1.47, 0.74], [0.74, 1.47], [0.73, 1.47], [0, 0.74], [0, 0.73], [0.73, 0], [0.74, 0]]}], "t": 72}, {"s": [{"c": true, "i": [[0, -0.4], [0, 0], [0.4, 0], [0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0]], "o": [[0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0], [0, -0.4], [0, 0], [0.4, 0]], "v": [[1.47, 0.73], [1.47, 0.74], [0.74, 1.47], [0.73, 1.47], [0, 0.74], [0, 0.73], [0.73, 0], [0.74, 0]]}], "t": 90}]}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "", "c": {"a": 0, "k": [0.6745, 0.6863, 0.7138]}, "r": 1, "o": {"a": 0, "k": 100}}], "ind": 6}, {"ty": 4, "nm": "bar", "sr": 1, "st": 0, "op": 91.3, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.73, 0.73], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.73, 3.67], "t": 18}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.73, 1.1], "t": 36}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.73, 2.2], "t": 54}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.73, 1.47], "t": 72}, {"s": [0.73, 0.73], "t": 90}]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [7.67, 6.6], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [8.07, 6.6], "t": 18}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [8.07, 6.6], "t": 36}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [8.07, 6.6], "t": 54}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [8.07, 6.6], "t": 72}, {"s": [7.67, 6.6], "t": 90}]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "shapes": [{"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, -0.4], [0, 0], [0.4, 0], [0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0]], "o": [[0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0], [0, -0.4], [0, 0], [0.4, 0]], "v": [[1.47, 0.73], [1.47, 0.74], [0.74, 1.47], [0.73, 1.47], [0, 0.74], [0, 0.73], [0.73, 0], [0.74, 0]]}], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, -0.4], [0, 0], [0.4, 0], [0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0]], "o": [[0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0], [0, -0.4], [0, 0], [0.4, 0]], "v": [[1.47, 0.73], [1.47, 6.6], [0.74, 7.33], [0.73, 7.33], [0, 6.6], [0, 0.73], [0.73, 0], [0.74, 0]]}], "t": 18}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, -0.4], [0, 0], [0.4, 0], [0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0]], "o": [[0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0], [0, -0.4], [0, 0], [0.4, 0]], "v": [[1.47, 0.73], [1.47, 1.47], [0.74, 2.2], [0.73, 2.2], [0, 1.47], [0, 0.73], [0.73, 0], [0.74, 0]]}], "t": 36}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, -0.4], [0, 0], [0.4, 0], [0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0]], "o": [[0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0], [0, -0.4], [0, 0], [0.4, 0]], "v": [[1.47, 0.73], [1.47, 3.67], [0.74, 4.4], [0.73, 4.4], [0, 3.67], [0, 0.73], [0.73, 0], [0.74, 0]]}], "t": 54}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, -0.4], [0, 0], [0.4, 0], [0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0]], "o": [[0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0], [0, -0.4], [0, 0], [0.4, 0]], "v": [[1.47, 0.73], [1.47, 2.2], [0.74, 2.93], [0.73, 2.93], [0, 2.2], [0, 0.73], [0.73, 0], [0.74, 0]]}], "t": 72}, {"s": [{"c": true, "i": [[0, -0.4], [0, 0], [0.4, 0], [0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0]], "o": [[0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0], [0, -0.4], [0, 0], [0.4, 0]], "v": [[1.47, 0.73], [1.47, 0.74], [0.74, 1.47], [0.73, 1.47], [0, 0.74], [0, 0.73], [0.73, 0], [0.74, 0]]}], "t": 90}]}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "", "c": {"a": 0, "k": [0.6745, 0.6863, 0.7138]}, "r": 1, "o": {"a": 0, "k": 100}}], "ind": 7}, {"ty": 4, "nm": "bar", "sr": 1, "st": 0, "op": 91.3, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.73, 0.73], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.73, 2.2], "t": 18}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.73, 4.4], "t": 36}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.73, 2.93], "t": 54}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.73, 4.4], "t": 72}, {"s": [0.73, 0.73], "t": 90}]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [4.2, 6.6], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [4.4, 6.6], "t": 18}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [4.4, 6.6], "t": 36}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [4.4, 6.6], "t": 54}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [4.4, 6.6], "t": 72}, {"s": [4.2, 6.6], "t": 90}]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "shapes": [{"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, -0.4], [0, 0], [0.4, 0], [0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0]], "o": [[0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0], [0, -0.4], [0, 0], [0.4, 0]], "v": [[1.47, 0.73], [1.47, 0.74], [0.74, 1.47], [0.73, 1.47], [0, 0.74], [0, 0.73], [0.73, 0], [0.74, 0]]}], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, -0.4], [0, 0], [0.4, 0], [0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0]], "o": [[0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0], [0, -0.4], [0, 0], [0.4, 0]], "v": [[1.47, 0.73], [1.47, 3.67], [0.74, 4.4], [0.73, 4.4], [0, 3.67], [0, 0.73], [0.73, 0], [0.74, 0]]}], "t": 18}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, -0.4], [0, 0], [0.4, 0], [0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0]], "o": [[0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0], [0, -0.4], [0, 0], [0.4, 0]], "v": [[1.47, 0.73], [1.47, 8.07], [0.74, 8.8], [0.73, 8.8], [0, 8.07], [0, 0.73], [0.73, 0], [0.74, 0]]}], "t": 36}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, -0.4], [0, 0], [0.4, 0], [0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0]], "o": [[0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0], [0, -0.4], [0, 0], [0.4, 0]], "v": [[1.47, 0.73], [1.47, 5.14], [0.74, 5.87], [0.73, 5.87], [0, 5.14], [0, 0.73], [0.73, 0], [0.74, 0]]}], "t": 54}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, -0.4], [0, 0], [0.4, 0], [0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0]], "o": [[0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0], [0, -0.4], [0, 0], [0.4, 0]], "v": [[1.47, 0.73], [1.47, 8.07], [0.74, 8.8], [0.73, 8.8], [0, 8.07], [0, 0.73], [0.73, 0], [0.74, 0]]}], "t": 72}, {"s": [{"c": true, "i": [[0, -0.4], [0, 0], [0.4, 0], [0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0]], "o": [[0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0], [0, -0.4], [0, 0], [0.4, 0]], "v": [[1.47, 0.73], [1.47, 0.74], [0.74, 1.47], [0.73, 1.47], [0, 0.74], [0, 0.73], [0.73, 0], [0.74, 0]]}], "t": 90}]}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "", "c": {"a": 0, "k": [0.6745, 0.6863, 0.7138]}, "r": 1, "o": {"a": 0, "k": 100}}], "ind": 8}, {"ty": 4, "nm": "bar", "sr": 1, "st": 0, "op": 91.3, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.73, 0.73], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.73, 4.4], "t": 18}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.73, 1.47], "t": 36}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.73, 6.6], "t": 54}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [0.73, 2.2], "t": 72}, {"s": [0.73, 0.73], "t": 90}]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0.73, 6.6]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "shapes": [{"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, -0.4], [0, 0], [0.4, 0], [0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0]], "o": [[0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0], [0, -0.4], [0, 0], [0.4, 0]], "v": [[1.47, 0.73], [1.47, 0.74], [0.74, 1.47], [0.73, 1.47], [0, 0.74], [0, 0.73], [0.73, 0], [0.74, 0]]}], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, -0.4], [0, 0], [0.4, 0], [0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0]], "o": [[0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0], [0, -0.4], [0, 0], [0.4, 0]], "v": [[1.47, 0.73], [1.47, 8.07], [0.74, 8.8], [0.73, 8.8], [0, 8.07], [0, 0.73], [0.73, 0], [0.74, 0]]}], "t": 18}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, -0.4], [0, 0], [0.4, 0], [0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0]], "o": [[0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0], [0, -0.4], [0, 0], [0.4, 0]], "v": [[1.47, 0.73], [1.47, 2.2], [0.74, 2.93], [0.73, 2.93], [0, 2.2], [0, 0.73], [0.73, 0], [0.74, 0]]}], "t": 36}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, -0.4], [0, 0], [0.4, 0], [0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0]], "o": [[0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0], [0, -0.4], [0, 0], [0.4, 0]], "v": [[1.47, 0.73], [1.47, 12.47], [0.74, 13.2], [0.73, 13.2], [0, 12.47], [0, 0.73], [0.73, 0], [0.74, 0]]}], "t": 54}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, -0.4], [0, 0], [0.4, 0], [0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0]], "o": [[0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0], [0, -0.4], [0, 0], [0.4, 0]], "v": [[1.47, 0.73], [1.47, 3.67], [0.74, 4.4], [0.73, 4.4], [0, 3.67], [0, 0.73], [0.73, 0], [0.74, 0]]}], "t": 72}, {"s": [{"c": true, "i": [[0, -0.4], [0, 0], [0.4, 0], [0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0]], "o": [[0, 0], [0, 0.4], [0, 0], [-0.4, 0], [0, 0], [0, -0.4], [0, 0], [0.4, 0]], "v": [[1.47, 0.73], [1.47, 0.74], [0.74, 1.47], [0.73, 1.47], [0, 0.74], [0, 0.73], [0.73, 0], [0.74, 0]]}], "t": 90}]}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "", "c": {"a": 0, "k": [0.6745, 0.6863, 0.7138]}, "r": 1, "o": {"a": 0, "k": 100}}], "ind": 9}, {"ty": 4, "nm": "bars Bg", "sr": 1, "st": 0, "op": 91.3, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [14.6, 6.6], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [15.4, 6.6], "t": 18}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [15.4, 6.6], "t": 36}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [15.4, 6.6], "t": 54}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [15.4, 6.6], "t": 72}, {"s": [14.6, 6.6], "t": 90}]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [14.6, 6.6], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [15.4, 6.6], "t": 18}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [15.4, 6.6], "t": 36}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [15.4, 6.6], "t": 54}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [15.4, 6.6], "t": 72}, {"s": [14.6, 6.6], "t": 90}]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "shapes": [{"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[29.2, 0], [29.2, 13.2], [0, 13.2], [0, 0]]}], "t": 0}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[30.8, 0], [30.8, 13.2], [0, 13.2], [0, 0]]}], "t": 18}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[30.8, 0], [30.8, 13.2], [0, 13.2], [0, 0]]}], "t": 36}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[30.8, 0], [30.8, 13.2], [0, 13.2], [0, 0]]}], "t": 54}, {"o": {"x": 0.33, "y": 1}, "i": {"x": 0.68, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[30.8, 0], [30.8, 13.2], [0, 13.2], [0, 0]]}], "t": 72}, {"s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[29.2, 0], [29.2, 13.2], [0, 13.2], [0, 0]]}], "t": 90}]}}], "ind": 10}]}]}