{"nm": "headphone_connecting", "ddd": 0, "h": 32, "w": 32, "meta": {"g": "LottieFiles Figma v75"}, "layers": [{"ty": 4, "nm": "Path", "sr": 1, "st": 0, "op": 100, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [0.98, 1.97]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [13.46, 17.12]}, "r": {"a": 0, "k": -30}, "sa": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 48}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 73}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 99}, {"s": [100], "t": 147}]}}, "shapes": [{"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -0.54], [0.25, -0.46], [0.41, 0.22], [-0.17, 0.4], [0, 0], [0, 0.22], [0.09, 0.19], [0, 0], [-0.39, 0.21], [-0.22, -0.41]], "o": [[0.25, 0.46], [0, 0.54], [-0.22, 0.41], [-0.39, -0.21], [0, 0], [0.09, -0.19], [0, -0.22], [0, 0], [-0.17, -0.4], [0.41, -0.22], [0, 0]], "v": [[1.59, 0.44], [1.97, 1.97], [1.59, 3.5], [0.44, 3.84], [0.07, 2.77], [0.15, 2.6], [0.28, 1.97], [0.15, 1.34], [0.07, 1.17], [0.44, 0.1], [1.59, 0.44]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "", "c": {"a": 0, "k": [0.1883, 0.1883, 0.1883]}, "r": 2, "o": {"a": 0, "k": 100}}], "ind": 1}, {"ty": 4, "nm": "Path", "sr": 1, "st": 0, "op": 100, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1.12, 3.09], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1.12, 3.09], "t": 48}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1.12, 3.09], "t": 73}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.98, 1.97], "t": 74}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.98, 1.97], "t": 98}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1.12, 3.09], "t": 99}, {"s": [1.12, 3.09], "t": 147}]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [16.13, 15.57], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [16.13, 15.57], "t": 48}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [16.13, 15.57], "t": 73}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [13.46, 17.12], "t": 74}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [13.46, 17.12], "t": 98}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [16.13, 15.57], "t": 99}, {"s": [16.13, 15.57], "t": 147}]}, "r": {"a": 0, "k": -30}, "sa": {"a": 0, "k": 0}, "o": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 48}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0], "t": 73}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 74}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 98}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [100], "t": 99}, {"s": [100], "t": 147}]}}, "shapes": [{"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, -0.94], [0, -0.94], [0.43, -0.8], [0.41, 0.22], [-0.22, 0.41], [0, 0.66], [0.3, 0.55], [-0.41, 0.22], [-0.22, -0.41]], "o": [[0.43, 0.8], [0.43, 0.8], [0, 0.94], [0, 0.94], [-0.22, 0.41], [-0.41, -0.22], [0.3, -0.55], [0, -0.66], [-0.22, -0.41], [0.41, -0.22], [0, 0]], "v": [[1.59, 0.44], [1.59, 0.44], [2.25, 3.09], [2.25, 3.09], [1.59, 5.74], [0.44, 6.09], [0.1, 4.94], [0.56, 3.09], [0.1, 1.25], [0.44, 0.1], [1.59, 0.44]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, -0.94], [0, -0.94], [0.43, -0.8], [0.41, 0.22], [-0.22, 0.41], [0, 0.66], [0.3, 0.55], [-0.41, 0.22], [-0.22, -0.41]], "o": [[0.43, 0.8], [0.43, 0.8], [0, 0.94], [0, 0.94], [-0.22, 0.41], [-0.41, -0.22], [0.3, -0.55], [0, -0.66], [-0.22, -0.41], [0.41, -0.22], [0, 0]], "v": [[1.59, 0.44], [1.59, 0.44], [2.25, 3.09], [2.25, 3.09], [1.59, 5.74], [0.44, 6.09], [0.1, 4.94], [0.56, 3.09], [0.1, 1.25], [0.44, 0.1], [1.59, 0.44]]}], "t": 48}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, -0.94], [0, -0.94], [0.43, -0.8], [0.41, 0.22], [-0.22, 0.41], [0, 0.66], [0.3, 0.55], [-0.41, 0.22], [-0.22, -0.41]], "o": [[0.43, 0.8], [0.43, 0.8], [0, 0.94], [0, 0.94], [-0.22, 0.41], [-0.41, -0.22], [0.3, -0.55], [0, -0.66], [-0.22, -0.41], [0.41, -0.22], [0, 0]], "v": [[1.59, 0.44], [1.59, 0.44], [2.25, 3.09], [2.25, 3.09], [1.59, 5.74], [0.44, 6.09], [0.1, 4.94], [0.56, 3.09], [0.1, 1.25], [0.44, 0.1], [1.59, 0.44]]}], "t": 73}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, -0.54], [0.25, -0.46], [0.41, 0.22], [-0.17, 0.4], [0, 0], [0, 0.22], [0.09, 0.19], [0, 0], [-0.39, 0.21], [-0.22, -0.41]], "o": [[0.25, 0.46], [0, 0.54], [-0.22, 0.41], [-0.39, -0.21], [0, 0], [0.09, -0.19], [0, -0.22], [0, 0], [-0.17, -0.4], [0.41, -0.22], [0, 0]], "v": [[1.59, 0.44], [1.97, 1.97], [1.59, 3.5], [0.44, 3.84], [0.07, 2.77], [0.15, 2.6], [0.28, 1.97], [0.15, 1.34], [0.07, 1.17], [0.44, 0.1], [1.59, 0.44]]}], "t": 74}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, -0.54], [0.25, -0.46], [0.41, 0.22], [-0.17, 0.4], [0, 0], [0, 0.22], [0.09, 0.19], [0, 0], [-0.39, 0.21], [-0.22, -0.41]], "o": [[0.25, 0.46], [0, 0.54], [-0.22, 0.41], [-0.39, -0.21], [0, 0], [0.09, -0.19], [0, -0.22], [0, 0], [-0.17, -0.4], [0.41, -0.22], [0, 0]], "v": [[1.59, 0.44], [1.97, 1.97], [1.59, 3.5], [0.44, 3.84], [0.07, 2.77], [0.15, 2.6], [0.28, 1.97], [0.15, 1.34], [0.07, 1.17], [0.44, 0.1], [1.59, 0.44]]}], "t": 98}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, -0.94], [0, -0.94], [0.43, -0.8], [0.41, 0.22], [-0.22, 0.41], [0, 0.66], [0.3, 0.55], [-0.41, 0.22], [-0.22, -0.41]], "o": [[0.43, 0.8], [0.43, 0.8], [0, 0.94], [0, 0.94], [-0.22, 0.41], [-0.41, -0.22], [0.3, -0.55], [0, -0.66], [-0.22, -0.41], [0.41, -0.22], [0, 0]], "v": [[1.59, 0.44], [1.59, 0.44], [2.25, 3.09], [2.25, 3.09], [1.59, 5.74], [0.44, 6.09], [0.1, 4.94], [0.56, 3.09], [0.1, 1.25], [0.44, 0.1], [1.59, 0.44]]}], "t": 99}, {"s": [{"c": true, "i": [[0, 0], [0, 0], [0, -0.94], [0, -0.94], [0.43, -0.8], [0.41, 0.22], [-0.22, 0.41], [0, 0.66], [0.3, 0.55], [-0.41, 0.22], [-0.22, -0.41]], "o": [[0.43, 0.8], [0.43, 0.8], [0, 0.94], [0, 0.94], [-0.22, 0.41], [-0.41, -0.22], [0.3, -0.55], [0, -0.66], [-0.22, -0.41], [0.41, -0.22], [0, 0]], "v": [[1.59, 0.44], [1.59, 0.44], [2.25, 3.09], [2.25, 3.09], [1.59, 5.74], [0.44, 6.09], [0.1, 4.94], [0.56, 3.09], [0.1, 1.25], [0.44, 0.1], [1.59, 0.44]]}], "t": 147}]}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "", "c": {"a": 0, "k": [0.1883, 0.1883, 0.1883]}, "r": 2, "o": {"a": 0, "k": 100}}], "ind": 2}, {"ty": 4, "nm": "Path", "sr": 1, "st": 0, "op": 100, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1.41, 4.36], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1.41, 4.36], "t": 48}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.98, 1.97], "t": 49}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [0.98, 1.97], "t": 73}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1.12, 3.09], "t": 74}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1.12, 3.09], "t": 98}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [1.41, 4.36], "t": 99}, {"s": [1.41, 4.36], "t": 147}]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [19.18, 13.81], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [19.18, 13.81], "t": 48}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [13.46, 17.12], "t": 49}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [13.46, 17.12], "t": 73}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [16.13, 15.57], "t": 74}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [16.13, 15.57], "t": 98}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [19.18, 13.81], "t": 99}, {"s": [19.18, 13.81], "t": 147}]}, "r": {"a": 0, "k": -30}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "shapes": [{"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [-0.27, -0.38], [-0.27, -0.38], [0, -1.43], [0.83, -1.2], [0.38, 0.27], [-0.27, 0.38], [0, 1.08], [0.64, 0.92], [-0.38, 0.27]], "o": [[0.38, -0.27], [0.38, -0.27], [0.83, 1.2], [0.83, 1.2], [0, 1.43], [-0.27, 0.38], [-0.38, -0.27], [0.64, -0.92], [0, -1.08], [-0.27, -0.38], [0, 0]], "v": [[0.36, 0.15], [0.36, 0.15], [1.54, 0.36], [1.54, 0.36], [2.81, 4.36], [1.54, 8.35], [0.36, 8.57], [0.15, 7.39], [1.12, 4.36], [0.15, 1.32], [0.36, 0.15]]}], "t": 0}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [-0.27, -0.38], [-0.27, -0.38], [0, -1.43], [0.83, -1.2], [0.38, 0.27], [-0.27, 0.38], [0, 1.08], [0.64, 0.92], [-0.38, 0.27]], "o": [[0.38, -0.27], [0.38, -0.27], [0.83, 1.2], [0.83, 1.2], [0, 1.43], [-0.27, 0.38], [-0.38, -0.27], [0.64, -0.92], [0, -1.08], [-0.27, -0.38], [0, 0]], "v": [[0.36, 0.15], [0.36, 0.15], [1.54, 0.36], [1.54, 0.36], [2.81, 4.36], [1.54, 8.35], [0.36, 8.57], [0.15, 7.39], [1.12, 4.36], [0.15, 1.32], [0.36, 0.15]]}], "t": 48}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, -0.54], [0.25, -0.46], [0.41, 0.22], [-0.17, 0.4], [0, 0], [0, 0.22], [0.09, 0.19], [0, 0], [-0.39, 0.21], [-0.22, -0.41]], "o": [[0.25, 0.46], [0, 0.54], [-0.22, 0.41], [-0.39, -0.21], [0, 0], [0.09, -0.19], [0, -0.22], [0, 0], [-0.17, -0.4], [0.41, -0.22], [0, 0]], "v": [[1.59, 0.44], [1.97, 1.97], [1.59, 3.5], [0.44, 3.84], [0.07, 2.77], [0.15, 2.6], [0.28, 1.97], [0.15, 1.34], [0.07, 1.17], [0.44, 0.1], [1.59, 0.44]]}], "t": 49}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, -0.54], [0.25, -0.46], [0.41, 0.22], [-0.17, 0.4], [0, 0], [0, 0.22], [0.09, 0.19], [0, 0], [-0.39, 0.21], [-0.22, -0.41]], "o": [[0.25, 0.46], [0, 0.54], [-0.22, 0.41], [-0.39, -0.21], [0, 0], [0.09, -0.19], [0, -0.22], [0, 0], [-0.17, -0.4], [0.41, -0.22], [0, 0]], "v": [[1.59, 0.44], [1.97, 1.97], [1.59, 3.5], [0.44, 3.84], [0.07, 2.77], [0.15, 2.6], [0.28, 1.97], [0.15, 1.34], [0.07, 1.17], [0.44, 0.1], [1.59, 0.44]]}], "t": 73}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, -0.94], [0, -0.94], [0.43, -0.8], [0.41, 0.22], [-0.22, 0.41], [0, 0.66], [0.3, 0.55], [-0.41, 0.22], [-0.22, -0.41]], "o": [[0.43, 0.8], [0.43, 0.8], [0, 0.94], [0, 0.94], [-0.22, 0.41], [-0.41, -0.22], [0.3, -0.55], [0, -0.66], [-0.22, -0.41], [0.41, -0.22], [0, 0]], "v": [[1.59, 0.44], [1.59, 0.44], [2.25, 3.09], [2.25, 3.09], [1.59, 5.74], [0.44, 6.09], [0.1, 4.94], [0.56, 3.09], [0.1, 1.25], [0.44, 0.1], [1.59, 0.44]]}], "t": 74}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, -0.94], [0, -0.94], [0.43, -0.8], [0.41, 0.22], [-0.22, 0.41], [0, 0.66], [0.3, 0.55], [-0.41, 0.22], [-0.22, -0.41]], "o": [[0.43, 0.8], [0.43, 0.8], [0, 0.94], [0, 0.94], [-0.22, 0.41], [-0.41, -0.22], [0.3, -0.55], [0, -0.66], [-0.22, -0.41], [0.41, -0.22], [0, 0]], "v": [[1.59, 0.44], [1.59, 0.44], [2.25, 3.09], [2.25, 3.09], [1.59, 5.74], [0.44, 6.09], [0.1, 4.94], [0.56, 3.09], [0.1, 1.25], [0.44, 0.1], [1.59, 0.44]]}], "t": 98}, {"o": {"x": 0, "y": 0}, "i": {"x": 1, "y": 1}, "s": [{"c": true, "i": [[0, 0], [0, 0], [-0.27, -0.38], [-0.27, -0.38], [0, -1.43], [0.83, -1.2], [0.38, 0.27], [-0.27, 0.38], [0, 1.08], [0.64, 0.92], [-0.38, 0.27]], "o": [[0.38, -0.27], [0.38, -0.27], [0.83, 1.2], [0.83, 1.2], [0, 1.43], [-0.27, 0.38], [-0.38, -0.27], [0.64, -0.92], [0, -1.08], [-0.27, -0.38], [0, 0]], "v": [[0.36, 0.15], [0.36, 0.15], [1.54, 0.36], [1.54, 0.36], [2.81, 4.36], [1.54, 8.35], [0.36, 8.57], [0.15, 7.39], [1.12, 4.36], [0.15, 1.32], [0.36, 0.15]]}], "t": 99}, {"s": [{"c": true, "i": [[0, 0], [0, 0], [-0.27, -0.38], [-0.27, -0.38], [0, -1.43], [0.83, -1.2], [0.38, 0.27], [-0.27, 0.38], [0, 1.08], [0.64, 0.92], [-0.38, 0.27]], "o": [[0.38, -0.27], [0.38, -0.27], [0.83, 1.2], [0.83, 1.2], [0, 1.43], [-0.27, 0.38], [-0.38, -0.27], [0.64, -0.92], [0, -1.08], [-0.27, -0.38], [0, 0]], "v": [[0.36, 0.15], [0.36, 0.15], [1.54, 0.36], [1.54, 0.36], [2.81, 4.36], [1.54, 8.35], [0.36, 8.57], [0.15, 7.39], [1.12, 4.36], [0.15, 1.32], [0.36, 0.15]]}], "t": 147}]}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "", "c": {"a": 0, "k": [0.1883, 0.1883, 0.1883]}, "r": 2, "o": {"a": 0, "k": 100}}], "ind": 3}, {"ty": 4, "nm": "Union", "sr": 1, "st": 0, "op": 100, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [13, 12.5]}, "s": {"a": 0, "k": [100, 100]}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [16, 16.5]}, "r": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}}, "shapes": [{"ty": "sh", "bm": 0, "hd": false, "nm": "", "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, -7.08], [0, 0], [-2.87, 0], [0, 0], [0, 0.35], [0, 0], [0.36, 0], [0, 0], [0, 0], [-6.1, 0], [0, -6.02], [0, 0], [0, 0], [0, -0.35], [0, 0], [-0.36, 0], [0, 0], [0, 2.83], [0, 0], [7.18, 0]], "o": [[-7.18, 0], [0, 0], [0, 2.83], [0, 0], [0.36, 0], [0, 0], [0, -0.35], [0, 0], [0, 0], [0, -6.02], [6.1, 0], [0, 0], [0, 0], [-0.36, 0], [0, 0], [0, 0.35], [0, 0], [2.87, 0], [0, 0], [0, -7.08], [0, 0]], "v": [[13, 0], [0, 12.82], [0, 19.87], [5.2, 25], [6.5, 25], [7.15, 24.36], [7.15, 15.38], [6.5, 14.74], [1.95, 14.74], [1.95, 12.82], [13, 1.92], [24.05, 12.82], [24.05, 14.74], [19.5, 14.74], [18.85, 15.38], [18.85, 24.36], [19.5, 25], [20.8, 25], [26, 19.87], [26, 12.82], [13, 0]]}}}, {"ty": "fl", "bm": 0, "hd": false, "nm": "", "c": {"a": 0, "k": [0.1883, 0.1883, 0.1883]}, "r": 1, "o": {"a": 0, "k": 100}}], "ind": 4}], "v": "5.7.0", "fr": 60, "op": 99, "ip": 0, "assets": []}