{"v": "5.12.2", "fr": 30, "ip": 0, "op": 260, "w": 800, "h": 800, "nm": "Function introduction 1", "ddd": 0, "assets": [{"id": "image_0", "w": 960, "h": 961, "u": "images/", "p": "img_0.png", "e": 0}, {"id": "image_1", "w": 960, "h": 960, "u": "images/", "p": "img_1.png", "e": 0}, {"id": "image_2", "w": 961, "h": 211, "u": "images/", "p": "img_2.png", "e": 0}, {"id": "image_3", "w": 960, "h": 208, "u": "images/", "p": "img_3.png", "e": 0}, {"id": "image_4", "w": 960, "h": 208, "u": "images/", "p": "img_4.png", "e": 0}, {"id": "image_5", "w": 960, "h": 208, "u": "images/", "p": "img_5.png", "e": 0}, {"id": "image_6", "w": 961, "h": 211, "u": "images/", "p": "img_6.png", "e": 0}, {"id": "image_7", "w": 245, "h": 115, "u": "images/", "p": "img_7.png", "e": 0}, {"id": "image_8", "w": 961, "h": 641, "u": "images/", "p": "img_8.png", "e": 0}, {"id": "image_9", "w": 960, "h": 960, "u": "images/", "p": "img_9.png", "e": 0}, {"id": "image_10", "w": 1920, "h": 1920, "u": "images/", "p": "img_10.png", "e": 0}, {"id": "image_11", "w": 800, "h": 800, "u": "images/", "p": "img_11.png", "e": 0}, {"id": "image_12", "w": 200, "h": 400, "u": "images/", "p": "img_12.png", "e": 0}, {"id": "image_13", "w": 200, "h": 400, "u": "images/", "p": "img_13.png", "e": 0}, {"id": "image_14", "w": 200, "h": 400, "u": "images/", "p": "img_14.png", "e": 0}, {"id": "image_15", "w": 200, "h": 400, "u": "images/", "p": "img_15.png", "e": 0}, {"id": "image_16", "w": 200, "h": 400, "u": "images/", "p": "img_16.png", "e": 0}, {"id": "image_17", "w": 200, "h": 400, "u": "images/", "p": "img_17.png", "e": 0}, {"id": "image_18", "w": 200, "h": 400, "u": "images/", "p": "img_18.png", "e": 0}, {"id": "image_19", "w": 200, "h": 400, "u": "images/", "p": "img_19.png", "e": 0}, {"id": "imgSeq_0", "w": 200, "h": 400, "t": "seq", "u": "images/", "p": "seq_0_0.png", "e": 0}, {"id": "imgSeq_1", "w": 200, "h": 400, "t": "seq", "u": "images/", "p": "seq_0_1.png", "e": 0}, {"id": "imgSeq_2", "w": 200, "h": 400, "t": "seq", "u": "images/", "p": "seq_0_2.png", "e": 0}, {"id": "imgSeq_3", "w": 200, "h": 400, "t": "seq", "u": "images/", "p": "seq_0_3.png", "e": 0}, {"id": "imgSeq_4", "w": 200, "h": 400, "t": "seq", "u": "images/", "p": "seq_0_4.png", "e": 0}, {"id": "imgSeq_5", "w": 200, "h": 400, "t": "seq", "u": "images/", "p": "seq_0_5.png", "e": 0}, {"id": "imgSeq_6", "w": 200, "h": 400, "t": "seq", "u": "images/", "p": "seq_0_6.png", "e": 0}, {"id": "imgSeq_7", "w": 200, "h": 400, "t": "seq", "u": "images/", "p": "seq_0_7.png", "e": 0}, {"id": "comp_0", "nm": "exercise start", "fr": 30, "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "Shape Layer 1", "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [400, 400, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-787.609, -315.609, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [128.045, 128.045, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [624.781, 624.781], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Ellipse Path 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-787.609, -315.609], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Ellipse 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 540, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 0, "nm": "Pre-comp 2", "tt": 1, "tp": 1, "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [400, 400, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [400, 400, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 800, "h": 800, "ip": 0, "op": 540, "st": 0, "bm": 0}]}, {"id": "comp_1", "nm": "Pre-comp 2", "fr": 30, "layers": [{"ddd": 0, "ind": 9, "ty": 2, "nm": "start (3).png", "cl": "png", "refId": "image_0", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 209, "s": [100]}, {"t": 210, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"s": true, "x": {"a": 1, "k": [{"i": {"x": [0.751], "y": [-3.26]}, "o": {"x": [0.167], "y": [0]}, "t": 37, "s": [1200]}, {"i": {"x": [0.12], "y": [0.917]}, "o": {"x": [0.485], "y": [-0.001]}, "t": 148, "s": [1200]}, {"i": {"x": [0.989], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 157, "s": [400]}, {"i": {"x": [0.988], "y": [1.301]}, "o": {"x": [0.013], "y": [0]}, "t": 185, "s": [400]}, {"i": {"x": [0.519], "y": [0.994]}, "o": {"x": [0.589], "y": [0.008]}, "t": 209, "s": [400]}, {"t": 219, "s": [-388]}], "ix": 3}, "y": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 37, "s": [400]}, {"i": {"x": [0.967], "y": [1]}, "o": {"x": [0.033], "y": [0]}, "t": 148, "s": [400]}, {"i": {"x": [0.989], "y": [1.04]}, "o": {"x": [0.167], "y": [0]}, "t": 157, "s": [400]}, {"i": {"x": [0.988], "y": [0.899]}, "o": {"x": [0.013], "y": [-0.028]}, "t": 185, "s": [400]}, {"i": {"x": [0.97], "y": [1.161]}, "o": {"x": [0.03], "y": [-0.168]}, "t": 209, "s": [400]}, {"t": 219, "s": [397.998]}], "ix": 4}}, "a": {"a": 0, "k": [480, 480, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [80, 80, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 0, "op": 540, "st": 0, "bm": 0}, {"ddd": 0, "ind": 14, "ty": 2, "nm": "race_onboarding_intro_cycling_0.png", "cl": "png", "refId": "image_1", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 37, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 209, "s": [0]}, {"t": 210, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"s": true, "x": {"a": 1, "k": [{"i": {"x": [0.998], "y": [0.976]}, "o": {"x": [0.002], "y": [0]}, "t": 37, "s": [400]}, {"t": 209, "s": [400]}], "ix": 3}, "y": {"a": 1, "k": [{"i": {"x": [0.998], "y": [1.123]}, "o": {"x": [0.002], "y": [0.336]}, "t": 37, "s": [400]}, {"t": 209, "s": [400]}], "ix": 4}}, "a": {"a": 0, "k": [480, 480, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [80, 80, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 30, "op": 570, "st": 30, "bm": 0}, {"ddd": 0, "ind": 16, "ty": 2, "nm": "Trail running.png", "cl": "png", "refId": "image_2", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 95, "s": [0]}, {"t": 96, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 95, "s": [400, -111.8, 0], "to": [0, 28.167, 0], "ti": [0, -28.167, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 102, "s": [400, 57.2, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 109, "s": [400, 57.2, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 152, "s": [400, 57.2, 0], "to": [-130.833, 0, 0], "ti": [130.833, 0, 0]}, {"t": 155, "s": [-385, 57.2, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [480, 104, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [80, 80, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 30, "op": 570, "st": 30, "bm": 0}, {"ddd": 0, "ind": 17, "ty": 2, "nm": "Trail running_basic.png", "cl": "png", "refId": "image_3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 57, "s": [400, -109.2, 0], "to": [0, 27.733, 0], "ti": [0, -27.733, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 64, "s": [400, 57.2, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 95, "s": [400, 57.2, 0], "to": [0, 27.833, 0], "ti": [0, -27.833, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 102, "s": [400, 224.2, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 104, "s": [400, 224.2, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 109, "s": [400, 224.2, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 152, "s": [400, 224.2, 0], "to": [-130.667, 0, 0], "ti": [130.667, 0, 0]}, {"t": 155, "s": [-384, 224.2, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [480, 104, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [80, 80, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 30, "op": 570, "st": 30, "bm": 0}, {"ddd": 0, "ind": 19, "ty": 2, "nm": "Hiking_basic.png", "cl": "png", "refId": "image_4", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 57, "s": [400, 57.8, 0], "to": [0, 27.733, 0], "ti": [0, -27.733, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 64, "s": [400, 224.2, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 95, "s": [400, 224.2, 0], "to": [0, 27.967, 0], "ti": [0, -27.967, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 102, "s": [400, 392, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 104, "s": [400, 392, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 109, "s": [400, 392, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 152, "s": [400, 392, 0], "to": [-130.667, 0, 0], "ti": [130.667, 0, 0]}, {"t": 155, "s": [-384, 392, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [480, 104, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [80, 80, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 30, "op": 570, "st": 30, "bm": 0}, {"ddd": 0, "ind": 23, "ty": 2, "nm": "Cycling basic.png", "cl": "png", "refId": "image_5", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 37, "s": [400, 224.2, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 57, "s": [400, 224.2, 0], "to": [0, 27.883, 0], "ti": [0, -27.883, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 64, "s": [400, 391.5, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 95, "s": [400, 391.5, 0], "to": [0, 28, 0], "ti": [0, -28, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 102, "s": [400, 559.5, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 104, "s": [400, 559.5, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 109, "s": [400, 559.5, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 152, "s": [400, 559.5, 0], "to": [-130.667, 0, 0], "ti": [130.667, 0, 0]}, {"t": 155, "s": [-384, 559.5, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [480, 104, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [80, 80, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 30, "op": 570, "st": 30, "bm": 0}, {"ddd": 0, "ind": 26, "ty": 2, "nm": "Running.png", "cl": "png", "refId": "image_6", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 30, "s": [0]}, {"t": 33, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 57, "s": [400, 391.5, 0], "to": [0, 22.568, 0], "ti": [0, -43.46, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 63, "s": [400, 534.895, 0], "to": [0, 10.235, 0], "ti": [0, -5.315, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 64, "s": [400, 558.831, 0], "to": [0, 0.01, 0], "ti": [0, -0.286, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 66, "s": [400, 558.818, 0], "to": [0, 0.503, 0], "ti": [0, 0.593, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 71, "s": [400, 558.823, 0], "to": [0, -2.942, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 95, "s": [400, 558.8, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 102, "s": [400, 714.8, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 104, "s": [400, 714.8, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 109, "s": [400, 714.8, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 152, "s": [400, 714.8, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 155, "s": [-384, 714.8, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [480.5, 106, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [80, 80, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 30, "op": 570, "st": 30, "bm": 0}, {"ddd": 0, "ind": 29, "ty": 2, "nm": "battery.png", "cl": "png", "refId": "image_7", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 30, "s": [0]}, {"t": 33, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 33, "s": [388, 633, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 57, "s": [388, 633, 0], "to": [0, 23.667, 0], "ti": [0, -27.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 63, "s": [388, 775, 0], "to": [0, 27.667, 0], "ti": [0, -4, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 64, "s": [388, 799, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 71, "s": [388, 799, 0], "to": [0, -3.5, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 95, "s": [388, 799, 0], "to": [0, -3.5, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 102, "s": [388, 874, 0], "to": [0, -3.5, 0], "ti": [0, 0, 0]}, {"t": 185, "s": [388, 874, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [122.5, 57.5, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 0, "op": 540, "st": 0, "bm": 0}, {"ddd": 0, "ind": 30, "ty": 2, "nm": "highlight (1).png", "cl": "png", "refId": "image_8", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 30, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 33, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 152, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 191, "s": [100]}, {"t": 192, "s": [5]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 64, "s": [400, 392, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 102, "s": [400, 392, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 152, "s": [400, 392, 0], "to": [-130, 0, 0], "ti": [130, 0, 0]}, {"t": 155, "s": [-380, 392, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [480, 320, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [80, 80, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 0, "op": 540, "st": 0, "bm": 0}, {"ddd": 0, "ind": 33, "ty": 2, "nm": "race_onboarding_intro_watchface.png", "cl": "png", "refId": "image_9", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 25, "s": [100]}, {"t": 30, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.609, "y": 0.609}, "t": 0, "s": [400, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.841, "y": 0.989}, "o": {"x": 0.609, "y": 0.315}, "t": 20, "s": [400, 400, 0], "to": [0, 133.333, 0], "ti": [0, -133.333, 0]}, {"t": 30, "s": [400, 1200, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [480.5, 480, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [80, 80, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 0, "op": 540, "st": 0, "bm": 0}, {"ddd": 0, "ind": 35, "ty": 2, "nm": "Frame_976_.png", "cl": "png", "refId": "image_10", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [400, 400, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [960, 960, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 0, "op": 540, "st": 0, "bm": 0}]}, {"id": "comp_2", "nm": "PNG sequence", "fr": 30, "layers": [{"ddd": 0, "ind": 1, "ty": 2, "nm": "crown_rotation_00000.png", "cl": "png", "refId": "image_12", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [645.5, 400, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [100, 200, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [25.5, 25.5, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 7, "op": 8, "st": 7, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 2, "nm": "crown_rotation_00001.png", "cl": "png", "refId": "image_13", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [645.5, 400, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [100, 200, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [25.5, 25.5, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 6, "op": 7, "st": 6, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 2, "nm": "crown_rotation_00002.png", "cl": "png", "refId": "image_14", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [645.5, 400, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [100, 200, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [25.5, 25.5, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 5, "op": 6, "st": 5, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 2, "nm": "crown_rotation_00003.png", "cl": "png", "refId": "image_15", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [645.5, 400, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [100, 200, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [25.5, 25.5, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 4, "op": 5, "st": 4, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 2, "nm": "crown_rotation_00004.png", "cl": "png", "refId": "image_16", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [645.5, 400, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [100, 200, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [25.5, 25.5, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 3, "op": 4, "st": 3, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 2, "nm": "crown_rotation_00005.png", "cl": "png", "refId": "image_17", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [645.5, 400, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [100, 200, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [25.5, 25.5, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 2, "op": 3, "st": 2, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 2, "nm": "crown_rotation_00006.png", "cl": "png", "refId": "image_18", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [645.5, 400, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [100, 200, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [25.5, 25.5, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 1, "op": 2, "st": 1, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 2, "nm": "crown_rotation_00007.png", "cl": "png", "refId": "image_19", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [645.5, 400, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [100, 200, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [25.5, 25.5, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 0, "op": 1, "st": 0, "bm": 0}]}, {"id": "sequence_0", "layers": [{"ty": 2, "sc": "#00ffff", "refId": "imgSeq_0", "ks": {"p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": [0]}, "o": {"a": 0, "k": [100]}}, "ip": 0, "st": 0, "op": 1, "sr": 1, "bm": 0}, {"ty": 2, "sc": "#00ffff", "refId": "imgSeq_1", "ks": {"p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": [0]}, "o": {"a": 0, "k": [100]}}, "ip": 1, "st": 1, "op": 2, "sr": 1, "bm": 0}, {"ty": 2, "sc": "#00ffff", "refId": "imgSeq_2", "ks": {"p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": [0]}, "o": {"a": 0, "k": [100]}}, "ip": 2, "st": 2, "op": 3, "sr": 1, "bm": 0}, {"ty": 2, "sc": "#00ffff", "refId": "imgSeq_3", "ks": {"p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": [0]}, "o": {"a": 0, "k": [100]}}, "ip": 3, "st": 3, "op": 4, "sr": 1, "bm": 0}, {"ty": 2, "sc": "#00ffff", "refId": "imgSeq_4", "ks": {"p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": [0]}, "o": {"a": 0, "k": [100]}}, "ip": 4, "st": 4, "op": 5, "sr": 1, "bm": 0}, {"ty": 2, "sc": "#00ffff", "refId": "imgSeq_5", "ks": {"p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": [0]}, "o": {"a": 0, "k": [100]}}, "ip": 5, "st": 5, "op": 6, "sr": 1, "bm": 0}, {"ty": 2, "sc": "#00ffff", "refId": "imgSeq_6", "ks": {"p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": [0]}, "o": {"a": 0, "k": [100]}}, "ip": 6, "st": 6, "op": 7, "sr": 1, "bm": 0}, {"ty": 2, "sc": "#00ffff", "refId": "imgSeq_7", "ks": {"p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": [0]}, "o": {"a": 0, "k": [100]}}, "ip": 7, "st": 7, "op": 9, "sr": 1, "bm": 0}]}], "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "形状图层 1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [400, 400, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [], "ip": 0, "op": 360, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 14, "ty": 0, "nm": "exercise start", "refId": "comp_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [400, 400, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [400, 400, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [45, 45, 100], "ix": 6, "l": 2}}, "ao": 0, "hasMask": true, "masksProperties": [{"inv": false, "mode": "a", "pt": {"a": 0, "k": {"i": [[219.687, 0], [0, -219.687], [-219.687, 0], [0, 219.687]], "o": [[-219.687, 0], [0, 219.687], [219.687, 0], [0, -219.687]], "v": [[393.333, 6.667], [-4.444, 404.444], [393.333, 802.222], [791.111, 404.444]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "蒙版 1"}], "w": 800, "h": 800, "ip": 0, "op": 273, "st": 0, "bm": 2}, {"ddd": 0, "ind": 17, "ty": 2, "nm": "SuuntoRace_AllBlack-front.png.png", "cl": "png png", "refId": "image_11", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [400, 400, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [400, 400, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [104, 104, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 0, "op": 360, "st": 0, "bm": 0}, {"ddd": 0, "ind": 19, "ty": 2, "nm": "crown_rotation_00000.png", "cl": "png", "refId": "image_12", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 147, "s": [645.5, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 149, "s": [641, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 155, "s": [645.5, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 203, "s": [645.5, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 205, "s": [641, 400, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 211, "s": [645.5, 400, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [100, 200, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [25.5, 25.5, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 102, "op": 462, "st": 102, "bm": 0}, {"ddd": 0, "ind": 20, "ty": 0, "nm": "PNG sequence", "refId": "comp_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [400, 400, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [400, 400, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 800, "h": 800, "ip": 94, "op": 102, "st": 94, "bm": 0}, {"ddd": 0, "ind": 21, "ty": 0, "nm": "crown_rotation_[00000-00007].png", "cl": "png", "refId": "sequence_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [645.5, 400, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [100, 200, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [25.5, 25.5, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "曲线", "np": 4, "mn": "ADBE CurvesCustom", "ix": 1, "en": 1, "ef": [{"ty": 7, "nm": "通道:", "mn": "ADBE CurvesCustom-0002", "ix": 1, "v": {"a": 0, "k": 1, "ix": 1}}, {}]}, {"ty": 5, "nm": "色相/饱和度", "np": 11, "mn": "ADBE HUE SATURATION", "ix": 2, "en": 1, "ef": [{"ty": 7, "nm": "通道控制", "mn": "ADBE HUE SATURATION-0002", "ix": 1, "v": {"a": 0, "k": 1, "ix": 1}}, {}, {"ty": 0, "nm": "主色相", "mn": "ADBE HUE SATURATION-0004", "ix": 3, "v": {"a": 0, "k": 0, "ix": 3}}, {"ty": 0, "nm": "主饱和度", "mn": "ADBE HUE SATURATION-0005", "ix": 4, "v": {"a": 0, "k": -70, "ix": 4}}, {"ty": 0, "nm": "主亮度", "mn": "ADBE HUE SATURATION-0006", "ix": 5, "v": {"a": 0, "k": 0, "ix": 5}}, {"ty": 7, "nm": "彩色化", "mn": "ADBE HUE SATURATION-0007", "ix": 6, "v": {"a": 0, "k": 0, "ix": 6}}, {"ty": 0, "nm": "着色色相", "mn": "ADBE HUE SATURATION-0008", "ix": 7, "v": {"a": 0, "k": 0, "ix": 7}}, {"ty": 0, "nm": "着色饱和度", "mn": "ADBE HUE SATURATION-0009", "ix": 8, "v": {"a": 0, "k": 25, "ix": 8}}, {"ty": 0, "nm": "着色亮度", "mn": "ADBE HUE SATURATION-0010", "ix": 9, "v": {"a": 0, "k": 0, "ix": 9}}]}], "tm": {"a": 0, "k": 0, "ix": 2}, "w": 200, "h": 400, "ip": 64, "op": 94, "st": 64, "bm": 0}, {"ddd": 0, "ind": 22, "ty": 0, "nm": "PNG sequence", "refId": "comp_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [400, 400, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [400, 400, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 800, "h": 800, "ip": 56, "op": 64, "st": 56, "bm": 0}, {"ddd": 0, "ind": 23, "ty": 0, "nm": "crown_rotation_[00000-00007].png", "cl": "png", "refId": "sequence_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [645.5, 400, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [100, 200, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [25.5, 25.5, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "曲线", "np": 4, "mn": "ADBE CurvesCustom", "ix": 1, "en": 1, "ef": [{"ty": 7, "nm": "通道:", "mn": "ADBE CurvesCustom-0002", "ix": 1, "v": {"a": 0, "k": 1, "ix": 1}}, {}]}, {"ty": 5, "nm": "色相/饱和度", "np": 11, "mn": "ADBE HUE SATURATION", "ix": 2, "en": 1, "ef": [{"ty": 7, "nm": "通道控制", "mn": "ADBE HUE SATURATION-0002", "ix": 1, "v": {"a": 0, "k": 1, "ix": 1}}, {}, {"ty": 0, "nm": "主色相", "mn": "ADBE HUE SATURATION-0004", "ix": 3, "v": {"a": 0, "k": 0, "ix": 3}}, {"ty": 0, "nm": "主饱和度", "mn": "ADBE HUE SATURATION-0005", "ix": 4, "v": {"a": 0, "k": -70, "ix": 4}}, {"ty": 0, "nm": "主亮度", "mn": "ADBE HUE SATURATION-0006", "ix": 5, "v": {"a": 0, "k": 0, "ix": 5}}, {"ty": 7, "nm": "彩色化", "mn": "ADBE HUE SATURATION-0007", "ix": 6, "v": {"a": 0, "k": 0, "ix": 6}}, {"ty": 0, "nm": "着色色相", "mn": "ADBE HUE SATURATION-0008", "ix": 7, "v": {"a": 0, "k": 0, "ix": 7}}, {"ty": 0, "nm": "着色饱和度", "mn": "ADBE HUE SATURATION-0009", "ix": 8, "v": {"a": 0, "k": 25, "ix": 8}}, {"ty": 0, "nm": "着色亮度", "mn": "ADBE HUE SATURATION-0010", "ix": 9, "v": {"a": 0, "k": 0, "ix": 9}}]}], "tm": {"a": 0, "k": 0, "ix": 2}, "w": 200, "h": 400, "ip": 26, "op": 56, "st": 26, "bm": 0}, {"ddd": 0, "ind": 24, "ty": 0, "nm": "PNG sequence", "refId": "comp_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [400, 400, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [400, 400, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 800, "h": 800, "ip": 18, "op": 26, "st": 18, "bm": 0}, {"ddd": 0, "ind": 25, "ty": 0, "nm": "crown_rotation_[00000-00007].png", "cl": "png", "refId": "sequence_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [645.5, 400, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [100, 200, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [25.5, 25.5, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "曲线", "np": 4, "mn": "ADBE CurvesCustom", "ix": 1, "en": 1, "ef": [{"ty": 7, "nm": "通道:", "mn": "ADBE CurvesCustom-0002", "ix": 1, "v": {"a": 0, "k": 1, "ix": 1}}, {}]}, {"ty": 5, "nm": "色相/饱和度", "np": 11, "mn": "ADBE HUE SATURATION", "ix": 2, "en": 1, "ef": [{"ty": 7, "nm": "通道控制", "mn": "ADBE HUE SATURATION-0002", "ix": 1, "v": {"a": 0, "k": 1, "ix": 1}}, {}, {"ty": 0, "nm": "主色相", "mn": "ADBE HUE SATURATION-0004", "ix": 3, "v": {"a": 0, "k": 0, "ix": 3}}, {"ty": 0, "nm": "主饱和度", "mn": "ADBE HUE SATURATION-0005", "ix": 4, "v": {"a": 0, "k": -70, "ix": 4}}, {"ty": 0, "nm": "主亮度", "mn": "ADBE HUE SATURATION-0006", "ix": 5, "v": {"a": 0, "k": 0, "ix": 5}}, {"ty": 7, "nm": "彩色化", "mn": "ADBE HUE SATURATION-0007", "ix": 6, "v": {"a": 0, "k": 0, "ix": 6}}, {"ty": 0, "nm": "着色色相", "mn": "ADBE HUE SATURATION-0008", "ix": 7, "v": {"a": 0, "k": 0, "ix": 7}}, {"ty": 0, "nm": "着色饱和度", "mn": "ADBE HUE SATURATION-0009", "ix": 8, "v": {"a": 0, "k": 25, "ix": 8}}, {"ty": 0, "nm": "着色亮度", "mn": "ADBE HUE SATURATION-0010", "ix": 9, "v": {"a": 0, "k": 0, "ix": 9}}]}], "tm": {"a": 0, "k": 0, "ix": 2}, "w": 200, "h": 400, "ip": 0, "op": 18, "st": -1, "bm": 0}, {"ddd": 0, "ind": 26, "ty": 1, "nm": "White BG", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [400, 400, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [400, 400, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "sw": 800, "sh": 800, "sc": "#ffffff", "ip": 0, "op": 360, "st": 0, "bm": 0}], "markers": [], "props": {}}