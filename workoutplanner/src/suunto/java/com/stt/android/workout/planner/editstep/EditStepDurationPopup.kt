package com.stt.android.workout.planner.editstep

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.material.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.soy.algorithms.planner.WorkoutStep
import com.stt.android.compose.theme.header
import com.stt.android.compose.theme.spacing
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.workout.planner.R
import com.stt.android.workout.planner.WorkoutPlannerDefaults
import com.stt.android.workout.planner.common.TextFieldFormattingHelper
import com.stt.android.workout.planner.defaultTime
import java.util.Locale
import com.stt.android.R as BaseR
import com.stt.android.core.R as CR

/**
 * A popup for setting the duration for a step. Includes title and an Ok button
 * for confirming the duration.
 *
 * This composable also converts the inputted values into a proper
 * [WorkoutStep.Exercise.Duration] object.
 */
@OptIn(ExperimentalComposeUiApi::class)
@Composable
fun EditStepDurationPopup(
    unit: MeasurementUnit,
    phase: WorkoutStep.Exercise.Phase,
    originalDuration: WorkoutStep.Exercise.Duration,
    onNewDuration: (WorkoutStep.Exercise.Duration) -> Unit,
    onDismissRequest: () -> Unit,
    modifier: Modifier = Modifier
) {
    var editMode by rememberSaveable { mutableStateOf(originalDuration.asModeEnum()) }
    var targetDistance by rememberSaveable(stateSaver = TextFieldValue.Saver) {
        mutableStateOf(
            TextFieldValue(
                TextFieldFormattingHelper.formatDistance(
                    unit,
                    originalDuration.distanceOrNull() ?: WorkoutPlannerDefaults.getDefaultDistance(unit)
                )
            )
        )
    }
    var targetTime by rememberSaveable(stateSaver = TextFieldValue.Saver) {
        mutableStateOf(
            TextFieldValue(
                TextFieldFormattingHelper.formatDurationAsHHMMSS(
                    originalDuration.secondsOrNull() ?: phase.defaultTime
                )
            )
        )
    }

    fun confirmDurationAndDismiss() {
        when (editMode) {
            EditStepDurationMode.DISTANCE -> {
                targetDistance.text.toDoubleOrNull()?.let {
                    onNewDuration(
                        WorkoutStep.Exercise.Duration.Or(
                            WorkoutStep.Exercise.Duration.Distance(unit.fromDistanceUnit(it)),
                            WorkoutStep.Exercise.Duration.LapButton
                        )
                    )
                }
            }
            EditStepDurationMode.TIME -> {
                TextFieldFormattingHelper.parseDurationAsSeconds(targetTime.text)?.let {
                    onNewDuration(
                        WorkoutStep.Exercise.Duration.Or(
                            WorkoutStep.Exercise.Duration.Time(it.toDouble()),
                            WorkoutStep.Exercise.Duration.LapButton
                        )
                    )
                }
            }
            EditStepDurationMode.LAP_BUTTON_PRESS -> {
                onNewDuration(
                    WorkoutStep.Exercise.Duration.LapButton
                )
            }

            EditStepDurationMode.NEVER -> {}
        }

        onDismissRequest()
    }

    Dialog(
        onDismissRequest = { confirmDurationAndDismiss() },
        // work around https://issuetracker.google.com/issues/221643630
        properties = DialogProperties(usePlatformDefaultWidth = false)
    ) {
        Surface(
            modifier = modifier
                .widthIn(max = dimensionResource(CR.dimen.dialog_max_width))
                .padding(horizontal = MaterialTheme.spacing.large)
                .fillMaxWidth(),
            shape = MaterialTheme.shapes.medium
        ) {
            Column(
                modifier = Modifier.verticalScroll(rememberScrollState())
            ) {
                Text(
                    text = stringResource(R.string.workout_planner_edit_step_duration_title)
                        .uppercase(Locale.getDefault()),
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(MaterialTheme.spacing.medium),
                    textAlign = TextAlign.Center,
                    style = MaterialTheme.typography.header
                )

                EditStepDurationOptions(
                    editMode = editMode,
                    onEditModeChange = { editMode = it },
                    targetDistance = targetDistance,
                    onTargetDistanceChanged = { targetDistance = it },
                    targetTime = targetTime,
                    onTargetTimeChanged = { targetTime = it },
                    unit = unit
                )

                Box(
                    modifier = Modifier.fillMaxWidth(),
                    contentAlignment = Alignment.CenterEnd
                ) {
                    TextButton(
                        onClick = { confirmDurationAndDismiss() },
                        modifier = Modifier.padding(MaterialTheme.spacing.medium),
                    ) {
                        Text(stringResource(BaseR.string.ok))
                    }
                }
            }
        }
    }
}

@Composable
@Preview
private fun EditStepDurationPopupPreview() {
    EditStepDurationPopup(
        unit = MeasurementUnit.METRIC,
        phase = WorkoutStep.Exercise.Phase.INTERVAL,
        originalDuration = WorkoutStep.Exercise.Duration.Time(5 * 60.0),
        onNewDuration = {},
        onDismissRequest = {}
    )
}
