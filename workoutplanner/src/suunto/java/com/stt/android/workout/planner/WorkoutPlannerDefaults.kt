package com.stt.android.workout.planner

import com.soy.algorithms.planner.WorkoutStep
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.domain.workout.ActivityType

object WorkoutPlannerDefaults {
    class StepConfig(
        val phase: WorkoutStep.Exercise.Phase,
        val duration: WorkoutStep.Exercise.Duration,
        val target: WorkoutStep.Exercise.Target
    ) {
        fun asPlannerStep() = WorkoutStep.Exercise(
            phase = phase,
            duration = duration,
            target = target
        )
    }

    val defaultActivityType = ActivityType.OTHER_6

    val warmUpHeartRateRange = 130..140
    val intervalHeartRateRange = 150..160
    val recoveryHeartRateRange = 100..110
    val coolDownHeartRateRange = 100..110

    val warmUpPowerRange = 140..150
    val intervalPowerRange = 200..210
    val recoveryPowerRange = 140..150
    val coolDownPowerRange = 140..150

    const val defaultWarmUpTime = 15 * 60 // 15 min
    const val defaultIntervalTime = 5 * 60 // 5 min
    const val defaultRecoveryTime = 1 * 60 // 1 min
    const val defaultCoolDownTime = 15 * 60 // 15 min

    @Suppress("ReplaceRangeStartEndInclusiveWithFirstLast")
    fun getIntervalStepDefaults(unit: MeasurementUnit) = StepConfig(
        phase = WorkoutStep.Exercise.Phase.INTERVAL,
        duration = WorkoutStep.Exercise.Duration.Or(
            WorkoutStep.Exercise.Duration.Distance(getDefaultDistance(unit)),
            WorkoutStep.Exercise.Duration.LapButton,
        ),
        target = WorkoutStep.Exercise.Target.HeartRate(
            value = intervalHeartRateRange.average(),
            min = intervalHeartRateRange.start.toDouble(),
            max = intervalHeartRateRange.endInclusive.toDouble(),
        )
    )

    const val initialContentRepeatStepCount = 5
    const val newRepeatStepCount = 3
    fun getRepeatStepDefaults(unit: MeasurementUnit) = listOf(
        getIntervalStepDefaults(unit),
        StepConfig(
            phase = WorkoutStep.Exercise.Phase.REST,
            duration = WorkoutStep.Exercise.Duration.Or(
                WorkoutStep.Exercise.Duration.Time(60.0), // 1 min
                WorkoutStep.Exercise.Duration.LapButton,
            ),
            target = WorkoutStep.Exercise.Target.HeartRate(
                value = recoveryHeartRateRange.average(),
                min = recoveryHeartRateRange.first.toDouble(),
                max = recoveryHeartRateRange.last.toDouble(),
            )
        )
    )

    fun getWarmupStepDefaults() = StepConfig(
        phase = WorkoutStep.Exercise.Phase.WARM_UP,
        duration = WorkoutStep.Exercise.Duration.Or(
            WorkoutStep.Exercise.Duration.Time(15 * 60.0), // 15 min
            WorkoutStep.Exercise.Duration.LapButton,
        ),
        target = WorkoutStep.Exercise.Target.HeartRate(
            value = warmUpHeartRateRange.average(),
            min = warmUpHeartRateRange.first.toDouble(),
            max = warmUpHeartRateRange.last.toDouble(),
        )
    )

    fun getCoolDownStepDefaults() = StepConfig(
        phase = WorkoutStep.Exercise.Phase.COOL_DOWN,
        duration = WorkoutStep.Exercise.Duration.Or(
            WorkoutStep.Exercise.Duration.Time(15 * 60.0), // 15 min
            WorkoutStep.Exercise.Duration.LapButton,
        ),
        target = WorkoutStep.Exercise.Target.HeartRate(
            value = coolDownHeartRateRange.average(),
            min = coolDownHeartRateRange.first.toDouble(),
            max = coolDownHeartRateRange.last.toDouble(),
        )
    )

    fun getDefaultDistance(unit: MeasurementUnit): Double =
        unit.fromDistanceUnit(1.0) // 1 km or 1 mile as meters
}

val WorkoutStep.Exercise.Phase.defaultHeartRateTarget: IntRange
    get() = when (this) {
        WorkoutStep.Exercise.Phase.WARM_UP -> WorkoutPlannerDefaults.warmUpHeartRateRange
        WorkoutStep.Exercise.Phase.INTERVAL -> WorkoutPlannerDefaults.intervalHeartRateRange
        WorkoutStep.Exercise.Phase.REST -> WorkoutPlannerDefaults.recoveryHeartRateRange
        WorkoutStep.Exercise.Phase.COOL_DOWN -> WorkoutPlannerDefaults.coolDownHeartRateRange
        else -> WorkoutPlannerDefaults.intervalHeartRateRange
    }

val WorkoutStep.Exercise.Phase.defaultPowerTarget: IntRange
    get() = when (this) {
        WorkoutStep.Exercise.Phase.WARM_UP -> WorkoutPlannerDefaults.warmUpPowerRange
        WorkoutStep.Exercise.Phase.INTERVAL -> WorkoutPlannerDefaults.intervalPowerRange
        WorkoutStep.Exercise.Phase.REST -> WorkoutPlannerDefaults.recoveryPowerRange
        WorkoutStep.Exercise.Phase.COOL_DOWN -> WorkoutPlannerDefaults.coolDownPowerRange
        else -> WorkoutPlannerDefaults.intervalPowerRange
    }

val WorkoutStep.Exercise.Phase.defaultTime: Int
    get() = when (this) {
        WorkoutStep.Exercise.Phase.WARM_UP -> WorkoutPlannerDefaults.defaultWarmUpTime
        WorkoutStep.Exercise.Phase.INTERVAL -> WorkoutPlannerDefaults.defaultIntervalTime
        WorkoutStep.Exercise.Phase.REST -> WorkoutPlannerDefaults.defaultRecoveryTime
        WorkoutStep.Exercise.Phase.COOL_DOWN -> WorkoutPlannerDefaults.defaultCoolDownTime
        else -> WorkoutPlannerDefaults.defaultIntervalTime
    }

fun WorkoutStep.Exercise.Phase.getDefaultSpeedTargetRange(
    unit: MeasurementUnit
): ClosedFloatingPointRange<Double> =
    if (unit == MeasurementUnit.IMPERIAL) {
        val rangeMph = if (this == WorkoutStep.Exercise.Phase.INTERVAL) {
            16.0..18.0
        } else {
            10.0..12.0
        }
        unit.fromSpeedUnit(rangeMph.start)..unit.fromSpeedUnit(rangeMph.endInclusive)
    } else {
        val rangeKmPerHour = if (this == WorkoutStep.Exercise.Phase.INTERVAL) {
            27.0..30.0
        } else {
            17.0..20.0
        }
        unit.fromSpeedUnit(rangeKmPerHour.start)..unit.fromSpeedUnit(rangeKmPerHour.endInclusive)
    }

fun WorkoutStep.Exercise.Phase.getDefaultPaceTargetRange(
    unit: MeasurementUnit
): ClosedFloatingPointRange<Double> =
    if (unit == MeasurementUnit.IMPERIAL) {
        val minutesPerMile = if (this == WorkoutStep.Exercise.Phase.INTERVAL) {
            6.5..7.0
        } else {
            8.5..9.0
        }

        // Use end of range for min speed and start of range for max speed
        unit.fromPaceUnit(minutesPerMile.endInclusive)..unit.fromPaceUnit(minutesPerMile.start)
    } else {
        val minutesPerKm = if (this == WorkoutStep.Exercise.Phase.INTERVAL) {
            4.5..5.0
        } else {
            5.5..6.0
        }

        // Use end of range for min speed and start of range for max speed
        unit.fromPaceUnit(minutesPerKm.endInclusive)..unit.fromPaceUnit(minutesPerKm.start)
    }

private fun IntRange.average(): Double = (first + last).toDouble() / 2.0
