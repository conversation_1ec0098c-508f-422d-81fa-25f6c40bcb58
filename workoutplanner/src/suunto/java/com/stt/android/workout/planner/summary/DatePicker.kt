package com.stt.android.workout.planner.summary

import androidx.compose.animation.animateContentSize
import androidx.compose.foundation.background
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Checkbox
import androidx.compose.material.CheckboxDefaults
import androidx.compose.material.Icon
import androidx.compose.material.IconButton
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.material3.BasicAlertDialog
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringArrayResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.window.DialogProperties
import com.stt.android.compose.modifiers.clickableThrottleFirst
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.bodyBold
import com.stt.android.compose.theme.disabledColor
import com.stt.android.compose.theme.spacing
import com.stt.android.workout.planner.R
import com.stt.android.workout.planner.common.MonthCalendar
import java.time.LocalDate
import java.time.YearMonth
import java.time.temporal.WeekFields

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DatePicker(
    onDismissRequest: () -> Unit,
    weekFields: WeekFields,
    date: LocalDate?,
    onDateChange: (LocalDate) -> Unit,
    minDate: LocalDate,
    selectedMonth: YearMonth,
    onPreviousMonthClick: () -> Unit,
    onNextMonthClick: () -> Unit,
    enablePreviousMonth: Boolean,
    skipDateSelection: Boolean, // "I don't prefer any specific day"
    onSkipDateSelectionChange: (Boolean) -> Unit,
    modifier: Modifier = Modifier,
) {
    BasicAlertDialog(
        modifier = modifier
            .clip(RoundedCornerShape(MaterialTheme.spacing.small))
            .background(MaterialTheme.colors.surface)
            .padding(MaterialTheme.spacing.smaller),
        onDismissRequest = onDismissRequest,
        properties = DialogProperties(usePlatformDefaultWidth = false),
    ) {
        DateSelection(
            weekFields = weekFields,
            date = date,
            onDateChange = onDateChange,
            minDate = minDate,
            selectedMonth = selectedMonth,
            onPreviousMonthClick = onPreviousMonthClick,
            onNextMonthClick = onNextMonthClick,
            enablePreviousMonth = enablePreviousMonth,
            skipDateSelection = skipDateSelection,
            onSkipDateSelectionChange = onSkipDateSelectionChange,
        )
    }
}

@Composable
private fun DateSelection(
    weekFields: WeekFields,
    date: LocalDate?,
    onDateChange: (LocalDate) -> Unit,
    minDate: LocalDate,
    selectedMonth: YearMonth,
    onPreviousMonthClick: () -> Unit,
    onNextMonthClick: () -> Unit,
    enablePreviousMonth: Boolean,
    skipDateSelection: Boolean, // "I don't prefer any specific day"
    onSkipDateSelectionChange: (Boolean) -> Unit,
    modifier: Modifier = Modifier,
) {
    Column(modifier.width(IntrinsicSize.Min)) {
        Row(
            modifier = Modifier.clickableThrottleFirst {
                onSkipDateSelectionChange(!skipDateSelection)
            },
            verticalAlignment = Alignment.CenterVertically
        ) {
            Checkbox(
                checked = skipDateSelection,
                onCheckedChange = onSkipDateSelectionChange,
                colors = CheckboxDefaults.colors(checkedColor = MaterialTheme.colors.primary)
            )

            Text(stringResource(R.string.workout_planner_skip_date_selection))
        }

        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            val enabledChevronColor = MaterialTheme.colors.primary
            val disabledChevronColor = enabledChevronColor.copy(alpha = 0.25f)

            IconButton(
                onClick = onPreviousMonthClick,
                enabled = enablePreviousMonth
            ) {
                Icon(
                    painter = painterResource(id = R.drawable.chevron_left),
                    tint = if (enablePreviousMonth) enabledChevronColor else disabledChevronColor,
                    contentDescription = stringResource(com.stt.android.R.string.previous_page)
                )
            }

            val abbreviatedMonth =
                stringArrayResource(com.stt.android.R.array.abbreviated_months)[selectedMonth.monthValue - 1]
            Text(
                text = "$abbreviatedMonth ${selectedMonth.year}",
                style = MaterialTheme.typography.bodyBold,
                color = if (skipDateSelection) MaterialTheme.colors.disabledColor else Color.Unspecified
            )

            IconButton(
                onClick = onNextMonthClick,
                enabled = !skipDateSelection
            ) {
                Icon(
                    painter = painterResource(id = com.stt.android.R.drawable.chevron_right),
                    tint = if (!skipDateSelection) enabledChevronColor else disabledChevronColor,
                    contentDescription = stringResource(com.stt.android.R.string.next_page)
                )
            }
        }

        MonthCalendar(
            modifier = Modifier
                .horizontalScroll(rememberScrollState())
                .verticalScroll(rememberScrollState())
                .padding(
                    top = MaterialTheme.spacing.small,
                    bottom = MaterialTheme.spacing.smaller,
                    start = MaterialTheme.spacing.smaller,
                    end = MaterialTheme.spacing.smaller,
                )
                .animateContentSize(),
            month = selectedMonth,
            weekFields = weekFields,
            selectedDate = if (skipDateSelection) null else date,
            onSelectedDateChange = onDateChange,
            minDate = minDate,
            enabled = !skipDateSelection
        )
    }
}

@Composable
@Preview(widthDp = 320)
@Preview(widthDp = 760)
private fun BasicDetailsFormPreview() = AppTheme {
    Surface {
        var month by remember { mutableStateOf(YearMonth.of(2022, 5)) }
        var date by remember { mutableStateOf<LocalDate?>(null) }
        var skipDateSelection by remember { mutableStateOf(false) }

        DateSelection(
            weekFields = WeekFields.SUNDAY_START,
            skipDateSelection = skipDateSelection,
            onSkipDateSelectionChange = { skipDateSelection = it },
            date = date,
            onDateChange = { date = it },
            minDate = LocalDate.of(2022, 5, 8),
            selectedMonth = month,
            onPreviousMonthClick = { month = month.minusMonths(1L) },
            onNextMonthClick = { month = month.plusMonths(1L) },
            enablePreviousMonth = month > YearMonth.of(2022, 5)
        )
    }
}
