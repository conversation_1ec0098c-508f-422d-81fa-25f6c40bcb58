package com.stt.android.domain.sleep

import com.stt.android.utils.splitByCompareLast
import com.suunto.algorithms.data.HeartRate
import java.time.ZonedDateTime
import kotlin.math.roundToInt
import kotlin.time.Duration
import kotlin.time.Duration.Companion.hours
import kotlin.time.Duration.Companion.milliseconds

/**
 * Represents sleep data for a single day. It's an aggregate of [SleepSegment]s.
 * @param serialNumber device serial number
 * @param timestamp UTC timestamp in ms of the start of the day
 * @param longSleep sleep data from long sleep session (aka night sleep), optional
 * @param naps nap list, maybe a day has multiple nap
 */
data class Sleep(
    val serialNumber: String,
    val timestamp: Long,
    val longSleep: LongSleep? = null,
    val naps: List<Nap> = emptyList(),
) {
    val hasLongSleep = longSleep != null && longSleep.sleepDuration > Duration.ZERO

    val hasNap = naps.isNotEmpty() && (getMergedNap()?.duration ?: Duration.ZERO) > Duration.ZERO

    val isEmpty = !hasLongSleep && !hasNap

    val hasOnlyNap = !hasLongSleep && hasNap

    val totalSleepDuration: Duration = (longSleep?.sleepDuration ?: Duration.ZERO) + (getMergedNap()?.duration ?: Duration.ZERO)

    fun getMergedNap(): Nap? {
        if (naps.isEmpty()) return null

        val duration = naps.sumOf { it.duration.inWholeMilliseconds }.milliseconds
        val fellAsleep = naps.first().fellAsleep
        val wokeUp = naps.last().wokeUp
        return Nap(
            duration = duration,
            fellAsleep = fellAsleep,
            wokeUp = wokeUp
        )
    }
}

/**
 * Represents "night" sleep data for a single day.
 * @param sleepDuration Sleep duration
 * @param deepSleepDuration Deep sleep duration
 * @param remSleepDuration REM sleep duration, only s7
 * @param lightSleepDuration Light sleep duration, only s7
 * @param awakeDuration Awake duration
 * @param feeling
 * @param avgHr Average heart rate
 * @param minHr Minimum heart rate
 * @param quality Sleep quality in range 0..1
 * @param fellAsleep UTC timestamp when sleep started
 * @param wokeUp UTC timestamp when sleep ended
 * @param bodyResourcesInsight enum with insights on stress/recovery
 * @param maxSpO2 max SpO2 measured during the night
 * @param altitude altitude in meters at which the max SpO2 value was measured
 * @param avgHrv average HRV in ms during the night
 */
data class LongSleep(
    val sleepDuration: Duration,
    val deepSleepDuration: Duration? = null,
    val remSleepDuration: Duration? = null,
    val lightSleepDuration: Duration? = null,
    val awakeDuration: Duration? = null,
    val feeling: Int? = null,
    val avgHr: HeartRate? = null,
    val minHr: HeartRate? = null,
    val quality: Float? = null,
    val fellAsleep: Long,
    val wokeUp: Long,
    val bodyResourcesInsight: BodyResourcesInsight = BodyResourcesInsight.NONE,
    val maxSpO2: Float? = null, // 0..1
    val altitude: Float? = null, // meters
    val avgHrv: Float? = null,
) {
    val qualityPerc: Int? = quality?.times(100)?.roundToInt()
}

data class Nap(
    val duration: Duration,
    val fellAsleep: Long,
    val wokeUp: Long,
)

data class SleepSegment(
    val serial: String,
    val timestamp: Long,
    val quality: Float? = null, // 0..1
    val avgHr: HeartRate? = null,
    val minHr: HeartRate? = null,
    val feeling: Int? = null,
    val duration: Duration,
    val deepSleepDuration: Duration? = null,
    val remSleepDuration: Duration? = null,
    val lightSleepDuration: Duration? = null,
    val timeISO8601: ZonedDateTime,
    val bedtimeStart: ZonedDateTime?,
    val bedtimeEnd: ZonedDateTime?,
    val bodyResourcesInsightId: Int? = null,
    val sleepId: Long? = null,
    val maxSpO2: Float? = null, // 0..1
    val altitude: Float? = null, // meters
    val avgHrv: Float? = null, // ms
    val avgHrvSampleCount: Int? = null, // how many samples were used to calculate avgHrv
    /**
     * Gomore algorithm.
     */
    val isNap: Boolean? = null,
    /**
     * Also called SOL, the total time the user was awake between start of sleep segment and sleep onset. Gomore algorithm.
     */
    val sleepOnsetLatencyDuration: Duration? = null,
    /**
     * Also called WASO, the total time the user was awake in between sleep onset and sleep offset. Gomore algorithm.
     */
    val wakeAfterSleepOnsetDuration: Duration? = null,
    /**
     * Also called WBO, the total time the user was awake after sleep offset, before off bed (end of sleep segment). Gomore algorithm.
     */
    val wakeBeforeOffBedDuration: Duration? = null,
) {
    val awakeDuration: Duration
        get() = (sleepOnsetLatencyDuration ?: Duration.ZERO) +
            (wakeAfterSleepOnsetDuration ?: Duration.ZERO) +
            (wakeBeforeOffBedDuration ?: Duration.ZERO)
}

enum class SleepStage {
    AWAKE,
    REM,
    LIGHT,
    DEEP,
}

data class SleepStageInterval(
    val duration: Duration,
    val stage: SleepStage,
    val timeISO8601: ZonedDateTime,
) {
    val startTimestamp: Long get() = timeISO8601.toEpochSecond()
}

private val EIGHT_HOURS_MS: Long = 8.hours.inWholeMilliseconds

/**
 * List<SleepSegment> needs to be sorted by ascending timestamp.
 * Current logic: GoMore algorithm can assign different sleep ids for segments from the same
 * night if the user has clearly been awake in between. Therefore sleepId is not used anymore and
 * the logic is aligned with iOS to solve this. See comments within the code.
 * Deprecated logic: Aggregation logic is different for s7 and NG1.
 * S7: we have `sleepId` in each sleep segment so we can aggregate when the id changes
 * NG1: The aggregation logic for now is to split nights by "4h awake" intervals
 */
fun List<SleepSegment>.aggregateToSleepNights(): List<Sleep> {
    return this.splitByCompareLast { s1, s2 ->
        // Aligned with iOS:
        // The nights need to be separated in the following cases：
        // 1.If `prevSegment.isNap` and `segment.isNotNap`, the segment is new night.
        // 2.If `prevSegment.isNap` and `segment.isNap` and two segments are not on the same day, the nap of segment belongs to the next day.
        // 3.If `prevSegment.isNotNap` and `segment.isNap` and two segments are not on the same day, the nap of segment belongs to the next day.
        // 4.If neither segment is a nap, use 8-hour separation between the segments as distinction of the nights.
        // 5.It may be the case that there is only a nap without a long sleep.

        val isPrevNap = s1.isNap == true
        val isNap = s2.isNap == true
        val isInSameDay = isSameDay(s1, s2)
        val gapMoreThan8h =  s2.timestamp - (s1.timestamp + s1.duration.inWholeMilliseconds) >= EIGHT_HOURS_MS

        (isPrevNap && !isNap) ||
            (isPrevNap && isNap && !isInSameDay) ||
            (!isPrevNap && isNap && !isInSameDay) ||
            (!isPrevNap && !isNap && gapMoreThan8h)
    }.mapNotNull { SleepHelper.toSleep(it) }
}

private fun isSameDay(s1: SleepSegment, s2: SleepSegment): Boolean {
    val s1EndDate = s1.timeISO8601.plusSeconds(s1.duration.inWholeSeconds).toLocalDate()
    val s2EndDate = s2.timeISO8601.plusSeconds(s2.duration.inWholeSeconds).toLocalDate()
    return s1EndDate.isEqual(s2EndDate)
}
