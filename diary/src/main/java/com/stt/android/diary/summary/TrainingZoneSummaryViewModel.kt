package com.stt.android.diary.summary

import androidx.lifecycle.asFlow
import androidx.lifecycle.viewModelScope
import androidx.paging.InvalidatingPagingSourceFactory
import androidx.paging.Pager
import androidx.paging.PagingConfig
import androidx.paging.PagingData
import androidx.paging.cachedIn
import com.stt.android.chart.api.model.ChartGranularity
import com.stt.android.chart.impl.usecases.FormatChartHighlightDateTimeUseCase
import com.stt.android.common.coroutines.CoroutineViewModel
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.UserSettingsController
import com.stt.android.core.domain.workouts.CoreActivityGroup
import com.stt.android.core.domain.workouts.CoreActivityGrouping
import com.stt.android.core.domain.workouts.CoreActivityType
import com.stt.android.core.domain.workouts.MINIMUM_COUNT_TO_SHOW_GROUP
import com.stt.android.diary.graph.data.ChartPage
import com.stt.android.diary.graph.data.SelectedPrimaryGraphLiveData
import com.stt.android.diary.graph.data.SelectedSecondaryGraphLiveData
import com.stt.android.diary.trainingv2.summary.CreateTrainingGraphChartDataUseCase
import com.stt.android.diary.trainingv2.summary.HighlightedData
import com.stt.android.diary.trainingv2.summary.TrainingGraphEntryYFormatter
import com.stt.android.diary.trainingv2.summary.TrainingGraphHighlightedData
import com.stt.android.diary.trainingv2.summary.TrainingGraphPage
import com.stt.android.diary.trainingv2.summary.TrainingGraphType
import com.stt.android.diary.trainingv2.summary.TrainingGraphUiState
import com.stt.android.diary.trainingv2.summary.titleResId
import com.stt.android.diary.trainingv2.summary.unitResId
import com.stt.android.domain.diary.models.DiaryPage
import com.stt.android.domain.diary.models.GraphDataType
import com.stt.android.domain.diary.models.GraphTimeRange
import com.stt.android.domain.diary.models.primaryGraphDataTypes
import com.stt.android.domain.diary.models.secondaryGraphDataTypes
import com.stt.android.domain.tags.UserTagsRepository
import com.stt.android.domain.user.subscription.IsSubscribedToPremiumUseCase
import com.stt.android.domain.workouts.SummaryWorkoutHeader
import com.stt.android.domain.workouts.WorkoutDataSource
import com.stt.android.domain.workouts.tag.SuuntoTag
import com.stt.android.infomodel.SummaryItem
import com.stt.android.utils.CalendarProvider
import com.stt.android.viewmodel.sort
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.toImmutableList
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.mapLatest
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import timber.log.Timber
import java.time.Clock
import javax.inject.Inject
import com.stt.android.chart.impl.R as ChartR
import com.stt.android.coroutines.combine as combineMulti

@HiltViewModel
class TrainingZoneSummaryViewModel @Inject constructor(
    private val trainingZoneSummaryGroupingCalculatorUseCase: TrainingZoneSummaryGroupingCalculatorUseCase,
    private val selectionsRepository: TrainingZoneSummarySelectionsRepository,
    private val formatter: TrainingZoneSummaryFormatter,
    userSettingsController: UserSettingsController,
    private val trainingZoneSummaryAnalytics: TrainingZoneSummaryAnalytics,
    private val clock: Clock,
    private val calendarProvider: CalendarProvider,
    val selectedPrimaryGraphLiveData: SelectedPrimaryGraphLiveData,
    val selectedSecondaryGraphLiveData: SelectedSecondaryGraphLiveData,
    isSubscribedToPremiumUseCase: IsSubscribedToPremiumUseCase,
    sortingUseCase: TrainingZoneSummarySortingUseCase,
    getWorkoutsUseCase: TrainingZoneSummaryGetWorkoutsUseCase,
    getRowsUseCase: TrainingZoneSummaryGetRowsUseCase,
    getTotalRowUseCase: TrainingZoneSummaryGetTotalRowUseCase,
    getColumnsUseCase: TrainingZoneSummaryGetSummaryColumnsUseCase,
    workoutDataSource: WorkoutDataSource,
    currentUserController: CurrentUserController,
    coroutinesDispatchers: CoroutinesDispatchers,
    userTagsRepository: UserTagsRepository,
    private val createTrainingGraphChartDataUseCase: CreateTrainingGraphChartDataUseCase,
    private val trainingGraphEntryYFormatter: TrainingGraphEntryYFormatter,
    private val formatChartHighlightDateTimeUseCase: FormatChartHighlightDateTimeUseCase,
    private val timeFrameSummaryWorkoutsCalculator: TimeFrameSummaryWorkoutsCalculator,
) : CoroutineViewModel(coroutinesDispatchers) {

    private val _allDoneActivityTypes = MutableStateFlow<List<CoreActivityType>>(emptyList())

    init {
        selectedPrimaryGraphLiveData.initialize(DiaryPage.SUMMARY)
        selectedSecondaryGraphLiveData.initialize(DiaryPage.SUMMARY)

        launch(Dispatchers.IO) {
            workoutDataSource
                .loadAllDoneActivityTypes(currentUserController.username)
                .mapNotNull { id ->
                    runCatching { CoreActivityType.valueOf(id) }.getOrNull()
                }
                .distinct()
                .let { types ->
                    _allDoneActivityTypes.value = types
                }
        }
    }

    private val measurementUnit = userSettingsController.settings.measurementUnit
    private val firstDayOfWeek = userSettingsController.settings.firstDayOfTheWeek
    private val distanceUnit = measurementUnit.distanceUnit

    val allSuuntoTags: List<SuuntoTag> = SuuntoTag.entries
    val allUserTags = flow {
        emit(userTagsRepository.getAllUserTags().sort())
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5_000),
        initialValue = emptyList()
    )

    val defaultSports =
        TrainingZoneSummarySelectionsRepository.DEFAULT_FILTER_SPORTS.toImmutableList()
    val defaultFilterUiState by lazy {
        TrainingZoneSummaryFilterUiState(
            grouping = TrainingZoneSummarySelectionsRepository.DEFAULT_FILTER_GROUPING,
            sports = defaultSports,
            selectedStartDateMillis = TrainingZoneSummarySelectionsRepository.DEFAULT_FILTER_START_DATE,
            selectedEndDateMillis = TrainingZoneSummarySelectionsRepository.DEFAULT_FILTER_END_DATE,
            distance = TrainingZoneSummarySelectionsRepository.DEFAULT_FILTER_DISTANCE,
            distanceUnit = distanceUnit,
            summarySuuntoTags = TrainingZoneSummarySelectionsRepository.DEFAULT_FILTER_SUMMARY_SUUNTO_TAGS.toImmutableList(),
            summaryUserTags = TrainingZoneSummarySelectionsRepository.DEFAULT_FILTER_SUMMARY_USER_TAGS.toImmutableList(),
            showEmptyRowsChecked = TrainingZoneSummarySelectionsRepository.DEFAULT_SHOW_EMPTY_ROWS_CHECKED,
        )
    }

    private val _filterUiState = MutableStateFlow(
        TrainingZoneSummaryFilterUiState(
            grouping = selectionsRepository.defaultGrouping,
            sports = selectionsRepository.defaultSports.toImmutableList(),
            selectedStartDateMillis = selectionsRepository.defaultStartDate,
            selectedEndDateMillis = selectionsRepository.defaultEndDate,
            distance = selectionsRepository.defaultDistance,
            distanceUnit = distanceUnit,
            summarySuuntoTags = selectionsRepository.defaultSummarySuuntoTags.toImmutableList(),
            summaryUserTags = selectionsRepository.defaultSummaryUserTags.toImmutableList(),
            showEmptyRowsChecked = selectionsRepository.defaultShowEmptyRowsChecked
        )
    )
    val filterUiState = _filterUiState.asStateFlow()

    val hasSelectedInFilter = _filterUiState.map { filter ->
        with(defaultFilterUiState) {
            selectedStartDateMillis != filter.selectedStartDateMillis ||
                selectedEndDateMillis != filter.selectedEndDateMillis ||
                summarySuuntoTags != filter.summarySuuntoTags ||
                summaryUserTags != filter.summaryUserTags ||
                distance != filter.distance ||
                showEmptyRowsChecked != filter.showEmptyRowsChecked
        }
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5_000),
        initialValue = false
    )

    private val _mainSorting: MutableStateFlow<TrainingZoneSummarySortingUiState> =
        MutableStateFlow(
            TrainingZoneSummarySortingUiState(
                selectedColumn = selectionsRepository.defaultSortingColumn,
                selectedOrder = selectionsRepository.defaultSortingOrder
            )
        )

    val mainSorting = _mainSorting.asStateFlow()

    private val _detailsSorting: MutableStateFlow<TrainingZoneSummarySortingUiState> =
        MutableStateFlow(
            TrainingZoneSummarySortingUiState(
                selectedColumn = TrainingZoneSummaryColumn.DATE,
                selectedOrder = TrainingZoneSummarySortingOrder.DESCENDING
            )
        )

    val detailsSorting = _detailsSorting.asStateFlow()

    private val _selectedWorkoutsIds: MutableStateFlow<List<Int>> = MutableStateFlow(
        emptyList()
    )

    private val _isDetailsLoading = MutableStateFlow(true)
    val isDetailsLoading = _isDetailsLoading.asStateFlow()

    val hasWorkouts: StateFlow<Boolean?> = workoutDataSource
        .hasWorkouts(currentUserController.username)
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5_000),
            initialValue = null
        )

    private val _layoutType = MutableStateFlow(selectionsRepository.defaultLayoutType)
    val layoutType = _layoutType.asStateFlow()

    val hasPremium: StateFlow<Boolean> = isSubscribedToPremiumUseCase().stateIn(
        scope = viewModelScope,
        SharingStarted.Eagerly,
        false
    )

    fun setStartDate(millis: Long?) {
        _filterUiState.update { it.copy(selectedStartDateMillis = millis) }
        selectionsRepository.defaultStartDate = millis
    }

    fun setEndDate(millis: Long?) {
        _filterUiState.update {
            it.copy(
                selectedEndDateMillis = millis
            )
        }
        selectionsRepository.defaultEndDate = millis
    }

    fun updateDistance(minDistance: Float, maxDistance: Float) {
        val distanceUiState = DistanceUiState.fromFilterRange(
            minDistance = minDistance,
            maxDistance = maxDistance
        )
        _filterUiState.update {
            it.copy(distance = distanceUiState)
        }
        selectionsRepository.defaultDistance = distanceUiState
    }

    fun updateSports(newValue: List<CoreActivityType>) {
        _filterUiState.update { it.copy(sports = newValue.toImmutableList()) }
        selectionsRepository.defaultSports = newValue
    }

    fun updateGrouping(newValue: TrainingZoneSummaryGrouping) {
        _filterUiState.update { it.copy(grouping = newValue) }
        selectionsRepository.defaultGrouping = newValue

        // sorting by number of workouts is not possible when the grouping is by activity
        // This handles the case where the number of workouts is selected and user switches to by activity grouping, we use date as default
        if (newValue == TrainingZoneSummaryGrouping.BY_ACTIVITY && _mainSorting.value.selectedColumn == TrainingZoneSummaryColumn.NUMBER_OF_WORKOUTS) {
            _mainSorting.update { it.copy(selectedColumn = TrainingZoneSummaryColumn.DATE) }
        }
    }

    fun resetFilters() {
        _filterUiState.value = defaultFilterUiState
        _graphTimeRange.value = defaultGraphTimeRange
        selectionsRepository.resetSelections()
    }

    fun setSelectedWorkouts(workouts: List<SummaryWorkoutHeader>, date: String) {
        _isDetailsLoading.value = true
        _selectedWorkoutsIds.value = workouts.map { it.id }
        _selectedDate.value = date
        _detailsSorting.value = _mainSorting.value.let {
            TrainingZoneSummarySortingUiState(
                selectedColumn = it.selectedColumn.takeIf { it != TrainingZoneSummaryColumn.NUMBER_OF_WORKOUTS }
                    ?: TrainingZoneSummaryColumn.DATE,
                selectedOrder = it.selectedOrder
            )
        }

        _detailsSorting.value = _mainSorting.value.copy(
            selectedColumn = _mainSorting.value.selectedColumn.takeIf { it != TrainingZoneSummaryColumn.NUMBER_OF_WORKOUTS }
                ?: TrainingZoneSummaryColumn.DATE,
        )
    }

    fun resetSelectedWorkouts() {
        _selectedWorkoutsIds.value = emptyList()
        _selectedDate.value = ""
    }

    fun setMainSorting(selectedColumn: TrainingZoneSummaryColumn) =
        setSorting(selectedColumn, _mainSorting)

    fun setDetailsSorting(selectedColumn: TrainingZoneSummaryColumn) =
        setSorting(selectedColumn, _detailsSorting)

    private fun setSorting(
        selectedColumn: TrainingZoneSummaryColumn,
        mutableFlow: MutableStateFlow<TrainingZoneSummarySortingUiState>
    ) {
        val currentSortingUiState = mutableFlow.value
        val currentSelectedColumn = currentSortingUiState.selectedColumn
        val currentSelectedSorting = currentSortingUiState.selectedOrder

        mutableFlow.value = TrainingZoneSummarySortingUiState(
            selectedColumn = selectedColumn,
            selectedOrder = if (currentSelectedColumn != selectedColumn || currentSelectedSorting == TrainingZoneSummarySortingOrder.ASCENDING) {
                TrainingZoneSummarySortingOrder.DESCENDING
            } else {
                TrainingZoneSummarySortingOrder.ASCENDING
            }
        )
    }

    // ----------------------- Improved structure
    private val _sports = _filterUiState.mapLatest { it.sports }.distinctUntilChanged()
    private val _grouping = _filterUiState.mapLatest { it.grouping }.distinctUntilChanged()
    private val _distance =
        _filterUiState.mapLatest { it.distance }.debounce(300).distinctUntilChanged()
    private val _selectedStartDateMillis =
        _filterUiState.mapLatest { it.selectedStartDateMillis }.distinctUntilChanged()
    private val _selectedEndDateMillis =
        _filterUiState.mapLatest { it.selectedEndDateMillis }.distinctUntilChanged()
    private val _isDistanceSupported =
        _filterUiState.mapLatest { it.isDistanceSupported }.distinctUntilChanged()
    private val _suuntoTags = _filterUiState.mapLatest { it.suuntoTags }.distinctUntilChanged()
    private val _userTags = _filterUiState.mapLatest { it.userTags }.distinctUntilChanged()
    private val _showEmptyRowsChecked =
        _filterUiState.mapLatest { it.showEmptyRowsChecked }.distinctUntilChanged()

    val grouping = _grouping

    val formattedStartDate = _selectedStartDateMillis
        .mapLatest { formatter.formatDatePickerDateDate(it) }
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5_000),
            initialValue = null
        )

    val formattedEndDate = _selectedEndDateMillis
        .mapLatest { formatter.formatDatePickerDateDate(it) }
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5_000),
            initialValue = null
        )

    val formattedSelectedDates = combine(
        _selectedStartDateMillis,
        _selectedEndDateMillis
    ) { selectedStartDateMillis, selectedEndDateMillis ->
        formatter.formatFilterDate(selectedStartDateMillis, selectedEndDateMillis)
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5_000),
        initialValue = null
    )

    val isShowEmptyRowsEnabled = combine(_grouping, _layoutType) { grouping, layoutType ->
        grouping != TrainingZoneSummaryGrouping.BY_ACTIVITY && layoutType == TrainingZoneSummaryLayoutType.TABLE
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5_000),
        initialValue = false
    )

    private val _workouts = combineMulti(
        _sports,
        _distance,
        _selectedStartDateMillis,
        _selectedEndDateMillis,
        _isDistanceSupported,
        _suuntoTags,
        _userTags
    ) { sports, distance, selectedStartDateMillis, selectedEndDateMillis, isDistanceSupported, suuntoTags, userTags ->
        getWorkoutsUseCase(
            sports,
            distance,
            selectedStartDateMillis,
            selectedEndDateMillis,
            isDistanceSupported,
            suuntoTags,
            userTags
        )
    }.flatMapLatest { it.map { it } }.distinctUntilChanged()

    private val _groupedWorkouts = combine(
        _workouts,
        _grouping,
    ) { workouts, grouping ->
        when (grouping) {
            TrainingZoneSummaryGrouping.WEEKLY -> {
                trainingZoneSummaryGroupingCalculatorUseCase.calculateWeekly(
                    filteredWorkouts = workouts,
                    firstDayOfTheWeek = firstDayOfWeek,
                )
            }

            TrainingZoneSummaryGrouping.MONTHLY -> {
                trainingZoneSummaryGroupingCalculatorUseCase.calculateMonthly(
                    filteredWorkouts = workouts,
                )
            }

            TrainingZoneSummaryGrouping.YEARLY -> {
                trainingZoneSummaryGroupingCalculatorUseCase.calculateYearly(
                    filteredWorkouts = workouts,
                    firstDayOfTheWeek = firstDayOfWeek,
                )
            }

            TrainingZoneSummaryGrouping.BY_ACTIVITY -> trainingZoneSummaryGroupingCalculatorUseCase.calculateByActivity(
                workouts
            )
        }
    }.distinctUntilChanged()

    @OptIn(ExperimentalCoroutinesApi::class)
    val filteredWorkoutSize =
        _workouts.mapLatest { workouts ->
            workouts.size
        }.stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5_000),
            initialValue = 0
        )

    private val _total = _workouts.mapLatest { getTotalRowUseCase(it) }.distinctUntilChanged()

    private val _sortedWorkouts = combine(
        _groupedWorkouts,
        _mainSorting,
        _showEmptyRowsChecked
    ) { workouts, sorting, showEmptyRowsChecked ->
        val supportsEmptyRows =
            sorting.selectedColumn == TrainingZoneSummaryColumn.DATE && showEmptyRowsChecked
        if (supportsEmptyRows) {
            sortingUseCase(workouts, sorting)
        } else {
            sortingUseCase(workouts.filter { !it.isEmpty }, sorting)
        }
    }.distinctUntilChanged()

    private val _rows = combine(
        _sortedWorkouts,
        _total
    ) { workouts, total ->
        getRowsUseCase(workouts, total)
    }.distinctUntilChanged()

    // By default, it is loading until the value of _summaryViewState is set
    private val _summaryIsLoading = MutableStateFlow(true)
    val summaryIsLoading = _summaryIsLoading.asStateFlow()
    private val _summaryViewState =
        MutableStateFlow(SummaryTableViewStateNew(rows = persistentListOf()))
    val summaryViewState: StateFlow<SummaryTableViewStateNew> = _summaryViewState.asStateFlow()

    private val _selectedDate: MutableStateFlow<String> = MutableStateFlow("")

    private val _detailsWorkouts = combine(
        _workouts,
        _selectedWorkoutsIds
    ) { workouts, selectedWorkoutsIds ->
        workouts.filter { it.id in selectedWorkoutsIds }
    }

    private val _groupedDetailsWorkouts = _detailsWorkouts
        .mapLatest { selectedWorkouts ->
            trainingZoneSummaryGroupingCalculatorUseCase.calculateByActivity(
                selectedWorkouts
            )
        }
    private val _totalDetails = _detailsWorkouts.mapLatest { getTotalRowUseCase(it) }
    private val _sortedDetailsWorkouts =
        combine(_groupedDetailsWorkouts, _detailsSorting) { workouts, sorting ->
            sortingUseCase(workouts, sorting)
        }
    private val _rowsDetails = combine(
        _sortedDetailsWorkouts,
        _totalDetails
    ) { workouts, total ->
        getRowsUseCase(workouts, total)
    }.onEach {
        _isDetailsLoading.value = false
    }

    private val _detailsColumns =
        combine(
            _rowsDetails.mapLatest { getColumnsUseCase(rows = it) },
            selectionsRepository.sortedSummaryColumnsFlow(),
            selectionsRepository.hiddenSummaryColumnsFlow()
        ) { columns, sortedColumns, hiddenColumns ->
            calculateShowColumns(columns, sortedColumns, hiddenColumns)
        }.onStart { emit(emptyList()) }

    private fun calculateShowColumns(
        supportedColumns: List<TrainingZoneSummaryColumn>,
        sortedColumns: List<TrainingZoneSummaryColumn>,
        hiddenColumns: List<TrainingZoneSummaryColumn>
    ): List<TrainingZoneSummaryColumn> =
        CANNOT_EDIT_COLUMNS + (sortedColumns - hiddenColumns.toSet()).intersect(supportedColumns.toSet())

    override fun onCleared() {
        super.onCleared()
        trackLeavingScreenEvents()
    }

    private fun trackLeavingScreenEvents() {
        val currentFilterUiState = _filterUiState.value
        trainingZoneSummaryAnalytics.trackTrainingZoneSummaryLastFiltering(
            sports = currentFilterUiState.sports,
            grouping = currentFilterUiState.grouping,
            fromInMillis = currentFilterUiState.selectedStartDateMillis,
            toInMillis = currentFilterUiState.selectedEndDateMillis,
            distanceUiState = currentFilterUiState.distance,
            isDistanceSupported = currentFilterUiState.isDistanceSupported,
            showEmptyRowsEnabled = currentFilterUiState.showEmptyRowsChecked,
            layoutType = _layoutType.value
        )
    }

    fun updateTags(
        summarySuuntoTags: ImmutableList<SummaryTag.SummarySuuntoTag>,
        summaryUserTags: ImmutableList<SummaryTag.SummaryUserTag>
    ) {
        _filterUiState.update {
            it.copy(
                summarySuuntoTags = summarySuuntoTags,
                summaryUserTags = summaryUserTags
            )
        }
        selectionsRepository.defaultSummarySuuntoTags = summarySuuntoTags
        selectionsRepository.defaultSummaryUserTags = summaryUserTags
        selectionsRepository.defaultRecentSummaryTags = (summarySuuntoTags + summaryUserTags)
    }

    fun updateShowEmptyWeeksChecked(showEmptyRowsChecked: Boolean) {
        _filterUiState.update {
            it.copy(
                showEmptyRowsChecked = showEmptyRowsChecked
            )
        }
        selectionsRepository.defaultShowEmptyRowsChecked = showEmptyRowsChecked
    }

    fun toggleSelectedLayout() {
        val newSelectedLayout = when (_layoutType.value) {
            TrainingZoneSummaryLayoutType.GRAPH -> TrainingZoneSummaryLayoutType.TABLE
            TrainingZoneSummaryLayoutType.TABLE -> TrainingZoneSummaryLayoutType.GRAPH
        }
        _layoutType.value = newSelectedLayout
        selectionsRepository.defaultLayoutType = newSelectedLayout
    }

    val summaryDetailsViewState = combine(
        _rowsDetails,
        _selectedDate,
        _detailsColumns
    ) { rows, selectedDate, selectedSummaryItems ->
        SummaryDetailsViewState(
            columns = selectedSummaryItems.sorted().toImmutableList(),
            rows = rows.toImmutableList(),
            maxValueLength = rows.toMaxValueLength(formatter),
            numberOfWorkouts = rows.count { !it.isTotal },
            selectedDate = selectedDate,
        )
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(),
        initialValue = SummaryDetailsViewState()
    )

    private val invalidateFactory = InvalidatingPagingSourceFactory {
        SummaryPagingSource(
            { _grouping.first().toGraphTimeRange() },
            { _groupedWorkouts.first() },
            { _selectedEndDateMillis.first() },
            selectedPrimaryGraphLiveData.value
                ?: DiaryPage.SUMMARY.primaryGraphDataTypes.first(),
            selectedSecondaryGraphLiveData.value
                ?: DiaryPage.SUMMARY.secondaryGraphDataTypes.first(),
            clock,
            calendarProvider,
            measurementUnit
        )
    }

    val graphs: Flow<PagingData<ChartPage>> = Pager(
        pagingSourceFactory = invalidateFactory,
        config = PagingConfig(
            pageSize = 1,
            prefetchDistance = 1,
            enablePlaceholders = false,
            initialLoadSize = 2
        )
    ).flow
        .flowOn(Dispatchers.Default)
        .cachedIn(viewModelScope)

    private val _possiblePrimaryGraphDataTypes = MutableStateFlow<List<GraphDataType>>(emptyList())
    val possiblePrimaryGraphDataTypes: Flow<List<GraphDataType>> =
        _possiblePrimaryGraphDataTypes.asStateFlow()

    private val _possibleSecondaryGraphDataTypes =
        MutableStateFlow<List<GraphDataType>>(emptyList())
    val possibleSecondaryGraphDataTypes: Flow<List<GraphDataType>> =
        _possibleSecondaryGraphDataTypes.asStateFlow()

    val defaultGraphTimeRange =
        TrainingZoneSummarySelectionsRepository.DEFAULT_TRAINING_GRAPH_TIME_RANGE
    private val _graphTimeRange = MutableStateFlow(selectionsRepository.trainingGraphTimeRange)
    val graphTimeRange: StateFlow<GraphTimeRange> = _graphTimeRange.asStateFlow()
    private val _primaryTrainingGraphType =
        MutableStateFlow(selectionsRepository.primaryTrainingGraphType)
    private val _secondaryTrainingGraphType =
        MutableStateFlow(selectionsRepository.secondaryTrainingGraphType)
    private val _currentGraphPage = MutableStateFlow<TrainingGraphPage?>(null)
    val currentGraphPage: StateFlow<TrainingGraphPage?> = _currentGraphPage.asStateFlow()
    private var currentGraphPageIndex = UNKNOWN_GRAPH_PAGE_INDEX
    private val _isGraphLoading = MutableStateFlow(true)
    val isGraphLoading = _isGraphLoading.asStateFlow()

    fun onGraphTimeRangeToggled(timeRange: GraphTimeRange) {
        _graphTimeRange.update {
            selectionsRepository.trainingGraphTimeRange = timeRange
            timeRange
        }
    }

    @OptIn(ExperimentalCoroutinesApi::class)
    private val _timeFrameWorkouts = combine(
        _graphTimeRange,
        _sports.flatMapLatest { getWorkoutsUseCase(it) }.distinctUntilChanged(),
        ::Pair
    ).onEach {
        _isGraphLoading.value = true
    }.mapLatest { (timeRange, workouts) ->
        timeFrameSummaryWorkoutsCalculator.calculate(
            timeRange = timeRange,
            workouts = workouts
        )
    }.onEach {
        currentGraphPageIndex = UNKNOWN_GRAPH_PAGE_INDEX
    }

    private val _trainingGraphTypes = combine(
        _primaryTrainingGraphType,
        _secondaryTrainingGraphType,
        ::Pair
    ).map { (primaryType, secondaryType) ->
        val primaryGraphTypes = TrainingGraphType.entries
            .filter { it != secondaryType }
        val secondaryGraphTypes = TrainingGraphType.entries
            .filter { it != primaryType }
        (primaryType to primaryGraphTypes) to (secondaryType to secondaryGraphTypes)
    }

    @OptIn(ExperimentalCoroutinesApi::class)
    val graphUiState = combine(
        _timeFrameWorkouts,
        _trainingGraphTypes,
        ::Pair
    ).mapLatest { (timeFrameWorkouts, graphTypes) ->
        val (primaryGraphType, primaryGraphTypes) = graphTypes.first
        val (secondaryGraphType, secondaryGraphTypes) = graphTypes.second

        val graphPages = timeFrameWorkouts.map {
            val chartData = createTrainingGraphChartDataUseCase(
                dateRange = it.timeFrame,
                workouts = it.workouts,
                chartGranularity = it.timeRange.toChartGranularity(),
                primaryGraphType = primaryGraphType,
                secondaryGraphType = secondaryGraphType,
            )
            TrainingGraphPage(
                countWorkouts = it.workouts.size,
                trainingDateRange = it.trainingDateRange,
                chartData = chartData,
            )
        }
        _currentGraphPage.value = graphPages.lastOrNull()
        if (currentGraphPageIndex !in graphPages.indices) {
            currentGraphPageIndex = graphPages.lastIndex
        }

        _isGraphLoading.value = false
        TrainingGraphUiState(
            graphPages = graphPages,
            graphPageIndex = currentGraphPageIndex,
            primaryGraphType = primaryGraphType,
            primaryGraphUnitResId = primaryGraphType.unitResId(measurementUnit),
            primaryGraphTypes = primaryGraphTypes,
            secondaryGraphType = secondaryGraphType,
            secondaryGraphUnitResId = secondaryGraphType.unitResId(measurementUnit),
            secondaryGraphTypes = secondaryGraphTypes,
            entryYFormatter = trainingGraphEntryYFormatter,
        )
    }
        .flowOn(coroutinesDispatchers.io)
        .catch {
            Timber.w(it, "Error loading training graph.")
        }
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5_000),
            initialValue = TrainingGraphUiState(
                graphPages = listOf(
                    TrainingGraphPage(countWorkouts = 0, trainingDateRange = null, chartData = null)
                ), // At least one page.
                graphPageIndex = 0,
                primaryGraphType = _primaryTrainingGraphType.value,
                primaryGraphUnitResId = null,
                secondaryGraphType = _secondaryTrainingGraphType.value,
                secondaryGraphUnitResId = null,
                primaryGraphTypes = emptyList(),
                secondaryGraphTypes = emptyList(),
                entryYFormatter = trainingGraphEntryYFormatter,
            )
        )

    @OptIn(ExperimentalCoroutinesApi::class)
    val selectedActivityGroupings = combine(
        _sports,
        _allDoneActivityTypes,
        ::Pair
    ).mapLatest { (activityTypes, allDoneActivityTypes) ->
        val groups = CoreActivityGroup.entries.filter {
            val typesInGroup = activityTypes.intersect(it.activityTypes)
            typesInGroup.size >= MINIMUM_COUNT_TO_SHOW_GROUP &&
                typesInGroup == allDoneActivityTypes.intersect(it.activityTypes)
        }
        groups + (activityTypes - groups.flatMap { it.activityTypes })
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5_000),
        initialValue = emptyList<CoreActivityGrouping>()
    )

    fun onGraphPageChanged(index: Int, graphPage: TrainingGraphPage) {
        _currentGraphPage.value = graphPage
        currentGraphPageIndex = index
    }

    fun updateTrainingPrimaryGraphType(graphType: TrainingGraphType) {
        _primaryTrainingGraphType.update {
            selectionsRepository.primaryTrainingGraphType = graphType
            graphType
        }
    }

    fun updateTrainingSecondaryGraphType(graphType: TrainingGraphType) {
        _secondaryTrainingGraphType.update {
            selectionsRepository.secondaryTrainingGraphType = graphType
            graphType
        }
    }

    private fun GraphTimeRange.toChartGranularity(): ChartGranularity = when (this) {
        GraphTimeRange.CURRENT_WEEK -> ChartGranularity.WEEKLY
        GraphTimeRange.CURRENT_MONTH -> ChartGranularity.MONTHLY
        GraphTimeRange.SEVEN_DAYS -> ChartGranularity.SEVEN_DAYS
        GraphTimeRange.THIRTY_DAYS -> ChartGranularity.THIRTY_DAYS
        GraphTimeRange.SIX_WEEKS -> ChartGranularity.SIX_WEEKS
        GraphTimeRange.SIX_MONTHS -> ChartGranularity.SIX_MONTHS
        GraphTimeRange.CURRENT_YEAR -> ChartGranularity.YEARLY
        GraphTimeRange.EIGHT_YEARS -> ChartGranularity.EIGHT_YEARS

        GraphTimeRange.EIGHT_WEEKS,
        GraphTimeRange.EIGHT_MONTHS,
        GraphTimeRange.THIRTEEN_MONTHS,
        GraphTimeRange.ONE_YEAR -> throw IllegalArgumentException("Unsupported time range: $this")
    }

    private val graphHighlightedEntryX = MutableStateFlow<Long?>(null)

    fun onGraphHighlightedEntryXEvent(entryX: Long?) {
        graphHighlightedEntryX.value = entryX
    }

    @OptIn(ExperimentalCoroutinesApi::class)
    val graphHighlightedData: StateFlow<TrainingGraphHighlightedData?> = combine(
        graphHighlightedEntryX,
        _currentGraphPage.map { it?.chartData },
        ::Pair
    ).mapLatest { (entryX, chartData) ->
        if (entryX == null || chartData == null) return@mapLatest null

        val primaryEntry = chartData.primarySeries.entries.flatten().find { it.x == entryX }
        val secondaryEntry = chartData.secondarySeries.entries.flatten().find { it.x == entryX }
        if (primaryEntry == null && secondaryEntry == null) return@mapLatest null

        val primaryHighlightedData = primaryEntry?.let { entry ->
            val (value, unitRes) = trainingGraphEntryYFormatter.format(
                entry.y,
                chartData.primaryGraphType
            )
            if (value != null) {
                HighlightedData(
                    chartData.primaryGraphType.titleResId,
                    value,
                    unitRes,
                    ChartR.string.chart_value_total
                )
            } else null
        }

        val secondaryHighlightedData = secondaryEntry?.let { entry ->
            val (value, unitRes) = trainingGraphEntryYFormatter.format(
                entry.y,
                chartData.secondaryGraphType
            )
            if (value != null) {
                HighlightedData(
                    chartData.secondaryGraphType.titleResId,
                    value,
                    unitRes,
                    ChartR.string.chart_value_total
                )
            } else null
        }

        TrainingGraphHighlightedData(
            primary = primaryHighlightedData,
            secondary = secondaryHighlightedData,
            formattedDate = formatChartHighlightDateTimeUseCase.formatDateTime(
                chartData.chartGranularity,
                entryX
            )
        )
    }.flowOn(coroutinesDispatchers.io).stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5_000),
        initialValue = null
    )

    init {
        combine(
            _groupedWorkouts,
            _workouts,
            selectedPrimaryGraphLiveData.asFlow(),
            selectedSecondaryGraphLiveData.asFlow(),
        ) { _, _, _, _ ->
            invalidateFactory.invalidate()
        }.launchIn(viewModelScope)

        viewModelScope.launch {
            _mainSorting
                .collectLatest {
                    selectionsRepository.defaultSortingColumn = it.selectedColumn
                    selectionsRepository.defaultSortingOrder = it.selectedOrder
                }
        }

        viewModelScope.launch {
            combine(
                _rows.mapLatest {
                    TempSummaryTableViewState(rows = it, columns = getColumnsUseCase(it))
                },
                selectionsRepository.sortedSummaryColumnsFlow(),
                selectionsRepository.hiddenSummaryColumnsFlow()
            ) { tempViewState, sortedColumns, hiddenColumns ->
                tempViewState.copy(
                    columns = calculateShowColumns(
                        tempViewState.columns,
                        sortedColumns,
                        hiddenColumns
                    )
                )
            }
                .catch {
                    Timber.w(it, "Error loading summary.")
                    _summaryIsLoading.value = false
                }
                .onEach { _summaryIsLoading.value = false }
                .collectLatest { tempViewState ->
                    val rows = tempViewState.rows
                    val columns = tempViewState.columns
                    _summaryViewState.value = if (rows.isEmpty()) {
                        SummaryTableViewStateNew(
                            columns = columns.toImmutableList(),
                            rows = persistentListOf(),
                            showNoDataForSelectedFilter = true,
                            maxValueLength = MaxValueLength(),
                        )
                    } else {
                        SummaryTableViewStateNew(
                            columns = columns.toImmutableList(),
                            rows = rows.toImmutableList(),
                            showNoDataForSelectedFilter = false,
                            maxValueLength = rows.toMaxValueLength(formatter),
                        )
                    }
                }
        }

        viewModelScope.launch {
            combine(
                _summaryViewState,
                selectedPrimaryGraphLiveData.asFlow(),
                selectedSecondaryGraphLiveData.asFlow()
            ) { summaryViewState, primaryGraphCandidate, secondaryGraphCandidate ->
                val columns = summaryViewState.columns.mapNotNull { it.toGraphDataType() }
                if (columns.isEmpty()) {
                    return@combine
                }

                val primaryGraphData = if (primaryGraphCandidate !in columns) {
                    // java.util.NoSuchElementException: Collection contains no element matching the predicate.
                    // This exception is possible if nullability is not supported
                    (columns.firstOrNull { it != secondaryGraphCandidate } ?: primaryGraphCandidate)
                        .also(selectedPrimaryGraphLiveData::updateValue)
                } else {
                    primaryGraphCandidate
                }

                val secondaryColumns = columns + GraphDataType.NONE
                val secondaryGraphData = if (secondaryGraphCandidate !in secondaryColumns) {
                    (secondaryColumns.firstOrNull { it != primaryGraphData } ?: GraphDataType.NONE)
                        .also(selectedSecondaryGraphLiveData::updateValue)
                } else {
                    secondaryGraphCandidate
                }

                _possiblePrimaryGraphDataTypes.value = columns.filter { it != secondaryGraphData }
                _possibleSecondaryGraphDataTypes.value =
                    secondaryColumns.filter { it != primaryGraphData }
            }.collect()
        }
    }

    companion object {
        private val UNKNOWN_GRAPH_PAGE_INDEX = -1
    }
}

private data class TempSummaryTableViewState(
    val columns: List<TrainingZoneSummaryColumn>,
    val rows: List<TableRowItemUiState>,
)

internal fun List<SummaryItem>.toTrainingZoneSummaryValueItems(): List<TrainingZoneSummaryColumn> {
    return CANNOT_EDIT_COLUMNS + mapNotNull {
        when (it) {
            SummaryItem.DIVETIME,
            SummaryItem.DURATION -> TrainingZoneSummaryColumn.DURATION

            SummaryItem.DISTANCE,
            SummaryItem.DIVEDISTANCE,
            SummaryItem.NAUTICALDISTANCE,
            SummaryItem.SWIMDISTANCE -> TrainingZoneSummaryColumn.DISTANCE

            SummaryItem.AVGSPEED -> TrainingZoneSummaryColumn.AVG_SPEED
            SummaryItem.AVGPACE -> TrainingZoneSummaryColumn.AVG_PACE

            SummaryItem.AVGHEARTRATE -> TrainingZoneSummaryColumn.AVG_HEART_RATE
            SummaryItem.ASCENTALTITUDE -> TrainingZoneSummaryColumn.ASCENT_ALTITUDE
            SummaryItem.TRAININGSTRESSSCORE -> TrainingZoneSummaryColumn.TRAINING_STRESS_SCORE
            SummaryItem.ENERGY -> TrainingZoneSummaryColumn.ENERGY
            SummaryItem.ESTVO2PEAK -> TrainingZoneSummaryColumn.EST_VO2PEAK
            SummaryItem.AVGPOWER -> TrainingZoneSummaryColumn.AVG_POWER
            SummaryItem.NORMALIZEDPOWER -> TrainingZoneSummaryColumn.NORMALIZED_POWER
            SummaryItem.AVGSWIMPACE -> TrainingZoneSummaryColumn.AVG_SWIM_PACE
            else -> null
        }
    }
}

private fun List<TableRowItemUiState>.toMaxValueLength(
    formatter: TrainingZoneSummaryFormatter
): MaxValueLength {
    return MaxValueLength(
        maxDate = map { it.date }.maxByOrNull { it.length } ?: "-",
        maxNumberOfWorkouts = map { it.numberOfWorkouts.toString() }.maxByOrNull { it.length }
            ?: "-",
        maxTotalDuration = map { formatter.toMeasurableString(it.totalDuration) }
            .maxByOrNull { it.length } ?: "-",
        maxTotalDistance = map { formatter.toMeasurableString(it.totalDistance) }
            .maxByOrNull { it.length } ?: "-",
        maxAvgSpeed = map { formatter.toMeasurableString(it.avgSpeed) }.maxByOrNull { it.length }
            ?: "-",
        maxAvgPace = map { formatter.toMeasurableString(it.avgPace) }.maxByOrNull { it.length }
            ?: "-",
        maxHeartRateAverage = map { formatter.toMeasurableString(it.heartRateAverage) }
            .maxByOrNull { it.length } ?: "-",
        maxTotalAscent = map { formatter.toMeasurableString(it.totalAscent) }.maxByOrNull { it.length }
            ?: "-",
        maxTss = map { formatter.toMeasurableString(it.tss) }.maxByOrNull { it.length } ?: "-",
        maxEnergyConsumption = map { formatter.toMeasurableString(it.energyConsumption) }
            .maxByOrNull { it.length } ?: "-",
        maxEstVo2Peak = map { formatter.toMeasurableString(it.estVo2Peak) }.maxByOrNull { it.length }
            ?: "-",
        maxAvgPower = map { formatter.toMeasurableString(it.avgPower) }.maxByOrNull { it.length }
            ?: "-",
        maxNormalizedPower = map { formatter.toMeasurableString(it.normalizedPower) }.maxByOrNull { it.length }
            ?: "-",
        maxAvgSwimPace = map { formatter.toMeasurableString(it.avgSwimPace) }.maxByOrNull { it.length }
            ?: "-",
        maxRowCount = maxOfOrNull { it.rowCount } ?: 1
    )
}

internal fun TrainingZoneSummaryGrouping.toGraphTimeRange(): GraphTimeRange {
    return when (this) {
        TrainingZoneSummaryGrouping.BY_ACTIVITY -> GraphTimeRange.EIGHT_WEEKS
        TrainingZoneSummaryGrouping.WEEKLY -> GraphTimeRange.EIGHT_MONTHS
        TrainingZoneSummaryGrouping.MONTHLY -> GraphTimeRange.THIRTEEN_MONTHS
        TrainingZoneSummaryGrouping.YEARLY -> GraphTimeRange.EIGHT_YEARS
    }
}

private fun TrainingZoneSummaryColumn.toGraphDataType(): GraphDataType? {
    return when (this) {
        TrainingZoneSummaryColumn.DURATION -> GraphDataType.DURATION
        TrainingZoneSummaryColumn.DISTANCE -> GraphDataType.DISTANCE
        TrainingZoneSummaryColumn.AVG_SPEED -> GraphDataType.AVG_SPEED
        TrainingZoneSummaryColumn.AVG_PACE -> GraphDataType.AVG_PACE
        TrainingZoneSummaryColumn.AVG_HEART_RATE -> GraphDataType.AVERAGE_HEART_RATE
        TrainingZoneSummaryColumn.ASCENT_ALTITUDE -> GraphDataType.ASCENT
        TrainingZoneSummaryColumn.TRAINING_STRESS_SCORE -> GraphDataType.TSS
        TrainingZoneSummaryColumn.ENERGY -> GraphDataType.CALORIES
        TrainingZoneSummaryColumn.EST_VO2PEAK -> GraphDataType.FITNESS_LEVEL
        TrainingZoneSummaryColumn.AVG_POWER -> GraphDataType.AVG_POWER
        TrainingZoneSummaryColumn.NORMALIZED_POWER -> GraphDataType.NORMALIZED_POWER
        TrainingZoneSummaryColumn.AVG_SWIM_PACE -> GraphDataType.AVG_SWIM_PACE
        TrainingZoneSummaryColumn.DATE -> null
        TrainingZoneSummaryColumn.NUMBER_OF_WORKOUTS -> null
    }
}
