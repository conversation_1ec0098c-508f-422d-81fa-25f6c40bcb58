package com.stt.android.diary.summary.composables

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.CircularProgressIndicator
import androidx.compose.material.FloatingActionButton
import androidx.compose.material.Icon
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material.icons.filled.KeyboardArrowRight
import androidx.compose.material.icons.filled.KeyboardArrowUp
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.rememberUpdatedState
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalWindowInfo
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.rememberTextMeasurer
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.stt.android.compose.component.SuuntoActivityIcon
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.bodyBold
import com.stt.android.compose.theme.bodySmall
import com.stt.android.compose.theme.darkGrey
import com.stt.android.compose.theme.disabledColor
import com.stt.android.compose.theme.dividerColor
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.mediumGrey
import com.stt.android.compose.theme.nearBlack
import com.stt.android.compose.theme.nearWhite
import com.stt.android.compose.theme.spacing
import com.stt.android.diary.summary.BasicWorkoutValue
import com.stt.android.diary.summary.MaxValueLength
import com.stt.android.diary.summary.TableRowItemUiState
import com.stt.android.diary.summary.TrainingZoneSummaryColumn
import com.stt.android.diary.summary.TrainingZoneSummaryGrouping
import com.stt.android.diary.summary.TrainingZoneSummarySortingOrder
import com.stt.android.diary.summary.TrainingZoneSummarySortingUiState
import com.stt.android.diary.summary.icon
import com.stt.android.domain.workouts.SummaryWorkoutHeader
import com.stt.android.home.diary.R
import eu.wewox.lazytable.LazyTable
import eu.wewox.lazytable.LazyTableDimensions
import eu.wewox.lazytable.LazyTableItem
import eu.wewox.lazytable.lazyTableDimensions
import eu.wewox.lazytable.lazyTablePinConfiguration
import eu.wewox.lazytable.rememberLazyTableState
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.toImmutableList
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import java.util.Locale
import kotlin.math.roundToInt

private val EDIT_COLUMN_WIDTH: Dp = 56.dp
private val ROW_HEIGHT: Dp = 56.dp

@Composable
internal fun StickySummaryTable(
    columns: ImmutableList<TrainingZoneSummaryColumn>,
    rows: ImmutableList<TableRowItemUiState>,
    hasTotalsRowItem: Boolean,
    isLoading: Boolean,
    onDateClicked: (selectedSports: ImmutableList<SummaryWorkoutHeader>, date: String) -> Unit,
    onHeaderItemClicked: (TrainingZoneSummaryColumn) -> Unit,
    sortingUiState: TrainingZoneSummarySortingUiState,
    maxValueLength: MaxValueLength,
    selectedGrouping: TrainingZoneSummaryGrouping,
    modifier: Modifier = Modifier,
    isTableScrolledUp: ((Boolean) -> Unit)? = null,
    showColumnsEditButton: Boolean = false,
    onColumnsEditClick: (() -> Unit)? = null,
) {
    val lazyTableState = rememberLazyTableState()
    val currentIsTableScrolledUp by rememberUpdatedState(newValue = isTableScrolledUp)
    var isScrolledUp by remember(lazyTableState) {
        mutableStateOf(false)
    }
    val windowInfo = LocalWindowInfo.current
    // Enable listening to scroll changes only when we have more enough items (12)
    // When we have few items, no need to hide the filter section (Hidden when the table scrolled up)
    val supportTableScrollUp = rows.size > if (windowInfo.containerSize.run { height > width }) 12 else 3
    if (supportTableScrollUp) {
        LaunchedEffect(lazyTableState) {
            snapshotFlow { lazyTableState.minaBoxState.translate }
                .filterNotNull()
                .collect { translate ->
                    isScrolledUp = translate.y > 100
                    currentIsTableScrolledUp?.invoke(isScrolledUp)
                }
        }
    }

    // The last column may show the edit button.
    val columnsCount = columns.size + if (showColumnsEditButton) 1 else 0
    val coroutineScope = rememberCoroutineScope()

    var isStatisticsButtonClicked by remember {
        mutableStateOf(false)
    }
    var hasTableScrolled by remember {
        mutableStateOf(false)
    }

    val hasNonShownColumns by remember {
        derivedStateOf {
            val translate = lazyTableState.minaBoxState.translate ?: return@derivedStateOf false
            windowInfo.containerSize.width < translate.maxX
        }
    }

    val isNextStatisticsButtonShown by remember(columnsCount) {
        derivedStateOf {
            hasNonShownColumns && !hasTableScrolled && !isStatisticsButtonClicked
        }
    }

    val showDateDivider by remember {
        derivedStateOf {
            val translate = lazyTableState.minaBoxState.translate
            translate != null && translate.x > 0f
        }
    }

    val hasRows by remember(rows) {
        mutableStateOf(rows.isNotEmpty())
    }

    val localDensity = LocalDensity.current

    LaunchedEffect(Unit) {
        snapshotFlow { lazyTableState.minaBoxState.translate }.first { it != null && it.x > 0f }
        hasTableScrolled = true
    }

    Box(modifier = modifier) {
        LazyTable(
            pinConfiguration = lazyTablePinConfiguration(
                rows = if (hasTotalsRowItem) 2 else 1, // total row is pinned
                columns = 1
            ),
            dimensions = calculateLazyTableDimensions(columns, maxValueLength, selectedGrouping),
            state = lazyTableState
        ) {
            items(
                count = columnsCount,
                layoutInfo = {
                    LazyTableItem(
                        column = it % columnsCount,
                        row = 0,
                    )
                },
            ) {
                if (it >= columns.size) {
                    SummaryColumnsEditCell(
                        onClick = {
                            onColumnsEditClick?.invoke()
                        },
                        modifier = Modifier.size(EDIT_COLUMN_WIDTH, ROW_HEIGHT)
                    )
                } else {
                    HeaderCell(
                        columnIndex = it,
                        columns = columns,
                        onHeaderItemClicked = onHeaderItemClicked,
                        sortingUiState = sortingUiState,
                        selectedGrouping = selectedGrouping,
                    )
                }
            }

            items(
                count = rows.size * columnsCount,
                layoutInfo = {
                    LazyTableItem(
                        column = it % columnsCount,
                        row = it / columnsCount + 1,
                    )
                }
            ) {
                val row = rows[it / columnsCount]
                val columnIndex = it % columnsCount
                if (columnIndex < columns.size) {
                    val (value: String?, unitRes: Int?) = when (columns[columnIndex]) {
                        TrainingZoneSummaryColumn.DATE -> {
                            if (row.rowCount > 1) {
                                when (selectedGrouping) {
                                    TrainingZoneSummaryGrouping.BY_ACTIVITY -> row.date
                                    TrainingZoneSummaryGrouping.WEEKLY -> stringResource(
                                        R.string.training_zone_summary_grouped_weeks_count,
                                        row.rowCount
                                    )

                                    TrainingZoneSummaryGrouping.MONTHLY -> stringResource(
                                        R.string.training_zone_summary_grouped_months_count,
                                        row.rowCount
                                    )

                                    TrainingZoneSummaryGrouping.YEARLY -> stringResource(
                                        R.string.training_zone_summary_grouped_years_count,
                                        row.rowCount
                                    )
                                }
                            } else {
                                row.date
                            } to null
                        }

                        TrainingZoneSummaryColumn.NUMBER_OF_WORKOUTS -> "x${row.numberOfWorkouts}" to null
                        TrainingZoneSummaryColumn.DURATION -> row.totalDuration?.value to row.totalDuration?.unitRes
                        TrainingZoneSummaryColumn.DISTANCE -> row.totalDistance?.value to row.totalDistance?.unitRes
                        TrainingZoneSummaryColumn.AVG_SPEED -> row.avgSpeed?.value to row.avgSpeed?.unitRes
                        TrainingZoneSummaryColumn.AVG_PACE -> row.avgPace?.value to row.avgPace?.unitRes
                        TrainingZoneSummaryColumn.AVG_HEART_RATE -> row.heartRateAverage?.value to row.heartRateAverage?.unitRes
                        TrainingZoneSummaryColumn.ASCENT_ALTITUDE -> row.totalAscent?.value to row.totalAscent?.unitRes
                        TrainingZoneSummaryColumn.TRAINING_STRESS_SCORE -> row.tss?.value to row.tss?.unitRes
                        TrainingZoneSummaryColumn.ENERGY -> row.energyConsumption?.value to row.energyConsumption?.unitRes
                        TrainingZoneSummaryColumn.EST_VO2PEAK -> row.estVo2Peak?.value to row.estVo2Peak?.unitRes
                        TrainingZoneSummaryColumn.AVG_POWER -> row.avgPower?.value to row.avgPower?.unitRes
                        TrainingZoneSummaryColumn.NORMALIZED_POWER -> row.normalizedPower?.value to row.normalizedPower?.unitRes
                        TrainingZoneSummaryColumn.AVG_SWIM_PACE -> row.avgSwimPace?.value to row.avgSwimPace?.unitRes
                    }
                    ContentCell(
                        row = row,
                        columnIndex = columnIndex,
                        value = value,
                        unitRes = unitRes,
                        showDateDivider = showDateDivider,
                        onDateClicked = onDateClicked
                    )
                }
            }
        }
        ScrollToSide(
            hasRows = hasRows,
            isNextStatisticsButtonShown = isNextStatisticsButtonShown,
            modifier = Modifier.align(Alignment.BottomEnd),
            onClick = {
                coroutineScope.launch {
                    lazyTableState.animateToCell(
                        column = localDensity.run {
                            windowInfo.containerSize.width.toDp().value.roundToInt().div(76) // width of one cell
                        } + 2, // 2: +1 for the first column which is sticky so not scrollable, +1 because we want to scroll to the next item
                        row = 0
                    )
                    isStatisticsButtonClicked = true
                }
            }
        )
        ScrollToTop(
            hasRows = hasRows,
            hasTableScrolledEnoughToShowBackToTopButton = isScrolledUp,
            modifier = Modifier.align(Alignment.BottomEnd),
            onClick = {
                coroutineScope.launch {
                    lazyTableState.animateToCell(
                        0,
                        if (hasTotalsRowItem) 2 else 1 // 0 is the header and 1 is the total row
                    )
                }
            }
        )
        if (isLoading) {
            Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
                CircularProgressIndicator()
            }
        }
    }
}

@Composable
private fun calculateLazyTableDimensions(
    columns: ImmutableList<TrainingZoneSummaryColumn>,
    maxValueLength: MaxValueLength,
    selectedGrouping: TrainingZoneSummaryGrouping
): LazyTableDimensions {
    val measuredMaxDate = calculateCellWidth(
        text = if (maxValueLength.maxRowCount > 1) {
            when (selectedGrouping) {
                TrainingZoneSummaryGrouping.BY_ACTIVITY -> maxValueLength.maxDate
                TrainingZoneSummaryGrouping.WEEKLY -> stringResource(
                    R.string.training_zone_summary_grouped_weeks_count,
                    maxValueLength.maxRowCount
                )

                TrainingZoneSummaryGrouping.MONTHLY -> stringResource(
                    R.string.training_zone_summary_grouped_months_count,
                    maxValueLength.maxRowCount
                )

                TrainingZoneSummaryGrouping.YEARLY -> stringResource(
                    R.string.training_zone_summary_grouped_years_count,
                    maxValueLength.maxRowCount
                )
            }.takeIf { it.length >= maxValueLength.maxDate.length } ?: maxValueLength.maxDate
        } else {
            maxValueLength.maxDate
        }
    )
    val measuredMaxNumberOfWorkouts = calculateCellWidth(text = maxValueLength.maxNumberOfWorkouts, minimumWidth = 56.dp)
    val measuredMaxTotalDuration = calculateCellWidth(text = maxValueLength.maxTotalDuration)
    val measuredMaxTotalDistance = calculateCellWidth(text = maxValueLength.maxTotalDistance)
    val measuredMaxAvgSpeed = calculateCellWidth(text = maxValueLength.maxAvgSpeed)
    val measuredMaxAvgPace = calculateCellWidth(text = maxValueLength.maxAvgPace)
    val measuredMaxHeartRateAverage = calculateCellWidth(text = maxValueLength.maxHeartRateAverage)
    val measuredMaxTotalAscent = calculateCellWidth(text = maxValueLength.maxTotalAscent)
    val measuredMaxTss = calculateCellWidth(text = maxValueLength.maxTss)
    val measuredMaxEnergyConsumption =
        calculateCellWidth(text = maxValueLength.maxEnergyConsumption)
    val measuredMaxEstVo2Peak = calculateCellWidth(text = maxValueLength.maxEstVo2Peak)
    val measuredMaxAvgPower = calculateCellWidth(text = maxValueLength.maxAvgPower)
    val measuredMaxNormalizedPower = calculateCellWidth(text = maxValueLength.maxNormalizedPower)
    val measuredMaxAvgSwimPace = calculateCellWidth(text = maxValueLength.maxAvgSwimPace)
    return lazyTableDimensions(
        columnSize = { columnIndex ->
            if (columnIndex >= columns.size) {
                EDIT_COLUMN_WIDTH
            } else {
                when (columns[columnIndex]) {
                    TrainingZoneSummaryColumn.DATE -> measuredMaxDate
                    TrainingZoneSummaryColumn.NUMBER_OF_WORKOUTS -> measuredMaxNumberOfWorkouts
                    TrainingZoneSummaryColumn.DURATION -> measuredMaxTotalDuration
                    TrainingZoneSummaryColumn.DISTANCE -> measuredMaxTotalDistance
                    TrainingZoneSummaryColumn.AVG_SPEED -> measuredMaxAvgSpeed
                    TrainingZoneSummaryColumn.AVG_PACE -> measuredMaxAvgPace
                    TrainingZoneSummaryColumn.AVG_HEART_RATE -> measuredMaxHeartRateAverage
                    TrainingZoneSummaryColumn.ASCENT_ALTITUDE -> measuredMaxTotalAscent
                    TrainingZoneSummaryColumn.TRAINING_STRESS_SCORE -> measuredMaxTss
                    TrainingZoneSummaryColumn.ENERGY -> measuredMaxEnergyConsumption
                    TrainingZoneSummaryColumn.EST_VO2PEAK -> measuredMaxEstVo2Peak
                    TrainingZoneSummaryColumn.AVG_POWER -> measuredMaxAvgPower
                    TrainingZoneSummaryColumn.NORMALIZED_POWER -> measuredMaxNormalizedPower
                    TrainingZoneSummaryColumn.AVG_SWIM_PACE -> measuredMaxAvgSwimPace
                }
            }
        },
        rowSize = { _ ->
            ROW_HEIGHT
        }
    )
}

@Composable
private fun calculateCellWidth(
    text: String,
    minimumWidth: Dp = 76.dp
): Dp {
    val textMeasurer = rememberTextMeasurer()
    val horizontalPadding = 24.dp
    val label = textMeasurer.measure(
        text = text,
        maxLines = 1,
        style = MaterialTheme.typography.bodyBold
    )
    return with(LocalDensity.current) { label.size.width.toDp() + horizontalPadding }.coerceAtLeast(
        minimumWidth
    )
}

@Composable
private fun ContentCell(
    row: TableRowItemUiState,
    columnIndex: Int,
    value: String?,
    unitRes: Int?,
    showDateDivider: Boolean,
    onDateClicked: (selectedSports: ImmutableList<SummaryWorkoutHeader>, date: String) -> Unit,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .fillMaxSize()
            .background(
                color = if (columnIndex % 2 == 1) {
                    MaterialTheme.colors.nearWhite
                } else {
                    Color.White
                }
            )
            .bottomBorder(
                strokeWidth = if (row.isTotal) {
                    1.5.dp
                } else {
                    1.dp
                },
                color = if (row.isTotal) {
                    MaterialTheme.colors.mediumGrey
                } else {
                    MaterialTheme.colors.dividerColor
                }
            )
            .endBorder(
                if (columnIndex == 0 && showDateDivider) {
                    1.dp
                } else {
                    0.dp
                },
                MaterialTheme.colors.dividerColor
            ),
        contentAlignment = if (columnIndex == 0) Alignment.CenterStart else Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            if (columnIndex == 0) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Center,
                    modifier = Modifier
                        .fillMaxSize()
                        .clickable(enabled = !row.isTotal && !row.isEmpty) {
                            onDateClicked(row.workouts, value.orEmpty())
                        }
                ) {
                    if (!row.isTotal) {
                        Text(
                            text = value.orEmpty(),
                            style = MaterialTheme.typography.bodyBold,
                            color = if (!row.isEmpty) MaterialTheme.colors.primary else MaterialTheme.colors.darkGrey,
                        )
                        if (row.showYear) {
                            Text(
                                text = row.year.toString(),
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colors.darkGrey
                            )
                        }
                    } else {
                        Text(
                            text = stringResource(R.string.training_zone_summary_total).uppercase(
                                Locale.getDefault()
                            ),
                            style = MaterialTheme.typography.bodyBold,
                            color = MaterialTheme.colors.nearBlack,
                        )
                    }
                }
            } else if (columnIndex == 1 && (row.showActivityType && !row.isTotal)) {
                SuuntoActivityIcon(
                    iconRes = row.activityIconRes,
                    tint = MaterialTheme.colors.onPrimary,
                    background = colorResource(row.activityColorRes),
                    iconSize = MaterialTheme.iconSizes.medium,
                )
            } else {
                Text(
                    text = buildAnnotatedString {
                        withStyle(
                            style = SpanStyle(
                                fontWeight = MaterialTheme.typography.bodyBold.fontWeight,
                                fontSize = MaterialTheme.typography.bodyBold.fontSize,
                                fontFamily = MaterialTheme.typography.bodyBold.fontFamily,
                                color = MaterialTheme.colors.nearBlack
                            )
                        ) {
                            append(value ?: if (!row.isEmpty) "-" else "")
                        }

                        unitRes?.let {
                            withStyle(
                                style = SpanStyle(
                                    fontWeight = MaterialTheme.typography.bodySmall.fontWeight,
                                    fontSize = MaterialTheme.typography.bodySmall.fontSize,
                                    fontFamily = MaterialTheme.typography.bodySmall.fontFamily,
                                    color = MaterialTheme.colors.nearBlack
                                )
                            ) {
                                append(" ")
                                append(stringResource(it))
                            }
                        }
                    },
                    textAlign = TextAlign.Center
                )
            }
        }
    }
}

@Composable
private fun HeaderCell(
    columns: ImmutableList<TrainingZoneSummaryColumn>,
    columnIndex: Int,
    onHeaderItemClicked: (TrainingZoneSummaryColumn) -> Unit,
    sortingUiState: TrainingZoneSummarySortingUiState,
    selectedGrouping: TrainingZoneSummaryGrouping,
    modifier: Modifier = Modifier
) {
    val currentColumn = columns[columnIndex]
    val isCurrentColumnSelected by remember(sortingUiState.selectedColumn) {
        derivedStateOf { sortingUiState.selectedColumn == currentColumn }
    }

    val isNotEnabled by remember(currentColumn, selectedGrouping) {
        derivedStateOf {
            currentColumn == TrainingZoneSummaryColumn.NUMBER_OF_WORKOUTS && selectedGrouping == TrainingZoneSummaryGrouping.BY_ACTIVITY
        }
    }

    Row(
        modifier = modifier
            .fillMaxSize()
            .background(color = MaterialTheme.colors.surface)
            .bottomBorder(1.dp, MaterialTheme.colors.dividerColor)
            .clickable(enabled = !isNotEnabled) { onHeaderItemClicked(currentColumn) },
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.Center
    ) {
        Icon(
            painter = painterResource(currentColumn.icon),
            contentDescription = null,
            modifier = Modifier.size(MaterialTheme.iconSizes.small),
            tint = if (isNotEnabled) {
                MaterialTheme.colors.disabledColor
            } else if (isCurrentColumnSelected) {
                MaterialTheme.colors.primary
            } else {
                Color.Unspecified
            }
        )

        if (isCurrentColumnSelected) {
            Icon(
                imageVector = when (sortingUiState.selectedOrder) {
                    TrainingZoneSummarySortingOrder.DESCENDING -> Icons.Filled.KeyboardArrowDown
                    TrainingZoneSummarySortingOrder.ASCENDING -> Icons.Filled.KeyboardArrowUp
                },
                contentDescription = null,
                modifier = Modifier,
                tint = MaterialTheme.colors.primary
            )
        }
    }
}

@Composable
private fun SummaryColumnsEditCell(
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
    ) {
        FloatingActionButton(
            onClick = onClick,
            backgroundColor = MaterialTheme.colors.primary,
            modifier = Modifier
                .size(24.dp)
                .align(Alignment.Center)
        ) {
            Icon(
                painter = painterResource(com.stt.android.R.drawable.ic_edit),
                contentDescription = null,
                modifier = Modifier.size(MaterialTheme.iconSizes.mini),
                tint = Color.White
            )
        }
    }
}

@Composable
private fun ScrollToTop(
    hasRows: Boolean,
    hasTableScrolledEnoughToShowBackToTopButton: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    AnimatedVisibility(
        visible = hasRows && hasTableScrolledEnoughToShowBackToTopButton,
        modifier = modifier,
        exit = fadeOut(),
        enter = fadeIn()
    ) {
        CustomFloatingActionButton(
            onClick = onClick,
            modifier = Modifier
                .padding(MaterialTheme.spacing.xsmaller)
                .size(MaterialTheme.iconSizes.large),
            backgroundColor = MaterialTheme.colors.surface
        ) {
            Icon(
                imageVector = Icons.Filled.KeyboardArrowUp,
                contentDescription = null,
            )
        }
    }
}

@Composable
private fun ScrollToSide(
    hasRows: Boolean,
    isNextStatisticsButtonShown: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    AnimatedVisibility(
        visible = hasRows && isNextStatisticsButtonShown,
        modifier = modifier,
        exit = fadeOut(),
        enter = fadeIn()
    ) {
        CustomFloatingActionButton(
            onClick = onClick,
            modifier = Modifier
                .padding(MaterialTheme.spacing.xsmaller)
                .size(MaterialTheme.iconSizes.large),
            backgroundColor = MaterialTheme.colors.surface
        ) {
            Icon(
                imageVector = Icons.Filled.KeyboardArrowRight,
                contentDescription = null,
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun StickySummaryTablePreview() {
    AppTheme {
        StickySummaryTable(
            columns = TrainingZoneSummaryColumn.entries.toList().toImmutableList(),
            rows = (0..5).map {
                TableRowItemUiState(
                    isTotal = it == 0,
                    showYear = true,
                    year = 2023,
                    date = "23.$it-30.$it.",
                    numberOfWorkouts = 20,
                    showActivityType = false,
                    activityIconRes = 0,
                    activityColorRes = 0,
                    totalDuration = BasicWorkoutValue(
                        value = "30'23:45",
                        unitRes = null
                    ),
                    totalDistance = BasicWorkoutValue(
                        value = "30",
                        unitRes = com.stt.android.core.R.string.km
                    ),
                    avgSpeed = BasicWorkoutValue(
                        value = null,
                        unitRes = null
                    ),
                    avgPace = BasicWorkoutValue(
                        value = null,
                        unitRes = null
                    ),
                    heartRateAverage = BasicWorkoutValue(
                        value = null,
                        unitRes = null
                    ),
                    totalAscent = BasicWorkoutValue(
                        value = null,
                        unitRes = null
                    ),
                    tss = BasicWorkoutValue(value = null, unitRes = null),
                    energyConsumption = BasicWorkoutValue(
                        value = null,
                        unitRes = null
                    ),
                    estVo2Peak = BasicWorkoutValue(
                        value = null,
                        unitRes = null
                    ),
                    workouts = persistentListOf(),
                    rowCount = 0,
                    avgPower = BasicWorkoutValue(
                        value = null,
                        unitRes = null
                    ),
                    normalizedPower = BasicWorkoutValue(
                        value = null,
                        unitRes = null
                    ),
                    avgSwimPace = BasicWorkoutValue(
                        value = null,
                        unitRes = null
                    ),
                )
            }.toImmutableList(),
            onDateClicked = { _, _ -> },
            isLoading = false,
            maxValueLength = MaxValueLength(),
            hasTotalsRowItem = true,
            sortingUiState = TrainingZoneSummarySortingUiState(
                selectedColumn = TrainingZoneSummaryColumn.DATE,
                selectedOrder = TrainingZoneSummarySortingOrder.DESCENDING
            ),
            onHeaderItemClicked = {},
            selectedGrouping = TrainingZoneSummaryGrouping.BY_ACTIVITY
        )
    }
}
