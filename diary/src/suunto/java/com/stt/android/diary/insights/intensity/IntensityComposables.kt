package com.stt.android.diary.insights.intensity

import androidx.annotation.ColorRes
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.Icon
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.material.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.Immutable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.layout.SubcomposeLayout
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Constraints
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.body
import com.stt.android.compose.theme.bodyBold
import com.stt.android.compose.theme.bodyLargeBold
import com.stt.android.compose.theme.cloudyGrey
import com.stt.android.compose.theme.lightGrey
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.widgets.Chip
import com.stt.android.diary.common.trainingHubDimens
import com.stt.android.diary.insights.coach.TrainingCoach
import com.stt.android.diary.insights.common.AverageComparisonChip
import com.stt.android.diary.insights.common.NumberOfWeeksAvg
import com.stt.android.diary.insights.common.ProgressIndicator
import com.stt.android.diary.insights.common.ProgressValue
import com.stt.android.diary.insights.common.Section
import com.stt.android.home.diary.R
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.toImmutableList
import com.stt.android.core.R as CR

@Composable
fun TrainingHubIntensity(
    intensityUiState: IntensityUiState,
    onIntensityTypeClick: (IntensityType) -> Unit,
    onShowInfoClicked: () -> Unit,
    modifier: Modifier = Modifier,
    intensityCoachPhrasesIds: List<Int>? = null,
) {
    Section(
        titleRes = R.string.training_hub_intensity_title,
        onActionClick = onShowInfoClicked,
        modifier = modifier
    ) {
        IntensityTypeSelector(
            selectedType = intensityUiState.selectedType,
            onIntensityTypeClick = onIntensityTypeClick,
        )
        intensityCoachPhrasesIds?.let {
            TrainingCoach(
                phrasesIds = it,
                modifier = Modifier.padding(
                    vertical = MaterialTheme.spacing.medium,
                )
            )
        }
        Spacer(modifier = Modifier.height(MaterialTheme.spacing.xsmaller))
        HearthRateZones(
            heartRateZones = intensityUiState.intensityZones.toImmutableList(),
            supportComparison = intensityUiState.supportComparison,
        )
    }
}

@Composable
internal fun IntensityTypeSelector(
    selectedType: IntensityType,
    onIntensityTypeClick: (IntensityType) -> Unit,
    modifier: Modifier = Modifier,
) {
    LazyRow(
        modifier = modifier
            .fillMaxWidth()
            .padding(
                top = MaterialTheme.spacing.xsmall,
                bottom = MaterialTheme.spacing.small
            ),
        horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.small)
    ) {
        items(
            items = IntensityType.entries,
            key = { it.name }
        ) { intensityType ->
            val isSelected = intensityType == selectedType
            val contentColor = if (isSelected) {
                Color.White
            } else {
                MaterialTheme.colors.onSurface
            }
            Chip(
                isSelected = isSelected,
                onChecked = {
                    onIntensityTypeClick(intensityType)
                },
                leftIcon = {
                    Icon(
                        painter = painterResource(intensityType.iconRes),
                        contentDescription = null,
                        tint = contentColor,
                        modifier = Modifier.size(MaterialTheme.trainingHubDimens.chipIconSize)
                    )
                },
                modifier = Modifier.height(MaterialTheme.trainingHubDimens.chipHeight),
                nonSelectedBackgroundColor = MaterialTheme.colors.lightGrey
            ) {
                Text(
                    text = stringResource(intensityType.titleRes),
                    style = MaterialTheme.typography.body,
                    color = contentColor,
                    modifier = Modifier.padding(horizontal = MaterialTheme.spacing.xsmall)
                )
            }
        }
    }
}

@Composable
private fun CircledNumber(
    text: String,
    backgroundColor: Color,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .size(MaterialTheme.trainingHubDimens.intensityCircledNumberSize)
            .clip(CircleShape)
            .background(color = backgroundColor),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = text,
            style = MaterialTheme.typography.bodyBold,
            color = Color.White
        )
    }
}

@Composable
private fun HearthRateZoneRow(
    zoneNumber: Int,
    zoneColor: Color,
    currentAvgFraction: Float,
    previousAvgFraction: Float?,
    currentProgress: String,
    previousProgress: String?,
    currentValue: Float,
    comparisonValue: Float?,
    progressValueWidth: Dp,
    supportComparison: Boolean,
    modifier: Modifier = Modifier
) {
    val comparisonIndicator = comparisonValue?.let { ComparisonIndicator.from(currentValue, it) }
    Row(
        modifier = modifier.height(MaterialTheme.trainingHubDimens.heartRateZoneRowHeight),
        verticalAlignment = Alignment.CenterVertically
    ) {
        CircledNumber(
            text = "$zoneNumber",
            backgroundColor = zoneColor
        )
        Spacer(modifier = Modifier.width(MaterialTheme.spacing.small))
        Column(
            modifier = Modifier.weight(1f),
        ) {
            ProgressIndicator(
                backgroundColor = zoneColor,
                widthFraction = currentAvgFraction.coerceAtLeast(0.01f)
            )
            previousAvgFraction?.let {
                Spacer(modifier = Modifier.height(MaterialTheme.spacing.xsmall))
                ProgressIndicator(
                    backgroundColor = MaterialTheme.colors.cloudyGrey,
                    widthFraction = it.coerceAtLeast(0.01f)
                )
            }
        }
        Spacer(modifier = Modifier.width(MaterialTheme.spacing.small))
        HearthRateZoneRowProgressValue(
            currentProgress = currentProgress,
            comparisonIndicator = comparisonIndicator,
            previousProgress = previousProgress,
            supportComparison = supportComparison,
            modifier = Modifier.width(progressValueWidth)
        )
    }
}

@Composable
private fun HearthRateZoneRowProgressValue(
    currentProgress: String,
    comparisonIndicator: ComparisonIndicator?,
    previousProgress: String?,
    supportComparison: Boolean,
    modifier: Modifier = Modifier
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = modifier
    ) {
        ProgressValue(
            text = currentProgress,
            unit = null,
            comparisonIndicator = comparisonIndicator
        )
        if (supportComparison) {
            previousProgress?.let {
                AverageComparisonChip(
                    text = it,
                    unit = null
                )
            }
        }
    }
}

@Composable
internal fun HearthRateZones(
    heartRateZones: ImmutableList<IntensityZoneInfoUiState>,
    supportComparison: Boolean,
    modifier: Modifier = Modifier,
) {
    MeasureHearthRateZoneRowProgressValueWidth(
        heartRateZones = heartRateZones,
        supportComparison = supportComparison,
    ) {
        Column(modifier = modifier) {
            for (heartRateZone in heartRateZones) {
                HearthRateZoneRow(
                    zoneNumber = heartRateZone.number,
                    zoneColor = colorResource(heartRateZone.color),
                    currentAvgFraction = heartRateZone.currentFraction,
                    previousAvgFraction = heartRateZone.comparisonFraction,
                    currentProgress = heartRateZone.currentValueFormatted,
                    previousProgress = heartRateZone.comparisonValueFormatted,
                    currentValue = heartRateZone.currentValue,
                    comparisonValue = heartRateZone.comparisonValue,
                    progressValueWidth = it,
                    supportComparison = supportComparison,
                )
            }
            if (supportComparison) {
                NumberOfWeeksAvg(
                    modifier = Modifier.padding(top = MaterialTheme.spacing.medium)
                )
            }
        }
    }
}

/**
 * The HearthRateZoneRow composable is a Row with a middle item that should take up all available space,
 * and a text item that may vary in length. While this layout works well on normal and larger devices, the text can be cut off on smaller devices.
 * To solve this issue, this composable measures all the texts on the right and returns the longest width. This width is then set for all the other texts, ensuring that none of the data is cut off.
 * In summary, the ImpactItem composable helps to ensure that all data is visible, regardless of the size of the device.
 */
@Composable
private fun MeasureHearthRateZoneRowProgressValueWidth(
    heartRateZones: ImmutableList<IntensityZoneInfoUiState>,
    supportComparison: Boolean,
    content: @Composable (impactItemValueMaxWidth: Dp) -> Unit
) {
    SubcomposeLayout { constraints ->
        val maxWidth = heartRateZones
            .maxOf { zone ->
                subcompose(
                    slotId = zone.hashCode(),
                    content = {
                        HearthRateZoneRowProgressValue(
                            currentProgress = zone.currentValueFormatted,
                            previousProgress = zone.comparisonValueFormatted,
                            comparisonIndicator = zone.comparisonValue?.let {
                                ComparisonIndicator.from(zone.currentValue, it)
                            },
                            supportComparison = supportComparison,
                        )
                    }
                ).first()
                    .measure(Constraints())
                    .width
                    .toDp()
            }

        val contentPlaceable = subcompose(
            slotId = "content",
            content = { content(maxWidth) }
        ).first()
            .measure(constraints)

        layout(contentPlaceable.width, contentPlaceable.height) {
            contentPlaceable.placeRelative(0, 0)
        }
    }
}

@Composable
private fun ArcChart(
    chartData: ImmutableList<GraphData>,
    size: Dp,
    modifier: Modifier = Modifier,
    alpha: Float = 1.0f
) {
    val context = LocalContext.current
    val sumOfDataSet = remember(chartData) {
        chartData.sumOf { it.value }
    }

    Canvas(
        modifier = modifier
            .size(size)
            .padding(10.dp)
    ) {
        var currentSegment = -90f // 0 represents 3 o'clock, -90 represents 12 o'clock
        if (sumOfDataSet == 0) {
            drawArc(
                color = Color.Gray,
                startAngle = currentSegment,
                sweepAngle = 10f,
                alpha = alpha
            )
        } else {
            for (item in chartData) {
                val segmentAngle = 360 *
                    item.value.toFloat() / sumOfDataSet
                drawArc(
                    color = Color(context.getColor(item.color)),
                    startAngle = currentSegment,
                    sweepAngle = segmentAngle,
                    alpha = alpha
                )
                currentSegment += segmentAngle
            }
        }
    }
}

private fun DrawScope.drawArc(
    color: Color,
    startAngle: Float,
    sweepAngle: Float,
    strokeWidth: Dp = 15.dp,
    alpha: Float = 1.0f
) {
    drawArc(
        color = color.copy(alpha = alpha),
        startAngle = startAngle,
        sweepAngle = sweepAngle,
        useCenter = false,
        style = Stroke(width = strokeWidth.toPx())
    )
}

@Preview(showBackground = true)
@Composable
private fun IntensityPreview() {
    AppTheme {
        TrainingHubIntensity(
            intensityUiState = IntensityUiState(
                selectedType = IntensityType.HEART_RATE,
                heartRateZones = makeHeartRateZones(),
                paceZones = makeHeartRateZones(),
                runningPowerZones = makeHeartRateZones(),
                cyclingPowerZones = makeHeartRateZones(),
                supportComparison = true,
            ),
            onIntensityTypeClick = {},
            onShowInfoClicked = {}
        )
    }
}

@Preview(showBackground = true, widthDp = 320, heightDp = 720)
@Composable
private fun IntensitySmallPreview() {
    AppTheme {
        TrainingHubIntensity(
            intensityUiState = IntensityUiState(
                selectedType = IntensityType.HEART_RATE,
                heartRateZones = makeHeartRateZones(),
                paceZones = makeHeartRateZones(),
                runningPowerZones = makeHeartRateZones(),
                cyclingPowerZones = makeHeartRateZones(),
                supportComparison = true,
            ),
            onIntensityTypeClick = {},
            onShowInfoClicked = {}
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun HearthRateZoneRowPreview() {
    AppTheme {
        HearthRateZoneRow(
            zoneNumber = 5,
            zoneColor = MaterialTheme.colors.error,
            currentAvgFraction = 0.00f,
            previousAvgFraction = 0.65f,
            currentProgress = "00’00",
            previousProgress = "08’15",
            currentValue = 0f,
            comparisonValue = 495f,
            progressValueWidth = 100.dp,
            supportComparison = true,
        )
    }
}

@Composable
private fun NestedArcChart(
    outerArcChartData: ImmutableList<GraphData>,
    innerArcChartData: ImmutableList<GraphData>,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
    ) {
        ArcChart(
            chartData = outerArcChartData,
            size = 100.dp,
            alpha = 0.5f
        )
        ArcChart(
            chartData = innerArcChartData,
            size = 60.dp,
            modifier = Modifier.align(Alignment.Center)
        )
    }
}

@Composable
private fun IntensityHeadline(
    outerArcChartData: ImmutableList<GraphData>,
    innerArcChartData: ImmutableList<GraphData>,
    insightMessage: String,
    onViewMoreClicked: () -> Unit
) {
    Row(
        verticalAlignment = Alignment.CenterVertically
    ) {
        NestedArcChart(
            outerArcChartData = outerArcChartData,
            innerArcChartData = innerArcChartData
        )
        Spacer(modifier = Modifier.width(MaterialTheme.spacing.smaller))
        Column(
            verticalArrangement = Arrangement.Bottom
        ) {
            AlertHeadline()
            Text(
                text = insightMessage,
                style = MaterialTheme.typography.body
            )
            TextButton(
                contentPadding = PaddingValues(0.dp),
                onClick = onViewMoreClicked
            ) {
                Text(
                    text = stringResource(R.string.training_hub_view_more),
                    style = MaterialTheme.typography.bodyBold,
                    color = MaterialTheme.colors.primary
                )
            }
        }
    }
}

@Composable
private fun AlertHeadline(
    modifier: Modifier = Modifier
) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = modifier.padding(vertical = MaterialTheme.spacing.small)
    ) {
        Icon(
            painterResource(R.drawable.ic_intensity_alert_headline),
            contentDescription = null
        )
        Spacer(modifier = Modifier.width(MaterialTheme.spacing.small))
        Text(
            text = stringResource(R.string.alert_headline),
            style = MaterialTheme.typography.bodyLargeBold
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun HearthRateZonesPreview() {
    AppTheme {
        HearthRateZones(
            heartRateZones = makeHeartRateZones(),
            supportComparison = true,
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun EmptyHearthRateZonesPreview() {
    AppTheme {
        HearthRateZones(
            heartRateZones = makeEmptyHeartRateZones(),
            supportComparison = true,
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun ArcChartPreview() {
    AppTheme {
        ArcChart(
            chartData = makeChartData(),
            size = 150.dp
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun NestedArcChartPreview() {
    AppTheme {
        NestedArcChart(
            outerArcChartData = makeChartData(),
            innerArcChartData = makeChartDataLastWeek()

        )
    }
}

@Preview(showBackground = true)
@Composable
private fun NestedArcEmptyChartPreview() {
    AppTheme {
        NestedArcChart(
            outerArcChartData = makeChartData(),
            innerArcChartData = makeEmptyChartData()
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun AlertHeadlinePreview() {
    AppTheme {
        AlertHeadline()
    }
}

@Preview(showBackground = true)
@Composable
private fun IntensityTypeSelectorPreview() {
    AppTheme {
        IntensityTypeSelector(
            selectedType = IntensityType.HEART_RATE,
            onIntensityTypeClick = {},
        )
    }
}

@Immutable
data class GraphData(
    val value: Int,
    @ColorRes val color: Int
)

fun makeEmptyChartData(): ImmutableList<GraphData> {
    return persistentListOf(
        GraphData(0, CR.color.heart_rate_5),
        GraphData(0, CR.color.heart_rate_4),
        GraphData(0, CR.color.heart_rate_3),
        GraphData(0, CR.color.heart_rate_2),
        GraphData(0, CR.color.heart_rate_1)
    )
}

fun makeChartData(): ImmutableList<GraphData> {
    return persistentListOf(
        GraphData(20, CR.color.heart_rate_5),
        GraphData(15, CR.color.heart_rate_4),
        GraphData(2, CR.color.heart_rate_3),
        GraphData(42, CR.color.heart_rate_2),
        GraphData(7, CR.color.heart_rate_1)
    )
}

fun makeChartDataLastWeek(): ImmutableList<GraphData> {
    return persistentListOf(
        GraphData(0, CR.color.heart_rate_5),
        GraphData(8, CR.color.heart_rate_4),
        GraphData(10, CR.color.heart_rate_3),
        GraphData(33, CR.color.heart_rate_2),
        GraphData(9, CR.color.heart_rate_1)
    )
}

fun makeHeartRateZones(): ImmutableList<IntensityZoneInfoUiState> =
    persistentListOf(
        IntensityZoneInfoUiState(
            number = 5,
            currentValue = 0.0f,
            comparisonValue = 0.0f,
            currentValueFormatted = "00’00",
            comparisonValueFormatted = "00’00",
            currentFraction = 0.00f,
            comparisonFraction = 0.00f
        ),
        IntensityZoneInfoUiState(
            number = 4,
            currentValue = 0.0f,
            comparisonValue = 0.0f,
            currentValueFormatted = "04’23",
            comparisonValueFormatted = "04’44",
            currentFraction = 0.25f,
            comparisonFraction = 0.22f
        ),
        IntensityZoneInfoUiState(
            number = 3,
            currentValue = 0.00f,
            comparisonValue = 0.00f,
            currentValueFormatted = "05’34",
            comparisonValueFormatted = "05’12",
            currentFraction = 0.45f,
            comparisonFraction = 0.50f
        ),
        IntensityZoneInfoUiState(
            number = 2,
            currentValue = 0.00f,
            comparisonValue = 0.00f,
            currentValueFormatted = "12’12",
            comparisonValueFormatted = "15’12",
            currentFraction = 0.60f,
            comparisonFraction = 0.70f,
        ),
        IntensityZoneInfoUiState(
            number = 1,
            currentValue = 0.00f,
            comparisonValue = 0.00f,
            currentValueFormatted = "06’32",
            comparisonValueFormatted = "08’15",
            currentFraction = 0.92f,
            comparisonFraction = 0.85f
        )
    )

fun makeEmptyHeartRateZones(): ImmutableList<IntensityZoneInfoUiState> =
    (5 downTo 1).map {
        IntensityZoneInfoUiState.empty(
            number = it,
            valueFormatted = "00’00",
        )
    }.toImmutableList()
