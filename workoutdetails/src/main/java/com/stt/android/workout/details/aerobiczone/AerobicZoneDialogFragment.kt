package com.stt.android.workout.details.aerobiczone

import android.app.Dialog
import android.content.res.Configuration
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Divider
import androidx.compose.material.MaterialTheme
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.ShapeDefaults
import androidx.compose.material3.SheetState
import androidx.compose.material3.Text
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.ViewCompositionStrategy
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.stt.android.R
import com.stt.android.aerobiczone.AerobicZonesInfoSheet
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.body
import com.stt.android.compose.theme.bodyXLargeBold
import com.stt.android.compose.theme.darkGrey
import com.stt.android.compose.theme.spacing
import com.stt.android.window.setFlagsAndColors
import com.stt.android.workout.details.aerobiczone.AerobicZoneDialogFragmentCreatorImpl.Companion.AEROBIC_ZONE_INFO_SHEET_NAME
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class AerobicZoneDialogFragment : DialogFragment() {
    private val aerobicZoneDialogFragmentViewModel: AerobicZoneDialogFragmentViewModel by viewModels()
    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = Dialog(requireContext(), R.style.FullScreenDialogStyle)
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE)
        dialog.window?.apply {
            setFlagsAndColors()
        }
        return dialog
    }

    @OptIn(ExperimentalMaterial3Api::class)
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View = ComposeView(requireContext()).apply {
        setViewCompositionStrategy(
            ViewCompositionStrategy.DisposeOnLifecycleDestroyed(viewLifecycleOwner)
        )

        val name = arguments?.getString(AEROBIC_ZONE_INFO_SHEET_NAME)
            ?: throw IllegalStateException("Aerobic zone info sheet not passed")
        val bottomSheet = AerobicZonesInfoSheet.valueOf(name)

        setContent {
            val configuration = LocalConfiguration.current

            val uiState by aerobicZoneDialogFragmentViewModel.uiState.collectAsStateWithLifecycle()
            val sheetState = rememberModalBottomSheetState()
            var showBottomSheet by rememberSaveable { mutableStateOf(false) }
            AppTheme {
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .background(MaterialTheme.colors.surface)
                ) {
                    when (val state = uiState) {
                        AerobicZoneDialogFragmentUiState.Loading -> Loading()

                        is AerobicZoneDialogFragmentUiState.Data -> when (bottomSheet) {
                            AerobicZonesInfoSheet.HEART_RATE -> AerobicHeartRateZones(
                                aerobicZoneInfoUiState = state.heartRateZoneInfoUiState,
                                onCloseClick = { dismiss() },
                                onInfoClick = { showBottomSheet = true },
                            )

                            AerobicZonesInfoSheet.PACE -> AerobicPaceZones(
                                aerobicZoneInfoUiState = state.paceZoneInfoUiState,
                                onCloseClick = {
                                    dismiss()
                                },
                                onInfoClick = { showBottomSheet = true },
                            )

                            AerobicZonesInfoSheet.POWER -> AerobicPowerZones(
                                aerobicZoneInfoUiState = state.powerZoneInfoUiState,
                                onCloseClick = {
                                    dismiss()
                                },
                                onInfoClick = { showBottomSheet = true },
                            )
                        }

                        AerobicZoneDialogFragmentUiState.NoData -> NoData()
                    }


                    if (showBottomSheet) {
                        // Duplicated code, I know this is crazy, but it works!
                        // Bug from the library: https://issuetracker.google.com/issues/292204649
                        if (configuration.orientation == Configuration.ORIENTATION_LANDSCAPE) {
                            BottomSheetWrapper(
                                sheetState = sheetState,
                                bottomSheet = bottomSheet,
                                onDismissRequest = {
                                    showBottomSheet = false
                                },
                            )
                        } else {
                            BottomSheetWrapper(
                                sheetState = sheetState,
                                bottomSheet = bottomSheet,
                                onDismissRequest = {
                                    showBottomSheet = false
                                },
                            )
                        }
                    }
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun BottomSheetWrapper(
    sheetState: SheetState,
    bottomSheet: AerobicZonesInfoSheet,
    onDismissRequest: () -> Unit,
    modifier: Modifier = Modifier
) {
    ModalBottomSheet(
        sheetState = sheetState,
        onDismissRequest = onDismissRequest,
        containerColor = MaterialTheme.colors.surface,
        shape = ShapeDefaults.Medium,
        modifier = modifier
            .fillMaxHeight()
    ) {
        val (titleRes, descriptionRes) = when (bottomSheet) {
            AerobicZonesInfoSheet.HEART_RATE -> R.string.item_title_heart_rate_threshold_measurement to R.string.item_description_heart_rate_threshold_measurement
            AerobicZonesInfoSheet.PACE -> R.string.item_title_pace_threshold_measurement to R.string.item_description_pace_threshold_measurement
            AerobicZonesInfoSheet.POWER -> R.string.item_title_power_threshold_measurement to R.string.item_description_power_threshold_measurement
        }
        AerobicBottomSheetContent(
            title = stringResource(titleRes),
            description = stringResource(descriptionRes),
        )
    }
}

@Composable
private fun AerobicBottomSheetContent(
    title: String,
    description: String,
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier) {
        Text(
            text = title,
            style = MaterialTheme.typography.bodyXLargeBold,
            modifier = Modifier.padding(horizontal = MaterialTheme.spacing.medium)
        )
        Spacer(modifier = Modifier.height(MaterialTheme.spacing.xxsmall))
        Text(
            text = stringResource(R.string.powered_by_zone_sense),
            style = MaterialTheme.typography.body,
            color = MaterialTheme.colors.darkGrey,
            modifier = Modifier.padding(horizontal = MaterialTheme.spacing.medium)
        )
        Divider(modifier = Modifier.padding(vertical = MaterialTheme.spacing.medium))
        Text(
            text = description,
            style = MaterialTheme.typography.body,
            modifier = Modifier.padding(horizontal = MaterialTheme.spacing.medium)
        )
        Divider(modifier = Modifier.padding(vertical = MaterialTheme.spacing.medium))
    }
}

@Composable
private fun Loading(
    modifier: Modifier = Modifier
) {
    Box(modifier = modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
        CircularProgressIndicator()
    }
}

@Composable
private fun NoData(
    modifier: Modifier = Modifier
) {
    Box(modifier = modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
        Text(
            text = stringResource(R.string.aerobic_zone_data_available),
            textAlign = TextAlign.Center
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun AerobicBottomSheetContentPreview() {
    AppTheme {
        AerobicBottomSheetContent(
            title = stringResource(R.string.item_title_pace_threshold_measurement),
            description = stringResource(R.string.item_description_pace_threshold_measurement)
        )
    }
}
