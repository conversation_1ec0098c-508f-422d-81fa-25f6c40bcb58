package com.stt.android.workout.details.graphanalysis.laps

import android.content.SharedPreferences
import androidx.core.content.edit
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.google.android.gms.maps.model.LatLng
import com.stt.android.di.MapPreferences
import com.stt.android.domain.advancedlaps.LapsTableRow
import com.stt.android.domain.advancedlaps.LapsTableType
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.utils.STTConstants
import com.stt.android.workout.details.graphanalysis.AnalysisLapsData
import dagger.hilt.android.scopes.ActivityRetainedScoped
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.combine
import java.util.concurrent.TimeUnit
import javax.inject.Inject

@ActivityRetainedScoped
class LapMarkerModel @Inject constructor(
    @MapPreferences private val preferences: SharedPreferences,
) {

    var isLapMarkerEnabled: Boolean
        get() = preferences.getBoolean(STTConstants.MapPreferences.KEY_SHOW_LAP_MARKER, true)
        set(value) {
            _lapMarkerEnabledFlow.tryEmit(value)
            preferences.edit { putBoolean(STTConstants.MapPreferences.KEY_SHOW_LAP_MARKER, value) }
        }

    private val _lapMarkerEnabledFlow = MutableStateFlow(isLapMarkerEnabled)

    private val _rawLapMarkerListFlow = MutableStateFlow<List<RawLapMarker>>(emptyList())

    private val _lapMarkerListLiveData = MutableLiveData<List<LapMarker>>(emptyList())
    val lapMarkerListLiveData: LiveData<List<LapMarker>>
        get() = _lapMarkerListLiveData

    fun update(
        header: WorkoutHeader,
        data: AnalysisLapsData,
        lapTimeRangeInWorkout: (WorkoutHeader, LapsTableRow) -> Pair<Float?, Float?>,
        selectLap: (LapsTableType, LapsTableRow) -> Unit,
    ) {
        val lapsTableType = data.lapsTableType
        val markers = if (lapsTableType != null) {
            val selectedRow = data.selectedLap
            data.lapsTables.firstOrNull {
                it.lapsType == lapsTableType
            }?.lapsTableRows?.mapNotNull { row ->
                val (startSeconds, endSeconds) = lapTimeRangeInWorkout(header, row)
                if (startSeconds != null && endSeconds != null) {
                    RawLapMarker(
                        row.lapNumber,
                        startSeconds.toLong(),
                        endSeconds.toLong(),
                        lapsTableType,
                        row,
                        highlight = if (selectedRow == null) true else row == selectedRow,
                        onSelected = { selectLap(lapsTableType, row) },
                    )
                } else null
            } ?: emptyList()
        } else emptyList()
        _rawLapMarkerListFlow.tryEmit(markers)
    }

    suspend fun transform(
        workoutTimeToGeoPoint: (Long) -> LatLng?,
    ) = combine(_lapMarkerEnabledFlow, _rawLapMarkerListFlow) { enabled, markers ->
        if (enabled) markers else emptyList()
    }.collectLatest { rawMarkers ->
        val markers = rawMarkers.mapNotNull { marker ->
            val millis = TimeUnit.SECONDS.toMillis(marker.endSecondsInWorkout)
            val position = workoutTimeToGeoPoint(millis)
            if (position != null) {
                LapMarker(
                    marker.number,
                    position,
                    marker.highlight,
                    marker.onSelected
                )
            } else null
        }
        _lapMarkerListLiveData.postValue(markers)
    }

    fun reset() {
        _rawLapMarkerListFlow.tryEmit(emptyList())
        _lapMarkerListLiveData.value = emptyList()
    }

    private data class RawLapMarker(
        val number: Int,
        val startSecondsInWorkout: Long,
        val endSecondsInWorkout: Long,
        val lapsTableType: LapsTableType,
        val lapsTableRow: LapsTableRow,
        val highlight: Boolean,
        val onSelected: () -> Unit,
    ) {
        /**
         * Note:
         * Use StateFlow's Strong equality-based conflation to reduce marker updates, exclude [onSelected] only
         **/
        override fun equals(other: Any?): Boolean {
            if (this === other) return true
            if (javaClass != other?.javaClass) return false

            other as RawLapMarker

            if (number != other.number) return false
            if (startSecondsInWorkout != other.startSecondsInWorkout) return false
            if (endSecondsInWorkout != other.endSecondsInWorkout) return false
            if (lapsTableType != other.lapsTableType) return false
            if (lapsTableRow != other.lapsTableRow) return false
            if (highlight != other.highlight) return false

            return true
        }

        override fun hashCode(): Int {
            var result = number
            result = 31 * result + startSecondsInWorkout.hashCode()
            result = 31 * result + endSecondsInWorkout.hashCode()
            result = 31 * result + lapsTableType.hashCode()
            result = 31 * result + lapsTableRow.hashCode()
            result = 31 * result + highlight.hashCode()
            return result
        }
    }
}
