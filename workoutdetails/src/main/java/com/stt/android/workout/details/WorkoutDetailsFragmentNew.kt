package com.stt.android.workout.details

import android.app.Activity
import android.content.Intent
import android.content.SharedPreferences
import android.os.Build
import android.os.Bundle
import android.view.LayoutInflater
import android.view.Menu
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.edit
import androidx.lifecycle.MutableLiveData
import androidx.navigation.fragment.findNavController
import androidx.navigation.ui.setupActionBarWithNavController
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager2.widget.ViewPager2
import com.google.android.material.snackbar.Snackbar
import com.google.android.material.tabs.TabLayoutMediator
import com.stt.android.aerobiczone.AerobicZoneDialogFragmentCreator
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.common.ui.IntentFactory
import com.stt.android.common.ui.SimpleDialogFragment
import com.stt.android.common.ui.observeNotNull
import com.stt.android.common.viewstate.ViewState
import com.stt.android.domain.sml.MultisportPartActivity
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.extensions.combineLatest
import com.stt.android.extensions.openAppSettings
import com.stt.android.home.explore.routes.RouteView
import com.stt.android.intentresolver.IntentKey
import com.stt.android.multimedia.gallery.MediaGalleryActivity
import com.stt.android.premium.PremiumPromotionNavigator
import com.stt.android.premium.PremiumPurchaseFlowLauncher
import com.stt.android.tags.TagsNavigator
import com.stt.android.ui.activities.WorkoutEditDetailsActivity
import com.stt.android.ui.components.WeatherConditionsView
import com.stt.android.ui.map.SelectedMapTypeLiveData
import com.stt.android.ui.utils.LiveEvent
import com.stt.android.ui.utils.setOnClickListenerThrottled
import com.stt.android.utils.PermissionUtils
import com.stt.android.utils.STTConstants
import com.stt.android.workout.details.databinding.WorkoutDetailsFragmentNewBinding
import com.stt.android.workout.details.laps.advanced.data.AdvancedLapsSelectDataFragment
import com.stt.android.workout.details.laps.advanced.table.AdvancedLapsSelectColumnRequest
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.FlowPreview
import pub.devrel.easypermissions.AfterPermissionGranted
import pub.devrel.easypermissions.EasyPermissions
import javax.inject.Inject
import com.stt.android.R as BaseR

@FlowPreview
@AndroidEntryPoint
class WorkoutDetailsFragmentNew : BaseWorkoutDetailsFragment(), EasyPermissions.PermissionCallbacks {
    @Inject
    lateinit var workoutDetailController: WorkoutDetailsController

    @Inject
    internal lateinit var appPrefs: SharedPreferences

    @Inject
    internal lateinit var intentFactory: IntentFactory

    @Inject
    internal lateinit var selectedMapTypeLiveData: SelectedMapTypeLiveData

    @Inject
    lateinit var tagsNavigator: TagsNavigator

    private var _binding: WorkoutDetailsFragmentNewBinding? = null
    private val binding get() = requireNotNull(_binding)

    private val isMenuPrepared = MutableLiveData(false)

    private var tabLayoutMediator: TabLayoutMediator? = null

    @Inject
    lateinit var premiumPurchaseFlowLauncher: PremiumPurchaseFlowLauncher

    @Inject
    lateinit var premiumPromotionNavigator: PremiumPromotionNavigator

    @Inject
    lateinit var aerobicZoneDialogFragmentCreator: AerobicZoneDialogFragmentCreator

    override val workoutDetailsController: BaseWorkoutDetailsController
        get() = workoutDetailController

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        premiumPurchaseFlowLauncher.register(this)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        setHasOptionsMenu(true)
        return WorkoutDetailsFragmentNewBinding.inflate(inflater, container, false).apply {
            _binding = this
            setupToolbar()
        }.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        viewModel.onMenuClickEvent.observeNotNull(viewLifecycleOwner, ::handleMenuClickEvent)
        viewModel.reportSuccess.observeNotNull(viewLifecycleOwner, ::handleReportSuccessEvent)
        viewModel.workoutDeleted.observeNotNull(viewLifecycleOwner, ::handleWorkoutDeleted)
        viewModel.onAddCommentClick.observeNotNull(this, ::handleAddCommentClickEvent)
        viewModel.shareLink.observeNotNull(viewLifecycleOwner, ::handleGpxShare)
        viewModel.shareLinkError.observeNotNull(viewLifecycleOwner, ::showSnackbarError)
        viewModel.targetDataIsMissing.observeNotNull(viewLifecycleOwner, ::handleTargetCheckResult)
        advancedLapsViewModel.onSelectDataRequested.observeNotNull(
            viewLifecycleOwner,
            ::onSelectDataRequested
        )
        // We need this because we use a data binding adapter to inflate menu items.
        // Without this it fails when returning from the full-screen map fragment, or other fragment.
        // TODO Get rid of inflating menu items using a data binding adapter.
        combineLatest(viewModel.viewState, isMenuPrepared).observeNotNull(
            viewLifecycleOwner,
            ::handleToolbarUpdate
        )

        viewModel.openSummariesTagClicked.observeNotNull(viewLifecycleOwner) {
            tagsNavigator.openDiaryForTag(requireContext(), it)
        }

        viewModel.openAerobicZoneInfoClicked.observeNotNull(viewLifecycleOwner) {
            val workoutHeader =
                viewModel.viewState.value?.data?.workoutHeader?.data ?: return@observeNotNull
            val dialog = aerobicZoneDialogFragmentCreator.create(
                dest = it,
                workoutId = workoutHeader.id,
                workoutKey = workoutHeader.key,
            )
            dialog.show(parentFragmentManager, it.name)
        }

        viewModel.openPremiumPromotionClicked.observeNotNull(viewLifecycleOwner) { analyticsSource ->
            premiumPurchaseFlowLauncher.launchFeaturePromotionAndAskAboutAppReset(
                context = requireContext(),
                analyticsSource = analyticsSource
            )
        }

        viewModel.onPremiumTagClicked.observeNotNull(viewLifecycleOwner) { analyticsSource ->
            premiumPromotionNavigator.openSuuntoTagsPromotionDialog(
                fragmentManager = childFragmentManager,
                analyticsSource = analyticsSource
            )
        }
    }

    override fun getRouteView(): RouteView = binding.routeView

    override fun getWeatherConditionsView(): WeatherConditionsView = binding.weatherConditionsView
    override fun getWorkoutDetailRV(): RecyclerView = binding.list
    override fun getWorkoutCoverImagePager(): ViewPager2 = binding.coverImagePager

    override fun onDestroyView() {
        super.onDestroyView()
        isMenuPrepared.value = false
        tabLayoutMediator?.detach()
        tabLayoutMediator = null
        mediaPageTracker.detach()
        binding.unbind()
        _binding = null
    }

    private fun navigateToSaveRoute() {
        viewModel.trackSaveRouteEvent()
        val domainWorkoutHeader =
            viewModel.viewState.value?.data?.workoutHeader?.data ?: return
        val intent = intentFactory.createIntent(
            requireContext(),
            IntentKey.SaveRoute(
                domainWorkoutHeader
            )
        )
        requireContext().startActivity(intent)
    }

    private fun shouldShowFollowRouteExplanation(): Boolean = appPrefs.getBoolean(
        STTConstants.DefaultPreferences.KEY_HAS_SHOWN_FOLLOW_ROUTE_EXPLANATION_DIALOG,
        true
    )

    private fun shouldShowGhostTargetExplanation(): Boolean = appPrefs.getBoolean(
        STTConstants.DefaultPreferences.KEY_HAS_SHOWN_GHOST_EXPLANATION_DIALOG,
        true
    )

    private fun showFollowWorkoutExplanationDialog() {
        val context = requireContext()
        val fragment = SimpleDialogFragment.newInstance(
            message = context.getString(BaseR.string.follow_workout_explanation_text),
            title = context.getString(BaseR.string.follow_route),
            positiveButtonText = context.getString(BaseR.string.ok)
        )

        fragment.setTargetFragment(this, FOLLOW_ROUTE_DIALOG_REQUEST_CODE)
        fragment.show(parentFragmentManager, FOLLOW_ROUTE_DIALOG_TAG)
    }

    private fun showGhostTargetExplanationDialog() {
        val context = requireContext()
        val fragment = SimpleDialogFragment.newInstance(
            message = context.getString(BaseR.string.ghost_explanation_text),
            title = context.getString(BaseR.string.ghost_target),
            positiveButtonText = context.getString(BaseR.string.ok)
        )

        fragment.setTargetFragment(this, GHOST_TARGET_DIALOG_REQUEST_CODE)
        fragment.show(parentFragmentManager, GHOST_TARGET_DIALOG_TAG)
    }

    @Deprecated("Deprecated in Java")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        when (requestCode) {
            FOLLOW_ROUTE_DIALOG_REQUEST_CODE -> {
                markFollowRouteExplanationSeen()
                viewModel.onMenuClick(R.id.followRoute)
            }
            GHOST_TARGET_DIALOG_REQUEST_CODE -> {
                markGhostTargetExplanationSeen()
                viewModel.onMenuClick(R.id.ghostTarget)
            }
            STTConstants.RequestCodes.EDIT_WORKOUT -> {
                when (resultCode) {
                    STTConstants.RequestResult.WORKOUT_DELETED -> requireActivity().finish()
                    STTConstants.RequestResult.MEDIA_EDITED -> {
                        viewModel.viewState.value?.data?.workoutHeader?.data?.let {
                            viewModel.mediaEdited(it)
                        }
                    }
                }
            }
            else -> {
                super.onActivityResult(requestCode, resultCode, data)
            }
        }
    }

    @Deprecated("Deprecated in Java")
    override fun onPrepareOptionsMenu(menu: Menu) {
        super.onPrepareOptionsMenu(menu)
        isMenuPrepared.value = true
    }

    private fun handleMenuClickEvent(menuId: Int) {
        val showFollowRouteExplanation =
            menuId == R.id.followRoute && shouldShowFollowRouteExplanation()

        val showGhostTargetExplanation =
            menuId == R.id.ghostTarget && shouldShowGhostTargetExplanation()

        when {
            showFollowRouteExplanation -> showFollowWorkoutExplanationDialog()
            showGhostTargetExplanation -> showGhostTargetExplanationDialog()
            menuId == R.id.report -> viewModel.reportWorkout()
            menuId == R.id.saveRoute -> navigateToSaveRoute()
            menuId == BaseR.id.downloadFit -> {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                    onStoragePermissionGrantedForFITDownload()
                } else {
                    onPermissionRequired(
                        this,
                        STORAGE_PERMISSION_REQUEST_CODE_FOR_FIT_FILE_DOWNLOAD,
                        PermissionUtils.STORAGE_PERMISSIONS,
                        BaseR.string.storage_permission_rationale_download,
                        ::onStoragePermissionGrantedForFITDownload
                    )
                }
            }
            menuId == BaseR.id.downloadJson -> {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                    onStoragePermissionGrantedForJsonDownload()
                } else {
                    onPermissionRequired(
                        this,
                        STORAGE_PERMISSION_REQUEST_CODE_FOR_JSON_DOWNLOAD,
                        PermissionUtils.STORAGE_PERMISSIONS,
                        BaseR.string.storage_permission_rationale_download,
                        ::onStoragePermissionGrantedForJsonDownload
                    )
                }
            }
            menuId == R.id.exportWorkout -> exportWorkoutClicked()
            menuId == R.id.exportRoute -> exportRouteClicked()
            menuId == R.id.exportKmlRoute -> exportKmlRouteClicked()
            menuId == BaseR.id.edit -> {
                val workoutHeader =
                    viewModel.viewState.value?.data?.workoutHeader?.data ?: return
                editWorkoutClicked(workoutHeader, AnalyticsPropertyValue.SourceProperty.EDIT_BUTTON)
            }
            else -> viewModel.onMenuClick(menuId)
        }
    }

    private fun editWorkoutClicked(workoutHeader: WorkoutHeader, source: String) {
        val editWorkoutIntent = WorkoutEditDetailsActivity.newStartIntentForEditing(
            context,
            workoutHeader.id,
            source
        )
        startActivityForResult(editWorkoutIntent, STTConstants.RequestCodes.EDIT_WORKOUT)
    }

    private fun exportWorkoutClicked() {
        viewModel.exportGpxWorkout()
    }

    private fun exportRouteClicked() {
        viewModel.exportGpxRoute()
    }

    private fun exportKmlRouteClicked() {
        viewModel.exportKmlRoute()
    }

    @Suppress("UNUSED_PARAMETER")
    private fun showSnackbarError(value: Unit) {
        Snackbar.make(
            binding.coordinatorLayout,
            BaseR.string.error_generic,
            Snackbar.LENGTH_LONG
        ).show()
    }

    private fun handleToolbarUpdate(pair: Pair<ViewState<WorkoutDetailsViewState?>, Boolean>) {
        val (state, isMenuPrepared) = pair
        val multisportPartActivity =
            arguments?.get(ARGUMENT_MULTISPORT_PART_ACTIVITY) as? MultisportPartActivity
        if (multisportPartActivity != state.data?.multisportPartActivity?.data) {
            return // State is not for this fragment instance, ignore
        }
        if (isMenuPrepared) {
            binding.state = state
        }
    }

    private fun handleReportSuccessEvent(state: Boolean) {
        if (state) {
            val dialog = SimpleDialogFragment.newInstance(
                getString(BaseR.string.report_content_description),
                getString(BaseR.string.report_content_title),
                getString(BaseR.string.close),
                null,
                false
            )
            dialog.show(
                parentFragmentManager,
                REPORT_CONTENT_DIALOG_TAG
            )
        } else {
            val snackbar = Snackbar.make(
                binding.coordinatorLayout,
                BaseR.string.report_content_error,
                Snackbar.LENGTH_INDEFINITE
            )
            snackbar.setAction(
                getString(BaseR.string.dismiss)
            ) { snackbar.dismiss() }
            snackbar.show()
        }
    }

    private fun handleWorkoutDeleted(workoutDeleted: Boolean) {
        if (workoutDeleted) {
            requireActivity().finish()
        }
    }

    @Suppress("UNUSED_PARAMETER")
    private fun handleAddCommentClickEvent(value: Unit) {
        // Collapse the cover image when the user taps 'Add comment' to mitigate this issue
        // https://suunto.tpondemand.com/entity/104971-newworkoutdetails-reactions-area-moves-up-to
        binding.appBar.setExpanded(false)
    }

    private fun handleTargetCheckResult(targetDataIsMissing: Boolean) {
        if (targetDataIsMissing) {
            val builder = AlertDialog.Builder(requireContext())
                .setTitle(getString(R.string.failed_to_create_competition_mode_title))
                .setMessage(getString(R.string.failed_to_create_competition_mode))
                .setPositiveButton(getString(com.stt.android.R.string.understand)) { _, _ ->
                    activity?.setResult(Activity.RESULT_CANCELED)
                    activity?.finish()
                }

            builder.create().show()
        } else {
            activity?.setResult(Activity.RESULT_OK)
            activity?.finish()
        }
    }

    private fun markFollowRouteExplanationSeen() {
        appPrefs.edit {
            putBoolean(
                STTConstants.DefaultPreferences.KEY_HAS_SHOWN_FOLLOW_ROUTE_EXPLANATION_DIALOG,
                false
            )
        }
    }

    private fun markGhostTargetExplanationSeen() {
        appPrefs.edit {
            putBoolean(
                STTConstants.DefaultPreferences.KEY_HAS_SHOWN_GHOST_EXPLANATION_DIALOG,
                false
            )
        }
    }

    private fun handleGpxShare(link: String) {
        val sendIntent: Intent = Intent().apply {
            action = Intent.ACTION_SEND
            putExtra(Intent.EXTRA_TEXT, link)
            type = "text/plain"
        }
        sendIntent.flags = Intent.FLAG_GRANT_READ_URI_PERMISSION
        val shareIntent =
            Intent.createChooser(sendIntent, resources.getString(BaseR.string.dialog_title_select))
        startActivity(shareIntent)
    }

    private fun onSelectDataRequested(event: LiveEvent<AdvancedLapsSelectColumnRequest>) {
        val request = event.getContentIfNotHandled() ?: return
        advancedLapsViewModel.getAvailableColumns(request.stId, request.lapsTableType)
            ?.let { dataTypes ->
                val fm = childFragmentManager
                val fragment =
                    AdvancedLapsSelectDataFragment.newInstance(
                        request.stId,
                        request.column,
                        dataTypes,
                        request.lapsTableType,
                        request.columnIndex
                    )
                fragment.show(fm, AdvancedLapsSelectDataFragment.FRAGMENT_TAG)
            }
    }

    private fun setupToolbar() {
        (requireActivity() as AppCompatActivity).apply {
            setSupportActionBar(null)
            setSupportActionBar(binding.toolbar)
            setupActionBarWithNavController(findNavController())
            supportActionBar?.setDisplayHomeAsUpEnabled(true)
            supportActionBar?.setDisplayShowTitleEnabled(false)
        }
    }

    override fun initCoverImageLoaderView(coverImagePager: ViewPager2) {
        super.initCoverImageLoaderView(coverImagePager)
        tabLayoutMediator = TabLayoutMediator(binding.pagerIndicator, coverImagePager) { _, _ -> }
        tabLayoutMediator?.attach()
    }

    override fun setShareButtonView(coverImageData: CoverImageData?) {
        binding.shareImageButton.visibility = View.VISIBLE
        binding.shareImageButton.setOnClickListenerThrottled {
            val index = binding.coverImagePager.currentItem
            coverImageData?.onShareClicked?.invoke(index)
        }

        val haveImage = coverImageData?.coverImages?.any {
            it is CoverImage.PhotoCoverImage || it is CoverImage.VideoCoverImage
        }

        if (haveImage != true) {
            binding.addPhotoButton.visibility = View.VISIBLE
            binding.addPhotoButton.setOnClickListenerThrottled {
                onPermissionRequired(
                    this,
                    STORAGE_PERMISSION_REQUEST_CODE_FOR_MEDIA_GALLERY,
                    PermissionUtils.STORAGE_PERMISSIONS,
                    com.stt.android.R.string.storage_permission_rationale,
                    ::onStoragePermissionForMediaGallery
                )
            }
        } else {
            binding.addPhotoButton.visibility = View.GONE
        }
    }

    override fun setShowWorkoutPlaybackButton(coverImageData: CoverImageData?) {
        if (coverImageData?.showWorkoutPlaybackButton == true) {
            binding.openGraphAnalysisButton.visibility = View.VISIBLE
            val supportsWorkoutPlayback = coverImageData.supportsWorkoutPlaybackOnMapView
            binding.openGraphAnalysisButton.setOnClickListenerThrottled {
                if (viewModel.viewState.value?.data?.isSubscribedToPremium == false && supportsWorkoutPlayback) {
                    premiumPromotionNavigator.openWorkoutPlaybackPromotionDialog(
                        childFragmentManager,
                        AnalyticsPropertyValue.BuyPremiumPopupShownSource.WORKOUT_DETAILS
                    )
                } else {
                    coverImageData.onOpenGraphAnalysisClick.invoke(coverImageData)
                }
            }

            if (supportsWorkoutPlayback) {
                binding.openGraphAnalysisButton.setImageResource(com.stt.android.R.drawable.ic_play_outline)
                binding.openGraphAnalysisButton.contentDescription =
                    requireContext().getString(com.stt.android.R.string.workout_playback_route_on_map)
            } else {
                binding.openGraphAnalysisButton.setImageResource(R.drawable.analysis_graph_fill)
                binding.openGraphAnalysisButton.contentDescription =
                    requireContext().getString(com.stt.android.R.string.workout_open_graph_analysis)
            }
        } else {
            binding.openGraphAnalysisButton.visibility = View.GONE
        }
    }

    override fun setSelectButton() {
        binding.selectBt.setOnClickListenerThrottled {
            viewModel.checkTargetDataMissing()
        }
        val selectBtnHeight = requireContext().resources.getDimensionPixelSize(com.stt.android.core.R.dimen.height_elevated_button)
        val bottomPadding = requireContext().resources.getDimensionPixelSize(com.stt.android.core.R.dimen.padding)
        binding.list.setPadding(0, 0, 0, selectBtnHeight + bottomPadding)
    }

    @AfterPermissionGranted(STORAGE_PERMISSION_REQUEST_CODE_FOR_MEDIA_GALLERY)
    private fun onStoragePermissionForMediaGallery() {
        val domainWorkoutHeader =
            viewModel.viewState.value?.data?.workoutHeader?.data ?: return

        startActivityForResult(
            MediaGalleryActivity.newIntentForDirectAddToWorkout(
                requireContext(),
                domainWorkoutHeader
            ),
            STTConstants.RequestCodes.EDIT_WORKOUT
        )
    }

    @AfterPermissionGranted(STORAGE_PERMISSION_REQUEST_CODE_FOR_FIT_FILE_DOWNLOAD)
    private fun onStoragePermissionGrantedForFITDownload() {
        (requireActivity() as AppCompatActivity).run {
            viewModel.downloadFitFile()
        }
    }

    @AfterPermissionGranted(STORAGE_PERMISSION_REQUEST_CODE_FOR_JSON_DOWNLOAD)
    private fun onStoragePermissionGrantedForJsonDownload() {
        (requireActivity() as AppCompatActivity).run {
            viewModel.downloadJsonFile()
        }
    }

    @Deprecated("Deprecated in Java")
    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        onRequestPermissionsResultHandler(requestCode, permissions, grantResults, this)
    }

    override fun onPermissionsGranted(requestCode: Int, perms: MutableList<String>) {
        // do nothing
    }

    override fun onPermissionsDenied(requestCode: Int, perms: MutableList<String>) {
        showPermissionErrorMessage()
    }

    private fun showPermissionErrorMessage() {
        val snackBar = Snackbar.make(
            binding.root,
            BaseR.string.storage_permission_rationale_picker,
            Snackbar.LENGTH_LONG
        )
        snackBar.setAction(BaseR.string.settings) {
            context?.openAppSettings()
        }
        snackBar.show()
    }

    companion object {
        private const val STORAGE_PERMISSION_REQUEST_CODE_FOR_MEDIA_GALLERY = 105
        private const val FOLLOW_ROUTE_DIALOG_REQUEST_CODE = 100
        private const val GHOST_TARGET_DIALOG_REQUEST_CODE = 101
        private const val STORAGE_PERMISSION_REQUEST_CODE_FOR_FIT_FILE_DOWNLOAD = 104
        private const val STORAGE_PERMISSION_REQUEST_CODE_FOR_JSON_DOWNLOAD = 106
        private const val FOLLOW_ROUTE_DIALOG_TAG = "FOLLOW_ROUTE_DIALOG_TAG"
        private const val GHOST_TARGET_DIALOG_TAG = "GHOST_TARGET_DIALOG_TAG"
        private const val REPORT_CONTENT_DIALOG_TAG = "REPORT_CONTENT_DIALOG_TAG"
    }
}
