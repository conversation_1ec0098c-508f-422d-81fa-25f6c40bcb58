FROM jenkins:latest

USER root

# Install java 7
RUN echo "deb http://ppa.launchpad.net/webupd8team/java/ubuntu precise main" | tee /etc/apt/sources.list.d/webupd8team-java.list
RUN echo "deb-src http://ppa.launchpad.net/webupd8team/java/ubuntu precise main" | tee -a /etc/apt/sources.list.d/webupd8team-java.list
RUN apt-key adv --keyserver hkp://keyserver.ubuntu.com:80 --recv-keys EEA14886
RUN apt-get update
RUN echo oracle-java7-installer shared/accepted-oracle-license-v1-1 select true | /usr/bin/debconf-set-selections
RUN apt-get -y install oracle-java7-installer
RUN apt-get -y install oracle-java7-set-default

# Install required 32bits libraries for aapt and other android tools
RUN apt-get -y install lib32z1 lib32stdc++6

# Replace ramon-sadornil and 1001 with the correct values that match your host user
RUN useradd -d "$JENKINS_HOME" -u 1001 -m -s /bin/bash ramon-sadornil
USER ramon-sadornil

ENV ANDROID_HOME /tmp/android_home

# Create a container with
# - If you don't need HTTPS:
#  docker run -p 80:8080 --name jenkins-00 -v /home/<USER>/docker_jenkins_volume:/var/jenkins_home jenkins
# - If you need HTTPS(443) and HTTP(8080) (more details at http://stackoverflow.com/a/29836864/1081101):
#  docker run -p 8080:8080 -p 443:8443 --name jenkins-00 -v /mnt/data/ramon-sadornil/jenkins-00_volume:/var/jenkins_home jenkins-own --httpPort=8080 --httpsPort=8443 --httpsKeyStore=/var/jenkins_home/jenkins_keystore.jks --httpsKeyStorePassword=<keystorepassword>
# Once created you start it again with
#  docker start jenkins-00
