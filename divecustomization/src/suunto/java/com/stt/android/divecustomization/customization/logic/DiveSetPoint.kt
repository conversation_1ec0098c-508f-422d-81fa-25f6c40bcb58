package com.stt.android.divecustomization.customization.logic

import com.soy.algorithms.divemodecustomization.entities.SwitchSetPoint
import com.soy.algorithms.divemodecustomization.entities.WithAvailability
import com.soy.algorithms.divemodecustomization.entities.validationrules.domain.SmlEditor
import com.stt.android.common.viewstate.ViewState
import com.stt.android.common.viewstate.flatMap
import com.stt.android.divecustomization.customization.barToPa
import com.stt.android.divecustomization.customization.entities.CustomizationModeWithAvailableOptions
import com.stt.android.divecustomization.customization.entities.DepthUnits
import com.stt.android.divecustomization.customization.entities.DiveRangeSelectionOption
import com.stt.android.divecustomization.customization.entities.DiveSelectableRangeSelectionOption
import com.stt.android.divecustomization.customization.entities.DiveSetPointContent
import com.stt.android.divecustomization.customization.getNearestValue
import com.stt.android.divecustomization.customization.getSettingOrNull
import com.stt.android.divecustomization.customization.paToBar
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import com.stt.android.core.R as CR

interface DiveSetPoint : CustomizationModeParent {
    fun getDiveSetPointFlow(): Flow<ViewState<DiveSetPointContent>> {
        return currentModeFlow.map { value ->
            value.flatMap { modeWithAvailableOptions ->
                ViewState.Loaded(
                    getDiveSetPointContent(modeWithAvailableOptions)
                )
            }
        }
    }

    fun setDiveSetPointLow(setPoint: Double) {
        getCurrentMode().let { currentMode ->
            val mode = currentMode.copy(
                diving = currentMode.diving.copy(
                    lowSetPoint = currentMode.diving.lowSetPoint.copy(
                        value = barToPa(setPoint).toInt()
                    )
                )
            )
            setCurrentMode(mode)
        }
    }

    fun setDiveSetPointHigh(setPoint: Double) {
        getCurrentMode().let { currentMode ->
            val mode = currentMode.copy(
                diving = currentMode.diving.copy(
                    highSetPoint = currentMode.diving.highSetPoint.copy(
                        value = barToPa(setPoint).toInt()
                    )
                )
            )
            setCurrentMode(mode)
        }
    }

    fun enableDiveSwitchPointLow(enable: Boolean) {
        getCurrentMode().let { currentMode ->
            val mode = currentMode.copy(
                diving = currentMode.diving.copy(
                    switchLowSetPoint = currentMode.diving.switchLowSetPoint.copy(
                        enabled = currentMode.diving.switchLowSetPoint.enabled.copy(
                            value = enable
                        )
                    )
                )
            )
            setCurrentMode(mode)
        }
    }

    fun enableDiveSwitchPointHigh(enable: Boolean) {
        getCurrentMode().let { currentMode ->
            val mode = currentMode.copy(
                diving = currentMode.diving.copy(
                    switchHighSetPoint = currentMode.diving.switchHighSetPoint.copy(
                        enabled = currentMode.diving.switchHighSetPoint.enabled.copy(
                            value = enable
                        )
                    )
                )
            )
            setCurrentMode(mode)
        }
    }

    fun setDiveSwitchPointLow(switchPoint: Double) {
        // Have to convert the depth back to meters if device units are in feet
        val depth = deviceToSiUnitsDepth(switchPoint)
        getCurrentMode().let { currentMode ->
            val mode = currentMode.copy(
                diving = currentMode.diving.copy(
                    switchLowSetPoint = currentMode.diving.switchLowSetPoint.copy(
                        depth = currentMode.diving.switchLowSetPoint.depth.copy(
                            value = depth
                        )
                    )
                )
            )
            setCurrentMode(mode)
        }
    }

    fun setDiveSwitchPointHigh(switchPoint: Double) {
        // Have to convert the depth back to meters if device units are in feet
        val depth = deviceToSiUnitsDepth(switchPoint)
        getCurrentMode().let { currentMode ->
            val mode = currentMode.copy(
                diving = currentMode.diving.copy(
                    switchHighSetPoint = currentMode.diving.switchHighSetPoint.copy(
                        depth = currentMode.diving.switchHighSetPoint.depth.copy(
                            value = depth
                        )
                    )
                )
            )
            setCurrentMode(mode)
        }
    }

    fun getDiveSetPointContent(modeWithAvailableOptions: CustomizationModeWithAvailableOptions): DiveSetPointContent? {
        return DiveSetPointContent(
            diveLowSetPoint = getLowSetPoint(modeWithAvailableOptions),
            diveHighSetPoint = getHighSetPoint(modeWithAvailableOptions),
            diveLowSwitchPoint = getLowSwitchPoint(modeWithAvailableOptions),
            diveHighSwitchPoint = getHighSwitchPoint(modeWithAvailableOptions)
        )
    }

    private fun getLowSetPoint(modeWithAvailableOptions: CustomizationModeWithAvailableOptions): DiveRangeSelectionOption<Double>? {
        return getSetPoint(
            modeWithAvailableOptions.mode.diving.lowSetPoint,
            modeWithAvailableOptions.availableOptions.modeDivingLowSetPoint.editor
        )
    }

    private fun getHighSetPoint(modeWithAvailableOptions: CustomizationModeWithAvailableOptions): DiveRangeSelectionOption<Double>? {
        return getSetPoint(
            modeWithAvailableOptions.mode.diving.highSetPoint,
            modeWithAvailableOptions.availableOptions.modeDivingHighSetPoint.editor
        )
    }

    private fun getSetPoint(
        setPoint: WithAvailability<Int>,
        conditions: SmlEditor?
    ): DiveRangeSelectionOption<Double>? {
        // Low and High set points are always shown in ata/bar irrespective of the device settings for pressure units
        val setPointSetting =
            getSettingOrNull(setPoint)

        val possibleValues = pressureValuesFromConditions(conditions)

        return if (setPointSetting != null) {
            val selectedValue =
                getNearestValue(possibleValues, paToBar(setPointSetting.toDouble()))

            DiveRangeSelectionOption(
                values = possibleValues,
                selectedValue = selectedValue,
                units = CR.string.bar
            )
        } else {
            null
        }
    }

    private fun getLowSwitchPoint(modeWithAvailableOptions: CustomizationModeWithAvailableOptions): DiveSelectableRangeSelectionOption<Double>? {
        return getSwitchPoint(
            modeWithAvailableOptions.mode.diving.switchLowSetPoint,
            modeWithAvailableOptions.availableOptions.modeDivingSwitchLowSetPointDepth.editor
        )
    }

    private fun getHighSwitchPoint(modeWithAvailableOptions: CustomizationModeWithAvailableOptions): DiveSelectableRangeSelectionOption<Double>? {
        return getSwitchPoint(
            modeWithAvailableOptions.mode.diving.switchHighSetPoint,
            modeWithAvailableOptions.availableOptions.modeDivingSwitchHighSetPointDepth.editor
        )
    }

    private fun getSwitchPoint(
        switchSetPoint: SwitchSetPoint,
        conditions: SmlEditor?
    ): DiveSelectableRangeSelectionOption<Double>? {
        val depthUnits = DepthUnits.from(getDeviceConfig().units.depth.value)
        val switchPointEnabled =
            getSettingOrNull(switchSetPoint.enabled)
        val switchPointDepth =
            getSettingOrNull(switchSetPoint.depth)

        return if (switchPointEnabled != null && switchPointDepth != null) {
            val depthValues = depthValuesFromConditions(conditions)
            val selectedValue = selectedDepthValue(switchPointDepth, depthValues)

            DiveSelectableRangeSelectionOption(
                enabled = switchPointEnabled,
                option = DiveRangeSelectionOption(
                    values = depthValues,
                    selectedValue = selectedValue,
                    units = depthUnits.getStringResource()
                )
            )
        } else {
            null
        }
    }
}
