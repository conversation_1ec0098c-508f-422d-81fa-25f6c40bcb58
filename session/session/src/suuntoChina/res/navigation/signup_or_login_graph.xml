<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/signup_or_login_graph"
    app:startDestination="@id/splashIntroFragment">

    <fragment
        android:id="@+id/splashIntroFragment"
        android:name="com.stt.android.session.splashintro.SplashIntroChinaFragment"
        android:label="fragment_splash_intro_fragment"
        tools:layout="@layout/fragment_login_intro">

        <action
            android:id="@+id/action_showFirstLaunchTerms"
            app:destination="@id/firstLaunchTermsDialogFragment" />

        <action android:id="@+id/action_continueWithPhoneChina"
            app:destination="@id/continueWithPhoneChinaFragment"
            app:enterAnim="@anim/fade_in"
            app:exitAnim="@anim/fade_out" />

        <action android:id="@+id/action_bindEmailFragment"
            app:destination="@id/bindEmailFragment"
            app:enterAnim="@anim/fade_in"
            app:exitAnim="@anim/fade_out" />

        <action android:id="@+id/action_bindEmailVerifyCodeFragment"
            app:destination="@id/bindEmailVerifyCodeFragment"
            app:enterAnim="@anim/fade_in"
            app:exitAnim="@anim/fade_out" />

    </fragment>

    <fragment
        android:id="@+id/continueWithPhoneChinaFragment"
        android:name="com.stt.android.session.FragmentContinueWithPhoneChina"
        android:label="fragment_continue_with_phone_china">

        <action android:id="@+id/action_inputPhoneVerifyCodeChina"
            app:destination="@id/inputPhoneVerifyCodeFragment"
            app:enterAnim="@anim/fade_in"
            app:exitAnim="@anim/fade_out" />

        <action android:id="@+id/action_continueWithEmailChina"
            app:destination="@id/continueWithEmailChinaFragment"
            app:popUpTo="@id/continueWithPhoneChinaFragment"
            app:popUpToInclusive="true"
            app:enterAnim="@anim/fade_in"
            app:exitAnim="@anim/fade_out" />
    </fragment>

    <fragment
        android:id="@+id/continueWithEmailChinaFragment"
        android:name="com.stt.android.session.FragmentContinueWithEmailChina"
        android:label="fragment_continue_with_email_china">

        <action android:id="@+id/action_continueWithPhoneChina"
            app:destination="@id/continueWithPhoneChinaFragment"
            app:popUpTo="@id/continueWithEmailChinaFragment"
            app:popUpToInclusive="true"
            app:enterAnim="@anim/fade_in"
            app:exitAnim="@anim/fade_out" />

        <action android:id="@+id/action_forgetPasswordFragment"
            app:destination="@id/forgetPasswordFragment"
            app:popUpTo="@id/continueWithEmailChinaFragment"
            app:popUpToInclusive="true"
            app:enterAnim="@anim/fade_in"
            app:exitAnim="@anim/fade_out" />

    </fragment>

    <fragment
        android:id="@+id/inputPhoneVerifyCodeFragment"
        android:name="com.stt.android.session.verify.FragmentInputPhoneVerifyCode"
        android:label="fragment_input_phone_verify_code">

        <argument
            android:name="phoneNumber"
            android:defaultValue=""
            app:argType="string"
            app:nullable="false" />

        <argument
            android:name="isForgetPassword"
            android:defaultValue="false"
            app:argType="boolean"
            app:nullable="false" />

        <action android:id="@+id/action_setPasswordFragment"
            app:destination="@id/setPasswordFragment"
            app:enterAnim="@anim/fade_in"
            app:exitAnim="@anim/fade_out"
            app:popUpTo="@id/inputPhoneVerifyCodeFragment"
            app:popUpToInclusive="true"/>
    </fragment>

    <fragment
        android:id="@+id/setPasswordFragment"
        android:name="com.stt.android.session.bindemail.FragmentSetPassword"
        android:label="fragment_continue_with_email_china">

        <argument
            android:name="email"
            android:defaultValue=""
            app:argType="string"
            app:nullable="false" />

        <argument
            android:name="phoneNumber"
            android:defaultValue=""
            app:argType="string"
            app:nullable="false" />

        <argument
            android:name="token"
            android:defaultValue=""
            app:argType="string"
            app:nullable="false" />

        <argument
            android:name="isReset"
            android:defaultValue="false"
            app:argType="boolean"
            app:nullable="false" />

        <action android:id="@+id/action_continueWithEmailChina"
            app:destination="@id/continueWithEmailChinaFragment"
            app:popUpTo="@id/forgetPasswordFragment"
            app:popUpToInclusive="true"
            app:enterAnim="@anim/fade_in"
            app:exitAnim="@anim/fade_out" />

    </fragment>

    <fragment
        android:id="@+id/forgetPasswordFragment"
        android:name="com.stt.android.session.forgetpassword.FragmentForgetPassword"
        android:label="fragment_forget_password">

        <argument
            android:name="type"
            android:defaultValue="0"
            app:argType="integer"
            app:nullable="false" />

        <action android:id="@+id/action_inputPhoneVerifyCodeChina"
            app:destination="@id/inputPhoneVerifyCodeFragment"
            app:enterAnim="@anim/fade_in"
            app:exitAnim="@anim/fade_out" />

        <action android:id="@+id/action_continueWithEmailChina"
            app:destination="@id/continueWithEmailChinaFragment"
            app:popUpTo="@id/forgetPasswordFragment"
            app:popUpToInclusive="true"
            app:enterAnim="@anim/fade_in"
            app:exitAnim="@anim/fade_out" />
    </fragment>



    <dialog
        android:id="@+id/firstLaunchTermsDialogFragment"
        android:name="com.stt.android.session.firstlaunch.FirstLaunchTermsDialogFragment">
    </dialog>

    <include app:graph="@navigation/phone_number_region_dialog_graph" />
</navigation>
