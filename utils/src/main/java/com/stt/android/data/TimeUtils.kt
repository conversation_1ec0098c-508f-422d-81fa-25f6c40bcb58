package com.stt.android.data

import com.stt.android.utils.toDaysPartSafe
import com.stt.android.utils.toHoursPartSafe
import com.stt.android.utils.toMinutesPartSafe
import java.time.Clock
import java.time.Duration
import java.time.Instant
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.Month
import java.time.Year
import java.time.ZoneId
import java.time.ZoneOffset
import java.time.ZonedDateTime
import java.time.chrono.Chronology
import java.time.format.DateTimeFormatter
import java.time.format.DateTimeFormatterBuilder
import java.time.format.FormatStyle
import java.time.temporal.ChronoUnit
import java.time.temporal.TemporalAdjusters
import java.util.Locale
import java.util.concurrent.TimeUnit

/**
 * Extension function to get milliseconds since Epoch from ZonedDateTime
 */
fun ZonedDateTime.toEpochMilli(): Long = toInstant().toEpochMilli()

/**
 * Extension function to get date for start of the year in the UTC offset
 */
fun Year.toStartOfYearUTC(): ZonedDateTime = atMonth(Month.JANUARY).atDay(1).atStartOfDay()
    .atOffset(ZoneOffset.UTC).toZonedDateTime()

/**
 * Extension function to get date for end of the year in the UTC offset
 */
fun Year.toEndOfYearUTC(): ZonedDateTime =
    atMonth(Month.DECEMBER).atEndOfMonth().atTime(LocalTime.MAX)
        .atOffset(ZoneOffset.UTC).toZonedDateTime()

/**
 * Extension function to get date for start of the year in the system timezone
 */
fun Year.toStartOfYear(): ZonedDateTime = atMonth(Month.JANUARY).atDay(1).atStartOfDay()
    .atZone(ZoneId.systemDefault())

/**
 * Extension function to get date for end of the year in the system timezone
 */
fun Year.toEndOfYear(): ZonedDateTime = atMonth(Month.DECEMBER).atEndOfMonth().atTime(LocalTime.MAX)
    .atZone(ZoneId.systemDefault())

/**
 * Extension function to get milliseconds for start of the year in the system timezone
 */
fun Year.toStartOfYearMilli(): Long = toStartOfYear().toEpochMilli()

/**
 * Extension function to get milliseconds for end of the year in the system timezone
 */
fun Year.toEndOfYearMilli(): Long = toEndOfYear().toEpochMilli()

/**
 * ST was released in 2004
 */
const val RELEASED_YEAR: Int = 2004

val releasedMilli = Year.of(RELEASED_YEAR).toStartOfYearMilli()

val releasedUTCMilli = Year.of(RELEASED_YEAR).toStartOfYearUTC().toEpochMilli()

object TimeUtils {

    fun zonedDateTime(
        year: Int,
        month: Int = 1,
        dayOfMonth: Int = 1,
        hour: Int = 0,
        minute: Int = 0,
        second: Int = 0,
        nanoOfSecond: Int = 0,
        offsetHours: Int = 0
    ): ZonedDateTime {
        return ZonedDateTime.of(
            year,
            month,
            dayOfMonth,
            hour,
            minute,
            second,
            nanoOfSecond,
            ZoneOffset.ofHours(offsetHours)
        )
    }

    /**
     * Converts time in milliseconds to ZonedDateTime at offset [offsetHours]
     */
    fun epochToZonedDateTimeAtOffset(epochMillis: Long, offsetHours: Int): ZonedDateTime {
        return ZonedDateTime.ofInstant(
            Instant.ofEpochMilli(epochMillis),
            ZoneOffset.ofHours(offsetHours)
        )
    }

    /**
     * Converts time in milliseconds to ZonedDateTime UTC
     */
    fun epochToZonedDateTime(epochMillis: Long): ZonedDateTime {
        return ZonedDateTime.ofInstant(Instant.ofEpochMilli(epochMillis), ZoneOffset.UTC)
    }

    /**
     * Converts time in milliseconds to default ZonedDateTime
     */
    @JvmStatic
    fun epochToLocalZonedDateTime(epochMillis: Long): ZonedDateTime {
        return ZonedDateTime.ofInstant(
            Instant.ofEpochMilli(epochMillis),
            ZoneOffset.systemDefault()
        )
    }

    /**
     * Get the last day of the month of the given epoch timestamp
     *
     * @return The ZonedDateTime of the last day of the month
     */
    fun getEndDateOfMonth(epochMillis: Long): ZonedDateTime {
        return epochToLocalZonedDateTime(epochMillis)
            .withDayOfMonth(1)
            .plusMonths(1)
            .minusDays(1)
    }

    /**
     * Get the last day of the year of the given epoch timestamp
     *
     * @return The ZonedDateTime of the last day of the year
     */
    fun getEndDateOfYear(epochMillis: Long): ZonedDateTime {
        return epochToLocalZonedDateTime(epochMillis)
            .with(TemporalAdjusters.lastDayOfYear())
    }

    /**
     * Extension function to get timestamp of start of the day i.e. midnight in local timezone.
     *
     * @return The timestamp of the start of the day in milliseconds
     */

    fun getTodayStartTime(clock: Clock) = getNow(clock).truncatedTo(ChronoUnit.DAYS).toEpochMilli()

    fun isAfterStartOfToday(clock: Clock, timestamp: Long): Boolean {
        return timestamp >= getTodayStartTime(clock)
    }

    fun isBeforeStartOfToday(clock: Clock, timestamp: Long): Boolean {
        return timestamp < getTodayStartTime(clock)
    }

    fun getNow(clock: Clock): ZonedDateTime =
        ZonedDateTime.of(LocalDateTime.now(clock), ZoneId.systemDefault())

    fun getNowUTC(clock: Clock): ZonedDateTime =
        ZonedDateTime.of(LocalDateTime.now(clock), ZoneOffset.UTC)

    fun getEpochMillisNow(clock: Clock): Long = getNowUTC(clock).toEpochMilli()

    /**
     * Converts the seconds to the string representation of hours and minutes. i.e. 3 h 12 min
     * If the minute value is zero, then only hour value will be shown. i.e instead of  3 h 00 min,
     * it will show 3h only
     * Minutes are represented with two digits if only the first digit is non-zero, i.e if minute
     * value has 1 digit, 0 will not be appended. i.e instead of  3 h 05 min,
     * it will show 3h 5 min
     * If minutes is zero, it will only show hours
     *
     * Given seconds value is rounded to the closest full minute. The seconds value is assumed to
     * be less than 24h hours.
     *
     * @param [seconds] Length of duration in seconds
     * @return String representation of hours and minutes
     */
    fun getTimeStringAsHourAndMinute(seconds: Long, hourText: String, minuteText: String): String {
        val roundedSeconds = seconds + 30L // Round 30+ seconds to 1 minute
        val hours = roundedSeconds / 60L / 60L
        val minutes = (roundedSeconds / 60L) % 60L

        val formatter = when {
            hours == 0L -> {
                // It is less than 1 hour. We don't want to show 0h.
                DateTimeFormatter.ofPattern("m '$minuteText'")
            }
            minutes == 0L -> {
                // Don't show minutes if 0
                DateTimeFormatter.ofPattern("H '$hourText'")
            }
            else -> {
                DateTimeFormatter.ofPattern("H '$hourText' m '$minuteText'")
            }
        }

        // TODO: Get rid of abusing LocalTime formatter for formatting duration
        return LocalTime.MIN.plusSeconds(roundedSeconds).format(formatter)
    }

    /**
     * Converts time in seconds to H:mm format e.g. 6:37
     * @param [seconds]
     * @return String presentation of hours and minutes
     */
    fun getDurationStringAsHmm(seconds: Long): String {
        val duration = Duration.ofSeconds(seconds)
        val hours = (duration.toDaysPartSafe() * 24) + duration.toHoursPartSafe()
        val minutes = duration.toMinutesPartSafe()
        val minutesZeroPad = if (minutes < 10) "0" else ""
        return "$hours:$minutesZeroPad$minutes"
    }

    /**
     * Returns a DateTimeFormatter that can format date to local short format without year
     */
    @JvmStatic
    fun dateWithoutYearFormatter(locale: Locale = Locale.getDefault()): DateTimeFormatter {
        var pattern: String = DateTimeFormatterBuilder.getLocalizedDateTimePattern(
            FormatStyle.SHORT,
            null,
            Chronology.ofLocale(locale),
            locale
        )
        // Regex checks if string contains y or Y character or characters and a possible "/", "-" or "."
        // character right after the y
        val regexYearIsFirst = Regex("[yY]+(?:/|-|\\.)")
        // Regex checks if string contains y or Y character or characters and a possible "/", "-" or "."
        // character right before the y
        val regexYearIsLast = Regex("(?:/|-|\\.)[yY]+")
        pattern = regexYearIsFirst.replace(pattern, "")
        pattern = regexYearIsLast.replace(pattern, "")

        return DateTimeFormatter.ofPattern(pattern)
    }

    /**
     * Returns duration in hours:minutes format. Adds leading zeros if hour or minute value is
     * less than 10
     * @param [seconds] duration in seconds
     */
    fun durationInSecondsToHoursAndMinutes(seconds: Long): String =
        durationInSecondsToHoursAndMinutes(seconds, false)

    /**
     * Returns duration in hours:minutes format. By default adds leading zeros if
     * hour or minute value is less than 10
     * @param [seconds] duration in seconds
     * @param [roundMinutes] if true, minutes value is rounded, if false, floor value is used
     * @param [zeroPadHours] add leading zero for hours if less than 10
     */
    fun durationInSecondsToHoursAndMinutes(
        seconds: Long,
        roundMinutes: Boolean,
        zeroPadHours: Boolean = true
    ): String {
        var hours = TimeUnit.SECONDS.toHours(seconds)
        val secondsWithRounding =
            if (roundMinutes) {
                seconds + TimeUnit.MINUTES.toSeconds(1) / 2
            } else {
                seconds
            }
        var minutes =
            TimeUnit.SECONDS.toMinutes(secondsWithRounding) - TimeUnit.HOURS.toMinutes(hours)
        // Check after rounding minutes value if it's a full hour
        if (minutes == TimeUnit.MINUTES.toSeconds(1)) {
            hours = hours.inc()
            minutes = 0
        }
        val hourString = if (hours < 10 && zeroPadHours) "0$hours" else "$hours"
        val minuteString = if (minutes < 10) "0$minutes" else "$minutes"
        return "$hourString:$minuteString"
    }

    /**
     * Converts ZonedDateTime to HH:mm format and uses 24:00 instead of 00:00
     * @param [date]
     * @param [zeroPadHours] add leading zeros if hour value is less than 10
     * @return String presentation of hours and minutes
     */
    fun getZonedDateTimeAsHoursAndMinutes(
        date: ZonedDateTime,
        zeroPadHours: Boolean = true
    ): String {
        val pattern = if (zeroPadHours) "HH:mm" else "H:mm"
        val formattedDate = date.format(DateTimeFormatter.ofPattern(pattern))
        return if (formattedDate == "00:00" || formattedDate == "0:00") {
            "24:00"
        } else {
            formattedDate
        }
    }

    fun millisecondToSecond(millisecond: Long): Long = millisecond / 1000
}
