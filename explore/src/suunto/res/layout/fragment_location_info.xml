<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="awayFromYou"
            type="String" />

        <variable
            name="locationTitle"
            type="String" />

        <variable
            name="coordinatesText"
            type="String" />

        <variable
            name="altitudeText"
            type="String" />

        <variable
            name="isLoading"
            type="boolean" />

        <import type="android.view.View" />
    </data>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clickable="false"
        android:focusable="false">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:clickable="false"
            android:focusable="false">

            <!-- TODO: Consider removing this View moving the style to the root View -->
            <View
                android:id="@+id/location_info_bottom_sheet_background"
                style="@style/RoundedCornerBottomSheetStyle"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:clickable="true"
                android:elevation="@dimen/elevation_navbar"
                android:focusable="false"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:ignore="KeyboardInaccessibleWidget" />

            <Space
                android:id="@+id/title_spacer"
                android:layout_width="0dp"
                android:layout_height="@dimen/size_spacing_large"
                android:layout_marginBottom="@dimen/size_spacing_medium"
                app:layout_constraintBottom_toTopOf="@id/location_info_popular_starting_point"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_goneMarginBottom="@dimen/size_spacing_small" />

            <ImageView
                android:id="@+id/location_info_drag_handle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/size_spacing_smaller"
                android:contentDescription="@null"
                android:elevation="@dimen/elevation_navbar"
                android:src="@drawable/ic_handle"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/location_info_bottom_sheet_background" />

            <View
                android:id="@+id/location_info_drag_handle_touch_area"
                android:layout_width="match_parent"
                android:layout_height="28dp"
                android:elevation="@dimen/elevation_navbar"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.fragment.app.FragmentContainerView
                android:id="@+id/topRoutesDotsFragment"
                android:name="com.stt.android.home.explore.toproutes.dots.TopRoutesDotsFragment"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_marginTop="@dimen/size_spacing_xsmall"
                android:elevation="@dimen/elevation_navbar"
                app:layout_constraintBottom_toBottomOf="@id/title_spacer"
                app:layout_constraintEnd_toStartOf="@id/location_info_drag_handle"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/title_spacer" />

            <androidx.constraintlayout.widget.Guideline
                android:id="@+id/location_info_start_margin"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                app:layout_constraintGuide_begin="@dimen/size_spacing_medium" />

            <androidx.constraintlayout.widget.Guideline
                android:id="@+id/location_info_end_margin"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                app:layout_constraintGuide_end="@dimen/size_spacing_medium" />

            <TextView
                android:id="@+id/location_info_popular_starting_point"
                style="@style/HeaderLabel.Medium"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:elevation="@dimen/elevation_navbar"
                android:text="@string/popular_starting_point_label"
                android:textAllCaps="false"
                android:visibility="gone"
                app:layout_constraintBottom_toTopOf="@id/location_info_location_title_text"
                app:layout_constraintEnd_toEndOf="@id/location_info_end_margin"
                app:layout_constraintStart_toStartOf="@id/location_info_start_margin"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="Popular starting point"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/location_info_location_title_text"
                style="@style/Body.Large.Bold"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/size_spacing_small"
                android:elevation="@dimen/elevation_navbar"
                android:ellipsize="end"
                android:maxLines="2"
                android:text="@{locationTitle}"
                android:visibility="@{locationTitle.isEmpty() ? View.GONE : View.VISIBLE}"
                app:layout_constraintEnd_toEndOf="@id/location_info_end_margin"
                app:layout_constraintStart_toStartOf="@id/location_info_start_margin"
                app:layout_constraintTop_toBottomOf="@id/location_info_popular_starting_point"
                tools:text="Montana del Filo, Santa Cruz de Tenerife" />

            <TextView
                android:id="@+id/nearby_title_text"
                style="@style/Body.Large.Bold"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/size_spacing_small"
                android:elevation="@dimen/elevation_navbar"
                android:ellipsize="end"
                android:maxLines="2"
                android:text="@string/nearby_title"
                android:visibility="visible"
                app:layout_constraintEnd_toEndOf="@id/location_info_end_margin"
                app:layout_constraintStart_toStartOf="@id/location_info_start_margin"
                app:layout_constraintTop_toBottomOf="@id/location_info_popular_starting_point"
                tools:text="Montana del Filo, Santa Cruz de Tenerife" />

            <TextView
                android:id="@+id/tvAwayFromYour"
                style="@style/Body.Medium.DarkGray"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:elevation="@dimen/elevation_navbar"
                android:text="@{awayFromYou}"
                android:visibility="@{locationTitle.isEmpty() ? View.GONE : View.VISIBLE}"
                app:layout_constraintStart_toStartOf="@id/location_info_location_title_text"
                app:layout_constraintTop_toBottomOf="@id/location_info_location_title_text"
                tools:text="1.56 km away from you" />

            <!-- The views below location_info_location_title_text are wrapped in a separate layout
            to allow setting elevation for all of them. -->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/location_info_buttons_container"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:elevation="@dimen/elevation_navbar"
                android:paddingVertical="@dimen/size_spacing_medium"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tvAwayFromYour">

                <com.google.android.material.floatingactionbutton.FloatingActionButton
                    android:id="@+id/location_info_save_poi_fab"
                    style="@style/Fab.Small"
                    android:layout_marginEnd="@dimen/size_spacing_medium"
                    android:contentDescription="@null"
                    android:visibility="@{isLoading ? View.GONE : View.VISIBLE }"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:srcCompat="@drawable/save_poi" />

                <com.google.android.material.floatingactionbutton.FloatingActionButton
                    android:id="@+id/location_info_copy_coordinates_fab"
                    style="@style/Fab.Small"
                    android:layout_marginEnd="@dimen/size_spacing_medium"
                    android:contentDescription="@null"
                    android:visibility="@{isLoading ? View.GONE : View.VISIBLE }"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/location_info_save_poi_fab"
                    app:layout_constraintTop_toTopOf="parent"
                    app:srcCompat="@drawable/copy_coordinates" />

                <TextView
                    android:id="@+id/location_info_coordinates_text"
                    style="@style/Body.Medium"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/size_spacing_medium"
                    android:layout_marginEnd="@dimen/size_spacing_medium"
                    android:maxLines="2"
                    android:text="@{coordinatesText}"
                    android:textIsSelectable="true"
                    android:visibility="@{isLoading ? View.INVISIBLE : View.VISIBLE }"
                    app:layout_constraintEnd_toEndOf="@id/location_info_copy_coordinates_fab"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="28°25'08.1&quot;N 16°24'19.8&quot;W" />

                <TextView
                    android:id="@+id/location_info_altitude_text"
                    style="@style/Body.Medium"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/size_spacing_medium"
                    android:layout_marginEnd="@dimen/size_spacing_medium"
                    android:text="@{altitudeText}"
                    android:visibility="@{isLoading ? View.INVISIBLE : View.VISIBLE }"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="@id/location_info_copy_coordinates_fab"
                    app:layout_constraintStart_toStartOf="parent"
                    tools:text="Altitude: 1280 m" />

                <ProgressBar
                    android:layout_width="@dimen/size_icon_large"
                    android:layout_height="@dimen/size_icon_large"
                    android:indeterminateTint="@color/cloudy_gray"
                    android:visibility="@{isLoading ? View.VISIBLE : View.GONE }"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <!-- Barrier to handle positioning of weather fragment based on what's visible -->
            <androidx.constraintlayout.widget.Barrier
                android:id="@+id/weather_position_barrier"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                app:barrierDirection="bottom"
                app:constraint_referenced_ids="nearby_title_text,location_info_buttons_container" />

            <androidx.fragment.app.FragmentContainerView
                android:id="@+id/weatherInfoFragment"
                android:name="com.stt.android.home.explore.weather.WeatherInfoFragment"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/size_spacing_medium"
                android:elevation="@dimen/elevation_navbar"
                app:layout_constraintEnd_toEndOf="@id/location_info_end_margin"
                app:layout_constraintStart_toStartOf="@id/location_info_start_margin"
                app:layout_constraintTop_toBottomOf="@id/weather_position_barrier" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/location_info_opreations"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/size_spacing_medium"
                android:elevation="@dimen/elevation_navbar"
                android:orientation="horizontal"
                android:visibility="@{isLoading ? View.GONE : View.VISIBLE }"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/weatherInfoFragment">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/navigate_on_watch_now"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:background="?android:attr/selectableItemBackground"
                    android:gravity="center_vertical"
                    android:paddingHorizontal="@dimen/size_spacing_medium"
                    android:paddingVertical="@dimen/size_spacing_medium"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:ignore="UseCompoundDrawables">

                    <ImageView
                        android:id="@+id/iv_navigate_now"
                        android:layout_width="@dimen/size_icon_small"
                        android:layout_height="@dimen/size_icon_small"
                        android:contentDescription="@null"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintBottom_toBottomOf="parent"
                        android:src="@drawable/watch_navigate_outline" />

                    <TextView
                        android:id="@+id/tv_navigate_now"
                        style="@style/Body.Larger"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/size_spacing_medium"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toEndOf="@id/iv_navigate_now"
                        app:layout_constraintEnd_toStartOf="@id/iv_navigate_now_arrow"
                        android:text="@string/navigate_on_watch_now" />

                    <ImageView
                        android:id="@+id/iv_navigate_now_arrow"
                        android:layout_width="@dimen/size_icon_small"
                        android:layout_height="@dimen/size_icon_small"
                        android:contentDescription="@null"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintBottom_toBottomOf="parent"
                        android:src="@drawable/ic_right_arrow"
                        app:tint="@color/near_black" />
                </androidx.constraintlayout.widget.ConstraintLayout>

                <View
                    android:id="@+id/divider"
                    android:layout_width="0dp"
                    android:layout_height="@dimen/size_divider"
                    android:background="?suuntoDividerColor"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/navigate_on_watch_now" />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/location_info_start_here"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:background="?android:attr/selectableItemBackground"
                    android:paddingHorizontal="@dimen/size_spacing_medium"
                    android:paddingVertical="@dimen/size_spacing_medium"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/divider"
                    tools:ignore="UseCompoundDrawables">

                    <ImageView
                        android:id="@+id/iv_location_start_here"
                        android:layout_width="@dimen/size_icon_small"
                        android:layout_height="@dimen/size_icon_small"
                        android:contentDescription="@null"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintBottom_toBottomOf="parent"
                        android:src="@drawable/route_start_button_a" />

                    <TextView
                        android:id="@+id/tv_location_start_here"
                        style="@style/Body.Larger"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/size_spacing_medium"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toEndOf="@id/iv_location_start_here"
                        app:layout_constraintEnd_toStartOf="@id/iv_location_start_here_arrow"
                        android:text="@string/route_to_destination" />

                    <ImageView
                        android:id="@+id/iv_location_start_here_arrow"
                        android:layout_width="@dimen/size_icon_small"
                        android:layout_height="@dimen/size_icon_small"
                        android:contentDescription="@null"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintBottom_toBottomOf="parent"
                        android:src="@drawable/ic_right_arrow"
                        app:tint="@color/near_black" />
                </androidx.constraintlayout.widget.ConstraintLayout>

                <View
                    android:id="@+id/divider1"
                    android:layout_width="0dp"
                    android:layout_height="@dimen/size_divider"
                    android:background="?suuntoDividerColor"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/location_info_start_here" />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/location_info_end_here"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:background="?android:attr/selectableItemBackground"
                    android:gravity="center_vertical"
                    android:paddingHorizontal="@dimen/size_spacing_medium"
                    android:paddingVertical="@dimen/size_spacing_medium"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/divider1"
                    tools:ignore="UseCompoundDrawables">

                    <ImageView
                        android:id="@+id/iv_location_end_here"
                        android:layout_width="@dimen/size_icon_small"
                        android:layout_height="@dimen/size_icon_small"
                        android:contentDescription="@null"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintBottom_toBottomOf="parent"
                        android:src="@drawable/route_end_button_b" />

                    <TextView
                        android:id="@+id/tv_location_end_here"
                        style="@style/Body.Larger"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/size_spacing_medium"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toEndOf="@id/iv_location_end_here"
                        app:layout_constraintEnd_toStartOf="@id/iv_location_end_here_arrow"
                        android:text="@string/route_from_destination" />

                    <ImageView
                        android:id="@+id/iv_location_end_here_arrow"
                        android:layout_width="@dimen/size_icon_small"
                        android:layout_height="@dimen/size_icon_small"
                        android:contentDescription="@null"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintBottom_toBottomOf="parent"
                        android:src="@drawable/ic_right_arrow"
                        app:tint="@color/near_black" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <View
                    android:id="@+id/divider2"
                    android:layout_width="0dp"
                    android:layout_height="@dimen/size_divider"
                    android:background="?suuntoDividerColor"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/location_info_end_here" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <!-- The views below location_info_location_title_text are wrapped in a separate layout
            to allow setting elevation for all of them. -->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/location_info_top_routes_container"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:elevation="@dimen/elevation_navbar"
                android:paddingBottom="@dimen/size_spacing_medium"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/location_info_opreations">

                <TextView
                    android:id="@+id/location_info_popular_routes_label"
                    style="@style/HeaderLabel.Medium"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/size_spacing_medium"
                    android:layout_marginTop="@dimen/size_spacing_medium"
                    android:text="@string/popular_routes"
                    android:textAllCaps="false"
                    android:visibility="gone"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:visibility="visible" />

                <TextView
                    android:id="@+id/location_info_no_popular_routes_text"
                    style="@style/Body.Larger"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/size_spacing_medium"
                    android:layout_marginTop="@dimen/size_spacing_small"
                    android:layout_marginEnd="@dimen/size_spacing_medium"
                    android:text="@string/no_popular_routes"
                    android:visibility="gone"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/location_info_popular_routes_label"
                    tools:visibility="visible" />

                <LinearLayout
                    android:id="@+id/location_info_popular_routes"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:visibility="gone"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/location_info_popular_routes_label" />

                <androidx.constraintlayout.widget.Barrier
                    android:id="@+id/popular_routes_barrier"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    app:barrierDirection="bottom"
                    app:constraint_referenced_ids="location_info_no_popular_routes_text,location_info_popular_routes" />

                <TextView
                    android:id="@+id/location_info_community_label"
                    style="@style/HeaderLabel.Medium"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/size_spacing_medium"
                    android:layout_marginTop="@dimen/size_spacing_medium"
                    android:text="@string/community"
                    android:textAllCaps="false"
                    android:visibility="gone"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/popular_routes_barrier"
                    tools:visibility="visible" />

                <include
                    android:id="@+id/location_info_community_item"
                    layout="@layout/item_community"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/list_item_two_lines_height"
                    android:visibility="gone"
                    app:layout_constraintTop_toBottomOf="@id/location_info_community_label" />
            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.core.widget.NestedScrollView>
</layout>
