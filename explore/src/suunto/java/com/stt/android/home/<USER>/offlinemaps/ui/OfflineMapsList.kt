package com.stt.android.home.explore.offlinemaps.ui

import android.text.format.Formatter
import androidx.annotation.StringRes
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListScope
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.tooling.preview.PreviewParameterProvider
import androidx.compose.ui.unit.dp
import com.stt.android.compose.component.SuuntoLinearProgressBar
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.SuuntoIcon
import com.stt.android.compose.theme.SuuntoIcons
import com.stt.android.compose.theme.darkGrey
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.lightGrey
import com.stt.android.compose.theme.material3.body
import com.stt.android.compose.theme.material3.header
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.widgets.PrimaryButton
import com.stt.android.home.explore.offlinemaps.entities.MemoryUsage
import com.stt.android.offlinemaps.datasource.DummyOfflineRegionDataSource
import com.stt.android.offlinemaps.entity.OfflineRegionResult
import com.stt.android.offlinemaps.ui.FullscreenAnimationPopup
import com.stt.android.offlinemaps.ui.OfflineRegionItem
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf
import kotlinx.coroutines.delay
import java.util.Locale
import com.stt.android.R as BaseR

@Composable
internal fun OfflineMapsList(
    supportsOfflineMapsOnMobile: Boolean,
    scrollToRegionId: String?,
    regions: ImmutableList<OfflineRegionResult.OfflineRegion>,
    memoryUsage: MemoryUsage?,
    cancellingDownload: Boolean,
    requestingDownload: Boolean,
    onClick: (regionId: String) -> Unit,
    onCancel: (OfflineRegionResult.OfflineRegion) -> Unit,
    onRetry: (OfflineRegionResult.OfflineRegion) -> Unit,
    onDownloadMaps: () -> Unit,
    modifier: Modifier = Modifier,
) {
    val waitingForDownload = regions.filter { region ->
        region.downloadRequested || (region.downloading && !region.downloadingUpdate) || region.downloadFailed
    }
    val downloaded = regions.filter(OfflineRegionResult.OfflineRegion::downloaded)
    val deleteRequested = regions.filter { region -> region.deleteRequested }
    val updatesAvailable = downloaded.filter { region -> region.updateAvailable || region.downloadingUpdate }
    if (waitingForDownload.isEmpty() && downloaded.isEmpty() && deleteRequested.isEmpty()) {
        EmptyState(
            onDownloadMaps = onDownloadMaps,
            modifier = modifier.fillMaxSize(),
        )

        return
    }

    val context = LocalContext.current
    val listState = rememberLazyListState()
    var showInfoDialog by rememberSaveable { mutableStateOf(DialogType.UNKNOWN) }

    LazyColumn(
        modifier = modifier,
        state = listState,
        contentPadding = PaddingValues(bottom = MaterialTheme.spacing.xxxlarge),
    ) {
        if (memoryUsage != null) {
            item(key = "Memory usage item") {
                MemoryUsageItem(
                    memoryUsage = memoryUsage,
                    modifier = Modifier.animateItem(),
                )
            }
        }

        item(key = "Download maps item") {
            DownloadMapsItem(
                onDownloadMaps = onDownloadMaps,
                modifier = Modifier.animateItem(),
            )
        }

        regionsSection(
            title = BaseR.string.offline_maps_delete_requested,
            icon = SuuntoIcons.Info,
            onIconClick = { showInfoDialog = DialogType.DELETE_INFO },
            subtitle = memoryUsage?.deleted
                ?.let { Formatter.formatShortFileSize(context, it) },
            regions = deleteRequested,
            regionKey = { "${it.id}_to_be_deleted" },
            cancellingDownload = cancellingDownload,
            requestingDownload = requestingDownload,
            onCancel = onCancel,
            onRetry = onRetry,
            showDivider = true,
        )

        regionsSection(
            title = BaseR.string.offline_maps_waiting_for_download,
            icon = null,
            onIconClick = null,
            subtitle = memoryUsage?.queued
                ?.let { Formatter.formatShortFileSize(context, it) },
            regions = waitingForDownload,
            regionKey = { "${it.id}_to_be_downloaded" },
            cancellingDownload = cancellingDownload,
            requestingDownload = requestingDownload,
            onCancel = onCancel,
            onRetry = onRetry,
            showDivider = true,
        )

        regionsSection(
            title = BaseR.string.offline_maps_updates_title,
            icon = SuuntoIcons.Info,
            onIconClick = { showInfoDialog = DialogType.UPDATE_INFO },
            subtitle = memoryUsage?.updated
                ?.let { Formatter.formatShortFileSize(context, it) },
            regions = updatesAvailable,
            regionKey = { "${it.id}_to_be_updated" },
            cancellingDownload = cancellingDownload,
            requestingDownload = requestingDownload,
            onCancel = onCancel,
            onRetry = onRetry,
            showDivider = true,
        )

        if (downloaded.isNotEmpty()) {
            item(key = BaseR.string.offline_maps_downloaded) {
                SectionHeader(
                    title = BaseR.string.offline_maps_downloaded,
                    icon = null,
                    onIconClick = null,
                    subtitle = memoryUsage?.downloaded
                        ?.let { Formatter.formatShortFileSize(context, it) },
                    modifier = Modifier.animateItem(),
                )
            }

            items(
                items = downloaded,
                key = OfflineRegionResult.OfflineRegion::id,
            ) { region ->
                OfflineMapItem(
                    supportsOfflineMapsOnMobile = supportsOfflineMapsOnMobile,
                    map = region,
                    onClick = onClick,
                    modifier = Modifier
                        .animateItem()
                        .padding(bottom = MaterialTheme.spacing.medium),
                )
            }
        }
    }

    FullscreenAnimationPopup(
        visible = showInfoDialog != DialogType.UNKNOWN,
        onBatteryTipDismiss = {
            showInfoDialog = DialogType.UNKNOWN
        },
        titleRes = when (showInfoDialog) {
            DialogType.DELETE_INFO -> BaseR.string.map_delete_info_title
            else -> BaseR.string.map_update_info_title
        },
        textRes = when (showInfoDialog) {
            DialogType.DELETE_INFO -> BaseR.string.map_delete_info_text
            else -> BaseR.string.map_update_info_text
        },
        assetFileName = when (showInfoDialog) {
            DialogType.DELETE_INFO -> "charging_prompt.json"
            else -> "download_maps_animation.json"
        },
    )

    LaunchedEffect(key1 = Unit) {
        downloaded.indexOfLast { it.id == scrollToRegionId }.apply {
            val index = if (this == -1) {
                val deleted = deleteRequested.indexOfLast { it.id == scrollToRegionId }
                if (deleted == -1) {
                    return@apply
                }
                deleted + 3 // Watch memory, "Download maps" item, "Waiting to be deleted" header
            } else {
                var indexOffset = 2 // "Downloaded" header item + "Download maps" item
                if (memoryUsage != null) {
                    indexOffset += 1
                }
                if (waitingForDownload.isNotEmpty()) {
                    indexOffset += waitingForDownload.size + 1 // header
                }
                if (deleteRequested.isNotEmpty()) {
                    indexOffset += deleteRequested.size + 1 // header
                }
                if (updatesAvailable.isNotEmpty()) {
                    indexOffset += updatesAvailable.size + 1 // header
                }
                this + indexOffset
            }
            delay(500)
            listState.animateScrollToItem(index = index)
        }
    }
}

@Composable
private fun MemoryUsageItem(
    memoryUsage: MemoryUsage,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier
            .background(MaterialTheme.colorScheme.surface),
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.small),
    ) {
        Spacer(Modifier.height(MaterialTheme.spacing.large))

        Box(
            modifier = Modifier
                .fillMaxWidth()
                .padding(
                    horizontal = MaterialTheme.spacing.medium,
                ),
        ) {
            Text(
                text = stringResource(id = BaseR.string.memory_usage_title),
                style = MaterialTheme.typography.header
            )

            Text(
                text = stringResource(
                    id = BaseR.string.memory_usage_free_space,
                    Formatter.formatShortFileSize(LocalContext.current, memoryUsage.free),
                ),
                modifier = Modifier.align(Alignment.CenterEnd),
                style = MaterialTheme.typography.body,
            )
        }

        Spacer(Modifier.height(MaterialTheme.spacing.small))

        SuuntoLinearProgressBar(
            progress = memoryUsage.used.toFloat() / memoryUsage.total.toFloat(),
            modifier = Modifier
                .padding(
                    horizontal = MaterialTheme.spacing.medium,
                )
                .fillMaxWidth(),
            color = if (memoryUsage.freePercentage <= 10) {
                MaterialTheme.colorScheme.error
            } else {
                MaterialTheme.colorScheme.primary
            },
        )

        Spacer(Modifier.height(MaterialTheme.spacing.medium))

        HorizontalDivider()
    }
}

@Composable
private fun DownloadMapsItem(
    onDownloadMaps: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .clickable(onClick = onDownloadMaps)
            .background(MaterialTheme.colorScheme.surface)
            .padding(MaterialTheme.spacing.medium),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Icon(
            painter = SuuntoIcons.OfflineMap.asPainter(),
            contentDescription = null,
            modifier = Modifier.size(MaterialTheme.iconSizes.medium)
        )

        Spacer(Modifier.width(MaterialTheme.spacing.medium))

        Text(
            text = stringResource(id = BaseR.string.offline_maps_download_maps_button),
            style = MaterialTheme.typography.bodyLarge,
        )

        Spacer(Modifier.weight(1.0F))

        Icon(
            painter = SuuntoIcons.ActionRight.asPainter(),
            contentDescription = null,
            modifier = Modifier.size(MaterialTheme.iconSizes.medium)
        )
    }
}

private fun LazyListScope.regionsSection(
    @StringRes title: Int,
    icon: SuuntoIcon?,
    onIconClick: (() -> Unit)?,
    subtitle: String?,
    regions: List<OfflineRegionResult.OfflineRegion>,
    regionKey: (OfflineRegionResult.OfflineRegion) -> Any,
    cancellingDownload: Boolean,
    requestingDownload: Boolean,
    onCancel: (OfflineRegionResult.OfflineRegion) -> Unit,
    onRetry: (OfflineRegionResult.OfflineRegion) -> Unit,
    showDivider: Boolean,
) {
    if (regions.isEmpty()) {
        return
    }

    item(key = title) {
        SectionHeader(
            title = title,
            icon = icon,
            onIconClick = onIconClick,
            subtitle = subtitle,
            modifier = Modifier.animateItem(),
        )
    }

    itemsIndexed(
        items = regions,
        key = { _, region -> regionKey(region) }
    ) { index, region ->
        OfflineRegionItem(
            region = region,
            cancellingDownload = cancellingDownload,
            requestingDownload = requestingDownload,
            onClick = { /* Do nothing */ },
            onCancel = onCancel,
            onRetry = onRetry,
            modifier = Modifier.animateItem(),
        )

        if (showDivider && index != regions.lastIndex) {
            HorizontalDivider(modifier = Modifier.animateItem())
        }
    }
}

@Composable
private fun SectionHeader(
    @StringRes title: Int,
    icon: SuuntoIcon?,
    onIconClick: (() -> Unit)?,
    subtitle: String?,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .background(MaterialTheme.colorScheme.lightGrey)
            .padding(MaterialTheme.spacing.medium),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Text(
            text = stringResource(title).uppercase(Locale.getDefault()),
            style = MaterialTheme.typography.header,
        )

        if (icon != null && onIconClick != null) {
            Icon(
                painter = icon.asPainter(),
                contentDescription = null,
                modifier = Modifier
                    .offset(x = 2.dp, y = (-6).dp)
                    .size(MaterialTheme.iconSizes.mini)
                    .clickable(onClick = onIconClick),
                tint = MaterialTheme.colorScheme.onSurfaceVariant,
            )
        }

        Spacer(Modifier.weight(1.0F))

        if (!subtitle.isNullOrEmpty()) {
            Text(
                text = subtitle,
                style = MaterialTheme.typography.body,
            )
        }
    }
}

@Composable
private fun EmptyState(
    onDownloadMaps: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(top = MaterialTheme.spacing.medium),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        Icon(
            painter = SuuntoIcons.OfflineMap.asPainter(),
            contentDescription = null,
            tint = MaterialTheme.colorScheme.darkGrey,
            modifier = Modifier
                .padding(MaterialTheme.spacing.medium),
        )

        Text(
            text = stringResource(BaseR.string.offline_maps_library_empty_text),
            modifier = Modifier
                .padding(horizontal = MaterialTheme.spacing.large),
            color = MaterialTheme.colorScheme.darkGrey,
            textAlign = TextAlign.Center,
            style = MaterialTheme.typography.bodyLarge,
        )

        Spacer(Modifier.height(MaterialTheme.spacing.xxlarge))

        PrimaryButton(
            text = stringResource(id = BaseR.string.offline_maps_download_maps_button),
            onClick = onDownloadMaps,
            modifier = Modifier
                .fillMaxWidth()
                .padding(
                    horizontal = MaterialTheme.spacing.medium,
                )
        )
    }
}

private enum class DialogType {
    UNKNOWN,
    DELETE_INFO,
    UPDATE_INFO,
}

@Preview(showBackground = true, backgroundColor = 0xFFCCCCCC)
@Composable
private fun OfflineMapsListPreview(
    @PreviewParameter(OfflineMapsListParamsProvider::class) params: OfflineMapsListParams,
) {
    M3AppTheme {
        OfflineMapsList(
            supportsOfflineMapsOnMobile = true,
            scrollToRegionId = null,
            regions = params.maps,
            memoryUsage = MemoryUsage(
                1000000L,
                33,
                2000000L,
                3000000L,
                100000L,
                100000L,
                100000L,
                100000L
            ),
            cancellingDownload = false,
            requestingDownload = false,
            onClick = {},
            onCancel = {},
            onRetry = {},
            onDownloadMaps = {},
        )
    }
}

private class OfflineMapsListParamsProvider : PreviewParameterProvider<OfflineMapsListParams> {
    override val values = sequenceOf(
        OfflineMapsListParams(
            maps = DummyOfflineRegionDataSource.DUMMY_OFFLINE_REGION_GROUPS[2].regions
        ),
        OfflineMapsListParams(
            maps = persistentListOf()
        )
    )
}

private data class OfflineMapsListParams(
    val maps: ImmutableList<OfflineRegionResult.OfflineRegion>,
)
