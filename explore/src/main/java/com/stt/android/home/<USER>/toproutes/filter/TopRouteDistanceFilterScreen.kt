package com.stt.android.home.explore.toproutes.filter

import android.annotation.SuppressLint
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Text
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.CornerRadius
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.cloudyGrey
import com.stt.android.compose.theme.lightGrey
import com.stt.android.compose.theme.material3.bodyLargeBold
import com.stt.android.compose.theme.material3.header
import com.stt.android.compose.theme.spacing
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.home.explore.R
import com.stt.android.home.explore.routes.list.DistanceFilter
import com.stt.android.home.explore.routes.list.FilterPoint
import kotlin.math.abs

@Composable
internal fun TopRouteDistanceFilterScreen(
    measurementUnit: MeasurementUnit,
    distanceFilter: DistanceFilter,
    onDistanceFilterSelect: (DistanceFilter) -> Unit,
    modifier: Modifier = Modifier,
) {
    Column(modifier = modifier) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .padding(
                    horizontal = MaterialTheme.spacing.medium,
                    vertical = MaterialTheme.spacing.small
                )
        ) {
            Text(
                modifier = Modifier.align(Alignment.CenterStart),
                text = stringResource(id = R.string.top_route_filter_distance),
                style = MaterialTheme.typography.bodyLargeBold,
                color = MaterialTheme.colorScheme.onSurface,
            )

            Text(
                modifier = Modifier.align(Alignment.CenterEnd),
                text = buildString {
                    if (distanceFilter.leftValue < FilterPoint.POINT_MORE_THAN_100KM.minKm) {
                        append(distanceFilter.leftValue)
                        append("-")
                    }
                    if (distanceFilter.rightValue >= FilterPoint.POINT_MORE_THAN_100KM.minKm) {
                        append(FilterPoint.POINT_MORE_THAN_100KM.digitalText)
                    } else {
                        append(distanceFilter.rightValue)
                    }
                    append(" ")
                    append(stringResource(measurementUnit.distanceUnit))
                },
                style = MaterialTheme.typography.header,
                color = MaterialTheme.colorScheme.primary
            )
        }

        HorizontalDivider(color = MaterialTheme.colorScheme.lightGrey)

        Box(
            modifier = Modifier.padding(MaterialTheme.spacing.medium),
        ) {
            DistanceRangeSlider(
                modifier = Modifier.fillMaxWidth(),
                measurementUnit = measurementUnit,
                initialStartKm = distanceFilter.leftValue,
                initialEndKm = distanceFilter.rightValue,
                onValueChange = {
                    onDistanceFilterSelect(it)
                }
            )
        }
    }
}

@SuppressLint("UseOfNonLambdaOffsetOverload")
@Composable
fun DistanceRangeSlider(
    measurementUnit: MeasurementUnit,
    onValueChange: (DistanceFilter) -> Unit,
    modifier: Modifier = Modifier,
    initialStartKm: Int = FilterPoint.POINT_MIN.minKm,
    initialEndKm: Int = FilterPoint.POINT_MORE_THAN_100KM.minKm,
) {
    val activeTrackBackgroundColor = MaterialTheme.colorScheme.onBackground
    val inActiveTrackBackgroundColor = MaterialTheme.colorScheme.cloudyGrey
    val thumbColor = MaterialTheme.colorScheme.onSurface

    val kmToScale: (Int) -> Float = { km ->
        when {
            km <= FilterPoint.POINT_30KM.minKm -> km.toFloat()
            km <= FilterPoint.POINT_100KM.minKm -> FilterPoint.POINT_30KM.scale + (km - FilterPoint.POINT_30KM.minKm) / 5f
            else -> FilterPoint.POINT_MORE_THAN_100KM.scale.toFloat()
        }
    }

    val scaleToKm: (Float) -> Int = { scale ->
        when {
            scale <= FilterPoint.POINT_30KM.scale -> scale.toInt()
            scale <= FilterPoint.POINT_100KM.scale -> FilterPoint.POINT_30KM.minKm + ((scale - FilterPoint.POINT_30KM.scale) * 5).toInt()
            else -> FilterPoint.POINT_MORE_THAN_100KM.minKm
        }
    }

    val valueRange = 0f..FilterPoint.POINT_MORE_THAN_100KM.scale.toFloat()
    var startScale = kmToScale(initialStartKm).coerceIn(valueRange)
    var endScale = kmToScale(initialEndKm).coerceIn(valueRange)

    val trackHeight = 6.dp
    val thumbRadius = 8.dp
    val thumbRadiusPx = with(LocalDensity.current) { thumbRadius.toPx() }

    Column(
        modifier = modifier
    ) {
        var widthPx by remember { mutableFloatStateOf(0f) }

        Canvas(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = MaterialTheme.spacing.small)
                .height(18.dp)
                .pointerInput(Unit) {
                    detectDragGestures { change, _ ->
                        change.consume()
                        val x = change.position.x.coerceIn(0f, widthPx)
                        val scale = (x / widthPx) * valueRange.endInclusive
                        val newScale = scale.coerceIn(valueRange)

                        val shouldUpdateStart =
                            abs(newScale - startScale) < abs(newScale - endScale)

                        if (shouldUpdateStart) {
                            startScale = newScale.coerceIn(valueRange.start, endScale)
                        } else {
                            endScale = newScale.coerceIn(startScale, valueRange.endInclusive)
                        }

                        val leftValue = scaleToKm(startScale)
                        val rightValue = scaleToKm(endScale)

                        onValueChange(
                            DistanceFilter(
                                minKM = FilterPoint.POINT_MIN.minKm,
                                leftValue = leftValue,
                                maxKm = FilterPoint.POINT_MORE_THAN_100KM.minKm,
                                rightValue = rightValue
                            )
                        )
                    }
                }
                .onSizeChanged { widthPx = it.width.toFloat() }
        ) {
            val trackY = size.height / 2
            val trackHeightPx = trackHeight.toPx()

            val startX = (startScale / valueRange.endInclusive) * size.width
            val endX = (endScale / valueRange.endInclusive) * size.width

            // Inactive track
            drawRoundRect(
                color = inActiveTrackBackgroundColor,
                topLeft = Offset(0f, trackY - trackHeightPx / 2),
                size = Size(size.width, trackHeightPx),
                cornerRadius = CornerRadius(trackHeightPx / 2, trackHeightPx / 2)
            )

            // Active track
            drawRoundRect(
                color = activeTrackBackgroundColor,
                topLeft = Offset(startX, trackY - trackHeightPx / 2),
                size = Size((endX - startX).coerceAtLeast(1f), trackHeightPx),
                cornerRadius = CornerRadius(trackHeightPx / 2, trackHeightPx / 2)
            )

            // Thumbs
            drawCircle(thumbColor, radius = thumbRadiusPx, center = Offset(startX, trackY))
            drawCircle(thumbColor, radius = thumbRadiusPx, center = Offset(endX, trackY))
        }

        Spacer(modifier = Modifier.height(MaterialTheme.spacing.xsmall))

        // Labels
        Box(modifier = Modifier.fillMaxWidth()) {
            Text(
                text = buildString {
                    append(FilterPoint.POINT_MIN.digitalText)
                    append(" ")
                    append(stringResource(measurementUnit.distanceUnit))
                },
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.secondary,
                modifier = Modifier.align(Alignment.CenterStart)
            )

            Text(
                text = buildString {
                    append(FilterPoint.POINT_30KM.digitalText)
                    append(" ")
                    append(stringResource(measurementUnit.distanceUnit))
                },
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.secondary,
                modifier = Modifier
                    .align(Alignment.CenterStart)
                    .offset(x = with(LocalDensity.current) {
                        (FilterPoint.POINT_30KM.scale.toFloat() / valueRange.endInclusive * widthPx).toDp() - 12.dp
                    })
            )

            Text(
                text = buildString {
                    append(FilterPoint.POINT_MORE_THAN_100KM.digitalText)
                    append(" ")
                    append(stringResource(measurementUnit.distanceUnit))
                },
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.secondary,
                modifier = Modifier.align(Alignment.CenterEnd)
            )
        }
    }
}

@Preview(backgroundColor = 0xFFFFFF, showBackground = true)
@Composable
private fun TopRouteDistanceFilterPreview() {
    M3AppTheme {
        TopRouteDistanceFilterScreen(
            measurementUnit = MeasurementUnit.METRIC,
            distanceFilter = DistanceFilter.default(),
            onDistanceFilterSelect = {}
        )
    }
}
