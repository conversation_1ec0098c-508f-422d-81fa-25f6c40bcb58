package com.stt.android.home.explore.searchv2

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.darkGrey
import com.stt.android.compose.theme.material3.bodyBold
import com.stt.android.compose.theme.nearBlack
import com.stt.android.compose.theme.nearWhite
import com.stt.android.compose.theme.spacing
import com.stt.android.home.explore.R

@Composable
internal fun MapSearchAppbar(
    onQueryChange: (String) -> Unit,
    onCancel: () -> Unit,
    modifier: Modifier = Modifier,
) {
    var query by rememberSaveable { mutableStateOf("") }

    Box(
        modifier = modifier
            .fillMaxWidth()
            .padding(
                horizontal = MaterialTheme.spacing.medium,
                vertical = MaterialTheme.spacing.small
            )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .height(40.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Row(
                modifier = Modifier
                    .weight(1f)
                    .fillMaxHeight()
                    .background(
                        color = MaterialTheme.colorScheme.nearWhite,
                        shape = RoundedCornerShape(40.dp)
                    )
                    .padding(
                        horizontal = MaterialTheme.spacing.smaller,
                        vertical = MaterialTheme.spacing.small
                    ),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    painter = painterResource(R.drawable.ic_map_search),
                    tint = MaterialTheme.colorScheme.darkGrey,
                    contentDescription = "null",
                    modifier = Modifier.size(24.dp)
                )

                Spacer(modifier = Modifier.width(MaterialTheme.spacing.xsmall))

                BasicTextField(
                    value = query,
                    onValueChange = { newText ->
                        query = newText
                        onQueryChange(newText)
                    },
                    textStyle = MaterialTheme.typography.bodyLarge.merge(
                        color = MaterialTheme.colorScheme.nearBlack
                    ),
                    cursorBrush = SolidColor(MaterialTheme.colorScheme.primary),
                    decorationBox = { innerTextField ->
                        Box(
                            contentAlignment = Alignment.CenterStart
                        ) {
                            if (query.isEmpty()) {
                                Text(
                                    text = stringResource(R.string.map_search_hilt),
                                    style = MaterialTheme.typography.bodyLarge,
                                    color = MaterialTheme.colorScheme.darkGrey,
                                )
                            }
                            innerTextField()
                        }
                    },
                    singleLine = true,
                    modifier = Modifier
                        .weight(1f)
                        .fillMaxHeight(),
                )

                if (query.isNotEmpty()) {
                    Icon(
                        painter = painterResource(R.drawable.ic_clear),
                        tint = MaterialTheme.colorScheme.darkGrey,
                        contentDescription = null,
                        modifier = Modifier
                            .size(24.dp)
                            .clickable {
                                query = ""
                                onQueryChange("")
                            }
                    )
                }
            }

            Spacer(modifier = Modifier.width(MaterialTheme.spacing.small))

            Text(
                text = stringResource(com.stt.android.R.string.cancel),
                style = MaterialTheme.typography.bodyBold,
                color = MaterialTheme.colorScheme.primary,
                modifier = Modifier
                    .fillMaxHeight()
                    .wrapContentHeight(Alignment.CenterVertically)
                    .clickable(
                        interactionSource = remember { MutableInteractionSource() },
                        indication = null,
                    ) {
                        onCancel()
                    }
            )
        }
    }
}

@Preview
@Composable
private fun MapSearchAppbarPreview() {
    M3AppTheme {
        MapSearchAppbar(
            onQueryChange = {},
            onCancel = {}
        )
    }
}
