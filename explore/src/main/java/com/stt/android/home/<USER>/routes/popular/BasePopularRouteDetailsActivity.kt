package com.stt.android.home.explore.routes.popular

import android.app.Activity
import android.content.Context
import android.content.DialogInterface
import android.content.Intent
import android.content.SharedPreferences
import android.net.Uri
import android.os.Bundle
import android.provider.Settings
import android.text.method.LinkMovementMethod
import android.view.MenuItem
import android.view.View
import android.view.ViewTreeObserver.OnGlobalLayoutListener
import androidx.activity.addCallback
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.annotation.IdRes
import androidx.appcompat.app.AppCompatActivity
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.core.text.HtmlCompat
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.fragment.app.commit
import androidx.fragment.app.commitNow
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.google.android.gms.maps.model.LatLng
import com.google.android.gms.maps.model.LatLngBounds
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.snackbar.Snackbar
import com.soy.algorithms.climbanalysis.entities.ClimbGuidance
import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.common.ui.SimpleDialogFragment
import com.stt.android.common.ui.debounce
import com.stt.android.common.ui.observeK
import com.stt.android.common.ui.observeNotNull
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.util.setContentWithM3Theme
import com.stt.android.controllers.UserSettingsController
import com.stt.android.core.utils.EventThrottler
import com.stt.android.di.FeatureTogglePreferences
import com.stt.android.domain.diarycalendar.LocationWithActivityType
import com.stt.android.domain.routes.TopRoute
import com.stt.android.domain.routes.WaypointTools
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.extensions.combineLatest
import com.stt.android.home.diary.diarycalendar.RouteAndActivityType
import com.stt.android.home.explore.R
import com.stt.android.home.explore.databinding.ActivityPopularRouteDetailBinding
import com.stt.android.home.explore.library.LibraryActivity
import com.stt.android.home.explore.routes.MapPresenter
import com.stt.android.home.explore.routes.MapView
import com.stt.android.home.explore.routes.RouteAltitudeChartData
import com.stt.android.home.explore.routes.RouteDetailsNavigator
import com.stt.android.home.explore.routes.RouteUtils.getRouteBounds
import com.stt.android.home.explore.routes.RouteUtils.routePointsToLatLngList
import com.stt.android.home.explore.routes.RouteValueFormatHelper
import com.stt.android.home.explore.routes.details.PopularRouteDetailsActivity
import com.stt.android.home.explore.routes.details.RouteDetailViewModel
import com.stt.android.home.explore.routes.details.TopRouteDetailsViewData
import com.stt.android.home.explore.routes.planner.BaseRoutePlannerActivity
import com.stt.android.home.explore.routes.ui.ClimbSegmentRenderer
import com.stt.android.intentresolver.TopRouteAction
import com.stt.android.intentresolver.LibraryTab
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.maps.MapFloatingActionButtons
import com.stt.android.maps.MapType
import com.stt.android.maps.OnMapReadyCallback
import com.stt.android.maps.SuuntoMap
import com.stt.android.maps.SuuntoMapOptions
import com.stt.android.maps.SuuntoSupportMapFragment
import com.stt.android.maps.SuuntoTileOverlay
import com.stt.android.maps.SuuntoTileOverlayGroup
import com.stt.android.maps.SuuntoTileOverlayOptions
import com.stt.android.maps.location.SuuntoLocationSource
import com.stt.android.maps.newLatLng
import com.stt.android.maps.newLatLngBounds
import com.stt.android.maps.newLatLngZoom
import com.stt.android.ui.map.HideCyclingForbiddenRoadsLiveData
import com.stt.android.ui.map.Map3dEnabledLiveData
import com.stt.android.ui.map.MapHelper
import com.stt.android.ui.map.RouteMarkerHelper
import com.stt.android.ui.map.SelectedHeatmapTypeLiveData
import com.stt.android.ui.map.SelectedRoadSurfaceTypesLiveData
import com.stt.android.ui.map.selection.MapSelectionDialogFragment
import com.stt.android.ui.utils.setOnClickListenerThrottled
import com.stt.android.utils.FlavorUtils
import com.stt.android.utils.PermissionUtils
import com.stt.android.utils.STTConstants
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch
import pub.devrel.easypermissions.EasyPermissions
import timber.log.Timber
import javax.inject.Inject
import com.stt.android.R as BaseR

abstract class BasePopularRouteDetailsActivity :
    AppCompatActivity(),
    MapView, SimpleDialogFragment.Callback,
    OnMapReadyCallback {
    class Navigator : RouteDetailsNavigator

    protected lateinit var binding: ActivityPopularRouteDetailBinding

    private val viewModel: RouteDetailViewModel by viewModels()

    private lateinit var formatHelper: RouteValueFormatHelper

    @Inject
    lateinit var mapPresenter: MapPresenter

    @Inject
    lateinit var userSettingsController: UserSettingsController

    @Inject
    lateinit var infoModelFormatter: InfoModelFormatter

    @Inject
    lateinit var measurementUnit: MeasurementUnit

    @Inject
    lateinit var waypointTools: WaypointTools

    @Inject
    lateinit var map3dEnabledLiveData: Map3dEnabledLiveData

    @Inject
    lateinit var selectedHeatmapTypeLiveData: SelectedHeatmapTypeLiveData

    @Inject
    lateinit var selectedRoadSurfaceTypesLiveData: SelectedRoadSurfaceTypesLiveData

    @Inject
    lateinit var hideCyclingForbiddenRoadsLiveData: HideCyclingForbiddenRoadsLiveData

    @Inject
    lateinit var locationSource: SuuntoLocationSource

    @Inject
    lateinit var amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker

    @Inject
    @FeatureTogglePreferences
    lateinit var featureTogglePreferences: SharedPreferences

    private var heatmapOverlay: SuuntoTileOverlay? = null
    private var startingPointsOverlay: SuuntoTileOverlay? = null
    private var roadSurfaceOverlay: SuuntoTileOverlayGroup? = null

    private var mapFragment: SuuntoSupportMapFragment? = null
    private var isRouteDrawn = false
    private val climbSegmentRenderer by lazy {
        ClimbSegmentRenderer(requireNotNull(mapFragment))
    }

    private val optionsClickThrottler = EventThrottler()

    protected var currentX: Int = 0
    protected var needRestore: Boolean = false

    private val appSettingsLauncher: ActivityResultLauncher<Intent> =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { _ ->
            // When user returns from app settings, check if location permissions are now granted
            // and attempt to center on location again if they are
            if (EasyPermissions.hasPermissions(this, *PermissionUtils.LOCATION_PERMISSIONS)) {
                centerOnCurrentLocation()
            }
        }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        savedInstanceState?.let {
            currentX = it.getInt(CURRENT_X, 0)
            needRestore = true
        }
        binding = ActivityPopularRouteDetailBinding.inflate(layoutInflater)
        setContentView(binding.root)
        setUpToolBar()
        createMapFragment()
        setupMapButtons()
        setupPopularRouteBottomSheet()
        initMapType()
        initMapListener()

        binding.shareWorkoutButton.setOnClickListenerThrottled {
            viewModel.shareRoute(this@BasePopularRouteDetailsActivity)
        }

        formatHelper = RouteValueFormatHelper(this, infoModelFormatter)

        lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.CREATED) {
                viewModel.routeDetailsViewData.collect { viewData ->
                    viewData?.let { onViewDataUpdated(it) }
                }
            }
        }

        viewModel.onRouteSaved.observeNotNull(this) {
            finish()
        }

        viewModel.climbSegmentData.observeK(this) { climbSegmentData ->
            climbSegmentData?.let {
                handleHighlightRouteByIndex(it.climbGuidance, it.segmentIndex, it.pointIndex)
            }
        }

        viewModel.onRouteFavoriteSaved.observeNotNull(this) {
            val isJumpLibrary =
                intent.getBooleanExtra(KEY_IS_JUMP_LIBRARY_AFTER_FAVORITE_SAVED, false)
            if (isJumpLibrary) {
                startActivity(
                    LibraryActivity.newStartIntent(
                        this,
                        LibraryTab.ROUTES,
                        topRouteAction = TopRouteAction.ADD_FAVORITE
                    )
                )
            }
        }
    }

    private fun setUpToolBar() {
        setSupportActionBar(binding.routeDetailToolbar)
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            title = ""
        }
        onBackPressedDispatcher.addCallback(this) {
            val isJumpLibrary =
                intent.getBooleanExtra(KEY_IS_JUMP_LIBRARY_AFTER_FAVORITE_SAVED, false)
            if (!isJumpLibrary) {
                setResult(Activity.RESULT_OK)
            }
            finish()
        }
    }

    private fun initMapType() {
        selectedHeatmapTypeLiveData.observeK(this) { heatmapType ->
            mapFragment?.getMapAsync { map ->
                heatmapOverlay?.remove()
                heatmapOverlay = null
                startingPointsOverlay?.remove()
                startingPointsOverlay = null
                if (heatmapType != null) {
                    heatmapOverlay = MapHelper.addHeatmapOverlay(map, heatmapType)
                    startingPointsOverlay = MapHelper.addStartingPointsOverlay(map, heatmapType)
                }
            }
        }

        combineLatest(
            selectedRoadSurfaceTypesLiveData,
            hideCyclingForbiddenRoadsLiveData
        )
            .debounce(SelectedRoadSurfaceTypesLiveData.DEBOUNCE_TIME_WITH_CYCLING_FORBIDDEN_ROADS)
            .observeK(this) {
                mapFragment?.getMapAsync { map ->
                    updateRoadSurface(map)
                }
            }

        viewModel.onTopRouteEditClicked.observeNotNull(this) { topRoute ->
            startActivity(
                BaseRoutePlannerActivity.newStartIntentEditTopRoute(
                    this,
                    topRoute.topRouteId,
                    topRoute.activityId
                )
            )
        }
    }

    private fun initMapListener() {
        mapFragment?.getMapAsync { map ->
            map.addOnMapMoveListener(object : SuuntoMap.OnMapMoveListener {
                override fun onMapMoveBegin() {
                    viewModel.setLocationEnabled(false)
                }

                override fun onMapMoveEnd() {
                    // do nothing
                }
            })
        }
    }

    private fun createMapFragment() {
        binding.popularRouteDetailMainContent.popularRouteDetailMainContentMapContainer.visibility =
            View.INVISIBLE
        val fm = supportFragmentManager
        mapFragment = fm.findFragmentByTag(MAP_FRAGMENT_TAG) as SuuntoSupportMapFragment?
        if (mapFragment == null) {
            val options = SuuntoMapOptions()
                .compassEnabled(true)
                .mapType(mapPresenter.currentMapType.name)
                .rotateGesturesEnabled(true)
                .scrollGesturesEnabled(true)
                .tiltGesturesEnabled(true)
                .zoomControlsEnabled(false)
                .zoomGesturesEnabled(true)
                .logoEnabled(resources.getBoolean(BaseR.bool.maps_logo_enabled))
                .attributionEnabled(resources.getBoolean(BaseR.bool.maps_logo_enabled))
                .showMyLocationMarker(false)
            mapFragment = SuuntoSupportMapFragment.newInstance(options)
            fm.commitNow {
                add(
                    R.id.popular_route_detail_main_content_map_container,
                    requireNotNull(mapFragment),
                    MAP_FRAGMENT_TAG
                )
            }
        }
    }

    private fun setupPopularRouteBottomSheet() {
        supportFragmentManager.commit {
            add(
                R.id.popularRouteDetailBottomSheet,
                PopularRouteBottomFragment.newInstance(),
                PopularRouteBottomFragment.TAG
            )
        }

        binding.bottomButtons.setContentWithM3Theme {
            val viewData by viewModel.routeDetailsViewData.collectAsState()
            viewData?.let {
                PopularRouteBottomButtons(
                    isPopularRouteSaved = viewModel.routeFavoriteSaved.collectAsState().value,
                    onEditButtonClick = it.onTopRouteEditClick,
                    onMyFavoriteClick = it.onFavoriteSavedClick,
                )
            }
        }
    }

    private fun setupMapButtons() {
        binding.popularRouteDetailMainContent.mapFloatingActionButtonLayout.setContentWithM3Theme {
            val viewState = viewModel.mapFloatingActionButtonState.collectAsState().value
            MapFloatingActionButtons(
                modifier = Modifier.padding(MaterialTheme.spacing.medium),
                showInfo = viewState.showInfo,
                showLocation = viewState.showLocation,
                showSearch = viewState.showSearch,
                showMapLayers = viewState.showMapLayers,
                show3D = viewState.show3D,
                enable3D = viewState.enable3D,
                locationEnabled = viewState.locationEnabled,
                onLocationButtonClick = {
                    centerOnCurrentLocation()
                },
                on3DButtonClick = {
                    viewModel.handle3dOptionToggled()
                },
                onInfoButtonClick = {
                },
                onMapLayersButtonClick = {
                    showMapSelectionDialogFragment()
                }
            )
        }
    }

    private fun updateRoadSurface(map: SuuntoMap) {
        val roadSurfaceTypes = selectedRoadSurfaceTypesLiveData.value ?: return
        val hideCyclingForbiddenRoads = hideCyclingForbiddenRoadsLiveData.value ?: return

        roadSurfaceOverlay?.remove()
        roadSurfaceOverlay = null

        if (roadSurfaceTypes.isNotEmpty()) {
            roadSurfaceOverlay = MapHelper.addRoadSurfaceOverlay(
                map,
                roadSurfaceTypes,
                hideCyclingForbiddenRoads
            )
        }
    }

    private fun centerOnCurrentLocation() {
        mapFragment?.getMapAsync { map ->
            if (!EasyPermissions.hasPermissions(this, *PermissionUtils.LOCATION_PERMISSIONS)) {
                requestLocationPermissions()
                return@getMapAsync
            }

            locationSource.getLastKnownLocation(
                onSuccess = { location ->
                    try {
                        val currentPosition = LatLng(location.latitude, location.longitude)
                        val currentZoomLevel = map.getCameraPosition()?.zoom ?: 0.0F

                        val targetZoom =
                            if (currentZoomLevel < STTConstants.MapPreferences.DEFAULT_ZOOM_LEVEL) {
                                STTConstants.MapPreferences.DEFAULT_ZOOM_LEVEL
                            } else {
                                currentZoomLevel
                            }

                        val cameraUpdate =
                            if (currentZoomLevel < STTConstants.MapPreferences.DEFAULT_ZOOM_LEVEL) {
                                newLatLngZoom(currentPosition, targetZoom)
                            } else {
                                newLatLng(currentPosition)
                            }

                        map.animateCamera(cameraUpdate)
                        Timber.d("Successfully centered map on current location: ${location.latitude}, ${location.longitude}")
                        viewModel.setLocationEnabled(true)
                    } catch (e: Exception) {
                        Timber.e(e, "Error animating camera to current location")
                        viewModel.setLocationEnabled(false)
                        showLocationError()
                    }
                },
                onFailure = { e ->
                    Timber.w(e, "Failed to get current location")
                    showLocationError()
                }
            )
        }
    }

    private fun showLocationError() {
        try {
            Snackbar.make(
                binding.root,
                R.string.no_current_location,
                Snackbar.LENGTH_LONG
            ).show()
        } catch (e: Exception) {
            Timber.e(e, "Error showing location error snackbar")
        }
    }

    private fun requestLocationPermissions() {
        if (EasyPermissions.somePermissionPermanentlyDenied(
                this,
                listOf(*PermissionUtils.LOCATION_PERMISSIONS)
            )
        ) {
            showLocationPermanentlyDeniedDialog()
        } else {
            PermissionUtils.requestPermissionsIfNeeded(
                this,
                PermissionUtils.LOCATION_PERMISSIONS,
                getString(com.stt.android.R.string.location_permission_rationale_for_location)
            )
        }
    }

    private fun showLocationPermanentlyDeniedDialog() {
        val sdf = SimpleDialogFragment.newInstance(
            getString(R.string.open_settings_to_enable_location),
            getString(R.string.location_permission_required_title),
            getString(R.string.open_settings_to_enable_location_button),
            getString(com.stt.android.R.string.cancel)
        )
        sdf.show(supportFragmentManager, LOCATION_PERMISSION_DIALOG_TAG)
    }

    override fun onDialogButtonPressed(tag: String?, which: Int) {
        if (tag != LOCATION_PERMISSION_DIALOG_TAG) return
        if (which == DialogInterface.BUTTON_POSITIVE) {
            try {
                val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
                    data = Uri.fromParts("package", packageName, null)
                }
                appSettingsLauncher.launch(intent)
            } catch (e: Exception) {
                Timber.w(e, "Error opening app settings")
                // Fallback to showing a snackbar if we can't open settings
                Snackbar.make(
                    binding.root,
                    R.string.open_settings_to_enable_location,
                    Snackbar.LENGTH_LONG
                ).show()
            }
        }
    }

    override fun onDialogDismissed(tag: String?) {
        // do nothing here.
    }

    private fun onViewDataUpdated(viewData: TopRouteDetailsViewData) {
        binding.loadingSpinner.isVisible = false

        showRouteAltitudeChart(
            chartData = viewData.routeAltitudeChartData,
            avgSpeed = viewData.route.averageSpeed,
            climbGuidance = viewData.climbGuidance
        )

        showRouteDetails(viewData.route, viewData.watchRouteListFull, viewData.climbGuidance)
    }

    private fun expandBottomSheetAndShowRoute(route: TopRoute, climbGuidance: ClimbGuidance) {
        val behavior = BottomSheetBehavior.from(binding.popularRouteDetailBottomSheet)
        if (behavior.state == BottomSheetBehavior.STATE_HALF_EXPANDED) {
            // Bottom sheet behavior and map padding have already been set up
            // Show route immediately
            setMapPaddingAndShowRoute(route, climbGuidance)
        }
        behavior.addBottomSheetCallback(object : BottomSheetBehavior.BottomSheetCallback() {
            override fun onStateChanged(bottomSheet: View, newState: Int) {
                if (newState == BottomSheetBehavior.STATE_HALF_EXPANDED) {
                    setMapPaddingAndShowRoute(route, climbGuidance)
                }
            }

            override fun onSlide(bottomSheet: View, slideOffset: Float) {
                setMapPadding()
            }
        })

        behavior.isFitToContents = false
        behavior.halfExpandedRatio = 0.52f
        behavior.peekHeight =
            resources.getDimensionPixelSize(R.dimen.fab_bottom_internal_padding_with_popular_routes)
        behavior.isHideable = false
        behavior.isDraggable = true
        behavior.expandedOffset = (resources.displayMetrics.heightPixels * 0.2f).toInt()
        behavior.state = BottomSheetBehavior.STATE_HALF_EXPANDED
    }

    private fun setMapPaddingAndShowRoute(route: TopRoute, climbGuidance: ClimbGuidance) {
        setMapPadding()
        showRouteOnMap(route, getRouteBounds(route.segments), climbGuidance)
    }

    protected abstract fun showRouteAltitudeChart(
        chartData: RouteAltitudeChartData,
        avgSpeed: Double,
        climbGuidance: ClimbGuidance
    )

    @Deprecated("Deprecated in Java")
    override fun onAttachFragment(fragment: Fragment) {
        super.onAttachFragment(fragment)
        if (fragment is SuuntoSupportMapFragment) {
            mapFragment = fragment.also {
                it.getMapAsync(this)
            }
        }
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        if (!optionsClickThrottler.checkAcceptEvent()) {
            return true
        }

        return when (item.itemId) {
            android.R.id.home -> {
                onBackPressedDispatcher.onBackPressed()
                return true
            }

            else -> {
                super.onOptionsItemSelected(item)
            }
        }
    }

    abstract fun showRequestPremiumDialog(@IdRes actionId: Int)

    private fun showMapSelectionDialogFragment() {
        mapFragment?.getMapAsync { map ->
            val mapCenter: LatLng? = map.getCameraPosition()?.target
            MapSelectionDialogFragment.newInstance(
                mapsProviderName = map.getProviderName(),
                showHeatmaps = true,
                showRoadSurface = true,
                showMyTracks = false,
                showMyPOIsGroup = false,
                mapCenter = mapCenter,
                analyticsSource = AnalyticsEvent.ROUTE_DETAILS_SCREEN
            )
                .show(supportFragmentManager, MapSelectionDialogFragment.FRAGMENT_TAG)
        }
    }

    override fun onMapReady(map: SuuntoMap) {
        map3dEnabledLiveData.observeNotNull(this) { enabled ->
            map.setMap3dModeEnabled(enabled)
        }
    }

    private val routeId: String
        get() = requireNotNull(intent.getStringExtra(STTConstants.ExtraKeys.TOP_ROUTE_ID))

    override fun onStart() {
        super.onStart()
        mapPresenter.takeView(this)
        val activityType = intent.getIntExtra(STTConstants.ExtraKeys.ROUTE_ACTIVITY_TYPE, -1)
        val isRouteSaved = if (intent.hasExtra(KEY_IS_FAVORITE_SAVED)) {
            intent.getBooleanExtra(KEY_IS_FAVORITE_SAVED, false)
        } else {
            null
        }
        viewModel.loadPopularRouteDetails(routeId, activityType, isRouteSaved)
    }

    override fun onStop() {
        super.onStop()
        mapPresenter.dropView()
    }

    protected open fun showRouteDetails(
        route: TopRoute?,
        watchRouteListFull: Boolean,
        climbGuidance: ClimbGuidance
    ) {
        if (route != null) {
            expandBottomSheetAndShowRoute(route, climbGuidance)
        }
    }

    private fun showRouteOnMap(
        route: TopRoute,
        bounds: LatLngBounds,
        climbGuidance: ClimbGuidance
    ) {
        val segments = route.segments
        if (isRouteDrawn || segments.isEmpty() || mapFragment == null) {
            return
        }
        // Climb guidance is not supported in ST
        if (FlavorUtils.isSportsTracker) {
            showLegacyRouteOnMap(route, bounds)
            return
        }

        lifecycleScope.launch {
            climbSegmentRenderer.render(climbGuidance.segments)

            val map = mapFragment?.getMap() ?: return@launch
            val mapView = mapFragment?.view
            if (mapView?.viewTreeObserver?.isAlive == true) {
                newLatLngBounds(bounds, map, mapView)
                binding.popularRouteDetailMainContent.popularRouteDetailMainContentMapContainer.isVisible =
                    true
            }

            val startPoint = segments.first().routePoints.firstOrNull()
            if (startPoint != null) {
                RouteMarkerHelper.drawStartPoint(
                    this@BasePopularRouteDetailsActivity,
                    map,
                    LatLng(startPoint.latitude, startPoint.longitude),
                    isSinglePointRoute = false,
                    isWorkout = false
                )
            }
            val endPoint = segments.last().routePoints.lastOrNull()
            if (endPoint != null && !endPoint.isSamePosition(startPoint)) {
                RouteMarkerHelper.drawEndPoint(
                    this@BasePopularRouteDetailsActivity,
                    map,
                    LatLng(endPoint.latitude, endPoint.longitude),
                    false
                )
            }

            RouteMarkerHelper.drawWaypoints(
                this@BasePopularRouteDetailsActivity,
                map,
                route.segments,
                route.turnWaypointsEnabled,
                waypointTools
            )

            isRouteDrawn = true
        }
    }

    private fun showLegacyRouteOnMap(route: TopRoute, bounds: LatLngBounds) {
        mapFragment?.getMapAsync { map: SuuntoMap ->
            map.batchUpdate {
                val mapView = mapFragment?.view
                if (mapView != null && mapView.viewTreeObserver.isAlive) {
                    newLatLngBounds(bounds, map, mapView)
                    binding.popularRouteDetailMainContent.popularRouteDetailMainContentMapContainer.visibility =
                        View.VISIBLE
                }
                val latLngs: MutableList<LatLng> = ArrayList()
                for (segment in route.segments) {
                    latLngs.addAll(routePointsToLatLngList(segment.routePoints))
                }
                if (latLngs.size > 1) {
                    RouteMarkerHelper.drawEndPoint(
                        this@BasePopularRouteDetailsActivity,
                        map,
                        latLngs.last(),
                        false
                    )
                }
                RouteMarkerHelper.drawWaypoints(this, map, route.segments, route.turnWaypointsEnabled, waypointTools)

                isRouteDrawn = true
            }
        }
    }

    private fun handleHighlightRouteByIndex(
        climbGuidance: ClimbGuidance,
        segmentIndex: Int,
        pointIndex: Int
    ) {
        lifecycleScope.launch {
            climbSegmentRenderer.render(climbGuidance.segments, segmentIndex, pointIndex)
        }
    }

    private fun newLatLngBounds(
        bounds: LatLngBounds,
        map: SuuntoMap,
        mapView: View
    ) {
        val cameraUpdate = newLatLngBounds(
            bounds,
            resources.getDimensionPixelSize(BaseR.dimen.size_spacing_xxlarge)
        )
        if (mapView.width > 0 && mapView.height > 0) {
            map.moveCamera(cameraUpdate)
        } else {
            mapView.viewTreeObserver.addOnGlobalLayoutListener(
                object : OnGlobalLayoutListener {
                    override fun onGlobalLayout() {
                        mapView.viewTreeObserver.removeOnGlobalLayoutListener(this)
                        map.moveCamera(cameraUpdate)
                    }
                }
            )
        }
    }

    fun setMapPadding() {
        mapPresenter.resetPadding()
        var bottomHeight = binding.root.bottom - binding.popularRouteDetailBottomSheet.top
        mapPresenter.resetPadding()
        if (binding.credit.isVisible) {
            bottomHeight += binding.credit.height
        }
        mapPresenter.addPadding(0, 0, 0, bottomHeight)
    }

    override fun setMapType(type: MapType) {
        mapFragment?.getMapAsync { map ->
            map.setMapType(type.name)
        }
    }

    override fun setMapCreditLink(credit: String) {
        with(binding.credit) {
            movementMethod = LinkMovementMethod.getInstance()
            text = HtmlCompat.fromHtml(credit, HtmlCompat.FROM_HTML_MODE_LEGACY)
            visibility = View.VISIBLE
            setMapPadding()
        }
    }

    override fun hideMapCreditLink() {
        binding.credit.visibility = View.GONE
        setMapPadding()
    }

    override fun setPadding(left: Int, top: Int, right: Int, bottom: Int) {
        mapFragment?.getMapAsync { map ->
            map.setPadding(left, top, right, bottom)
        }
    }

    override fun hasMapSelectionControls(): Boolean {
        return false
    }

    override fun heatmapsEnabled(): Boolean {
        return false
    }

    override fun myTracksEnabled(): Boolean = false

    override fun roadSurfaceEnabled(): Boolean {
        return false
    }

    override fun removeHeatmapOverlay() {
        // Not supported
    }

    override fun drawMyTracks(
        myTracks: List<RouteAndActivityType>,
        myTrackMarkers: List<LocationWithActivityType>,
    ) {
        // Not supported
    }

    override fun removeMyTracks() {
        // Not supported
    }

    override fun removeRoadSurfaceOverlay() {
        // Not supported
    }

    override fun removeStartingPointsOverlay() {
        // Not supported
    }

    override fun addHeatmapOverlay(options: SuuntoTileOverlayOptions) {
        // Not supported
    }

    override fun addRoadSurfaceOverlay(options: List<SuuntoTileOverlayOptions>) {
        // Not supported
    }

    override fun addStartingPointsOverlay(options: SuuntoTileOverlayOptions) {
        // Not supported
    }

    override fun onSaveInstanceState(outState: Bundle) {
        outState.putInt(CURRENT_X, currentX)
        super.onSaveInstanceState(outState)
    }

    override fun onDestroy() {
        mapFragment?.let {
            if (isFinishing) {
                try {
                    supportFragmentManager.commit(allowStateLoss = false) {
                        remove(it)
                    }
                } catch (_: IllegalStateException) {
                }
            }
        }
        lifecycleScope.cancel()
        super.onDestroy()
    }

    companion object {
        private const val CURRENT_X = "CURRENT_X"
        private const val MAP_FRAGMENT_TAG = "TopRouteDetailsMapFragment"
        private const val KEY_IS_FAVORITE_SAVED = "com.stt.android.KEY_IS_FAVORITE_SAVED"
        private const val KEY_IS_JUMP_LIBRARY_AFTER_FAVORITE_SAVED = "com.stt.android.KEY_IS_JUMP_LIBRARY_AFTER_FAVORITE_SAVED"
        const val KEY_DISTANCE_TO_CURRENT_POSITION =
            "com.stt.android.KEY_DISTANCE_TO_CURRENT_POSITION"
        private const val LOCATION_PERMISSION_DIALOG_TAG = "LocationPermissionDialog"

        fun newStartIntentFromLibrary(
            context: Context,
            routeId: String,
            activityType: Int,
            distanceToCurrentPosition: Double,
            isFavoriteSaved: Boolean
        ): Intent {
            require(routeId.isNotEmpty())
            return Intent(context, PopularRouteDetailsActivity::class.java).apply {
                putExtra(STTConstants.ExtraKeys.TOP_ROUTE_ID, routeId)
                putExtra(STTConstants.ExtraKeys.ROUTE_ACTIVITY_TYPE, activityType)
                putExtra(KEY_DISTANCE_TO_CURRENT_POSITION, distanceToCurrentPosition)
                putExtra(KEY_IS_FAVORITE_SAVED, isFavoriteSaved)
            }
        }

        fun newStartIntentFromMap(
            context: Context,
            routeId: String,
            activityType: Int,
            distanceToCurrentPosition: Double
        ): Intent {
            require(routeId.isNotEmpty())
            return Intent(context, PopularRouteDetailsActivity::class.java).apply {
                putExtra(STTConstants.ExtraKeys.TOP_ROUTE_ID, routeId)
                putExtra(STTConstants.ExtraKeys.ROUTE_ACTIVITY_TYPE, activityType)
                putExtra(KEY_DISTANCE_TO_CURRENT_POSITION, distanceToCurrentPosition)
                putExtra(KEY_IS_JUMP_LIBRARY_AFTER_FAVORITE_SAVED, true)
            }
        }
    }
}
