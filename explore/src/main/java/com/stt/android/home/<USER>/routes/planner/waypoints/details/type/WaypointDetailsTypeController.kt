package com.stt.android.home.explore.routes.planner.waypoints.details.type

import android.content.Context
import com.stt.android.common.viewstate.ViewState
import com.stt.android.common.viewstate.ViewStateEpoxyController
import com.stt.android.di.FragmentContext
import com.stt.android.home.explore.waypointTypeHeader
import com.stt.android.home.explore.waypointTypeItem
import javax.inject.Inject

class WaypointDetailsTypeController
@Inject constructor(
    @FragmentContext private val fragmentContext: Context
) : ViewStateEpoxyController<WaypointDetailsTypeContainer?>() {
    override fun buildModels(viewState: ViewState<WaypointDetailsTypeContainer?>) {
        viewState.data?.run {
            waypointTypeHeader {
                id("header")
                titleStringRes(titleStringRes)
            }

            // The list should have WAYPOINT type first and the rest sorted alphabetically.
            val pinned = types
                .filter { it.pinnedAtTop }

            val sorted = types
                .filter { !it.pinnedAtTop }
                .sortedBy { fragmentContext.resources.getString(it.nameResId) }

            for (type in pinned + sorted) {
                waypointTypeItem {
                    id(type.typeId)
                    item(type)
                    onClickListener { model, _, _, _ ->
                        viewState.data?.run {
                            onClicked(model.item())
                        }
                    }
                }
            }
        }
    }
}
