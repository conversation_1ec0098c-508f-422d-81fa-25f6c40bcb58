package com.stt.android.home.explore.routes.ui

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.databinding.DataBindingUtil
import com.github.mikephil.charting.data.Entry
import com.soy.algorithms.climbanalysis.entities.ClimbGuidance
import com.soy.algorithms.climbanalysis.entities.ClimbSegment
import com.stt.android.home.explore.R
import com.stt.android.home.explore.databinding.ViewClimbGuidanceAwareRouteAltitudeChartWithAxisBinding
import com.stt.android.home.explore.routes.RouteAltitudeChartData
import com.stt.android.ui.utils.TextFormatter
import com.stt.android.core.R as CR

class ClimbGuidanceAwareRouteAltitudeChartWithAxis @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyle: Int = 0
) : ConstraintLayout(context, attrs, defStyle) {
    private val binding: ViewClimbGuidanceAwareRouteAltitudeChartWithAxisBinding =
        DataBindingUtil.inflate(
            LayoutInflater.from(context),
            R.layout.view_climb_guidance_aware_route_altitude_chart_with_axis,
            this,
            true
        )

    init {
        with(binding.routeAltitudeAxis) {
            setColor(ContextCompat.getColor(context, CR.color.near_black))
            markerLineCount = 2
            markerLineWidthRatio = 1.0f
            setLineWidth(resources.getDimension(R.dimen.altitude_line_stroke_width))
        }
    }

    fun moveHighlightToEnd(climbGuidance: ClimbGuidance) {
        binding.routeAltitudeChart.moveHighlightMoveEnd(climbGuidance)
    }

    fun moveHighlightToX(x: Float, climbGuidance: ClimbGuidance) {
        binding.routeAltitudeChart.moveHighlightToX(x, climbGuidance)
    }

    fun getCurrentClimbSegment(): ClimbSegment? = binding.routeAltitudeChart.currentClimbSegment

    fun isDraggedToEnd(): Boolean = binding.routeAltitudeChart.isDraggedToEnd()

    fun getScrubbingCount(): Int {
        return binding.routeAltitudeChart.getScrubbingCount()
    }

    /**
     * Update altitude chart data points, min/max labels and "No altitude information" label
     * visibility. The chart view is invalidated when data is updated to make sure the view is
     * refreshed immediately.
     */
    fun updateRouteAltitudeChart(
        data: RouteAltitudeChartData,
        climbGuidance: ClimbGuidance,
        onValueHighlighted: ((x: Int) -> Unit)?,
    ) = with(binding) {
        if (data.entries.none { it.y != 0.0f }) {
            noAltitudeInformationText.isVisible = true

            routeAltitudeChart.isVisible = false
            routeAltitudeChart.drawRouteAltitudeChart(
                // always show a line at 0 even when there are no data points
                data.copy(
                    entries = listOf(
                        Entry(0.0f, 0f),
                        Entry(0.0f, (data.entries.size - 1).toFloat())
                    )
                ),
                climbGuidance
            )
        } else {
            noAltitudeInformationText.isVisible = false

            routeAltitudeChart.isVisible = true
            routeAltitudeChart.drawRouteAltitudeChart(data, climbGuidance)
        }
        routeAltitudeChart.onValueHighlighted = onValueHighlighted

        val unit = resources.getString(data.measurementUnit.altitudeUnit)
        minText = "${TextFormatter.formatAltitude(data.minValue.toDouble())} $unit"
        maxText = "${TextFormatter.formatAltitude(data.maxValue.toDouble())} $unit"
    }
}
