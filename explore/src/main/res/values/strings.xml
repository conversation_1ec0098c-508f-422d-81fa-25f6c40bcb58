<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="no_current_location">Current location not available yet</string>
    <string name="open_settings_to_enable_location">This feature requires access to your location. Please turn on location access in settings.</string>
    <string name="location_permission_required_title">Location permission required</string>
    <string name="open_settings_to_enable_location_button">Open settings</string>
    <string name="route_follow">FOLLOW ROUTE</string>
    <string name="route_suitable_activities">Suitable activity types:</string>
    <string name="route_tip">Tap the map to set a starting point for your route</string>
    <string name="route_name_hint">Route name</string>
    <string name="profile_image">Profile image</string>
    <string name="latitude">Latitude</string>
    <string name="longitude">Longitude</string>
    <string name="library">Library</string>

    <!-- Waypoints -->
    <string name="waypoint_details_name_label">Waypoint name</string>
    <string name="waypoint_details_description_label">Add some description</string>
    <string name="waypoint_details_type_label">Type</string>
    <string name="waypoint_details_from_point_a">From point A: %s</string>
    <string name="waypoint_types_header">WAYPOINT TYPES</string>
    <string name="waypoint_guidance">Waypoint guidance</string>
    <string name="waypoint_guidance_title">Tap and hold route to add waypoint</string>
    <string name="waypoint_guidance_message">Waypoints help you navigate and you can use them to highlight useful locations like peaks, camps or the next water point.</string>
    <string name="waypoint_guidance_button">GOT IT</string>
    <string name="waypoint_max_count_reached_title">Waypoint limit reached</string>
    <string name="waypoint_max_count_reached_message">You’ve reached the maximum number of waypoints for one route. Remove waypoints to add new ones.</string>
    <string name="turn_by_turn_max_count_reached_title">Turn-by-turn waypoints</string>
    <string name="turn_by_turn_max_count_reached_message">Due to the length of the route, turn-by-turn waypoints will no longer be added automatically. You can still add waypoints manually.</string>
    <string name="turn_by_turn_limited_support_title">Turn-by-turn waypoints</string>
    <string name="turn_by_turn_limited_support_message">This feature is limited to watches that support waypoint names.</string>
    <string name="turn_by_turn_guidance">Turn-by-turn</string>
    <string name="tap_the_route_add_waypoint">Tap the route to add waypoint</string>

    <!-- Waypoint types -->
    <string name="waypoint_type_building">Building</string>
    <string name="waypoint_type_home">Home</string>
    <string name="waypoint_type_car">Car</string>
    <string name="waypoint_type_parking">Parking</string>
    <string name="waypoint_type_camp">Camp</string>
    <string name="waypoint_type_camping">Camping site</string>
    <string name="waypoint_type_food">Food</string>
    <string name="waypoint_type_restaurant">Restaurant</string>
    <string name="waypoint_type_cafe">Cafe</string>
    <string name="waypoint_type_lodging">Lodging</string>
    <string name="waypoint_type_hostel">Hostel</string>
    <string name="waypoint_type_hotel">Hotel</string>
    <string name="waypoint_type_water">Water</string>
    <string name="waypoint_type_river">River</string>
    <string name="waypoint_type_lake">Lake</string>
    <string name="waypoint_type_coast">Coast</string>
    <string name="waypoint_type_mountain">Mountain</string>
    <string name="waypoint_type_hill">Hill</string>
    <string name="waypoint_type_valley">Valley</string>
    <string name="waypoint_type_cliff">Cliff</string>
    <string name="waypoint_type_forest">Forest</string>
    <string name="waypoint_type_crossroads">Crossroads</string>
    <string name="waypoint_type_sight">Sight</string>
    <string name="waypoint_type_begin">Begin</string>
    <string name="waypoint_type_end">End</string>
    <string name="waypoint_type_geocache">Geocache</string>
    <string name="waypoint_type_waypoint">Waypoint</string>
    <string name="waypoint_type_road">Road</string>
    <string name="waypoint_type_trail">Trail</string>
    <string name="waypoint_type_rock">Rock</string>
    <string name="waypoint_type_meadow">Meadow</string>
    <string name="waypoint_type_cave">Cave</string>
    <string name="waypoint_type_emergency">Emergency</string>
    <string name="waypoint_type_information">Information</string>
    <string name="waypoint_type_peak">Peak</string>
    <string name="waypoint_type_waterfall">Waterfall</string>
    <!-- End Waypoint types -->

    <!-- POI types -->
    <string name="poi_types_header">Place (POI) types</string>
    <string name="poi_type_poi">POI</string>
    <string name="poi_type_fishingspot">Fishing spot</string>
    <string name="poi_type_stand">Stand</string>
    <string name="poi_type_prints">Prints</string>
    <string name="poi_type_trailcam">Trail cam</string>
    <string name="poi_type_bedding">Bedding</string>
    <string name="poi_type_scrape">Scrape</string>
    <string name="poi_type_rub">Rub</string>
    <string name="poi_type_biggame">Big game</string>
    <string name="poi_type_smallgame">Small game</string>
    <string name="poi_type_bird">Bird</string>
    <string name="poi_type_shot">Shot</string>
    <string name="poi_type_fish">Fish</string>
    <string name="poi_type_big_fish">Big fish</string>
    <string name="poi_type_coral_reef">Coral reef</string>
    <string name="poi_type_beach">Beach</string>
    <string name="poi_type_marine_mammals">Marine mammals</string>
    <string name="poi_type_kelp_forest">Kelp forest</string>
    <string name="poi_type_wreck">Wreck</string>
    <string name="poi_type_marine_reserve">Marine reserve</string>
    <string name="poi_type_avalanche">Avalanche</string>
    <string name="poi_type_danger">Danger</string>
    <string name="poi_type_aidstation">Aid station</string>
    <string name="poi_type_waterpoint">Water point</string>
    <string name="poi_type_mushrooms">Mushrooms</string>
    <string name="poi_type_campfire">Campfire</string>
    <!-- end POI types -->

    <!-- Turn-by-turn waypoint types -->
    <string name="waypoint_type_left_turn">Left</string>
    <string name="waypoint_type_right_turn">Right</string>
    <string name="waypoint_type_sharp_left_turn">Sharp left</string>
    <string name="waypoint_type_sharp_right_turn">Sharp right</string>
    <string name="waypoint_type_slight_left_turn">Slight left</string>
    <string name="waypoint_type_slight_right_turn">Slight right</string>
    <string name="waypoint_type_left_at_fork">Left at fork</string>
    <string name="waypoint_type_right_at_fork">Right at fork</string>
    <string name="waypoint_type_u_turn">U-turn</string>
    <string name="waypoint_type_continue">Continue</string>
    <string name="waypoint_type_roundabout_exit_1">1st exit</string>
    <string name="waypoint_type_roundabout_exit_2">2nd exit</string>
    <string name="waypoint_type_roundabout_exit_3">3rd exit</string>
    <string name="waypoint_type_roundabout_exit_4">4th exit</string>
    <string name="waypoint_type_roundabout_exit_5">5th exit</string>
    <string name="waypoint_type_roundabout_exit_n">Roundabout</string>
    <!-- end Turn-by-turn waypoint types -->

    <!-- Add to route -->
    <string name="add_to_route_title">Add to route</string>
    <string name="add_to_route_type_waypoint">Waypoint</string>
    <string name="add_to_route_type_waypoint_desc">Highlight useful locations on route</string>
    <string name="add_to_route_type_planning_point">Planning point</string>
    <string name="add_to_route_type_planning_point_desc">Drag these to edit the route</string>
    <!-- end Add to route -->

    <!-- POI details -->
    <string name="save_poi">Save POI</string>
    <string name="add_poi_to_watch">Use in watch</string>
    <string name="poi_name_editor_title">Name</string>
    <string name="error_could_not_save_poi">Couldn’t save POI, please try again.</string>
    <string name="error_could_not_edit_poi">Couldn’t save changes, please try again.</string>
    <string name="poi_sync_in_progress">Syncing…</string>
    <string name="poi_limit_on_watch_exceeded_title">Watch has maximum number of POIs (%1$d)</string>
    <string name="poi_limit_on_watch_exceeded_body">Before you can add this to watch, please remove one. Open POI list and remove the POIs you don’t need anymore.</string>
    <string name="poi_limit_exceeded_during_sync_title">Some POIs removed from watch during sync</string>
    <string name="poi_limit_exceeded_during_sync_body">Your POI list is full. During sync some of the oldest POIs were removed from watch. You can manage POI list in map library.</string>
    <string name="poi_details_menu_edit_name">Edit name</string>
    <string name="poi_details_menu_edit_coordinates">Edit coordinates</string>
    <string name="poi_details_menu_edit_altitude">Edit altitude</string>
    <!-- Shown below both latitude and longitude inputs with an example value. "E.g. 24.972285" -->
    <string name="poi_details_edit_coordinates_example">E.g. %1$s</string>
    <!-- end POI details -->

    <!-- POI list -->
    <string name="poi_list_empty_state">Add a POI by tapping on map and then save POI</string>
    <string name="poi_list_go_to_map">Go to map</string>
    <string name="poi_created_on_date">Added: %1$s</string>
    <string name="poi_distance_to">%1$s away</string>
    <!-- End POI list -->

    <!-- Top routes -->
    <string name="top_routes_title">%s ROUTES</string>
    <string name="top_route_activity_icon">Activity icon</string>
    <string name="top_routes_search_here">Search here</string>
    <string name="popular_routes">Popular routes</string>
    <string name="no_popular_routes">Try a broader search by zooming out or moving the map.</string>
    <string name="zoom_level_low_for_popular_routes">To explore routes you need to zoom closer.</string>
    <string name="no_popular_routes_card_label">No popular routes nearby</string>
    <string name="no_popular_routes_card_msg">Try a broader search by zooming out or moving the map.</string>
    <string name="zoom_level_low_card_label">Zoom closer</string>
    <string name="zoom_level_low_card_msg">To explore routes you need to zoom closer.</string>
    <string name="nearby_title">Nearby</string>
    <string name="public_exercises">Public exercises</string>
    <!-- End Top routes -->

    <!-- Map disclaimer -->
    <string name="map_disclaimer_title">Maps and navigation</string>
    <string name="map_disclaimer">Suggested routes are developed for your convenience only, based upon aggregated user data and should only be used as general guidance.\n\nPLEASE NOTE that Suunto has not in any way checked or verified the routes shown, such as their accessibility, safety or suitability for exercise. Routes may change due to many reasons including natural changes in the landscape or construction. Always use good judgment and a backup map and compass when navigating new areas or when in difficult terrain or conditions.\n\nDo not follow routes if they seem to lead you to unsafe situations. User accepts full responsibility for all use of any route / navigational information and for any consequences thereof. Be careful and stay safe!</string>
    <!-- End Map disclaimer -->

    <!-- Popular starting points -->
    <string name="popular_starting_point_label">Popular starting point</string>
    <!-- End Popular starting points -->

    <!-- Map instructions -->
    <string name="create_and_edit_route_title">Create and edit route</string>
    <string name="create_and_edit_route">Tap the map to plan a route. Drag the planning points to change the route.</string>
    <string name="add_planning_point_title">Add a planning point</string>
    <string name="add_planning_point">Press and hold a place on the map to add a planning point. Drag the planning point on the map to change the route.</string>
    <string name="add_waypoint_title">Add a waypoint</string>
    <string name="add_waypoint">Press and hold a place on the route to add a waypoint. Waypoints help you navigate by highlighting useful locations like peaks, camps or the next water point. Your watch notifies you when you’re approaching a waypoint.</string>
    <string name="enter_3d_map_title">Enter 3D map</string>
    <string name="enter_3d_map">Tilt the map with two fingers to go to the 3D map. To go back to the 2D map, tilt it back.</string>
    <string name="maps_and_layers_title">Maps and layers</string>
    <string name="maps_and_layers">Tap the buttons on top of the screen to switch between map layers. Different maps and layers serve different purposes.</string>
    <!-- Map instructions -->

    <string name="buy_premium_popup_create_new_route_description">Create, import, and export routes for your training. You can share your routes if you and your buddies want to train on the same trail.</string>
    <string name="buy_premium_popup_search_places">Find your way to your next location or create a route beforehand for your next trip.</string>
    <string name="buy_premium_to_follow_routes">Buy Premium to follow routes</string>
    <string name="buy_premium_to_create_routes">Upgrade to Sports Tracker Premium to create and import routes</string>
    <string name="buy_premium_to_see_popular_routes">Get Premium to see popular routes in your area.</string>

    <!-- route/poi search and filter in library -->
    <string name="enter_route_name_or_activity_type">Enter route name or activity type</string>
    <string name="enter_poi_name_or_activity_type">Enter POI name or type</string>
    <string name="sorting_default">Synced first</string>
    <string name="sorting_nearest_to_me">Nearest first</string>
    <string name="sorting_by_creation_time">Latest first</string>
    <string name="sorting_location_permissions_message">To filter the routes or points of interest closest to me, please turn on location access in settings.</string>
    <string name="any_distance">Any distance</string>
    <string name="distance_filter">FILTER</string>
    <string name="no_results_in_distance_range">There are no routes in this distance range</string>
    <string name="filter_with_no_result">There are no routes that match your filter.</string>
    <string name="mine_route_empty_tip">Create or browse routes on map. You can also import GPX or KML route by tapping “Route import” button above</string>
    <string name="popular_route_empty_tip">Save popular routes on map</string>
    <!-- End route/poi search and filter in library -->

    <!-- route import -->
    <string name="route_import_ignored_waypoints">%1$d waypoints over %2$d m (%3$d ft) from the route were excluded. Consider adding them as POIs on the map for detailed guidance.</string>
    <!-- End route import -->

    <!--Route details-->
    <string name="delete_route_menu_txt">Delete route</string>
    <string name="edit_route_menu_txt">Edit route</string>
    <string name="copy_route_menu_txt">Copy route</string>
    <string name="map_style_menu_txt">Map style</string>
    <!--End route details-->

    <string name="route_sections">Route sections</string>
    <string name="route_climb_guidance_climb">Climb</string>
    <string name="route_climb_guidance_descent">Descent</string>
    <string name="route_climb_guidance_uphill">Uphill</string>
    <string name="route_climb_guidance_downhill">Downhill</string>
    <string name="route_climb_guidance_flat">Flat</string>
    <string name="route_away_from_you">%1$s away from you</string>
    <string name="route_navigation_name">Quick Navigate Route</string>
    <string name="route_sport_type">Sport type</string>

    <!--MapSearch-->
    <string name="map_search_hilt">Search location</string>
    <string name="map_search_history_recent">Recent</string>
    <string name="map_search_delete_dialog_content">Are you sure you want to clear your search history?</string>

    <string name="map_popular_route">%d popular routes</string>

    <string name="library_my_routes_label">My routes</string>
    <string name="library_created_routes_label">Created routes</string>
    <string name="library_favorite_routes_label">Favorite routes</string>
    <string name="top_route_popular_routes">Popular routes</string>
    <string name="top_route_filter_default">Default</string>
    <string name="top_route_filter_activity_all">All</string>
    <string name="top_route_filter_sort">Sort</string>
    <string name="top_route_filter_sort_use_in_watch">Use in watch</string>
    <string name="top_route_filter_distance">Distance</string>
    <string name="top_route_filter_activity_type">Suitable activity types</string>
    <string name="top_route_filter_sports_type">Sports type</string>
    <string name="top_route_filter_sort_most_popular">Most popular</string>
    <string name="top_route_filter_sort_nearest">Nearest</string>
    <string name="top_route_filter_empty_content">No popular routes nearby. Try a broader search by zooming out or moving the map.</string>
    <string name="top_route_away_from_your">%s away from you</string>
    <string name="top_route_add_favorite_successful">Favorited successfully</string>
    <string name="top_route_saved_successful">Saved successfully</string>

    <string name="waypoint_details_start_point">Starting point</string>
    <string name="waypoint_details_end_point">Ending point</string>
</resources>
