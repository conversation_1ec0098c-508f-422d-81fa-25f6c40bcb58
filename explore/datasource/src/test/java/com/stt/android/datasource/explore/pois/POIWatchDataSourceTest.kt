package com.stt.android.datasource.explore.pois

import com.google.common.truth.Truth.assertThat
import com.suunto.connectivity.poi.POIWatchAPI
import kotlinx.coroutines.runBlocking
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.Mockito.`when`
import org.mockito.junit.MockitoJUnitRunner
import org.mockito.kotlin.never
import org.mockito.kotlin.verify
import kotlin.test.assertTrue

@RunWith(MockitoJUnitRunner::class)
class POIWatchDataSourceTest {

    @Mock
    lateinit var poiWatchAPI: POIWatchAPI

    lateinit var poiWatchDataSource: POIWatchDataSource

    lateinit var poiWatchCache: POIWatchCache

    @Before
    fun setup() {
        poiWatchCache = POIWatchCache()
        poiWatchDataSource = POIWatchDataSource(poiWatchAPI, poiWatchCache)
    }

    @Test
    fun `fetchCreationDateList() should build index cache`() {
        runBlocking {
            val testIndexes = listOf(1L, 2L, 3L)
            `when`(poiWatchAPI.getCreationIndexedList("123")).thenReturn(testIndexes)
            assertThat(poiWatchCache.isInvalidated("123")).isTrue()
            assertThat(poiWatchCache.getCachedIndexes()).isEmpty()
            poiWatchDataSource.fetchCreationDateIndexedList("123")
            assertThat(poiWatchCache.isInvalidated("123")).isFalse()
            assertThat(poiWatchCache.getCachedIndexes()).containsExactlyElementsIn(testIndexes)
        }
    }

    @Test
    fun `fetchPOI() should build index cache before fetching`() {
        runBlocking {
            val testIndexes = listOf(1L, 2L, 3L)
            val testPOI = testPOI(2L)
            `when`(poiWatchAPI.getCreationIndexedList("123")).thenReturn(testIndexes)
            `when`(poiWatchAPI.getPOIAt("123", 1)).thenReturn(testPOI.toWatchData())
            assertThat(poiWatchCache.isInvalidated("123")).isTrue()
            assertThat(poiWatchCache.getCachedIndexes()).isEmpty()
            val poi = poiWatchDataSource.fetchPOI("123", 2L)
            assertThat(poiWatchCache.isInvalidated("123")).isFalse()
            assertThat(poiWatchCache.getCachedIndexes()).containsExactlyElementsIn(testIndexes)
            assertThat(poi).isEqualTo(testPOI.copy(watchEnabled = true, key = null))
        }
    }

    @Test
    fun `addPOI() should invalidate index cache`() {
        runBlocking {
            val testIndexes = listOf(1L, 2L, 3L)
            val testPOI = testPOI(2L)
            `when`(poiWatchAPI.getCreationIndexedList("123")).thenReturn(testIndexes)
            poiWatchDataSource.fetchCreationDateIndexedList("123")
            assertThat(poiWatchCache.isInvalidated("123")).isFalse()
            poiWatchDataSource.addPOI("123", testPOI)
            assertThat(poiWatchCache.isInvalidated("123")).isTrue()
        }
    }

    @Test
    fun `deletePOI() should delete from highest to lowest index`() {
        runBlocking {
            val testIndexes = listOf(1L, 2L, 3L)
            val poisToDelete = listOf(2L, 3L, 4L).map { testPOI(it) }
            poiWatchCache.setCache("123", testIndexes)
            `when`(poiWatchAPI.removePOIAt("123", 1)).thenReturn(true)
            `when`(poiWatchAPI.removePOIAt("123", 2)).thenReturn(true)
            val results = poiWatchDataSource.deletePOIs("123", poisToDelete)
            assertThat(poiWatchCache.isInvalidated("123")).isTrue()
            assertTrue(results.getValue(2L).isSuccess)
            assertTrue(results.getValue(2L).isSuccess)
            assertTrue(results.getValue(3L).isSuccess)
            assertTrue(results.getValue(4L).isFailure)
            val orderVerifier = Mockito.inOrder(poiWatchAPI)
            orderVerifier.verify(poiWatchAPI).removePOIAt("123", 2)
            orderVerifier.verify(poiWatchAPI).removePOIAt("123", 1)
            verify(poiWatchAPI, never()).removePOIAt("123", -1)
        }
    }
}
