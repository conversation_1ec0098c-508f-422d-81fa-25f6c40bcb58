package com.stt.android.datasource.explore.pois

import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.data.pois.POIRemoteSyncTrigger
import com.stt.android.data.source.local.pois.LocalPOI
import com.stt.android.data.source.local.pois.LocalPOISyncState
import com.stt.android.data.source.local.pois.POIDao
import com.stt.android.data.source.local.pois.POISyncLogEventDao
import com.stt.android.di.IsSuuntoFlavor
import com.stt.android.domain.explore.pois.POI
import com.stt.android.exceptions.explore.POISyncAlreadyRunningException
import com.suunto.connectivity.poi.POISyncLogicResult
import timber.log.Timber
import javax.inject.Inject

class POIRemoteSyncLogic(
    override val poiDao: POIDao,
    override val poiSyncLogEventDao: POISyncLogEventDao,
    override val watchEnabledPOIsMaxCount: Int,
    private val poiRemoteDataSource: POIRemoteDataSource,
    private val supportsWatch: Boolean
) : POISyncLogic, POIRemoteSyncTrigger {
    @Inject
    constructor(
        poiDao: POIDao,
        poiSyncLogEventDao: POISyncLogEventDao,
        poiRemoteDataSource: POIRemoteDataSource,
        @IsSuuntoFlavor supportsWatch: Boolean,
    ) : this(
        poiDao,
        poiSyncLogEventDao,
        POIWatchDataSource.WATCH_ENABLED_MAX_COUNT,
        poiRemoteDataSource,
        supportsWatch,
    )

    override suspend fun syncPOIsWithBackend(): POISyncLogicResult {
        // 1. check if POI sync is running with watch and abort
        try {
            poiSyncLogEventDao.ensureSyncStart(isWatchSync = false)
        } catch (e: POISyncAlreadyRunningException) {
            Timber.d("Sync already ongoing. Returning failure without updating sync log")
            return POISyncLogicResult.Failure("Sync already ongoing")
        }

        return try {
            val errorsLogs = mutableListOf<String>()
            var hasNewData = false
            var watchEnabledLimitExceeded = false
            runSuspendCatching {
                // 2. we check which POIs should be deleted on backend first
                hasNewData = deletePOIsInBackend(errorsLogs) || hasNewData

                // 3. Clear-up deleted POIs from local DB not pending any sync
                clearupLocalDeletedPOIs()

                // 4. fetch all POIs from backend and save new POIs
                // note: we propagate remotePOIs downwards even after doing modifications to backend
                // so the list might not contain the POIs how they are in the backend exactly, but it
                // will not be a problem for the sync, as long as the operation sequence
                // remains: pull -> edit -> push
                val remotePOIs = poiRemoteDataSource.fetchAll()
                hasNewData = pullNewPOIsFromBackend(remotePOIs) || hasNewData

                // 5. Edit existing POIs with last `modified` data conflict resolution
                hasNewData = syncPOIModificationsWithBackend(remotePOIs, errorsLogs) || hasNewData

                // 6. Save new local POIs to backend
                hasNewData = pushNewPOIsToBackend(remotePOIs, errorsLogs) || hasNewData

                // 7. Handle too many POIs with watch enabled
                watchEnabledLimitExceeded = handleTooManyPOIsWithWatchEnabled(errorsLogs)
                hasNewData = hasNewData || watchEnabledLimitExceeded
            }.onFailure { e ->
                val message = "Failure during syncPOIsWithBackend()"
                Timber.w(e, message)
                errorsLogs.add("$message. ${e::class.java.name}: ${e.message}")
            }

            processSyncResult(
                hasNewData = hasNewData,
                errorsLogs = errorsLogs,
                isWatchSync = false,
                didExceedWatchEnabledPOILimit = watchEnabledLimitExceeded,
            )
        } finally {
            // Make sure we mark sync as "stopped" even if the task is cancelled.
            stopSync()
        }
    }

    override suspend fun isPOISyncWithWatchNeeded(): Boolean =
        poiDao.fetchAllPendingWatchSync().isNotEmpty()

    private suspend fun pushNewPOIsToBackend(
        remotePOIs: List<POI>,
        errorsLogs: MutableList<String>
    ): Boolean {
        val remotePOIsById = remotePOIs.associateBy { it.creation }
        val localPOIsById = poiDao.fetchAllVisible()
            .filter { !remotePOIsById.containsKey(it.creation) }
            .associateBy { it.creation }
        if (localPOIsById.isEmpty()) return false
        val pushResults = poiRemoteDataSource.upsertAll(localPOIsById.values.map { it.toDomain() })
        for (pushResult in pushResults) {
            val localPOI = pushResult.poi?.creation?.let { localPOIsById[it] } ?: continue
            if (pushResult.isSuccess) {
                val newSyncState = if (supportsWatch && localPOI.pendingSyncToWatch) {
                    LocalPOISyncState.PENDING_WATCH
                } else {
                    LocalPOISyncState.IDLE
                }
                val updatedPOI = pushResult.poi.toData(
                    syncState = newSyncState,
                    deleted = false
                )
                poiDao.update(updatedPOI)
            } else {
                errorsLogs.add("Error saving new POI in backend: $pushResult")
                poiDao.update(localPOI.copy(remoteSyncErrorCode = pushResult.errorCode))
            }
        }
        return true
    }

    private suspend fun syncPOIModificationsWithBackend(
        remotePOIs: List<POI>,
        errorsLogs: MutableList<String>
    ): Boolean {
        var hasNewData = false
        val localPOIsById = poiDao.fetchAll().associateBy { it.creation }
        for (remotePOI in remotePOIs) {
            var localPOI = localPOIsById[remotePOI.creation] ?: continue
            // first we make sure localPOI key is aligned with backend POI
            if (localPOI.key != remotePOI.key) {
                localPOI = localPOI.copy(key = remotePOI.key)
                poiDao.update(localPOI)
                hasNewData = true
            }
            // proceed with conflict resolution only if there are changes or local POI is pending sync to backend
            if (remotePOI == localPOI.toDomain() && !localPOI.pendingSyncToBackend) continue
            if (remotePOI.modified <= localPOI.modified) {
                updateLocalPOIToBackend(localPOI, errorsLogs)
            } else {
                updateLocalPOIFromBackend(localPOI, remotePOI)
            }
            hasNewData = true
        }
        return hasNewData
    }

    private suspend fun updateLocalPOIFromBackend(localPOI: LocalPOI, remotePOI: POI) {
        // updating local POI with pending watch sync if needed
        val syncState = when {
            !supportsWatch -> {
                LocalPOISyncState.IDLE
            }
            localPOI.pendingSyncToWatch || remotePOI.watchEnabled -> {
                LocalPOISyncState.PENDING_WATCH
            }
            else -> {
                LocalPOISyncState.IDLE
            }
        }
        poiDao.update(remotePOI.toData(syncState = syncState, deleted = false))
    }

    private suspend fun updateLocalPOIToBackend(
        localPOI: LocalPOI,
        errorsLogs: MutableList<String>
    ) {
        val editResult = poiRemoteDataSource.upsert(localPOI.toDomain())
        if (editResult.isSuccess) {
            val newSyncState = if (supportsWatch && localPOI.pendingSyncToWatch) {
                LocalPOISyncState.PENDING_WATCH
            } else {
                LocalPOISyncState.IDLE
            }
            // we check if backend has returned a result because this operation could add a new POI
            // to backend if it wasn't found (and generate a new key for it)
            val poiToUpdate = editResult.poi?.toData(
                syncState = newSyncState,
                deleted = false
            ) ?: localPOI.copy(syncState = newSyncState)
            poiDao.update(poiToUpdate)
        } else {
            errorsLogs.add("Backend updating POI in backend: $editResult")
            poiDao.update(localPOI.copy(remoteSyncErrorCode = editResult.errorCode))
        }
    }

    private suspend fun pullNewPOIsFromBackend(remotePOIs: List<POI>): Boolean {
        val localPOIsById = poiDao.fetchAll().associateBy { it.creation }
        // saving new POIs from backend with pending watch sync
        val newPOIsFromBackend = remotePOIs
            .filter { remotePOI -> remotePOI.creation !in localPOIsById }
            .map {
                val syncState =
                    if (supportsWatch && it.watchEnabled) LocalPOISyncState.PENDING_WATCH else LocalPOISyncState.IDLE
                it.toData(syncState = syncState, deleted = false)
            }
        poiDao.insert(newPOIsFromBackend)
        return newPOIsFromBackend.isNotEmpty()
    }

    private suspend fun deletePOIsInBackend(errorsLogs: MutableList<String>): Boolean {
        val poisToDelete = poiDao.fetchAllPendingBackendSync()
            .filter { it.deleted }
            .groupBy { it.key != null }
        val poisToDeleteLocally = poisToDelete[false] ?: listOf()
        val poisToDeleteInBackend = poisToDelete[true] ?: listOf()

        // update local POIs which were not synced to backend yet
        for (localPOI in poisToDeleteLocally) {
            updateLocalPOIBackendSyncDone(localPOI)
        }

        for (localPOI in poisToDeleteInBackend) {
            if (localPOI.key != null) {
                poiRemoteDataSource.delete(localPOI.toDomain())
                updateLocalPOIBackendSyncDone(localPOI)
            } else {
                errorsLogs.add("Cannot delete POI in backend with null key: $localPOI")
            }
        }
        return poisToDeleteLocally.isNotEmpty() || poisToDeleteInBackend.isNotEmpty()
    }

    private suspend fun updateLocalPOIBackendSyncDone(localPOI: LocalPOI) {
        poiDao.update(
            localPOI.copy(
                syncState = if (supportsWatch && localPOI.pendingSyncToWatch) {
                    LocalPOISyncState.PENDING_WATCH
                } else {
                    LocalPOISyncState.IDLE
                }
            )
        )
    }

    private suspend fun handleTooManyPOIsWithWatchEnabled(errorsLogs: MutableList<String>): Boolean {
        val localWatchEnabledPOIs = poiDao.fetchAllWatchEnabled()
        if (localWatchEnabledPOIs.size > watchEnabledPOIsMaxCount) {
            val poisToDisable = localWatchEnabledPOIs.subList(
                watchEnabledPOIsMaxCount,
                localWatchEnabledPOIs.size
            ).map {
                it.copy(
                    watchEnabled = false,
                    syncState = if (supportsWatch) {
                        LocalPOISyncState.PENDING_WATCH
                    } else {
                        LocalPOISyncState.IDLE
                    }
                )
            }
            poiDao.update(poisToDisable)
            for (localPOI in poisToDisable) {
                updateLocalPOIToBackend(localPOI, errorsLogs)
            }
            return true
        } else {
            return false
        }
    }
}
