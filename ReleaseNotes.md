#### 5.5.4 (2025-07-28)
 - Fixes
  - Update some strings by author:JiangXianming2
  - 189834 189829 Recovery, daily health six month time range calculate by author:JiangXianming2
  - 189965,189953,190032,190074 Some detail bugs by author:JiangXianming2
  - 190056 Fix TP & found bugs by author:<PERSON><PERSON>ianming2
  - 190115 Top Route speed issue on edit mode by author:moon
  - the issue causing a conflict between Quick Navigation and Climb Guidance UI interactions by author:Jessie<PERSON><PERSON>i
  - 182473 The route that is created and saved by quick navigation cannot be synchronized to the watch during exercise by author:Jessie<PERSON><PERSON><PERSON>
  - 189891 Merge long sleep with naps on the same day by author:dt2dev
  - 189479,189486 Some sleep bugs and more by author:dt2dev
  - 190030 In the impacts module, when there is No data, one - is missing before each of the two no data by author:JiangXianming2
  - 190005 Popular routes favorite list not refresh issue by author:moon
  - 189958 Statistics chart can not slide up in landscape screen by author:<PERSON><PERSON><PERSON>ming2
  - 189824 189827 188485 The time range of 6M by author:JiangXianming2
  - 189791 189820 189825 189841 Fix TP bugs by author:wang<PERSON><PERSON><PERSON>-<PERSON>unto
  - 189908,189903,189902,189850,189795,189756,188594 Various bugs by author:shuaiwenchen
  - 189777 Do not use media store when sharing diary calendar by author:dt2dev
  - 189798 When there are no search results, there is no prompt by author:JiangXianming2
  - 189302 Update progress 6W and M  time ranges x-axis style by author:dt2dev
  - Cluster marker text style issue by author:moon
  - Some popular routes UI issues, 1/x by author:moon
  - 189661,188594  by author:shuaiwenchen
  - 189676,189696,189756,189456 bug by author:shuaiwenchen
  - chart time selection label add space by author:shuaiwenchen
  - 189668 189682 Two simple bugs by author:JiangXianming2
  - 189432 X button of new homepage onboarding view is bigger than design by author:JiangXianming2
  - 189552 crash issue from DragToHighlightGraphHandler by author:JessieeLi
  - 181254 181342 update the prompt text by author:JessieeLi
  - Update popular edit UI by author:moon
  - 189648,189639,189621,189618,189525 bug by author:shuaiwenchen
  - 188686 Suunto ZH displays Welcome back after account registration, inconsistent with the international version's Welcome to the community by author:JessieeLi
  - 189339,189305,189370,189379,189476,189328,189501,189317,189310 Some progress bugs by author:dt2dev
  - 189457,189644 Target setting bugs by author:dt2dev
  - Library route labels duplicate click issue by author:moon
  - Fix home widgets UI review issues by author:wangxiaochun-suunto
  - 189442 189460 189470 Some bugs of commute by author:liangkun3892
  - 189491,189455,189434,189316,189111,189516 bug by author:shuaiwenchen
  - 181430 The lap data in the comparison details are incorrect by author:JessieeLi
  - 189258 Division by zero issue by author:dt2dev
  - 189536 Popular routes favorite status issue by author:moon
  - 185350 Input dispatching timed out from AppUpdatesActivity by author:JessieeLi
  - 185345 Input dispatching timed out from RoutePlannerActivity by author:JessieeLi
  - 185354 Input dispatching timed out from FullscreenGraphAnalysisActivity by author:JessieeLi
  - 189352,189435,189436 Some sleep related bugs by author:dt2dev
  - resolved an issue where the keyboard would appear even when the edit field was not visible.[develop] by author:JessieeLi

 - Features
  - Add top route feature toggle default value for SA global by author:moon
  - 175417 User badges add list pages, 5/x by author:liangkun3892
  - 186685 Support multisport mode customization in NG3 watch, 5/x by author:wangxiaochun-suunto
  - 175417 User badges, 4/x  by author:liangkun3892
  - 189334 Add install firmware feature for dilu by author:moon
  - Add strings to translate for home widgets by author:wangxiaochun-suunto
  - 186685 Support multisport mode customization in NG3 watch, 4/x by author:wangxiaochun-suunto
  - Adjust heatmap position on map selection dialog by author:moon
  - 186685 Support multisport mode customization in NG3 watch, 3/x by author:wangxiaochun-suunto
  - 189433 Optimize User Profile page phase 2, 1/x by author:wangxiaochun-suunto
  - 186685 Support multisport mode customization in NG3 watch, 2/x by author:wangxiaochun-suunto
  - 188150 Support marathon training mode customization, 3/x by author:wangxiaochun-suunto
  - 186685 Support multisport mode customization in NG3 watch, 1/x by author:wangxiaochun-suunto
  - 175417 User badges, 3/x by author:liangkun3892
  - 187306 Support offline maps on mobile, 3/x by author:Xizhi Zhu (Steven)
  - 175417 User badges ,add toggle and entry for badges, 2/x by author:liangkun3892
  - 187306 Support offline maps on mobile, 2/x by author:Xizhi Zhu (Steven)
  - 188655 Climb guidance 3/x by author:JessieeLi
  - 187306 Support offline maps on mobile, 1/x by author:Xizhi Zhu (Steven)
  - 175417 User badges, 1/x by author:liangkun3892
  - 188649 188711 188709 climb guidance 2/x by author:JessieeLi

 - Technical
  - Remove new training zone toggle by author:JiangXianming2
  - Move some strings to translation by author:JiangXianming2
  - Add click ripple effect to the route icon by author:JiangXianming2
  - Update some configurations for China test env by author:JiangXianming2
  - Remove legacy user profile activity, 2/2 by author:JiangXianming2
  - Remove legacy user profile activity, 1/x by author:Xizhi Zhu (Steven)
  - Refactor OfflineMapsSelectionViewModel to communicate through event and state, 3/x by author:Xizhi Zhu (Steven)
  - Refactor OfflineMapsSelectionViewModel to communicate through event and state, 2/x by author:Xizhi Zhu (Steven)
  - Refactor OfflineMapsSelectionViewModel to communicate through event and state, 1/x by author:Xizhi Zhu (Steven)
