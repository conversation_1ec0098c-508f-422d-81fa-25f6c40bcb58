{"formatVersion": 1, "database": {"version": 18, "identityHash": "8a5adaab0618dd431ed595ddbbf3d35d", "entities": [{"tableName": "sleep", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`serial` TEXT NOT NULL, `timestamp` INTEGER NOT NULL, `sleep_seconds` REAL NOT NULL, `deep_sleep` INTEGER, `awake` INTEGER, `feeling` INTEGER, `avg_hr` REAL, `min_hr` REAL, `quality` INTEGER, `fell_asleep` INTEGER, `woke_up` INTEGER, `segments` TEXT, PRIMARY KEY(`serial`, `timestamp`))", "fields": [{"fieldPath": "serial", "columnName": "serial", "affinity": "TEXT", "notNull": true}, {"fieldPath": "timestamp", "columnName": "timestamp", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "sleepSeconds", "columnName": "sleep_seconds", "affinity": "REAL", "notNull": true}, {"fieldPath": "deepSleep", "columnName": "deep_sleep", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "awake", "columnName": "awake", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "feeling", "columnName": "feeling", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "avgHr", "columnName": "avg_hr", "affinity": "REAL", "notNull": false}, {"fieldPath": "minHr", "columnName": "min_hr", "affinity": "REAL", "notNull": false}, {"fieldPath": "quality", "columnName": "quality", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "fell<PERSON>leep", "columnName": "fell_asleep", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "wokeUp", "columnName": "woke_up", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "segments", "columnName": "segments", "affinity": "TEXT", "notNull": false}], "primaryKey": {"columnNames": ["serial", "timestamp"], "autoGenerate": false}, "indices": [], "foreignKeys": []}, {"tableName": "sleepsegments", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`serial` TEXT NOT NULL, `timestamp_seconds` INTEGER NOT NULL, `quality` INTEGER, `avg_hr` REAL, `min_hr` REAL, `feeling` INTEGER, `duration_seconds` REAL NOT NULL, `deep_sleep_duration_seconds` REAL, `synced_status` INTEGER NOT NULL, `timestamp_iso` TEXT NOT NULL, `bedtime_start` INTEGER, `bedtime_end` INTEGER, PRIMARY KEY(`timestamp_seconds`))", "fields": [{"fieldPath": "serial", "columnName": "serial", "affinity": "TEXT", "notNull": true}, {"fieldPath": "timestampSeconds", "columnName": "timestamp_seconds", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "quality", "columnName": "quality", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "avgHr", "columnName": "avg_hr", "affinity": "REAL", "notNull": false}, {"fieldPath": "minHr", "columnName": "min_hr", "affinity": "REAL", "notNull": false}, {"fieldPath": "feeling", "columnName": "feeling", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "durationSeconds", "columnName": "duration_seconds", "affinity": "REAL", "notNull": true}, {"fieldPath": "deepSleepDurationSeconds", "columnName": "deep_sleep_duration_seconds", "affinity": "REAL", "notNull": false}, {"fieldPath": "syncedStatus", "columnName": "synced_status", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "timeISO8601", "columnName": "timestamp_iso", "affinity": "TEXT", "notNull": true}, {"fieldPath": "bedtimeStart", "columnName": "bedtime_start", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "bedtimeEnd", "columnName": "bedtime_end", "affinity": "INTEGER", "notNull": false}], "primaryKey": {"columnNames": ["timestamp_seconds"], "autoGenerate": false}, "indices": [], "foreignKeys": []}, {"tableName": "trenddata_v2", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`serial` TEXT NOT NULL, `timestamp_seconds` INTEGER NOT NULL, `energy` REAL NOT NULL, `steps` INTEGER NOT NULL, `synced_status` INTEGER NOT NULL, `timestamp_iso` TEXT NOT NULL, PRIMARY KEY(`timestamp_seconds`))", "fields": [{"fieldPath": "serial", "columnName": "serial", "affinity": "TEXT", "notNull": true}, {"fieldPath": "timestampSeconds", "columnName": "timestamp_seconds", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "energy", "columnName": "energy", "affinity": "REAL", "notNull": true}, {"fieldPath": "steps", "columnName": "steps", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "syncedStatus", "columnName": "synced_status", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "timeISO8601", "columnName": "timestamp_iso", "affinity": "TEXT", "notNull": true}], "primaryKey": {"columnNames": ["timestamp_seconds"], "autoGenerate": false}, "indices": [], "foreignKeys": []}, {"tableName": "diveextension", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`maxDepth` REAL, `algorithm` TEXT, `personalSetting` INTEGER, `diveNumberInSeries` INTEGER, `cns` REAL, `algorithmLock` INTEGER, `diveMode` TEXT, `otu` REAL, `pauseDuration` REAL, `gasConsumption` REAL, `altitudeSetting` REAL, `gasQuantities` TEXT NOT NULL, `surfaceTime` REAL, `gasesUsed` TEXT NOT NULL, `maxDepthTemperature` REAL, `avgDepth` REAL, `minGF` REAL, `maxGF` REAL, `workoutId` INTEGER NOT NULL, PRIMARY KEY(`workoutId`))", "fields": [{"fieldPath": "max<PERSON><PERSON><PERSON>", "columnName": "max<PERSON><PERSON><PERSON>", "affinity": "REAL", "notNull": false}, {"fieldPath": "algorithm", "columnName": "algorithm", "affinity": "TEXT", "notNull": false}, {"fieldPath": "personalSetting", "columnName": "personalSetting", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "diveNumberInSeries", "columnName": "diveNumberInSeries", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "cns", "columnName": "cns", "affinity": "REAL", "notNull": false}, {"fieldPath": "algorithmLock", "columnName": "algorithmLock", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "diveMode", "columnName": "diveMode", "affinity": "TEXT", "notNull": false}, {"fieldPath": "otu", "columnName": "otu", "affinity": "REAL", "notNull": false}, {"fieldPath": "pauseDuration", "columnName": "pauseDuration", "affinity": "REAL", "notNull": false}, {"fieldPath": "gasConsumption", "columnName": "gasConsumption", "affinity": "REAL", "notNull": false}, {"fieldPath": "altitudeSetting", "columnName": "altitudeSetting", "affinity": "REAL", "notNull": false}, {"fieldPath": "gasQuantities", "columnName": "gasQuantities", "affinity": "TEXT", "notNull": true}, {"fieldPath": "surfaceTime", "columnName": "surfaceTime", "affinity": "REAL", "notNull": false}, {"fieldPath": "gasesUsed", "columnName": "gasesUsed", "affinity": "TEXT", "notNull": true}, {"fieldPath": "maxDepthTemperature", "columnName": "maxDepthTemperature", "affinity": "REAL", "notNull": false}, {"fieldPath": "avg<PERSON><PERSON><PERSON>", "columnName": "avg<PERSON><PERSON><PERSON>", "affinity": "REAL", "notNull": false}, {"fieldPath": "minGF", "columnName": "minGF", "affinity": "REAL", "notNull": false}, {"fieldPath": "maxGF", "columnName": "maxGF", "affinity": "REAL", "notNull": false}, {"fieldPath": "workoutId", "columnName": "workoutId", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"columnNames": ["workoutId"], "autoGenerate": false}, "indices": [], "foreignKeys": []}, {"tableName": "smlextension", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`sml_zip` BLOB, `workoutId` INTEGER NOT NULL, PRIMARY KEY(`workoutId`))", "fields": [{"fieldPath": "smlZip", "columnName": "sml_zip", "affinity": "BLOB", "notNull": false}, {"fieldPath": "workoutId", "columnName": "workoutId", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"columnNames": ["workoutId"], "autoGenerate": false}, "indices": [], "foreignKeys": []}, {"tableName": "smlzippreference", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`workoutId` INTEGER NOT NULL, `logbookEntryId` INTEGER NOT NULL, `zipPath` TEXT NOT NULL, `workoutKey` TEXT, `synced` INTEGER NOT NULL, `syncedErrorMessage` TEXT, PRIMARY KEY(`workoutId`))", "fields": [{"fieldPath": "workoutId", "columnName": "workoutId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "logbookEntryId", "columnName": "logbookEntryId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "zipPath", "columnName": "zipPath", "affinity": "TEXT", "notNull": true}, {"fieldPath": "workoutKey", "columnName": "workoutKey", "affinity": "TEXT", "notNull": false}, {"fieldPath": "synced", "columnName": "synced", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "syncedErrorMessage", "columnName": "syncedErrorMessage", "affinity": "TEXT", "notNull": false}], "primaryKey": {"columnNames": ["workoutId"], "autoGenerate": false}, "indices": [], "foreignKeys": []}, {"tableName": "swimmingextension", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`avgSwolf` INTEGER NOT NULL, `avgStrokeRate` REAL NOT NULL, `workoutId` INTEGER NOT NULL, PRIMARY KEY(`workoutId`))", "fields": [{"fieldPath": "avgSwolf", "columnName": "avgSwolf", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "avgStrokeRate", "columnName": "avgStrokeRate", "affinity": "REAL", "notNull": true}, {"fieldPath": "workoutId", "columnName": "workoutId", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"columnNames": ["workoutId"], "autoGenerate": false}, "indices": [], "foreignKeys": []}, {"tableName": "routes", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`_id` TEXT NOT NULL, `watchRouteId` INTEGER NOT NULL, `key` TEXT NOT NULL, `ownerUserName` TEXT NOT NULL, `name` TEXT NOT NULL, `visibility` TEXT NOT NULL, `activityIds` TEXT NOT NULL, `avgSpeed` REAL NOT NULL, `totalDistance` REAL NOT NULL, `startPoint` TEXT NOT NULL, `centerPoint` TEXT NOT NULL, `stopPoint` TEXT NOT NULL, `locallyChanged` INTEGER NOT NULL, `modifiedDate` INTEGER NOT NULL, `deleted` INTEGER NOT NULL, `created` INTEGER NOT NULL, `watchSyncState` TEXT NOT NULL, `watchSyncResponseCode` INTEGER NOT NULL, `watchEnabled` INTEGER NOT NULL, `segments` BLOB NOT NULL, PRIMARY KEY(`_id`))", "fields": [{"fieldPath": "id", "columnName": "_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "watchRouteId", "columnName": "watchRouteId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "key", "columnName": "key", "affinity": "TEXT", "notNull": true}, {"fieldPath": "ownerUserName", "columnName": "ownerUserName", "affinity": "TEXT", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "visibility", "columnName": "visibility", "affinity": "TEXT", "notNull": true}, {"fieldPath": "activityIds", "columnName": "activityIds", "affinity": "TEXT", "notNull": true}, {"fieldPath": "averageSpeed", "columnName": "avgSpeed", "affinity": "REAL", "notNull": true}, {"fieldPath": "totalDistance", "columnName": "totalDistance", "affinity": "REAL", "notNull": true}, {"fieldPath": "startPoint", "columnName": "startPoint", "affinity": "TEXT", "notNull": true}, {"fieldPath": "centerPoint", "columnName": "centerPoint", "affinity": "TEXT", "notNull": true}, {"fieldPath": "stopPoint", "columnName": "stopPoint", "affinity": "TEXT", "notNull": true}, {"fieldPath": "locallyChanged", "columnName": "locallyChanged", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "modifiedDate", "columnName": "modifiedDate", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "deleted", "columnName": "deleted", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "createdDate", "columnName": "created", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "watchSyncState", "columnName": "watchSyncState", "affinity": "TEXT", "notNull": true}, {"fieldPath": "watchSyncResponseCode", "columnName": "watchSyncResponseCode", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "watchEnabled", "columnName": "watchEnabled", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "segments", "columnName": "segments", "affinity": "BLOB", "notNull": true}], "primaryKey": {"columnNames": ["_id"], "autoGenerate": false}, "indices": [], "foreignKeys": []}, {"tableName": "rankings", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`_id` TEXT NOT NULL, `workoutKey` TEXT NOT NULL, `rankingType` TEXT NOT NULL, `ranking` INTEGER, `numberOfWorkouts` INTEGER, PRIMARY KEY(`_id`))", "fields": [{"fieldPath": "id", "columnName": "_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "key", "columnName": "workoutKey", "affinity": "TEXT", "notNull": true}, {"fieldPath": "type", "columnName": "rankingType", "affinity": "TEXT", "notNull": true}, {"fieldPath": "ranking", "columnName": "ranking", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "numberOfWorkouts", "columnName": "numberOfWorkouts", "affinity": "INTEGER", "notNull": false}], "primaryKey": {"columnNames": ["_id"], "autoGenerate": false}, "indices": [], "foreignKeys": []}, {"tableName": "workoutAttributesUpdate", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`workoutId` INTEGER NOT NULL, `ownerUsername` TEXT NOT NULL, `attributes` TEXT, `fields` TEXT NOT NULL, `requiresUserConfirmation` INTEGER NOT NULL, PRIMARY KEY(`workoutId`))", "fields": [{"fieldPath": "workoutId", "columnName": "workoutId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "ownerUsername", "columnName": "ownerUsername", "affinity": "TEXT", "notNull": true}, {"fieldPath": "attributes", "columnName": "attributes", "affinity": "TEXT", "notNull": false}, {"fieldPath": "fields", "columnName": "fields", "affinity": "TEXT", "notNull": true}, {"fieldPath": "requiresUserConfirmation", "columnName": "requiresUserConfirmation", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"columnNames": ["workoutId"], "autoGenerate": false}, "indices": [], "foreignKeys": []}, {"tableName": "goal_definitions", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `userName` TEXT NOT NULL, `name` TEXT, `type` INTEGER NOT NULL, `period` INTEGER NOT NULL, `startTime` INTEGER NOT NULL, `endTime` INTEGER NOT NULL, `target` INTEGER NOT NULL, `created` INTEGER NOT NULL, `activityIds` TEXT NOT NULL)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "userName", "columnName": "userName", "affinity": "TEXT", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": false}, {"fieldPath": "type", "columnName": "type", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "period", "columnName": "period", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "startTime", "columnName": "startTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "endTime", "columnName": "endTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "target", "columnName": "target", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "created", "columnName": "created", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "activityIds", "columnName": "activityIds", "affinity": "TEXT", "notNull": true}], "primaryKey": {"columnNames": ["id"], "autoGenerate": true}, "indices": [], "foreignKeys": []}, {"tableName": "weatherextension", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`airPressure` REAL, `cloudiness` INTEGER, `groundLevelAirPressure` REAL, `humidity` INTEGER, `rainVolume1h` REAL, `rainVolume3h` REAL, `seaLevelAirPressure` REAL, `snowVolume1h` REAL, `snowVolume3h` REAL, `temperature` REAL, `weatherIcon` TEXT, `windDirection` REAL, `windSpeed` REAL, `workoutId` INTEGER NOT NULL, PRIMARY KEY(`workoutId`))", "fields": [{"fieldPath": "airPressure", "columnName": "airPressure", "affinity": "REAL", "notNull": false}, {"fieldPath": "cloudiness", "columnName": "cloudiness", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "groundLevelAirPressure", "columnName": "groundLevelAirPressure", "affinity": "REAL", "notNull": false}, {"fieldPath": "humidity", "columnName": "humidity", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "rainVolume1h", "columnName": "rainVolume1h", "affinity": "REAL", "notNull": false}, {"fieldPath": "rainVolume3h", "columnName": "rainVolume3h", "affinity": "REAL", "notNull": false}, {"fieldPath": "seaLevelAirPressure", "columnName": "seaLevelAirPressure", "affinity": "REAL", "notNull": false}, {"fieldPath": "snowVolume1h", "columnName": "snowVolume1h", "affinity": "REAL", "notNull": false}, {"fieldPath": "snowVolume3h", "columnName": "snowVolume3h", "affinity": "REAL", "notNull": false}, {"fieldPath": "temperature", "columnName": "temperature", "affinity": "REAL", "notNull": false}, {"fieldPath": "weatherIcon", "columnName": "weatherIcon", "affinity": "TEXT", "notNull": false}, {"fieldPath": "windDirection", "columnName": "windDirection", "affinity": "REAL", "notNull": false}, {"fieldPath": "windSpeed", "columnName": "windSpeed", "affinity": "REAL", "notNull": false}, {"fieldPath": "workoutId", "columnName": "workoutId", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"columnNames": ["workoutId"], "autoGenerate": false}, "indices": [], "foreignKeys": []}], "views": [], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '8a5adaab0618dd431ed595ddbbf3d35d')"]}}