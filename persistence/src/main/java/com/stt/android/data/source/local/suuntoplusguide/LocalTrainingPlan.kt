package com.stt.android.data.source.local.suuntoplusguide

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.TypeConverters
import com.stt.android.data.source.local.NullableIntListJsonConverter
import com.stt.android.data.source.local.NullableLocalDateConverter
import com.stt.android.data.source.local.TABLE_TRAINING_PLANS
import java.time.LocalDate

@Entity(tableName = TABLE_TRAINING_PLANS)
@TypeConverters(NullableIntListJsonConverter::class, NullableLocalDateConverter::class, CoursesMapConverter::class)
data class LocalTrainingPlan(
    @PrimaryKey
    @ColumnInfo(name = ID)
    val id: String,
    @ColumnInfo(name = CATALOGUE_ID) val catalogueId: String?, // Null if not from catalogue
    @ColumnInfo(name = MODIFIED) val modifiedMillis: Long, // UTC ts milliseconds
    @ColumnInfo(name = NAME) val name: String,
    @ColumnInfo(name = OWNER) val owner: String,
    @ColumnInfo(name = OWNER_ID) val ownerId: String?,
    @ColumnInfo(name = START_DATE) val startDate: LocalDate?,
    @ColumnInfo(name = END_DATE) val endDate: LocalDate?,
    @ColumnInfo(name = URL) val url: String?,
    @ColumnInfo(name = ICON_URL) val iconUrl: String?,
    @ColumnInfo(name = BACKGROUND_URL) val backgroundUrl: String?,
    @ColumnInfo(name = DESCRIPTION) val description: String,
    @ColumnInfo(name = SUBTITLE) val subTitle: String?,
    @ColumnInfo(name = RICH_DESCRIPTION) val richDescription: String?,
    @ColumnInfo(name = PRIORITY_INDEX) val priorityIndex: Int?,
    @ColumnInfo(name = ACTIVITY_IDS) val activityIds: List<Int>?,
    @ColumnInfo(name = PINNED) val pinned: Boolean,
    @ColumnInfo(name = DELETED) val deleted: Boolean,
    @ColumnInfo(name = REMOTE_SYNC_ERROR_CODE) val remoteSyncErrorCode: Int?,
    @ColumnInfo(name = WATCH_SYNC_ERROR_CODE) val watchSyncErrorCode: Int?,
    @ColumnInfo(name = PLAN_ID) val planId: String?,
    @ColumnInfo(name = COURSESMAP) val coursesMap: Map<Int, List<String>>?,
) {
    companion object DbFields {
        const val ID = "id" // Plan plugin ID, the same as watch plug-in ID
        const val CATALOGUE_ID = "catalogueId"
        const val PLAN_ID = "planId" // Plan ID
        const val MODIFIED = "modified"
        const val NAME = "name"
        const val OWNER = "owner"
        const val OWNER_ID = "ownerId"
        const val URL = "url"
        const val ICON_URL = "iconUrl"
        const val BACKGROUND_URL = "backgroundUrl"
        const val START_DATE = "startDate"
        const val END_DATE = "endDate"
        const val DESCRIPTION = "description"
        const val SUBTITLE = "subTitle"
        const val RICH_DESCRIPTION = "richDescription"
        const val ACTIVITY_IDS = "activityIds"
        const val PRIORITY_INDEX = "priority"
        const val PINNED = "pinned"
        const val DELETED = "deleted"
        const val REMOTE_SYNC_ERROR_CODE = "remoteSyncErrorCode"
        const val WATCH_SYNC_ERROR_CODE = "watchSyncErrorCode"
        const val COURSESMAP = "coursesMap"
    }
}
