@file:Suppress("DEPRECATION")

package com.stt.android.data.source.local

import android.content.ContentValues
import android.database.sqlite.SQLiteDatabase
import androidx.core.content.contentValuesOf
import androidx.room.migration.Migration
import androidx.sqlite.db.SupportSQLiteDatabase
import androidx.sqlite.db.SupportSQLiteQueryBuilder
import com.squareup.moshi.JsonAdapter
import com.squareup.moshi.Moshi
import com.squareup.moshi.Types
import com.stt.android.data.TimeUtils
import com.stt.android.data.source.local.gear.LocalGear
import com.stt.android.data.source.local.menstrualcycle.LocalMenstrualCycle
import com.stt.android.data.source.local.routes.LocalRoute
import com.stt.android.data.source.local.routes.LocalRouteUpToVersion13
import com.stt.android.data.source.local.routes.LocalRouteVerticalDeltaCalc
import com.stt.android.data.source.local.routes.RouteSegmentProtoConverter
import com.stt.android.data.source.local.sleep.LocalSleepSegment
import com.stt.android.data.source.local.sleep.LocalSleepSegmentOldDBv35
import com.stt.android.data.source.local.sleep.OldLocalSleep
import com.stt.android.data.source.local.summaryextension.LocalSummaryExtension
import com.stt.android.data.source.local.suuntoplusfeature.LocalSuuntoPlusFeature
import com.stt.android.data.source.local.suuntoplusguide.LocalSuuntoPlusGuide
import com.stt.android.data.source.local.suuntoplusguide.LocalSuuntoPlusPluginDeviceStatus
import com.stt.android.data.source.local.tags.LocalSuuntoTag
import com.stt.android.data.source.local.tags.LocalSuuntoTagConverter
import com.stt.android.data.source.local.trenddata.LocalTrendData
import com.stt.android.data.source.local.trenddata.LocalTrendDataOldDBv20
import com.stt.android.data.source.local.trenddata.LocalTrendDataOldDBv7
import com.stt.android.data.source.local.workout.LocalWorkoutHeader
import com.stt.android.data.source.local.workout.attributes.LocalWorkoutAttributesUpdate
import com.stt.android.db.forEach
import com.stt.android.db.getBlob
import com.stt.android.db.getDouble
import com.stt.android.db.getInt
import com.stt.android.db.getLong
import com.stt.android.db.getString
import com.stt.android.db.getStringOrNull
import com.stt.android.timeline.entity.SleepAttributesHeaderTimelineWrapper
import com.stt.android.utils.takeIfNotEmpty
import com.suunto.connectivity.suuntoconnectivity.device.ProductType
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoDeviceType
import timber.log.Timber
import java.time.LocalDate
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.util.concurrent.TimeUnit
import javax.inject.Inject

@Suppress("MemberVisibilityCanBePrivate", "PropertyName")
class Migrations
@Inject constructor(
    private val moshi: Moshi
) {
    val MIGRATE_1_2 = object : Migration(1, 2) {
        override fun migrate(db: SupportSQLiteDatabase) {
            db.execSQL(
                """
                CREATE TABLE IF NOT EXISTS $TABLE_DIVE_EXTENSION
                (workoutId INTEGER NOT NULL,
                maxDepth REAL,
                maxDepthTemperature REAL,
                avgDepth REAL,
                diveMode TEXT,
                diveNumberInSeries INTEGER,
                surfaceTime REAL,
                gasTypes TEXT NOT NULL,
                avgConsumption REAL,
                algorithmLock INTEGER,
                cns REAL,
                otu REAL,
                personalSetting INTEGER,
                altitudeSetting REAL,
                algorithm TEXT,
                depth TEXT NOT NULL,
                temperature TEXT NOT NULL,
                ventilation TEXT NOT NULL,
                tankPressures TEXT NOT NULL,
                PRIMARY KEY(workoutId))
                """.trimIndent()
            )
        }
    }

    val MIGRATE_2_3 = object : Migration(2, 3) {
        override fun migrate(db: SupportSQLiteDatabase) {
            db.doIfTableExists(TABLE_DIVE_EXTENSION) {
                db.execSQL("ALTER TABLE $TABLE_DIVE_EXTENSION RENAME TO ${TABLE_DIVE_EXTENSION}_old")
            }
            db.execSQL(
                """
                CREATE TABLE IF NOT EXISTS $TABLE_DIVE_EXTENSION
                    (workoutId INTEGER NOT NULL,
                    maxDepth REAL,
                    algorithm TEXT,
                    personalSetting INTEGER,
                    diveNumberInSeries INTEGER,
                    cns REAL,
                    algorithmLock INTEGER,
                    diveMode TEXT,
                    otu REAL,
                    pauseDuration REAL,
                    gasConsumption REAL,
                    altitudeSetting REAL,
                    gasQuantities TEXT NOT NULL,
                    surfaceTime REAL,
                    gasesUsed TEXT NOT NULL,
                    maxDepthTemperature REAL,
                    avgDepth REAL,
                    PRIMARY KEY(workoutId))
                """.trimIndent()
            )
            db.execSQL(
                """
                CREATE TABLE IF NOT EXISTS smlextension (
                    workoutId INTEGER NOT NULL,
                    sml TEXT,
                    PRIMARY KEY(workoutId)
                )
                """.trimIndent()
            )
            db.execSQL("DROP TABLE IF EXISTS ${TABLE_DIVE_EXTENSION}_old")
        }
    }

    val MIGRATE_3_4 = object : Migration(3, 4) {
        private val minGf = "minGF"
        private val maxGf = "maxGF"

        override fun migrate(db: SupportSQLiteDatabase) {
            db.doIfTableExists(TABLE_DIVE_EXTENSION) {
                val existingColumns = db.getColumnsFor(TABLE_DIVE_EXTENSION)

                if (!existingColumns.contains(minGf)) {
                    db.execSQL("ALTER TABLE $TABLE_DIVE_EXTENSION ADD COLUMN $minGf REAL;")
                }
                if (!existingColumns.contains(maxGf)) {
                    db.execSQL("ALTER TABLE $TABLE_DIVE_EXTENSION ADD COLUMN $maxGf REAL;")
                }
            }
        }
    }

    val MIGRATE_4_5 = object : Migration(4, 5) {
        private val DEEP_SLEEP = "deep_sleep"
        private val AWAKE = "awake"
        private val FEELING = "feeling"
        private val AVG_HR = "avg_hr"
        private val MIN_HR = "min_hr"
        private val QUALITY = "quality"
        private val FELL_ASLEEP = "fell_asleep"
        private val WOKE_UP = "woke_up"
        private val SEGMENTS = "segments"

        override fun migrate(db: SupportSQLiteDatabase) {
            val existingColumns = db.getColumnsFor(TABLE_SLEEP)
            if (!existingColumns.contains(DEEP_SLEEP)) {
                db.execSQL("ALTER TABLE $TABLE_SLEEP ADD COLUMN $DEEP_SLEEP INTEGER;")
            }
            if (!existingColumns.contains(AWAKE)) {
                db.execSQL("ALTER TABLE $TABLE_SLEEP ADD COLUMN $AWAKE INTEGER;")
            }
            if (!existingColumns.contains(FEELING)) {
                db.execSQL("ALTER TABLE $TABLE_SLEEP ADD COLUMN $FEELING INTEGER;")
            }
            if (!existingColumns.contains(AVG_HR)) {
                db.execSQL("ALTER TABLE $TABLE_SLEEP ADD COLUMN $AVG_HR REAL;")
            }
            if (!existingColumns.contains(MIN_HR)) {
                db.execSQL("ALTER TABLE $TABLE_SLEEP ADD COLUMN $MIN_HR REAL;")
            }
            if (!existingColumns.contains(QUALITY)) {
                db.execSQL("ALTER TABLE $TABLE_SLEEP ADD COLUMN $QUALITY INTEGER;")
            }
            if (!existingColumns.contains(FELL_ASLEEP)) {
                db.execSQL("ALTER TABLE $TABLE_SLEEP ADD COLUMN $FELL_ASLEEP INTEGER;")
            }
            if (!existingColumns.contains(WOKE_UP)) {
                db.execSQL("ALTER TABLE $TABLE_SLEEP ADD COLUMN $WOKE_UP INTEGER;")
            }
            if (!existingColumns.contains(SEGMENTS)) {
                db.execSQL("ALTER TABLE $TABLE_SLEEP ADD COLUMN $SEGMENTS TEXT;")
            }
        }
    }

    val MIGRATE_5_6 = object : Migration(5, 6) {
        override fun migrate(db: SupportSQLiteDatabase) {
            db.execSQL("CREATE TABLE IF NOT EXISTS `$TABLE_SML_ZIP_REFERENCE` (`workoutId` INTEGER NOT NULL, `logbookEntryId` INTEGER NOT NULL, `zipPath` TEXT NOT NULL, `workoutKey` TEXT, `synced` INTEGER NOT NULL, `syncedErrorMessage` TEXT, PRIMARY KEY(`workoutId`))")
        }
    }

    val MIGRATE_6_7 = object : Migration(6, 7) {
        override fun migrate(db: SupportSQLiteDatabase) {
            db.execSQL(
                """
                CREATE TABLE IF NOT EXISTS `$TABLE_ROUTES` (
                    `_id` TEXT NOT NULL,
                    `key` TEXT NOT NULL,
                    `ownerUserName` TEXT NOT NULL,
                    `name` TEXT NOT NULL,
                    `visibility` TEXT NOT NULL,
                    `activityIds` TEXT NOT NULL,
                    `avgSpeed` REAL NOT NULL,
                    `totalDistance` REAL NOT NULL,
                    `startPoint` TEXT NOT NULL,
                    `centerPoint` TEXT NOT NULL,
                    `stopPoint` TEXT NOT NULL,
                    `locallyChanged` INTEGER NOT NULL,
                    `deleted` INTEGER NOT NULL,
                    `created` INTEGER NOT NULL,
                    `modifiedDate` INTEGER NOT NULL,
                    `watchSyncState` TEXT NOT NULL,
                    `watchSyncResponseCode` INTEGER NOT NULL,
                    `segments` TEXT NOT NULL,
                    `watchRouteId` INTEGER NOT NULL,
                    `watchEnabled` INTEGER NOT NULL,
                    PRIMARY KEY(`_id`))
                """.trimIndent()
            )
        }
    }

    /**
     * This migration changes:
     * - the way we save sleep data, saving one raw per SleepSegment instead of
     * saving aggregated sleep nights. Also serial is not part of the primary keys.
     * - the way we save trend data, adding ZonedDateTime and not having serial as primary key.
     */
    @Suppress("DEPRECATION")
    val MIGRATE_7_8 = object : Migration(7, 8) {

        private val remoteTimelineHeaderJsonListAdapter: JsonAdapter<List<SleepAttributesHeaderTimelineWrapper>> =
            moshi.adapter(
                Types.newParameterizedType(
                    List::class.java,
                    SleepAttributesHeaderTimelineWrapper::class.java
                )
            )

        override fun migrate(db: SupportSQLiteDatabase) {
            Timber.d("Executing room migration 7->8")
            // sleep data migration
            db.execSQL("CREATE TABLE IF NOT EXISTS `$TABLE_SLEEP_SEGMENTS` (`serial` TEXT NOT NULL, `timestamp_seconds` INTEGER NOT NULL, `quality` INTEGER, `avg_hr` REAL, `min_hr` REAL, `feeling` INTEGER, `duration_seconds` REAL NOT NULL, `deep_sleep_duration_seconds` REAL, `synced_status` INTEGER NOT NULL, `timestamp_iso` TEXT NOT NULL, PRIMARY KEY(`timestamp_seconds`))")
            val localSleepSegments = extractAllSleepSegments(db)
            Timber.d("Migrating ${localSleepSegments.size} sleep segments")
            localSleepSegments.forEach {
                db.insert(
                    TABLE_SLEEP_SEGMENTS,
                    SQLiteDatabase.CONFLICT_IGNORE,
                    it.asContentValues()
                )
            }
            // trend data migration
            db.execSQL("CREATE TABLE IF NOT EXISTS `$TABLE_TREND_DATA` (`serial` TEXT NOT NULL, `timestamp_seconds` INTEGER NOT NULL, `energy` REAL NOT NULL, `steps` INTEGER NOT NULL, `synced_status` INTEGER NOT NULL, `timestamp_iso` TEXT NOT NULL, PRIMARY KEY(`timestamp_seconds`))")
            val localTrendData = extractAllTrendData(db)
            Timber.d("Migrating ${localTrendData.size} trend data")
            localTrendData.forEach {
                db.insert(TABLE_TREND_DATA, SQLiteDatabase.CONFLICT_IGNORE, it.asContentValues())
            }
            db.execSQL("DROP TABLE IF EXISTS $TABLE_TREND_DATA_OLD")
        }

        fun LocalSleepSegmentOldDBv35.asContentValues(recycled: ContentValues? = null): ContentValues {
            val contentValues = recycled?.apply { clear() } ?: ContentValues()
            return contentValues.apply {
                put(LocalSleepSegmentOldDBv35.COLUMN_SERIAL, serial)
                put(LocalSleepSegmentOldDBv35.COLUMN_TIMESTAMP_SECONDS, timestampSeconds)
                put(LocalSleepSegmentOldDBv35.COLUMN_QUALITY, quality)
                put(LocalSleepSegmentOldDBv35.COLUMN_AVG_HR, avgHr)
                put(LocalSleepSegmentOldDBv35.COLUMN_MIN_HR, minHr)
                put(LocalSleepSegmentOldDBv35.COLUMN_FEELING, feeling)
                put(LocalSleepSegmentOldDBv35.COLUMN_DURATION_SECONDS, durationSeconds)
                put(LocalSleepSegmentOldDBv35.COLUMN_DEEP_SLEEP_DURATION_SECONDS, deepSleepDurationSeconds)
                put(LocalSleepSegmentOldDBv35.COLUMN_SYNCED_STATUS, syncedStatus)
                put(
                    LocalSleepSegmentOldDBv35.COLUMN_TIMESTAMP_ISO,
                    ZonedDateTimeConverter().fromZonedDateTime(timeISO8601)
                )
            }
        }

        private fun extractAllSleepSegments(db: SupportSQLiteDatabase): List<LocalSleepSegmentOldDBv35> {
            val localSleepSegments = mutableListOf<LocalSleepSegmentOldDBv35>()
            db.query(SupportSQLiteQueryBuilder.builder(TABLE_SLEEP).create()).use { cursor ->
                cursor.forEach {
                    try {
                        val localSleep = OldLocalSleep(cursor)
                        val segments: List<SleepAttributesHeaderTimelineWrapper> = try {
                            remoteTimelineHeaderJsonListAdapter.fromJson(
                                localSleep.segments ?: "[]"
                            )
                                ?: emptyList()
                        } catch (e: Exception) {
                            Timber.w("Error reading localSleep segments: $localSleep")
                            emptyList()
                        }
                        if (segments.isEmpty()) {
                            val epochTimestamp = localSleep.timestamp
                            val timestampSeconds = TimeUnit.MILLISECONDS.toSeconds(epochTimestamp)
                            val timestampISO = TimeUtils.epochToLocalZonedDateTime(epochTimestamp)
                            // treat this as single segment sleep event
                            localSleepSegments.add(
                                LocalSleepSegmentOldDBv35(
                                    localSleep.serial,
                                    timestampSeconds,
                                    localSleep.quality,
                                    localSleep.avgHr,
                                    localSleep.minHr,
                                    localSleep.feeling,
                                    localSleep.sleepSeconds,
                                    localSleep.deepSleep?.toFloat(),
                                    LocalSleepSegment.STATUS_NOT_SYNCED,
                                    timestampISO
                                )
                            )
                        } else {
                            // add all the segments
                            localSleepSegments.addAll(
                                segments.mapNotNull { it.attributes.sleepSmlEntity.header }
                                    .map { header ->
                                        val epochTimestamp =
                                            header.dateTime.toInstant().toEpochMilli()
                                        val timestampSeconds =
                                            TimeUnit.MILLISECONDS.toSeconds(epochTimestamp)
                                        LocalSleepSegmentOldDBv35(
                                            localSleep.serial,
                                            timestampSeconds,
                                            header.quality,
                                            header.hr?.avg,
                                            header.hr?.min,
                                            header.feeling,
                                            header.duration,
                                            header.deepSleepDuration,
                                            LocalSleepSegment.STATUS_NOT_SYNCED,
                                            // converting to local timezone preserving instant
                                            header.dateTime.withZoneSameInstant(ZoneId.systemDefault())
                                        )
                                    }
                            )
                        }
                    } catch (e: Exception) {
                        Timber.w(e, "Error during sleep data migration")
                    }
                }
            }
            return localSleepSegments
        }

        private fun extractAllTrendData(db: SupportSQLiteDatabase): List<LocalTrendDataOldDBv20> {
            val localTrendData = mutableListOf<LocalTrendDataOldDBv20>()
            db.query(SupportSQLiteQueryBuilder.builder(TABLE_TREND_DATA_OLD).create())
                .use { cursor ->
                    cursor.forEach {
                        try {
                            val localTrendDataOld = LocalTrendDataOldDBv7(cursor)
                            val timeISO8601 =
                                TimeUtils.epochToLocalZonedDateTime(localTrendDataOld.timestamp)
                            localTrendData.add(
                                LocalTrendDataOldDBv20(
                                    localTrendDataOld.serial,
                                    timeISO8601.toInstant().epochSecond,
                                    localTrendDataOld.energy,
                                    localTrendDataOld.steps,
                                    LocalTrendData.STATUS_NOT_SYNCED,
                                    timeISO8601
                                )
                            )
                        } catch (e: Exception) {
                            Timber.w(e, "Error during trend data migration")
                        }
                    }
                }
            return localTrendData
        }
    }

    val MIGRATE_8_9 = object : Migration(8, 9) {
        override fun migrate(db: SupportSQLiteDatabase) {
            db.execSQL(
                """
                CREATE TABLE IF NOT EXISTS `$TABLE_SWIMMING_EXTENSION` (
                    `avgSwolf` INTEGER NOT NULL,
                    `workoutId` INTEGER NOT NULL,
                    PRIMARY KEY(`workoutId`)
                )
                """.trimIndent()
            )
        }
    }

    val MIGRATE_9_10 = object : Migration(9, 10) {

        override fun migrate(db: SupportSQLiteDatabase) {
            val table = "smlextension"

            db.doIfTableExists(table) {
                db.execSQL("ALTER TABLE $table RENAME TO ${table}_old")
            }

            db.execSQL(
                """
                CREATE TABLE IF NOT EXISTS $table (
                    workoutId INTEGER NOT NULL,
                    sml_zip BLOB,
                    PRIMARY KEY(workoutId)
                )
                """.trimIndent()
            )
            db.execSQL("DROP TABLE IF EXISTS ${table}_old")
        }
    }

    val MIGRATE_10_11 = object : Migration(10, 11) {
        private val avgStrokeRate = "avgStrokeRate"
        override fun migrate(db: SupportSQLiteDatabase) {
            val existingColumns = db.getColumnsFor(TABLE_SWIMMING_EXTENSION)
            if (!existingColumns.contains(avgStrokeRate)) {
                db.execSQL("ALTER TABLE $TABLE_SWIMMING_EXTENSION ADD COLUMN $avgStrokeRate REAL NOT NULL DEFAULT 0.0;")
            }
        }
    }

    val MIGRATE_11_12 = object : Migration(11, 12) {
        override fun migrate(db: SupportSQLiteDatabase) {
            db.execSQL(
                """
                CREATE TABLE IF NOT EXISTS `$TABLE_RANKINGS` (
                    `_id` TEXT NOT NULL,
                    `workoutKey` TEXT NOT NULL,
                    `rankingType` TEXT NOT NULL,
                    `ranking` INTEGER,
                    `numberOfWorkouts` INTEGER,
                    PRIMARY KEY(`_id`)
                )
                """.trimIndent()
            )
        }
    }

    val MIGRATE_12_13 = object : Migration(12, 13) {
        override fun migrate(db: SupportSQLiteDatabase) {
            db.execSQL(
                """
                CREATE TABLE IF NOT EXISTS `$TABLE_GOAL_DEFINITION` (
                    `id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL,
                    `userName` TEXT NOT NULL,
                    `name` TEXT,
                    `type` INTEGER NOT NULL,
                    `period` INTEGER NOT NULL,
                    `startTime` INTEGER NOT NULL,
                    `endTime` INTEGER NOT NULL,
                    `target` INTEGER NOT NULL,
                    `created` INTEGER NOT NULL,
                    `activityIds` TEXT NOT NULL
                )
                """.trimIndent()
            )
        }
    }

    @Suppress("DEPRECATION")
    val MIGRATE_13_14 = object : Migration(13, 14) {

        override fun migrate(db: SupportSQLiteDatabase) {
            val oldRoutes = try {
                fetchAllRoutes(db)
            } catch (e: Exception) {
                Timber.w(e, "Error fetching old routes")
                listOf()
            }

            db.doIfTableExists("routes") {
                db.execSQL("ALTER TABLE `routes` RENAME TO `routes_old`")
            }

            db.execSQL(
                """
                CREATE TABLE IF NOT EXISTS `routes` (
                    `_id` TEXT NOT NULL,
                    `key` TEXT NOT NULL,
                    `ownerUserName` TEXT NOT NULL,
                    `name` TEXT NOT NULL,
                    `visibility` TEXT NOT NULL,
                    `activityIds` TEXT NOT NULL,
                    `avgSpeed` REAL NOT NULL,
                    `totalDistance` REAL NOT NULL,
                    `startPoint` TEXT NOT NULL,
                    `centerPoint` TEXT NOT NULL,
                    `stopPoint` TEXT NOT NULL,
                    `locallyChanged` INTEGER NOT NULL,
                    `deleted` INTEGER NOT NULL,
                    `created` INTEGER NOT NULL,
                    `modifiedDate` INTEGER NOT NULL,
                    `watchSyncState` TEXT NOT NULL,
                    `watchSyncResponseCode` INTEGER NOT NULL,
                    `segments` BLOB NOT NULL,
                    `watchRouteId` INTEGER NOT NULL,
                    `watchEnabled` INTEGER NOT NULL,
                    PRIMARY KEY(`_id`))
                """.trimIndent()
            )

            oldRoutes
                .forEach {
                    db.insert(
                        "routes",
                        SQLiteDatabase.CONFLICT_IGNORE,
                        ContentValues().apply {
                            put(LocalRoute.ID, it.id)
                            put(LocalRoute.WATCH_ROUTE_ID, it.watchRouteId)
                            put(LocalRoute.KEY, it.key)
                            put(LocalRoute.OWNER_USER_NAME, it.ownerUserName)
                            put(LocalRoute.NAME, it.name)
                            put(LocalRoute.VISIBILITY, it.visibility)
                            put(
                                LocalRoute.ACTIVITY_IDS,
                                LocalRoute.intListJsonConverter.fromIntList(it.activityIds)
                            )
                            put(LocalRoute.AVERAGE_SPEED, it.averageSpeed)
                            put(LocalRoute.TOTAL_DISTANCE, it.totalDistance)
                            put(
                                LocalRoute.START_POINT,
                                LocalRoute.pointJsonConverter.fromPoint(it.startPoint)
                            )
                            put(
                                LocalRoute.CENTER_POINT,
                                LocalRoute.pointJsonConverter.fromPoint(it.centerPoint)
                            )
                            put(
                                LocalRoute.STOP_POINT,
                                LocalRoute.pointJsonConverter.fromPoint(it.stopPoint)
                            )
                            put(LocalRoute.LOCALLY_CHANGED, if (it.locallyChanged) 1 else 0)
                            put(LocalRoute.MODIFIED_DATE, it.modifiedDate)
                            put(LocalRoute.DELETED, if (it.deleted) 1 else 0)
                            put(LocalRoute.CREATED_DATE, it.createdDate)
                            put(LocalRoute.WATCH_SYNC_STATE, it.watchSyncState)
                            put(LocalRoute.WATCH_SYNC_RESPONSE_CODE, it.watchSyncResponseCode)
                            put(LocalRoute.WATCH_ENABLED, if (it.watchEnabled) 1 else 0)
                            put(
                                LocalRoute.SEGMENTS,
                                RouteSegmentProtoConverter().fromRouteSegment(it.segments)
                            )
                        }
                    )
                }

            db.execSQL("DROP TABLE IF EXISTS `routes_old`")
        }

        private fun fetchAllRoutes(db: SupportSQLiteDatabase): List<LocalRouteUpToVersion13> {
            db.query("SELECT * FROM `routes`").use { cursor ->
                val routes = mutableListOf<LocalRouteUpToVersion13>()
                cursor.forEach { routes.add(LocalRouteUpToVersion13(cursor)) }
                return routes
            }
        }
    }

    val MIGRATE_14_15 = object : Migration(14, 15) {
        override fun migrate(db: SupportSQLiteDatabase) {
            val existingColumns = db.getColumnsFor(TABLE_SLEEP_SEGMENTS)
            if (!existingColumns.contains(LocalSleepSegment.COLUMN_BEDTIME_START)) {
                db.execSQL("ALTER TABLE $TABLE_SLEEP_SEGMENTS ADD COLUMN ${LocalSleepSegment.COLUMN_BEDTIME_START} INTEGER;")
            }

            if (!existingColumns.contains(LocalSleepSegment.COLUMN_BEDTIME_END)) {
                db.execSQL("ALTER TABLE $TABLE_SLEEP_SEGMENTS ADD COLUMN ${LocalSleepSegment.COLUMN_BEDTIME_END} INTEGER;")
            }
        }
    }

    val MIGRATE_15_16 = object : Migration(15, 16) {
        override fun migrate(db: SupportSQLiteDatabase) {
            db.execSQL(
                """
                CREATE TABLE IF NOT EXISTS $TABLE_WORKOUT_ATTRIBUTES_UPDATE
                (${LocalWorkoutAttributesUpdate.WORKOUT_ID} INTEGER NOT NULL,
                ${LocalWorkoutAttributesUpdate.OWNER_USERNAME} TEXT NOT NULL,
                ${LocalWorkoutAttributesUpdate.WORKOUT_ATTRIBUTES} TEXT,
                ${LocalWorkoutAttributesUpdate.FIELDS} TEXT NOT NULL,
                PRIMARY KEY(workoutId))
                """.trimIndent()
            )
        }
    }

    val MIGRATE_16_17 = object : Migration(16, 17) {
        override fun migrate(db: SupportSQLiteDatabase) {
            val existingColumns = db.getColumnsFor(TABLE_WORKOUT_ATTRIBUTES_UPDATE)
            if (!existingColumns.contains(LocalWorkoutAttributesUpdate.REQUIRES_USER_CONFIRMATION)) {
                db.execSQL("ALTER TABLE $TABLE_WORKOUT_ATTRIBUTES_UPDATE ADD COLUMN ${LocalWorkoutAttributesUpdate.REQUIRES_USER_CONFIRMATION} INTEGER NOT NULL DEFAULT 0;")
            }
        }
    }

    val MIGRATE_17_18 = object : Migration(17, 18) {
        override fun migrate(db: SupportSQLiteDatabase) {
            db.execSQL(
                """
                CREATE TABLE IF NOT EXISTS `$TABLE_WEATHER_EXTENSION` (
                    `workoutId` INTEGER NOT NULL,
                    `airPressure` REAL,
                    `cloudiness` INTEGER,
                    `groundLevelAirPressure` REAL,
                    `humidity` INTEGER,
                    `rainVolume1h` REAL,
                    `rainVolume3h` REAL,
                    `seaLevelAirPressure` REAL,
                    `snowVolume1h` REAL,
                    `snowVolume3h` REAL,
                    `temperature` REAL,
                    `weatherIcon` TEXT,
                    `windDirection` REAL,
                    `windSpeed` REAL,
                    PRIMARY KEY(`workoutId`)
                )
                """.trimIndent()
            )
        }
    }

    val MIGRATE_18_19 = object : Migration(18, 19) {
        override fun migrate(db: SupportSQLiteDatabase) {
            db.execSQL(
                """
                CREATE TABLE IF NOT EXISTS `$TABLE_ACHIEVEMENTS` (
                    `id` TEXT NOT NULL,
                    `workoutKey` TEXT NOT NULL,
                    `activityType` INTEGER NOT NULL,
                    `timestamp` INTEGER NOT NULL,
                    `cumulativeAchievements` TEXT NOT NULL,
                    `personalBestAchievements` TEXT NOT NULL,
                    PRIMARY KEY(`id`)
                )
                """.trimIndent()
            )
        }
    }

    val MIGRATE_19_20 = object : Migration(19, 20) {
        override fun migrate(db: SupportSQLiteDatabase) {
            db.execSQL("DROP TABLE IF EXISTS smlextension")
        }
    }

    val MIGRATE_20_21 = object : Migration(20, 21) {
        override fun migrate(db: SupportSQLiteDatabase) {
            // create SummaryExtension db table
            db.execSQL(
                """
                CREATE TABLE IF NOT EXISTS `$TABLE_SUMMARY_EXTENSION` (
                    `workoutId` INTEGER NOT NULL,
                    `pte` REAL,
                    `feeling` INTEGER,
                    `avgTemperature` REAL,
                    `peakEpoc` REAL,
                    `avgPower` REAL,
                    `avgCadence` REAL,
                    `avgSpeed` REAL,
                    `ascentTime` REAL,
                    `descentTime` REAL,
                    `performanceLevel` REAL,
                    `recoveryTime` INTEGER,
                    `descent` REAL,
                    `ascent` REAL,
                    `deviceHardwareVersion` TEXT,
                    `deviceSoftwareVersion` TEXT,
                    `deviceName` TEXT,
                    `deviceSerialNumber` TEXT,
                    `deviceManufacturer` TEXT,
                    `exerciseId` TEXT,
                    `zapps` TEXT NOT NULL,
                    PRIMARY KEY(`workoutId`)
                )
                """.trimIndent()
            )

            // Update all routes without any activityIds to have RUNNING as activityIds.
            db.execSQL(
                """
                UPDATE `$TABLE_ROUTES`
                SET activityIds='${arrayOf(1).contentToString()}'
                WHERE activityIds='${arrayOf<Int>().contentToString()}'
                """.trimIndent()
            )
        }
    }

    val MIGRATE_21_22 = object : Migration(21, 22) {
        private val heartrate = "heartrate"
        override fun migrate(db: SupportSQLiteDatabase) {
            val existingColumns = db.getColumnsFor(TABLE_TREND_DATA)
            if (!existingColumns.contains(heartrate)) {
                db.execSQL("ALTER TABLE $TABLE_TREND_DATA ADD COLUMN $heartrate REAL NOT NULL DEFAULT 0.0;")
            }
        }
    }

    val MIGRATE_22_23 = object : Migration(22, 23) {
        override fun migrate(db: SupportSQLiteDatabase) {
            db.execSQL(
                """
                CREATE TABLE IF NOT EXISTS `$TABLE_RECOVERY_DATA` (
                    `serial` TEXT NOT NULL,
                    `timestamp_seconds` INTEGER NOT NULL,
                    `balance` INTEGER NOT NULL,
                    `stress_state` INTEGER NOT NULL,
                    `synced_status` INTEGER NOT NULL,
                    `timestamp_iso` TEXT NOT NULL,
                    PRIMARY KEY(`timestamp_seconds`)
                )
                """.trimIndent()
            )
        }
    }

    val MIGRATE_23_24 = object : Migration(23, 24) {
        override fun migrate(db: SupportSQLiteDatabase) {
            db.execSQL(
                """
                CREATE TABLE IF NOT EXISTS `$TABLE_POIS` (
                    `creation` INTEGER NOT NULL,
                    `modified` INTEGER NOT NULL,
                    `longitude` REAL NOT NULL,
                    `latitude` REAL NOT NULL,
                    `altitude` REAL,
                    `name` TEXT,
                    `type` INTEGER,
                    `activityId` INTEGER,
                    `country` TEXT,
                    `locality` TEXT,
                    `watchEnabled` INTEGER NOT NULL,
                    `key` TEXT,
                    `syncState` TEXT NOT NULL,
                    `deleted` INTEGER NOT NULL,
                    `remoteSyncErrorCode` INTEGER,
                    `watchSyncErrorCode` INTEGER,
                    PRIMARY KEY(`creation`)
                )
                """.trimIndent()
            )
            db.execSQL(
                """
                CREATE TABLE IF NOT EXISTS `$TABLE_POI_SYNC_LOG` (
                    `_id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL,
                    `timestamp` INTEGER NOT NULL,
                    `timestamp_iso` TEXT NOT NULL,
                    `event` TEXT NOT NULL,
                    `shown` INTEGER,
                    `metadata` TEXT
                )
                """.trimIndent()
            )
        }
    }

    val MIGRATE_24_25 = object : Migration(24, 25) {
        override fun migrate(db: SupportSQLiteDatabase) {
            val existingColumns = db.getColumnsFor(TABLE_SLEEP_SEGMENTS)
            if (!existingColumns.contains(LocalSleepSegmentOldDBv35.COLUMN_QUALITY_SCORE)) {
                db.execSQL("ALTER TABLE $TABLE_SLEEP_SEGMENTS ADD COLUMN `${LocalSleepSegmentOldDBv35.COLUMN_QUALITY_SCORE}` INTEGER;")
            }
            if (!existingColumns.contains(LocalSleepSegmentOldDBv35.COLUMN_REM_SLEEP_DURATION_SECONDS)) {
                db.execSQL("ALTER TABLE $TABLE_SLEEP_SEGMENTS ADD COLUMN `${LocalSleepSegmentOldDBv35.COLUMN_REM_SLEEP_DURATION_SECONDS}` REAL;")
            }
            if (!existingColumns.contains(LocalSleepSegmentOldDBv35.COLUMN_LIGHT_SLEEP_DURATION_SECONDS)) {
                db.execSQL("ALTER TABLE $TABLE_SLEEP_SEGMENTS ADD COLUMN `${LocalSleepSegmentOldDBv35.COLUMN_LIGHT_SLEEP_DURATION_SECONDS}` REAL;")
            }
            if (!existingColumns.contains(LocalSleepSegmentOldDBv35.COLUMN_SLEEP_FEEDBACK_ID)) {
                db.execSQL("ALTER TABLE $TABLE_SLEEP_SEGMENTS ADD COLUMN `${LocalSleepSegmentOldDBv35.COLUMN_SLEEP_FEEDBACK_ID}` INTEGER;")
            }
            if (!existingColumns.contains(LocalSleepSegmentOldDBv35.COLUMN_SLEEP_INSIGHTS_ID)) {
                db.execSQL("ALTER TABLE $TABLE_SLEEP_SEGMENTS ADD COLUMN `${LocalSleepSegmentOldDBv35.COLUMN_SLEEP_INSIGHTS_ID}` INTEGER;")
            }
            if (!existingColumns.contains(LocalSleepSegmentOldDBv35.COLUMN_BODY_RESOURCES_FEEDBACK_ID)) {
                db.execSQL("ALTER TABLE $TABLE_SLEEP_SEGMENTS ADD COLUMN `${LocalSleepSegmentOldDBv35.COLUMN_BODY_RESOURCES_FEEDBACK_ID}` INTEGER;")
            }
            if (!existingColumns.contains(LocalSleepSegmentOldDBv35.COLUMN_BODY_RESOURCES_INSIGHT_ID)) {
                db.execSQL("ALTER TABLE $TABLE_SLEEP_SEGMENTS ADD COLUMN `${LocalSleepSegmentOldDBv35.COLUMN_BODY_RESOURCES_INSIGHT_ID}` INTEGER;")
            }
            if (!existingColumns.contains(LocalSleepSegmentOldDBv35.COLUMN_SLEEP_REGULARITY_INSIGHT_ID)) {
                db.execSQL("ALTER TABLE $TABLE_SLEEP_SEGMENTS ADD COLUMN `${LocalSleepSegmentOldDBv35.COLUMN_SLEEP_REGULARITY_INSIGHT_ID}` INTEGER;")
            }
            if (!existingColumns.contains(LocalSleepSegmentOldDBv35.COLUMN_SLEEP_ID)) {
                db.execSQL("ALTER TABLE $TABLE_SLEEP_SEGMENTS ADD COLUMN `${LocalSleepSegmentOldDBv35.COLUMN_SLEEP_ID}` INTEGER;")
            }
        }
    }

    val MIGRATE_25_26 = object : Migration(25, 26) {
        private val isInProgress = "isInProgress"
        override fun migrate(db: SupportSQLiteDatabase) {
            val existingColumns = db.getColumnsFor(TABLE_ROUTES)
            if (!existingColumns.contains(isInProgress)) {
                db.execSQL("ALTER TABLE $TABLE_ROUTES ADD COLUMN $isInProgress INTEGER NOT NULL DEFAULT 0;")
            }
        }
    }

    val MIGRATE_26_27 = object : Migration(26, 27) {
        override fun migrate(db: SupportSQLiteDatabase) {
            val existingColumns = db.getColumnsFor(TABLE_TREND_DATA)
            if (!existingColumns.contains(LocalTrendData.COLUMN_HR_MIN)) {
                db.execSQL("ALTER TABLE $TABLE_TREND_DATA ADD COLUMN ${LocalTrendData.COLUMN_HR_MIN} REAL;")
            }
            if (!existingColumns.contains(LocalTrendData.COLUMN_HR_MAX)) {
                db.execSQL("ALTER TABLE $TABLE_TREND_DATA ADD COLUMN ${LocalTrendData.COLUMN_HR_MAX} REAL;")
            }
        }
    }

    val MIGRATE_27_28 = object : Migration(27, 28) {
        override fun migrate(db: SupportSQLiteDatabase) {
            val existingColumns = db.getColumnsFor(TABLE_TREND_DATA)
            if (!existingColumns.contains(LocalTrendData.COLUMN_SPO2)) {
                db.execSQL("ALTER TABLE $TABLE_TREND_DATA ADD COLUMN ${LocalTrendData.COLUMN_SPO2} REAL;")
            }
            if (!existingColumns.contains(LocalTrendData.COLUMN_ALTITUDE)) {
                db.execSQL("ALTER TABLE $TABLE_TREND_DATA ADD COLUMN ${LocalTrendData.COLUMN_ALTITUDE} REAL;")
            }
        }
    }

    val MIGRATE_28_29 = object : Migration(28, 29) {
        override fun migrate(db: SupportSQLiteDatabase) {
            val existingColumns = db.getColumnsFor("routes")
            if (!existingColumns.contains("ascent")) {
                calculateAndAddAscent(db)
            }
        }

        private fun calculateAndAddAscent(db: SupportSQLiteDatabase) {
            db.execSQL("ALTER TABLE `routes` RENAME TO `routes_old_28`")

            db.execSQL(
                """CREATE TABLE IF NOT EXISTS `routes` (
                    | `_id` TEXT NOT NULL,
                    | `watchRouteId` INTEGER NOT NULL,
                    | `key` TEXT NOT NULL,
                    | `ownerUserName` TEXT NOT NULL,
                    | `name` TEXT NOT NULL,
                    | `visibility` TEXT NOT NULL,
                    | `activityIds` TEXT NOT NULL,
                    | `avgSpeed` REAL NOT NULL,
                    | `totalDistance` REAL NOT NULL,
                    | `ascent` REAL NOT NULL,
                    | `startPoint` TEXT NOT NULL,
                    | `centerPoint` TEXT NOT NULL,
                    | `stopPoint` TEXT NOT NULL,
                    | `locallyChanged` INTEGER NOT NULL,
                    | `modifiedDate` INTEGER NOT NULL,
                    | `deleted` INTEGER NOT NULL,
                    | `created` INTEGER NOT NULL,
                    | `watchSyncState` TEXT NOT NULL,
                    | `watchSyncResponseCode` INTEGER NOT NULL,
                    | `watchEnabled` INTEGER NOT NULL,
                    | `segments` BLOB NOT NULL,
                    | `isInProgress` INTEGER NOT NULL,
                    | PRIMARY KEY(`_id`))
                """.trimMargin()
            )

            val routeIds = mutableListOf<String>()
            db.query("SELECT `_id` FROM `routes_old_28`").use { cursor ->
                cursor.forEach {
                    routeIds += cursor.getString("_id")
                }
            }

            val converter = RouteSegmentProtoConverter()

            for (routeId in routeIds) {
                // we have to be careful not to repeat the exact same migration text in migration 13->14
                // due to AOSP bug caching queries by string hashcode https://issuetracker.google.com/issues/271083856
                db.query("SELECT * FROM `routes_old_28` WHERE `_id`='$routeId'").use { cursor ->
                    cursor.moveToFirst()
                    val verticalDelta = try {
                        val segmentsBlob = cursor.getBlob("segments")
                        val segments = converter.toRouteSegment(segmentsBlob)
                        LocalRouteVerticalDeltaCalc.calculateVerticalDelta(segments)
                    } catch (e: Exception) {
                        Timber.w(e, "Error calculating ascend in DB migration 28 -> 29")
                        null
                    }
                    val ascent = verticalDelta?.ascent ?: 0.0
                    db.insert(
                        "routes",
                        SQLiteDatabase.CONFLICT_IGNORE,
                        contentValuesOf(
                            "_id" to cursor.getString("_id"),
                            "watchRouteId" to cursor.getInt("watchRouteId"),
                            "key" to cursor.getString("key"),
                            "ownerUserName" to cursor.getString("ownerUserName"),
                            "name" to cursor.getString("name"),
                            "visibility" to cursor.getString("visibility"),
                            "activityIds" to cursor.getString("activityIds"),
                            "avgSpeed" to cursor.getDouble("avgSpeed"),
                            "totalDistance" to cursor.getDouble("totalDistance"),
                            "ascent" to ascent,
                            "startPoint" to cursor.getString("startPoint"),
                            "centerPoint" to cursor.getString("centerPoint"),
                            "stopPoint" to cursor.getString("stopPoint"),
                            "locallyChanged" to cursor.getInt("locallyChanged"),
                            "modifiedDate" to cursor.getLong("modifiedDate"),
                            "deleted" to cursor.getInt("deleted"),
                            "created" to cursor.getLong("created"),
                            "watchSyncState" to cursor.getString("watchSyncState"),
                            "watchSyncResponseCode" to cursor.getInt("watchSyncResponseCode"),
                            "watchEnabled" to cursor.getInt("watchEnabled"),
                            "segments" to cursor.getBlob("segments"),
                            "isInProgress" to cursor.getInt("isInProgress"),
                        )
                    )
                }
            }
            db.execSQL("DROP TABLE `routes_old_28`")
        }
    }

    val MIGRATE_29_30 = object : Migration(29, 30) {
        override fun migrate(db: SupportSQLiteDatabase) {
            val existingColumns = db.getColumnsFor(TABLE_ROUTES)
            if (!existingColumns.contains(LocalRoute.TURN_WAYPOINTS_ENABLED)) {
                db.execSQL("ALTER TABLE $TABLE_ROUTES ADD COLUMN ${LocalRoute.TURN_WAYPOINTS_ENABLED} INTEGER NOT NULL DEFAULT 0;")
            }
        }
    }

    val MIGRATE_30_31 = object : Migration(30, 31) {
        override fun migrate(db: SupportSQLiteDatabase) {
            db.execSQL(
                """
                CREATE TABLE IF NOT EXISTS `$TABLE_GEAR` (
                    `${LocalGear.SERIAL_NUMBER}` TEXT NOT NULL,
                    `${LocalGear.MANUFACTURER}` TEXT NOT NULL,
                    `${LocalGear.NAME}` TEXT NOT NULL,
                    `${LocalGear.SOFTWARE_VERSION}` TEXT NOT NULL,
                    `${LocalGear.HARDWARE_VERSION}` TEXT NOT NULL,
                    `${LocalGear.LAST_SYNC_DATE}` INTEGER NOT NULL,
                    `${LocalGear.FIRST_SYNC_DATE}` INTEGER,
                    PRIMARY KEY(`${LocalGear.SERIAL_NUMBER}`)
                )
                """.trimIndent()
            )
        }
    }

    val MIGRATE_31_32 = object : Migration(31, 32) {
        override fun migrate(db: SupportSQLiteDatabase) {
            db.execSQL(
                """
                CREATE TABLE IF NOT EXISTS `$TABLE_SUUNTO_PLUS_GUIDES` (
                    `id` TEXT PRIMARY KEY NOT NULL,
                    `modified` INTEGER NOT NULL,
                    `name` TEXT NOT NULL,
                    `owner` TEXT NOT NULL,
                    `description` TEXT NOT NULL,
                    `url` TEXT NOT NULL,
                    `date` TEXT,
                    `activityIds` TEXT NOT NULL,
                    `pinned` INTEGER NOT NULL,
                    `deleted` INTEGER NOT NULL,
                    `remoteSyncErrorCode` INTEGER,
                    `watchSyncErrorCode` INTEGER
                )
                """.trimIndent()
            )

            db.execSQL(
                """
                CREATE TABLE IF NOT EXISTS `suunto_plus_guide_device_status` (
                    `serial` TEXT NOT NULL,
                    `guide_id` TEXT NOT NULL,
                    `modified` INTEGER NOT NULL,
                    `status` TEXT NOT NULL,
                    `capabilities` TEXT NOT NULL,
                    PRIMARY KEY(serial, guide_id),
                    FOREIGN KEY(guide_id) REFERENCES $TABLE_SUUNTO_PLUS_GUIDES(id) ON DELETE CASCADE
                );
                """.trimIndent()
            )

            db.execSQL(
                """
                CREATE TABLE IF NOT EXISTS `$TABLE_SUUNTO_PLUS_GUIDE_SYNC_LOG` (
                    `id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL,
                    `timestamp` INTEGER NOT NULL,
                    `timestamp_iso` TEXT NOT NULL,
                    `event` TEXT NOT NULL,
                    `metadata` TEXT
                )
                """.trimIndent()
            )

            db.execSQL(
                """
                CREATE TABLE IF NOT EXISTS `$TABLE_WATCH_CAPABILITIES` (
                    `serial` TEXT NOT NULL PRIMARY KEY,
                    `model` TEXT NOT NULL,
                    `fw` TEXT NOT NULL,
                    `hw` TEXT NOT NULL,
                    `capabilities` TEXT NOT NULL
                )
                """.trimIndent()
            )
        }
    }

    val MIGRATE_32_33 = object : Migration(32, 33) {
        override fun migrate(db: SupportSQLiteDatabase) {
            val existingColumns = db.getColumnsFor(TABLE_SUUNTO_PLUS_GUIDES)
            if (!existingColumns.contains(LocalSuuntoPlusGuide.ICON_URL)) {
                db.execSQL("ALTER TABLE $TABLE_SUUNTO_PLUS_GUIDES ADD COLUMN ${LocalSuuntoPlusGuide.ICON_URL} TEXT;")
            }
        }
    }

    val MIGRATE_33_34 = object : Migration(33, 34) {
        override fun migrate(db: SupportSQLiteDatabase) {
            val existingColumns = db.getColumnsFor("suunto_plus_guide_device_status")
            if (!existingColumns.contains(LocalSuuntoPlusPluginDeviceStatus.INTEREST_VALUE)) {
                db.execSQL("ALTER TABLE `suunto_plus_guide_device_status` ADD COLUMN ${LocalSuuntoPlusPluginDeviceStatus.INTEREST_VALUE} INTEGER;")
            }
        }
    }

    val MIGRATE_34_35 = object : Migration(34, 35) {
        override fun migrate(db: SupportSQLiteDatabase) {
            // SQLite doesn't support adding columns with a not null constraint & no default value, or adding the not null constraint after
            // filling in the values to previously created nullable column. So we're recreating the table to add a column...
            val tmpTableName = "${TABLE_ROUTES}_tmp"

            db.execSQL(
                """
                CREATE TABLE IF NOT EXISTS `$tmpTableName` (
                    `_id` TEXT NOT NULL,
                    `watchRouteId` INTEGER NOT NULL,
                    `key` TEXT NOT NULL,
                    `ownerUserName` TEXT NOT NULL,
                    `name` TEXT NOT NULL,
                    `visibility` TEXT NOT NULL,
                    `activityIds` TEXT NOT NULL,
                    `avgSpeed` REAL NOT NULL,
                    `totalDistance` REAL NOT NULL,
                    `ascent` REAL NOT NULL,
                    `startPoint` TEXT NOT NULL,
                    `centerPoint` TEXT NOT NULL,
                    `stopPoint` TEXT NOT NULL,
                    `locallyChanged` INTEGER NOT NULL,
                    `modifiedDate` INTEGER NOT NULL,
                    `segmentsModifiedDate` INTEGER NOT NULL,
                    `deleted` INTEGER NOT NULL,
                    `created` INTEGER NOT NULL,
                    `watchSyncState` TEXT NOT NULL,
                    `watchSyncResponseCode` INTEGER NOT NULL,
                    `watchEnabled` INTEGER NOT NULL,
                    `segments` BLOB NOT NULL,
                    `isInProgress` INTEGER NOT NULL,
                    `turnWaypointsEnabled` INTEGER NOT NULL,
                    PRIMARY KEY(`_id`)
                )
                """.trimIndent()
            )

            // Note that during selection from old table, segmentsModifiedDate is set to modifiedDate
            db.doIfTableExists(TABLE_ROUTES) {
                db.execSQL(
                    """
                    INSERT INTO $tmpTableName (
                        `_id`,
                        `watchRouteId`,
                        `key`,
                        `ownerUserName`,
                        `name`,
                        `visibility`,
                        `activityIds`,
                        `avgSpeed`,
                        `totalDistance`,
                        `ascent`,
                        `startPoint`,
                        `centerPoint`,
                        `stopPoint`,
                        `locallyChanged`,
                        `modifiedDate`,
                        `segmentsModifiedDate`,
                        `deleted`,
                        `created`,
                        `watchSyncState`,
                        `watchSyncResponseCode`,
                        `watchEnabled`,
                        `segments`,
                        `isInProgress`,
                        `turnWaypointsEnabled`
                    )
                    SELECT
                        `_id`,
                        `watchRouteId`,
                        `key`,
                        `ownerUserName`,
                        `name`,
                        `visibility`,
                        `activityIds`,
                        `avgSpeed`,
                        `totalDistance`,
                        `ascent`,
                        `startPoint`,
                        `centerPoint`,
                        `stopPoint`,
                        `locallyChanged`,
                        `modifiedDate`,
                        `modifiedDate`,
                        `deleted`,
                        `created`,
                        `watchSyncState`,
                        `watchSyncResponseCode`,
                        `watchEnabled`,
                        `segments`,
                        `isInProgress`,
                        `turnWaypointsEnabled`
                    FROM $TABLE_ROUTES
                    """.trimIndent()
                )
                db.execSQL("DROP TABLE $TABLE_ROUTES")
            }

            db.execSQL("ALTER TABLE $tmpTableName RENAME TO $TABLE_ROUTES")
        }
    }

    val MIGRATE_35_36 = object : Migration(35, 36) {
        override fun migrate(db: SupportSQLiteDatabase) {
            // Use a common watch plug-in device status table instead of SuuntoPlus Guide device status
            db.execSQL("DROP TABLE `suunto_plus_guide_device_status`")
            db.execSQL(
                """
                CREATE TABLE IF NOT EXISTS `suunto_plus_plugin_device_status` (
                    `serial` TEXT NOT NULL,
                    `plugin_id` TEXT NOT NULL,
                    `modified` INTEGER NOT NULL,
                    `status` TEXT NOT NULL,
                    `type` TEXT NOT NULL,
                    `interest_value` INTEGER,
                    `capabilities` TEXT NOT NULL,
                    PRIMARY KEY(serial, plugin_id)
                );
                """.trimIndent()
            )

            // Add SuuntoPlus Features
            db.execSQL(
                """
                CREATE TABLE IF NOT EXISTS `$TABLE_SUUNTO_PLUS_FEATURES` (
                    `id` TEXT NOT NULL PRIMARY KEY,
                    `modified` INTEGER NOT NULL,
                    `name` TEXT NOT NULL,
                    `owner` TEXT NOT NULL,
                    `description` TEXT NOT NULL,
                    `short_description` TEXT,
                    `url` TEXT,
                    `iconUrl` TEXT NOT NULL,
                    `enabled` INTEGER NOT NULL,
                    `expiration` INTEGER
                );
                """.trimIndent()
            )
        }
    }

    val MIGRATE_36_37 = object : Migration(36, 37) {
        override fun migrate(db: SupportSQLiteDatabase) {
            // SQLite doesn't support dropping NOT NULL constraint. So let's re-create the table to
            // drop the NOT NULL constraint for 'url' in TABLE_SUUNTO_PLUS_GUIDES.
            val tmpTableName = "${TABLE_SUUNTO_PLUS_GUIDES}_tmp"

            db.execSQL(
                """
                CREATE TABLE IF NOT EXISTS `$tmpTableName` (
                    `id` TEXT PRIMARY KEY NOT NULL,
                    `modified` INTEGER NOT NULL,
                    `name` TEXT NOT NULL,
                    `owner` TEXT NOT NULL,
                    `description` TEXT NOT NULL,
                    `url` TEXT,
                    `iconUrl` TEXT,
                    `date` TEXT,
                    `activityIds` TEXT,
                    `pinned` INTEGER NOT NULL,
                    `deleted` INTEGER NOT NULL,
                    `remoteSyncErrorCode` INTEGER,
                    `watchSyncErrorCode` INTEGER
                )
                """.trimIndent()
            )

            db.doIfTableExists(TABLE_SUUNTO_PLUS_GUIDES) {
                db.execSQL(
                    """
                    INSERT INTO `$tmpTableName` (
                        `id`,
                        `modified`,
                        `name`,
                        `owner`,
                        `description`,
                        `url`,
                        `iconUrl`,
                        `date`,
                        `activityIds`,
                        `pinned`,
                        `deleted`,
                        `remoteSyncErrorCode`,
                        `watchSyncErrorCode`
                    )
                    SELECT
                        `id`,
                        `modified`,
                        `name`,
                        `owner`,
                        `description`,
                        `url`,
                        `iconUrl`,
                        `date`,
                        `activityIds`,
                        `pinned`,
                        `deleted`,
                        `remoteSyncErrorCode`,
                        `watchSyncErrorCode`
                    FROM $TABLE_SUUNTO_PLUS_GUIDES
                    """.trimIndent()
                )
                db.execSQL("DROP TABLE $TABLE_SUUNTO_PLUS_GUIDES")
            }

            db.execSQL("ALTER TABLE $tmpTableName RENAME TO $TABLE_SUUNTO_PLUS_GUIDES")
        }
    }

    val MIGRATE_37_38 = object : Migration(37, 38) {
        override fun migrate(db: SupportSQLiteDatabase) {
            val existingColumns = db.getColumnsFor(TABLE_SLEEP_SEGMENTS)
            if (!existingColumns.contains(LocalSleepSegment.COLUMN_MAX_SPO2)) {
                db.execSQL("ALTER TABLE $TABLE_SLEEP_SEGMENTS ADD COLUMN ${LocalSleepSegment.COLUMN_MAX_SPO2} REAL;")
            }
            if (!existingColumns.contains(LocalSleepSegment.COLUMN_ALTITUDE)) {
                db.execSQL("ALTER TABLE $TABLE_SLEEP_SEGMENTS ADD COLUMN ${LocalSleepSegment.COLUMN_ALTITUDE} REAL;")
            }
        }
    }

    val MIGRATE_38_39 = object : Migration(38, 39) {
        override fun migrate(db: SupportSQLiteDatabase) {
            migrateTrendDataTable(db)
            migrateRecoveryDataTable(db)
            migrateSleepDataTable(db)
        }

        private fun migrateTrendDataTable(db: SupportSQLiteDatabase) {
            // rename current trenddata_v2 table
            db.execSQL("ALTER TABLE $TABLE_TREND_DATA RENAME TO ${TABLE_TREND_DATA}_old")
            // create new table with nullable 'heartrate', everything else stays the same
            db.execSQL(
                """CREATE TABLE IF NOT EXISTS `$TABLE_TREND_DATA` (
                |`serial` TEXT NOT NULL,
                |`timestamp_seconds` INTEGER NOT NULL,
                |`energy` REAL NOT NULL,
                |`steps` INTEGER NOT NULL,
                |`heartrate` REAL,
                |`synced_status` INTEGER NOT NULL,
                |`timestamp_iso` TEXT NOT NULL,
                |`hrMin` REAL,
                |`hrMax` REAL,
                |`spo2` REAL,
                |`altitude` REAL,
                |PRIMARY KEY(`timestamp_seconds`))
                """.trimMargin()
            )
            // copy all data to the new table
            db.execSQL(
                """INSERT INTO $TABLE_TREND_DATA SELECT
                |`serial`,
                |`timestamp_seconds`,
                |`energy`,
                |`steps`,
                |`heartrate`,
                |`synced_status`,
                |`timestamp_iso`,
                |`hrMin`,
                |`hrMax`,
                |`spo2`,
                |`altitude`
                |FROM ${TABLE_TREND_DATA}_old
                """.trimMargin()
            )
            // convert 'heartrate', 'hrMin', 'hrMax' to Hz
            db.execSQL(
                """UPDATE $TABLE_TREND_DATA SET
                |`heartrate` = `heartrate` / 60,
                |`hrMin` = `hrMin` / 60,
                |`hrMax` = `hrMax` / 60
                """.trimMargin()
            )
            // convert `heartrate` = 0 to NULL
            db.execSQL("UPDATE $TABLE_TREND_DATA SET `heartrate` = NULL where `heartrate` = 0")
            // drop old data
            db.execSQL("DROP TABLE ${TABLE_TREND_DATA}_old")
        }

        private fun migrateRecoveryDataTable(db: SupportSQLiteDatabase) {
            // rename current recoverydata table
            db.execSQL("ALTER TABLE $TABLE_RECOVERY_DATA RENAME TO ${TABLE_RECOVERY_DATA}_old")
            // create new table with nullable 'heartrate', everything else stays the same
            db.execSQL(
                """CREATE TABLE IF NOT EXISTS `$TABLE_RECOVERY_DATA` (
                |`serial` TEXT NOT NULL,
                |`timestamp_seconds` INTEGER NOT NULL,
                |`balance` REAL NOT NULL,
                |`stress_state` INTEGER NOT NULL,
                |`synced_status` INTEGER NOT NULL,
                |`timestamp_iso` TEXT NOT NULL,
                |PRIMARY KEY(`timestamp_seconds`))
                """.trimMargin()
            )
            // copy all data to the new table and convert balance to float 0..1
            db.execSQL(
                """INSERT INTO $TABLE_RECOVERY_DATA SELECT
                |`serial`,
                |`timestamp_seconds`,
                |`balance` / 100.0 AS `balance`,
                |`stress_state`,
                |`synced_status`,
                |`timestamp_iso`
                |FROM ${TABLE_RECOVERY_DATA}_old
                """.trimMargin()
            )
            // drop old data
            db.execSQL("DROP TABLE ${TABLE_RECOVERY_DATA}_old")
        }

        private fun migrateSleepDataTable(db: SupportSQLiteDatabase) {
            // rename current sleepsegments table
            db.execSQL("ALTER TABLE $TABLE_SLEEP_SEGMENTS RENAME TO ${TABLE_SLEEP_SEGMENTS}_old")
            // create new table for sleep with the following changes
            // quality INT->REAL
            // quality_score DROP
            // sleep_feedback_id DROP
            // sleep_insights_id DROP
            // body_resources_feedback_id DROP
            // sleep_regularity_insight_id DROP
            db.execSQL(
                """CREATE TABLE IF NOT EXISTS `$TABLE_SLEEP_SEGMENTS` (
                |`serial` TEXT NOT NULL,
                |`timestamp_seconds` INTEGER NOT NULL,
                |`quality` REAL,
                |`avg_hr` REAL,
                |`min_hr` REAL,
                |`feeling` INTEGER,
                |`duration_seconds` REAL NOT NULL,
                |`deep_sleep_duration_seconds` REAL,
                |`synced_status` INTEGER NOT NULL,
                |`timestamp_iso` TEXT NOT NULL,
                |`bedtime_start` INTEGER,
                |`bedtime_end` INTEGER,
                |`rem_sleep_duration_seconds` REAL,
                |`light_sleep_duration_seconds` REAL,
                |`body_resources_insight_id` INTEGER,
                |`sleep_id` INTEGER,
                |`max_spo2` REAL,
                |`altitude` REAL,
                |PRIMARY KEY(`timestamp_seconds`))
                """.trimMargin()
            )
            // copy kept data to the new table and convert quality to float 0..1
            db.execSQL(
                """INSERT INTO $TABLE_SLEEP_SEGMENTS SELECT
                |`serial`,
                |`timestamp_seconds`,
                |`quality` / 100.0 as `quality`,
                |`avg_hr`,
                |`min_hr`,
                |`feeling`,
                |`duration_seconds`,
                |`deep_sleep_duration_seconds`,
                |`synced_status`,
                |`timestamp_iso`,
                |`bedtime_start`,
                |`bedtime_end`,
                |`rem_sleep_duration_seconds`,
                |`light_sleep_duration_seconds`,
                |`body_resources_insight_id`,
                |`sleep_id`,
                |`max_spo2`,
                |`altitude`
                |FROM ${TABLE_SLEEP_SEGMENTS}_old
                """.trimMargin()
            )
            // drop old data
            db.execSQL("DROP TABLE ${TABLE_SLEEP_SEGMENTS}_old")
        }
    }

    val MIGRATE_39_40 = object : Migration(39, 40) {
        override fun migrate(db: SupportSQLiteDatabase) {
            db.execSQL(
                """
                CREATE TABLE IF NOT EXISTS `$TABLE_USER` (
                    `id` INTEGER PRIMARY KEY NOT NULL,
                    `key` TEXT,
                    `username` TEXT NOT NULL,
                    `session` TEXT,
                    `website` TEXT,
                    `city` TEXT,
                    `country` TEXT,
                    `profileImageUrl` TEXT,
                    `profileImageKey` TEXT,
                    `realName` TEXT,
                    `description` TEXT,
                    `followModel` INTEGER,
                    `fieldTester` INTEGER
                )
                """.trimIndent()
            )

            db.execSQL(
                """
                CREATE INDEX IF NOT EXISTS `index_user_username` ON `$TABLE_USER` (`username`)
                """.trimIndent()
            )
        }
    }

    val MIGRATE_40_41 = object : Migration(40, 41) {
        override fun migrate(db: SupportSQLiteDatabase) {
            val existingColumns = db.getColumnsFor(TABLE_SUUNTO_PLUS_FEATURES)
            if (!existingColumns.contains(LocalSuuntoPlusFeature.PLUGIN_ID)) {
                // Plug-in ID is nullable to enable migration. It will be filled in with language
                // specific Zapp file IDs at the next remote sync.
                db.execSQL("ALTER TABLE $TABLE_SUUNTO_PLUS_FEATURES ADD COLUMN ${LocalSuuntoPlusFeature.PLUGIN_ID} TEXT;")
            }
        }
    }

    val MIGRATE_41_42 = object : Migration(41, 42) {
        override fun migrate(db: SupportSQLiteDatabase) {
            db.execSQL(
                """
                CREATE TABLE IF NOT EXISTS `$TABLE_WORKOUT_HEADERS` (
                    `id` INTEGER NOT NULL,
                    `key` TEXT,
                    `totalDistance` REAL NOT NULL,
                    `maxSpeed` REAL NOT NULL,
                    `activityId` INTEGER NOT NULL,
                    `avgSpeed` REAL NOT NULL,
                    `description` TEXT,
                    `startPosition` TEXT,
                    `stopPosition` TEXT,
                    `centerPosition` TEXT,
                    `startTime` INTEGER NOT NULL,
                    `stopTime` INTEGER NOT NULL,
                    `totalTime` REAL NOT NULL,
                    `energyConsumption` REAL NOT NULL,
                    `username` TEXT NOT NULL,
                    `heartRateAvg` REAL NOT NULL,
                    `heartRateAvgPercentage` REAL NOT NULL,
                    `heartRateMax` REAL NOT NULL,
                    `heartRateMaxPercentage` REAL NOT NULL,
                    `heartRateUserSetMax` REAL NOT NULL,
                    `pictureCount` INTEGER NOT NULL,
                    `viewCount` INTEGER NOT NULL,
                    `commentCount` INTEGER NOT NULL,
                    `sharingFlags` INTEGER NOT NULL,
                    `locallyChanged` INTEGER NOT NULL,
                    `deleted` INTEGER NOT NULL,
                    `manuallyCreated` INTEGER NOT NULL,
                    `averageCadence` INTEGER NOT NULL,
                    `maxCadence` INTEGER NOT NULL,
                    `polyline` TEXT,
                    `stepCount` INTEGER NOT NULL,
                    `reactionCount` INTEGER NOT NULL,
                    `totalAscent` REAL NOT NULL,
                    `totalDescent` REAL NOT NULL,
                    `recoveryTime` INTEGER NOT NULL,
                    `maxAltitude` REAL,
                    `minAltitude` REAL,
                    `seen` INTEGER NOT NULL,
                    `extensionsFetched` INTEGER NOT NULL,
                    `tss` TEXT,
                    `tssList` TEXT,
                    PRIMARY KEY(`id`)
                )
                """.trimIndent()
            )
            db.execSQL("CREATE INDEX IF NOT EXISTS `index_workout_headers_key` ON `$TABLE_WORKOUT_HEADERS` (`key`)")
            db.execSQL("CREATE INDEX IF NOT EXISTS `index_workout_headers_startTime` ON `$TABLE_WORKOUT_HEADERS` (`startTime`)")
        }
    }

    val MIGRATE_42_43 = object : Migration(42, 43) {
        override fun migrate(db: SupportSQLiteDatabase) {
            val existingColumns = db.getColumnsFor(TABLE_SUMMARY_EXTENSION)
            if (!existingColumns.contains(LocalSummaryExtension.COLUMN_PRODUCT_TYPE)) {
                db.execSQL("ALTER TABLE $TABLE_SUMMARY_EXTENSION ADD COLUMN ${LocalSummaryExtension.COLUMN_PRODUCT_TYPE} TEXT;")
            }
            if (!existingColumns.contains(LocalSummaryExtension.COLUMN_DISPLAY_NAME)) {
                db.execSQL("ALTER TABLE $TABLE_SUMMARY_EXTENSION ADD COLUMN ${LocalSummaryExtension.COLUMN_DISPLAY_NAME} TEXT;")
            }

            db.query(
                """
                SELECT summary.${LocalSummaryExtension.COLUMN_WORKOUT_ID}, summary.${LocalSummaryExtension.COLUMN_DEVICE_NAME}
                FROM $TABLE_SUMMARY_EXTENSION summary, $TABLE_WORKOUT_HEADERS headers
                WHERE summary.${LocalSummaryExtension.COLUMN_WORKOUT_ID} = headers.${LocalWorkoutHeader.ID}
                """.trimIndent()
            ).use { cursor ->
                cursor.forEach {
                    val workoutId = cursor.getInt(LocalSummaryExtension.COLUMN_WORKOUT_ID)
                    val deviceName =
                        cursor.getStringOrNull(LocalSummaryExtension.COLUMN_DEVICE_NAME)

                    if (!deviceName.isNullOrBlank()) {
                        val productType = ProductType.fromVariantName(deviceName)
                        val suuntoDeviceType = SuuntoDeviceType.fromVariantName(deviceName)
                        var displayName: String? = null

                        // Unrecognized is considered as not released
                        if (suuntoDeviceType.isReleased) {
                            displayName = suuntoDeviceType.displayName
                        }

                        if (productType != null && !displayName.isNullOrBlank()) {
                            val contentValues = ContentValues().apply {
                                put(LocalSummaryExtension.COLUMN_PRODUCT_TYPE, productType.name)
                                put(LocalSummaryExtension.COLUMN_DISPLAY_NAME, displayName)
                            }
                            db.update(
                                TABLE_SUMMARY_EXTENSION,
                                SQLiteDatabase.CONFLICT_IGNORE,
                                contentValues,
                                "${LocalSummaryExtension.COLUMN_WORKOUT_ID}=?",
                                arrayOf("$workoutId")
                            )
                        } else {
                            Timber.e("Error migrating $workoutId, $deviceName")
                        }
                    }
                }
            }
        }
    }

    val MIGRATE_43_44 = object : Migration(43, 44) {
        override fun migrate(db: SupportSQLiteDatabase) {
            val existingColumns = db.getColumnsFor(TABLE_WORKOUT_HEADERS)
            if (!existingColumns.contains("isCommute")) {
                db.execSQL("ALTER TABLE $TABLE_WORKOUT_HEADERS ADD COLUMN isCommute INTEGER NOT NULL DEFAULT 0;")
            }
        }
    }

    val MIGRATE_44_45 = object : Migration(44, 45) {
        override fun migrate(db: SupportSQLiteDatabase) {
            db.execSQL(
                """
                CREATE TABLE IF NOT EXISTS `$TABLE_SUUNTO_PLUS_GUIDE_CATALOGUE` (
                    `id` TEXT PRIMARY KEY NOT NULL,
                    `name` TEXT NOT NULL,
                    `owner` TEXT NOT NULL,
                    `description` TEXT NOT NULL,
                    `shortDescription` TEXT,
                    `url` TEXT,
                    `iconUrl` TEXT,
                    `date` TEXT,
                    `activityIds` TEXT NOT NULL
                )
                """.trimIndent()
            )

            if (LocalSuuntoPlusGuide.CATALOGUE_ID !in db.getColumnsFor(TABLE_SUUNTO_PLUS_GUIDES)) {
                // Add catalogue ID for guides added from catalogue (Suunto Guides).
                db.execSQL("ALTER TABLE $TABLE_SUUNTO_PLUS_GUIDES ADD COLUMN ${LocalSuuntoPlusGuide.CATALOGUE_ID} TEXT;")
            }
        }
    }

    val MIGRATE_45_46 = object : Migration(45, 46) {
        override fun migrate(db: SupportSQLiteDatabase) {
            db.execSQL("DROP TABLE IF EXISTS `$TABLE_SUUNTO_PLUS_GUIDE_CATALOGUE`")
        }
    }

    val MIGRATE_69_70 = object : Migration(69, 70) {
        override fun migrate(db: SupportSQLiteDatabase) {
            val suuntoPlusFeaturesColumns = db.getColumnsFor(TABLE_SUUNTO_PLUS_FEATURES)
            if (!suuntoPlusFeaturesColumns.contains(LocalSuuntoPlusFeature.LOCALIZED_RICHTEXT)) {
                db.execSQL(
                    "ALTER TABLE $TABLE_SUUNTO_PLUS_FEATURES ADD COLUMN ${LocalSuuntoPlusFeature.LOCALIZED_RICHTEXT} TEXT;"
                )
            }
            if (!suuntoPlusFeaturesColumns.contains(LocalSuuntoPlusFeature.LOCALIZED_RICHTEXT_AUTOMATICALLY)) {
                db.execSQL(
                    "ALTER TABLE $TABLE_SUUNTO_PLUS_FEATURES ADD COLUMN ${LocalSuuntoPlusFeature.LOCALIZED_RICHTEXT_AUTOMATICALLY} INTEGER;"
                )
            }
        }
    }

    val MIGRATE_70_71 = object : Migration(70, 71) {
        private val descent = "descent"
        override fun migrate(db: SupportSQLiteDatabase) {
            val existingColumns = db.getColumnsFor(TABLE_ROUTES)
            if (!existingColumns.contains(descent)) {
                db.execSQL("ALTER TABLE $TABLE_ROUTES ADD COLUMN $descent REAL NOT NULL DEFAULT 0.0;")
            }
        }
    }

    val MIGRATE_71_72 = object : Migration(71, 72) {
        override fun migrate(db: SupportSQLiteDatabase) {
            val routeIds = mutableListOf<String>()
            db.query("SELECT `_id` FROM $TABLE_ROUTES").use { cursor ->
                cursor.forEach {
                    routeIds += cursor.getString("_id")
                }
            }

            val converter = RouteSegmentProtoConverter()
            for (routeId in routeIds) {
                db.query("SELECT * FROM $TABLE_ROUTES WHERE `_id`='$routeId'").use { cursor ->
                    cursor.moveToFirst()
                    try {
                        val segmentsBlob = cursor.getBlob(LocalRoute.SEGMENTS)
                        val segments = converter.toRouteSegment(segmentsBlob)
                        val segmentsWithDescent = segments.map { segment ->
                            val verticalData = try {
                                LocalRouteVerticalDeltaCalc.calculateCumulativeVerticalDelta(segment.routePoints)
                            } catch (e: Exception) {
                                Timber.w(
                                    e,
                                    "Error calculating descent for route:$routeId in DB migration 71 -> 72"
                                )
                                null
                            }
                            segment.copy(descent = verticalData?.descent ?: 0.0)
                        }
                        val verticalData =
                            LocalRouteVerticalDeltaCalc.calculateVerticalDelta(segmentsWithDescent)
                        val descent = verticalData?.descent ?: 0.0

                        val contentValues = ContentValues().apply {
                            put(LocalRoute.DESCENT, descent)
                            put(
                                LocalRoute.SEGMENTS,
                                converter.fromRouteSegment(segmentsWithDescent)
                            )
                        }
                        db.update(
                            TABLE_ROUTES,
                            SQLiteDatabase.CONFLICT_IGNORE,
                            contentValues,
                            "${LocalRoute.ID}=?",
                            arrayOf(routeId)
                        )
                    } catch (e: Exception) {
                        Timber.w(
                            e,
                            "Error in DB migration 71 -> 72 for route:$routeId"
                        )
                    }
                }
            }
        }
    }

    val MIGRATE_72_73 = object : Migration(72, 73) {
        override fun migrate(db: SupportSQLiteDatabase) {
            val routeIds = mutableListOf<String>()
            db.query("SELECT `_id` FROM $TABLE_ROUTES").use { cursor ->
                cursor.forEach {
                    routeIds += cursor.getString("_id")
                }
            }

            val converter = RouteSegmentProtoConverter()
            for (routeId in routeIds) {
                db.query("SELECT * FROM $TABLE_ROUTES WHERE `_id`='$routeId'").use { cursor ->
                    cursor.moveToFirst()
                    try {
                        val segmentsBlob = cursor.getBlob(LocalRoute.SEGMENTS)
                        val segments = LocalRouteVerticalDeltaCalc.recalculateVerticalData(
                            converter.toRouteSegment(segmentsBlob)
                        )

                        val verticalData =
                            LocalRouteVerticalDeltaCalc.calculateVerticalDelta(segments)
                        val ascent = verticalData?.ascent ?: 0.0
                        val descent = verticalData?.descent ?: 0.0

                        val contentValues = ContentValues().apply {
                            put(LocalRoute.ASCENT, ascent)
                            put(LocalRoute.DESCENT, descent)
                            put(LocalRoute.SEGMENTS, converter.fromRouteSegment(segments))
                        }
                        db.update(
                            TABLE_ROUTES,
                            SQLiteDatabase.CONFLICT_IGNORE,
                            contentValues,
                            "${LocalRoute.ID}=?",
                            arrayOf(routeId)
                        )
                    } catch (e: Exception) {
                        Timber.w(
                            e,
                            "Error recalculating ascent/descent for segments for route:$routeId in DB migration 72 -> 73"
                        )
                    }
                }
            }
        }
    }

    val MIGRATE_83_84 = object : Migration(83, 84) {
        override fun migrate(db: SupportSQLiteDatabase) {
            db.query("SELECT ${LocalMenstrualCycle.LOCAL_ID}, ${LocalMenstrualCycle.INCLUDED_DATES} FROM $TABLE_MENSTRUAL_CYCLE")
                .use { cursor ->
                    try {
                        cursor.forEach {
                            val localId = cursor.getString(LocalMenstrualCycle.LOCAL_ID)
                            val includedDatesJson =
                                cursor.getString(LocalMenstrualCycle.INCLUDED_DATES)
                            try {
                                val updatedDatesJson = convertToNewDateFormat(includedDatesJson)
                                val updateStatement =
                                    "UPDATE $TABLE_MENSTRUAL_CYCLE SET ${LocalMenstrualCycle.INCLUDED_DATES} = ? WHERE ${LocalMenstrualCycle.LOCAL_ID} = ?"
                                db.execSQL(updateStatement, arrayOf(updatedDatesJson, localId))
                            } catch (e: Exception) {
                                Timber.w(
                                    e,
                                    "Error occurred when converting the date format: $includedDatesJson"
                                )
                                val deleteStatement =
                                    "DELETE FROM $TABLE_MENSTRUAL_CYCLE WHERE ${LocalMenstrualCycle.LOCAL_ID} = ?"
                                db.execSQL(deleteStatement, arrayOf(localId))
                            }
                        }
                    } catch (e: Exception) {
                        Timber.w(e, "Error in DB 83 -> 84.")
                        db.execSQL("DELETE FROM $TABLE_MENSTRUAL_CYCLE")
                    }
                }
        }

        fun convertToNewDateFormat(oldJson: String): String {
            val moshi = Moshi.Builder().build()
            val jsonAdapter =
                moshi.adapter<List<LocalMenstrualCycle.OldDateFormat>>(
                    Types.newParameterizedType(List::class.java, LocalMenstrualCycle.OldDateFormat::class.java)
                )
            val newDateList = jsonAdapter.fromJson(oldJson)?.map {
                LocalDate.of(it.year, it.month, it.day).format(DateTimeFormatter.ISO_LOCAL_DATE)
            } ?: emptyList()
            val newJsonAdapter = moshi.adapter<List<String>>(
                Types.newParameterizedType(List::class.java, String::class.java)
            )
            return newJsonAdapter.toJson(newDateList)
        }
    }

    val MIGRATE_84_85 = object : Migration(84, 85) {
        override fun migrate(db: SupportSQLiteDatabase) {
            db.doIfTableExists(TABLE_WORKOUT_HEADERS) {
                val existingColumns = db.getColumnsFor(TABLE_WORKOUT_HEADERS)
                if (!existingColumns.contains(LocalWorkoutHeader.SUUNTO_TAGS)) {
                    db.execSQL("ALTER TABLE $TABLE_WORKOUT_HEADERS ADD COLUMN ${LocalWorkoutHeader.SUUNTO_TAGS} TEXT;")
                }
            }

            val converter = LocalSuuntoTagConverter()
            db.query("""
                SELECT ${LocalWorkoutHeader.ID}, isCommute, impact_cardio, impact_muscular 
                FROM $TABLE_WORKOUT_HEADERS
                WHERE isCommute == 1 OR impact_cardio IS NOT NULL OR impact_muscular IS NOT NULL
            """.trimIndent()).use { cursor ->
                cursor.forEach {
                    val workoutId = cursor.getString(LocalWorkoutHeader.ID)
                    val isCommute = cursor.getInt("isCommute") == 1
                    val impactCardio = cursor.getStringOrNull("impact_cardio")
                    val impactMuscular = cursor.getStringOrNull("impact_muscular")
                    val suuntoTags = buildList {
                        if (isCommute) {
                            add(LocalSuuntoTag.COMMUTE)
                        }
                        impactCardio?.let(LocalSuuntoTag::valueOf)?.let(::add)
                        impactMuscular?.let(LocalSuuntoTag::valueOf)?.let(::add)
                    }.let(converter::fromLocalSuuntoTagList)

                    db.execSQL("""
                        UPDATE $TABLE_WORKOUT_HEADERS
                        SET ${LocalWorkoutHeader.SUUNTO_TAGS} = '$suuntoTags'
                        WHERE ${LocalWorkoutHeader.ID} = $workoutId
                    """.trimIndent())
                }
            }

            // NOTE: We should drop those columns, but SQLite doesn't support DROP COLUMN in older versions.
            // db.execSQL("ALTER TABLE $TABLE_WORKOUT_HEADERS DROP COLUMN isCommute;")
            // db.execSQL("ALTER TABLE $TABLE_WORKOUT_HEADERS DROP COLUMN impact_cardio;")
            // db.execSQL("ALTER TABLE $TABLE_WORKOUT_HEADERS DROP COLUMN impact_muscular;")
        }
    }

    // Add new migrations here
    val list = arrayOf(
        MIGRATE_1_2,
        MIGRATE_2_3,
        MIGRATE_3_4,
        MIGRATE_4_5,
        MIGRATE_5_6,
        MIGRATE_6_7,
        MIGRATE_7_8,
        MIGRATE_8_9,
        MIGRATE_9_10,
        MIGRATE_10_11,
        MIGRATE_11_12,
        MIGRATE_12_13,
        MIGRATE_13_14,
        MIGRATE_14_15,
        MIGRATE_15_16,
        MIGRATE_16_17,
        MIGRATE_17_18,
        MIGRATE_18_19,
        MIGRATE_19_20,
        MIGRATE_20_21,
        MIGRATE_21_22,
        MIGRATE_22_23,
        MIGRATE_23_24,
        MIGRATE_24_25,
        MIGRATE_25_26,
        MIGRATE_26_27,
        MIGRATE_27_28,
        MIGRATE_28_29,
        MIGRATE_29_30,
        MIGRATE_30_31,
        MIGRATE_31_32,
        MIGRATE_32_33,
        MIGRATE_33_34,
        MIGRATE_34_35,
        MIGRATE_35_36,
        MIGRATE_36_37,
        MIGRATE_37_38,
        MIGRATE_38_39,
        MIGRATE_39_40,
        MIGRATE_40_41,
        MIGRATE_41_42,
        MIGRATE_42_43,
        MIGRATE_43_44,
        MIGRATE_44_45,
        MIGRATE_45_46,
        MIGRATE_69_70,
        MIGRATE_70_71,
        MIGRATE_71_72,
        MIGRATE_72_73,
        MIGRATE_83_84,
        MIGRATE_84_85,
    )
}
