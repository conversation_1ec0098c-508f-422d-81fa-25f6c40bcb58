package com.stt.android.sportmode.datascreen.options

import androidx.annotation.StringRes
import com.stt.android.sportmode.R
import com.stt.android.sportmode.modesetting.baseStr

object DataOptionTitleMapping {
    private val mapping: Map<String, Int> = mapOf(
        "TXT_ENVIRONMENT" to R.string.data_options_environment,
        "TXT_TEMPERATURE" to R.string.data_options_temperature,
        "TXT_SUNRISE" to R.string.data_options_sunrise,
        "TXT_SUNSET" to R.string.data_options_sunset,
        "TXT_SEA_LEVEL_PRESSURE" to R.string.data_options_sea_level_pressure,
        "TXT_PHYSIOLOGY" to R.string.data_options_physiology,
        "TXT_TOTAL_ACTIVITY_ENERGY_EXPENDITURE" to R.string.data_options_total_activity_energy_expenditure,
        "TXT_TOTAL_ENERGY_EXPENDITURE" to R.string.data_options_total_energy_expenditure,
        "TXT_FAT_EXPENDITURE_RATE" to R.string.data_options_fat_expenditure_rate,
        "TXT_CARBOHYDRATE_EXPENDITURE_RATE" to R.string.data_options_carbohydrate_expenditure_rate,
        "TXT_FAT_EXPENDITURE" to R.string.data_options_fat_expenditure,
        "TXT_CARBOHYDRATE_EXPENDITURE" to R.string.data_options_carbohydrate_expenditure,
        "TXT_FAT_OXIDATION_PERCENTAGE" to R.string.data_options_fat_oxidation_percentage,
        "TXT_TRAINING_STRESS_SCORE" to R.string.data_options_training_stress_score,
        "TXT_POWER" to R.string.data_options_power,
        "TXT_RUNNING_POWER" to R.string.data_options_running_power,
        "TXT_AVG_RUNNING_POWER" to R.string.data_options_avg_running_power,
        "TXT_RUNNING_POWER_3S" to R.string.data_options_running_power_3s,
        "TXT_RUNNING_POWER_10S" to R.string.data_options_running_power_10s,
        "TXT_RUNNING_POWER_30S" to R.string.data_options_running_power_30s,
        "TXT_CYCLING_POWER" to R.string.data_options_cycling_power,
        "TXT_AVG_CYCLING_POWER" to R.string.data_options_avg_cycling_power,
        "TXT_CYCLING_POWER_3S" to R.string.data_options_cycling_power_3s,
        "TXT_CYCLING_POWER_10S" to R.string.data_options_cycling_power_10s,
        "TXT_CYCLING_POWER_30S" to R.string.data_options_cycling_power_30s,
        "TXT_VERTICAL" to R.string.data_options_vertical,
        "TXT_ASCENT" to R.string.data_options_ascent,
        "TXT_DESCENT" to R.string.data_options_descent,
        "TXT_ALTITUDE" to R.string.data_options_altitude,
        "TXT_MAX_ALTITUDE" to R.string.data_options_max_altitude,
        "TXT_MIN_ALTITUDE" to R.string.data_options_min_altitude,
        "TXT_VERTICAL_OSCILLATION" to R.string.data_options_vertical_oscillation,
        "TXT_AVG_VERTICAL_OSCILLATION" to R.string.data_options_avg_vertical_oscillation,
        "TXT_HEART_RATE" to R.string.data_options_heart_rate,
        "TXT_AVG_HEART_RATE" to R.string.data_options_avg_heart_rate,
        "TXT_MAX_HEART_RATE" to R.string.data_options_max_heart_rate,
        "TXT_MIN_HEART_RATE" to R.string.data_options_min_heart_rate,
        "TXT_COUNT" to R.string.data_options_count,
        "TXT_STEPS" to R.string.data_options_steps,
        "TXT_SWIM_STROKES" to R.string.data_options_swim_strokes,
        "TXT_JUMPS" to R.string.data_options_jumps,
        "TXT_MAX_CONTINUOUS_JUMPS" to R.string.data_options_max_continuous_jumps,
        "TXT_ROWING_STROKES" to R.string.data_options_rowing_strokes,
        "TXT_CROSSTRAINER_STEPS" to R.string.data_options_crosstrainer_steps,
        "TXT_RHYTHM" to R.string.data_options_rhythm,
        "TXT_CADENCE" to R.string.data_options_cadence,
        "TXT_AVG_CADENCE" to R.string.data_options_avg_cadence,
        "TXT_MAX_CADENCE" to R.string.data_options_max_cadence,
        "TXT_AVG_SWIM_STROKE_RATE" to R.string.data_options_avg_swim_stroke_rate,
        "TXT_JUMP_ROPE_CADENCE" to R.string.data_options_jump_rope_cadence,
        "TXT_AVG_JUMP_ROPE_CADENCE" to R.string.data_options_avg_jump_rope_cadence,
        "TXT_ROWING_STROKE_RATE" to R.string.data_options_rowing_stroke_rate,
        "TXT_AVG_ROWING_STROKE_RATE" to R.string.data_options_avg_rowing_stroke_rate,
        "TXT_CROSSTRAINER_CADENCE" to R.string.data_options_crosstrainer_cadence,
        "TXT_AVG_CROSSTRAINER_CADENCE" to R.string.data_options_avg_crosstrainer_cadence,
        "TXT_DISTANCE" to R.string.data_options_distance,
        "TXT_3D_DISTANCE" to R.string.data_options_3d_distance,
        "TXT_TREADMILL_DISTANCE" to R.string.data_options_treadmill_distance,
        "TXT_SWIM_DISTANCE" to R.string.data_options_swim_distance,
        "TXT_AVG_STRIDE" to R.string.data_options_avg_stride,
        "TXT_AVG_DISTANCE_PER_STROKE" to R.string.data_options_avg_distance_per_stroke,
        "TXT_DURATION_AND_TIME" to R.string.data_options_duration_and_time,
        "TXT_DURATION" to R.string.data_options_duration,
        "TXT_TIMEOFDAY" to R.string.data_options_timeofday,
        "TXT_ASCENT_TIME" to R.string.data_options_ascent_time,
        "TXT_DESCENT_TIME" to R.string.data_options_descent_time,
        "TXT_GROUND_CONTACT_TIME" to R.string.data_options_ground_contact_time,
        "TXT_AVG_GROUND_CONTACT_TIME" to R.string.data_options_avg_ground_contact_time,
        "TXT_SPEED_PACE_INTERVAL" to R.string.data_options_speed_pace_interval,
        "TXT_PACE" to R.string.data_options_pace,
        "TXT_AVG_PACE" to R.string.data_options_avg_pace,
        "TXT_MAX_PACE" to R.string.data_options_max_pace,
        "TXT_NORMALIZED_GRADED_PACE" to R.string.data_options_normalized_graded_pace,
        "TXT_AVG_NORMALIZED_GRADED_PACE" to R.string.data_options_avg_normalized_graded_pace,
        "TXT_MAX_NORMALIZED_GRADED_PACE" to R.string.data_options_max_normalized_graded_pace,
        "TXT_SPEED" to R.string.data_options_speed,
        "TXT_AVG_SPEED" to R.string.data_options_avg_speed,
        "TXT_MAX_SPEED" to R.string.data_options_max_speed,
        "TXT_ASCENT_SPEED" to R.string.data_options_ascent_speed,
        "TXT_AVG_ASCENT_SPEED" to R.string.data_options_avg_ascent_speed,
        "TXT_MAX_ASCENT_SPEED" to R.string.data_options_max_ascent_speed,
        "TXT_DESCENT_SPEED" to R.string.data_options_descent_speed,
        "TXT_AVG_DESCENT_SPEED" to R.string.data_options_avg_descent_speed,
        "TXT_MAX_DESCENT_SPEED" to R.string.data_options_max_descent_speed,
        "TXT_DOWNHILL_SPEED" to R.string.data_options_downhill_speed,
        "TXT_AVG_DOWNHILL_SPEED" to R.string.data_options_avg_downhill_speed,
        "TXT_MAX_DOWNHILL_SPEED" to R.string.data_options_max_downhill_speed,
        "TXT_SWIM_PACE" to R.string.data_options_swim_pace,
        "TXT_AVG_SWIM_PACE" to R.string.data_options_avg_swim_pace,
        "TXT_AVG_POOL_SWIM_PACE" to R.string.data_options_avg_pool_swim_pace,
        "TXT_OTHER" to R.string.data_options_other,
        "TXT_BATTERY" to R.string.data_options_battery,
        "TXT_AVG_GROUND_CONTACT_TIME_BALANCE" to R.string.data_options_avg_ground_contact_time_balance,
        "TXT_SWOLF" to R.string.data_options_swolf,
        "TXT_AUTO_LAP_AVG_RUNNING_POWER" to R.string.data_options_auto_lap_avg_running_power,
        "TXT_AUTO_LAP_AVG_CYCLING_POWER" to R.string.data_options_auto_lap_avg_cycling_power,
        "TXT_AUTO_LAP_ASCENT" to R.string.data_options_auto_lap_ascent,
        "TXT_AUTO_LAP_DESCENT" to R.string.data_options_auto_lap_descent,
        "TXT_AUTO_LAP_MAX_ALTITUDE" to R.string.data_options_auto_lap_max_altitude,
        "TXT_AUTO_LAP_MIN_ALTITUDE" to R.string.data_options_auto_lap_min_altitude,
        "TXT_AUTO_LAP_AVG_VERTICAL_OSCILLATION" to R.string.data_options_auto_lap_avg_vertical_oscillation,
        "TXT_AUTO_LAP_AVG_HEART_RATE" to R.string.data_options_auto_lap_avg_heart_rate,
        "TXT_AUTO_LAP_MAX_HEART_RATE" to R.string.data_options_auto_lap_max_heart_rate,
        "TXT_AUTO_LAP_MIN_HEART_RATE" to R.string.data_options_auto_lap_min_heart_rate,
        "TXT_AUTO_LAP_STEPS" to R.string.data_options_auto_lap_steps,
        "TXT_AUTO_LAP_SWIM_STROKES" to R.string.data_options_auto_lap_swim_strokes,
        "TXT_AUTO_LAP_JUMPS" to R.string.data_options_auto_lap_jumps,
        "TXT_AUTO_LAP_ROWING_STROKES" to R.string.data_options_auto_lap_rowing_strokes,
        "TXT_AUTO_LAP_CROSSTRAINER_STEPS" to R.string.data_options_auto_lap_crosstrainer_steps,
        "TXT_AUTO_LAP_AVG_CADENCE" to R.string.data_options_auto_lap_avg_cadence,
        "TXT_AUTO_LAP_MAX_CADENCE" to R.string.data_options_auto_lap_max_cadence,
        "TXT_AUTO_LAP_AVG_SWIM_STROKE_RATE" to R.string.data_options_auto_lap_avg_swim_stroke_rate,
        "TXT_AUTO_LAP_AVG_JUMP_ROPE_CADENCE" to R.string.data_options_auto_lap_avg_jump_rope_cadence,
        "TXT_AUTO_LAP_AVG_ROWING_STROKE_RATE" to R.string.data_options_auto_lap_avg_rowing_stroke_rate,
        "TXT_AUTO_LAP_AVG_CROSSTRAINER_CADENCE" to R.string.data_options_auto_lap_avg_crosstrainer_cadence,
        "TXT_AUTO_LAP_DISTANCE" to R.string.data_options_auto_lap_distance,
        "TXT_AUTO_LAP_3D_DISTANCE" to R.string.data_options_auto_lap_3d_distance,
        "TXT_AUTO_LAP_TREADMILL_DISTANCE" to R.string.data_options_auto_lap_treadmill_distance,
        "TXT_AUTO_LAP_SWIM_DISTANCE" to R.string.data_options_auto_lap_swim_distance,
        "TXT_AUTO_LAP_AVG_STRIDE" to R.string.data_options_auto_lap_avg_stride,
        "TXT_AUTO_LAP_AVG_DISTANCE_PER_STROKE" to R.string.data_options_auto_lap_avg_distance_per_stroke,
        "TXT_AUTO_LAP_DURATION" to R.string.data_options_auto_lap_duration,
        "TXT_AUTO_LAP_ASCENT_TIME" to R.string.data_options_auto_lap_ascent_time,
        "TXT_AUTO_LAP_DESCENT_TIME" to R.string.data_options_auto_lap_descent_time,
        "TXT_AUTO_LAP_AVG_GROUND_CONTACT_TIME" to R.string.data_options_auto_lap_avg_ground_contact_time,
        "TXT_AUTO_LAP_AVG_PACE" to R.string.data_options_auto_lap_avg_pace,
        "TXT_AUTO_LAP_MAX_PACE" to R.string.data_options_auto_lap_max_pace,
        "TXT_AUTO_LAP_AVG_NORMALIZED_GRADED_PACE" to R.string.data_options_auto_lap_avg_normalized_graded_pace,
        "TXT_AUTO_LAP_MAX_NORMALIZED_GRADED_PACE" to R.string.data_options_auto_lap_max_normalized_graded_pace,
        "TXT_AUTO_LAP_AVG_SPEED" to R.string.data_options_auto_lap_avg_speed,
        "TXT_AUTO_LAP_MAX_SPEED" to R.string.data_options_auto_lap_max_speed,
        "TXT_AUTO_LAP_AVG_ASCENT_SPEED" to R.string.data_options_auto_lap_avg_ascent_speed,
        "TXT_AUTO_LAP_MAX_ASCENT_SPEED" to R.string.data_options_auto_lap_max_ascent_speed,
        "TXT_AUTO_LAP_AVG_DESCENT_SPEED" to R.string.data_options_auto_lap_avg_descent_speed,
        "TXT_AUTO_LAP_MAX_DESCENT_SPEED" to R.string.data_options_auto_lap_max_descent_speed,
        "TXT_AUTO_LAP_AVG_DOWNHILL_SPEED" to R.string.data_options_auto_lap_avg_downhill_speed,
        "TXT_AUTO_LAP_MAX_DOWNHILL_SPEED" to R.string.data_options_auto_lap_max_downhill_speed,
        "TXT_AUTO_LAP_AVG_SWIM_PACE" to R.string.data_options_auto_lap_avg_swim_pace,
        "TXT_AUTO_LAP_AVG_POOL_SWIM_PACE" to R.string.data_options_auto_lap_avg_pool_swim_pace,
        "TXT_AUTO_LAP_AVG_GROUND_CONTACT_TIME_BALANCE" to R.string.data_options_auto_lap_avg_ground_contact_time_balance,
        "TXT_MANUAL_LAP_AVG_RUNNING_POWER" to R.string.data_options_manual_lap_avg_running_power,
        "TXT_MANUAL_LAP_AVG_CYCLING_POWER" to R.string.data_options_manual_lap_avg_cycling_power,
        "TXT_MANUAL_LAP_ASCENT" to R.string.data_options_manual_lap_ascent,
        "TXT_MANUAL_LAP_DESCENT" to R.string.data_options_manual_lap_descent,
        "TXT_MANUAL_LAP_MAX_ALTITUDE" to R.string.data_options_manual_lap_max_altitude,
        "TXT_MANUAL_LAP_MIN_ALTITUDE" to R.string.data_options_manual_lap_min_altitude,
        "TXT_MANUAL_LAP_AVG_VERTICAL_OSCILLATION" to R.string.data_options_manual_lap_avg_vertical_oscillation,
        "TXT_MANUAL_LAP_AVG_HEART_RATE" to R.string.data_options_manual_lap_avg_heart_rate,
        "TXT_MANUAL_LAP_MAX_HEART_RATE" to R.string.data_options_manual_lap_max_heart_rate,
        "TXT_MANUAL_LAP_MIN_HEART_RATE" to R.string.data_options_manual_lap_min_heart_rate,
        "TXT_MANUAL_LAP_STEPS" to R.string.data_options_manual_lap_steps,
        "TXT_MANUAL_LAP_SWIM_STROKES" to R.string.data_options_manual_lap_swim_strokes,
        "TXT_MANUAL_LAP_JUMPS" to R.string.data_options_manual_lap_jumps,
        "TXT_MANUAL_LAP_ROWING_STROKES" to R.string.data_options_manual_lap_rowing_strokes,
        "TXT_MANUAL_LAP_CROSSTRAINER_STEPS" to R.string.data_options_manual_lap_crosstrainer_steps,
        "TXT_MANUAL_LAP_AVG_CADENCE" to R.string.data_options_manual_lap_avg_cadence,
        "TXT_MANUAL_LAP_MAX_CADENCE" to R.string.data_options_manual_lap_max_cadence,
        "TXT_MANUAL_LAP_AVG_SWIM_STROKE_RATE" to R.string.data_options_manual_lap_avg_swim_stroke_rate,
        "TXT_MANUAL_LAP_AVG_JUMP_ROPE_CADENCE" to R.string.data_options_manual_lap_avg_jump_rope_cadence,
        "TXT_MANUAL_LAP_AVG_ROWING_STROKE_RATE" to R.string.data_options_manual_lap_avg_rowing_stroke_rate,
        "TXT_MANUAL_LAP_AVG_CROSSTRAINER_CADENCE" to R.string.data_options_manual_lap_avg_crosstrainer_cadence,
        "TXT_MANUAL_LAP_DISTANCE" to R.string.data_options_manual_lap_distance,
        "TXT_MANUAL_LAP_3D_DISTANCE" to R.string.data_options_manual_lap_3d_distance,
        "TXT_MANUAL_LAP_TREADMILL_DISTANCE" to R.string.data_options_manual_lap_treadmill_distance,
        "TXT_MANUAL_LAP_SWIM_DISTANCE" to R.string.data_options_manual_lap_swim_distance,
        "TXT_MANUAL_LAP_AVG_STRIDE" to R.string.data_options_manual_lap_avg_stride,
        "TXT_MANUAL_LAP_AVG_DISTANCE_PER_STROKE" to R.string.data_options_manual_lap_avg_distance_per_stroke,
        "TXT_MANUAL_LAP_DURATION" to R.string.data_options_manual_lap_duration,
        "TXT_MANUAL_LAP_ASCENT_TIME" to R.string.data_options_manual_lap_ascent_time,
        "TXT_MANUAL_LAP_DESCENT_TIME" to R.string.data_options_manual_lap_descent_time,
        "TXT_MANUAL_LAP_AVG_GROUND_CONTACT_TIME" to R.string.data_options_manual_lap_avg_ground_contact_time,
        "TXT_MANUAL_LAP_AVG_PACE" to R.string.data_options_manual_lap_avg_pace,
        "TXT_MANUAL_LAP_MAX_PACE" to R.string.data_options_manual_lap_max_pace,
        "TXT_MANUAL_LAP_AVG_NORMALIZED_GRADED_PACE" to R.string.data_options_manual_lap_avg_normalized_graded_pace,
        "TXT_MANUAL_LAP_MAX_NORMALIZED_GRADED_PACE" to R.string.data_options_manual_lap_max_normalized_graded_pace,
        "TXT_MANUAL_LAP_AVG_SPEED" to R.string.data_options_manual_lap_avg_speed,
        "TXT_MANUAL_LAP_MAX_SPEED" to R.string.data_options_manual_lap_max_speed,
        "TXT_MANUAL_LAP_AVG_ASCENT_SPEED" to R.string.data_options_manual_lap_avg_ascent_speed,
        "TXT_MANUAL_LAP_MAX_ASCENT_SPEED" to R.string.data_options_manual_lap_max_ascent_speed,
        "TXT_MANUAL_LAP_AVG_DESCENT_SPEED" to R.string.data_options_manual_lap_avg_descent_speed,
        "TXT_MANUAL_LAP_MAX_DESCENT_SPEED" to R.string.data_options_manual_lap_max_descent_speed,
        "TXT_MANUAL_LAP_AVG_DOWNHILL_SPEED" to R.string.data_options_manual_lap_avg_downhill_speed,
        "TXT_MANUAL_LAP_MAX_DOWNHILL_SPEED" to R.string.data_options_manual_lap_max_downhill_speed,
        "TXT_MANUAL_LAP_AVG_SWIM_PACE" to R.string.data_options_manual_lap_avg_swim_pace,
        "TXT_MANUAL_LAP_AVG_POOL_SWIM_PACE" to R.string.data_options_manual_lap_avg_pool_swim_pace,
        "TXT_MANUAL_LAP_AVG_GROUND_CONTACT_TIME_BALANCE" to R.string.data_options_manual_lap_avg_ground_contact_time_balance,
        "TXT_INTERVAL_AVG_RUNNING_POWER" to R.string.data_options_interval_avg_running_power,
        "TXT_INTERVAL_AVG_CYCLING_POWER" to R.string.data_options_interval_avg_cycling_power,
        "TXT_INTERVAL_ASCENT" to R.string.data_options_interval_ascent,
        "TXT_INTERVAL_DESCENT" to R.string.data_options_interval_descent,
        "TXT_INTERVAL_MAX_ALTITUDE" to R.string.data_options_interval_max_altitude,
        "TXT_INTERVAL_MIN_ALTITUDE" to R.string.data_options_interval_min_altitude,
        "TXT_INTERVAL_AVG_VERTICAL_OSCILLATION" to R.string.data_options_interval_avg_vertical_oscillation,
        "TXT_INTERVAL_AVG_HEART_RATE" to R.string.data_options_interval_avg_heart_rate,
        "TXT_INTERVAL_MAX_HEART_RATE" to R.string.data_options_interval_max_heart_rate,
        "TXT_INTERVAL_MIN_HEART_RATE" to R.string.data_options_interval_min_heart_rate,
        "TXT_INTERVAL_STEPS" to R.string.data_options_interval_steps,
        "TXT_INTERVAL_SWIM_STROKES" to R.string.data_options_interval_swim_strokes,
        "TXT_INTERVAL_JUMPS" to R.string.data_options_interval_jumps,
        "TXT_INTERVAL_ROWING_STROKES" to R.string.data_options_interval_rowing_strokes,
        "TXT_INTERVAL_CROSSTRAINER_STEPS" to R.string.data_options_interval_crosstrainer_steps,
        "TXT_INTERVAL_AVG_CADENCE" to R.string.data_options_interval_avg_cadence,
        "TXT_INTERVAL_MAX_CADENCE" to R.string.data_options_interval_max_cadence,
        "TXT_INTERVAL_AVG_SWIM_STROKE_RATE" to R.string.data_options_interval_avg_swim_stroke_rate,
        "TXT_INTERVAL_AVG_JUMP_ROPE_CADENCE" to R.string.data_options_interval_avg_jump_rope_cadence,
        "TXT_INTERVAL_AVG_ROWING_STROKE_RATE" to R.string.data_options_interval_avg_rowing_stroke_rate,
        "TXT_INTERVAL_AVG_CROSSTRAINER_CADENCE" to R.string.data_options_interval_avg_crosstrainer_cadence,
        "TXT_INTERVAL_DISTANCE" to R.string.data_options_interval_distance,
        "TXT_INTERVAL_3D_DISTANCE" to R.string.data_options_interval_3d_distance,
        "TXT_INTERVAL_TREADMILL_DISTANCE" to R.string.data_options_interval_treadmill_distance,
        "TXT_INTERVAL_SWIM_DISTANCE" to R.string.data_options_interval_swim_distance,
        "TXT_INTERVAL_AVG_STRIDE" to R.string.data_options_interval_avg_stride,
        "TXT_INTERVAL_AVG_DISTANCE_PER_STROKE" to R.string.data_options_interval_avg_distance_per_stroke,
        "TXT_INTERVAL_DURATION" to R.string.data_options_interval_duration,
        "TXT_INTERVAL_ASCENT_TIME" to R.string.data_options_interval_ascent_time,
        "TXT_INTERVAL_DESCENT_TIME" to R.string.data_options_interval_descent_time,
        "TXT_INTERVAL_AVG_GROUND_CONTACT_TIME" to R.string.data_options_interval_avg_ground_contact_time,
        "TXT_INTERVAL_AVG_PACE" to R.string.data_options_interval_avg_pace,
        "TXT_INTERVAL_MAX_PACE" to R.string.data_options_interval_max_pace,
        "TXT_INTERVAL_AVG_NORMALIZED_GRADED_PACE" to R.string.data_options_interval_avg_normalized_graded_pace,
        "TXT_INTERVAL_MAX_NORMALIZED_GRADED_PACE" to R.string.data_options_interval_max_normalized_graded_pace,
        "TXT_INTERVAL_AVG_SPEED" to R.string.data_options_interval_avg_speed,
        "TXT_INTERVAL_MAX_SPEED" to R.string.data_options_interval_max_speed,
        "TXT_INTERVAL_AVG_ASCENT_SPEED" to R.string.data_options_interval_avg_ascent_speed,
        "TXT_INTERVAL_MAX_ASCENT_SPEED" to R.string.data_options_interval_max_ascent_speed,
        "TXT_INTERVAL_AVG_DESCENT_SPEED" to R.string.data_options_interval_avg_descent_speed,
        "TXT_INTERVAL_MAX_DESCENT_SPEED" to R.string.data_options_interval_max_descent_speed,
        "TXT_INTERVAL_AVG_DOWNHILL_SPEED" to R.string.data_options_interval_avg_downhill_speed,
        "TXT_INTERVAL_MAX_DOWNHILL_SPEED" to R.string.data_options_interval_max_downhill_speed,
        "TXT_INTERVAL_AVG_SWIM_PACE" to R.string.data_options_interval_avg_swim_pace,
        "TXT_INTERVAL_AVG_POOL_SWIM_PACE" to R.string.data_options_interval_avg_pool_swim_pace,
        "TXT_INTERVAL_AVG_GROUND_CONTACT_TIME_BALANCE" to R.string.data_options_interval_avg_ground_contact_time_balance,
        "TXT_CURRENT_SEGMENT_DURATION" to R.string.data_options_current_segment_duration,
        "TXT_CURRENT_SEGMENT_AVG_POOL_SWIM_PACE" to R.string.data_options_current_segment_avg_pool_swim_pace,
        "TXT_CURRENT_SEGMENT_SWOLF" to R.string.data_options_current_segment_swolf,
        "TXT_CURRENT_SEGMENT_AVG_SWIM_STROKE_RATE" to R.string.data_options_current_segment_avg_swim_stroke_rate,
        "TXT_CURRENT_SEGMENT_SWIM_STROKES" to R.string.data_options_current_segment_swim_strokes,
    )

    @StringRes
    fun getTextResByName(name: String): Int {
        return mapping.getOrElse(name) { baseStr.edit }
    }
}
