package com.stt.android.sportmode.trainingmode.competition

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CornerSize
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.Chip
import androidx.compose.material.ChipDefaults
import androidx.compose.material.Divider
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.Icon
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Scaffold
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.material.TextField
import androidx.compose.material.TextFieldDefaults
import androidx.compose.material.TopAppBar
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Search
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.tooling.preview.Preview
import com.stt.android.compose.component.SuuntoIconButton
import com.stt.android.compose.modifiers.narrowContent
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.SuuntoIcons
import com.stt.android.compose.theme.body
import com.stt.android.compose.theme.cloudyGrey
import com.stt.android.compose.theme.darkGrey
import com.stt.android.compose.theme.lightGrey
import com.stt.android.compose.theme.nearBlack
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.widgets.EmptyContent
import com.stt.android.compose.widgets.LoadingContent
import com.stt.android.newfeed.FilterTag
import com.stt.android.newfeed.WorkoutCardInfo
import com.stt.android.social.following.PeopleActivity
import com.stt.android.sportmode.R
import com.stt.android.sportmode.trainingmode.CompetitionTargetFilterViewState
import com.stt.android.sportmode.trainingmode.CompetitionTargetSelectViewState
import com.stt.android.sportmode.trainingmode.DistanceFilter
import com.stt.android.sportmode.trainingmode.SortBy
import com.stt.android.sportmode.trainingmode.TrainingModeEditViewModel
import com.stt.android.ui.components.workout.WorkoutCard
import com.stt.android.ui.components.workout.WorkoutCardViewModel
import java.util.Locale
import com.stt.android.R as BaseR

@Suppress("ktlint:compose:vm-forwarding-check")
@Composable
fun CompetitionTargetSelectScreen(
    onCloseClicked: () -> Unit,
    onItemClicked: (WorkoutCardInfo) -> Unit,
    viewModel: TrainingModeEditViewModel,
    workoutCardViewModel: WorkoutCardViewModel,
    modifier: Modifier = Modifier,
) {
    val uiState by viewModel.uiState.collectAsState()
    val filterUIState by viewModel.filterUiState.collectAsState()
    var selectedDistance by remember { mutableStateOf(filterUIState.selectedDistanceFilter) }
    var selectedSortBy by remember { mutableStateOf(filterUIState.selectedSortBy) }
    LaunchedEffect(Unit) {
        viewModel.updateFilterState(searchQuery = "")
    }
    Scaffold(
        modifier = modifier,
        topBar = {
            CompetitionTopBar(onCloseClicked)
        },
    ) { paddingValues ->
        ContentBody(
            workoutCardViewModel = workoutCardViewModel,
            selectedDistanceFilter = selectedDistance,
            selectedSortBy = selectedSortBy,
            filterTags = listOf(FilterTag.ALL, FilterTag.Me, FilterTag.FOLLOWING),

            onDistanceFilterClicked = { distanceFilter ->
                viewModel.updateFilterState(distanceFilter = distanceFilter)
                selectedDistance = distanceFilter
            },
            onSortByClicked = { sortBy ->
                viewModel.updateFilterState(sortBy = sortBy)
                selectedSortBy = sortBy
            },
            uiState = uiState,
            filterUIState = filterUIState,
            onItemClicked = onItemClicked,
            onValueChanged = {
                viewModel.updateFilterState(searchQuery = it)
            },
            onChipClick = { filterTag ->
                viewModel.onFilterTagChanged(filterTag)
            },
            modifier = Modifier
                .padding(paddingValues)
                .fillMaxSize()
                .narrowContent(),
        )
    }
}

@Composable
private fun CompetitionTopBar(onCloseClicked: () -> Unit) {
    TopAppBar(
        title = {
            Text(
                text = stringResource(BaseR.string.competition_target_select_title).uppercase(),
                color = MaterialTheme.colors.surface
            )
        },
        navigationIcon = {
            SuuntoIconButton(
                icon = SuuntoIcons.ActionClose,
                onClick = onCloseClicked,
                contentDescription = stringResource(BaseR.string.back),
                tint = Color.White,
            )
        },
        backgroundColor = MaterialTheme.colors.nearBlack,
    )
}

@Composable
@Suppress("ktlint:compose:vm-forwarding-check")
private fun ContentBody(
    workoutCardViewModel: WorkoutCardViewModel,
    filterTags: List<FilterTag>,
    selectedDistanceFilter: DistanceFilter,
    selectedSortBy: SortBy,
    uiState: CompetitionTargetSelectViewState,
    filterUIState: CompetitionTargetFilterViewState,
    onChipClick: (FilterTag) -> Unit,
    onDistanceFilterClicked: (DistanceFilter) -> Unit,
    onSortByClicked: (SortBy) -> Unit,
    onItemClicked: (WorkoutCardInfo) -> Unit,
    onValueChanged: (String) -> Unit,
    modifier: Modifier = Modifier,
) {
    var showDistanceDialog by remember { mutableStateOf(false) }
    var showSortByDialog by remember { mutableStateOf(false) }
    Column(modifier = modifier) {
        CompetitionTargetSearchBar(
            keyWords = filterUIState.searchQuery,
            onValueChanged = onValueChanged
        )
        FiltersSection(
            filterDistance = selectedDistanceFilter.let {
                if (filterUIState.isImperial) it.toImperialText() else it.toText()
            },
            filterSortBy = selectedSortBy.toText().lowercase(Locale.US),
            onFilterDistanceClicked = { showDistanceDialog = true },
            onSortByClicked = { showSortByDialog = true }
        )
        FilterChips(
            filterTags = filterTags,
            selectedFilterTag = filterUIState.selectedFilterTag,
            onClick = onChipClick,
        )
        TargetWorkoutFeed(workoutCardViewModel, filterUIState, uiState, onItemClicked)
    }
    if (showDistanceDialog) {
        SelectItemDialog(
            title = stringResource(id = com.stt.android.R.string.distance),
            items = DistanceFilter.entries.map {
                it.let {
                    if (filterUIState.isImperial) it.toImperialText() else it.toText()
                }
            },
            onDismissRequest = { showDistanceDialog = false },
            selectedItem = selectedDistanceFilter.let { if (filterUIState.isImperial) it.toImperialText() else it.toText() },
            onItemSelected = { index ->
                onDistanceFilterClicked.invoke(DistanceFilter.entries.elementAt(index))
                showDistanceDialog = false
            },
        )
    }

    if (showSortByDialog) {
        SelectItemDialog(
            title = stringResource(id = R.string.competition_target_sort_by_title),
            items = SortBy.entries.map { it.toText() },
            onDismissRequest = { showSortByDialog = false },
            selectedItem = selectedSortBy.toText(),
            onItemSelected = { item ->
                onSortByClicked.invoke(SortBy.entries.elementAt(item))
                showSortByDialog = false
            },
        )
    }
}

@Composable
fun CompetitionTargetSearchBar(
    modifier: Modifier = Modifier,
    keyWords: String = "",
    onValueChanged: (String) -> Unit = {},
) {
    val keyboardController = LocalSoftwareKeyboardController.current
    val focusManager = LocalFocusManager.current
    var searchKeyWords by rememberSaveable { mutableStateOf(keyWords) }

    TextField(
        value = keyWords,
        onValueChange = {
            searchKeyWords = it
            onValueChanged(it)
        },
        keyboardOptions = KeyboardOptions.Default.copy(
            imeAction = ImeAction.Search
        ),
        keyboardActions = KeyboardActions(
            onSearch = {
                onValueChanged.invoke(searchKeyWords)
                keyboardController?.hide()
                focusManager.clearFocus()
            }
        ),
        placeholder = {
            Text(
                stringResource(id = R.string.competition_target_search_bar_placeholder_text),
                color = MaterialTheme.colors.darkGrey,
                style = MaterialTheme.typography.body
            )
        },
        leadingIcon = {
            Icon(
                Icons.Filled.Search,
                contentDescription = null,
                modifier = Modifier.padding(start = MaterialTheme.spacing.medium)
            )
        },
        modifier = modifier.fillMaxWidth(),
        colors = TextFieldDefaults.textFieldColors(
            backgroundColor = MaterialTheme.colors.lightGrey,
            focusedIndicatorColor = Color.Transparent,
            unfocusedIndicatorColor = Color.Transparent
        ),
        shape = MaterialTheme.shapes.small.copy(all = CornerSize(0)),
        singleLine = true
    )
}

@Composable
fun FiltersSection(
    filterDistance: String,
    filterSortBy: String,
    onFilterDistanceClicked: () -> Unit,
    onSortByClicked: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .background(MaterialTheme.colors.surface)
            .height(MaterialTheme.spacing.xxlarge)
            .padding(start = MaterialTheme.spacing.medium, end = MaterialTheme.spacing.medium),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Row(
            modifier = Modifier.clickable { onFilterDistanceClicked.invoke() },
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Text(
                filterDistance,
                style = MaterialTheme.typography.body2
            )
            Spacer(modifier = Modifier.width(MaterialTheme.spacing.small))
            Icon(
                painter = painterResource(id = BaseR.drawable.ic_dropdown_arrow_down_fill),
                contentDescription = null
            )
        }

        Spacer(modifier = Modifier.width(MaterialTheme.spacing.xxlarge))
        Row(
            modifier = Modifier.clickable { onSortByClicked.invoke() },
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Text(
                "Sort by $filterSortBy",
                style = MaterialTheme.typography.body2
            )
            Spacer(modifier = Modifier.width(MaterialTheme.spacing.small))
            Icon(
                painter = painterResource(id = BaseR.drawable.ic_dropdown_arrow_down_fill),
                contentDescription = null
            )
        }
    }
}

@Composable
@Suppress("ktlint:compose:vm-forwarding-check")
fun TargetWorkoutFeed(
    workoutCardViewModel: WorkoutCardViewModel,
    filterUIState: CompetitionTargetFilterViewState,
    uiState: CompetitionTargetSelectViewState,
    onItemClicked: (WorkoutCardInfo) -> Unit,
    modifier: Modifier = Modifier,
) {
    when (uiState) {
        is CompetitionTargetSelectViewState.Loaded -> {
            val context = LocalContext.current
            if (uiState.targetWorkoutList.isNotEmpty()) {
                LazyColumn(modifier = modifier.background(color = MaterialTheme.colors.lightGrey)) {
                    items(
                        items = uiState.targetWorkoutList,
                        key = { item -> item.workoutHeader.id }
                    ) { item ->
                        WorkoutCard(
                            workoutHeader = item.workoutHeader,
                            viewModel = workoutCardViewModel,
                            onClick = { onItemClicked.invoke(item) },
                            modifier = Modifier.padding(
                                vertical = MaterialTheme.spacing.small,
                                horizontal = MaterialTheme.spacing.medium,
                            ),
                            configuration = WorkoutCardViewModel.Configuration(
                                showUser = true,
                            ),
                        )
                    }
                }
            } else {
                EmptyContent(
                    modifier = modifier,
                    emptyTipIconResId = if (shouldShowFilteredResults(filterUIState, uiState)) null else BaseR.drawable.ic_find_people_outline,
                    emptyTipTextResId = if (shouldShowFilteredResults(filterUIState, uiState)) R.string.competition_target_filter_result_empty else BaseR.string.find_people_tips,
                    emptyTipBtnTextResId = if (shouldShowFilteredResults(filterUIState, uiState)) null else BaseR.string.find_people,
                    onClickBtn = {
                        context.startActivity(PeopleActivity.newIntent(context))
                    }
                )
            }
        }
        is CompetitionTargetSelectViewState.Error -> EmptyContent(
            modifier = modifier,
            emptyTipIconResId = BaseR.drawable.ic_empty_other,
            emptyTipTextResId = BaseR.string.no_tracked_activity
        )
        is CompetitionTargetSelectViewState.Loading -> LoadingContent(true)
    }
}

fun shouldShowFilteredResults(filterUIState: CompetitionTargetFilterViewState, uiState: CompetitionTargetSelectViewState.Loaded): Boolean {
    val hasSearchQuery = filterUIState.searchQuery.isNotEmpty()
    val hasDistanceFilter = filterUIState.selectedDistanceFilter != DistanceFilter.ALL
    val hasSorting = filterUIState.selectedSortBy != SortBy.POST_TIME
    return (hasSearchQuery || hasDistanceFilter || hasSorting) && !uiState.findPeopleNeeded
}

@Composable
private fun DistanceFilter.toText() =
    when (this) {
        DistanceFilter.ALL -> stringResource(id = R.string.competition_target_distance_all)
        DistanceFilter.FILTER_5 -> stringResource(id = R.string.competition_target_distance_filter_5)
        DistanceFilter.FILTER_5_10 -> stringResource(id = R.string.competition_target_distance_filter_5_10)
        DistanceFilter.FILTER_10_21 -> stringResource(id = R.string.competition_target_distance_filter_10_21)
        DistanceFilter.FILTER_21_45 -> stringResource(id = R.string.competition_target_distance_filter_21_45)
        DistanceFilter.FILTER_45 -> stringResource(id = R.string.competition_target_distance_filter_45)
    }

@Composable
private fun DistanceFilter.toImperialText() =
    when (this) {
        DistanceFilter.ALL -> stringResource(id = R.string.competition_target_distance_all)
        DistanceFilter.FILTER_5 -> stringResource(id = R.string.competition_target_distance_filter_5_imperial)
        DistanceFilter.FILTER_5_10 -> stringResource(id = R.string.competition_target_distance_filter_5_10_imperial)
        DistanceFilter.FILTER_10_21 -> stringResource(id = R.string.competition_target_distance_filter_10_21_imperial)
        DistanceFilter.FILTER_21_45 -> stringResource(id = R.string.competition_target_distance_filter_21_45_imperial)
        DistanceFilter.FILTER_45 -> stringResource(id = R.string.competition_target_distance_filter_45_imperial)
    }

@Composable
private fun SortBy.toText() =
    when (this) {
        SortBy.POST_TIME -> stringResource(id = R.string.competition_target_filter_sort_post_time)
        SortBy.PACE -> stringResource(id = R.string.competition_target_filter_sort_pace)
        SortBy.DURATION -> stringResource(id = R.string.competition_target_filter_sort_duration)
    }

@Composable
fun FilterChips(
    modifier: Modifier = Modifier,
    filterTags: List<FilterTag> = listOf(FilterTag.ALL, FilterTag.Me, FilterTag.FOLLOWING),
    selectedFilterTag: FilterTag = FilterTag.ALL,
    onClick: (FilterTag) -> Unit = {},
) {
    var selectedChip by rememberSaveable { mutableStateOf(selectedFilterTag) }
    Row(
        modifier = modifier
            .fillMaxWidth()
            .background(MaterialTheme.colors.surface)
            .height(MaterialTheme.spacing.xxxxlarge),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.small)
    ) {
        Spacer(modifier = Modifier.width(MaterialTheme.spacing.medium))
        filterTags.forEach { filterTag ->
            ChipItem(
                isSelected = filterTag == selectedChip,
                text = filterTag.name,
                onClick = {
                    selectedChip = filterTag
                    onClick.invoke(filterTag)
                }
            )
        }
    }
}

@OptIn(ExperimentalMaterialApi::class)
@Composable
fun ChipItem(text: String, isSelected: Boolean, modifier: Modifier = Modifier, onClick: () -> Unit = {}) {
    Chip(
        onClick = onClick,
        colors = ChipDefaults.chipColors(
            backgroundColor = if (isSelected) MaterialTheme.colors.primary else MaterialTheme.colors.cloudyGrey,
            contentColor = Color.White
        )
    ) {
        Text(
            text = text,
            style = MaterialTheme.typography.body2,
            color = if (isSelected) Color.White else MaterialTheme.colors.nearBlack,
            modifier = modifier.padding(MaterialTheme.spacing.xsmall)
        )
    }
}


@Composable
@Preview(showBackground = true)
private fun CompetitionTargetFilterPreview() {
    AppTheme {
        Surface {
            Column {
                CompetitionTargetSearchBar()
                FiltersSection(
                    filterDistance = stringResource(id = R.string.competition_target_distance_filter_5_10),
                    filterSortBy = stringResource(id = R.string.competition_target_filter_sort_post_time),
                    onFilterDistanceClicked = {},
                    onSortByClicked = {}
                )
                Divider()
                LazyColumn(modifier = Modifier.background(color = MaterialTheme.colors.lightGrey)) {
                    items(5) {
                        // TODO
                    }
                }
            }
        }
    }
}
